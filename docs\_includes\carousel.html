<div class='splide'>
    <div class='splide__track'>
        <ul class='splide__list'>
            {% for img in listimages(directory) %}
            {% with src=img %}
            {% include "splide_image.html" %}
            {% endwith %}
            {% endfor %}
        </ul>
    </div>
</div>

<script>
	document.addEventListener( 'DOMContentLoaded', function () {
		new Splide(
            '.splide', {
                type: 'loop',
                perPage: {{ per_page }},
                autoplay: true,
                pagination: false,
                // cover: true,
                // autoWidth: true,
                height: '25rem',
                focus: 'center',
                padding: {
                    left: '5rem',
                    right: '5rem',
                }
            }
        ).mount();
	} );
</script>
