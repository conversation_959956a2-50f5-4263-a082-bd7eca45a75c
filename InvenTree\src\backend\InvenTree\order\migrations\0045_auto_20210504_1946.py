# Generated by Django 3.2 on 2021-05-04 19:46

from django.db import migrations
import common.currency
import common.settings
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0044_auto_20210404_2016'),
    ]

    operations = [
        migrations.AddField(
            model_name='salesorderlineitem',
            name='sale_price',
            field=djmoney.models.fields.MoneyField(blank=True, decimal_places=4, default_currency=common.currency.currency_code_default(), help_text='Unit sale price', max_digits=19, null=True, verbose_name='Sale Price'),
        ),
        migrations.AddField(
            model_name='salesorderlineitem',
            name='sale_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=common.currency.currency_code_mappings(), default=common.currency.currency_code_default(), editable=False, max_length=3),
        ),
    ]
