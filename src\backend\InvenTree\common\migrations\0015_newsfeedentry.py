# Generated by Django 3.2.14 on 2022-07-31 19:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0014_notificationmessage'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewsFeedEntry',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feed_id', models.CharField(max_length=250, unique=True, verbose_name='Id')),
                ('title', models.Char<PERSON><PERSON>(max_length=250, verbose_name='Title')),
                ('link', models.URLField(max_length=250, verbose_name='Link')),
                ('published', models.DateTime<PERSON>ield(max_length=250, verbose_name='Published')),
                ('author', models.Char<PERSON><PERSON>(max_length=250, verbose_name='Author')),
                ('summary', models.Char<PERSON><PERSON>(max_length=250, verbose_name='Summary')),
                ('read', models.<PERSON><PERSON>anField(default=False, help_text='Was this news item read?', verbose_name='Read')),
            ],
        ),
    ]
