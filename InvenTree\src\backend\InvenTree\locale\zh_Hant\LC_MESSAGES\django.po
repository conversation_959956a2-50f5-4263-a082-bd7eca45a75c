msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 11:20\n"
"Last-Translator: \n"
"Language-Team: Chinese Traditional\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: zh-TW\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr ""

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "未找到 API 端點"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "提供了無效的單位"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "提供了無效的過濾器"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "用户沒有權限查閲當前模型。"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "電子郵件 (重複)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "郵箱地址已確認"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "您必須每次輸入相同的電子郵件。"

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "提供的主電子郵件地址無效。"

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "提供的郵箱域名未被批准。"

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "提供了無效的單位 ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "沒有提供數值"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "不能將 {original} 轉換到 {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "提供的數量無效"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "在管理面板中可以找到錯誤詳細信息"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "輸入日期"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr ""

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "備註"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "值' {name}' 未出現在模式格式中"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "提供的值與所需模式不匹配："

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr ""

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "序號為空白"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "複製序列號"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr ""

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "組範圍 {group} 超出了允許的數量 ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "未找到序列號"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "從這個值中刪除 HTML 標籤"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "連接錯誤"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "服務器響應狀態碼無效"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "發生異常"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "服務器響應的內容長度值無效"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "圖片尺寸過大"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "圖片下載超出最大尺寸"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "遠程服務器返回了空響應"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "提供的 URL 不是一個有效的圖片文件"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "阿拉伯語"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgarian"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Czech"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danish"

#: InvenTree/locales.py:24
msgid "German"
msgstr "German"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Greek"

#: InvenTree/locales.py:26
msgid "English"
msgstr "English"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spanish"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spanish (Mexican)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "愛沙尼亞語"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persian"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finnish"

#: InvenTree/locales.py:32
msgid "French"
msgstr "French"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebrew"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Hungarian"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italian"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japanese"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Korean"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "立陶宛語"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Latvian"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Dutch"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norwegian"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polish"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portuguese"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portuguese (Brazilian)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "羅馬尼亞語"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russian"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovak"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovenian"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbian"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Swedish"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thai"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turkish"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "烏克蘭語"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamese"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "中文 (簡體)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "中文 (繁體)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr ""

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "電子郵件"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "驗證外掛程式時發生錯誤"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Metadata必須是一個Python Dictionary物件"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "外掛程式Metadata"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "外掛程式使用的JSON Metadata欄位"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "格式錯誤"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "指定了不明的格式鍵值"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "缺少必須的格式鍵值"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "參考欄位不能空白"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "參考欄位並須符合格式"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "參考編號過大"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "無效的選項"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "名稱"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "描述"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "描述（選填）"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "路徑"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "同一個上層元件下不能有重複的名字"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Markdown 註記（選填）"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "條碼資料"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "第三方條碼資料"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "條碼雜湊值"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "條碼資料的唯一雜湊值"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "發現現有條碼"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr ""

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "伺服器錯誤"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "伺服器紀錄了一個錯誤。"

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "必須是有效的數字"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "貨幣"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "從可用選項中選擇貨幣"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "無效值"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "遠程圖片"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "遠程圖片文件的 URL"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "未啓用從遠程 URL下載圖片"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "從遠程URL下載圖像失敗"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "無效的物理單位"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "無效的貨幣代碼"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "訂單狀態"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "上層生產工單"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "包含變體"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "零件"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "類別"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "可測試部分"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "分配給我"

#: build/api.py:154
msgid "Assigned To"
msgstr "負責人"

#: build/api.py:189
msgid "Created before"
msgstr ""

#: build/api.py:193
msgid "Created after"
msgstr ""

#: build/api.py:197
msgid "Has start date"
msgstr ""

#: build/api.py:205
msgid "Start date before"
msgstr ""

#: build/api.py:209
msgid "Start date after"
msgstr ""

#: build/api.py:213
msgid "Has target date"
msgstr ""

#: build/api.py:221
msgid "Target date before"
msgstr ""

#: build/api.py:225
msgid "Target date after"
msgstr ""

#: build/api.py:229
msgid "Completed before"
msgstr ""

#: build/api.py:233
msgid "Completed after"
msgstr ""

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr ""

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr ""

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "排除樹"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "工單必須被取消才能被刪除"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "耗材"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "非必須項目"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "裝配"

#: build/api.py:450
msgid "Tracked"
msgstr "追蹤中"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "可測試"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr ""

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "已分配"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "可用數量"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "生產工單"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "地點"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "生產工單"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "裝配物料清單尚未驗證"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "無法為未激活的零件創建生產訂單"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "無法為已解鎖的零件創建生產訂單"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "必須指定負責的用户或組"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "無法更改生產工單"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:244
msgid "Build Order Reference"
msgstr "生產工單代號"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "參考代號"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "關於生產工單的簡單説明（選填）"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "這張生產工單對應的上層生產工單"

#: build/models.py:273
msgid "Select part to build"
msgstr "選擇要生產的零件"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "銷售訂單代號"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "這張生產工單對應的銷售訂單"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "來源倉儲地點"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "選擇領取料件的倉儲地點（留白表示可以從任何地點領取）"

#: build/models.py:300
msgid "External Build"
msgstr ""

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr "目標倉儲地點"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "選擇已完成項目庫存地點"

#: build/models.py:315
msgid "Build Quantity"
msgstr "生產數量"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "要生產的項目數量"

#: build/models.py:322
msgid "Completed items"
msgstr "已完成項目"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "已經完成的庫存品數量"

#: build/models.py:328
msgid "Build Status"
msgstr "生產狀態"

#: build/models.py:333
msgid "Build status code"
msgstr "生產狀態代碼"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "批號"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "此產出的批號"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "建立日期"

#: build/models.py:356
msgid "Build start date"
msgstr ""

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:363
msgid "Target completion date"
msgstr "目標完成日期"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "生產的預計完成日期。若超過此日期則工單會逾期。"

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "完成日期"

#: build/models.py:378
msgid "completed by"
msgstr "完成者"

#: build/models.py:387
msgid "Issued by"
msgstr "發布者"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "發布此生產工單的使用者"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "負責人"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "負責此生產工單的使用者或羣組"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "外部連結"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "外部URL連結"

#: build/models.py:410
msgid "Build Priority"
msgstr "製造優先度"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "此生產工單的優先程度"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "專案代碼"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "此生產工單隸屬的專案代碼"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "未能卸載任務以完成生產分配"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "生產工單 {build} 已經完成"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "一張生產工單已經完成"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "對於可跟蹤的零件，必須提供序列號"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "未指定產出"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "產出已完成"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "產出與生產訂單不匹配"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "數量必須大於零"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "數量不能大於輸出數量"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "產出 {serial} 未通過所有必要測試"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "生產訂單行項目"

#: build/models.py:1602
msgid "Build object"
msgstr "生產對象"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "數量"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "生產工單所需數量"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "生產項必須指定產出，因為主零件已經被標記為可追蹤的"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "分配的數量（{q}）不能超過可用的庫存數量（{a}）"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "庫存品項超額分配"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "分配的數量必須大於零"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "有序號的品項數量必須為1"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "選擇的庫存品項和BOM的項目不符"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "庫存品項"

#: build/models.py:1905
msgid "Source stock item"
msgstr "來源庫存項目"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "要分配的庫存數量"

#: build/models.py:1924
msgid "Install into"
msgstr "安裝到"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "目的庫存品項"

#: build/serializers.py:115
msgid "Build Level"
msgstr "構建等級"

#: build/serializers.py:124
msgid "Part Name"
msgstr "零件名稱"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "項目編碼標籤"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "產出"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "產出與之前的生產不匹配"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "產出零件與生產訂單零件不匹配"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "此產出已經完成"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "此產出尚未完全分配"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "輸入產出數量"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "可追蹤的零件數量必須為整數"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "因為BOM包含可追蹤的零件，所以數量必須為整數"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "序號"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "輸出產出的序列號"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "生產輸出的庫存地點"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "自動分配序號"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "自動為需要項目分配對應的序號"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "序號已存在或無效"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "必須提供產出清單"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "廢品產出的庫存位置"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "放棄分配"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "取消對廢品產出的任何庫存分配"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "廢品產出的原因"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "已完成刪除的庫存地點"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "接受不完整的分配"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "如果庫存尚未全部分配，則完成產出"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "消費已分配的庫存"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "消耗已分配給此生產的任何庫存"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "移除未完成的產出"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "刪除所有未完成的產出"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "不允許"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "接受作為此生產訂單的消費"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "完成此生產訂單前取消分配"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "超出分配的庫存"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "如何處理分配給生產訂單的額外庫存項"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "有庫存項目已被過度分配"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "接受未分配"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "接受庫存項未被完全分配至生產訂單"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "所需庫存尚未完全分配"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "接受不完整"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "允許所需數量的產出未完成"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "未完成所需生產數量"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "生產訂單有打開的子生產訂單"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "生產訂單必須處於生產狀態"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "生產訂單有未完成的產出"

#: build/serializers.py:869
msgid "Build Line"
msgstr "生產行"

#: build/serializers.py:877
msgid "Build output"
msgstr "產出"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "生產產出必須指向相同的生產"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "生產行項目"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part 必須與生產訂單零件相同"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "商品必須有庫存"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "可用量 ({q}) 超出限制"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "對於被追蹤的零件的分配，必須指定生產產出"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "對於未被追蹤的零件，無法指定生產產出"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "必須提供分配項目"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "零件來源的庫存地點(留空則可來源於任何庫存地點)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "排除位置"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "從該選定的庫存地點排除庫存項"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "可互換庫存"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "在多個位置的庫存項目可以互換使用"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "替代品庫存"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "允許分配可替換的零件"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "可選項目"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "分配可選的物料清單給生產訂單"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "啓動自動分配任務失敗"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "物料清單參考"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "物料清單零件識別號碼"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "物料清單零件名稱"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr ""

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "供應商零件"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "已分配數量"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "構建參考"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "零件類別名稱"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "可追蹤"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "已繼承的"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "允許變體"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "物料清單項"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "已訂購"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "生產中"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "外部庫存"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "可用庫存"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "可用的替代品庫存"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "可用的變體庫存"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "待定"

#: build/status_codes.py:12
msgid "Production"
msgstr "生產"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "被掛起"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "已取消"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "完成"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "生產訂單所需庫存"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "逾期的生產訂單"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "生產訂單 {bo} 現已逾期"

#: common/api.py:688
msgid "Is Link"
msgstr "是否鏈接"

#: common/api.py:696
msgid "Is File"
msgstr "是否為文件"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "用户沒有權限刪除此附件"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "用户沒有權限刪除此附件"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "無效的貨幣代碼"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "重複的貨幣代碼"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "未提供有效的貨幣代碼"

#: common/currency.py:146
msgid "No plugin"
msgstr "暫無插件"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "已是最新"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "最後更新時間戳"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "唯一項目編碼"

#: common/models.py:171
msgid "Project description"
msgstr "項目描述"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "負責此項目的用户或羣組"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr ""

#: common/models.py:780
msgid "Settings value"
msgstr "設定值"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "所選值不是一個有效的選項"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "該值必須是布爾值"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "該值必須為整數"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:914
msgid "Key string must be unique"
msgstr "鍵字符串必須是唯一的"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "使用者"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "批發價數量"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "價格"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "指定數量的單位價格"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "端點"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "接收此網絡鈎子的端點"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "此網絡鈎子的名稱"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "激活"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "網絡鈎子是否已啓用"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "令牌"

#: common/models.py:1437
msgid "Token for access"
msgstr "訪問令牌"

#: common/models.py:1445
msgid "Secret"
msgstr "密鑰"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "HMAC共享密鑰"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "消息ID"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "此郵件的唯一標識符"

#: common/models.py:1563
msgid "Host"
msgstr "主機"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "接收此消息的主機"

#: common/models.py:1572
msgid "Header"
msgstr "標題"

#: common/models.py:1573
msgid "Header of this message"
msgstr "此消息的標題"

#: common/models.py:1580
msgid "Body"
msgstr "正文"

#: common/models.py:1581
msgid "Body of this message"
msgstr "此消息的正文"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "接收此消息的終點"

#: common/models.py:1596
msgid "Worked on"
msgstr "工作於"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "這條消息的工作完成了嗎？"

#: common/models.py:1723
msgid "Id"
msgstr "標識"

#: common/models.py:1725
msgid "Title"
msgstr "標題"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "連結"

#: common/models.py:1729
msgid "Published"
msgstr "已發佈"

#: common/models.py:1731
msgid "Author"
msgstr "作者"

#: common/models.py:1733
msgid "Summary"
msgstr "摘要"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "閲讀"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "這條新聞被閲讀了嗎？"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "圖像"

#: common/models.py:1753
msgid "Image file"
msgstr "圖像文件"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "此圖像的目標模型類型"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "此圖像的目標型號ID"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "自定義單位"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "單位符號必須唯一"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "單位名稱必須是有效的標識符"

#: common/models.py:1843
msgid "Unit name"
msgstr "單位名稱"

#: common/models.py:1850
msgid "Symbol"
msgstr "符號"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "可選單位符號"

#: common/models.py:1857
msgid "Definition"
msgstr "定義"

#: common/models.py:1858
msgid "Unit definition"
msgstr "單位定義"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "附件"

#: common/models.py:1933
msgid "Missing file"
msgstr "缺少檔案"

#: common/models.py:1934
msgid "Missing external link"
msgstr "缺少外部連結"

#: common/models.py:1971
msgid "Model type"
msgstr ""

#: common/models.py:1972
msgid "Target model type for image"
msgstr ""

#: common/models.py:1980
msgid "Select file to attach"
msgstr "選擇附件"

#: common/models.py:1996
msgid "Comment"
msgstr "註解"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "附件評論"

#: common/models.py:2013
msgid "Upload date"
msgstr "上傳日期"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "上傳文件的日期"

#: common/models.py:2018
msgid "File size"
msgstr "文件大小"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "文件大小，以字節為單位"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "為附件指定的模型類型無效"

#: common/models.py:2077
msgid "Custom State"
msgstr "自定狀態"

#: common/models.py:2078
msgid "Custom States"
msgstr "定製狀態"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "參考狀態設定"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "使用此自定義狀態擴展狀態的狀態集"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "邏輯密鑰"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "等同於商業邏輯中自定義狀態的狀態邏輯鍵"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "值"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2102
msgid "Name of the state"
msgstr "狀態名"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "標籤"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "在前端顯示的標籤"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "顏色"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "將在前端顯示顏色"

#: common/models.py:2128
msgid "Model"
msgstr "模式"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "該狀態關聯的模型"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "必須選定模型"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "必須選取密鑰"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "必須選中邏輯密鑰"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "密鑰必須不同於邏輯密鑰"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "密鑰必須不同於參考狀態的邏輯密鑰"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "邏輯密鑰必須在參考狀態的邏輯鍵中"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr ""

#: common/models.py:2222
msgid "Selection Lists"
msgstr ""

#: common/models.py:2227
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2234
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "已鎖定"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2255
msgid "Source Plugin"
msgstr ""

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2261
msgid "Source String"
msgstr ""

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2271
msgid "Default Entry"
msgstr ""

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "已創建"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2283
msgid "Last Updated"
msgstr "最近更新"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2318
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2319
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "掃描條碼"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "數據"

#: common/models.py:2377
msgid "Barcode data"
msgstr "條碼數據"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "掃描條碼"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "時間戳"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "掃描條碼的日期和時間"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "處理條碼的 URL 終點"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "上下文"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "掃描條碼的上下文數據"

#: common/models.py:2415
msgid "Response"
msgstr "響應"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "掃描條碼的響應數據"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "結果"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "條碼掃描成功嗎？"

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr ""

#: common/models.py:2574
msgid "Email Messages"
msgstr ""

#: common/models.py:2581
msgid "Announced"
msgstr ""

#: common/models.py:2583
msgid "Sent"
msgstr ""

#: common/models.py:2584
msgid "Failed"
msgstr ""

#: common/models.py:2587
msgid "Delivered"
msgstr ""

#: common/models.py:2595
msgid "Confirmed"
msgstr ""

#: common/models.py:2601
msgid "Inbound"
msgstr ""

#: common/models.py:2602
msgid "Outbound"
msgstr ""

#: common/models.py:2607
msgid "No Reply"
msgstr ""

#: common/models.py:2608
msgid "Track Delivery"
msgstr ""

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr ""

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr ""

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr ""

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr ""

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "鍵"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "新建{verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "新訂單已創建並分配給您"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} 已取消"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "分配給您的訂單已取消"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "收到的物品"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "已根據採購訂單收到物品"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "已收到退貨訂單中的物品"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr ""

#: common/serializers.py:486
msgid "Is Running"
msgstr "正在運行"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "等待完成的任務"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "預定的任務"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "失敗的任務"

#: common/serializers.py:519
msgid "Task ID"
msgstr "任務ID"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "唯一任務ID"

#: common/serializers.py:521
msgid "Lock"
msgstr "鎖定"

#: common/serializers.py:521
msgid "Lock time"
msgstr "鎖定時間"

#: common/serializers.py:523
msgid "Task name"
msgstr "任務名稱"

#: common/serializers.py:525
msgid "Function"
msgstr "功能"

#: common/serializers.py:525
msgid "Function name"
msgstr "功能名稱"

#: common/serializers.py:527
msgid "Arguments"
msgstr "參數"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "任務參數"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "關鍵字參數"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "任務關鍵詞參數"

#: common/serializers.py:640
msgid "Filename"
msgstr "檔案名稱"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "模型類型"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "用户無權為此模式創建或編輯附件"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "無分組"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "網站 URL 已配置為鎖定"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "需要重啓"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "設置已更改，需要服務器重啓"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "等待遷移"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "待處理的數據庫遷移數"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:199
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "服務器實例名稱"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "服務器實例的字符串描述符"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "使用實例名稱"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "在標題欄中使用實例名稱"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "限制顯示 `關於` 信息"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "只向超級管理員顯示關於信息"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "公司名稱"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "內部公司名稱"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "基本 URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "服務器實例的基準 URL"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "默認貨幣單位"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "選擇價格計算的默認貨幣"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "支持幣種"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "支持的貨幣代碼列表"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "貨幣更新間隔時間"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "檢查更新的頻率(設置為零以禁用)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "天"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "幣種更新插件"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "使用貨幣更新插件"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "從URL下載"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "允許從外部 URL 下載遠程圖片和文件"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "下載大小限制"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "遠程圖片的最大允許下載大小"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "用於從 URL 下載的 User-agent"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "允許覆蓋用於從外部 URL 下載圖片和文件的 user-agent(留空為默認值)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "嚴格的 URL 驗證"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "驗證 URL 時需要 schema 規範"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "更新檢查間隔"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "檢查更新的頻率(設置為零以禁用)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "自動備份"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "啟動資料庫和媒體文件自動備份"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "自動備份間隔"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "指定自動備份之間的間隔天數"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "任務刪除間隔"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "後台任務結果將在指定天數後刪除"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "錯誤日誌刪除間隔"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "錯誤日誌將在指定天數後被刪除"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "通知刪除間隔"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "用户通知將在指定天數後被刪除"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "條形碼支持"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "在網頁界面啓用條形碼掃描器支持"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "存儲條碼結果"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "存儲條碼掃描結果"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "條碼掃描最大計數"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "存儲條碼掃描結果的最大數量"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "條形碼掃描延遲設置"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "條形碼輸入處理延遲時間"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "條碼攝像頭支持"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "允許通過網絡攝像頭掃描條形碼"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "條形碼顯示數據"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "在瀏覽器中將條形碼數據顯示為文本"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "條形碼生成插件"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "用於內部條形碼數據生成的插件"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "零件修訂"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "啓用零件修訂字段"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "僅限裝配修訂版本"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "僅允許對裝配零件進行修訂"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "允許從裝配中刪除"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "允許刪除已在裝配中使用的零件"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN 內部零件號"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "匹配零件 IPN（內部零件號）的正則表達式模式"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "允許重複的 IPN（內部零件號）"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "允許多個零件共享相同的 IPN（內部零件號）"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "允許編輯 IPN（內部零件號）"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "允許編輯零件時更改內部零件號"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "複製零件物料清單數據"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "複製零件時默認複製物料清單數據"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "複製零件參數數據"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "複製零件時默認複製參數數據"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "複製零件測試數據"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "複製零件時默認複製測試數據"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "複製類別參數模板"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "創建零件時複製類別參數模板"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "模板"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "零件默認為模板"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "默認情況下，元件可由其他零件組裝而成"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "組件"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "默認情況下，零件可用作子部件"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "可購買"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "默認情況下可購買零件"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "可銷售"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "零件默認為可銷售"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "默認情況下可跟蹤零件"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "虛擬的"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "默認情況下，零件是虛擬的"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "顯示相關零件"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "顯示零件的相關零件"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "初始庫存數據"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "允許在添加新零件時創建初始庫存"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "初始供應商數據"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "允許在添加新零件時創建初始供應商數據"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "零件名稱顯示格式"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "顯示零件名稱的格式"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "零件類別默認圖標"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "零件類別默認圖標 (空表示沒有圖標)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "強制參數單位"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "如果提供了單位，參數值必須與指定的單位匹配"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "最小定價小數位數"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "呈現定價數據時顯示的最小小數位數"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "最大定價小數位數"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "呈現定價數據時顯示的最大小數位數"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "使用供應商定價"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "將供應商的價批發價納入總體定價計算中"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "購買歷史記錄覆蓋"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "歷史採購訂單定價優先於供應商批發價"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "使用庫存項定價"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "使用手動輸入的庫存數據進行定價計算"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "庫存項目定價時間"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "從定價計算中排除超過此天數的庫存項目"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "使用變體定價"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "在整體定價計算中包括變體定價"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "僅限活躍變體"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "僅使用活躍變體零件計算變體價格"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "價格重建間隔"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "零件價格自動更新前的天數"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "內部價格"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "啓用內部零件價格"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "覆蓋內部價格"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "如果有內部價格，內部價格將覆蓋價格範圍計算"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "啓用標籤打印功能"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "啓用從網絡界面打印標籤"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "標籤圖片 DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "生成圖像文件以供標籤打印插件使用時的 DPI 分辨率"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "啓用報告"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "啓用報告生成"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "調試模式"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "以調試模式生成報告（HTML 輸出）"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "日誌錯誤報告"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "記錄生成報告時出現的錯誤"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "頁面大小"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "PDF 報告默認頁面大小"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "全局唯一序列號"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "庫存項的序列號必須全局唯一"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "刪除已耗盡的庫存"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "設置庫存耗盡時的默認行為"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "批號模板"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "為庫存項生成默認批號的模板"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "庫存過期"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "啓用庫存過期功能"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "銷售過期庫存"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "允許銷售過期庫存"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "庫存過期時間"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "庫存項在到期前被視為過期的天數"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "生產過期庫存"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "允許用過期的庫存生產"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "庫存所有權控制"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "啓用庫存地點和項目的所有權控制"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "庫存地點默認圖標"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "庫存地點默認圖標 (空表示沒有圖標)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "顯示已安裝的庫存項"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "在庫存表中顯示已安裝的庫存項"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "在安裝項目時檢查物料清單"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "已安裝的庫存項目必須存在於上級零件的物料清單中"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "允許超出庫存轉移"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "允許非庫存的庫存項目在庫存位置之間轉移"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "生產訂單參考模式"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "生成生產訂單參考字段所需的模式"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "要求負責人"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "必須為每個訂單分配一個負責人"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "需要活動零件"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "防止為非活動零件創建生產訂單"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "需要鎖定零件"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "防止為未鎖定的零件創建生產訂單"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "需要有效的物料清單"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "除非物料清單已驗證，否則禁止創建生產訂單"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "需要關閉子訂單"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "在所有子訂單關閉之前，阻止生產訂單的完成"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "阻止直到測試通過"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "在所有必要的測試通過之前，阻止產出完成"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "啓用訂單退貨"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "在用户界面中啓用訂單退貨功能"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "退貨訂單參考模式"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "生成退貨訂單參考字段所需的模式"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "編輯已完成的退貨訂單"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "允許編輯已完成的退貨訂單"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "銷售訂單參考模式"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "生成銷售訂單參考字段所需參照模式"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "銷售訂單默認配送方式"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "啓用創建銷售訂單的默認配送功能"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "編輯已完成的銷售訂單"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "允許在訂單配送或完成後編輯銷售訂單"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "標記該訂單為已完成？"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "標記為已發貨的銷售訂單將自動完成，繞過“已發貨”狀態"

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "採購訂單參考模式"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "生成採購訂單參考字段所需的模式"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "編輯已完成的採購訂單"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "允許在採購訂單已配送或完成後編輯訂單"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "自動完成採購訂單"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "當收到所有行項目時，自動將採購訂單標記為完成"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "忘記啓用密碼"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "在登錄頁面上啓用忘記密碼功能"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "啓用註冊"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "在登錄頁面為用户啓用自行註冊功能"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "啓用單點登錄"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "在登錄界面啓用單點登錄"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "啓用單點登錄註冊"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "允許登錄頁面上的用户通過 SSO 進行自我註冊"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "啓用單點登錄羣組同步"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "啓用庫存管理系統組和由身份提供者提供的組的同步功能"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "單點登錄系統組密鑰"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "由身份提供者提供的組聲明屬性名稱"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "單點登錄系統組地圖"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "從單點登錄系統組組到本地庫存管理系統組的映射。如果本地組不存在，它將被創建。"

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "移除單點登錄系統以外的羣組"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "如果分配給用户的組不是身份提供者的後端，是否應該刪除它們。禁用此設置可能會造成安全問題"

#: common/setting/system.py:959
msgid "Email required"
msgstr "需要郵箱地址"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "要求用户在註冊時提供郵件"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "自動填充單點登錄系統用户"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "自動使用單點登錄系統賬户的數據填寫用户詳細信息"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "發兩次郵件"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "註冊時詢問用户他們的電子郵件兩次"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "兩次輸入密碼"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "當註冊時請用户輸入密碼兩次"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "域名白名單"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "限制註冊到某些域名 (逗號分隔，以 @ 開頭)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "註冊羣組"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "註冊時分配給新用户的組。 如果啓用了單點登錄系統羣組同步，此羣組僅在無法從 IdP 分配任何羣組的情況下才被設置。"

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "強制啓用多因素安全認證"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "用户必須使用多因素安全認證。"

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "啓動時檢查插件"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "啓動時檢查全部插件是否已安裝 - 在容器環境中啓用"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "檢查插件更新"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "啓用定期檢查已安裝插件的更新"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "啓用統一資源定位符集成"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "啓用插件以添加統一資源定位符路由"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "啓用導航集成"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "啓用插件以集成到導航中"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "啓用應用集成"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "啓用插件添加應用"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "啓用調度集成"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "啓用插件來運行預定任務"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "啓用事件集成"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "啓用插件響應內部事件"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "啓用界面集成"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "啓用插件集成到用户界面"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "排除外部地點"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "自動盤點週期"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "顯示用户全名"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "顯示用户全名而不是用户名"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "啓用測試站數據"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "啓用測試站數據收集以獲取測試結果"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "內聯標籤顯示"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "在瀏覽器中顯示PDF標籤，而不是作為文件下載"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "默認標籤打印機"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "配置默認情況下應選擇哪個標籤打印機"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "內聯報告顯示"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "在瀏覽器中顯示PDF報告，而不是作為文件下載"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "搜索零件"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "在搜索預覽窗口中顯示零件"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "搜索供應商零件"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "在搜索預覽窗口中顯示供應商零件"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "搜索製造商零件"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "在搜索預覽窗口中顯示製造商零件"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "隱藏非活動零件"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "從搜索預覽窗口中排除非活動零件"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "搜索分類"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "在搜索預覽窗口中顯示零件類別"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "搜索庫存"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "在搜索預覽窗口中顯示庫存項目"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "隱藏不可用的庫存項目"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "排除搜索預覽窗口中不可用的庫存項目"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "搜索地點"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "在搜索預覽窗口中顯示庫存位置"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "搜索公司"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "在搜索預覽窗口中顯示公司"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "搜索生產訂單"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "在搜索預覽窗口中顯示生產訂單"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "搜索採購訂單"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "在搜索預覽窗口中顯示採購訂單"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "排除未激活的採購訂單"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "從搜索預覽窗口中排除不活動的採購訂單"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "搜索銷售訂單"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "在搜索預覽窗口中顯示銷售訂單"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "排除未激活的銷售訂單"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "從搜索預覽窗口中排除不活動的銷售訂單"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "搜索退貨訂單"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "在搜索預覽窗口中顯示退貨訂單"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "排除未激活的退貨訂單"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "從搜索預覽窗口中排除不活動的退貨訂單"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "搜索預覽結果"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "在搜索預覽窗口的每個部分中顯示的結果數"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "正則表達式搜索"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "在搜索查詢中啓用正則表達式"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "整詞搜索"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "搜索查詢返回整詞匹配的結果"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Esc鍵關閉窗體"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "使用ESC鍵關閉模態窗體"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "固定導航欄"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "導航欄位置固定在屏幕頂部"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:207
msgid "Date Format"
msgstr "時間格式"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "顯示時間的首選格式"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "接收錯誤報告"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "接收系統錯誤通知"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "上次使用的打印設備"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "為用户保存上次使用的打印設備"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "未提供附件型號"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "附件模型類型無效"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "最小位置不能大於最大位置"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "最大名額不能小於最小名額"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "不允許空域。"

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "無效的域名: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "零件已激活"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "製造商處於活動狀態"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "供應商零件處於激活狀態"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "內部零件已激活"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "供應商已激活"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "製造商"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "公司"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:120
msgid "Companies"
msgstr "公司"

#: company/models.py:148
msgid "Company description"
msgstr "公司簡介"

#: company/models.py:149
msgid "Description of the company"
msgstr "公司簡介"

#: company/models.py:155
msgid "Website"
msgstr "網站"

#: company/models.py:156
msgid "Company website URL"
msgstr "公司網站"

#: company/models.py:162
msgid "Phone number"
msgstr "電話號碼"

#: company/models.py:164
msgid "Contact phone number"
msgstr "聯繫電話"

#: company/models.py:171
msgid "Contact email address"
msgstr "聯繫人電子郵箱地址"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "聯繫人"

#: company/models.py:178
msgid "Point of contact"
msgstr "聯絡點"

#: company/models.py:184
msgid "Link to external company information"
msgstr "外部公司信息鏈接"

#: company/models.py:198
msgid "Is this company active?"
msgstr "這家公司是否激活？"

#: company/models.py:203
msgid "Is customer"
msgstr "是客户"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "你是否向該公司出售商品？"

#: company/models.py:209
msgid "Is supplier"
msgstr "是否為供應商"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "你從這家公司買東西嗎？"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "是製造商嗎"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "這家公司生產零件嗎？"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "此公司使用的默認貨幣"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "地址"

#: company/models.py:355
msgid "Addresses"
msgstr "地址"

#: company/models.py:412
msgid "Select company"
msgstr "選擇公司"

#: company/models.py:417
msgid "Address title"
msgstr "地址標題"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "描述地址條目的標題"

#: company/models.py:424
msgid "Primary address"
msgstr "主要地址"

#: company/models.py:425
msgid "Set as primary address"
msgstr "設置主要地址"

#: company/models.py:430
msgid "Line 1"
msgstr "第1行"

#: company/models.py:431
msgid "Address line 1"
msgstr "地址行1"

#: company/models.py:437
msgid "Line 2"
msgstr "第2行"

#: company/models.py:438
msgid "Address line 2"
msgstr "地址行2"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "郵政編碼"

#: company/models.py:451
msgid "City/Region"
msgstr "城市/地區"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "郵政編碼城市/地區"

#: company/models.py:458
msgid "State/Province"
msgstr "省/市/自治區"

#: company/models.py:459
msgid "State or province"
msgstr "省、自治區或直轄市"

#: company/models.py:465
msgid "Country"
msgstr "國家/地區"

#: company/models.py:466
msgid "Address country"
msgstr "地址所在國家"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "快遞運單"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "運輸快遞注意事項"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "內部裝運通知單"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "內部使用的裝運通知單"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "鏈接地址信息 (外部)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "製造商零件"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "基礎零件"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "選擇零件"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "選擇製造商"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "製造商零件編號"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "製造商零件編號"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "外部製造商零件鏈接的URL"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "製造商零件説明"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "製造商零件參數"

#: company/models.py:635
msgid "Parameter name"
msgstr "參數名稱"

#: company/models.py:642
msgid "Parameter value"
msgstr "參數值"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "單位"

#: company/models.py:650
msgid "Parameter units"
msgstr "參數單位"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "包裝單位必須與基礎零件單位兼容"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "包裝單位必須大於零"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "鏈接的製造商零件必須引用相同的基礎零件"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "供應商"

#: company/models.py:829
msgid "Select supplier"
msgstr "選擇供應商"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "供應商庫存管理單位"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "此供應商零件是否處於活動狀態？"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "選擇製造商零件"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "外部供應商零件鏈接的URL"

#: company/models.py:867
msgid "Supplier part description"
msgstr "供應商零件説明"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "備註"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "基本費用"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "最低費用(例如庫存費)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "打包"

#: company/models.py:892
msgid "Part packaging"
msgstr "零件打包"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "包裝數量"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "單包供應的總數量。為單個項目留空。"

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "多個"

#: company/models.py:919
msgid "Order multiple"
msgstr "訂購多個"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "供應商提供的數量"

#: company/models.py:937
msgid "Availability Updated"
msgstr "可用性已更新"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "上次更新可用性數據的日期"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "供應商批發價"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "此供應商使用的默認貨幣"

#: company/serializers.py:245
msgid "Company Name"
msgstr "公司名稱"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "有庫存"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "此項目的附加狀態信息"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "自定義狀態密鑰"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "放置"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:73
msgid "Data File"
msgstr "數據文件"

#: importer/models.py:74
msgid "Data file to import"
msgstr "要導入的數據文件"

#: importer/models.py:83
msgid "Columns"
msgstr "列"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:96
msgid "Import status"
msgstr "導入狀態"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "字段默認值"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "字段覆蓋"

#: importer/models.py:120
msgid "Field Filters"
msgstr "字段篩選器"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "某些必填字段尚未映射"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "列已映射到數據庫字段"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "字段已映射到數據列"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "列映射必須鏈接到有效的導入會話"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "數據文件中不存在列"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "目標模型中不存在字段"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "所選字段為只讀"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "導入會話"

#: importer/models.py:471
msgid "Field"
msgstr "字段"

#: importer/models.py:473
msgid "Column"
msgstr "列"

#: importer/models.py:542
msgid "Row Index"
msgstr "行索引"

#: importer/models.py:545
msgid "Original row data"
msgstr "原始行數據"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "錯誤"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "有效"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "不支持的數據文件格式"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "打開數據文件失敗"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "數據文件維度無效"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "字段默認值無效"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "無效的字段覆蓋"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "字段篩選器無效"

#: importer/serializers.py:177
msgid "Rows"
msgstr "行"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "要接受的行ID列表"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "未提供行"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "行不屬於此會話"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "行包含無效數據"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "行已完成"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "正在初始化"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "映射列"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "導入數據"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "處理數據中"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "數據文件超出最大大小限制"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "數據文件不包含標頭"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "數據文件包含的列太多"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "數據文件包含的行太多"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "值必須是有效的字典對象"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "拷貝"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "每個標籤要打印的份數"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "已連接"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "未知"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "正在打印"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "無媒體"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "卡紙"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "已斷開連接"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "標籤打印機"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "直接打印各種物品的標籤。"

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "打印機位置"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "將打印機定位到特定位置"

#: machine/models.py:25
msgid "Name of machine"
msgstr "設備名稱"

#: machine/models.py:29
msgid "Machine Type"
msgstr "設備類型"

#: machine/models.py:29
msgid "Type of machine"
msgstr "設備類型"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "驅動"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "設備使用的驅動器"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "可以禁用設備"

#: machine/models.py:95
msgid "Driver available"
msgstr "可用驅動"

#: machine/models.py:100
msgid "No errors"
msgstr "無錯誤"

#: machine/models.py:105
msgid "Initialized"
msgstr "已初始化"

#: machine/models.py:117
msgid "Machine status"
msgstr "設備狀態"

#: machine/models.py:145
msgid "Machine"
msgstr "設備"

#: machine/models.py:157
msgid "Machine Config"
msgstr "設備配置"

#: machine/models.py:162
msgid "Config type"
msgstr "配置類型"

#: order/api.py:121
msgid "Order Reference"
msgstr "訂單參考"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "未完成"

#: order/api.py:165
msgid "Has Project Code"
msgstr "有項目編碼"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "創建人"

#: order/api.py:183
msgid "Created Before"
msgstr ""

#: order/api.py:187
msgid "Created After"
msgstr ""

#: order/api.py:191
msgid "Has Start Date"
msgstr ""

#: order/api.py:199
msgid "Start Date Before"
msgstr ""

#: order/api.py:203
msgid "Start Date After"
msgstr ""

#: order/api.py:207
msgid "Has Target Date"
msgstr ""

#: order/api.py:215
msgid "Target Date Before"
msgstr ""

#: order/api.py:219
msgid "Target Date After"
msgstr ""

#: order/api.py:270
msgid "Has Pricing"
msgstr "有定價"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr ""

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr ""

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "訂單"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "訂單完成"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "內部零件"

#: order/api.py:578
msgid "Order Pending"
msgstr "訂單待定"

#: order/api.py:958
msgid "Completed"
msgstr "已完成"

#: order/api.py:1214
msgid "Has Shipment"
msgstr ""

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "採購訂單"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "銷售訂單"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "退貨訂單"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "總價格"

#: order/models.py:91
msgid "Total price for this order"
msgstr "此訂單的總價"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "訂單貨幣"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "此訂單的貨幣 (留空以使用公司默認值)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "聯繫人與所選公司不匹配"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:436
msgid "Order description (optional)"
msgstr "訂單描述 (可選)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "為此訂單選擇項目編碼"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "鏈接到外部頁面"

#: order/models.py:458
msgid "Start date"
msgstr ""

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "預計日期"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "訂單交付的預期日期。訂單將在此日期後過期。"

#: order/models.py:487
msgid "Issue Date"
msgstr "簽發日期"

#: order/models.py:488
msgid "Date order was issued"
msgstr "訂單發出日期"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "負責此訂單的用户或組"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "此訂單的聯繫人"

#: order/models.py:517
msgid "Company address for this order"
msgstr "此訂單的公司地址"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "訂單參考"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "狀態"

#: order/models.py:618
msgid "Purchase order status"
msgstr "採購訂單狀態"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "訂購物品的公司"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "供應商參考"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "供應商訂單參考代碼"

#: order/models.py:654
msgid "received by"
msgstr "接收人"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "訂單完成日期"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "目的地"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr ""

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "零件供應商必須與採購訂單供應商匹配"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "行項目與採購訂單不匹配"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "數量必須是正數"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "客户"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "出售物品的公司"

#: order/models.py:1318
msgid "Sales order status"
msgstr "銷售訂單狀態"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "客户參考 "

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "客户訂單參考代碼"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "發貨日期"

#: order/models.py:1343
msgid "shipped by"
msgstr "發貨人"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "訂單已完成"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "訂單已取消"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "只有未結訂單才能標記為已完成"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "由於發貨不完整，訂單無法完成"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "訂單無法完成，因為行項目不完整"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1711
msgid "Item quantity"
msgstr "項目數量"

#: order/models.py:1728
msgid "Line item reference"
msgstr "行項目參考"

#: order/models.py:1735
msgid "Line item notes"
msgstr "行項目註釋"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "此行項目的目標日期 (留空以使用訂單中的目標日期)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "行項目描述 (可選)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "此行的附加上下文"

#: order/models.py:1788
msgid "Unit price"
msgstr "單位價格"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "採購訂單行項目"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "供應商零件必須與供應商匹配"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr "供應商零件"

#: order/models.py:1891
msgid "Received"
msgstr "已接收"

#: order/models.py:1892
msgid "Number of items received"
msgstr "收到的物品數量"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "採購價格"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "每單位的採購價格"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "採購訂單附加行"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "銷售訂單行項目"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "虛擬零件不能分配給銷售訂單"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "只有可銷售的零件才能分配給銷售訂單"

#: order/models.py:2063
msgid "Sale Price"
msgstr "售出價格"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "單位售出價格"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "已配送"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "發貨數量"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "銷售訂單發貨"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "發貨日期"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "送達日期"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "裝運交貨日期"

#: order/models.py:2221
msgid "Checked By"
msgstr "審核人"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "檢查此裝運的用户"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "配送"

#: order/models.py:2230
msgid "Shipment number"
msgstr "配送單號"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "跟蹤單號"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "配送跟蹤信息"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "發票編號"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "相關發票的參考號"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "貨物已發出"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "發貨沒有分配庫存項目"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "銷售訂單加行"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "銷售訂單分配"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "庫存項目尚未分配"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "無法將庫存項目分配給具有不同零件的行"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "無法將庫存分配給沒有零件的生產線"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "分配數量不能超過庫存數量"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "序列化庫存項目的數量必須為1"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "銷售訂單與發貨不匹配"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "發貨與銷售訂單不匹配"

#: order/models.py:2451
msgid "Line"
msgstr "行"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "銷售訂單發貨參考"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "項目"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "選擇要分配的庫存項目"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "輸入庫存分配數量"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "退貨訂單參考"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "退回物品的公司"

#: order/models.py:2625
msgid "Return order status"
msgstr "退貨訂單狀態"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "退貨訂單行項目"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "選擇要從客户處退回的商品"

#: order/models.py:2910
msgid "Received Date"
msgstr "接收日期"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "收到此退貨的日期"

#: order/models.py:2923
msgid "Outcome"
msgstr "結果"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "該行項目的結果"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "與此行項目的退貨或維修相關的成本"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr "退貨訂單附加行"

#: order/serializers.py:90
msgid "Order ID"
msgstr "訂單ID"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "要複製的訂單ID"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "複製行"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr "從原始訂單複製行項目"

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr "複製額外行"

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr "從原始訂單複製額外的行項目"

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "行項目"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "已完成行項目"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "複製訂單"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr "指定複製此訂單的選項"

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "訂單ID不正確"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "供應商名稱"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "訂單不能取消"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "允許關閉行項目不完整的訂單"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "訂單中的行項目不完整"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "訂單未打開"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "自動定價"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "根據供應商零件數據自動計算採購價格"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "購買價格貨幣"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "合併項目"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "將具有相同零件、目的地和目標日期的項目合併到一個行項目中"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "庫存量單位"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "內部零件編號"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "內部零件名稱"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "必須指定供應商零件"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "必須指定採購訂單"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "供應商必須匹配採購訂單"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "採購訂單必須與供應商匹配"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "行項目"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "為收到的物品選擇目的地位置"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "輸入入庫項目的批號"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "有效期至"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "輸入入庫庫存項目的序列號"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr "覆蓋傳入庫存項目的包裝資料"

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr "傳入庫存項目的附加説明"

#: order/serializers.py:826
msgid "Barcode"
msgstr "條形碼"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "掃描條形碼"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "條形碼已被使用"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "必須提供行項目"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "必須指定目標位置"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "提供的條形碼值必須是唯一的"

#: order/serializers.py:1066
msgid "Shipments"
msgstr ""

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "完成配送"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "售出價格貨幣"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "未提供裝運詳細信息"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "行項目與此訂單不關聯"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "數量必須為正"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "輸入要分配的序列號"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "貨物已發出"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "發貨與此訂單無關"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "未找到以下序列號的匹配項"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "以下序列號不可用"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "退貨訂單行項目"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "行項目與退貨訂單不匹配"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "行項目已收到"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "只能根據正在進行的訂單接收物品"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "行價格貨幣"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "丟失"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "已退回"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "正在進行"

#: order/status_codes.py:105
msgid "Return"
msgstr "退回"

#: order/status_codes.py:108
msgid "Repair"
msgstr "維修"

#: order/status_codes.py:111
msgid "Replace"
msgstr "替換"

#: order/status_codes.py:114
msgid "Refund"
msgstr "退款"

#: order/status_codes.py:117
msgid "Reject"
msgstr "拒絕"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "逾期採購訂單"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "採購訂單 {po} 已逾期"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "逾期銷售訂單"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "銷售訂單 {so} 已逾期"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:111
msgid "Starred"
msgstr "已加星標"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "按星標類別篩選"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "深度"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "按類別深度篩選"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "頂級"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "按頂級類別篩選"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "級聯"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "在篩選結果中包含子類別"

#: part/api.py:185
msgid "Parent"
msgstr "父類"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "按父類別篩選"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "排除指定類別下的子類別"

#: part/api.py:434
msgid "Has Results"
msgstr "有結果"

#: part/api.py:660
msgid "Is Variant"
msgstr ""

#: part/api.py:668
msgid "Is Revision"
msgstr "是修訂版本"

#: part/api.py:678
msgid "Has Revisions"
msgstr "有修訂版本"

#: part/api.py:859
msgid "BOM Valid"
msgstr "物料清單合規"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "裝配部份是可測試的"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "組件部份是可測試的"

#: part/api.py:1576
msgid "Uses"
msgstr "使用"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "零件類別"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "零件類別"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "默認位置"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "此類別零件的默認庫存地點"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "結構性"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "零件可能無法直接分配到結構類別，但可以分配到子類別。"

#: part/models.py:134
msgid "Default keywords"
msgstr "默認關鍵字"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "此類別零件的默認關鍵字"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "圖標"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "圖標(可選)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "您不能使這個零件類別結構化，因為有些零件已經分配給了它！"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "零件"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "無法刪除這個零件，因為它已被鎖定"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "無法刪除這個零件，因為它仍然處於活動狀態"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "無法刪除這個零件，因為它被使用在了裝配中"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "零件 \"{self}\" 不能用在 \"{parent}\" 的物料清單 (遞歸)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "零件 \"{parent}\" 被使用在了 \"{self}\" 的物料清單 (遞歸)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "內部零件號必須匹配正則表達式 {pattern}"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "零件不能是對自身的修訂"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "無法對已經是修訂版本的零件進行修訂"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "必須指定修訂代碼"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "修訂僅對裝配零件允許"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "無法對模版零件進行修訂"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "上級零件必須指向相同的模版"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "該序列號庫存項己存在"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "在零件設置中不允許重複的內部零件號"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "重複的零件修訂版本已經存在。"

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "有這個名字，內部零件號，和修訂版本的零件已經存在"

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "零件不能分配到結構性零件類別！"

#: part/models.py:1051
msgid "Part name"
msgstr "零件名稱"

#: part/models.py:1056
msgid "Is Template"
msgstr "是模板"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "這個零件是一個模版零件嗎?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "這個零件是另一零件的變體嗎？"

#: part/models.py:1068
msgid "Variant Of"
msgstr "變體"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "零件描述(可選)"

#: part/models.py:1082
msgid "Keywords"
msgstr "關鍵詞"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "提高搜索結果可見性的零件關鍵字"

#: part/models.py:1093
msgid "Part category"
msgstr "零件類別"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "內部零件號 IPN"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "零件修訂版本或版本號"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "版本"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "這零件是另一零件的修訂版本嗎？"

#: part/models.py:1119
msgid "Revision Of"
msgstr "修訂版本"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "該物品通常存放在哪裏？"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "默認供應商"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "默認供應商零件"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "默認到期"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "此零件庫存項的過期時間 (天)"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "最低庫存"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "允許的最小庫存量"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "此零件的計量單位"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "這個零件可由其他零件加工而成嗎？"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "這個零件可用於創建其他零件嗎？"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "此零件是否有唯一物品的追蹤功能"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "這一部分能否記錄到測試結果？"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "這個零件可從外部供應商購買嗎？"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "此零件可以銷售給客户嗎?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "這個零件是否已激活？"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "無法編輯鎖定的零件"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "這是一個虛擬零件，例如一個軟件產品或許可證嗎？"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "物料清單校驗和"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "保存的物料清單校驗和"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "物料清單檢查人"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "物料清單檢查日期"

#: part/models.py:1312
msgid "Creation User"
msgstr "新建用户"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "此零件的負責人"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "出售多個"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "用於緩存定價計算的貨幣"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "最低物料清單成本"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "元件的最低成本"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "物料清單的最高成本"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "元件的最高成本"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "最低購買成本"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "最高歷史購買成本"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "最大購買成本"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "最高歷史購買成本"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "最低內部價格"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "基於內部批發價的最低成本"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "最大內部價格"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "基於內部批發價的最高成本"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "供應商最低價格"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "外部供應商零件的最低價格"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "供應商最高價格"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "來自外部供應商的商零件的最高價格"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "最小變體成本"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "計算出的變體零件的最低成本"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "最大變體成本"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "計算出的變體零件的最大成本"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "最低成本"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "覆蓋最低成本"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "最高成本"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "覆蓋最大成本"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "計算總最低成本"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "計算總最大成本"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "最低售出價格"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "基於批發價的最低售出價格"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "最高售出價格"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "基於批發價的最大售出價格"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "最低銷售成本"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "歷史最低售出價格"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "最高銷售成本"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "歷史最高售出價格"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "用於盤點的零件"

#: part/models.py:3444
msgid "Item Count"
msgstr "物品數量"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "盤點時的個別庫存條目數"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "盤點時可用庫存總額"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "日期"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "進行盤點的日期"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "最低庫存成本"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "現有存庫存最低成本估算"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "最高庫存成本"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "目前庫存最高成本估算"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "零件售出價格折扣"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "零件測試模板"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "模板名稱無效 - 必須包含至少一個字母或者數字"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "選擇必須是唯一的"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "測試模板只能為可拆分的部件創建"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "零件已存在具有相同主鍵的測試模板"

#: part/models.py:3684
msgid "Test Name"
msgstr "測試名"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "輸入測試的名稱"

#: part/models.py:3691
msgid "Test Key"
msgstr "測試主鍵"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "簡化測試主鍵"

#: part/models.py:3699
msgid "Test Description"
msgstr "測試説明"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "輸入測試的描述"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "已啓用"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "此測試是否已啓用？"

#: part/models.py:3709
msgid "Required"
msgstr "必須的"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "需要此測試才能通過嗎？"

#: part/models.py:3715
msgid "Requires Value"
msgstr "需要值"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "添加測試結果時是否需要一個值？"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "需要附件"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "添加測試結果時是否需要文件附件？"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "選項"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "此測試的有效選擇 (逗號分隔)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "零件參數模板"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "勾選框參數不能有單位"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "複選框參數不能有選項"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "參數模板名稱必須是唯一的"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "參數名稱"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "此參數的物理單位"

#: part/models.py:3865
msgid "Parameter description"
msgstr "參數説明"

#: part/models.py:3871
msgid "Checkbox"
msgstr "勾選框"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "此參數是否為勾選框？"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "此參數的有效選擇 (逗號分隔)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3931
msgid "Part Parameter"
msgstr "零件參數"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "參數不能被修改 - 零件被鎖定"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "無效的參數值選擇"

#: part/models.py:4046
msgid "Parent Part"
msgstr "父零件"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "參數模板"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "參數值"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "可選註釋字段"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "零件類別參數模板"

#: part/models.py:4176
msgid "Default Value"
msgstr "默認值"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "默認參數值"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "物料清單項目不能被修改 - 裝配已鎖定"

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "物料清單項目不能修改 - 變體裝配已鎖定"

#: part/models.py:4363
msgid "Select parent part"
msgstr "選擇父零件"

#: part/models.py:4373
msgid "Sub part"
msgstr "子零件"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "選擇要用於物料清單的零件"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "此物料清單項目的數量"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "此物料清單項目是可選的"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "這個物料清單項目是耗材 (它沒有在生產訂單中被追蹤)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "物料清單項目引用"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "物料清單項目註釋"

#: part/models.py:4451
msgid "Checksum"
msgstr "校驗和"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "物料清單行校驗和"

#: part/models.py:4457
msgid "Validated"
msgstr "已驗證"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "此物料清單項目已驗證"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "獲取繼承的"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "此物料清單項目是由物料清單繼承的變體零件"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "變體零件的庫存項可以用於此物料清單項目"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "可追蹤零件的數量必須是整數"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "必須指定子零件"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "物料清單項目替代品"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "替代品零件不能與主零件相同"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "上級物料清單項目"

#: part/models.py:4782
msgid "Substitute part"
msgstr "替代品零件"

#: part/models.py:4798
msgid "Part 1"
msgstr "零件 1"

#: part/models.py:4806
msgid "Part 2"
msgstr "零件2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "選擇相關的零件"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "零件關係不能在零件和自身之間創建"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "複製關係已經存在"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "上級類別"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "上級零件類別"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "子類別"

#: part/serializers.py:202
msgid "Results"
msgstr "結果"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "根據該模板記錄的結果數量"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "購買此庫存項的貨幣"

#: part/serializers.py:275
msgid "File is not an image"
msgstr ""

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "使用此模板的零件數"

#: part/serializers.py:480
msgid "Original Part"
msgstr "原始零件"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "選擇要複製的原始零件"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "複製圖片"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "從原零件複製圖片"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "複製物料清單"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "從原始零件複製材料清單"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "複製參數"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "從原始零件複製參數數據"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "複製備註"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "從原始零件複製備註"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "初始化庫存數量"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "指定此零件的初始庫存數量。如果數量為零，則不添加任何庫存。"

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "初始化庫存地點"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "初始化指定此零件的庫存地點"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "選擇供應商(或為空以跳過)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "選擇製造商(或為空)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "製造商零件號"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "所選公司不是一個有效的供應商"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "所選公司不是一個有效的製造商"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "與此製造商零件編號 (MPN) 的相匹配的製造商零件已存在"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "匹配此庫存單位 (SKU) 的供應商零件已存在"

#: part/serializers.py:907
msgid "Category Name"
msgstr "類別名稱"

#: part/serializers.py:936
msgid "Building"
msgstr "正在生產"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "庫存項"

#: part/serializers.py:968
msgid "Revisions"
msgstr "修訂"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "供應商"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "庫存總量"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "未分配的庫存"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "變體庫存"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "重複零件"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "從另一個零件複製初始數據"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "初始庫存"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "創建具有初始庫存數量的零件"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "供應商信息"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "添加此零件的初始供應商信息"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "複製類別參數"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "從選擇的零件複製參數模版"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "現有的圖片"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "現有零件圖片的文件名"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "圖片不存在"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "驗證整個物料清單"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "可以創建"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "最低價格"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "覆蓋已計算的最低價格值"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "最低價格貨幣"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "最高價格"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "覆蓋已計算的最高價格值"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "最高價格貨幣"

#: part/serializers.py:1498
msgid "Update"
msgstr "更新"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "更新這個零件的價格"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "無法將所提供的貨幣轉換為 {default_currency}"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "最低價格不能高於最高價格。"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "最高價格不能低於最低價格"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr "選擇父裝配"

#: part/serializers.py:1716
msgid "Select the component part"
msgstr "選擇零部件"

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "選擇要複製物料清單的零件"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "移除現有數據"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "複製前刪除現有的物料清單項目"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "包含繼承的"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "包含從模板零件繼承的物料清單項目"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "跳過無效行"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "啓用此選項以跳過無效行"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "複製替代品零件"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "複製物料清單項目時複製替代品零件"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "低庫存通知"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "可用的 {part.name}庫存已經跌到設置的最低值"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:107
msgid "Sample"
msgstr ""

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "已安裝"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "插件不能被刪除，因為它當前處於激活狀態"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "未指定操作"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "未找到指定操作"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "未找到匹配條形碼數據"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "找到匹配條形碼數據"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "不支持模型"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "找不到模型實例"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "條形碼匹配現有項目"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "沒有找到匹配的零件數據"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "沒有找到匹配的供應商零件"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "找到多個匹配的供應商零件"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "沒有找到匹配條碼數據的插件"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "匹配的供應商零件"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "項目已被接收"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "找到多個匹配的行項目"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "未找到匹配的行項目"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "未提供銷售訂單"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "條形碼與現有的庫存項不匹配"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "庫存項與行項目不匹配"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "可用庫存不足"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "庫存項已分配到銷售訂單"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "沒有足夠的信息"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "需要更多信息以接收行項目"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "已收到採購訂單行項目"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "已掃描的條形碼數據"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "要生成條形碼的模型名稱"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "要生成條形碼的模型對象的主鍵"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "根據採購訂單以分配項目"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "根據採購訂單以接收項目"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "採購訂單尚未提交"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "項目接收地點"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "無法選擇一個結構性位置"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "根據銷售訂單以分配項目"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "根據銷售訂單行項目分配項目"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "根據銷售訂單配送分配項目"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "已交付"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "待分配數"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "標籤打印失敗"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "渲染標籤到 PDF 時出錯"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "渲染標籤到 HTML 時出錯"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "沒有要打印的項目"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "功能類別"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "特色選項"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "功能源 (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree 條形碼"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "提供條形碼本地支持"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "InvenTree 貢獻者"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "條形碼內部格式"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "選擇內部條形碼格式"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON 條形碼 (人類可讀)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "短條形碼 (空間優化)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "短條形碼前綴"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "自定義用於短條形碼的前綴，可能對有多個InvenTree實例的環境有用。"

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Slack傳入Webhook url"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "用於發送消息到slack頻道的 URL"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "打開鏈接"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree 貨幣兑換"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "默認貨幣兑換集成"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF 標籤打印機"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "為打印 PDF 標籤提供本機支持"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Debug模式"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "啓用Debug模式 - 返回原始的 HTML 而不是 PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree 設備標籤打印機"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "提供使用設備打印的支持"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "最近使用"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "選項"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "標籤頁大小"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "跳過標籤"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "打印標籤頁時跳過標籤的數量"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "邊框"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "打印每個標籤的邊框"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "橫屏模式"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "在橫屏模式下打印標籤表"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "庫存樹標籤工作表"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "單張紙上的組合多個標籤"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "標籤大過頁面"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "沒有生成標籤"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "供應商集成 - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "為掃描 DigiKey 條形碼提供支持"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "作為“DigiKey”的供應商。"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "供應商集成 - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "為掃描 LCSC 條形碼提供支持"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "作為“LCSC”的供應商。"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "供應商集成 - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "為掃描 Mouser條形碼提供支持"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "作為“Mouser”的供應商。"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "供應商集成 - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "為掃描 TME 條形碼提供支持"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "作為‘TME’的供應商"

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "只有員工用户可以管理插件"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "插件安裝已禁用"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "插件安裝成功"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "插件安裝到 {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "在插件倉庫中找不到插件"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "插件不是一個打包的插件"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "找不到插件包名稱"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "插件卸載已禁用"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "插件無法卸載，因為它目前處於激活狀態"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "插件卸載成功"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "插件配置"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "插件配置"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "插件的鍵"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "插件名稱"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "軟件包名"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "已安裝的軟件包名字，如果插件是通過 PIP 安裝的"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "插件是否激活"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "示例插件"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "內置插件"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "軟件包插件"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "插件"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "未找到作者"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "插件 '{p}' 與當前 InvenTree 版本{v} 不兼容"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "插件所需最低版本 {v}"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "插件所需最高版本 {v}"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "啓用 採購功能"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "在 InvenTree 界面中啓用採購功能"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "API密鑰"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "訪問外部 API 所需的密鑰"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "數字化"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "數值設置"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "選擇設置"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "帶有多個選項的設置"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "貨幣兑換插件示例"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree 貢獻者"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "啓用零件面板"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "啓用自定義面板來查看部件"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "啓用採購訂單面板"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "啓用自定義面板以查看購買訂單"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "啓用破損面板"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "啓用損壞的面板來測試"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "啓用動態面板"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "啓用動態面板來測試"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "源URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "軟件包的來源 - 這可以是自定義註冊表或 VCS 路徑"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "插件包名稱 - 也可以包含版本指示器"

#: plugin/serializers.py:128
msgid "Version"
msgstr "版本"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "插件版本説明。新版請留白。"

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "確認插件安裝"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "這將把這個插件安裝到當前實例中。這個實例將進行維護。"

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "安裝尚未確認"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "必須提供軟件包名稱或者URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "完全重載"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "執行插件庫的完整重載"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "強制重載"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "強制重載插件庫，即使已經加載"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "收集插件"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "收集插件並添加到註冊表中"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "激活插件"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "激活此插件"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "刪除配置"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "從數據庫中刪除插件配置"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "項目"

#: report/api.py:114
msgid "Plugin not found"
msgstr "插件未找到"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr "插件不支持標籤打印"

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr "無效的標籤尺寸"

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr "沒有有效的項目提供到模板"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "法律"

#: report/helpers.py:46
msgid "Letter"
msgstr "字母"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr "已存在具有此名稱的模板"

#: report/models.py:217
msgid "Template name"
msgstr "模版名稱"

#: report/models.py:223
msgid "Template description"
msgstr "模板説明"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr "修訂編號 (自動增量)"

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr "打印時附加到模型"

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "打印時將報告輸出保存為附件與鏈接模型實例"

#: report/models.py:281
msgid "Filename Pattern"
msgstr "文件名樣式"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr "生成文件名模式"

#: report/models.py:287
msgid "Template is enabled"
msgstr "模板已啓用"

#: report/models.py:294
msgid "Target model type for template"
msgstr "模版的目標模型類型"

#: report/models.py:314
msgid "Filters"
msgstr "篩選器"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "模版查詢篩選器 (逗號分隔的鍵值對列表)"

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "模板包文件"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "PDF 報告的頁面大小"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "橫向渲染報告"

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "寬度 [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "標籤寬度，以毫米為單位。"

#: report/models.py:674
msgid "Height [mm]"
msgstr "高度 [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "標籤高度，以毫米為單位。"

#: report/models.py:780
msgid "Error printing labels"
msgstr ""

#: report/models.py:799
msgid "Snippet"
msgstr "代碼片段"

#: report/models.py:800
msgid "Report snippet file"
msgstr "報告代碼片段文件"

#: report/models.py:807
msgid "Snippet file description"
msgstr "代碼片段文件描述"

#: report/models.py:825
msgid "Asset"
msgstr "資產"

#: report/models.py:826
msgid "Report asset file"
msgstr "報告資產文件"

#: report/models.py:833
msgid "Asset file description"
msgstr "資產文件描述"

#: report/serializers.py:96
msgid "Select report template"
msgstr "選擇報表模板"

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr "要包含在報告中的項目主鍵列表"

#: report/serializers.py:137
msgid "Select label template"
msgstr "選擇標籤模板"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr "打印插件"

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr "選擇用於標籤打印的插件"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "二維碼"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "二維碼"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "物料清單"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "所需材料"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "零件圖像"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "已派發"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "需要給"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "發佈者"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "供應商已刪除"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "單位價格"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "額外行項目"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "總計"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "序列號"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "分配"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "隊列"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "庫存地點項目"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "庫存項測試報告"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "已安裝的項目"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "系列"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "測試結果"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "測試"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "通過"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "失敗"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "無結果 (必填)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "沒有結果"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "資產文件不存在"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "找不到圖片文件"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "parpart_image 標籤需要一個零件實例"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "公司_圖片標籤需要一個公司實例"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "按位置深度篩選"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr "按頂級位置篩選"

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "在篩選結果中包含子地點"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "上級地點"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "按上級位置篩選"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:594
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:623
msgid "Minimum stock"
msgstr ""

#: stock/api.py:627
msgid "Maximum stock"
msgstr ""

#: stock/api.py:630
msgid "Status Code"
msgstr "狀態代碼"

#: stock/api.py:670
msgid "External Location"
msgstr "外部地點"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:868
msgid "Part Tree"
msgstr "零件樹"

#: stock/api.py:890
msgid "Updated before"
msgstr ""

#: stock/api.py:894
msgid "Updated after"
msgstr ""

#: stock/api.py:898
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:902
msgid "Stocktake After"
msgstr ""

#: stock/api.py:907
msgid "Expiry date before"
msgstr "過期日期前"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "過期日期後"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "過期"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "請先輸入數量"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "必須提供有效的零件"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "給定的供應商零件不存在"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "供應商零件有定義的包裝大小，但 use_pack_size 標誌未設置"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "不能為不可跟蹤的零件提供序列號"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "庫存地點類型"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "庫存地點類型"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "為所有沒有圖標的位置設置默認圖標(可選)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "庫存地點"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "庫存地點"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "所有者"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "選擇所有者"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "庫存項可能不直接位於結構庫存地點，但可能位於其子地點。"

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "外部"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "這是一個外部庫存地點"

#: stock/models.py:233
msgid "Location type"
msgstr "位置類型"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "該位置的庫存地點類型"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "您不能將此庫存地點設置為結構性，因為某些庫存項已經位於它！"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr ""

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "庫存項不能存放在結構性庫存地點！"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "無法為虛擬零件創建庫存項"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "零件類型 ('{self.supplier_part.part}') 必須為 {self.part}"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "有序列號的項目的數量必須是1"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "如果數量大於1，則不能設置序列號"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "項目不能屬於其自身"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "如果is_building=True，則項必須具有構建引用"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "構建引用未指向同一零件對象"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "母庫存項目"

#: stock/models.py:1028
msgid "Base part"
msgstr "基礎零件"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "為此庫存項目選擇匹配的供應商零件"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "這個庫存物品在哪裏？"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "包裝此庫存物品存儲在"

#: stock/models.py:1064
msgid "Installed In"
msgstr "安裝於"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "此項目是否安裝在另一個項目中？"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "此項目的序列號"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "此庫存項的批號"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "庫存數量"

#: stock/models.py:1120
msgid "Source Build"
msgstr "源代碼構建"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "為此庫存項目構建"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "消費者"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "構建消耗此庫存項的生產訂單"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "採購訂單來源"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "此庫存商品的採購訂單"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "目的地銷售訂單"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "庫存物品的到期日。在此日期之後，庫存將被視為過期"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "耗盡時刪除"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "當庫存耗盡時刪除此庫存項"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "購買時一個單位的價格"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "轉換為零件"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "零件未設置為可跟蹤"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "數量必須是整數"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "數量不得超過現有庫存量 ({self.quantity})"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "數量不匹配序列號"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "測試模板不存在"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "庫存項已分配到銷售訂單"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "庫存項已安裝在另一個項目中"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "庫存項包含其他項目"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "庫存項已分配給客户"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "庫存項目前正在生產"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "序列化的庫存不能合併"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "複製庫存項"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "庫存項必須指相同零件"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "庫存項必須是同一供應商的零件"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "庫存狀態碼必須匹配"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "庫存項不能移動，因為它沒有庫存"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr "庫存項跟蹤"

#: stock/models.py:2847
msgid "Entry notes"
msgstr "條目註釋"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr "庫存項測試結果"

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "必須為此測試提供值"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "測試附件必須上傳"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr "此測試的值無效"

#: stock/models.py:2951
msgid "Test result"
msgstr "測試結果"

#: stock/models.py:2958
msgid "Test output value"
msgstr "測試輸出值"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "測驗結果附件"

#: stock/models.py:2970
msgid "Test notes"
msgstr "測試備註"

#: stock/models.py:2978
msgid "Test station"
msgstr "測試站"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "進行測試的測試站的標識符"

#: stock/models.py:2985
msgid "Started"
msgstr "已開始"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "測試開始的時間戳"

#: stock/models.py:2992
msgid "Finished"
msgstr "已完成"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "測試結束的時間戳"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "生成批量代碼"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "選擇生產訂單"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "選擇要生成批量代碼的庫存項"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "選擇要生成批量代碼的位置"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "選擇要生成批量代碼的零件"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "選擇採購訂單"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "輸入批量代碼的數量"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "生成的序列號"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "選擇要生成序列號的零件"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "要生成的序列號的數量"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "此結果的測試模板"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "必須提供模板 ID 或測試名稱"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "測試完成時間不能早於測試開始時間"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "父項"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr "父庫存項"

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "添加時使用包裝尺寸：定義的數量是包裝的數量"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "輸入新項目的序列號"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "供應商零件編號"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "已過期"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "子項目"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr "跟蹤項目"

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "此庫存商品的購買價格，單位或包裝"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "輸入要序列化的庫存項目數量"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "數量不得超過現有庫存量 ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "目標庫存位置"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "此零件不能分配序列號"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "序列號已存在"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "選擇要安裝的庫存項目"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "安裝數量"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "輸入要安裝的項目數量"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "添加交易記錄 (可選)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "安裝數量必須至少為1"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "庫存項不可用"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "所選零件不在物料清單中"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "安裝數量不得超過可用數量"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "已卸載項目的目標位置"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "選擇要將庫存項目轉換為的零件"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "所選零件不是有效的轉換選項"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "無法轉換已分配供應商零件的庫存項"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "庫存項狀態代碼"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "選擇要更改狀態的庫存項目"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "未選擇庫存商品"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "轉租"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "上級庫存地點"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "零件必須可銷售"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "物料已分配到銷售訂單"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "項目被分配到生產訂單中"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "客户分配庫存項目"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "所選公司不是客户"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "庫存分配説明"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "必須提供庫存物品清單"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "庫存合併説明"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "允許不匹配的供應商"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "允許合併具有不同供應商零件的庫存項目"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "允許不匹配的狀態"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "允許合併具有不同狀態代碼的庫存項目"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "必須提供至少兩件庫存物品"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "無更改"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "庫存項主鍵值"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "庫存交易記錄"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "需要關注"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "破損"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "銷燬"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "拒絕"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "隔離"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "舊庫存跟蹤條目"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "庫存項已創建"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "已編輯庫存項"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "已分配序列號"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "庫存計數"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "已手動添加庫存"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "已手動刪除庫存"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "地點已更改"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "庫存已更新"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "已安裝到裝配中"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "已從裝配中刪除"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "已安裝組件項"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "已刪除組件項"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "從上級項拆分"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "拆分子項"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "合併的庫存項"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "轉換為變體"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "已創建生產訂單產出"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "生產訂單已出產"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "生產訂單產出被拒絕"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "被工單消耗的"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "按銷售訂單出貨"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "按採購訂單接收"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "按退貨訂單退回"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "寄送給客户"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "從客户端退回"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "權限受限"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "您沒有查看此頁面的權限。"

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "認證失敗"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "您已從InvenTree註銷。"

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "找不到頁面"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "請求的頁面不存在"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "服務器內部錯誤"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s 服務器引起一個內部錯誤"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "有關更多詳細信息，請參閲管理界面中的錯誤日誌"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "網站正在維護中"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "該網站目前正在維護中，應該很快就會重新上線！"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "需要重新啓動服務器"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "配置選項已更改，需要重新啓動服務器"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "有關詳細信息，請與系統管理員聯繫"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "待處理的數據庫遷移"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "有一些待處理的數據庫遷移需要注意"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "點擊以下鏈接查看此訂單"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "以下生產訂單需要庫存"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "生產訂單 %(build)s - 生產… %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "點擊以下鏈接查看此生產訂單"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "以下零件所需庫存不足"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "所需數量"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "您收到此郵件是因為您訂閲了此零件的通知 "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "點擊以下鏈接查看此零件"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "最小數量"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "用户"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "選擇分配給此組的用户"

#: users/admin.py:137
msgid "Personal info"
msgstr "個人信息"

#: users/admin.py:139
msgid "Permissions"
msgstr "權限"

#: users/admin.py:142
msgid "Important dates"
msgstr "重要日期"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "令牌已被撤銷"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "令牌已過期"

#: users/models.py:100
msgid "API Token"
msgstr "API 令牌"

#: users/models.py:101
msgid "API Tokens"
msgstr "API 令牌"

#: users/models.py:137
msgid "Token Name"
msgstr "令牌名稱"

#: users/models.py:138
msgid "Custom token name"
msgstr "自定義令牌名稱"

#: users/models.py:144
msgid "Token expiry date"
msgstr "令牌過期日期"

#: users/models.py:152
msgid "Last Seen"
msgstr "最近一次在線"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "最近使用令牌的時間"

#: users/models.py:157
msgid "Revoked"
msgstr "撤銷"

#: users/models.py:235
msgid "Permission set"
msgstr "權限設置"

#: users/models.py:244
msgid "Group"
msgstr "組"

#: users/models.py:248
msgid "View"
msgstr "查看"

#: users/models.py:248
msgid "Permission to view items"
msgstr "查看項目的權限"

#: users/models.py:252
msgid "Add"
msgstr "添加"

#: users/models.py:252
msgid "Permission to add items"
msgstr "添加項目的權限"

#: users/models.py:256
msgid "Change"
msgstr "更改"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "編輯項目的權限"

#: users/models.py:262
msgid "Delete"
msgstr "刪除"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "刪除項目的權限"

#: users/models.py:501
msgid "Bot"
msgstr ""

#: users/models.py:502
msgid "Internal"
msgstr ""

#: users/models.py:504
msgid "Guest"
msgstr ""

#: users/models.py:513
msgid "Language"
msgstr ""

#: users/models.py:514
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:519
msgid "Theme"
msgstr ""

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr ""

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr ""

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:541
msgid "Position"
msgstr ""

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr ""

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr ""

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr ""

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr ""

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr "管理員"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "採購訂單"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "銷售訂單"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "退貨訂單"

#: users/serializers.py:196
msgid "Username"
msgstr "用户名"

#: users/serializers.py:199
msgid "First Name"
msgstr "名"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "用户的名字（不包括姓氏）"

#: users/serializers.py:203
msgid "Last Name"
msgstr "姓"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "用户的姓氏"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "用户的電子郵件地址"

#: users/serializers.py:326
msgid "Staff"
msgstr "職員"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "此用户是否擁有員工權限"

#: users/serializers.py:332
msgid "Superuser"
msgstr "超級用户"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "此用户是否為超級用户"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "此用户帳户是否已激活"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "您的帳號已經建立完成。"

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "請使用重設密碼功能來登入"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "歡迎使用 InvenTree"

