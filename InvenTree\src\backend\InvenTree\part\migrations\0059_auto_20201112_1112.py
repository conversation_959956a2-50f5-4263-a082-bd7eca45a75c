# Generated by Django 3.0.7 on 2020-11-12 00:12

import InvenTree.fields
import django.core.validators
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0058_remove_partsellpricebreak_cost'),
    ]

    operations = [
        migrations.AlterField(
            model_name='partsellpricebreak',
            name='quantity',
            field=InvenTree.fields.RoundingDecimalField(decimal_places=5, default=1, help_text='Price break quantity', max_digits=15, validators=[django.core.validators.MinValueValidator(1)], verbose_name='Quantity'),
        ),
    ]
