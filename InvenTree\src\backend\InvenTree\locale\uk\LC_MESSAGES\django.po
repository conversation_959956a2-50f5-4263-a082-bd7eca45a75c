msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Ukrainian\n"
"Language: uk_UA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=((n%10==1 && n%100!=11) ? 0 : ((n%10 >= 2 && n%10 <=4 && (n%100 < 12 || n%100 > 14)) ? 1 : ((n%10 == 0 || (n%10 >= 5 && n%10 <=9)) || (n%100 >= 11 && n%100 <= 14)) ? 2 : 3));\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: uk\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Необхідно увімкнути двофакторну автентифікацію, перед тим як робити будь-що інше."

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "Кінцева точка API не знайдена"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr "Для масових операцій необхідно надати перелік сутностей або фільтрів"

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr "Сутності необхідно надати списком"

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Надано неправильний список сутностей"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr "Фільтри необхідно надавати у вигляді словника"

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Надано неправильні фільтри"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr "Немає сутностей що відповідають наданим критеріям"

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "У користувача немає дозволу на перегляд цієї моделі"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "Email (ще раз)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "Підтвердження адреси електронної пошти"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Ви повинні використовувати щоразу однаковий email."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Вказана основна адреса електронної пошти недійсна."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Наданий домен електронної пошти не затверджено."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Надано неправильну одиницю виміру ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Значення не вказане"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Не вдалося перетворити {original} на {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Невірна кількість"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Деталі помилки можна знайти на панелі адміністратора"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Введіть дату"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Неправильне десяткове значення"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Нотатки"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Значення '{name}' не відповідає шаблону формату"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Надане значення не відповідає обов'язковому шаблону: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Неможливо серіалізувати більше ніж 1000 сутностей за раз"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Пустий серійний номер"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr ""

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr ""

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr ""

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr ""

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr ""

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr ""

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr ""

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Відбулося виключення"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr ""

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr ""

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr ""

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr ""

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr ""

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr ""

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr ""

#: InvenTree/locales.py:22
msgid "Czech"
msgstr ""

#: InvenTree/locales.py:23
msgid "Danish"
msgstr ""

#: InvenTree/locales.py:24
msgid "German"
msgstr ""

#: InvenTree/locales.py:25
msgid "Greek"
msgstr ""

#: InvenTree/locales.py:26
msgid "English"
msgstr ""

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr ""

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr ""

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr ""

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr ""

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr ""

#: InvenTree/locales.py:32
msgid "French"
msgstr ""

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr ""

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr ""

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Угорська"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Італійська"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr ""

#: InvenTree/locales.py:38
msgid "Korean"
msgstr ""

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr ""

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr ""

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr ""

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr ""

#: InvenTree/locales.py:43
msgid "Polish"
msgstr ""

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr ""

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Португальська (Бразилія)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr ""

#: InvenTree/locales.py:47
msgid "Russian"
msgstr ""

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr ""

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr ""

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr ""

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr ""

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Тайська"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Турецька"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Українська"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "В’єтнамська"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Китайська (спрощена)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Китайська (Традиційна)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr ""

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr ""

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr ""

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr ""

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr ""

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr ""

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr ""

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr ""

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr ""

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr ""

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr ""

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr ""

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr ""

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Назва"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Опис"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Опис (опціонально)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Шлях"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr ""

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Примітки в Markdown (опціонально)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr ""

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr ""

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr ""

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr ""

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr ""

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr ""

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Помилка сервера"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr ""

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr ""

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr ""

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr ""

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr ""

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr ""

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr ""

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr ""

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr ""

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr ""

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr ""

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr ""

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr ""

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr ""

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Деталь"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr ""

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr ""

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr ""

#: build/api.py:154
msgid "Assigned To"
msgstr ""

#: build/api.py:189
msgid "Created before"
msgstr ""

#: build/api.py:193
msgid "Created after"
msgstr ""

#: build/api.py:197
msgid "Has start date"
msgstr ""

#: build/api.py:205
msgid "Start date before"
msgstr ""

#: build/api.py:209
msgid "Start date after"
msgstr ""

#: build/api.py:213
msgid "Has target date"
msgstr ""

#: build/api.py:221
msgid "Target date before"
msgstr ""

#: build/api.py:225
msgid "Target date after"
msgstr ""

#: build/api.py:229
msgid "Completed before"
msgstr ""

#: build/api.py:233
msgid "Completed after"
msgstr ""

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr ""

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr ""

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr ""

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr ""

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Розхідний матеріал"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr ""

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Збірка"

#: build/api.py:450
msgid "Tracked"
msgstr ""

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Тестуємо"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr ""

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr ""

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Доступно"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr ""

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Місце"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr ""

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr ""

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr ""

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr ""

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr ""

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr ""

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:244
msgid "Build Order Reference"
msgstr ""

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr ""

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr ""

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr ""

#: build/models.py:273
msgid "Select part to build"
msgstr "Обрати деталь для створення"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr ""

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr ""

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr ""

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr ""

#: build/models.py:300
msgid "External Build"
msgstr ""

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr ""

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr ""

#: build/models.py:315
msgid "Build Quantity"
msgstr ""

#: build/models.py:318
msgid "Number of stock items to build"
msgstr ""

#: build/models.py:322
msgid "Completed items"
msgstr ""

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr ""

#: build/models.py:328
msgid "Build Status"
msgstr ""

#: build/models.py:333
msgid "Build status code"
msgstr ""

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr ""

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr ""

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr ""

#: build/models.py:356
msgid "Build start date"
msgstr ""

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:363
msgid "Target completion date"
msgstr ""

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr ""

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr ""

#: build/models.py:378
msgid "completed by"
msgstr ""

#: build/models.py:387
msgid "Issued by"
msgstr ""

#: build/models.py:388
msgid "User who issued this build order"
msgstr ""

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr ""

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr ""

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr ""

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr ""

#: build/models.py:410
msgid "Build Priority"
msgstr ""

#: build/models.py:413
msgid "Priority of this build order"
msgstr ""

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr ""

#: build/models.py:422
msgid "Project code for this build order"
msgstr ""

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr ""

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr ""

#: build/models.py:728
msgid "A build order has been completed"
msgstr ""

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr ""

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr ""

#: build/models.py:1042
msgid "Build output is already completed"
msgstr ""

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr ""

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr ""

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr ""

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr ""

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr ""

#: build/models.py:1602
msgid "Build object"
msgstr ""

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Кількість"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr ""

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr ""

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr ""

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr ""

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr ""

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr ""

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr ""

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr ""

#: build/models.py:1905
msgid "Source stock item"
msgstr ""

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr ""

#: build/models.py:1924
msgid "Install into"
msgstr ""

#: build/models.py:1925
msgid "Destination stock item"
msgstr ""

#: build/serializers.py:115
msgid "Build Level"
msgstr ""

#: build/serializers.py:124
msgid "Part Name"
msgstr ""

#: build/serializers.py:142
msgid "Project Code Label"
msgstr ""

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr ""

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr ""

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr ""

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr ""

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr ""

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr ""

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr ""

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr ""

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr ""

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr ""

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr ""

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr ""

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr ""

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr ""

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr ""

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr ""

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr ""

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr ""

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr ""

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr ""

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr ""

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr ""

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr ""

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr ""

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr ""

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr ""

#: build/serializers.py:733
msgid "Not permitted"
msgstr ""

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr ""

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr ""

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr ""

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr ""

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr ""

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr ""

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr ""

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr ""

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr ""

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr ""

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr ""

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr ""

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr ""

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr ""

#: build/serializers.py:869
msgid "Build Line"
msgstr ""

#: build/serializers.py:877
msgid "Build output"
msgstr ""

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr ""

#: build/serializers.py:916
msgid "Build Line Item"
msgstr ""

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr ""

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr ""

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr ""

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr ""

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr ""

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr ""

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr ""

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr ""

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr ""

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr ""

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr ""

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr ""

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr ""

#: build/serializers.py:1122
msgid "Optional Items"
msgstr ""

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr ""

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr ""

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr ""

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr ""

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr ""

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr ""

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr ""

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr ""

#: build/serializers.py:1383
msgid "Build Reference"
msgstr ""

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr ""

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr ""

#: build/serializers.py:1416
msgid "Inherited"
msgstr ""

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Дозволити варіанти"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr ""

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr ""

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "У виробництві"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr ""

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr ""

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr ""

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr ""

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr ""

#: build/status_codes.py:12
msgid "Production"
msgstr ""

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr ""

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr ""

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr ""

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr ""

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr ""

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr ""

#: common/api.py:688
msgid "Is Link"
msgstr ""

#: common/api.py:696
msgid "Is File"
msgstr ""

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr ""

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr ""

#: common/currency.py:122
msgid "Invalid currency code"
msgstr ""

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr ""

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr ""

#: common/currency.py:146
msgid "No plugin"
msgstr ""

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr ""

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr ""

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr ""

#: common/models.py:171
msgid "Project description"
msgstr ""

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr ""

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr ""

#: common/models.py:780
msgid "Settings value"
msgstr ""

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr ""

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr ""

#: common/models.py:859
msgid "Value must be an integer value"
msgstr ""

#: common/models.py:867
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:914
msgid "Key string must be unique"
msgstr ""

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Користувач"

#: common/models.py:1346
msgid "Price break quantity"
msgstr ""

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Ціна"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr ""

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr ""

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr ""

#: common/models.py:1416
msgid "Name for this webhook"
msgstr ""

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr ""

#: common/models.py:1420
msgid "Is this webhook active"
msgstr ""

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr ""

#: common/models.py:1437
msgid "Token for access"
msgstr ""

#: common/models.py:1445
msgid "Secret"
msgstr ""

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr ""

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr ""

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr ""

#: common/models.py:1563
msgid "Host"
msgstr ""

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr ""

#: common/models.py:1572
msgid "Header"
msgstr ""

#: common/models.py:1573
msgid "Header of this message"
msgstr ""

#: common/models.py:1580
msgid "Body"
msgstr ""

#: common/models.py:1581
msgid "Body of this message"
msgstr ""

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr ""

#: common/models.py:1596
msgid "Worked on"
msgstr ""

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr ""

#: common/models.py:1723
msgid "Id"
msgstr ""

#: common/models.py:1725
msgid "Title"
msgstr "Назва"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Посилання"

#: common/models.py:1729
msgid "Published"
msgstr ""

#: common/models.py:1731
msgid "Author"
msgstr ""

#: common/models.py:1733
msgid "Summary"
msgstr ""

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr ""

#: common/models.py:1736
msgid "Was this news item read?"
msgstr ""

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Зображення"

#: common/models.py:1753
msgid "Image file"
msgstr ""

#: common/models.py:1765
msgid "Target model type for this image"
msgstr ""

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr ""

#: common/models.py:1791
msgid "Custom Unit"
msgstr ""

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr ""

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr ""

#: common/models.py:1843
msgid "Unit name"
msgstr ""

#: common/models.py:1850
msgid "Symbol"
msgstr ""

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr ""

#: common/models.py:1857
msgid "Definition"
msgstr ""

#: common/models.py:1858
msgid "Unit definition"
msgstr ""

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr ""

#: common/models.py:1933
msgid "Missing file"
msgstr ""

#: common/models.py:1934
msgid "Missing external link"
msgstr ""

#: common/models.py:1971
msgid "Model type"
msgstr ""

#: common/models.py:1972
msgid "Target model type for image"
msgstr ""

#: common/models.py:1980
msgid "Select file to attach"
msgstr ""

#: common/models.py:1996
msgid "Comment"
msgstr "Коментар"

#: common/models.py:1997
msgid "Attachment comment"
msgstr ""

#: common/models.py:2013
msgid "Upload date"
msgstr "Дата завантаження"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Дата завантаження файлу"

#: common/models.py:2018
msgid "File size"
msgstr "Розмір файлу"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Розмір файлу в байтах"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr ""

#: common/models.py:2077
msgid "Custom State"
msgstr ""

#: common/models.py:2078
msgid "Custom States"
msgstr ""

#: common/models.py:2083
msgid "Reference Status Set"
msgstr ""

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr ""

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr ""

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2102
msgid "Name of the state"
msgstr ""

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Етикетка"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr ""

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Колір"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr ""

#: common/models.py:2128
msgid "Model"
msgstr "Модель"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr ""

#: common/models.py:2144
msgid "Model must be selected"
msgstr ""

#: common/models.py:2147
msgid "Key must be selected"
msgstr ""

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr ""

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr ""

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Список вибору"

#: common/models.py:2222
msgid "Selection Lists"
msgstr ""

#: common/models.py:2227
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2234
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Заблоковано"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2255
msgid "Source Plugin"
msgstr ""

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2261
msgid "Source String"
msgstr ""

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2271
msgid "Default Entry"
msgstr ""

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr ""

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2283
msgid "Last Updated"
msgstr ""

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2318
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2319
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2372
msgid "Barcode Scan"
msgstr ""

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Дані"

#: common/models.py:2377
msgid "Barcode data"
msgstr ""

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr ""

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr ""

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr ""

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr ""

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2415
msgid "Response"
msgstr ""

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr ""

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr ""

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr ""

#: common/models.py:2574
msgid "Email Messages"
msgstr ""

#: common/models.py:2581
msgid "Announced"
msgstr ""

#: common/models.py:2583
msgid "Sent"
msgstr ""

#: common/models.py:2584
msgid "Failed"
msgstr ""

#: common/models.py:2587
msgid "Delivered"
msgstr ""

#: common/models.py:2595
msgid "Confirmed"
msgstr ""

#: common/models.py:2601
msgid "Inbound"
msgstr ""

#: common/models.py:2602
msgid "Outbound"
msgstr ""

#: common/models.py:2607
msgid "No Reply"
msgstr ""

#: common/models.py:2608
msgid "Track Delivery"
msgstr ""

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr ""

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr ""

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr ""

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr ""

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr ""

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr ""

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr ""

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr ""

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr ""

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr ""

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr ""

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr ""

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr ""

#: common/serializers.py:486
msgid "Is Running"
msgstr ""

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr ""

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr ""

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr ""

#: common/serializers.py:519
msgid "Task ID"
msgstr ""

#: common/serializers.py:519
msgid "Unique task ID"
msgstr ""

#: common/serializers.py:521
msgid "Lock"
msgstr ""

#: common/serializers.py:521
msgid "Lock time"
msgstr ""

#: common/serializers.py:523
msgid "Task name"
msgstr ""

#: common/serializers.py:525
msgid "Function"
msgstr ""

#: common/serializers.py:525
msgid "Function name"
msgstr ""

#: common/serializers.py:527
msgid "Arguments"
msgstr ""

#: common/serializers.py:527
msgid "Task arguments"
msgstr ""

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr ""

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr ""

#: common/serializers.py:640
msgid "Filename"
msgstr ""

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr ""

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr ""

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr ""

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr ""

#: common/setting/system.py:173
msgid "Restart required"
msgstr ""

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr ""

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr ""

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr ""

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:199
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr ""

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr ""

#: common/setting/system.py:213
msgid "Use instance name"
msgstr ""

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr ""

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr ""

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr ""

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr ""

#: common/setting/system.py:226
msgid "Internal company name"
msgstr ""

#: common/setting/system.py:230
msgid "Base URL"
msgstr ""

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr ""

#: common/setting/system.py:237
msgid "Default Currency"
msgstr ""

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr ""

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr ""

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr ""

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr ""

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr ""

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr ""

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr ""

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr ""

#: common/setting/system.py:264
msgid "Download from URL"
msgstr ""

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr ""

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr ""

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr ""

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr ""

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr ""

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr ""

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr ""

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr ""

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr ""

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr ""

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr ""

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr ""

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr ""

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr ""

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr ""

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr ""

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr ""

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr ""

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr ""

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr ""

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr ""

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr ""

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr ""

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr ""

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr ""

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr ""

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr ""

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr ""

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr ""

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr ""

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr ""

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr ""

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr ""

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr ""

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr ""

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr ""

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr ""

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr ""

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr ""

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr ""

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr ""

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr ""

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr ""

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr ""

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr ""

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr ""

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr ""

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Шаблон"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr ""

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr ""

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Компонент"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr ""

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr ""

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr ""

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Доступний для продажу"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr ""

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr ""

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Віртуальний"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr ""

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Показати пов'язані деталі"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr ""

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr ""

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr ""

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr ""

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr ""

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr ""

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr ""

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr ""

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr ""

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr ""

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr ""

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr ""

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr ""

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr ""

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr ""

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr ""

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr ""

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr ""

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr ""

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr ""

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr ""

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr ""

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr ""

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr ""

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr ""

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr ""

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr ""

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr ""

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr ""

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr ""

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr ""

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr ""

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr ""

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr ""

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr ""

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr ""

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr ""

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr ""

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr ""

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr ""

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr ""

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr ""

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr ""

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr ""

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr ""

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr ""

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr ""

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr ""

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr ""

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr ""

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr ""

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr ""

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr ""

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr ""

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr ""

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr ""

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr ""

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr ""

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr ""

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr ""

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr ""

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr ""

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr ""

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr ""

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr ""

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr ""

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr ""

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr ""

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr ""

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr ""

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr ""

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr ""

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr ""

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr ""

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr ""

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr ""

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr ""

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr ""

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr ""

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr ""

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr ""

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr ""

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr ""

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr ""

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr ""

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr ""

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr ""

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr ""

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr ""

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr ""

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr ""

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr ""

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr ""

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr ""

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr ""

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr ""

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr ""

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr ""

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr ""

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr ""

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr ""

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr ""

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr ""

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr ""

#: common/setting/system.py:909
msgid "Enable registration"
msgstr ""

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr ""

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr ""

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr ""

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr ""

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr ""

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr ""

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr ""

#: common/setting/system.py:937
msgid "SSO group key"
msgstr ""

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr ""

#: common/setting/system.py:943
msgid "SSO group map"
msgstr ""

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr ""

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr ""

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Чи призначені групи користувачеві повинні бути видалені, якщо вони не є резервним сервером IdP. Відключення цього налаштування може спричинити проблеми безпеки"

#: common/setting/system.py:959
msgid "Email required"
msgstr ""

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr ""

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr ""

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr ""

#: common/setting/system.py:971
msgid "Mail twice"
msgstr ""

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr ""

#: common/setting/system.py:977
msgid "Password twice"
msgstr ""

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr ""

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr ""

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr ""

#: common/setting/system.py:991
msgid "Group on signup"
msgstr ""

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr ""

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr ""

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr ""

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr ""

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr ""

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr ""

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr ""

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr ""

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr ""

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr ""

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr ""

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr ""

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr ""

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr ""

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr ""

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr ""

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr ""

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr ""

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr ""

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr ""

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr ""

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr ""

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr ""

#: common/setting/user.py:23
msgid "Inline label display"
msgstr ""

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr ""

#: common/setting/user.py:31
msgid "Default label printer"
msgstr ""

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr ""

#: common/setting/user.py:37
msgid "Inline report display"
msgstr ""

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr ""

#: common/setting/user.py:45
msgid "Search Parts"
msgstr ""

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr ""

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr ""

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr ""

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr ""

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr ""

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr ""

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr ""

#: common/setting/user.py:69
msgid "Search Categories"
msgstr ""

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr ""

#: common/setting/user.py:75
msgid "Search Stock"
msgstr ""

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr ""

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr ""

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr ""

#: common/setting/user.py:89
msgid "Search Locations"
msgstr ""

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr ""

#: common/setting/user.py:95
msgid "Search Companies"
msgstr ""

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr ""

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr ""

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr ""

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr ""

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr ""

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr ""

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr ""

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr ""

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr ""

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr ""

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr ""

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr ""

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr ""

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr ""

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr ""

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr ""

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr ""

#: common/setting/user.py:157
msgid "Regex Search"
msgstr ""

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr ""

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr ""

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr ""

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr ""

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr ""

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr ""

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr ""

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:207
msgid "Date Format"
msgstr ""

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr ""

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr ""

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr ""

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr ""

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr ""

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr ""

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr ""

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr ""

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr ""

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr ""

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr ""

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "Позиція активна"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr ""

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Позиція постачальника активна"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Внутрішня позиція активна"

#: company/api.py:287
msgid "Supplier is Active"
msgstr ""

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Виробник"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr ""

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:120
msgid "Companies"
msgstr ""

#: company/models.py:148
msgid "Company description"
msgstr ""

#: company/models.py:149
msgid "Description of the company"
msgstr ""

#: company/models.py:155
msgid "Website"
msgstr ""

#: company/models.py:156
msgid "Company website URL"
msgstr ""

#: company/models.py:162
msgid "Phone number"
msgstr ""

#: company/models.py:164
msgid "Contact phone number"
msgstr ""

#: company/models.py:171
msgid "Contact email address"
msgstr ""

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr ""

#: company/models.py:178
msgid "Point of contact"
msgstr ""

#: company/models.py:184
msgid "Link to external company information"
msgstr ""

#: company/models.py:198
msgid "Is this company active?"
msgstr ""

#: company/models.py:203
msgid "Is customer"
msgstr ""

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr ""

#: company/models.py:209
msgid "Is supplier"
msgstr ""

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr ""

#: company/models.py:215
msgid "Is manufacturer"
msgstr ""

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr ""

#: company/models.py:224
msgid "Default currency used for this company"
msgstr ""

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr ""

#: company/models.py:355
msgid "Addresses"
msgstr ""

#: company/models.py:412
msgid "Select company"
msgstr ""

#: company/models.py:417
msgid "Address title"
msgstr ""

#: company/models.py:418
msgid "Title describing the address entry"
msgstr ""

#: company/models.py:424
msgid "Primary address"
msgstr ""

#: company/models.py:425
msgid "Set as primary address"
msgstr ""

#: company/models.py:430
msgid "Line 1"
msgstr ""

#: company/models.py:431
msgid "Address line 1"
msgstr ""

#: company/models.py:437
msgid "Line 2"
msgstr ""

#: company/models.py:438
msgid "Address line 2"
msgstr ""

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr ""

#: company/models.py:451
msgid "City/Region"
msgstr ""

#: company/models.py:452
msgid "Postal code city/region"
msgstr ""

#: company/models.py:458
msgid "State/Province"
msgstr ""

#: company/models.py:459
msgid "State or province"
msgstr ""

#: company/models.py:465
msgid "Country"
msgstr ""

#: company/models.py:466
msgid "Address country"
msgstr ""

#: company/models.py:472
msgid "Courier shipping notes"
msgstr ""

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr ""

#: company/models.py:479
msgid "Internal shipping notes"
msgstr ""

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr ""

#: company/models.py:487
msgid "Link to address information (external)"
msgstr ""

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Позиція виробника"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Базова позиція"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Обрати позицію"

#: company/models.py:540
msgid "Select manufacturer"
msgstr ""

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr ""

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr ""

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr ""

#: company/models.py:563
msgid "Manufacturer part description"
msgstr ""

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr ""

#: company/models.py:635
msgid "Parameter name"
msgstr ""

#: company/models.py:642
msgid "Parameter value"
msgstr ""

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr ""

#: company/models.py:650
msgid "Parameter units"
msgstr ""

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr ""

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr ""

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr ""

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr ""

#: company/models.py:829
msgid "Select supplier"
msgstr ""

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr ""

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr ""

#: company/models.py:851
msgid "Select manufacturer part"
msgstr ""

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr ""

#: company/models.py:867
msgid "Supplier part description"
msgstr ""

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Примітка"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "Базова вартість"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Мінімальний платіж (напр. комісія за збереження)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr ""

#: company/models.py:892
msgid "Part packaging"
msgstr ""

#: company/models.py:897
msgid "Pack Quantity"
msgstr ""

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr ""

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr ""

#: company/models.py:919
msgid "Order multiple"
msgstr ""

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr ""

#: company/models.py:937
msgid "Availability Updated"
msgstr ""

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr ""

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr ""

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr ""

#: company/serializers.py:245
msgid "Company Name"
msgstr ""

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "В наявності"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr ""

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:73
msgid "Data File"
msgstr ""

#: importer/models.py:74
msgid "Data file to import"
msgstr ""

#: importer/models.py:83
msgid "Columns"
msgstr ""

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:96
msgid "Import status"
msgstr ""

#: importer/models.py:106
msgid "Field Defaults"
msgstr ""

#: importer/models.py:113
msgid "Field Overrides"
msgstr ""

#: importer/models.py:120
msgid "Field Filters"
msgstr ""

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr ""

#: importer/models.py:471
msgid "Field"
msgstr ""

#: importer/models.py:473
msgid "Column"
msgstr ""

#: importer/models.py:542
msgid "Row Index"
msgstr ""

#: importer/models.py:545
msgid "Original row data"
msgstr ""

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr ""

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Дійсно"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr ""

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr ""

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr ""

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:177
msgid "Rows"
msgstr ""

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr ""

#: importer/serializers.py:191
msgid "No rows provided"
msgstr ""

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr ""

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr ""

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr ""

#: importer/status_codes.py:13
msgid "Initializing"
msgstr ""

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr ""

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr ""

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr ""

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr ""

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr ""

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr ""

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr ""

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr ""

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr ""

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr ""

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr ""

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr ""

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr ""

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr ""

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr ""

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr ""

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr ""

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr ""

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr ""

#: machine/models.py:25
msgid "Name of machine"
msgstr ""

#: machine/models.py:29
msgid "Machine Type"
msgstr ""

#: machine/models.py:29
msgid "Type of machine"
msgstr ""

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr ""

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr ""

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr ""

#: machine/models.py:95
msgid "Driver available"
msgstr ""

#: machine/models.py:100
msgid "No errors"
msgstr ""

#: machine/models.py:105
msgid "Initialized"
msgstr ""

#: machine/models.py:117
msgid "Machine status"
msgstr ""

#: machine/models.py:145
msgid "Machine"
msgstr ""

#: machine/models.py:157
msgid "Machine Config"
msgstr ""

#: machine/models.py:162
msgid "Config type"
msgstr ""

#: order/api.py:121
msgid "Order Reference"
msgstr ""

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr ""

#: order/api.py:165
msgid "Has Project Code"
msgstr ""

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr ""

#: order/api.py:183
msgid "Created Before"
msgstr ""

#: order/api.py:187
msgid "Created After"
msgstr ""

#: order/api.py:191
msgid "Has Start Date"
msgstr ""

#: order/api.py:199
msgid "Start Date Before"
msgstr ""

#: order/api.py:203
msgid "Start Date After"
msgstr ""

#: order/api.py:207
msgid "Has Target Date"
msgstr ""

#: order/api.py:215
msgid "Target Date Before"
msgstr ""

#: order/api.py:219
msgid "Target Date After"
msgstr ""

#: order/api.py:270
msgid "Has Pricing"
msgstr ""

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr ""

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr ""

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr ""

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr ""

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Внутрішній компонент"

#: order/api.py:578
msgid "Order Pending"
msgstr ""

#: order/api.py:958
msgid "Completed"
msgstr ""

#: order/api.py:1214
msgid "Has Shipment"
msgstr ""

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr ""

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr ""

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr ""

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr ""

#: order/models.py:91
msgid "Total price for this order"
msgstr ""

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr ""

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr ""

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr ""

#: order/models.py:383
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:436
msgid "Order description (optional)"
msgstr ""

#: order/models.py:445
msgid "Select project code for this order"
msgstr ""

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr ""

#: order/models.py:458
msgid "Start date"
msgstr ""

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr ""

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr ""

#: order/models.py:487
msgid "Issue Date"
msgstr ""

#: order/models.py:488
msgid "Date order was issued"
msgstr ""

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr ""

#: order/models.py:507
msgid "Point of contact for this order"
msgstr ""

#: order/models.py:517
msgid "Company address for this order"
msgstr ""

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr ""

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr ""

#: order/models.py:618
msgid "Purchase order status"
msgstr ""

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr ""

#: order/models.py:644
msgid "Supplier Reference"
msgstr ""

#: order/models.py:645
msgid "Supplier order reference code"
msgstr ""

#: order/models.py:654
msgid "received by"
msgstr ""

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr ""

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr ""

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr ""

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr ""

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr ""

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr ""

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr ""

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr ""

#: order/models.py:1318
msgid "Sales order status"
msgstr ""

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr ""

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr ""

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr ""

#: order/models.py:1343
msgid "shipped by"
msgstr ""

#: order/models.py:1382
msgid "Order is already complete"
msgstr ""

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr ""

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr ""

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr ""

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr ""

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1711
msgid "Item quantity"
msgstr ""

#: order/models.py:1728
msgid "Line item reference"
msgstr ""

#: order/models.py:1735
msgid "Line item notes"
msgstr ""

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr ""

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr ""

#: order/models.py:1778
msgid "Additional context for this line"
msgstr ""

#: order/models.py:1788
msgid "Unit price"
msgstr ""

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr ""

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr ""

#: order/models.py:1891
msgid "Received"
msgstr ""

#: order/models.py:1892
msgid "Number of items received"
msgstr ""

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr ""

#: order/models.py:1901
msgid "Unit purchase price"
msgstr ""

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr ""

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr ""

#: order/models.py:2063
msgid "Sale Price"
msgstr ""

#: order/models.py:2064
msgid "Unit sale price"
msgstr ""

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr ""

#: order/models.py:2074
msgid "Shipped quantity"
msgstr ""

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2206
msgid "Date of shipment"
msgstr ""

#: order/models.py:2212
msgid "Delivery Date"
msgstr ""

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr ""

#: order/models.py:2221
msgid "Checked By"
msgstr ""

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr ""

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr ""

#: order/models.py:2230
msgid "Shipment number"
msgstr ""

#: order/models.py:2238
msgid "Tracking Number"
msgstr ""

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr ""

#: order/models.py:2246
msgid "Invoice Number"
msgstr ""

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr ""

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr ""

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr ""

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr ""

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr ""

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr ""

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr ""

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr ""

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr ""

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr ""

#: order/models.py:2451
msgid "Line"
msgstr ""

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr ""

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr ""

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr ""

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr ""

#: order/models.py:2600
msgid "Return Order reference"
msgstr ""

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr ""

#: order/models.py:2625
msgid "Return order status"
msgstr ""

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr ""

#: order/models.py:2910
msgid "Received Date"
msgstr ""

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr ""

#: order/models.py:2923
msgid "Outcome"
msgstr ""

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr ""

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr ""

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:90
msgid "Order ID"
msgstr ""

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:96
msgid "Copy Lines"
msgstr ""

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr ""

#: order/serializers.py:122
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:389
msgid "Supplier Name"
msgstr ""

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr ""

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr ""

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr ""

#: order/serializers.py:611
msgid "Order is not open"
msgstr ""

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr ""

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr ""

#: order/serializers.py:656
msgid "Merge Items"
msgstr ""

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr ""

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr ""

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr ""

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr ""

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr ""

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr ""

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr ""

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr ""

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr ""

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr ""

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr ""

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:826
msgid "Barcode"
msgstr ""

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr ""

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr ""

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr ""

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr ""

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr ""

#: order/serializers.py:1066
msgid "Shipments"
msgstr ""

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr ""

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr ""

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr ""

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr ""

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr ""

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr ""

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr ""

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr ""

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr ""

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr ""

#: order/serializers.py:1982
msgid "Return order line item"
msgstr ""

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr ""

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr ""

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr ""

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2136
msgid "Line price currency"
msgstr ""

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr ""

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr ""

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr ""

#: order/status_codes.py:105
msgid "Return"
msgstr ""

#: order/status_codes.py:108
msgid "Repair"
msgstr ""

#: order/status_codes.py:111
msgid "Replace"
msgstr ""

#: order/status_codes.py:114
msgid "Refund"
msgstr ""

#: order/status_codes.py:117
msgid "Reject"
msgstr ""

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr ""

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr ""

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr ""

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr ""

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:111
msgid "Starred"
msgstr ""

#: part/api.py:113
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Глибина"

#: part/api.py:130
msgid "Filter by category depth"
msgstr ""

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr ""

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr ""

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:185
msgid "Parent"
msgstr "Батьківський елемент"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Фільтр за батьківською категорією"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:434
msgid "Has Results"
msgstr ""

#: part/api.py:660
msgid "Is Variant"
msgstr ""

#: part/api.py:668
msgid "Is Revision"
msgstr ""

#: part/api.py:678
msgid "Has Revisions"
msgstr ""

#: part/api.py:859
msgid "BOM Valid"
msgstr ""

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1511
msgid "Component part is testable"
msgstr ""

#: part/api.py:1576
msgid "Uses"
msgstr ""

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr ""

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr ""

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr ""

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr ""

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr ""

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr ""

#: part/models.py:134
msgid "Default keywords"
msgstr ""

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr ""

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr ""

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr ""

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr ""

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Позиції"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "Неможливо видалити цю позицію, оскільки вона заблокована"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Неможливо видалити цю позицію, оскільки вона ще активна"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Неможливо видалити цю позицію, бо вона використовується у збірці"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr ""

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr ""

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr ""

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:724
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr ""

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr ""

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr ""

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr ""

#: part/models.py:1051
msgid "Part name"
msgstr "Назва позиції"

#: part/models.py:1056
msgid "Is Template"
msgstr "Це шаблон"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Ця позиція є шаблоном?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr ""

#: part/models.py:1068
msgid "Variant Of"
msgstr ""

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Опис позиції (опціонально)"

#: part/models.py:1082
msgid "Keywords"
msgstr ""

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr ""

#: part/models.py:1093
msgid "Part category"
msgstr ""

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr ""

#: part/models.py:1108
msgid "Part revision or version number"
msgstr ""

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Ревізія"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1119
msgid "Revision Of"
msgstr "Ревізія"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr ""

#: part/models.py:1190
msgid "Default Supplier"
msgstr ""

#: part/models.py:1191
msgid "Default supplier part"
msgstr ""

#: part/models.py:1198
msgid "Default Expiry"
msgstr ""

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr ""

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Мінімальний запас"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Мінімально дозволений рівень запасів"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Одиниці виміру для цієї позиції"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Чи можна побудувати цю позицію з інших компонентів?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr ""

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr ""

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr ""

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr ""

#: part/models.py:1258
msgid "Is this part active?"
msgstr ""

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr ""

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr ""

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr ""

#: part/models.py:1291
msgid "BOM checked by"
msgstr ""

#: part/models.py:1296
msgid "BOM checked date"
msgstr ""

#: part/models.py:1312
msgid "Creation User"
msgstr ""

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr ""

#: part/models.py:2257
msgid "Sell multiple"
msgstr ""

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr ""

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr ""

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr ""

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr ""

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr ""

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr ""

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr ""

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr ""

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr ""

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr ""

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr ""

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr ""

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr ""

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr ""

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr ""

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr ""

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr ""

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr ""

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr ""

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr ""

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr ""

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr ""

#: part/models.py:3372
msgid "Override minimum cost"
msgstr ""

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr ""

#: part/models.py:3379
msgid "Override maximum cost"
msgstr ""

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr ""

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr ""

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr ""

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr ""

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr ""

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr ""

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr ""

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr ""

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr ""

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr ""

#: part/models.py:3439
msgid "Part for stocktake"
msgstr ""

#: part/models.py:3444
msgid "Item Count"
msgstr ""

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr ""

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr ""

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Дата"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr ""

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr ""

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr ""

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr ""

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr ""

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3595
msgid "Part Test Template"
msgstr ""

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr ""

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr ""

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3684
msgid "Test Name"
msgstr "Тестова назва"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr ""

#: part/models.py:3691
msgid "Test Key"
msgstr ""

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3699
msgid "Test Description"
msgstr ""

#: part/models.py:3700
msgid "Enter description for this test"
msgstr ""

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr ""

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3709
msgid "Required"
msgstr ""

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr ""

#: part/models.py:3715
msgid "Requires Value"
msgstr ""

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr ""

#: part/models.py:3721
msgid "Requires Attachment"
msgstr ""

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr ""

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr ""

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr ""

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr ""

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr ""

#: part/models.py:3850
msgid "Parameter Name"
msgstr ""

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr ""

#: part/models.py:3865
msgid "Parameter description"
msgstr ""

#: part/models.py:3871
msgid "Checkbox"
msgstr "Прапорець"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr ""

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr ""

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3931
msgid "Part Parameter"
msgstr ""

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr ""

#: part/models.py:4046
msgid "Parent Part"
msgstr ""

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr ""

#: part/models.py:4060
msgid "Parameter Value"
msgstr ""

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr ""

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4176
msgid "Default Value"
msgstr ""

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr ""

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4363
msgid "Select parent part"
msgstr ""

#: part/models.py:4373
msgid "Sub part"
msgstr ""

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr ""

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr ""

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr ""

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr ""

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr ""

#: part/models.py:4445
msgid "BOM item notes"
msgstr ""

#: part/models.py:4451
msgid "Checksum"
msgstr ""

#: part/models.py:4452
msgid "BOM line checksum"
msgstr ""

#: part/models.py:4457
msgid "Validated"
msgstr ""

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr ""

#: part/models.py:4463
msgid "Gets inherited"
msgstr ""

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr ""

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr ""

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr ""

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr ""

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr ""

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr ""

#: part/models.py:4774
msgid "Parent BOM item"
msgstr ""

#: part/models.py:4782
msgid "Substitute part"
msgstr ""

#: part/models.py:4798
msgid "Part 1"
msgstr "Позиція 1"

#: part/models.py:4806
msgid "Part 2"
msgstr "Позиція 2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr ""

#: part/models.py:4814
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr ""

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr ""

#: part/serializers.py:116
msgid "Parent Category"
msgstr ""

#: part/serializers.py:117
msgid "Parent part category"
msgstr ""

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr ""

#: part/serializers.py:202
msgid "Results"
msgstr "Результати"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr ""

#: part/serializers.py:275
msgid "File is not an image"
msgstr ""

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:480
msgid "Original Part"
msgstr ""

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr ""

#: part/serializers.py:486
msgid "Copy Image"
msgstr ""

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr ""

#: part/serializers.py:493
msgid "Copy BOM"
msgstr ""

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr ""

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr ""

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr ""

#: part/serializers.py:507
msgid "Copy Notes"
msgstr ""

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr ""

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr ""

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr ""

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr ""

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr ""

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr ""

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr ""

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Виробничий номер позиції"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr ""

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr ""

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr ""

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr ""

#: part/serializers.py:907
msgid "Category Name"
msgstr ""

#: part/serializers.py:936
msgid "Building"
msgstr ""

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr ""

#: part/serializers.py:968
msgid "Revisions"
msgstr ""

#: part/serializers.py:972
msgid "Suppliers"
msgstr ""

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr ""

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:992
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr ""

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr ""

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Початковий запас"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr ""

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr ""

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr ""

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr ""

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr ""

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Наявне зображення"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr ""

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr ""

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr ""

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr ""

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Мінімальна ціна"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr ""

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr ""

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Максимальна ціна"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr ""

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr ""

#: part/serializers.py:1498
msgid "Update"
msgstr ""

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr ""

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr ""

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr ""

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr ""

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1716
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr ""

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr ""

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr ""

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr ""

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr ""

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr ""

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr ""

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr ""

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr ""

#: part/tasks.py:40
msgid "Low stock notification"
msgstr ""

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr ""

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:107
msgid "Sample"
msgstr ""

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr ""

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr ""

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr ""

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr ""

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr ""

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr ""

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr ""

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr ""

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr ""

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr ""

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr ""

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr ""

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr ""

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr ""

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr ""

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr ""

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr ""

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr ""

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr ""

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr ""

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr ""

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr ""

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr ""

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr ""

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr ""

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr ""

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr ""

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr ""

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr ""

#: plugin/models.py:46
msgid "Key of plugin"
msgstr ""

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr ""

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr ""

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr ""

#: plugin/models.py:175
msgid "Sample plugin"
msgstr ""

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr ""

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:209
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr ""

#: plugin/plugin.py:384
msgid "No author found"
msgstr ""

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr ""

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr ""

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr ""

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr ""

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr ""

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr ""

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr ""

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr ""

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr ""

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr ""

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr ""

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr ""

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr ""

#: plugin/serializers.py:128
msgid "Version"
msgstr ""

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr ""

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr ""

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr ""

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr ""

#: plugin/serializers.py:188
msgid "Full reload"
msgstr ""

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr ""

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr ""

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr ""

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr ""

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr ""

#: report/api.py:114
msgid "Plugin not found"
msgstr ""

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr ""

#: report/helpers.py:46
msgid "Letter"
msgstr ""

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:217
msgid "Template name"
msgstr ""

#: report/models.py:223
msgid "Template description"
msgstr ""

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:281
msgid "Filename Pattern"
msgstr ""

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:287
msgid "Template is enabled"
msgstr ""

#: report/models.py:294
msgid "Target model type for template"
msgstr ""

#: report/models.py:314
msgid "Filters"
msgstr ""

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr ""

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr ""

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr ""

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr ""

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr ""

#: report/models.py:674
msgid "Height [mm]"
msgstr ""

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr ""

#: report/models.py:780
msgid "Error printing labels"
msgstr ""

#: report/models.py:799
msgid "Snippet"
msgstr ""

#: report/models.py:800
msgid "Report snippet file"
msgstr ""

#: report/models.py:807
msgid "Snippet file description"
msgstr ""

#: report/models.py:825
msgid "Asset"
msgstr ""

#: report/models.py:826
msgid "Report asset file"
msgstr ""

#: report/models.py:833
msgid "Asset file description"
msgstr ""

#: report/serializers.py:96
msgid "Select report template"
msgstr ""

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:137
msgid "Select label template"
msgstr ""

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR-код"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR-код"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr ""

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Зображення позиції"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr ""

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr ""

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr ""

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr ""

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr ""

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr ""

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr ""

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr ""

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr ""

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr ""

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr ""

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr ""

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr ""

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr ""

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr ""

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr ""

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr ""

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr ""

#: stock/api.py:283
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr ""

#: stock/api.py:340
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:594
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:623
msgid "Minimum stock"
msgstr ""

#: stock/api.py:627
msgid "Maximum stock"
msgstr ""

#: stock/api.py:630
msgid "Status Code"
msgstr ""

#: stock/api.py:670
msgid "External Location"
msgstr ""

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:868
msgid "Part Tree"
msgstr ""

#: stock/api.py:890
msgid "Updated before"
msgstr ""

#: stock/api.py:894
msgid "Updated after"
msgstr ""

#: stock/api.py:898
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:902
msgid "Stocktake After"
msgstr ""

#: stock/api.py:907
msgid "Expiry date before"
msgstr ""

#: stock/api.py:911
msgid "Expiry date after"
msgstr ""

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr ""

#: stock/api.py:1015
msgid "Quantity is required"
msgstr ""

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr ""

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr ""

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr ""

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr ""

#: stock/models.py:72
msgid "Stock Location type"
msgstr ""

#: stock/models.py:73
msgid "Stock Location types"
msgstr ""

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr ""

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr ""

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr ""

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr ""

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr ""

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr ""

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr ""

#: stock/models.py:227
msgid "This is an external stock location"
msgstr ""

#: stock/models.py:233
msgid "Location type"
msgstr ""

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr ""

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr ""

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr ""

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr ""

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr ""

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr ""

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr ""

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr ""

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr ""

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr ""

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr ""

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr ""

#: stock/models.py:1028
msgid "Base part"
msgstr ""

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr ""

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr ""

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr ""

#: stock/models.py:1064
msgid "Installed In"
msgstr ""

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr ""

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr ""

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr ""

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr ""

#: stock/models.py:1120
msgid "Source Build"
msgstr ""

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr ""

#: stock/models.py:1130
msgid "Consumed By"
msgstr ""

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr ""

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr ""

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr ""

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr ""

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr ""

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr ""

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr ""

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr ""

#: stock/models.py:1234
msgid "Converted to part"
msgstr ""

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr ""

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr ""

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr ""

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr ""

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr ""

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr ""

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr ""

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr ""

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr ""

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr ""

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr ""

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr ""

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr ""

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr ""

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr ""

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2847
msgid "Entry notes"
msgstr ""

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr ""

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr ""

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2951
msgid "Test result"
msgstr ""

#: stock/models.py:2958
msgid "Test output value"
msgstr ""

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr ""

#: stock/models.py:2970
msgid "Test notes"
msgstr ""

#: stock/models.py:2978
msgid "Test station"
msgstr ""

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2985
msgid "Started"
msgstr ""

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2992
msgid "Finished"
msgstr ""

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:451
msgid "Parent Item"
msgstr ""

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr ""

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr ""

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr ""

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr ""

#: stock/serializers.py:652
msgid "Child Items"
msgstr ""

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr ""

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr ""

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr ""

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr ""

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr ""

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr ""

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr ""

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr ""

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr ""

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr ""

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr ""

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr ""

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr ""

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr ""

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr ""

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr ""

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr ""

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr ""

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr ""

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr ""

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr ""

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr ""

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr ""

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr ""

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr ""

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr ""

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr ""

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr ""

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr ""

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr ""

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr ""

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr ""

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr ""

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr ""

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr ""

#: stock/serializers.py:1551
msgid "No Change"
msgstr ""

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr ""

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr ""

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr ""

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr ""

#: stock/status_codes.py:13
msgid "Damaged"
msgstr ""

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr ""

#: stock/status_codes.py:15
msgid "Rejected"
msgstr ""

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr ""

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr ""

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr ""

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr ""

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr ""

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr ""

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr ""

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr ""

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr ""

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr ""

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr ""

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr ""

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr ""

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr ""

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr ""

#: stock/status_codes.py:72
msgid "Split child item"
msgstr ""

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr ""

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr ""

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr ""

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr ""

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr ""

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr ""

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr ""

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr ""

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr ""

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr ""

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr ""

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr ""

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr ""

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr ""

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr ""

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr ""

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr ""

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr ""

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr ""

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr ""

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr ""

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr ""

#: templates/base.html:51
msgid "Server Restart Required"
msgstr ""

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr ""

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr ""

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr ""

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr ""

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr ""

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr ""

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr ""

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr ""

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr ""

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr ""

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr ""

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr ""

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr ""

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr ""

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr ""

#: users/admin.py:137
msgid "Personal info"
msgstr ""

#: users/admin.py:139
msgid "Permissions"
msgstr ""

#: users/admin.py:142
msgid "Important dates"
msgstr ""

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr ""

#: users/authentication.py:33
msgid "Token has expired"
msgstr ""

#: users/models.py:100
msgid "API Token"
msgstr ""

#: users/models.py:101
msgid "API Tokens"
msgstr ""

#: users/models.py:137
msgid "Token Name"
msgstr ""

#: users/models.py:138
msgid "Custom token name"
msgstr ""

#: users/models.py:144
msgid "Token expiry date"
msgstr ""

#: users/models.py:152
msgid "Last Seen"
msgstr ""

#: users/models.py:153
msgid "Last time the token was used"
msgstr ""

#: users/models.py:157
msgid "Revoked"
msgstr ""

#: users/models.py:235
msgid "Permission set"
msgstr ""

#: users/models.py:244
msgid "Group"
msgstr ""

#: users/models.py:248
msgid "View"
msgstr ""

#: users/models.py:248
msgid "Permission to view items"
msgstr ""

#: users/models.py:252
msgid "Add"
msgstr ""

#: users/models.py:252
msgid "Permission to add items"
msgstr ""

#: users/models.py:256
msgid "Change"
msgstr ""

#: users/models.py:258
msgid "Permissions to edit items"
msgstr ""

#: users/models.py:262
msgid "Delete"
msgstr ""

#: users/models.py:264
msgid "Permission to delete items"
msgstr ""

#: users/models.py:501
msgid "Bot"
msgstr ""

#: users/models.py:502
msgid "Internal"
msgstr ""

#: users/models.py:504
msgid "Guest"
msgstr ""

#: users/models.py:513
msgid "Language"
msgstr ""

#: users/models.py:514
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:519
msgid "Theme"
msgstr ""

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr ""

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr ""

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:541
msgid "Position"
msgstr ""

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr ""

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr ""

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr ""

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr ""

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr ""

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr ""

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr ""

#: users/ruleset.py:34
msgid "Return Orders"
msgstr ""

#: users/serializers.py:196
msgid "Username"
msgstr ""

#: users/serializers.py:199
msgid "First Name"
msgstr "Ім`я"

#: users/serializers.py:199
msgid "First name of the user"
msgstr ""

#: users/serializers.py:203
msgid "Last Name"
msgstr "Прізвище"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr ""

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "Адреса електронної пошти користувача"

#: users/serializers.py:326
msgid "Staff"
msgstr "Персонал"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr ""

#: users/serializers.py:332
msgid "Superuser"
msgstr ""

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr ""

#: users/serializers.py:336
msgid "Is this user account active"
msgstr ""

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:452
msgid "Your account has been created."
msgstr ""

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr ""

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr ""

