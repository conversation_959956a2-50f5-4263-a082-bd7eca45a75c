msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: German\n"
"Language: de_DE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: de\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Sie müssen die Zwei-Faktor-Authentifizierung aktivieren, bevor <PERSON>e etwas tun können."

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "API-Endpunkt nicht gefunden"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Ungültige Artikelliste angegeben"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Ungültige Filter angegeben"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr "Alle Filter dürfen nur mit true verwendet werden"

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "Benutzer hat keine Berechtigung, dieses Modell anzuzeigen"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "E-Mail (nochmal)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "Bestätigung der E-Mail Adresse"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "E-Mail Adressen müssen übereinstimmen."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Die angegebene primäre E-Mail-Adresse ist ungültig."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Die angegebene E-Mail-Domain ist nicht freigegeben."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Ungültige Einheit angegeben ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Kein Wert angegeben"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Konnte {original} nicht in {unit} umwandeln"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Keine gültige Menge"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Fehlerdetails finden Sie im Admin-Panel"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Datum eingeben"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Ungültiger Dezimalwert"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Notizen"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Wert '{name}' hält das Musterformat nicht ein"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Angegebener Wert entspricht nicht dem benötigten Muster: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Kann nicht mehr als 1000 Elemente auf einmal serialisieren"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Keine Seriennummer angegeben"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Duplizierter Seriennummer"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Ungültige Gruppe: {group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Gruppenbereich {group} überschreitet die zulässige Menge ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Keine Seriennummern gefunden"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Entferne HTML-Tags von diesem Wert"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "Daten enthalten verbotene Markdown-Inhalte"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Verbindungsfehler"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Server antwortete mit ungültigem Statuscode"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Ausnahme aufgetreten"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Server antwortete mit ungültigem Wert für die Inhaltslänge"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Bild ist zu groß"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Bilddownload überschreitet maximale Größe"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Remote-Server gab leere Antwort zurück"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Angegebene URL ist kein gültiges Bild"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabisch"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgarisch"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tschechisch"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Dänisch"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Deutsch"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Griechisch"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Englisch"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spanisch"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spanisch (Mexikanisch)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estnisch"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Persisch"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Beenden"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Französisch"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebräisch"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hinduistisch"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Ungarisch"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italienisch"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japanisch"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Koreanisch"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Litauisch"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Lettisch"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Niederländisch"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norwegisch"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polnisch"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugiesisch"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugiesisch (Brasilien)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumänisch"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russisch"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slowakisch"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slowenisch"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbisch"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Schwedisch"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thailändisch"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Türkisch"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrainisch"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamesisch"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Chinesisch (Vereinfacht)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Chinesisch (Traditionell)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Bei der App anmelden"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "Email"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Fehler beim Ausführen der Plugin Validierung"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Metadaten müssen ein Python-Dict Objekt sein"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Plugin Metadaten"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "JSON-Metadatenfeld, für die Verwendung durch externe Plugins"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Falsch formatiertes Muster"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Unbekannter Formatschlüssel angegeben"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Erforderlicher Formatschlüssel fehlt"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "Referenz-Feld darf nicht leer sein"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "Referenz muss erforderlichem Muster entsprechen"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Referenznummer ist zu groß"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Ungültige Auswahl"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Name"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Beschreibung"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Beschreibung (optional)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Pfad"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Doppelte Namen können nicht unter dem selben Elternteil existieren"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Markdown Notizen (optional)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Barcode-Daten"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Drittanbieter-Barcode-Daten"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Barcode-Hash"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Eindeutiger Hash der Barcode-Daten"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Bestehender Barcode gefunden"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Aufgabe fehlgeschlagen"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Hintergrundarbeiteraufgabe '{f}' fehlgeschlagen nach {n} Versuchen"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Serverfehler"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "Ein Fehler wurde vom Server protokolliert."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Muss eine gültige Nummer sein"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Währung"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Währung aus verfügbaren Optionen auswählen"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Ungültiger Wert"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Grafiken aus externen Quellen"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL der Remote-Bilddatei"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Das Herunterladen von Bildern von Remote-URLs ist nicht aktiviert"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Fehler beim Herunterladen des Bildes von entfernter URL"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Ungültige physikalische Einheit"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Kein gültiger Währungscode"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Bestellstatus"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Eltern-Bauauftrag"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Varianten einschließen"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Teil"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Kategorie"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Vorgänger-Build"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Mir zugewiesen"

#: build/api.py:154
msgid "Assigned To"
msgstr "Zugewiesen zu"

#: build/api.py:189
msgid "Created before"
msgstr "Erstellt vor"

#: build/api.py:193
msgid "Created after"
msgstr "Erstellt nach"

#: build/api.py:197
msgid "Has start date"
msgstr "Hat Startdatum"

#: build/api.py:205
msgid "Start date before"
msgstr "Gültigkeitsdauer vor"

#: build/api.py:209
msgid "Start date after"
msgstr "Gültigkeitsdauer nach"

#: build/api.py:213
msgid "Has target date"
msgstr "geplantes Bestelldatum"

#: build/api.py:221
msgid "Target date before"
msgstr "Zieldatum vor"

#: build/api.py:225
msgid "Target date after"
msgstr "Zieldatum nach"

#: build/api.py:229
msgid "Completed before"
msgstr "Abgeschlossen vor"

#: build/api.py:233
msgid "Completed after"
msgstr "Abgeschlossen nach"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr ""

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr ""

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Baum ausschließen"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "Bauauftrag muss abgebrochen werden, bevor er gelöscht werden kann"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Verbrauchsmaterial"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Optional"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Baugruppe"

#: build/api.py:450
msgid "Tracked"
msgstr "Nachverfolgt"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Prüfbar"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "Offene Bestellung"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Zugeordnet"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Verfügbar"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Bauauftrag"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Lagerort"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Bauaufträge"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "Die Stückliste wurde noch nicht kontrolliert"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Baureihenfolge kann nicht für ein inaktives Teil erstellt werden"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Baureihenfolge kann nicht für ein inaktives Teil erstellt werden"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Verantwortlicher Benutzer oder Gruppe muss angegeben werden"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "Teil in Bauauftrag kann nicht geändert werden"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr "Zieldatum muss nach dem Startdatum liegen"

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Bauauftragsreferenz"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referenz"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Kurze Beschreibung des Baus (optional)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Bauauftrag, zu dem dieser Bauauftrag zugwiesen ist"

#: build/models.py:273
msgid "Select part to build"
msgstr "Teil für den Bauauftrag wählen"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Auftrag Referenz"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Bestellung, die diesem Bauauftrag zugewiesen ist"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Quell-Lagerort"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Entnahme-Lagerort für diesen Bauauftrag wählen (oder leer lassen für einen beliebigen Lagerort)"

#: build/models.py:300
msgid "External Build"
msgstr ""

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr "Ziel-Lagerort"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Lagerort an dem fertige Objekte gelagert werden auswählen"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Bau-Anzahl"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Anzahl der zu bauenden Lagerartikel"

#: build/models.py:322
msgid "Completed items"
msgstr "Fertiggestellte Teile"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Anzahl der fertigen Lagerartikel"

#: build/models.py:328
msgid "Build Status"
msgstr "Bauauftrags-Status"

#: build/models.py:333
msgid "Build status code"
msgstr "Bau-Statuscode"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Losnummer"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Losnummer für dieses Endprodukt"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Erstelldatum"

#: build/models.py:356
msgid "Build start date"
msgstr "Startdatum des Bauauftrags"

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr "Geplantes Startdatum des Bauauftrags"

#: build/models.py:363
msgid "Target completion date"
msgstr "geplantes Fertigstellungsdatum"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Zieldatum für Bauauftrag-Fertigstellung."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Fertigstellungsdatum"

#: build/models.py:378
msgid "completed by"
msgstr "Fertiggestellt von"

#: build/models.py:387
msgid "Issued by"
msgstr "Aufgegeben von"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Nutzer der diesen Bauauftrag erstellt hat"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Verantwortlicher Benutzer"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Benutzer oder Gruppe verantwortlich für diesen Bauauftrag"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Externer Link"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Link zu einer externen URL"

#: build/models.py:410
msgid "Build Priority"
msgstr "Bauauftrags-Priorität"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Priorität dieses Bauauftrags"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Projektcode"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Projektcode für diesen Auftrag"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Fehler beim Abladen der Aufgabe, um die Build-Allokation abzuschließen"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Bauauftrag {build} wurde fertiggestellt"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "Ein Bauauftrag wurde fertiggestellt"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Seriennummern müssen für nachverfolgbare Teile angegeben werden"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "kein Endprodukt angegeben"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "Endprodukt bereits hergstellt"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "Endprodukt stimmt nicht mit dem Bauauftrag überein"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "Anzahl muss größer Null sein"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "Menge kann nicht größer als die Ausgangsmenge sein"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Build Ausgabe {serial} hat nicht alle erforderlichen Tests bestanden"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Bauauftragsposition"

#: build/models.py:1602
msgid "Build object"
msgstr "Objekt bauen"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Anzahl"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Erforderliche Menge für Auftrag"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Bauauftragsposition muss ein Endprodukt festlegen, da der übergeordnete Teil verfolgbar ist"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Zugewiesene Menge ({q}) darf nicht verfügbare Menge ({a}) übersteigen"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "BestandObjekt ist zu oft zugewiesen"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "Reserviermenge muss größer null sein"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "Anzahl muss 1 für Objekte mit Seriennummer sein"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "Ausgewählter Lagerbestand stimmt nicht mit BOM-Linie überein"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Lagerartikel"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Quell-Lagerartikel"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Anzahl an Lagerartikel dem Bauauftrag zuweisen"

#: build/models.py:1924
msgid "Install into"
msgstr "Installiere in"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Ziel-Lagerartikel"

#: build/serializers.py:115
msgid "Build Level"
msgstr ""

#: build/serializers.py:124
msgid "Part Name"
msgstr "Name des Teils"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr ""

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Endprodukt"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "Endprodukt stimmt nicht mit übergeordnetem Bauauftrag überein"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "Endprodukt entspricht nicht dem Teil des Bauauftrags"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Dieses Endprodukt wurde bereits fertiggestellt"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Dieses Endprodukt ist nicht vollständig zugewiesen"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Menge der Endprodukte angeben"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Ganzzahl für verfolgbare Teile erforderlich"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Ganzzahl erforderlich da die Stückliste nachverfolgbare Teile enthält"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Seriennummer"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Seriennummer für dieses Endprodukt eingeben"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Lagerort für Bauprodukt"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Seriennummern automatisch zuweisen"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Benötigte Lagerartikel automatisch mit passenden Seriennummern zuweisen"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "Die folgenden Seriennummern existieren bereits oder sind ungültig"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "Eine Liste von Endprodukten muss angegeben werden"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Lagerort für ausgemusterte Ausgänge"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Zuteilungen verwerfen"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Bestandszuteilung für ausgemusterte Endprodukte verwerfen"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Grund für das Verwerfen des Bauauftrages/der Bauaufträge"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Lagerort für fertige Endprodukte"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Unvollständige Zuweisung akzeptieren"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Endprodukte fertigstellen, auch wenn Bestand nicht fertig zugewiesen wurde"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Zugewiesen Bestand verbrauchen"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Verbrauche alle Bestände, die diesem Bauauftrag bereits zugewiesen wurden"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Unfertige Endprodukte entfernen"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Lösche alle noch nicht abgeschlossenen Endprodukte"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Nicht erlaubt"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Als von diesem Bauauftrag verbraucht setzen"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Bestandszuordnung vor dem Abschluss dieses Bauauftrags freigeben"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Überbelegter Lagerbestand"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Wie sollen zusätzliche Lagerbestandteile, die dem Bauauftrag zugewiesen wurden, behandelt werden"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Der Bestand einiger Lagerartikel ist überbelegt"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Nicht zugewiesene akzeptieren"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Akzeptieren, dass Lagerartikel diesem Bauauftrag nicht vollständig zugewiesen wurden"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "Benötigter Bestand wurde nicht vollständig zugewiesen"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Unvollständig Zuweisung akzeptieren"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Akzeptieren, dass die erforderliche Anzahl der Bauaufträge nicht abgeschlossen ist"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "Benötigte Teil-Anzahl wurde noch nicht fertiggestellt"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr ""

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr ""

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "Bauauftrag hat unvollständige Aufbauten"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Bauauftragsposition"

#: build/serializers.py:877
msgid "Build output"
msgstr "Endprodukt"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "Endprodukt muss auf den gleichen Bauauftrag verweisen"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Bauauftragspositionsartikel"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part muss auf dasselbe Teil verweisen wie der Bauauftrag"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "Teil muss auf Lager sein"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Verfügbare Menge ({q}) überschritten"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Für Zuweisung von verfolgten Teilen muss ein Endprodukt angegeben sein"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Endprodukt kann bei Zuweisung nicht-verfolgter Teile nicht angegeben werden"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Zuweisungen müssen angegeben werden"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Lagerort, von dem Teile bezogen werden sollen (leer lassen, um sie von jedem Lagerort zu nehmen)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Lagerort ausschließen"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Lagerartikel vom ausgewählten Ort ausschließen"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Wechselbares Lagerbestand"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Lagerartikel an mehreren Standorten können austauschbar verwendet werden"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Ersatzbestand"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Zuordnung von Ersatzteilen erlauben"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Optionale Positionen"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Optionale Stücklisten-Positionen dem Bauauftrag hinzufügen"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Fehler beim Starten der automatischen Zuweisung"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "Stücklisten-Referenz"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr ""

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr ""

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "Zusammenbau"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Zuliefererteil"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Zugewiesene Menge"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr ""

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr ""

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Nachverfolgbar"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Vererbt"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Varianten zulassen"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "Stücklisten-Position"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "Bestellt"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "In Produktion"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Externes Lager"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Verfügbarer Bestand"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Verfügbares Ersatzmaterial"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr ""

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Ausstehend"

#: build/status_codes.py:12
msgid "Production"
msgstr "in Arbeit"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr ""

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Storniert"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Fertig"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Bestand für Bauauftrag erforderlich"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Überfälliger Bauauftrag"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Bauauftrag {bo} ist jetzt überfällig"

#: common/api.py:688
msgid "Is Link"
msgstr "Link"

#: common/api.py:696
msgid "Is File"
msgstr "Datei"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr ""

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "Benutzer hat keine Berechtigung zum Löschen des Anhangs"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Ungültiges Währungskürzel"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Doppeltes Währungskürzel"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr ""

#: common/currency.py:146
msgid "No plugin"
msgstr "Kein Plugin"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Aktualisiert"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Zeitstempel der letzten Aktualisierung"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "Eindeutiger Projektcode"

#: common/models.py:171
msgid "Project description"
msgstr "Projektbeschreibung"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Benutzer oder Gruppe verantwortlich für dieses Projekt"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr ""

#: common/models.py:780
msgid "Settings value"
msgstr "Einstellungs-Wert"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "Wert ist keine gültige Option"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Wahrheitswert erforderlich"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Nur Ganzzahl eingeben"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:914
msgid "Key string must be unique"
msgstr "Schlüsseltext muss eindeutig sein"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Benutzer"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Preisstaffelungs Anzahl"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Preis"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Stückpreis für die angegebene Anzahl"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Endpunkt"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Endpunkt, an dem dieser Webhook empfangen wird"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Name für diesen Webhook"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Aktiv"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Ist dieser Webhook aktiv"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Token"

#: common/models.py:1437
msgid "Token for access"
msgstr "Token für Zugang"

#: common/models.py:1445
msgid "Secret"
msgstr "Geheimnis"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Shared Secret für HMAC"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "Nachrichten-ID"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Eindeutige Kennung für diese Nachricht"

#: common/models.py:1563
msgid "Host"
msgstr "Host"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Host von dem diese Nachricht empfangen wurde"

#: common/models.py:1572
msgid "Header"
msgstr "Kopfzeile"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Header dieser Nachricht"

#: common/models.py:1580
msgid "Body"
msgstr "Body"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Body dieser Nachricht"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Endpunkt, über den diese Nachricht empfangen wurde"

#: common/models.py:1596
msgid "Worked on"
msgstr "Bearbeitet"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Wurde die Arbeit an dieser Nachricht abgeschlossen?"

#: common/models.py:1723
msgid "Id"
msgstr "ID"

#: common/models.py:1725
msgid "Title"
msgstr "Titel"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Link"

#: common/models.py:1729
msgid "Published"
msgstr "Veröffentlicht"

#: common/models.py:1731
msgid "Author"
msgstr "Autor"

#: common/models.py:1733
msgid "Summary"
msgstr "Zusammenfassung"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Gelesen"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Wurde diese Nachricht gelesen?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Bild"

#: common/models.py:1753
msgid "Image file"
msgstr "Bilddatei"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr ""

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr ""

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Benutzerdefinierte Einheit"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "Einheitensymbol muss eindeutig sein"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Einheitsname muss eine gültige Kennung sein"

#: common/models.py:1843
msgid "Unit name"
msgstr "Einheitsname"

#: common/models.py:1850
msgid "Symbol"
msgstr "Symbol"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Optionales Einheitssymbol"

#: common/models.py:1857
msgid "Definition"
msgstr "Definition"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Einheitsdefinition"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Anhang"

#: common/models.py:1933
msgid "Missing file"
msgstr "Fehlende Datei"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Fehlender externer Link"

#: common/models.py:1971
msgid "Model type"
msgstr ""

#: common/models.py:1972
msgid "Target model type for image"
msgstr ""

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Datei zum Anhängen auswählen"

#: common/models.py:1996
msgid "Comment"
msgstr "Kommentar"

#: common/models.py:1997
msgid "Attachment comment"
msgstr ""

#: common/models.py:2013
msgid "Upload date"
msgstr "Upload Datum"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Datum der hochgeladenen Datei"

#: common/models.py:2018
msgid "File size"
msgstr "Dateigröße"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Dateigröße in Bytes"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Ungültiger Modelltyp für Anhang angegeben"

#: common/models.py:2077
msgid "Custom State"
msgstr ""

#: common/models.py:2078
msgid "Custom States"
msgstr ""

#: common/models.py:2083
msgid "Reference Status Set"
msgstr ""

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr ""

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Wert"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2102
msgid "Name of the state"
msgstr "Name des Bundeslandes"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Bezeichnung"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "Bezeichnung, die im Frontend angezeigt wird"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Farbe"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "Farbe, die im Frontend angezeigt wird"

#: common/models.py:2128
msgid "Model"
msgstr ""

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr ""

#: common/models.py:2144
msgid "Model must be selected"
msgstr ""

#: common/models.py:2147
msgid "Key must be selected"
msgstr ""

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr ""

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr ""

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "Name muss sich von den Namen des Referenzstatus unterscheiden"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Auswahlliste"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "Auswahllisten"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "Name der Auswahlliste"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "Beschreibung der Auswahlliste"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Gesperrt"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "Ist diese Auswahlliste gesperrt?"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "Kann diese Auswahlliste benutzt werden?"

#: common/models.py:2255
msgid "Source Plugin"
msgstr ""

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2261
msgid "Source String"
msgstr ""

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2271
msgid "Default Entry"
msgstr ""

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Erstellt"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2283
msgid "Last Updated"
msgstr "Zuletzt aktualisiert"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2318
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2319
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2372
msgid "Barcode Scan"
msgstr ""

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Wert"

#: common/models.py:2377
msgid "Barcode data"
msgstr ""

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr ""

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Zeitstempel"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr ""

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Kontext"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2415
msgid "Response"
msgstr ""

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Ergebnis"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr ""

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr ""

#: common/models.py:2574
msgid "Email Messages"
msgstr ""

#: common/models.py:2581
msgid "Announced"
msgstr ""

#: common/models.py:2583
msgid "Sent"
msgstr ""

#: common/models.py:2584
msgid "Failed"
msgstr ""

#: common/models.py:2587
msgid "Delivered"
msgstr ""

#: common/models.py:2595
msgid "Confirmed"
msgstr ""

#: common/models.py:2601
msgid "Inbound"
msgstr ""

#: common/models.py:2602
msgid "Outbound"
msgstr ""

#: common/models.py:2607
msgid "No Reply"
msgstr ""

#: common/models.py:2608
msgid "Track Delivery"
msgstr ""

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr ""

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr ""

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr ""

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr ""

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Schlüssel"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Neue {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Eine neue Bestellung wurde erstellt und Ihnen zugewiesen"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} storniert"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Eine Bestellung, die Ihnen zugewiesen war, wurde storniert"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Artikel erhalten"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Artikel wurden aus einer Bestellung erhalten"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Artikel wurden aus einer Rücksendung erhalten"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr ""

#: common/serializers.py:486
msgid "Is Running"
msgstr "Wird ausgeführt"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Anstehende Aufgaben"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Geplante Aufgaben"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Fehlgeschlagene Aufgaben"

#: common/serializers.py:519
msgid "Task ID"
msgstr "Aufgabe-ID"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "Eindeutige Aufgaben-ID"

#: common/serializers.py:521
msgid "Lock"
msgstr "Sperren"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Sperrzeit"

#: common/serializers.py:523
msgid "Task name"
msgstr "Aufgabenname"

#: common/serializers.py:525
msgid "Function"
msgstr "Funktion"

#: common/serializers.py:525
msgid "Function name"
msgstr "Funktionsname"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Parameter"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Aufgaben-Parameter"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Schlüsselwort Parameter"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Schlüsselwort Parameter für Aufgaben"

#: common/serializers.py:640
msgid "Filename"
msgstr "Dateiname"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Modelltyp"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Benutzer hat keine Berechtigung, Anhänge für dieses Modell zu erstellen oder zu bearbeiten"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "Keine Gruppe"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "Seiten-URL ist durch die Konfiguration gesperrt"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Neustart erforderlich"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "Eine Einstellung wurde geändert, die einen Neustart des Servers erfordert"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Ausstehende Migrationen"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Anzahl der ausstehenden Datenbankmigrationen"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:199
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Name der Serverinstanz"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Kurze Beschreibung der Instanz"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Name der Instanz verwenden"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Den Namen der Instanz in der Titelleiste verwenden"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Anzeige von `Über` einschränken"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Zeige das `Über` Fenster nur Administratoren"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Firmenname"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "interner Firmenname"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "Basis-URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "Basis-URL für dieses Instanz"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Standardwährung"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Wählen Sie die Basiswährung für Preisberechnungen aus"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Verfügbare Währungen"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Liste der unterstützten Währungskürzel"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Währungsaktualisierungsintervall"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Wie oft Wechselkurse aktualisiert werden sollen (auf Null zum Deaktivieren setzen)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "Tage"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Währungs-Aktualisierungs-Plugin"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Zu verwendendes Währungs-Aktualisierungs-Plugin"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Von URL herunterladen"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Herunterladen von externen Bildern und Dateien von URLs erlaubt"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Download-Größenlimit"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Maximal zulässige Größe für heruntergeladene Bilder"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "Benutzer-Agent zum Herunterladen von Daten"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Überschreiben des Benutzer-Agenten, der verwendet wird, um Bilder und Dateien von externer Servern herunterzuladen (leer für die Standardeinstellung)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Strenge URL-Prüfung"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Erfordert die Schema-Spezifikation bei der Validierung von URLs"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Prüfungsintervall aktualisieren"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Wie oft soll nach Updates gesucht werden? (auf 0 setzen zum Deaktivieren)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Automatische Sicherung"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Automatische Sicherung der Datenbank- und Mediendateien aktivieren"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Intervall für automatische Sicherung"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Anzahl der Tage zwischen automatischen Sicherungen"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Aufgabenlöschinterval"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Ergebnisse der Hintergrundaufgabe werden nach der angegebenen Anzahl von Tagen gelöscht"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Löschintervall für Fehlerprotokolle"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Fehlerprotokolle werden nach der angegebenen Anzahl von Tagen gelöscht"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Löschintervall für Benachrichtigungen"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Benutzerbenachrichtigungen werden nach der angegebenen Anzahl von Tagen gelöscht"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Bacode-Feature verwenden"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Barcode-Scanner Unterstützung im Webinterface aktivieren"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr ""

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr ""

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr ""

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr ""

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Barcode-Eingabeverzögerung"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Verzögerungszeit bei Barcode-Eingabe"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Barcode Webcam-Unterstützung"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Barcode-Scannen über Webcam im Browser erlauben"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr ""

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr ""

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr ""

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Artikelrevisionen"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Revisions-Feld für Artikel aktivieren"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Löschen aus Baugruppe erlauben"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Erlaube das Löschen von Teilen, die in einer Baugruppe verwendet werden"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN Regex"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "RegEx Muster für die Zuordnung von Teil-IPN"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Mehrere Artikel mit gleicher IPN erlaubt"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Mehrere Artikel mit gleicher IPN erlaubt"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Ändern von IPN erlaubt"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Ändern der IPN während des Bearbeiten eines Teils erlaubt"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Teil-Stückliste kopieren"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Stückliste von Teil kopieren wenn das Teil dupliziert wird "

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Teil-Parameter kopieren"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Parameter-Daten für dieses Teil kopieren wenn das Teil dupliziert wird"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Teil-Testdaten kopieren"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Test-Daten für dieses Teil kopieren wenn das Teil dupliziert wird"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Kategorie-Parametervorlage kopieren"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Kategorie-Parameter Vorlagen kopieren wenn ein Teil angelegt wird"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Vorlage"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Teile sind standardmäßig Vorlagen"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Teile können standardmäßig aus anderen Teilen angefertigt werden"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Komponente"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Teile können standardmäßig in Baugruppen benutzt werden"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Kaufbar"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Artikel sind grundsätzlich kaufbar"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Verkäuflich"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Artikel sind grundsätzlich verkaufbar"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Artikel sind grundsätzlich verfolgbar"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtuell"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Teile sind grundsätzlich virtuell"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Verwandte Teile anzeigen"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Verwandte Teile eines Teils anzeigen"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Initialer Lagerbestand"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Erstellen von Lagerbestand beim Hinzufügen eines neuen Teils erlauben"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Initiale Lieferantendaten"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Erstellen von Lieferantendaten beim Hinzufügen eines neuen Teils erlauben"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Anzeigeformat für Teilenamen"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Format für den Namen eines Teiles"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Standardsymbol der Teilkategorie"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Standardsymbol der Teilkategorie (leer bedeutet kein Symbol)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Parameter Einheiten durchsetzen"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Wenn Einheiten angegeben werden, müssen die Parameterwerte mit den angegebenen Einheiten übereinstimmen"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Dezimalstellen für minimalen Preis"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Mindestanzahl der Dezimalstellen bei der Darstellung der Preisdaten"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Dezimalstellen für maximalen Preis"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Maximale Anzahl der Dezimalstellen bei der Darstellung der Preisdaten"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Zulieferer-Preise verwenden"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Lieferanten-Staffelpreise in die Gesamt-Preisberechnungen einbeziehen"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Kaufverlauf überschreiben"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Historische Bestellungspreise überschreiben die Lieferanten-Staffelpreise"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Lagerartikel-Preis verwenden"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Preise aus manuell eingegebenen Lagerdaten für Preisberechnungen verwenden"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Lagerartikelpreis Alter"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Lagerartikel, die älter als diese Anzahl an Tagen sind, von der Preisberechnung ausschließen"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Variantenpreise verwenden"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Variantenpreise in die Gesamt-Preisberechnungen einbeziehen"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Nur aktive Varianten"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Nur aktive Variantenteile zur Berechnung der Variantenbepreisung verwenden"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Intervall für Neuberechnung von Preisen"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Anzahl der Tage bis die Teile-Preisberechnungen automatisch aktualisiert werden"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Interne Preise"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Interne Preise für Teile aktivieren"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Interne Preisüberschreibung"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Falls verfügbar, überschreiben interne Preise Preispannenberechnungen"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Labeldruck aktivieren"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Labeldruck über die Website aktivieren"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "Label Bild DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "DPI-Auflösung bei der Erstellung von Bilddateien für Etikettendruck-Plugins"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Berichte aktivieren"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Berichterstellung aktivieren"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Entwickler-Modus"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Berichte im Entwickler-Modus generieren (als HTML)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Berichtsfehler protokollieren"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Fehler, die beim Erstellen von Berichten auftreten, protokollieren"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Seitengröße"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Standardseitenformat für PDF-Bericht"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Global einzigartige Seriennummern"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "Seriennummern für Lagerartikel müssen global eindeutig sein"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Erschöpften Lagerartikel löschen"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Legt das Standardverhalten fest, wenn ein Lagerartikel aufgebraucht ist"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Losnummer Vorlage"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Vorlage für die Generierung von Standard-Losnummern für Lagerbestände"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Bestands-Ablauf"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Ablaufen von Bestand ermöglichen"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Abgelaufenen Bestand verkaufen"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Verkauf von abgelaufenem Bestand erlaubt"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Bestands-Stehzeit"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Anzahl an Tagen, an denen Bestand als abgestanden markiert wird, bevor sie ablaufen"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Abgelaufenen Bestand verbauen"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Verbauen von abgelaufenen Bestand erlaubt"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Bestands-Eigentümerkontrolle"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Eigentümerkontrolle für Lagerorte und Teile aktivieren"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Standardsymbol für Lagerort"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Standardsymbol für Lagerstandort (leer bedeutet kein Symbol)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Zeige installierte Lagerartikel"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Anzeige der installierten Lagerartikel in Bestandstabellen"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Prüfe BOM bei der Installation von Elementen"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Installierte Lagerbestandteile müssen im BOM für den übergeordneten Teil vorhanden sein"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Erlaube Verschieben von \"nicht auf Lager\" Bestand"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Lagerartikel, die nicht auf Lager sind, können zwischen Lagerstandorten übertragen werden"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Bauauftragsreferenz-Muster"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Benötigtes Muster für die Generierung des Referenzfeldes für Bauaufträge"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "Verantwortlicher Besitzer erforderlich"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "Jeder Bestellung muss ein verantwortlicher Besitzer zugewiesen werden"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr ""

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr ""

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr ""

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr ""

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr ""

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr ""

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr ""

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Blockieren bis Test bestanden"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Verhindert die Fertigstellung bis alle erforderlichen Tests bestanden sind"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Rücksendungen aktivieren"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Aktivieren der Rücksendung-Funktion in der Benutzeroberfläche"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Referenz Muster für Rücksendungen"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Benötigtes Muster für die Generierung des Referenzfeldes für Rücksendungen"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Abgeschlossene Rücksendungen bearbeiten"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Bearbeitung von Rücksendungen nach Abschluss erlauben"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Auftragsreferenz-Muster"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Benötigtes Muster für die Generierung des Referenzfeldes für Aufträge"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Auftrag Standardsendung"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Erstelle eine Standardsendung für Aufträge"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Abgeschlossene Aufträge bearbeiten"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Bearbeitung von Aufträgen nach Versand oder Abschluss erlauben"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Versendete Bestellungen als abgeschlossen markieren"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Als versendet markierte Aufträge werden automatisch abgeschlossen und überspringen den Status \"Versandt\""

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Bestellungsreferenz-Muster"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Benötigtes Muster für die Generierung des Referenzfeldes für Bestellungen"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Abgeschlossene Einkaufsaufträge bearbeiten"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Bearbeitung von Einkaufsaufträgen nach Versand oder Abschluss erlauben"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Bestellungen automatisch abschließen"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Bestellung automatisch als abgeschlossen markieren, wenn der Empfang aller Artikel bestätigt wurde"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Passwort vergessen aktivieren"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Passwort-vergessen-Funktion auf den Anmeldeseiten aktivieren"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Registrierung erlauben"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Selbstregistrierung für Benutzer auf den Anmeldeseiten aktivieren"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "SSO aktivieren"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "SSO auf den Anmeldeseiten aktivieren"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "SSO Selbstregistrierung aktivieren"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Selbstregistrierung über SSO für Benutzer auf den Anmeldeseiten aktivieren"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "SSO Gruppensynchronisation aktivieren"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr ""

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "SSO Gruppenschlüssel"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr ""

#: common/setting/system.py:943
msgid "SSO group map"
msgstr ""

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr ""

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr ""

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr ""

#: common/setting/system.py:959
msgid "Email required"
msgstr "Email-Adresse erforderlich"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Benutzer müssen bei der Registrierung eine E-Mail angeben"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "SSO-Benutzer automatisch ausfüllen"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Benutzer-Details automatisch aus SSO-Konto ausfüllen"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "E-Mail zweimal"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Bei der Registrierung den Benutzer zweimal nach der E-Mail-Adresse fragen"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Passwort zweimal"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Bei der Registrierung den Benutzer zweimal nach dem Passwort fragen"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Erlaubte Domains"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Anmeldung auf bestimmte Domänen beschränken (kommagetrennt, beginnend mit @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Gruppe bei Registrierung"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr ""

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "MFA erzwingen"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Benutzer müssen Multifaktor-Authentifizierung verwenden."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Plugins beim Start prüfen"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Beim Start überprüfen, ob alle Plugins installiert sind - Für Container aktivieren"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Nach Plugin-Aktualisierungen suchen"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Periodische Überprüfungen auf Updates für installierte Plugins aktivieren"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "URL-Integration aktivieren"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Plugins zum Hinzufügen von URLs aktivieren"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Navigations-Integration aktivieren"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Plugins zur Integration in die Navigation aktivieren"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "App-Integration aktivieren"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Plugins zum Hinzufügen von Apps aktivieren"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Terminplan-Integration aktivieren"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Geplante Aufgaben aktivieren"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Ereignis-Integration aktivieren"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Plugins ermöglichen auf interne Ereignisse zu reagieren"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Externe Standorte ausschließen"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Automatische Inventur-Periode"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Vollständige Namen von Benutzern anzeigen"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Vollständigen Namen von Benutzern anstatt Benutzername anzeigen"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Teststation-Daten aktivieren"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Teststation-Datenerfassung für Testergebnisse aktivieren"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Label inline anzeigen"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "PDF-Labels im Browser anzeigen, anstatt als Datei herunterzuladen"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Standard-Etikettendrucker"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Einen standardmäßig ausgewählten Etikettendrucker konfigurieren"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Berichte inline anzeigen"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "PDF-Berichte im Browser anzeigen, anstatt als Datei herunterzuladen"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Teile suchen"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Teile in der Suchvorschau anzeigen"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Zulieferteile durchsuchen"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Zuliefererteile in der Suchvorschau anzeigen"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Herstellerteile durchsuchen"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Herstellerteile in der Suchvorschau anzeigen"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Inaktive Teile ausblenden"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Inaktive Teile in der Suchvorschau ausblenden"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Kategorien durchsuchen"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Teilekategorien in der Suchvorschau anzeigen"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Bestand durchsuchen"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Lagerartikel in Suchvorschau anzeigen"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Nicht verfügbare Artikel ausblenden"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Nicht verfügbare Lagerartikel aus der Suchvorschau ausschließen"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Lagerorte durchsuchen"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Lagerorte in Suchvorschau anzeigen"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Firmen durchsuchen"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Firmen in der Suchvorschau anzeigen"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Bauaufträge durchsuchen"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Bauaufträge in der Suchvorschau anzeigen"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Bestellungen durchsuchen"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Bestellungen in der Suchvorschau anzeigen"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Inaktive Bestellungen ausblenden"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Inaktive Bestellungen in der Suchvorschau ausblenden"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Aufträge durchsuchen"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Aufträge in der Suchvorschau anzeigen"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Inaktive Aufträge ausblenden"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Inaktive Aufträge in der Suchvorschau ausblenden"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Suche nach Rücksendungen"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Rücksendungen in der Suchvorschau anzeigen"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Inaktive Rücksendungen ausblenden"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Inaktive Rücksendungen in der Suchvorschau ausblenden"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Anzahl Suchergebnisse"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Anzahl der Ergebnisse, die in der Vorschau pro Sektion angezeigt werden sollen"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex Suche"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Reguläre Ausdrücke in Suchabfragen aktivieren"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Ganzes Wort suchen"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Suchabfragen liefern Ergebnisse für ganze Wortkombinationen"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Esc-Taste schließt Formulare"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Benutze die Esc-Taste, um Formulare zu schließen"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Fixierter Navigationsleiste"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "Position der Navigationsleiste am oberen Bildschirmrand fixieren"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Datumsformat"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Bevorzugtes Format für die Anzeige von Daten"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Fehlerberichte empfangen"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Benachrichtigungen bei Systemfehlern erhalten"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Zuletzt verwendete Druckmaschinen"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Die zuletzt benutzten Druckmaschinen für einen Benutzer speichern"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr ""

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr ""

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr ""

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr ""

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Eine leere Domain ist nicht erlaubt."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Ungültiger Domainname: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "Teil ist aktiv"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Hersteller ist aktiv"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Lieferantenteil ist aktiv"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Internes Teil ist aktiv"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Lieferant ist aktiv"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Hersteller"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Firma"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:120
msgid "Companies"
msgstr "Firmen"

#: company/models.py:148
msgid "Company description"
msgstr "Firmenbeschreibung"

#: company/models.py:149
msgid "Description of the company"
msgstr "Firmenbeschreibung"

#: company/models.py:155
msgid "Website"
msgstr "Webseite"

#: company/models.py:156
msgid "Company website URL"
msgstr "Firmenwebsite Adresse/URL"

#: company/models.py:162
msgid "Phone number"
msgstr "Kontakt-Tel."

#: company/models.py:164
msgid "Contact phone number"
msgstr "Kontakt-Telefon"

#: company/models.py:171
msgid "Contact email address"
msgstr "Kontakt-Email"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Kontakt"

#: company/models.py:178
msgid "Point of contact"
msgstr "Anlaufstelle"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Link auf externe Firmeninformation"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Ist dieses Unternehmen aktiv?"

#: company/models.py:203
msgid "Is customer"
msgstr "Ist Kunde"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Verkaufen Sie Teile an diese Firma?"

#: company/models.py:209
msgid "Is supplier"
msgstr "Ist Zulieferer"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Kaufen Sie Teile von dieser Firma?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "Ist Hersteller"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Produziert diese Firma Teile?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Standard-Währung für diese Firma"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Adresse"

#: company/models.py:355
msgid "Addresses"
msgstr "Adressen"

#: company/models.py:412
msgid "Select company"
msgstr "Firma auswählen"

#: company/models.py:417
msgid "Address title"
msgstr "Adresstitel"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Titel zur Beschreibung des Adresseintrages"

#: company/models.py:424
msgid "Primary address"
msgstr "Primäre Adresse"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Als primäre Adresse festlegen"

#: company/models.py:430
msgid "Line 1"
msgstr "Linie 1"

#: company/models.py:431
msgid "Address line 1"
msgstr "Adresszeile 1"

#: company/models.py:437
msgid "Line 2"
msgstr "Linie 2"

#: company/models.py:438
msgid "Address line 2"
msgstr "Adresszeile 2"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "Postleitzahl"

#: company/models.py:451
msgid "City/Region"
msgstr "Stadt/Region"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Postleitzahl Stadt/Region"

#: company/models.py:458
msgid "State/Province"
msgstr "Staat/Provinz"

#: company/models.py:459
msgid "State or province"
msgstr "Bundesland"

#: company/models.py:465
msgid "Country"
msgstr "Land"

#: company/models.py:466
msgid "Address country"
msgstr "Adresse Land"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Versandnotizen"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Notizen für Versandkurier"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Interne Versandnotizen"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Versandnotizen für interne Verwendung"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Link zu Adressinformationen (extern)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Herstellerteil"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Basisteil"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Teil auswählen"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Hersteller auswählen"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "MPN"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Hersteller-Teilenummer"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "Externe URL für das Herstellerteil"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Teilbeschreibung des Herstellers"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Teilenummer des Herstellers"

#: company/models.py:635
msgid "Parameter name"
msgstr "Parametername"

#: company/models.py:642
msgid "Parameter value"
msgstr "Parameterwert"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Einheiten"

#: company/models.py:650
msgid "Parameter units"
msgstr "Parametereinheit"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Packeinheiten müssen mit den Basisteileinheiten kompatibel sein"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Packeinheiten müssen größer als Null sein"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "Verlinktes Herstellerteil muss dasselbe Basisteil referenzieren"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Zulieferer"

#: company/models.py:829
msgid "Select supplier"
msgstr "Zulieferer auswählen"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Lagerbestandseinheit (SKU) des Zulieferers"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Ist dieser Lieferantenteil aktiv?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Herstellerteil auswählen"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "Teil-URL des Zulieferers"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Zuliefererbeschreibung des Teils"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Notiz"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "Basiskosten"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Mindestpreis"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Verpackungen"

#: company/models.py:892
msgid "Part packaging"
msgstr "Teile-Verpackungen"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Packmenge"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Gesamtmenge, die in einer einzelnen Packung geliefert wird. Für Einzelstücke leer lassen."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "Vielfache"

#: company/models.py:919
msgid "Order multiple"
msgstr "Mehrere bestellen"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Verfügbare Menge von Lieferanten"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Verfügbarkeit aktualisiert"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Datum des letzten Updates der Verfügbarkeitsdaten"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr ""

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Standard-Währung für diesen Zulieferer"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Firmenname"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "Auf Lager"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Export-Format"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "Export-Dateiformat wählen"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Export-Plugin"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "Export-Plugin wählen"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Platziert"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:73
msgid "Data File"
msgstr "Datendatei"

#: importer/models.py:74
msgid "Data file to import"
msgstr "Zu importierende Datei"

#: importer/models.py:83
msgid "Columns"
msgstr "Spalten"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:96
msgid "Import status"
msgstr "Importstatus"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Standardwerte"

#: importer/models.py:113
msgid "Field Overrides"
msgstr ""

#: importer/models.py:120
msgid "Field Filters"
msgstr ""

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr ""

#: importer/models.py:471
msgid "Field"
msgstr ""

#: importer/models.py:473
msgid "Column"
msgstr ""

#: importer/models.py:542
msgid "Row Index"
msgstr ""

#: importer/models.py:545
msgid "Original row data"
msgstr ""

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Fehler"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Gültig"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Dateiformat nicht unterstützt"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Fehler beim Öffnen der Datei"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Ungültige Dateigröße"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:177
msgid "Rows"
msgstr "Zeilen"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Liste der zu akzeptierenden Zeilen IDs"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Keine Zeilen ausgewählt"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr ""

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr ""

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr ""

#: importer/status_codes.py:13
msgid "Initializing"
msgstr ""

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr ""

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr ""

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr ""

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr ""

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr ""

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr ""

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr ""

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Kopien"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Anzahl der zu druckenden Kopien für jedes Label"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Verbunden"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Unbekannt"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Drucken"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Keine Medien"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr ""

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Verbindung getrennt"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Etikettendrucker"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Drucken Sie Etiketten direkt für verschiedene Artikel."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Druckerstandort"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Den Drucker an einen bestimmten Ort aufstellen"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Name des Geräts"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Gerätetyp"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Typ der Maschine"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Treiber"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Verwendeter Treiber für die Maschine"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Maschinen können deaktiviert werden"

#: machine/models.py:95
msgid "Driver available"
msgstr "Treiber verfügbar"

#: machine/models.py:100
msgid "No errors"
msgstr "Keine Fehler"

#: machine/models.py:105
msgid "Initialized"
msgstr "Initialisiert"

#: machine/models.py:117
msgid "Machine status"
msgstr "Status der Maschine"

#: machine/models.py:145
msgid "Machine"
msgstr "Maschine"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Maschinenkonfiguration"

#: machine/models.py:162
msgid "Config type"
msgstr "Konfigurationstyp"

#: order/api.py:121
msgid "Order Reference"
msgstr "Bestellreferenz"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "Ausstehend"

#: order/api.py:165
msgid "Has Project Code"
msgstr ""

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Erstellt von"

#: order/api.py:183
msgid "Created Before"
msgstr ""

#: order/api.py:187
msgid "Created After"
msgstr ""

#: order/api.py:191
msgid "Has Start Date"
msgstr ""

#: order/api.py:199
msgid "Start Date Before"
msgstr ""

#: order/api.py:203
msgid "Start Date After"
msgstr ""

#: order/api.py:207
msgid "Has Target Date"
msgstr ""

#: order/api.py:215
msgid "Target Date Before"
msgstr ""

#: order/api.py:219
msgid "Target Date After"
msgstr ""

#: order/api.py:270
msgid "Has Pricing"
msgstr "Hat Preise"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr ""

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr ""

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Bestellung"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "Bestellung abgeschlossen"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Internes Teil"

#: order/api.py:578
msgid "Order Pending"
msgstr "Bestellung ausstehend"

#: order/api.py:958
msgid "Completed"
msgstr "Fertig"

#: order/api.py:1214
msgid "Has Shipment"
msgstr ""

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Bestellung"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Auftrag"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Rücksendeauftrag"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Gesamtpreis"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Gesamtpreis für diese Bestellung"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Auftragswährung"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Währung für diesen Auftrag (leer lassen, um Firmenstandard zu verwenden)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "Kontakt stimmt nicht mit der ausgewählten Firma überein"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Auftragsbeschreibung (optional)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Projektcode für diesen Auftrag auswählen"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Link auf externe Seite"

#: order/models.py:458
msgid "Start date"
msgstr ""

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Zieldatum"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Geplantes Lieferdatum für Auftrag."

#: order/models.py:487
msgid "Issue Date"
msgstr "Aufgabedatum"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Datum an dem die Bestellung aufgegeben wurde"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Nutzer oder Gruppe der/die für diesen Auftrag zuständig ist/sind"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Ansprechpartner für diesen Auftrag"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Firmenadresse für diesen Auftrag"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Bestell-Referenz"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Status"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Bestellungs-Status"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Firma bei der die Teile bestellt werden"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Zulieferer-Referenz"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Zulieferer Bestellreferenz"

#: order/models.py:654
msgid "received by"
msgstr "Empfangen von"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Datum an dem der Auftrag fertigstellt wurde"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Ziel-Lager"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr ""

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Teile-Zulieferer muss dem Zulieferer der Bestellung entsprechen"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "Position stimmt nicht mit Kaufauftrag überein"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "Anzahl muss eine positive Zahl sein"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Kunde"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Firma an die die Teile verkauft werden"

#: order/models.py:1318
msgid "Sales order status"
msgstr ""

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Kundenreferenz"

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Bestellreferenz"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Versanddatum"

#: order/models.py:1343
msgid "shipped by"
msgstr "Versand von"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "Bestellung ist bereits abgeschlossen"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "Bestellung ist bereits storniert"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Nur ein offener Auftrag kann als abgeschlossen markiert werden"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Auftrag kann nicht abgeschlossen werden, da unvollständige Sendungen vorhanden sind"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Auftrag kann nicht abgeschlossen werden, da es unvollständige Positionen gibt"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1711
msgid "Item quantity"
msgstr "Anzahl"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Position - Referenz"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Position - Notizen"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Zieldatum für diesen Einzelposten (leer lassen, um das Zieldatum des Auftrags zu verwenden)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Positionsbeschreibung (optional)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Zusätzlicher Kontext für diese Zeile"

#: order/models.py:1788
msgid "Unit price"
msgstr "Stückpreis"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "Lieferantenteil muss mit Lieferant übereinstimmen"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr "Zuliefererteil"

#: order/models.py:1891
msgid "Received"
msgstr "Empfangen"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Empfangene Objekt-Anzahl"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Preis"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Preis pro Einheit"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Ein virtuelles Teil kann nicht einem Auftrag zugeordnet werden"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Nur verkaufbare Teile können einem Auftrag zugewiesen werden"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Verkaufspreis"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Stückverkaufspreis"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Versendet"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Versendete Menge"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Versanddatum"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Lieferdatum"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Versanddatum"

#: order/models.py:2221
msgid "Checked By"
msgstr "Kontrolliert von"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Benutzer, der diese Sendung kontrolliert hat"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Sendung"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Sendungsnummer"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Sendungsverfolgungsnummer"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Informationen zur Sendungsverfolgung"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "Rechnungsnummer"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Referenznummer für zugehörige Rechnung"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "Sendung wurde bereits versandt"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "Sendung hat keine zugewiesene Lagerartikel"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "Lagerartikel wurde nicht zugewiesen"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Kann Lagerartikel keiner Zeile mit einem anderen Teil hinzufügen"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Kann Lagerartikel keiner Zeile ohne Teil hinzufügen"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Die zugeordnete Anzahl darf nicht die verfügbare Anzahl überschreiten"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "Anzahl für serialisierte Lagerartikel muss 1 sein"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "Auftrag gehört nicht zu Sendung"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Sendung gehört nicht zu Auftrag"

#: order/models.py:2451
msgid "Line"
msgstr "Position"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Sendungsnummer-Referenz"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Position"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Lagerartikel für Zuordnung auswählen"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Anzahl für Bestandszuordnung eingeben"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Rücksendungsreferenz"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Firma von der die Artikel zurückgeschickt werden"

#: order/models.py:2625
msgid "Return order status"
msgstr "Status der Rücksendung"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Artikel zur Rücksendung auswählen"

#: order/models.py:2910
msgid "Received Date"
msgstr "Empfangsdatum"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "Das Datum des Empfangs dieses Rücksendeartikels"

#: order/models.py:2923
msgid "Outcome"
msgstr "Ergebnis"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "Ergebnis für dieses Zeilenelement"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "Kosten für die Rückgabe oder Reparatur dieses Objektes"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:90
msgid "Order ID"
msgstr ""

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:96
msgid "Copy Lines"
msgstr ""

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Positionen"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "Abgeschlossene Positionen"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Lieferant"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "Bestellung kann nicht verworfen werden"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Erlaube das Schließen des Auftrags mit unvollständigen Positionen"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "Auftrag hat unvollständige Positionen"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "Der Auftrag ist nicht offen"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "Automatische Preisgestaltung"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Kaufpreis automatisch basierend auf Lieferantenbestandsdaten berechnen"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Kaufpreiswährung"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Elemente zusammenfügen"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Zusammenführen von Elementen mit dem gleichen Teil, Ziel- und Zieldatum zu einem Zeilenelement"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "Lieferanten-Teilenummer"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Interne Teilenummer"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "Zuliefererteil muss ausgewählt werden"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "Bestellung muss angegeben sein"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "Lieferant muss mit der Bestellung übereinstimmen"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "Die Bestellung muss mit dem Lieferant übereinstimmen"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Position"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Zielort für empfangene Teile auswählen"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Losnummer für eingehende Lagerartikel"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Ablaufdatum"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Seriennummern für eingehende Lagerartikel"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:826
msgid "Barcode"
msgstr "Barcode"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Gescannter Barcode"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Barcode ist bereits in Verwendung"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Positionen müssen angegeben werden"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "Ziel-Lagerort muss angegeben werden"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "Barcode muss eindeutig sein"

#: order/serializers.py:1066
msgid "Shipments"
msgstr ""

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Abgeschlossene Sendungen"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Verkaufspreis-Währung"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Keine Sendungsdetails angegeben"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "Position ist nicht diesem Auftrag zugeordnet"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "Anzahl muss positiv sein"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Seriennummern zum Zuweisen eingeben"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "Sendung wurde bereits versandt"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "Sendung ist nicht diesem Auftrag zugeordnet"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Folgende Serienummern konnten nicht gefunden werden"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr ""

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "Artikel der Bestellzeile zurücksenden"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "Artikel entspricht nicht der Rücksendeschrift"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "Artikel wurde bereits erhalten"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "Artikel können nur bei laufenden Bestellungen empfangen werden"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "Verkaufspreis-Währung"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Verloren"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Zurückgegeben"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "In Bearbeitung"

#: order/status_codes.py:105
msgid "Return"
msgstr "Zurück"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Reparatur"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Ersetzen"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Rückerstattung"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Ablehnen"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Überfällige Bestellung"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Bestellung {po} ist jetzt überfällig"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Überfälliger Auftrag"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Auftrag {so} ist jetzt überfällig"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:111
msgid "Starred"
msgstr "Markiert"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "Nach markierten Kategorien filtern"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Ebenen"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "Filter nach Kategorietiefe"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "Oberste Ebene"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "Mehrstufig"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "Unterkategorien in gefilterte Ergebnisse einbeziehen"

#: part/api.py:185
msgid "Parent"
msgstr "Übergeordnetes"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Nach übergeordneter Kategorie filtern"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "Unterkategorien in der angegebenen Kategorie ausschließen"

#: part/api.py:434
msgid "Has Results"
msgstr "Ergebnisse"

#: part/api.py:660
msgid "Is Variant"
msgstr ""

#: part/api.py:668
msgid "Is Revision"
msgstr ""

#: part/api.py:678
msgid "Has Revisions"
msgstr ""

#: part/api.py:859
msgid "BOM Valid"
msgstr ""

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1511
msgid "Component part is testable"
msgstr ""

#: part/api.py:1576
msgid "Uses"
msgstr "Verwendet"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Teil-Kategorie"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Teil-Kategorien"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Standard-Lagerort"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Standard-Lagerort für Teile dieser Kategorie"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Strukturell"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Teile können nicht direkt einer strukturellen Kategorie zugeordnet werden, können aber untergeordneten Kategorien zugeordnet werden."

#: part/models.py:134
msgid "Default keywords"
msgstr "Standard Stichwörter"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Standard-Stichworte für Teile dieser Kategorie"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Symbol"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Symbol (optional)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Sie können diese Teilekategorie nicht als strukturell festlegen, da ihr bereits Teile zugewiesen sind!"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Teile"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Dieses Teil kann nicht gelöscht werden, da es noch aktiv ist"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Dieses Teil kann nicht gelöscht werden, da es in einem Bauauftrag verwendet wird"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Teil '{self}' kann in der Stückliste nicht für '{parent}' (rekursiv) verwendet werden"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Teil '{parent}' wird in der Stückliste für '{self}' (rekursiv) verwendet"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN muss mit Regex-Muster {pattern} übereinstimmen"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:724
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Ein Lagerartikel mit dieser Seriennummer existiert bereits"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "Doppelte IPN in den Teil-Einstellungen nicht erlaubt"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Teil mit diesem Namen, IPN und Revision existiert bereits."

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Strukturellen Teilekategorien können keine Teile zugewiesen werden!"

#: part/models.py:1051
msgid "Part name"
msgstr "Name des Teils"

#: part/models.py:1056
msgid "Is Template"
msgstr "Ist eine Vorlage"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Ist dieses Teil eine Vorlage?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Ist dieses Teil eine Variante eines anderen Teils?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Variante von"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Artikelbeschreibung (optional)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Schlüsselwörter"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Schlüsselworte um die Sichtbarkeit in Suchergebnissen zu verbessern"

#: part/models.py:1093
msgid "Part category"
msgstr "Teile-Kategorie"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN (Interne Produktnummer)"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Revisions- oder Versionsnummer"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Version"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1119
msgid "Revision Of"
msgstr ""

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Wo wird dieses Teil normalerweise gelagert?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Standard Zulieferer"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Standard Zuliefererteil"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Standard Ablaufzeit"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Ablauf-Zeit (in Tagen) für Bestand dieses Teils"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Minimaler Bestand"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Minimal zulässiger Bestand"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Maßeinheit für diesen Teil"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Kann dieses Teil aus anderen Teilen angefertigt werden?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Kann dieses Teil zum Bauauftrag von anderen genutzt werden?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Hat dieses Teil Tracking für einzelne Objekte?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Kann dieses Teil von externen Zulieferern gekauft werden?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Kann dieses Teil an Kunden verkauft werden?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Ist dieses Teil aktiv?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Ist dieses Teil virtuell, wie zum Beispiel eine Software oder Lizenz?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "Prüfsumme der Stückliste"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Prüfsumme der Stückliste gespeichert"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "Stückliste kontrolliert von"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "BOM Kontrolldatum"

#: part/models.py:1312
msgid "Creation User"
msgstr "Erstellungs-Nutzer"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Verantwortlicher Besitzer für dieses Teil"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Mehrere verkaufen"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Währung für die Berechnung der Preise im Cache"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Minimale Stücklisten Kosten"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Minimale Kosten für Teile"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Maximale Stücklisten Kosten"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Maximale Kosten für Teile"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Minimale Einkaufskosten"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Minimale historische Kaufkosten"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Maximale Einkaufskosten"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Maximale historische Einkaufskosten"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Minimaler interner Preis"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Minimale Kosten basierend auf den internen Staffelpreisen"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Maximaler interner Preis"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Maximale Kosten basierend auf internen Preisstaffeln"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Minimaler Lieferantenpreis"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Mindestpreis für Teil von externen Lieferanten"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Maximaler Lieferantenpreis"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Maximaler Preis für Teil von externen Lieferanten"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Minimale Variantenkosten"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Berechnete minimale Kosten für Variantenteile"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Maximale Variantenkosten"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Berechnete maximale Kosten für Variantenteile"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Minimale Kosten"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Mindestkosten überschreiben"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Maximale Kosten"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Maximale Kosten überschreiben"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Berechnete Mindestkosten"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Berechnete Maximalkosten"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Mindestverkaufspreis"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Mindestverkaufspreis basierend auf Staffelpreisen"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Maximaler Verkaufspreis"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Maximalverkaufspreis basierend auf Staffelpreisen"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Mindestverkaufskosten"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Minimaler historischer Verkaufspreis"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Maximale Verkaufskosten"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Maximaler historischer Verkaufspreis"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Teil für die Inventur"

#: part/models.py:3444
msgid "Item Count"
msgstr "Stückzahl"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Anzahl einzelner Bestandseinträge zum Zeitpunkt der Inventur"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Insgesamt verfügbarer Lagerbestand zum Zeitpunkt der Inventur"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Datum"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Datum der Inventur"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Mindestbestandswert"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Geschätzter Mindestwert des vorhandenen Bestands"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Maximaler Bestandswert"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Geschätzter Maximalwert des vorhandenen Bestands"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3595
msgid "Part Test Template"
msgstr ""

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Ungültiger Vorlagenname - es muss mindestens ein alphanumerisches Zeichen enthalten sein"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "Auswahl muss einzigartig sein"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr ""

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "Testvorlage mit demselben Schlüssel existiert bereits für Teil"

#: part/models.py:3684
msgid "Test Name"
msgstr "Test-Name"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Namen für diesen Test eingeben"

#: part/models.py:3691
msgid "Test Key"
msgstr "Testschlüssel"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "Vereinfachter Schlüssel zum Test"

#: part/models.py:3699
msgid "Test Description"
msgstr "Test-Beschreibung"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Beschreibung für diesen Test eingeben"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Aktiviert"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "Ist dieser Test aktiviert?"

#: part/models.py:3709
msgid "Required"
msgstr "Benötigt"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Muss dieser Test erfolgreich sein?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Erfordert Wert"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Muss für diesen Test ein Wert für das Test-Ergebnis eingetragen werden?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Anhang muss eingegeben werden"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Muss für diesen Test ein Anhang für das Test-Ergebnis hinzugefügt werden?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "Auswahlmöglichkeiten"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "Gültige Optionen für diesen Test (durch Komma getrennt)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "Checkbox-Parameter können keine Einheiten haben"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "Checkbox-Parameter können keine Auswahl haben"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "Vorlagen-Name des Parameters muss eindeutig sein"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Name des Parameters"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Physikalische Einheiten für diesen Parameter"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Parameter-Beschreibung"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Checkbox"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Ist dieser Parameter eine Checkbox?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Gültige Optionen für diesen Parameter (durch Kommas getrennt)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3931
msgid "Part Parameter"
msgstr ""

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Ungültige Auswahl für Parameterwert"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Ausgangsteil"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Parameter Vorlage"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Parameter Wert"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Optionales Notizfeld"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4176
msgid "Default Value"
msgstr "Standard-Wert"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Standard Parameter Wert"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4363
msgid "Select parent part"
msgstr "Ausgangsteil auswählen"

#: part/models.py:4373
msgid "Sub part"
msgstr "Untergeordnetes Teil"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Teil für die Nutzung in der Stückliste auswählen"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "Stücklisten-Anzahl für dieses Stücklisten-Teil"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Diese Stücklisten-Position ist optional"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Diese Stücklisten-Position ist ein Verbrauchsartikel (sie wird nicht in Bauaufträgen verfolgt)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "Referenz der Postion auf der Stückliste"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "Notizen zur Stücklisten-Position"

#: part/models.py:4451
msgid "Checksum"
msgstr "Prüfsumme"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "Prüfsumme der Stückliste"

#: part/models.py:4457
msgid "Validated"
msgstr "überprüft"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Diese Stücklistenposition wurde validiert"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Wird vererbt"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Diese Stücklisten-Position wird in die Stücklisten von Teil-Varianten vererbt"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Bestand von Varianten kann für diese Stücklisten-Position verwendet werden"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "Menge muss eine Ganzzahl sein"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "Zuliefererteil muss festgelegt sein"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "Stücklisten Ersatzteile"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "Ersatzteil kann nicht identisch mit dem Hauptteil sein"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Übergeordnete Stücklisten Position"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Ersatzteil"

#: part/models.py:4798
msgid "Part 1"
msgstr "Teil 1"

#: part/models.py:4806
msgid "Part 2"
msgstr "Teil 2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "verknüpftes Teil auswählen"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Teil-Beziehung kann nicht zwischen einem Teil und sich selbst erstellt werden"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "Doppelte Beziehung existiert bereits"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "Übergeordnete Kategorie"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "Übergeordnete Teilkategorie"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Unter-Kategorien"

#: part/serializers.py:202
msgid "Results"
msgstr "Ergebnisse"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "Anzahl der Ergebnisse, die in dieser Vorlage aufgezeichnet wurden"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Kaufwährung dieses Lagerartikels"

#: part/serializers.py:275
msgid "File is not an image"
msgstr ""

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "Anzahl der Teile, die diese Vorlage verwenden"

#: part/serializers.py:480
msgid "Original Part"
msgstr "Originalteil"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Originalteil zum Duplizieren auswählen"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Bild kopieren"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Bild vom Originalteil kopieren"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Stückliste kopieren"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Stückliste vom Originalteil kopieren"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Parameter kopieren"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Parameterdaten vom Originalteil kopieren"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Anmerkungen kopieren"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Notizen aus Originalteil kopieren"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Start-Bestandsmenge"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Initiale Lagermenge für dieses Teil. Wenn die Menge null ist, wird kein Lagerbestand hinzugefügt."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Initialer Lagerort"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Lagerstandort für dieses Teil angeben"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Lieferant auswählen (oder leer lassen, um zu überspringen)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Hersteller auswählen (oder leer lassen, um zu überspringen)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Hersteller-Teilenummer"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "Ausgewählte Firma ist kein gültiger Lieferant"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "Ausgewählte Firma ist kein gültiger Hersteller"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "Herstellerteil mit dieser MPN existiert bereits"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "Lieferantenteil mit dieser SKU existiert bereits"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Kategoriename"

#: part/serializers.py:936
msgid "Building"
msgstr "Im Bau"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Lagerartikel"

#: part/serializers.py:968
msgid "Revisions"
msgstr ""

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Zulieferer"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Gesamtbestand"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "Nicht zugewiesenes Lager"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "Alternatives Lager"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Teil duplizieren"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Initiale Daten von anderem Teil kopieren"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Initialer Lagerbestand"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Erstelle Teil mit Ausgangsbestand"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Lieferanteninformationen"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Lieferanteninformationen zu diesem Teil hinzufügen"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Kategorieparameter kopieren"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Parametervorlagen aus der ausgewählten Teilkategorie kopieren"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Vorhandenes Bild"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "Dateiname eines vorhandenen Teilbildes"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "Bilddatei existiert nicht"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Gesamte Stückliste validieren"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Herstellbar"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Niedrigster Preis"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "Berechneten Wert für Mindestpreis überschreiben"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "Mindestpreis Währung"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Höchster Preis"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "Berechneten Wert für maximalen Preis überschreiben"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "Maximalpreis Währung"

#: part/serializers.py:1498
msgid "Update"
msgstr "Aktualisieren"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Preis für dieses Teil aktualisieren"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Konnte nicht von den angegebenen Währungen in {default_currency} umrechnen"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "Mindestpreis darf nicht größer als der Maximalpreis sein"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "Der Maximalpreis darf nicht kleiner als der Mindestpreis sein"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1716
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Teil auswählen, von dem Stückliste kopiert wird"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Bestehende Daten entfernen"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Bestehende Stücklisten-Positionen vor dem Kopieren entfernen"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Vererbtes einschließen"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Stücklisten-Positionen einbeziehen, die von Vorlage-Teilen geerbt werden"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Ungültige Zeilen überspringen"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Aktiviere diese Option, um ungültige Zeilen zu überspringen"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Ersatzteile kopieren"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Ersatzteile beim Duplizieren von Stücklisten-Positionen kopieren"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Benachrichtigungen über geringen Bestand"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Der verfügbare Bestand für {part.name} ist unter das konfigurierte Mindestniveau gefallen"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:107
msgid "Sample"
msgstr ""

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Installiert"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "Das Plugin kann nicht gelöscht werden, da es derzeit aktiv ist"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Keine Aktion angegeben"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Keine passende Aktion gefunden"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Keine Treffer für Barcode"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Treffer für Barcode gefunden"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Barcode entspricht einem bereits vorhandenen Artikel"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Keine passenden Teiledaten gefunden"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Keine passenden Zulieferteile gefunden"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Mehrere passende Zulieferteile gefunden"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Zulieferteil zugeordnet"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Artikel wurde bereits erhalten"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Mehrere passende Elemente gefunden"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Kein passendes Element gefunden"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Barcode stimmt nicht mit einem vorhandenen Lagerartikel überein"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Lagerartikel stimmt nicht mit dem Element überein"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Unzureichender Bestand verfügbar"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Lagerartikel der Bestellung zugeordnet"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Nicht genügend Informationen"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Weitere Informationen zum Empfang des Zeilenelements erforderlich"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Erhaltene Bestellartikel"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Gescannte Barcode Daten"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Ordne Artikel Bestellung zu"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Ordne erhaltene Artikel Bestellung zu"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Bestellung wurde nicht aufgegeben"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Ort für den Empfang von Artikeln"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Kann keinen strukturellen Standort auswählen"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Kundenauftrag zum Zuordnen von Artikeln zu"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Artikel der Verkaufsbestellung zuweisen"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Sendung des Verkaufsauftrags zur Zuweisung von Artikeln gegen"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Sendung wurde bereits geliefert"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Zugewiesene Menge"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Labeldruck fehlgeschlagen"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "Fehler beim Rendern des Etikett als PDF"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "Fehler beim Rendern des Etikett als HTML"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Keine Elemente zum Drucken übergeben"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr ""

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree Barcodes"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Bietet native Unterstützung für Barcodes"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "InvenTree Mitwirkende"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Eingehender Webhook-URL für Slack"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "URL, die verwendet wird, um Nachrichten an einen Slack-Kanal zu senden"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Link öffnen"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree Währungsumstellung"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Standard-Wechselkursintegration"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF-Etikettendrucker"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Bietet native Unterstützung für das Drucken von PDF-Etiketten"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Debug-Modus"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Debug-Modus aktivieren - gibt Roh-HTML statt PDF zurück"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree Maschinen-Etikettendrucker"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "Unterstützt das Drucken mit einer Maschine"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "Zuletzt benutzt"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "Optionen"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Seitengröße für das Etikett"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Etiketten überspringen"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Diese Anzahl der Etiketten beim Drucken von Etiketten überspringen"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Rand"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "Einen Rahmen um jedes Label drucken"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Querformat"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "Labelblatt im Querformat drucken"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "InvenTree Etikettendrucker"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "Anordnen mehrerer Etiketten auf einem einzigen Blatt"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "Label ist zu groß für Seitengröße"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "Es wurden keine Etiketten generiert"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "Lieferantenintegration - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Unterstützt das Scannen von DigiKey-Barcodes"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "Der Lieferant, der als 'DigiKey' fungiert"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "Lieferantenintegration - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "Unterstützt das Scannen von LCSC-Barcodes"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "Der Lieferant, der als \"LCSC\" fungiert"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "Lieferantenintegration - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "Unterstützt das Scannen von Mouser-Barcodes"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "Der Lieferant, der als 'Mouser' fungiert"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "Lieferantenintegration - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "Unterstützt das Scannen von TME-Barcodes"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "Der Lieferant, der als 'TME' fungiert"

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "Nur Mitarbeiter können Plugins verwalten"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "Plugin-Installation ist deaktiviert"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "Plugin wurde erfolgreich installiert"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Plugin installiert in {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "Plugin wurde nicht in der Registry gefunden"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "Plugin ist kein gepacktes Plugin"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "Plugin-Paketname nicht gefunden"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "Plugin-Deinstallation ist deaktiviert"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "Plugin kann nicht deinstalliert werden, da es momentan aktiv ist"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "Plugin erfolgreich deinstallieren"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Plugin-Konfiguration"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Plugin-Konfigurationen"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Schlüssel des Plugins"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Name des Plugins"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Paket-Name"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "Name des installierten Paketes, wenn das Plugin über PIP installiert wurde"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Ist das Plugin aktiv"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Beispiel-Plugin"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Integriertes Plugin"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "Paket-Plugin"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "Plugin"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Kein Autor gefunden"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Plugin '{p}' ist nicht kompatibel mit der aktuellen InvenTree Version {v}"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Plugin benötigt mindestens Version {v}"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Plugin benötigt maximal Version {v}"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "Bestellungen aktivieren"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "Kauf-Funktionalität in InvenTree aktivieren"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "API-Schlüssel"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Schlüssel für den Zugriff auf das externe API"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Numerisch"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "Eine numerische Einstellung"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Auswahleinstellungen"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "Eine Einstellung mit mehreren Optionen"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Beispiel-Währungswechsel-Plugin"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree Mitwirkende"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Quell-URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Quelle für das Paket - dies kann eine eigene Registry oder ein VCS-Pfad sein"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Name für das Plugin-Paket - kann auch einen Versionstext enthalten"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Version"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Versionsangabe für das Plugin. Leer lassen für die neueste Version."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Plugin-Installation bestätigen"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Dies wird dieses Plugin sofort in die aktuelle Instanz installieren. Die Instanz wird sofort in Wartung gehen."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installation nicht bestätigt"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Entweder Paketname oder URL muss angegeben werden"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Komplett neu laden"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Führe ein vollständiges Nachladen der Plugin-Registrierung durch"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Neuladen erzwingen"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Erzwinge ein erneutes Laden der Plugin-Registrierung, auch wenn sie bereits geladen ist"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Plugins sammeln"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Plugins sammeln und zur Registrierung hinzufügen"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "Plugin aktivieren"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "Dieses Plugin aktivieren"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "Konfiguration löschen"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "Plugin-Konfiguration aus der Datenbank löschen"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "Teile"

#: report/api.py:114
msgid "Plugin not found"
msgstr "Plugin nicht gefunden"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "US-Legal"

#: report/helpers.py:46
msgid "Letter"
msgstr "US-Letter"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:217
msgid "Template name"
msgstr "Vorlagen Name"

#: report/models.py:223
msgid "Template description"
msgstr ""

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Dateinamen-Muster"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:287
msgid "Template is enabled"
msgstr ""

#: report/models.py:294
msgid "Target model type for template"
msgstr ""

#: report/models.py:314
msgid "Filters"
msgstr "Filter"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr ""

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "Seitengröße für PDF-Berichte"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "Bericht in Querformat anzeigen"

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Breite [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Label-Breite in mm"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Höhe [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Label-Höhe in mm"

#: report/models.py:780
msgid "Error printing labels"
msgstr ""

#: report/models.py:799
msgid "Snippet"
msgstr "Schnipsel"

#: report/models.py:800
msgid "Report snippet file"
msgstr "Berichts-Snippet"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Snippet-Beschreibung"

#: report/models.py:825
msgid "Asset"
msgstr "Ressource"

#: report/models.py:826
msgid "Report asset file"
msgstr "Berichts-Ressource"

#: report/models.py:833
msgid "Asset file description"
msgstr "Ressource-Beschreibung"

#: report/serializers.py:96
msgid "Select report template"
msgstr ""

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:137
msgid "Select label template"
msgstr "Etiketten-Vorlage auswählen"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR-Code"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR-Code"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Stückliste"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Benötigte Materialien"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Artikelbild"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Aufgegeben"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "benötigt für"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Aufgegeben von"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Lieferant gelöscht"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Stück-Preis"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Zusätzliche Positionen"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Summe"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Seriennummer"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Zuweisungen"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Losnummer"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Lagerstandorte"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Lagerartikel Test-Bericht"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Verbaute Objekte"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Seriennummer"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Testergebnisse"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Test"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "bestanden"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "fehlgeschlagen"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Kein Ergebnis (erforderlich)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Kein Ergebnis"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Die Bestandsdatei ist nicht vorhanden"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Bilddatei nicht gefunden"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image tag benötigt eine Bauteilinstanz"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "company_image tag erfordert eine Firmeninstanz"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "Filtern nach Standorttiefe"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "Unterorte in gefilterte Ergebnisse einbeziehen"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "Übergeordneter Ort"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "Filtern nach übergeordnetem Ort"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:594
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:623
msgid "Minimum stock"
msgstr ""

#: stock/api.py:627
msgid "Maximum stock"
msgstr ""

#: stock/api.py:630
msgid "Status Code"
msgstr "Statuscode"

#: stock/api.py:670
msgid "External Location"
msgstr "Externer Standort"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:868
msgid "Part Tree"
msgstr "Teile-Baum"

#: stock/api.py:890
msgid "Updated before"
msgstr ""

#: stock/api.py:894
msgid "Updated after"
msgstr ""

#: stock/api.py:898
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:902
msgid "Stocktake After"
msgstr ""

#: stock/api.py:907
msgid "Expiry date before"
msgstr "Gültigkeitsdauer vor"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "Gültigkeitsdauer nach"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "überfällig"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "Menge ist erforderlich"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "Gültiges Teil muss angegeben werden"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "Der angegebene Lieferantenartikel existiert nicht"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Das Zulieferteil hat eine Packungsgröße definiert, aber das Kennzeichen use_pack_size ist nicht gesetzt"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Seriennummern können für nicht verfolgbare Teile nicht angegeben werden"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "Lagerstandort Typ"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "Lagerstandorte Typen"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Standardsymbol für alle Orte, die kein Icon gesetzt haben (optional)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Bestand-Lagerort"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Bestand-Lagerorte"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Besitzer"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Besitzer auswählen"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Lagerartikel können nicht direkt an einen strukturellen Lagerort verlegt werden, können aber an einen untergeordneten Lagerort verlegt werden."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Extern"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Dies ist ein externer Lagerort"

#: stock/models.py:233
msgid "Location type"
msgstr "Standorttyp"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "Standortart dieses Standortes"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Sie können diesen Lagerort nicht als strukturell markieren, da sich bereits Lagerartikel darin befinden!"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr ""

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Lagerartikel können nicht in strukturelle Lagerorte abgelegt werden!"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Für virtuelle Teile können keine Lagerartikel erstellt werden"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Artikeltyp ('{self.supplier_part.part}') muss {self.part} sein"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "Anzahl muss für Objekte mit Seriennummer 1 sein"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Seriennummer kann nicht gesetzt werden wenn die Anzahl größer als 1 ist"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "Teil kann nicht zu sich selbst gehören"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "Teil muss eine Referenz haben wenn is_building wahr ist"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "Referenz verweist nicht auf das gleiche Teil"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Eltern-Lagerartikel"

#: stock/models.py:1028
msgid "Base part"
msgstr "Basis-Teil"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Passendes Zuliefererteil für diesen Lagerartikel auswählen"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Wo wird dieses Teil normalerweise gelagert?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "Verpackung, in der dieser Lagerartikel gelagert ist"

#: stock/models.py:1064
msgid "Installed In"
msgstr "verbaut in"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "Ist dieses Teil in einem anderen verbaut?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Seriennummer für dieses Teil"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Losnummer für diesen Lagerartikel"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Bestand"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Quellbau"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Bauauftrag für diesen Lagerartikel"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "Verbraucht von"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "Bauauftrag der diesen Lagerartikel verbrauchte"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Quelle Bestellung"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Bestellung für diesen Lagerartikel"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Ziel-Auftrag"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Ablaufdatum für Lagerartikel. Bestand wird danach als abgelaufen gekennzeichnet"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Löschen wenn leer"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Diesen Lagerartikel löschen wenn der Bestand aufgebraucht ist"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Preis für eine Einheit bei Einkauf"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "In Teil umgewandelt"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "Teil ist nicht verfolgbar"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "Anzahl muss eine Ganzzahl sein"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Menge darf die verfügbare Lagermenge ({self.quantity}) nicht überschreiten"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "Anzahl stimmt nicht mit den Seriennummern überein"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "Testvorlage existiert nicht"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "Artikel wurde einem Kundenauftrag zugewiesen"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "Lagerartikel ist in anderem Element verbaut"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "Lagerartikel enthält andere Artikel"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "Artikel wurde einem Kunden zugewiesen"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "Lagerartikel wird aktuell produziert"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Nachverfolgbare Lagerartikel können nicht zusammengeführt werden"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Artikel duplizeren"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "Lagerartikel müssen auf dasselbe Teil verweisen"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "Lagerartikel müssen auf dasselbe Lieferantenteil verweisen"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "Status-Codes müssen zusammenpassen"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Lagerartikel kann nicht bewegt werden, da kein Bestand vorhanden ist"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Eintrags-Notizen"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Wert muss für diesen Test angegeben werden"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "Anhang muss für diesen Test hochgeladen werden"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2951
msgid "Test result"
msgstr "Testergebnis"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Test Ausgabe Wert"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Test Ergebnis Anhang"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Test Notizen"

#: stock/models.py:2978
msgid "Test station"
msgstr "Teststation"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "Der Bezeichner der Teststation, in der der Test durchgeführt wurde"

#: stock/models.py:2985
msgid "Started"
msgstr "Gestartet"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "Der Zeitstempel des Teststarts"

#: stock/models.py:2992
msgid "Finished"
msgstr "Fertiggestellt"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "Der Zeitstempel der Test-Beendigung"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "Testvorlage für dieses Ergebnis"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "Vorlagen-ID oder Testname muss angegeben werden"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "Die Test-Endzeit kann nicht früher als die Startzeit des Tests sein"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Elternposition"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Packungsgröße beim Hinzufügen verwenden: Die definierte Menge ist die Anzahl der Pakete"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Seriennummern für neue Teile eingeben"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr ""

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "abgelaufen"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Untergeordnete Objekte"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Einkaufspreis dieses Lagerartikels, pro Einheit oder Verpackungseinheit"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Anzahl der zu serialisierenden Lagerartikel eingeben"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Anzahl darf nicht die verfügbare Menge überschreiten ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Ziel-Bestand"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Seriennummern können diesem Teil nicht zugewiesen werden"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "Seriennummern existieren bereits"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Lagerartikel für Installation auswählen"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "Zu installierende Menge"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "Anzahl der zu verwendenden Artikel eingeben"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr " Transaktionsnotizen hinzufügen (optional)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "Die zu verwendende Menge muss mindestens 1 sein"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "Lagerartikel ist nicht verfügbar"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "Ausgewähltes Teil ist nicht in der Stückliste"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "Die zu verwendende Menge darf die verfügbare Menge nicht überschreiten"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Ziel Lagerort für unverbautes Objekt"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Wählen Sie einen Teil aus, zu dem dieser Lagerartikel geändert werden soll"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "Das ausgewählte Teil ist keine gültige Option für die Umwandlung"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Lagerartikel konnte nicht mit Zulieferteil zugewiesen werden"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "Lagerartikel Status-Code"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "Lagerartikel auswählen, um den Status zu ändern"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "Keine Lagerartikel ausgewählt"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Unter-Lagerorte"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "Übergeordneter Lagerort"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "Teil muss verkaufbar sein"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "Artikel ist einem Kundenauftrag zugeordnet"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "Artikel ist einem Fertigungsauftrag zugeordnet"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Kunde zum Zuweisen von Lagerartikel"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "Ausgewählte Firma ist kein Kunde"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Notizen zur Lagerzuordnung"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "Eine Liste der Lagerbestände muss angegeben werden"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Notizen zur Lagerartikelzusammenführung"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Unterschiedliche Lieferanten erlauben"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Zusammenführen von Lagerartikeln mit unterschiedlichen Lieferanten erlauben"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Unterschiedliche Status erlauben"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Zusammenführen von Lagerartikeln mit unterschiedlichen Status-Codes erlauben"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Mindestens zwei Lagerartikel müssen angegeben werden"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "Keine Änderung"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Primärschlüssel Lagerelement"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Bestandsbewegungsnotizen"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "erfordert Eingriff"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Beschädigt"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Zerstört"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Zurückgewiesen"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "In Quarantäne"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Alter Bestand-Verfolgungs-Eintrag"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Lagerartikel erstellt"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Lagerartikel bearbeitet"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Seriennummer hinzugefügt"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Bestand gezählt"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Bestand manuell hinzugefügt"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Bestand manuell entfernt"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Standort geändert"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Lagerbestand aktualisiert"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "In Baugruppe installiert"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Aus Baugruppe entfernt"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Komponente installiert"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Komponente entfernt"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Vom übergeordneten Element geteilt"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Unterobjekt geteilt"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Lagerartikel zusammengeführt"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "In Variante umgewandelt"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "Endprodukt erstellt"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Endprodukt fertiggestellt"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "Endprodukt abgelehnt"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Durch Bauauftrag verbraucht"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Versandt gegen Verkaufsauftrag"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Gegen Bestellung empfangen"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Zurückgeschickt gegen Rücksendeauftrag"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Zum Kunden geschickt"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Rücksendung vom Kunden"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Zugriff verweigert"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Keine Berechtigung zum Anzeigen dieser Seite."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Authentifizierungsfehler"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Sie wurden von InvenTree abgemeldet."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Seite nicht gefunden"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "Seite existiert nicht"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Interner Serverfehler"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "Der %(inventree_title)s -Server hat einen internen Fehler aufgeworfen"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Weitere Details finden Sie im Fehlerlog im Admin-Interface"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "System wird gewartet"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Die Seite ist derzeit in Wartung und sollte bald wieder verfügbar sein!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Server-Neustart erforderlich"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Eine Konfigurationsoption wurde geändert, die einen Neustart des Servers erfordert"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Bitte kontaktieren Sie Ihren Administrator für mehr Informationen"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Ausstehende Datenbankmigrationen"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Es gibt ausstehende Datenbankmigrationen, die Ihre Aufmerksamkeit erfordern"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Klicken Sie auf den folgenden Link, um diesen Auftrag anzuzeigen"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Für die folgenden Bauaufträge werden Lagerartikel benötigt"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Bauauftrag %(build)s - %(quantity)s x %(part)s bauen"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klicken Sie auf den folgenden Link, um diesen Bauauftrag anzuzeigen"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Bei den folgenden Teilen gibt es wenige Lagerartikel"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Benötigte Menge"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Sie erhalten diese E-Mail, weil Sie Benachrichtigungen für diesen Teil abonniert haben "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klicken Sie auf den folgenden Link, um diesen Teil anzuzeigen"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Mindestmenge"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr "Tage bis Ablauf"

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Benutzer"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Welche Benutzer gehören zu dieser Gruppe"

#: users/admin.py:137
msgid "Personal info"
msgstr "Persöhnliche Informationen"

#: users/admin.py:139
msgid "Permissions"
msgstr "Berechtigungen"

#: users/admin.py:142
msgid "Important dates"
msgstr "wichtige Daten"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Token wurde widerrufen"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token ist abgelaufen"

#: users/models.py:100
msgid "API Token"
msgstr "API Token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API-Tokens"

#: users/models.py:137
msgid "Token Name"
msgstr "Tokenname"

#: users/models.py:138
msgid "Custom token name"
msgstr "Benutzerdefinierter Tokenname"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Token Ablaufdatum"

#: users/models.py:152
msgid "Last Seen"
msgstr "Zuletzt gesehen"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Das letzte Mal, wo das Token verwendet wurde"

#: users/models.py:157
msgid "Revoked"
msgstr "Widerrufen"

#: users/models.py:235
msgid "Permission set"
msgstr "Berechtigung geändert"

#: users/models.py:244
msgid "Group"
msgstr "Gruppe"

#: users/models.py:248
msgid "View"
msgstr "Ansicht"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Berechtigung Einträge anzuzeigen"

#: users/models.py:252
msgid "Add"
msgstr "Hinzufügen"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Berechtigung Einträge zu erstellen"

#: users/models.py:256
msgid "Change"
msgstr "Ändern"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Berechtigungen Einträge zu ändern"

#: users/models.py:262
msgid "Delete"
msgstr "Löschen"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Berechtigung Einträge zu löschen"

#: users/models.py:501
msgid "Bot"
msgstr ""

#: users/models.py:502
msgid "Internal"
msgstr ""

#: users/models.py:504
msgid "Guest"
msgstr ""

#: users/models.py:513
msgid "Language"
msgstr ""

#: users/models.py:514
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:519
msgid "Theme"
msgstr ""

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr ""

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr ""

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:541
msgid "Position"
msgstr ""

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr ""

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr ""

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr ""

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr ""

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr "Admin"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Bestellungen"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Aufträge"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Rücksendeaufträge"

#: users/serializers.py:196
msgid "Username"
msgstr "Benutzername"

#: users/serializers.py:199
msgid "First Name"
msgstr "Vorname"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Vorname des Benutzers"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Nachname"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Nachname des Benutzers"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "E-Mailadresse des Benutzers"

#: users/serializers.py:326
msgid "Staff"
msgstr "Mitarbeiter"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Hat der Benutzer die Mitarbeiter Berechtigung"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Administrator"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "Ist dieser Benutzer ein Administrator"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Ist dieses Benutzerkonto aktiv"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Ihr Konto wurde erstellt."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Bitte benutzen Sie die Passwort-zurücksetzen-Funktion, um sich anzumelden"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Willkommen bei InvenTree"

