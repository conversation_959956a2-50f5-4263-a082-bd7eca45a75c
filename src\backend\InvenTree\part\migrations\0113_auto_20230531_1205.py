# Generated by Django 3.2.19 on 2023-05-25 16:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0112_auto_20230525_1606'),
    ]

    operations = [
        migrations.AddField(
            model_name='bomitemsubstitute',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.AddField(
            model_name='partcategoryparametertemplate',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.AddField(
            model_name='partparameter',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.Add<PERSON>ield(
            model_name='partrelated',
            name='metadata',
            field=models.J<PERSON><PERSON>ield(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.AddField(
            model_name='parttesttemplate',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
    ]
