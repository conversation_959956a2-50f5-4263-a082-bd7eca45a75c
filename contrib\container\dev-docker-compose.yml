# Docker compose recipe for InvenTree development server
# - Runs PostgreSQL as the database backend
# - Uses built-in django webserver
# - Runs the InvenTree background worker process
# - Serves media and static content directly from Django webserver

# IMPORTANT NOTE:
# The InvenTree development image does not clone source code from git.
# Instead, it runs from source code on your local machine.
# The django server will auto-detect any code changes and reload the server.

# If you have cloned the InvenTree git repo, and not made any changes to this file,
# then the default setup in this file should work straight out of the box, without modification

services:

    # Database service
    # Use PostgreSQL as the database backend
    # Note: This can be changed to a different backend if required
    inventree-dev-db:
        image: postgres:13
        expose:
            - 5432/tcp
        environment:
            - PGDATA=/var/lib/postgresql/data/pgdb
            - POSTGRES_USER=pguser
            - POSTGRES_PASSWORD=pgpassword
            - POSTGRES_DB=inventree
        volumes:
          # Map 'data' volume such that postgres database is stored externally
          - ./data:/var/lib/postgresql/data:z
        restart: unless-stopped

    # InvenTree web server service
    # Runs the django built-in webserver application
    inventree-dev-server:
        depends_on:
          - inventree-dev-db
        build: &build_config
            context: .
            dockerfile: contrib/container/Dockerfile
            target: dev
        # Cache the built image to be used by the inventree-dev-worker process
        image: inventree-dev-image
        ports:
            # Expose web server on port 8000
            - 8000:8000
        volumes:
            # Mount local source directory to /home/<USER>
            - ./:/home/<USER>
        env_file:
            - contrib/container/docker.dev.env
        restart: unless-stopped

    # Background worker process handles long-running or periodic tasks
    inventree-dev-worker:
        image: inventree-dev-image
        build: *build_config
        command: invoke worker
        depends_on:
            - inventree-dev-server
        volumes:
            # Mount local source directory to /home/<USER>
            - ./:/home/<USER>
        env_file:
            - contrib/container/docker.dev.env
        restart: unless-stopped
