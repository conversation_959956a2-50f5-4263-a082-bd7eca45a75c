# Generated by Django 2.2 on 2019-05-20 12:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('part', '0001_initial'),
        ('company', '0002_auto_20190520_2204'),
        ('stock', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='partcategory',
            name='default_location',
            field=models.ForeignKey(blank=True, help_text='Default location for parts in this category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_categories', to='stock.StockLocation'),
        ),
        migrations.AddField(
            model_name='partcategory',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='children', to='part.PartCategory'),
        ),
        migrations.AddField(
            model_name='partattachment',
            name='part',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='part.Part'),
        ),
        migrations.AddField(
            model_name='part',
            name='bom_checked_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='boms_checked', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='part',
            name='category',
            field=models.ForeignKey(blank=True, help_text='Part category', null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='parts', to='part.PartCategory'),
        ),
        migrations.AddField(
            model_name='part',
            name='default_location',
            field=models.ForeignKey(blank=True, help_text='Where is this item normally stored?', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_parts', to='stock.StockLocation'),
        ),
        migrations.AddField(
            model_name='part',
            name='default_supplier',
            field=models.ForeignKey(blank=True, help_text='Default supplier part', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_parts', to='company.SupplierPart'),
        ),
        migrations.AddField(
            model_name='bomitem',
            name='part',
            field=models.ForeignKey(help_text='Select parent part', limit_choices_to={'active': True, 'buildable': True}, on_delete=django.db.models.deletion.CASCADE, related_name='bom_items', to='part.Part'),
        ),
        migrations.AddField(
            model_name='bomitem',
            name='sub_part',
            field=models.ForeignKey(help_text='Select part to be used in BOM', limit_choices_to={'active': True, 'consumable': True}, on_delete=django.db.models.deletion.CASCADE, related_name='used_in', to='part.Part'),
        ),
        migrations.AlterUniqueTogether(
            name='partstar',
            unique_together={('part', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='part',
            unique_together={('name', 'variant')},
        ),
        migrations.AlterUniqueTogether(
            name='bomitem',
            unique_together={('part', 'sub_part')},
        ),
    ]
