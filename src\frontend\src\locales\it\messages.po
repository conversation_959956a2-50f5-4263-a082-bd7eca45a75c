msgid ""
msgstr ""
"POT-Creation-Date: 2023-06-09 22:10+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: it\n"
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-20 11:54\n"
"Last-Translator: \n"
"Language-Team: Italian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: it\n"
"X-Crowdin-File: /src/frontend/src/locales/en/messages.po\n"
"X-Crowdin-File-ID: 252\n"

#: lib/components/RowActions.tsx:36
#: src/components/items/ActionDropdown.tsx:287
#: src/pages/Index/Scan.tsx:64
msgid "Duplicate"
msgstr "Duplica"

#: lib/components/RowActions.tsx:46
#: src/components/items/ActionDropdown.tsx:243
msgid "Edit"
msgstr "Modifica"

#: lib/components/RowActions.tsx:56
#: src/components/forms/ApiForm.tsx:719
#: src/components/items/ActionDropdown.tsx:255
#: src/components/items/RoleTable.tsx:155
#: src/hooks/UseForm.tsx:160
#: src/pages/Notifications.tsx:109
#: src/tables/plugin/PluginListTable.tsx:243
msgid "Delete"
msgstr "Elimina"

#: lib/components/RowActions.tsx:66
#: src/components/details/DetailsImage.tsx:83
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:187
#: src/components/items/ActionDropdown.tsx:275
#: src/components/items/ActionDropdown.tsx:276
#: src/contexts/ThemeContext.tsx:45
#: src/hooks/UseForm.tsx:33
#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:106
#: src/tables/FilterSelectDrawer.tsx:336
#: src/tables/build/BuildOutputTable.tsx:570
msgid "Cancel"
msgstr "Annulla"

#: lib/components/RowActions.tsx:136
#: src/components/nav/NavigationDrawer.tsx:198
#: src/forms/PurchaseOrderForms.tsx:795
#: src/forms/StockForms.tsx:737
#: src/forms/StockForms.tsx:783
#: src/forms/StockForms.tsx:829
#: src/forms/StockForms.tsx:868
#: src/forms/StockForms.tsx:904
#: src/forms/StockForms.tsx:983
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:456
msgid "Actions"
msgstr "Azioni"

#: lib/components/SearchInput.tsx:34
#: src/components/forms/fields/RelatedModelField.tsx:387
#: src/components/nav/Header.tsx:168
#: src/pages/Index/Settings/UserSettings.tsx:74
#: src/pages/part/PartDetail.tsx:1161
msgid "Search"
msgstr "Ricerca"

#: lib/components/YesNoButton.tsx:20
msgid "Pass"
msgstr "Salta / Ignora"

#: lib/components/YesNoButton.tsx:21
msgid "Fail"
msgstr "Errore"

#: lib/components/YesNoButton.tsx:43
#: src/tables/Filter.tsx:35
msgid "Yes"
msgstr "Si"

#: lib/components/YesNoButton.tsx:44
#: src/tables/Filter.tsx:36
msgid "No"
msgstr "No"

#: lib/enums/ModelInformation.tsx:28
#: src/components/wizards/OrderPartsWizard.tsx:132
#: src/forms/BuildForms.tsx:310
#: src/forms/BuildForms.tsx:384
#: src/forms/BuildForms.tsx:448
#: src/forms/BuildForms.tsx:602
#: src/forms/BuildForms.tsx:757
#: src/forms/BuildForms.tsx:860
#: src/forms/PurchaseOrderForms.tsx:791
#: src/forms/ReturnOrderForms.tsx:239
#: src/forms/SalesOrderForms.tsx:267
#: src/forms/StockForms.tsx:303
#: src/forms/StockForms.tsx:732
#: src/forms/StockForms.tsx:778
#: src/forms/StockForms.tsx:824
#: src/forms/StockForms.tsx:863
#: src/forms/StockForms.tsx:899
#: src/forms/StockForms.tsx:937
#: src/forms/StockForms.tsx:979
#: src/forms/StockForms.tsx:1027
#: src/forms/StockForms.tsx:1071
#: src/pages/build/BuildDetail.tsx:183
#: src/pages/part/PartDetail.tsx:1213
#: src/tables/ColumnRenderers.tsx:82
#: src/tables/part/RelatedPartTable.tsx:53
#: src/tables/stock/StockTrackingTable.tsx:87
msgid "Part"
msgstr "Articolo"

#: lib/enums/ModelInformation.tsx:29
#: lib/enums/Roles.tsx:35
#: src/components/nav/NavigationDrawer.tsx:77
#: src/defaults/links.tsx:36
#: src/pages/Index/Settings/SystemSettings.tsx:190
#: src/pages/part/CategoryDetail.tsx:130
#: src/pages/part/CategoryDetail.tsx:273
#: src/pages/part/CategoryDetail.tsx:312
#: src/pages/part/PartDetail.tsx:951
msgid "Parts"
msgstr "Articoli"

#: lib/enums/ModelInformation.tsx:37
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:13
msgid "Part Parameter Template"
msgstr "Modello parametro articolo"

#: lib/enums/ModelInformation.tsx:38
msgid "Part Parameter Templates"
msgstr "Modelli parametro articolo"

#: lib/enums/ModelInformation.tsx:45
msgid "Part Test Template"
msgstr "Modello Test Articolo"

#: lib/enums/ModelInformation.tsx:46
msgid "Part Test Templates"
msgstr "Modelli Test Articolo"

#: lib/enums/ModelInformation.tsx:52
#: src/components/wizards/OrderPartsWizard.tsx:143
#: src/pages/company/SupplierPartDetail.tsx:409
#: src/pages/stock/StockDetail.tsx:286
#: src/tables/build/BuildAllocatedStockTable.tsx:155
#: src/tables/part/PartPurchaseOrdersTable.tsx:50
#: src/tables/purchasing/SupplierPartTable.tsx:64
#: src/tables/stock/StockItemTable.tsx:248
msgid "Supplier Part"
msgstr "Articolo Fornitore"

#: lib/enums/ModelInformation.tsx:53
#: src/pages/purchasing/PurchasingIndex.tsx:92
msgid "Supplier Parts"
msgstr "Articoli fornitore"

#: lib/enums/ModelInformation.tsx:61
#: src/tables/part/PartPurchaseOrdersTable.tsx:56
#: src/tables/stock/StockItemTable.tsx:254
msgid "Manufacturer Part"
msgstr "Articolo Produttore"

#: lib/enums/ModelInformation.tsx:62
#: src/pages/purchasing/PurchasingIndex.tsx:109
msgid "Manufacturer Parts"
msgstr "Articoli Produttore"

#: lib/enums/ModelInformation.tsx:70
#: src/pages/part/CategoryDetail.tsx:343
#: src/tables/Filter.tsx:381
msgid "Part Category"
msgstr "Categoria Articolo"

#: lib/enums/ModelInformation.tsx:71
#: lib/enums/Roles.tsx:37
#: src/pages/part/CategoryDetail.tsx:334
#: src/pages/part/PartDetail.tsx:1202
msgid "Part Categories"
msgstr "Categorie Articolo"

#: lib/enums/ModelInformation.tsx:79
#: src/forms/BuildForms.tsx:385
#: src/forms/BuildForms.tsx:449
#: src/forms/BuildForms.tsx:604
#: src/forms/BuildForms.tsx:758
#: src/forms/SalesOrderForms.tsx:269
#: src/pages/stock/StockDetail.tsx:976
#: src/tables/stock/StockTrackingTable.tsx:48
#: src/tables/stock/StockTrackingTable.tsx:55
msgid "Stock Item"
msgstr "Articolo in magazzino"

#: lib/enums/ModelInformation.tsx:80
#: lib/enums/Roles.tsx:45
#: src/pages/company/CompanyDetail.tsx:212
#: src/pages/part/CategoryDetail.tsx:287
#: src/pages/part/PartStockHistoryDetail.tsx:101
#: src/pages/stock/LocationDetail.tsx:123
#: src/pages/stock/LocationDetail.tsx:182
msgid "Stock Items"
msgstr "Articoli in magazzino"

#: lib/enums/ModelInformation.tsx:88
#: lib/enums/Roles.tsx:47
#: src/pages/stock/LocationDetail.tsx:420
msgid "Stock Location"
msgstr "Ubicazione articolo"

#: lib/enums/ModelInformation.tsx:89
#: src/pages/stock/LocationDetail.tsx:176
#: src/pages/stock/LocationDetail.tsx:412
#: src/pages/stock/StockDetail.tsx:967
msgid "Stock Locations"
msgstr "Ubicazioni articolo"

#: lib/enums/ModelInformation.tsx:97
msgid "Stock Location Type"
msgstr "Tipo ubicazione articolo"

#: lib/enums/ModelInformation.tsx:98
msgid "Stock Location Types"
msgstr "Tipi ubicazione articolo"

#: lib/enums/ModelInformation.tsx:103
#: src/pages/Index/Settings/SystemSettings.tsx:248
#: src/pages/part/PartDetail.tsx:910
msgid "Stock History"
msgstr "Cronologia Magazzino"

#: lib/enums/ModelInformation.tsx:104
msgid "Stock Histories"
msgstr "Cronologie Magazzino"

#: lib/enums/ModelInformation.tsx:109
msgid "Build"
msgstr "Produzione"

#: lib/enums/ModelInformation.tsx:110
msgid "Builds"
msgstr "Produzione"

#: lib/enums/ModelInformation.tsx:118
msgid "Build Line"
msgstr "Linea di produzione"

#: lib/enums/ModelInformation.tsx:119
msgid "Build Lines"
msgstr "Linee di produzione"

#: lib/enums/ModelInformation.tsx:126
msgid "Build Item"
msgstr "Costruisci articolo"

#: lib/enums/ModelInformation.tsx:127
msgid "Build Items"
msgstr "Costruisci articoli"

#: lib/enums/ModelInformation.tsx:132
#: src/pages/company/CompanyDetail.tsx:342
#: src/tables/company/CompanyTable.tsx:47
#: src/tables/company/ContactTable.tsx:67
msgid "Company"
msgstr "Azienda"

#: lib/enums/ModelInformation.tsx:133
msgid "Companies"
msgstr "Aziende"

#: lib/enums/ModelInformation.tsx:140
#: src/pages/build/BuildDetail.tsx:310
#: src/pages/purchasing/PurchaseOrderDetail.tsx:235
#: src/pages/sales/ReturnOrderDetail.tsx:199
#: src/pages/sales/SalesOrderDetail.tsx:211
#: src/tables/ColumnRenderers.tsx:353
#: src/tables/Filter.tsx:278
#: src/tables/TableHoverCard.tsx:101
msgid "Project Code"
msgstr "Codice del progetto"

#: lib/enums/ModelInformation.tsx:141
#: src/pages/Index/Settings/AdminCenter/Index.tsx:162
msgid "Project Codes"
msgstr "Codici del progetto"

#: lib/enums/ModelInformation.tsx:147
#: src/components/wizards/OrderPartsWizard.tsx:183
#: src/pages/build/BuildDetail.tsx:227
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:33
#: src/pages/purchasing/PurchaseOrderDetail.tsx:531
#: src/pages/stock/StockDetail.tsx:349
#: src/tables/part/PartPurchaseOrdersTable.tsx:32
#: src/tables/stock/StockItemTable.tsx:240
#: src/tables/stock/StockTrackingTable.tsx:120
msgid "Purchase Order"
msgstr "Ordine d'acquisto"

#: lib/enums/ModelInformation.tsx:148
#: lib/enums/Roles.tsx:39
#: src/pages/Index/Settings/SystemSettings.tsx:283
#: src/pages/company/CompanyDetail.tsx:205
#: src/pages/company/SupplierPartDetail.tsx:266
#: src/pages/part/PartDetail.tsx:874
#: src/pages/purchasing/PurchasingIndex.tsx:60
msgid "Purchase Orders"
msgstr "Ordini d'acquisto"

#: lib/enums/ModelInformation.tsx:156
msgid "Purchase Order Line"
msgstr "Riga ordine di acquisto"

#: lib/enums/ModelInformation.tsx:157
msgid "Purchase Order Lines"
msgstr "Righe ordine di acquisto"

#: lib/enums/ModelInformation.tsx:162
#: src/pages/build/BuildDetail.tsx:283
#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#: src/pages/sales/SalesOrderDetail.tsx:586
#: src/pages/sales/SalesOrderShipmentDetail.tsx:94
#: src/pages/sales/SalesOrderShipmentDetail.tsx:358
#: src/pages/stock/StockDetail.tsx:358
#: src/tables/part/PartSalesAllocationsTable.tsx:41
#: src/tables/sales/SalesOrderAllocationTable.tsx:107
#: src/tables/stock/StockTrackingTable.tsx:131
msgid "Sales Order"
msgstr "Ordine di Vendita"

#: lib/enums/ModelInformation.tsx:163
#: lib/enums/Roles.tsx:43
#: src/pages/Index/Settings/SystemSettings.tsx:299
#: src/pages/company/CompanyDetail.tsx:225
#: src/pages/part/PartDetail.tsx:886
#: src/pages/sales/SalesIndex.tsx:82
msgid "Sales Orders"
msgstr "Ordini di Vendita"

#: lib/enums/ModelInformation.tsx:171
#: src/pages/sales/SalesOrderShipmentDetail.tsx:357
msgid "Sales Order Shipment"
msgstr "Spedizione dell'ordine di vendita"

#: lib/enums/ModelInformation.tsx:172
msgid "Sales Order Shipments"
msgstr "Spedizioni dell'ordine di vendita"

#: lib/enums/ModelInformation.tsx:178
#: src/pages/sales/ReturnOrderDetail.tsx:516
#: src/tables/stock/StockTrackingTable.tsx:142
msgid "Return Order"
msgstr "Ordine di reso"

#: lib/enums/ModelInformation.tsx:179
#: lib/enums/Roles.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:315
#: src/pages/company/CompanyDetail.tsx:232
#: src/pages/part/PartDetail.tsx:893
#: src/pages/sales/SalesIndex.tsx:103
msgid "Return Orders"
msgstr "Ordini di reso"

#: lib/enums/ModelInformation.tsx:187
msgid "Return Order Line Item"
msgstr "Articolo Linea Ordine Reso"

#: lib/enums/ModelInformation.tsx:188
msgid "Return Order Line Items"
msgstr "Articoli Linea Ordine Reso"

#: lib/enums/ModelInformation.tsx:193
#: src/tables/company/AddressTable.tsx:52
msgid "Address"
msgstr "Indirizzo"

#: lib/enums/ModelInformation.tsx:194
#: src/pages/company/CompanyDetail.tsx:266
msgid "Addresses"
msgstr "Indirizzi"

#: lib/enums/ModelInformation.tsx:200
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:89
#: src/pages/core/UserDetail.tsx:135
#: src/pages/purchasing/PurchaseOrderDetail.tsx:211
#: src/pages/sales/ReturnOrderDetail.tsx:175
#: src/pages/sales/SalesOrderDetail.tsx:187
msgid "Contact"
msgstr "Contatto"

#: lib/enums/ModelInformation.tsx:201
#: src/pages/company/CompanyDetail.tsx:260
#: src/pages/core/CoreIndex.tsx:33
msgid "Contacts"
msgstr "Contatti"

#: lib/enums/ModelInformation.tsx:207
msgid "Owner"
msgstr "Proprietario"

#: lib/enums/ModelInformation.tsx:208
msgid "Owners"
msgstr "Proprietari"

#: lib/enums/ModelInformation.tsx:214
#: src/pages/Auth/ChangePassword.tsx:36
#: src/pages/core/UserDetail.tsx:220
#: src/tables/Filter.tsx:327
#: src/tables/settings/ApiTokenTable.tsx:105
#: src/tables/settings/ApiTokenTable.tsx:132
#: src/tables/settings/BarcodeScanHistoryTable.tsx:79
#: src/tables/settings/ExportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:77
#: src/tables/stock/StockItemTestResultTable.tsx:216
#: src/tables/stock/StockTrackingTable.tsx:190
#: src/tables/stock/StockTrackingTable.tsx:218
msgid "User"
msgstr "Utente"

#: lib/enums/ModelInformation.tsx:215
#: src/components/nav/NavigationDrawer.tsx:112
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:15
#: src/pages/core/CoreIndex.tsx:21
#: src/pages/core/UserDetail.tsx:226
msgid "Users"
msgstr "Utenti"

#: lib/enums/ModelInformation.tsx:221
#: src/pages/core/GroupDetail.tsx:78
msgid "Group"
msgstr "Gruppo"

#: lib/enums/ModelInformation.tsx:222
#: src/components/nav/NavigationDrawer.tsx:118
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:23
#: src/pages/core/CoreIndex.tsx:27
#: src/pages/core/GroupDetail.tsx:82
#: src/pages/core/UserDetail.tsx:99
#: src/tables/settings/UserTable.tsx:276
msgid "Groups"
msgstr "Gruppi"

#: lib/enums/ModelInformation.tsx:229
msgid "Import Session"
msgstr "Importa Sessione"

#: lib/enums/ModelInformation.tsx:230
msgid "Import Sessions"
msgstr "Importa Sessioni"

#: lib/enums/ModelInformation.tsx:237
msgid "Label Template"
msgstr "Modello Etichetta"

#: lib/enums/ModelInformation.tsx:238
#: src/pages/Index/Settings/AdminCenter/Index.tsx:199
msgid "Label Templates"
msgstr "Modelli Etichetta"

#: lib/enums/ModelInformation.tsx:245
msgid "Report Template"
msgstr "Modello Report"

#: lib/enums/ModelInformation.tsx:246
#: src/pages/Index/Settings/AdminCenter/Index.tsx:205
msgid "Report Templates"
msgstr "Modelli Report"

#: lib/enums/ModelInformation.tsx:253
#: src/components/plugins/PluginDrawer.tsx:145
msgid "Plugin Configuration"
msgstr "Configurazione Plugin"

#: lib/enums/ModelInformation.tsx:254
msgid "Plugin Configurations"
msgstr "Configurazioni Plugin"

#: lib/enums/ModelInformation.tsx:261
msgid "Content Type"
msgstr "Tipo Contenuto"

#: lib/enums/ModelInformation.tsx:262
msgid "Content Types"
msgstr "Tipi Contenuti"

#: lib/enums/ModelInformation.tsx:267
msgid "Selection List"
msgstr "Elenco selezione"

#: lib/enums/ModelInformation.tsx:268
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:21
msgid "Selection Lists"
msgstr "Elenchi di selezione"

#: lib/enums/ModelInformation.tsx:274
#: src/components/barcodes/BarcodeInput.tsx:114
#: src/components/dashboard/DashboardLayout.tsx:224
#: src/components/editors/NotesEditor.tsx:74
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:157
#: src/components/forms/fields/ApiFormField.tsx:263
#: src/components/forms/fields/TableField.tsx:45
#: src/components/importer/ImportDataSelector.tsx:192
#: src/components/importer/ImporterColumnSelector.tsx:217
#: src/components/importer/ImporterDrawer.tsx:88
#: src/components/modals/LicenseModal.tsx:85
#: src/components/nav/NavigationTree.tsx:210
#: src/components/nav/NotificationDrawer.tsx:235
#: src/components/nav/SearchDrawer.tsx:572
#: src/components/settings/SettingList.tsx:145
#: src/forms/BomForms.tsx:69
#: src/functions/auth.tsx:612
#: src/pages/ErrorPage.tsx:11
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:124
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:643
#: src/pages/part/PartPricingPanel.tsx:71
#: src/states/IconState.tsx:46
#: src/states/IconState.tsx:76
#: src/tables/InvenTreeTableHeader.tsx:125
#: src/tables/bom/BomTable.tsx:527
#: src/tables/stock/StockItemTestResultTable.tsx:335
msgid "Error"
msgstr "Errore"

#: lib/enums/ModelInformation.tsx:275
#: src/tables/machine/MachineListTable.tsx:354
#: src/tables/machine/MachineTypeTable.tsx:281
msgid "Errors"
msgstr "Errori"

#: lib/enums/Roles.tsx:31
msgid "Admin"
msgstr "Admin"

#: lib/enums/Roles.tsx:33
#: src/pages/Index/Settings/SystemSettings.tsx:264
#: src/pages/build/BuildIndex.tsx:75
#: src/pages/part/PartDetail.tsx:903
#: src/pages/sales/SalesOrderDetail.tsx:394
msgid "Build Orders"
msgstr "Ordini di Produzione"

#: lib/enums/Roles.tsx:50
#: src/pages/Index/Settings/AdminCenter/Index.tsx:202
#~ msgid "Stocktake"
#~ msgstr "Stocktake"

#: src/components/Boundary.tsx:12
msgid "Error rendering component"
msgstr "Errore nel renderizzare il componente"

#: src/components/Boundary.tsx:14
msgid "An error occurred while rendering this component. Refer to the console for more information."
msgstr "Si è verificato un errore durante il rendering di questo componente. Fare riferimento alla console per maggiori informazioni."

#: src/components/DashboardItemProxy.tsx:34
#~ msgid "Title"
#~ msgstr "Title"

#: src/components/barcodes/BarcodeCameraInput.tsx:103
msgid "Error while scanning"
msgstr "Errore durante la scansione"

#: src/components/barcodes/BarcodeCameraInput.tsx:117
msgid "Error while stopping"
msgstr "Errore durante l'arresto"

#: src/components/barcodes/BarcodeCameraInput.tsx:159
msgid "Start scanning by selecting a camera and pressing the play button."
msgstr "Inizia la scansione selezionando una fotocamera e premendo il pulsante play."

#: src/components/barcodes/BarcodeCameraInput.tsx:180
msgid "Stop scanning"
msgstr "Interrompi la scansione"

#: src/components/barcodes/BarcodeCameraInput.tsx:190
msgid "Start scanning"
msgstr "Avvia scansione"

#: src/components/barcodes/BarcodeInput.tsx:34
#: src/tables/general/BarcodeScanTable.tsx:55
#: src/tables/settings/BarcodeScanHistoryTable.tsx:64
msgid "Barcode"
msgstr "Codice a barre"

#: src/components/barcodes/BarcodeInput.tsx:35
#: src/components/barcodes/BarcodeKeyboardInput.tsx:18
#: src/defaults/actions.tsx:72
msgid "Scan"
msgstr "Scansione"

#: src/components/barcodes/BarcodeInput.tsx:53
msgid "Camera Input"
msgstr "Ingresso Fotocamera"

#: src/components/barcodes/BarcodeInput.tsx:63
msgid "Scanner Input"
msgstr "Ingresso Scanner"

#: src/components/barcodes/BarcodeInput.tsx:105
msgid "Barcode Data"
msgstr "Dati codice a barre"

#: src/components/barcodes/BarcodeInput.tsx:109
msgid "No barcode data"
msgstr "Nessun dato del codice a barre"

#: src/components/barcodes/BarcodeInput.tsx:110
msgid "Scan or enter barcode data"
msgstr "Scansiona o inserisci i dati del codice a barre"

#: src/components/barcodes/BarcodeKeyboardInput.tsx:64
msgid "Enter barcode data"
msgstr "Inserire il codice a barre"

#: src/components/barcodes/BarcodeScanDialog.tsx:49
#: src/components/buttons/ScanButton.tsx:15
#: src/components/nav/NavigationDrawer.tsx:129
#: src/forms/PurchaseOrderForms.tsx:454
#: src/forms/PurchaseOrderForms.tsx:560
msgid "Scan Barcode"
msgstr "Scansiona codice a barre"

#: src/components/barcodes/BarcodeScanDialog.tsx:105
msgid "No matching item found"
msgstr "Nessun elemento corrispondente trovato"

#: src/components/barcodes/BarcodeScanDialog.tsx:134
msgid "Barcode does not match the expected model type"
msgstr "Il codice a barre non corrisponde al tipo di modello previsto"

#: src/components/barcodes/BarcodeScanDialog.tsx:145
#: src/components/editors/NotesEditor.tsx:84
#: src/components/editors/NotesEditor.tsx:118
#: src/components/forms/ApiForm.tsx:451
#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:45
#: src/tables/bom/BomTable.tsx:518
#: src/tables/settings/PendingTasksTable.tsx:68
msgid "Success"
msgstr "Operazione completata"

#: src/components/barcodes/BarcodeScanDialog.tsx:151
msgid "Failed to handle barcode"
msgstr "Gestione del codice a barre non riuscita"

#: src/components/barcodes/BarcodeScanDialog.tsx:167
#: src/pages/Index/Scan.tsx:129
msgid "Failed to scan barcode"
msgstr "Scansione del codice a barre non riuscita"

#: src/components/barcodes/QRCode.tsx:94
msgid "Low (7%)"
msgstr "Basso (7%)"

#: src/components/barcodes/QRCode.tsx:95
msgid "Medium (15%)"
msgstr "Medio (15%)"

#: src/components/barcodes/QRCode.tsx:96
msgid "Quartile (25%)"
msgstr "Quartile (25%)"

#: src/components/barcodes/QRCode.tsx:97
msgid "High (30%)"
msgstr "Alto (30%)"

#: src/components/barcodes/QRCode.tsx:107
msgid "Custom barcode"
msgstr "Codice a barre personalizzato"

#: src/components/barcodes/QRCode.tsx:108
msgid "A custom barcode is registered for this item. The shown code is not that custom barcode."
msgstr "Per questo articolo è registrato un codice a barre personalizzato. Il codice visualizzato non è quello personalizzato."

#: src/components/barcodes/QRCode.tsx:127
msgid "Barcode Data:"
msgstr "Dati codice a barre:"

#: src/components/barcodes/QRCode.tsx:138
msgid "Select Error Correction Level"
msgstr "Seleziona Livello Correzione Errori"

#: src/components/barcodes/QRCode.tsx:170
msgid "Failed to link barcode"
msgstr "Collegamento al codice a barre non riuscito"

#: src/components/barcodes/QRCode.tsx:179
#: src/pages/part/PartDetail.tsx:522
#: src/pages/purchasing/PurchaseOrderDetail.tsx:204
#: src/pages/sales/ReturnOrderDetail.tsx:168
#: src/pages/sales/SalesOrderDetail.tsx:180
#: src/pages/sales/SalesOrderShipmentDetail.tsx:168
msgid "Link"
msgstr "Collegamento"

#: src/components/barcodes/QRCode.tsx:200
msgid "This will remove the link to the associated barcode"
msgstr "Questo rimuoverà il collegamento al codice a barre associato"

#: src/components/barcodes/QRCode.tsx:205
#: src/components/items/ActionDropdown.tsx:190
#: src/forms/PurchaseOrderForms.tsx:551
msgid "Unlink Barcode"
msgstr "Scollega Codice a Barre"

#: src/components/buttons/AdminButton.tsx:86
msgid "Open in admin interface"
msgstr "Apri nell'interfaccia di amministrazione"

#: src/components/buttons/CopyButton.tsx:18
#~ msgid "Copy to clipboard"
#~ msgstr "Copy to clipboard"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copied"
msgstr "Copiato"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copy"
msgstr "Copia"

#: src/components/buttons/PrintingActions.tsx:51
msgid "Printing Labels"
msgstr "Stampa Etichette"

#: src/components/buttons/PrintingActions.tsx:56
msgid "Printing Reports"
msgstr "Stampa report"

#: src/components/buttons/PrintingActions.tsx:77
#~ msgid "Printing"
#~ msgstr "Printing"

#: src/components/buttons/PrintingActions.tsx:78
#~ msgid "Printing completed successfully"
#~ msgstr "Printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:114
#~ msgid "Label printing completed successfully"
#~ msgstr "Label printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:116
msgid "Print Label"
msgstr "Stampa Etichetta"

#: src/components/buttons/PrintingActions.tsx:121
#~ msgid "The label could not be generated"
#~ msgstr "The label could not be generated"

#: src/components/buttons/PrintingActions.tsx:122
#: src/components/buttons/PrintingActions.tsx:148
msgid "Print"
msgstr "Stampa"

#: src/components/buttons/PrintingActions.tsx:131
msgid "Print Report"
msgstr "Stampa report"

#: src/components/buttons/PrintingActions.tsx:152
#~ msgid "Generate"
#~ msgstr "Generate"

#: src/components/buttons/PrintingActions.tsx:153
#~ msgid "Report printing completed successfully"
#~ msgstr "Report printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:159
#~ msgid "The report could not be generated"
#~ msgstr "The report could not be generated"

#: src/components/buttons/PrintingActions.tsx:169
msgid "Printing Actions"
msgstr "Azioni di stampa"

#: src/components/buttons/PrintingActions.tsx:174
msgid "Print Labels"
msgstr "Stampa Etichette"

#: src/components/buttons/PrintingActions.tsx:180
msgid "Print Reports"
msgstr "Stampa report"

#: src/components/buttons/RemoveRowButton.tsx:8
msgid "Remove this row"
msgstr "Rimuovi questa riga"

#: src/components/buttons/SSOButton.tsx:40
msgid "You will be redirected to the provider for further actions."
msgstr "Verrai reindirizzato al provider per ulteriori azioni."

#: src/components/buttons/SSOButton.tsx:44
#~ msgid "This provider is not full set up."
#~ msgstr "This provider is not full set up."

#: src/components/buttons/SSOButton.tsx:54
#~ msgid "Sign in redirect failed."
#~ msgstr "Sign in redirect failed."

#: src/components/buttons/ScanButton.tsx:15
#~ msgid "Scan QR code"
#~ msgstr "Scan QR code"

#: src/components/buttons/ScanButton.tsx:19
msgid "Open Barcode Scanner"
msgstr "Apri scanner di codici a barre"

#: src/components/buttons/ScanButton.tsx:20
#~ msgid "Open QR code scanner"
#~ msgstr "Open QR code scanner"

#: src/components/buttons/SpotlightButton.tsx:12
msgid "Open spotlight"
msgstr "Accendi torcia"

#: src/components/buttons/StarredToggleButton.tsx:36
msgid "Subscription Updated"
msgstr "Abbonamento aggiornato"

#: src/components/buttons/StarredToggleButton.tsx:57
#~ msgid "Unsubscribe from part"
#~ msgstr "Unsubscribe from part"

#: src/components/buttons/StarredToggleButton.tsx:66
msgid "Unsubscribe from notifications"
msgstr "Annulla l'iscrizione alle notifiche"

#: src/components/buttons/StarredToggleButton.tsx:67
msgid "Subscribe to notifications"
msgstr "Iscriviti alle notifiche"

#: src/components/calendar/Calendar.tsx:99
#: src/components/calendar/Calendar.tsx:162
msgid "Calendar Filters"
msgstr "Filtri Del Calendario"

#: src/components/calendar/Calendar.tsx:114
msgid "Previous month"
msgstr "Mese precedente"

#: src/components/calendar/Calendar.tsx:123
msgid "Select month"
msgstr "Seleziona mese"

#: src/components/calendar/Calendar.tsx:144
msgid "Next month"
msgstr "Mese successivo"

#: src/components/calendar/Calendar.tsx:175
#: src/tables/InvenTreeTableHeader.tsx:291
msgid "Download data"
msgstr "Scarica dati"

#: src/components/calendar/OrderCalendar.tsx:132
msgid "Order Updated"
msgstr "Ordine Aggiornato"

#: src/components/calendar/OrderCalendar.tsx:142
msgid "Error updating order"
msgstr "Errore nell'aggiornare l'ordine"

#: src/components/calendar/OrderCalendar.tsx:178
#: src/tables/Filter.tsx:144
msgid "Overdue"
msgstr "In ritardo"

#: src/components/dashboard/DashboardLayout.tsx:225
msgid "Failed to load dashboard widgets."
msgstr "Impossibile caricare i widget della dashboard."

#: src/components/dashboard/DashboardLayout.tsx:235
msgid "No Widgets Selected"
msgstr "Nessun Widget Selezionato"

#: src/components/dashboard/DashboardLayout.tsx:238
msgid "Use the menu to add widgets to the dashboard"
msgstr "Usa il menu per aggiungere widget alla dashboard"

#: src/components/dashboard/DashboardMenu.tsx:62
#: src/components/dashboard/DashboardMenu.tsx:138
msgid "Accept Layout"
msgstr "Accetta Disposizione"

#: src/components/dashboard/DashboardMenu.tsx:94
#: src/components/nav/NavigationDrawer.tsx:71
#: src/defaults/actions.tsx:28
#: src/defaults/links.tsx:31
#: src/pages/Index/Home.tsx:8
msgid "Dashboard"
msgstr "Bacheca"

#: src/components/dashboard/DashboardMenu.tsx:102
msgid "Edit Layout"
msgstr "Modificare la Disposizione"

#: src/components/dashboard/DashboardMenu.tsx:111
msgid "Add Widget"
msgstr "Aggiungi Widget"

#: src/components/dashboard/DashboardMenu.tsx:120
msgid "Remove Widgets"
msgstr "Rimuovi Widget"

#: src/components/dashboard/DashboardMenu.tsx:129
msgid "Clear Widgets"
msgstr "Cancella Widget"

#: src/components/dashboard/DashboardWidget.tsx:81
msgid "Remove this widget from the dashboard"
msgstr "Rimuovi questo widget dalla dashboard"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:77
msgid "Filter dashboard widgets"
msgstr "Filtra i widget della dashboard"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:98
msgid "Add this widget to the dashboard"
msgstr "Aggiungi questo widget alla dashboard"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:123
msgid "No Widgets Available"
msgstr "Nessun Widget Disponibile"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:124
msgid "There are no more widgets available for the dashboard"
msgstr "Non ci sono più widget disponibili per la dashboard"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:24
msgid "Subscribed Parts"
msgstr "Articoli Sottoscritti"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:25
msgid "Show the number of parts which you have subscribed to"
msgstr "Mostra il numero di articoli a cui sei sottoscritto"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:31
msgid "Subscribed Categories"
msgstr "Categoria sottoscritta"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:32
msgid "Show the number of part categories which you have subscribed to"
msgstr "Mostra il numero di categorie di articoli a cui sei sottoscritto"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:38
msgid "Invalid BOMs"
msgstr "Distinta base non valida"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:39
msgid "Assemblies requiring bill of materials validation"
msgstr "Assemblaggi che richiedono la convalida di una distinta base"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:50
#: src/tables/part/PartTable.tsx:250
msgid "Low Stock"
msgstr "Disponibilità scarsa"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:52
msgid "Show the number of parts which are low on stock"
msgstr "Mostra il numero di articoli che sono scarsi in stock"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:57
msgid "Required for Build Orders"
msgstr "Richiesto per gli ordini di produzione"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:59
msgid "Show parts which are required for active build orders"
msgstr "Mostra gli articolo che sono necessari per gli ordini di produzione attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:64
msgid "Expired Stock Items"
msgstr "Elementi in Giacenza Scaduti"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:66
msgid "Show the number of stock items which have expired"
msgstr "Mostra il numero di elementi in giacenza scaduti"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:72
msgid "Stale Stock Items"
msgstr "Scorte obsolete"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:74
msgid "Show the number of stock items which are stale"
msgstr "Mostra il numero di elementi in giacenza obsoleti"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:80
msgid "Active Build Orders"
msgstr "Ordini di Produzione Attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:82
msgid "Show the number of build orders which are currently active"
msgstr "Mostra il numero di ordini di produzione attualmente attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:87
msgid "Overdue Build Orders"
msgstr "Ordini di Produzione in Ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:89
msgid "Show the number of build orders which are overdue"
msgstr "Mostra il numero di ordini di produzione in ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:94
msgid "Assigned Build Orders"
msgstr "Ordini di Produzione Assegnati"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:96
msgid "Show the number of build orders which are assigned to you"
msgstr "Mostra il numero di ordini di produzione assegnati a te"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:101
msgid "Active Sales Orders"
msgstr "Ordini di Vendita Attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:103
msgid "Show the number of sales orders which are currently active"
msgstr "Mostra il numero di ordini di vendita attualmente attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:108
msgid "Overdue Sales Orders"
msgstr "Ordini Di Vendita in Ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:110
msgid "Show the number of sales orders which are overdue"
msgstr "Mostra il numero di ordini di vendita in ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:115
msgid "Assigned Sales Orders"
msgstr "Ordini di Vendita Assegnati"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:117
msgid "Show the number of sales orders which are assigned to you"
msgstr "Mostra il numero di ordini di vendita assegnati a te"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:122
msgid "Active Purchase Orders"
msgstr "Ordini Di Acquisto Attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:124
msgid "Show the number of purchase orders which are currently active"
msgstr "Mostra il numero di ordini di acquisto attualmente attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:129
msgid "Overdue Purchase Orders"
msgstr "Ordini Di Acquisto In Ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:131
msgid "Show the number of purchase orders which are overdue"
msgstr "Mostra il numero di ordini di acquisto in ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:136
msgid "Assigned Purchase Orders"
msgstr "Ordini Di Acquisto Assegnati"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:138
msgid "Show the number of purchase orders which are assigned to you"
msgstr "Mostra il numero di ordini di acquisto assegnati a te"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:143
msgid "Active Return Orders"
msgstr "Ordini di Reso Attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:145
msgid "Show the number of return orders which are currently active"
msgstr "Mostra il numero di ordini di reso attualmente attivi"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:150
msgid "Overdue Return Orders"
msgstr "Ordini di Reso in Ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:152
msgid "Show the number of return orders which are overdue"
msgstr "Mostra il numero di ordini di reso in ritardo"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:157
msgid "Assigned Return Orders"
msgstr "Ordini di Reso Assegnati"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:159
msgid "Show the number of return orders which are assigned to you"
msgstr "Mostra il numero di ordini di reso assegnati a te"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:179
#: src/components/dashboard/widgets/GetStartedWidget.tsx:15
#: src/defaults/links.tsx:86
msgid "Getting Started"
msgstr "Per Iniziare"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:180
#: src/defaults/links.tsx:89
msgid "Getting started with InvenTree"
msgstr "Per iniziare con InvenTree"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:187
#: src/components/dashboard/widgets/NewsWidget.tsx:123
msgid "News Updates"
msgstr "Aggiornamenti Notizie"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:188
msgid "The latest news from InvenTree"
msgstr "Le ultime notizie da InvenTree"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:18
#: src/components/nav/MainMenu.tsx:87
msgid "Change Color Mode"
msgstr "Cambia Modalità Colore"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:23
msgid "Change the color mode of the user interface"
msgstr "Cambia la modalità colore dell'interfaccia utente"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:18
msgid "Change Language"
msgstr "Cambia Lingua"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:23
msgid "Change the language of the user interface"
msgstr "Cambia la lingua dell'interfaccia utente"

#: src/components/dashboard/widgets/NewsWidget.tsx:60
#: src/components/nav/NotificationDrawer.tsx:94
#: src/pages/Notifications.tsx:53
msgid "Mark as read"
msgstr "Segna come letto"

#: src/components/dashboard/widgets/NewsWidget.tsx:115
msgid "Requires Superuser"
msgstr "Richiede Superuser"

#: src/components/dashboard/widgets/NewsWidget.tsx:116
msgid "This widget requires superuser permissions"
msgstr "Questo widget richiede i permessi di superuser"

#: src/components/dashboard/widgets/NewsWidget.tsx:133
msgid "No News"
msgstr "Nessuna notizia"

#: src/components/dashboard/widgets/NewsWidget.tsx:134
msgid "There are no unread news items"
msgstr "Non ci sono notizie non lette"

#: src/components/details/Details.tsx:117
#~ msgid "Email:"
#~ msgstr "Email:"

#: src/components/details/Details.tsx:123
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:76
#: src/pages/core/UserDetail.tsx:93
#: src/pages/core/UserDetail.tsx:203
#: src/tables/settings/UserTable.tsx:420
msgid "Superuser"
msgstr "Superuser"

#: src/components/details/Details.tsx:124
#: src/pages/core/UserDetail.tsx:87
#: src/pages/core/UserDetail.tsx:200
#: src/tables/settings/UserTable.tsx:415
msgid "Staff"
msgstr "Staff"

#: src/components/details/Details.tsx:125
msgid "Email: "
msgstr "Email: "

#: src/components/details/Details.tsx:407
msgid "No name defined"
msgstr "Nessun nome definito"

#: src/components/details/DetailsImage.tsx:77
msgid "Remove Image"
msgstr "Rimuovi l'immagine"

#: src/components/details/DetailsImage.tsx:80
msgid "Remove the associated image from this item?"
msgstr "Rimuovi l'immagine associata all'articolo?"

#: src/components/details/DetailsImage.tsx:83
#: src/forms/StockForms.tsx:828
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:203
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:408
msgid "Remove"
msgstr "Rimuovi"

#: src/components/details/DetailsImage.tsx:109
msgid "Drag and drop to upload"
msgstr "Trascina e rilascia per caricare"

#: src/components/details/DetailsImage.tsx:112
msgid "Click to select file(s)"
msgstr "Fare clic per selezionare i file(s)"

#: src/components/details/DetailsImage.tsx:172
msgid "Image uploaded"
msgstr "Immagine caricata"

#: src/components/details/DetailsImage.tsx:173
msgid "Image has been uploaded successfully"
msgstr "Immagine caricata con successo"

#: src/components/details/DetailsImage.tsx:180
#: src/tables/general/AttachmentTable.tsx:201
msgid "Upload Error"
msgstr "Errore Di Caricamento"

#: src/components/details/DetailsImage.tsx:250
msgid "Clear"
msgstr "Elimina"

#: src/components/details/DetailsImage.tsx:256
#: src/components/forms/ApiForm.tsx:661
#: src/contexts/ThemeContext.tsx:44
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:654
msgid "Submit"
msgstr "Invia"

#: src/components/details/DetailsImage.tsx:300
msgid "Select from existing images"
msgstr "Seleziona da immagini esistenti"

#: src/components/details/DetailsImage.tsx:308
msgid "Select Image"
msgstr "Seleziona un'immagine"

#: src/components/details/DetailsImage.tsx:324
msgid "Download remote image"
msgstr "Scarica immagine remota"

#: src/components/details/DetailsImage.tsx:339
msgid "Upload new image"
msgstr "Carica nuova immagine"

#: src/components/details/DetailsImage.tsx:346
msgid "Upload Image"
msgstr "Carica immagine"

#: src/components/details/DetailsImage.tsx:359
msgid "Delete image"
msgstr "Elimina immagine"

#: src/components/details/DetailsImage.tsx:393
msgid "Download Image"
msgstr "Scarica immagine"

#: src/components/details/DetailsImage.tsx:398
msgid "Image downloaded successfully"
msgstr "Immagine scaricata con successo"

#: src/components/details/PartIcons.tsx:43
#~ msgid "Part is a template part (variants can be made from this part)"
#~ msgstr "Part is a template part (variants can be made from this part)"

#: src/components/details/PartIcons.tsx:49
#~ msgid "Part can be assembled from other parts"
#~ msgstr "Part can be assembled from other parts"

#: src/components/details/PartIcons.tsx:55
#~ msgid "Part can be used in assemblies"
#~ msgstr "Part can be used in assemblies"

#: src/components/details/PartIcons.tsx:61
#~ msgid "Part stock is tracked by serial number"
#~ msgstr "Part stock is tracked by serial number"

#: src/components/details/PartIcons.tsx:67
#~ msgid "Part can be purchased from external suppliers"
#~ msgstr "Part can be purchased from external suppliers"

#: src/components/details/PartIcons.tsx:73
#~ msgid "Part can be sold to customers"
#~ msgstr "Part can be sold to customers"

#: src/components/details/PartIcons.tsx:78
#~ msgid "Part is virtual (not a physical part)"
#~ msgstr "Part is virtual (not a physical part)"

#: src/components/editors/NotesEditor.tsx:75
msgid "Image upload failed"
msgstr "Il caricamento della foto è fallito"

#: src/components/editors/NotesEditor.tsx:85
msgid "Image uploaded successfully"
msgstr "Immagine caricata con successo"

#: src/components/editors/NotesEditor.tsx:119
msgid "Notes saved successfully"
msgstr "Note salvate con successo"

#: src/components/editors/NotesEditor.tsx:130
msgid "Failed to save notes"
msgstr "Salvataggio delle note non riuscito"

#: src/components/editors/NotesEditor.tsx:133
msgid "Error Saving Notes"
msgstr "Errore Nel Salvare Le Note"

#: src/components/editors/NotesEditor.tsx:151
#~ msgid "Disable Editing"
#~ msgstr "Disable Editing"

#: src/components/editors/NotesEditor.tsx:153
msgid "Save Notes"
msgstr "Salva note"

#: src/components/editors/NotesEditor.tsx:172
msgid "Close Editor"
msgstr "Chiudere l'editor"

#: src/components/editors/NotesEditor.tsx:179
msgid "Enable Editing"
msgstr "Abilita Modifica"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Preview Notes"
#~ msgstr "Preview Notes"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Edit Notes"
#~ msgstr "Edit Notes"

#: src/components/editors/TemplateEditor/CodeEditor/index.tsx:9
msgid "Code"
msgstr "Codice"

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:44
#~ msgid "Failed to parse error response from server."
#~ msgstr "Failed to parse error response from server."

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:50
msgid "Error rendering preview"
msgstr "Errore nel renderizzare l'anteprima"

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:120
msgid "Preview not available, click \"Reload Preview\"."
msgstr "Anteprima non disponibile, clicca su \"Ricarica anteprima\"."

#: src/components/editors/TemplateEditor/PdfPreview/index.tsx:9
msgid "PDF Preview"
msgstr "Anteprima PDF"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:109
msgid "Error loading template"
msgstr "Errore durante il caricamento del modello"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:121
msgid "Error saving template"
msgstr "Errore durante il salvataggio del modello"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:151
#~ msgid "Save & Reload preview?"
#~ msgstr "Save & Reload preview?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:158
msgid "Could not load the template from the server."
msgstr "Impossibile caricare il modello dal server."

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:175
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:306
msgid "Save & Reload Preview"
msgstr "Salva & ricarica l'anteprima"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:180
msgid "Are you sure you want to Save & Reload the preview?"
msgstr "Sei sicuro di voler salvare e ricaricare l'anteprima?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:182
msgid "To render the preview the current template needs to be replaced on the server with your modifications which may break the label if it is under active use. Do you want to proceed?"
msgstr "Per visualizzare l'anteprima, il modello attuale deve essere sostituito sul server con le modifiche apportate, il che potrebbe interrompere l'etichetta se è in uso attivo. Volete procedere?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:186
msgid "Save & Reload"
msgstr "Salva & ricarica"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:218
msgid "Preview updated"
msgstr "Anteprima aggiornata"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:219
msgid "The preview has been updated successfully."
msgstr "L' anteprima è stata aggiornata con successo."

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:263
#~ msgid "Save & Reload preview"
#~ msgstr "Save & Reload preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:298
msgid "Reload preview"
msgstr "Ricarica anteprima"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:299
msgid "Use the currently stored template from the server"
msgstr "Utilizzare il modello attualmente memorizzato dal server"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:307
msgid "Save the current template and reload the preview"
msgstr "Salva il modello corrente e ricarica l'anteprima"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:322
#~ msgid "to preview"
#~ msgstr "to preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:366
msgid "Select instance to preview"
msgstr "Selezionare l'istanza da visualizzare in anteprima"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:410
msgid "Error rendering template"
msgstr "Errore nel visualizzare il modello"

#: src/components/errors/ClientError.tsx:23
msgid "Client Error"
msgstr "Errore Client"

#: src/components/errors/ClientError.tsx:24
msgid "Client error occurred"
msgstr "Si è verificato un errore del client"

#: src/components/errors/GenericErrorPage.tsx:50
msgid "Status Code"
msgstr "Codici di stato"

#: src/components/errors/GenericErrorPage.tsx:63
msgid "Return to the index page"
msgstr "Ritorno alla pagina dell'indice"

#: src/components/errors/NotAuthenticated.tsx:8
msgid "Not Authenticated"
msgstr "Non autenticato"

#: src/components/errors/NotAuthenticated.tsx:9
msgid "You are not logged in."
msgstr "Non hai effettuato l'accesso."

#: src/components/errors/NotFound.tsx:8
msgid "Page Not Found"
msgstr "Pagina Non Trovata"

#: src/components/errors/NotFound.tsx:9
msgid "This page does not exist"
msgstr "Questa pagina non esiste"

#: src/components/errors/PermissionDenied.tsx:8
#: src/functions/notifications.tsx:25
msgid "Permission Denied"
msgstr "Permesso negato"

#: src/components/errors/PermissionDenied.tsx:9
msgid "You do not have permission to view this page."
msgstr "Non ha i permessi per visualizzare questa pagina."

#: src/components/errors/ServerError.tsx:8
msgid "Server Error"
msgstr "Errore del server"

#: src/components/errors/ServerError.tsx:9
msgid "A server error occurred"
msgstr "Si è verificato un errore del server"

#: src/components/forms/ApiForm.tsx:103
#: src/components/forms/ApiForm.tsx:580
msgid "Form Error"
msgstr "Errore Modulo"

#: src/components/forms/ApiForm.tsx:487
#~ msgid "Form Errors Exist"
#~ msgstr "Form Errors Exist"

#: src/components/forms/ApiForm.tsx:588
msgid "Errors exist for one or more form fields"
msgstr "Esistono errori per uno o più campi del modulo"

#: src/components/forms/ApiForm.tsx:699
#: src/hooks/UseForm.tsx:129
#: src/tables/plugin/PluginListTable.tsx:204
msgid "Update"
msgstr "Aggiorna"

#: src/components/forms/AuthenticationForm.tsx:48
#: src/components/forms/AuthenticationForm.tsx:74
#: src/functions/auth.tsx:83
#~ msgid "Check your your input and try again."
#~ msgstr "Check your your input and try again."

#: src/components/forms/AuthenticationForm.tsx:52
#~ msgid "Welcome back!"
#~ msgstr "Welcome back!"

#: src/components/forms/AuthenticationForm.tsx:53
#~ msgid "Login successfull"
#~ msgstr "Login successfull"

#: src/components/forms/AuthenticationForm.tsx:65
#: src/functions/auth.tsx:74
#~ msgid "Mail delivery successfull"
#~ msgstr "Mail delivery successfull"

#: src/components/forms/AuthenticationForm.tsx:73
msgid "Login successful"
msgstr "Accesso riuscito"

#: src/components/forms/AuthenticationForm.tsx:74
msgid "Logged in successfully"
msgstr "Accesso effettuato con successo"

#: src/components/forms/AuthenticationForm.tsx:81
#: src/components/forms/AuthenticationForm.tsx:89
msgid "Login failed"
msgstr "Accesso non riuscito"

#: src/components/forms/AuthenticationForm.tsx:82
#: src/components/forms/AuthenticationForm.tsx:90
#: src/components/forms/AuthenticationForm.tsx:106
#: src/functions/auth.tsx:282
msgid "Check your input and try again."
msgstr "Controllare i dati inseriti e riprovare."

#: src/components/forms/AuthenticationForm.tsx:100
#: src/functions/auth.tsx:273
msgid "Mail delivery successful"
msgstr "Spedizione email riuscita"

#: src/components/forms/AuthenticationForm.tsx:101
msgid "Check your inbox for the login link. If you have an account, you will receive a login link. Check in spam too."
msgstr "Controlla la tua casella di posta per il link di accesso. Se hai un account, riceverai un link di accesso. Controlla anche lo spam."

#: src/components/forms/AuthenticationForm.tsx:105
msgid "Mail delivery failed"
msgstr "Invio della posta non riuscito"

#: src/components/forms/AuthenticationForm.tsx:125
msgid "Or continue with other methods"
msgstr "Oppure proseguire con altri metodi"

#: src/components/forms/AuthenticationForm.tsx:136
#: src/components/forms/AuthenticationForm.tsx:296
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:64
#: src/pages/core/UserDetail.tsx:48
msgid "Username"
msgstr "Nome utente"

#: src/components/forms/AuthenticationForm.tsx:136
#~ msgid "I will use username and password"
#~ msgstr "I will use username and password"

#: src/components/forms/AuthenticationForm.tsx:138
#: src/components/forms/AuthenticationForm.tsx:298
msgid "Your username"
msgstr "Il tuo nome utente"

#: src/components/forms/AuthenticationForm.tsx:143
#: src/components/forms/AuthenticationForm.tsx:311
#: src/pages/Auth/ResetPassword.tsx:34
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:708
msgid "Password"
msgstr "Password"

#: src/components/forms/AuthenticationForm.tsx:145
#: src/components/forms/AuthenticationForm.tsx:313
msgid "Your password"
msgstr "La tua password"

#: src/components/forms/AuthenticationForm.tsx:164
msgid "Reset password"
msgstr "Reimposta password"

#: src/components/forms/AuthenticationForm.tsx:173
#: src/components/forms/AuthenticationForm.tsx:303
#: src/pages/Auth/Reset.tsx:17
#: src/pages/core/UserDetail.tsx:71
msgid "Email"
msgstr "Email"

#: src/components/forms/AuthenticationForm.tsx:174
#: src/pages/Auth/Reset.tsx:18
msgid "We will send you a link to login - if you are registered"
msgstr "Ti invieremo un link per accedere - se sei registrato"

#: src/components/forms/AuthenticationForm.tsx:190
msgid "Send me an email"
msgstr "Inviami una email"

#: src/components/forms/AuthenticationForm.tsx:192
msgid "Use username and password"
msgstr "Usa nome utente e password"

#: src/components/forms/AuthenticationForm.tsx:201
msgid "Log In"
msgstr "Accedi"

#: src/components/forms/AuthenticationForm.tsx:203
#: src/pages/Auth/Reset.tsx:26
msgid "Send Email"
msgstr "Invia email"

#: src/components/forms/AuthenticationForm.tsx:239
msgid "Passwords do not match"
msgstr "Le password non corrispondono"

#: src/components/forms/AuthenticationForm.tsx:256
msgid "Registration successful"
msgstr "Registrazione completata con successo"

#: src/components/forms/AuthenticationForm.tsx:257
msgid "Please confirm your email address to complete the registration"
msgstr "Per favore conferma il tuo indirizzo email per completare la registrazione"

#: src/components/forms/AuthenticationForm.tsx:280
msgid "Input error"
msgstr "Errore d'inserimento dati"

#: src/components/forms/AuthenticationForm.tsx:281
msgid "Check your input and try again. "
msgstr "Controllare i dati inseriti e riprovare. "

#: src/components/forms/AuthenticationForm.tsx:305
msgid "This will be used for a confirmation"
msgstr "Questo verrà utilizzato per una conferma"

#: src/components/forms/AuthenticationForm.tsx:318
msgid "Password repeat"
msgstr "Ripeti password"

#: src/components/forms/AuthenticationForm.tsx:320
msgid "Repeat password"
msgstr "Ripeti password"

#: src/components/forms/AuthenticationForm.tsx:332
#: src/pages/Auth/Login.tsx:121
#: src/pages/Auth/Register.tsx:13
msgid "Register"
msgstr "Registrati"

#: src/components/forms/AuthenticationForm.tsx:338
msgid "Or use SSO"
msgstr "Oppure usa SSO"

#: src/components/forms/AuthenticationForm.tsx:348
msgid "Registration not active"
msgstr "Registrazione non attiva"

#: src/components/forms/AuthenticationForm.tsx:349
msgid "This might be related to missing mail settings or could be a deliberate decision."
msgstr "Questo potrebbe essere legato alle impostazioni di posta mancanti o potrebbe essere una decisione deliberata."

#: src/components/forms/HostOptionsForm.tsx:36
#: src/components/forms/HostOptionsForm.tsx:67
msgid "Host"
msgstr "Host"

#: src/components/forms/HostOptionsForm.tsx:42
#: src/components/forms/HostOptionsForm.tsx:70
#: src/components/forms/InstanceOptions.tsx:124
#: src/components/plugins/PluginDrawer.tsx:68
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:19
#: src/pages/part/CategoryDetail.tsx:86
#: src/pages/part/PartDetail.tsx:447
#: src/pages/stock/LocationDetail.tsx:84
#: src/tables/machine/MachineTypeTable.tsx:72
#: src/tables/machine/MachineTypeTable.tsx:118
#: src/tables/machine/MachineTypeTable.tsx:236
#: src/tables/machine/MachineTypeTable.tsx:339
#: src/tables/plugin/PluginErrorTable.tsx:33
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:36
#: src/tables/settings/ApiTokenTable.tsx:58
#: src/tables/settings/GroupTable.tsx:95
#: src/tables/settings/GroupTable.tsx:148
#: src/tables/settings/GroupTable.tsx:196
#: src/tables/settings/PendingTasksTable.tsx:37
#: src/tables/stock/LocationTypesTable.tsx:74
msgid "Name"
msgstr "Nome"

#: src/components/forms/HostOptionsForm.tsx:75
msgid "No one here..."
msgstr "Non c'è nessuno qui..."

#: src/components/forms/HostOptionsForm.tsx:86
msgid "Add Host"
msgstr "Aggiungi Host"

#: src/components/forms/HostOptionsForm.tsx:90
#: src/components/items/RoleTable.tsx:224
#: src/components/items/TransferList.tsx:215
#: src/components/items/TransferList.tsx:223
msgid "Save"
msgstr "Salva"

#: src/components/forms/InstanceOptions.tsx:43
#~ msgid "Select destination instance"
#~ msgstr "Select destination instance"

#: src/components/forms/InstanceOptions.tsx:58
msgid "Select Server"
msgstr "Seleziona Il Server"

#: src/components/forms/InstanceOptions.tsx:68
#: src/components/forms/InstanceOptions.tsx:92
msgid "Edit host options"
msgstr "Modifica opzioni host"

#: src/components/forms/InstanceOptions.tsx:71
#~ msgid "Edit possible host options"
#~ msgstr "Edit possible host options"

#: src/components/forms/InstanceOptions.tsx:76
msgid "Save host selection"
msgstr "Salva selezione host"

#: src/components/forms/InstanceOptions.tsx:98
#~ msgid "Version: {0}"
#~ msgstr "Version: {0}"

#: src/components/forms/InstanceOptions.tsx:100
#~ msgid "API:{0}"
#~ msgstr "API:{0}"

#: src/components/forms/InstanceOptions.tsx:102
#~ msgid "Name: {0}"
#~ msgstr "Name: {0}"

#: src/components/forms/InstanceOptions.tsx:104
#~ msgid "State: <0>worker</0> ({0}), <1>plugins</1>{1}"
#~ msgstr "State: <0>worker</0> ({0}), <1>plugins</1>{1}"

#: src/components/forms/InstanceOptions.tsx:118
#: src/pages/Index/Settings/SystemSettings.tsx:45
msgid "Server"
msgstr "Server"

#: src/components/forms/InstanceOptions.tsx:130
#: src/components/plugins/PluginDrawer.tsx:88
#: src/tables/plugin/PluginListTable.tsx:127
msgid "Version"
msgstr "Versione"

#: src/components/forms/InstanceOptions.tsx:136
#: src/components/modals/AboutInvenTreeModal.tsx:122
#: src/components/modals/ServerInfoModal.tsx:34
msgid "API Version"
msgstr "Versione API"

#: src/components/forms/InstanceOptions.tsx:142
#: src/components/nav/NavigationDrawer.tsx:205
#: src/pages/Index/Settings/AdminCenter/Index.tsx:218
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:46
msgid "Plugins"
msgstr "Plugin"

#: src/components/forms/InstanceOptions.tsx:143
#: src/tables/part/PartTestTemplateTable.tsx:117
#: src/tables/settings/TemplateTable.tsx:251
#: src/tables/settings/TemplateTable.tsx:362
#: src/tables/stock/StockItemTestResultTable.tsx:416
msgid "Enabled"
msgstr "Abilitato"

#: src/components/forms/InstanceOptions.tsx:143
msgid "Disabled"
msgstr "Disabilitato"

#: src/components/forms/InstanceOptions.tsx:149
msgid "Worker"
msgstr "Worker"

#: src/components/forms/InstanceOptions.tsx:150
#: src/tables/settings/FailedTasksTable.tsx:48
msgid "Stopped"
msgstr "Fermato"

#: src/components/forms/InstanceOptions.tsx:150
msgid "Running"
msgstr "In Esecuzione"

#: src/components/forms/fields/IconField.tsx:83
msgid "No icon selected"
msgstr "Nessuna icona selezionata"

#: src/components/forms/fields/IconField.tsx:161
msgid "Uncategorized"
msgstr "Non categorizzato"

#: src/components/forms/fields/IconField.tsx:211
#: src/components/nav/Layout.tsx:80
#: src/tables/part/PartThumbTable.tsx:201
msgid "Search..."
msgstr "Ricerca..."

#: src/components/forms/fields/IconField.tsx:225
msgid "Select category"
msgstr "Seleziona categoria"

#: src/components/forms/fields/IconField.tsx:234
msgid "Select pack"
msgstr "Seleziona la confezione"

#. placeholder {0}: filteredIcons.length
#: src/components/forms/fields/IconField.tsx:239
msgid "{0} icons"
msgstr "{0} icone"

#: src/components/forms/fields/RelatedModelField.tsx:388
#: src/components/modals/AboutInvenTreeModal.tsx:94
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:332
msgid "Loading"
msgstr "Caricamento"

#: src/components/forms/fields/RelatedModelField.tsx:390
msgid "No results found"
msgstr "Nessun risultato trovato"

#: src/components/forms/fields/TableField.tsx:46
msgid "modelRenderer entry required for tables"
msgstr "Voce ModelRenderer necessaria per le tabelle"

#: src/components/forms/fields/TableField.tsx:187
msgid "No entries available"
msgstr "Nessuna voce disponibile"

#: src/components/forms/fields/TableField.tsx:198
msgid "Add new row"
msgstr "Aggiungi nuova riga"

#: src/components/images/DetailsImage.tsx:252
#~ msgid "Select image"
#~ msgstr "Select image"

#: src/components/images/Thumbnail.tsx:12
msgid "Thumbnail"
msgstr "Miniatura"

#: src/components/importer/ImportDataSelector.tsx:175
msgid "Importing Rows"
msgstr "Importa Righe"

#: src/components/importer/ImportDataSelector.tsx:176
msgid "Please wait while the data is imported"
msgstr "Si prega di attendere mentre i dati vengono importati"

#: src/components/importer/ImportDataSelector.tsx:193
msgid "An error occurred while importing data"
msgstr "Si è verificato un errore durante l'importazione dei dati"

#: src/components/importer/ImportDataSelector.tsx:214
msgid "Edit Data"
msgstr "Modifica dati"

#: src/components/importer/ImportDataSelector.tsx:246
msgid "Delete Row"
msgstr "Elimina riga"

#: src/components/importer/ImportDataSelector.tsx:276
msgid "Row"
msgstr "Riga"

#: src/components/importer/ImportDataSelector.tsx:294
msgid "Row contains errors"
msgstr "La riga contiene errori"

#: src/components/importer/ImportDataSelector.tsx:335
msgid "Accept"
msgstr "Accetta"

#: src/components/importer/ImportDataSelector.tsx:368
msgid "Valid"
msgstr "Valido"

#: src/components/importer/ImportDataSelector.tsx:369
msgid "Filter by row validation status"
msgstr "Filtra per stato di convalida della riga"

#: src/components/importer/ImportDataSelector.tsx:374
#: src/components/wizards/WizardDrawer.tsx:101
#: src/tables/build/BuildOutputTable.tsx:543
msgid "Complete"
msgstr "Completato"

#: src/components/importer/ImportDataSelector.tsx:375
msgid "Filter by row completion status"
msgstr "Filtra per stato completamento riga"

#: src/components/importer/ImportDataSelector.tsx:393
msgid "Import selected rows"
msgstr "Importa righe selezionate"

#: src/components/importer/ImportDataSelector.tsx:408
msgid "Processing Data"
msgstr "Elaborazione dati"

#: src/components/importer/ImporterColumnSelector.tsx:55
#: src/components/importer/ImporterColumnSelector.tsx:186
#: src/components/items/ErrorItem.tsx:12
#: src/functions/api.tsx:60
#: src/functions/auth.tsx:333
msgid "An error occurred"
msgstr "Si è verificato un errore"

#: src/components/importer/ImporterColumnSelector.tsx:68
msgid "Select column, or leave blank to ignore this field."
msgstr "Seleziona la colonna o lascia vuoto per ignorare questo campo."

#: src/components/importer/ImporterColumnSelector.tsx:91
#~ msgid "Select a column from the data file"
#~ msgstr "Select a column from the data file"

#: src/components/importer/ImporterColumnSelector.tsx:104
#~ msgid "Map data columns to database fields"
#~ msgstr "Map data columns to database fields"

#: src/components/importer/ImporterColumnSelector.tsx:119
#~ msgid "Imported Column Name"
#~ msgstr "Imported Column Name"

#: src/components/importer/ImporterColumnSelector.tsx:192
msgid "Ignore this field"
msgstr "Ignora questo campo"

#: src/components/importer/ImporterColumnSelector.tsx:206
msgid "Mapping data columns to database fields"
msgstr "Mappatura colonne di dati ai campi del database"

#: src/components/importer/ImporterColumnSelector.tsx:211
msgid "Accept Column Mapping"
msgstr "Accetta Mappatura Colonna"

#: src/components/importer/ImporterColumnSelector.tsx:224
msgid "Database Field"
msgstr "Campo Database"

#: src/components/importer/ImporterColumnSelector.tsx:225
msgid "Field Description"
msgstr "Campo descrizione"

#: src/components/importer/ImporterColumnSelector.tsx:226
msgid "Imported Column"
msgstr "Colonna Importata"

#: src/components/importer/ImporterColumnSelector.tsx:227
msgid "Default Value"
msgstr "Valore Predefinito"

#: src/components/importer/ImporterDrawer.tsx:43
msgid "Upload File"
msgstr "Carica file"

#: src/components/importer/ImporterDrawer.tsx:44
msgid "Map Columns"
msgstr "Mappa colonne"

#: src/components/importer/ImporterDrawer.tsx:45
msgid "Import Data"
msgstr "Importa dati"

#: src/components/importer/ImporterDrawer.tsx:46
msgid "Process Data"
msgstr "Elaborazione dati"

#: src/components/importer/ImporterDrawer.tsx:47
msgid "Complete Import"
msgstr "Importazione Completata"

#: src/components/importer/ImporterDrawer.tsx:89
msgid "Failed to fetch import session data"
msgstr "Recupero dati della sessione d'importazione non riuscito"

#: src/components/importer/ImporterDrawer.tsx:97
#~ msgid "Cancel import session"
#~ msgstr "Cancel import session"

#: src/components/importer/ImporterDrawer.tsx:104
msgid "Import Complete"
msgstr "Importazione Completata"

#: src/components/importer/ImporterDrawer.tsx:107
msgid "Data has been imported successfully"
msgstr "I dati sono stati importati correttamente"

#: src/components/importer/ImporterDrawer.tsx:109
#: src/components/modals/AboutInvenTreeModal.tsx:197
#: src/components/modals/ServerInfoModal.tsx:134
#: src/forms/BomForms.tsx:132
msgid "Close"
msgstr "Chiudi"

#: src/components/importer/ImporterDrawer.tsx:119
#~ msgid "Import session has unknown status"
#~ msgstr "Import session has unknown status"

#: src/components/importer/ImporterDrawer.tsx:128
msgid "Importing Data"
msgstr "Importazione dei dati"

#: src/components/importer/ImporterImportProgress.tsx:36
#~ msgid "Importing Records"
#~ msgstr "Importing Records"

#: src/components/importer/ImporterImportProgress.tsx:39
#~ msgid "Imported rows"
#~ msgstr "Imported rows"

#: src/components/importer/ImporterStatus.tsx:19
msgid "Unknown Status"
msgstr "Stato sconosciuto"

#: src/components/items/ActionDropdown.tsx:133
msgid "Options"
msgstr "Opzioni"

#: src/components/items/ActionDropdown.tsx:162
#~ msgid "Link custom barcode"
#~ msgstr "Link custom barcode"

#: src/components/items/ActionDropdown.tsx:169
#: src/tables/InvenTreeTableHeader.tsx:193
#: src/tables/InvenTreeTableHeader.tsx:194
msgid "Barcode Actions"
msgstr "Azioni Codice A Barre"

#: src/components/items/ActionDropdown.tsx:174
msgid "View Barcode"
msgstr "Visualizza codice a barre"

#: src/components/items/ActionDropdown.tsx:176
msgid "View barcode"
msgstr "Visualizza codice a barre"

#: src/components/items/ActionDropdown.tsx:182
msgid "Link Barcode"
msgstr "Collega Codice a Barre"

#: src/components/items/ActionDropdown.tsx:184
msgid "Link a custom barcode to this item"
msgstr "Collega un codice a barre personalizzato a questo articolo"

#: src/components/items/ActionDropdown.tsx:192
msgid "Unlink custom barcode"
msgstr "Scollega codice a barre personalizzato"

#: src/components/items/ActionDropdown.tsx:244
msgid "Edit item"
msgstr "Modifica articolo"

#: src/components/items/ActionDropdown.tsx:256
msgid "Delete item"
msgstr "Elimina articolo"

#: src/components/items/ActionDropdown.tsx:264
#: src/components/items/ActionDropdown.tsx:265
msgid "Hold"
msgstr "Trattenuto"

#: src/components/items/ActionDropdown.tsx:288
msgid "Duplicate item"
msgstr "Duplica articolo"

#: src/components/items/BarcodeInput.tsx:24
#~ msgid "Scan barcode data here using barcode scanner"
#~ msgstr "Scan barcode data here using barcode scanner"

#: src/components/items/ColorToggle.tsx:17
msgid "Toggle color scheme"
msgstr "Commuta schema colore"

#: src/components/items/DocTooltip.tsx:92
#: src/components/items/GettingStartedCarousel.tsx:20
msgid "Read More"
msgstr "Approfondisci"

#: src/components/items/ErrorItem.tsx:8
#: src/functions/api.tsx:51
#: src/tables/settings/PendingTasksTable.tsx:80
msgid "Unknown error"
msgstr "Errore sconosciuto"

#: src/components/items/ErrorItem.tsx:13
#~ msgid "An error occurred:"
#~ msgstr "An error occurred:"

#: src/components/items/GettingStartedCarousel.tsx:27
#~ msgid "Read more"
#~ msgstr "Read more"

#: src/components/items/InfoItem.tsx:27
msgid "None"
msgstr "Vuoto"

#: src/components/items/InvenTreeLogo.tsx:23
msgid "InvenTree Logo"
msgstr "Logo InvenTree"

#: src/components/items/LanguageToggle.tsx:21
msgid "Select language"
msgstr "Seleziona Lingua"

#: src/components/items/OnlyStaff.tsx:10
#: src/components/modals/AboutInvenTreeModal.tsx:50
msgid "This information is only available for staff users"
msgstr "Questa informazione è disponibile solo per gli utenti del personale"

#: src/components/items/Placeholder.tsx:14
#~ msgid "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."
#~ msgstr "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."

#: src/components/items/Placeholder.tsx:17
#~ msgid "PLH"
#~ msgstr "PLH"

#: src/components/items/RoleTable.tsx:81
msgid "Updating"
msgstr "Aggiornamento in corso"

#: src/components/items/RoleTable.tsx:82
msgid "Updating group roles"
msgstr "Aggiornamento dei ruoli di gruppo"

#: src/components/items/RoleTable.tsx:118
#: src/components/settings/ConfigValueList.tsx:42
#: src/pages/part/pricing/BomPricingPanel.tsx:191
#: src/pages/part/pricing/VariantPricingPanel.tsx:51
#: src/tables/purchasing/SupplierPartTable.tsx:137
msgid "Updated"
msgstr "Aggiornato"

#: src/components/items/RoleTable.tsx:119
msgid "Group roles updated"
msgstr "Ruoli di gruppo aggiornati"

#: src/components/items/RoleTable.tsx:135
msgid "Role"
msgstr "Ruolo"

#: src/components/items/RoleTable.tsx:140
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:413
msgid "View"
msgstr "Vista"

#: src/components/items/RoleTable.tsx:145
msgid "Change"
msgstr "Cambiare"

#: src/components/items/RoleTable.tsx:150
#: src/forms/StockForms.tsx:867
#: src/tables/stock/StockItemTestResultTable.tsx:364
msgid "Add"
msgstr "Aggiungi"

#: src/components/items/RoleTable.tsx:203
msgid "Reset group roles"
msgstr "Reimposta i ruoli di gruppo"

#: src/components/items/RoleTable.tsx:212
msgid "Reset"
msgstr "Reimposta"

#: src/components/items/RoleTable.tsx:215
msgid "Save group roles"
msgstr "Salva ruoli di gruppo"

#: src/components/items/TransferList.tsx:65
msgid "No items"
msgstr "Nessun articolo"

#: src/components/items/TransferList.tsx:161
#: src/components/render/Stock.tsx:95
#: src/pages/part/PartDetail.tsx:980
#: src/pages/stock/StockDetail.tsx:262
#: src/pages/stock/StockDetail.tsx:913
#: src/tables/build/BuildAllocatedStockTable.tsx:135
#: src/tables/build/BuildLineTable.tsx:190
#: src/tables/part/PartTable.tsx:124
#: src/tables/stock/StockItemTable.tsx:183
#: src/tables/stock/StockItemTable.tsx:344
msgid "Available"
msgstr "Disponibile"

#: src/components/items/TransferList.tsx:162
msgid "Selected"
msgstr "Selezionati"

#: src/components/modals/AboutInvenTreeModal.tsx:103
#~ msgid "Your InvenTree version status is"
#~ msgstr "Your InvenTree version status is"

#: src/components/modals/AboutInvenTreeModal.tsx:116
msgid "InvenTree Version"
msgstr "Versione di InvenTree"

#: src/components/modals/AboutInvenTreeModal.tsx:128
msgid "Python Version"
msgstr "Versione Python"

#: src/components/modals/AboutInvenTreeModal.tsx:133
msgid "Django Version"
msgstr "Versione Django"

#: src/components/modals/AboutInvenTreeModal.tsx:142
msgid "Commit Hash"
msgstr "Hash del Commit"

#: src/components/modals/AboutInvenTreeModal.tsx:147
msgid "Commit Date"
msgstr "Data del Commit"

#: src/components/modals/AboutInvenTreeModal.tsx:152
msgid "Commit Branch"
msgstr "Branch del commit"

#: src/components/modals/AboutInvenTreeModal.tsx:163
msgid "Version Information"
msgstr "Informazioni sulla versione"

#: src/components/modals/AboutInvenTreeModal.tsx:165
#~ msgid "Credits"
#~ msgstr "Credits"

#: src/components/modals/AboutInvenTreeModal.tsx:168
#~ msgid "InvenTree Documentation"
#~ msgstr "InvenTree Documentation"

#: src/components/modals/AboutInvenTreeModal.tsx:169
#~ msgid "View Code on GitHub"
#~ msgstr "View Code on GitHub"

#: src/components/modals/AboutInvenTreeModal.tsx:172
msgid "Links"
msgstr "Collegamenti"

#: src/components/modals/AboutInvenTreeModal.tsx:178
#: src/components/nav/NavigationDrawer.tsx:217
#: src/defaults/actions.tsx:35
msgid "Documentation"
msgstr "Documentazione"

#: src/components/modals/AboutInvenTreeModal.tsx:179
msgid "Source Code"
msgstr "Codice Sorgente"

#: src/components/modals/AboutInvenTreeModal.tsx:180
msgid "Mobile App"
msgstr "App Mobile"

#: src/components/modals/AboutInvenTreeModal.tsx:181
msgid "Submit Bug Report"
msgstr "Invia Segnalazione Bug"

#: src/components/modals/AboutInvenTreeModal.tsx:189
#: src/components/modals/ServerInfoModal.tsx:147
#~ msgid "Dismiss"
#~ msgstr "Dismiss"

#: src/components/modals/AboutInvenTreeModal.tsx:190
msgid "Copy version information"
msgstr "Copia informazioni versione"

#: src/components/modals/AboutInvenTreeModal.tsx:207
msgid "Development Version"
msgstr "Versione di sviluppo"

#: src/components/modals/AboutInvenTreeModal.tsx:209
msgid "Up to Date"
msgstr "Aggiornato"

#: src/components/modals/AboutInvenTreeModal.tsx:211
msgid "Update Available"
msgstr "Aggiornamento disponibile"

#: src/components/modals/LicenseModal.tsx:41
msgid "No license text available"
msgstr "Nessun testo di licenza disponibile"

#: src/components/modals/LicenseModal.tsx:48
msgid "No Information provided - this is likely a server issue"
msgstr "Nessuna informazione fornita - questo è probabilmente un problema del server"

#: src/components/modals/LicenseModal.tsx:81
msgid "Loading license information"
msgstr "Caricamento delle informazioni sulla licenza"

#: src/components/modals/LicenseModal.tsx:87
msgid "Failed to fetch license information"
msgstr "Recupero delle informazioni sulla licenza non riuscito"

#: src/components/modals/LicenseModal.tsx:99
msgid "{key} Packages"
msgstr "{key} Pacchetti"

#: src/components/modals/QrCodeModal.tsx:24
#~ msgid "Unknown response"
#~ msgstr "Unknown response"

#: src/components/modals/QrCodeModal.tsx:39
#~ msgid "No scans yet!"
#~ msgstr "No scans yet!"

#: src/components/modals/QrCodeModal.tsx:57
#~ msgid "Close modal"
#~ msgstr "Close modal"

#: src/components/modals/ServerInfoModal.tsx:22
msgid "Instance Name"
msgstr "Nome istanza"

#: src/components/modals/ServerInfoModal.tsx:28
msgid "Server Version"
msgstr "Versione Server"

#: src/components/modals/ServerInfoModal.tsx:38
#~ msgid "Bebug Mode"
#~ msgstr "Bebug Mode"

#: src/components/modals/ServerInfoModal.tsx:40
msgid "Database"
msgstr "Database"

#: src/components/modals/ServerInfoModal.tsx:49
#: src/components/nav/Alerts.tsx:41
msgid "Debug Mode"
msgstr "Modalità Debug"

#: src/components/modals/ServerInfoModal.tsx:54
msgid "Server is running in debug mode"
msgstr "Server in esecuzione in modalità debug"

#: src/components/modals/ServerInfoModal.tsx:62
msgid "Docker Mode"
msgstr "Modalità Docker"

#: src/components/modals/ServerInfoModal.tsx:65
msgid "Server is deployed using docker"
msgstr "Il server è distribuito utilizzando docker"

#: src/components/modals/ServerInfoModal.tsx:71
msgid "Plugin Support"
msgstr "Supporto Plugin"

#: src/components/modals/ServerInfoModal.tsx:76
msgid "Plugin support enabled"
msgstr "Supporto Plugin Abilitato"

#: src/components/modals/ServerInfoModal.tsx:78
msgid "Plugin support disabled"
msgstr "Supporto plugin disabilitato"

#: src/components/modals/ServerInfoModal.tsx:85
msgid "Server status"
msgstr "Stato del Server"

#: src/components/modals/ServerInfoModal.tsx:91
msgid "Healthy"
msgstr "Sano"

#: src/components/modals/ServerInfoModal.tsx:93
msgid "Issues detected"
msgstr "Problemi rilevati"

#: src/components/modals/ServerInfoModal.tsx:102
#: src/components/nav/Alerts.tsx:50
msgid "Background Worker"
msgstr "Processo in background"

#: src/components/modals/ServerInfoModal.tsx:107
msgid "The background worker process is not running"
msgstr "Il processo di lavoro in background non è in esecuzione"

#: src/components/modals/ServerInfoModal.tsx:107
#~ msgid "The Background worker process is not running."
#~ msgstr "The Background worker process is not running."

#: src/components/modals/ServerInfoModal.tsx:115
#: src/pages/Index/Settings/AdminCenter/Index.tsx:119
msgid "Email Settings"
msgstr "Impostazioni e-mail"

#: src/components/modals/ServerInfoModal.tsx:118
#~ msgid "Email settings not configured"
#~ msgstr "Email settings not configured"

#: src/components/modals/ServerInfoModal.tsx:120
#: src/components/nav/Alerts.tsx:61
msgid "Email settings not configured."
msgstr "Impostazioni dell'email non configurate."

#: src/components/nav/Alerts.tsx:43
msgid "The server is running in debug mode."
msgstr "Il server è in esecuzione in modalità debug."

#: src/components/nav/Alerts.tsx:52
msgid "The background worker process is not running."
msgstr "Il processo di lavoro in background non è in esecuzione."

#: src/components/nav/Alerts.tsx:59
msgid "Email settings"
msgstr "Impostazioni email"

#: src/components/nav/Alerts.tsx:68
msgid "Server Restart"
msgstr "Riavvio Server"

#: src/components/nav/Alerts.tsx:70
msgid "The server requires a restart to apply changes."
msgstr "Il server richiede un riavvio per applicare le modifiche."

#: src/components/nav/Alerts.tsx:80
msgid "Database Migrations"
msgstr "Migrazioni Database"

#: src/components/nav/Alerts.tsx:82
msgid "There are pending database migrations."
msgstr "Ci sono migrazioni di database in sospeso."

#: src/components/nav/Alerts.tsx:98
msgid "Alerts"
msgstr "Avvisi"

#: src/components/nav/Alerts.tsx:141
msgid "Learn more about {code}"
msgstr "Scopri di più su {code}"

#: src/components/nav/Header.tsx:187
#: src/components/nav/NavigationDrawer.tsx:141
#: src/components/nav/NotificationDrawer.tsx:181
#: src/pages/Index/Settings/SystemSettings.tsx:121
#: src/pages/Index/Settings/UserSettings.tsx:106
#: src/pages/Notifications.tsx:45
#: src/pages/Notifications.tsx:130
msgid "Notifications"
msgstr "Notifiche"

#: src/components/nav/Layout.tsx:83
msgid "Nothing found..."
msgstr "Nessun risultato..."

#: src/components/nav/MainMenu.tsx:40
#: src/pages/Index/Profile/Profile.tsx:15
#~ msgid "Profile"
#~ msgstr "Profile"

#: src/components/nav/MainMenu.tsx:52
#: src/components/nav/NavigationDrawer.tsx:193
#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:21
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:39
msgid "Settings"
msgstr "Impostazioni"

#: src/components/nav/MainMenu.tsx:59
#: src/pages/Index/Settings/UserSettings.tsx:145
msgid "Account Settings"
msgstr "Impostazioni Account"

#: src/components/nav/MainMenu.tsx:59
#: src/defaults/menuItems.tsx:15
#~ msgid "Account settings"
#~ msgstr "Account settings"

#: src/components/nav/MainMenu.tsx:67
#: src/components/nav/NavigationDrawer.tsx:153
#: src/components/nav/SettingsHeader.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:347
#: src/pages/Index/Settings/SystemSettings.tsx:352
msgid "System Settings"
msgstr "Impostazioni di sistema"

#: src/components/nav/MainMenu.tsx:68
#~ msgid "Current language {locale}"
#~ msgstr "Current language {locale}"

#: src/components/nav/MainMenu.tsx:71
#~ msgid "Switch to pseudo language"
#~ msgstr "Switch to pseudo language"

#: src/components/nav/MainMenu.tsx:76
#: src/components/nav/NavigationDrawer.tsx:160
#: src/components/nav/SettingsHeader.tsx:42
#: src/defaults/actions.tsx:83
#: src/pages/Index/Settings/AdminCenter/Index.tsx:282
#: src/pages/Index/Settings/AdminCenter/Index.tsx:287
msgid "Admin Center"
msgstr "Centro Amministratore"

#: src/components/nav/MainMenu.tsx:96
msgid "Logout"
msgstr "Disconnettiti"

#: src/components/nav/NavHoverMenu.tsx:84
#~ msgid "View all"
#~ msgstr "View all"

#: src/components/nav/NavHoverMenu.tsx:100
#: src/components/nav/NavHoverMenu.tsx:110
#~ msgid "Get started"
#~ msgstr "Get started"

#: src/components/nav/NavHoverMenu.tsx:103
#~ msgid "Overview over high-level objects, functions and possible usecases."
#~ msgstr "Overview over high-level objects, functions and possible usecases."

#: src/components/nav/NavigationDrawer.tsx:60
#~ msgid "Pages"
#~ msgstr "Pages"

#: src/components/nav/NavigationDrawer.tsx:84
#: src/components/render/Part.tsx:33
#: src/defaults/links.tsx:42
#: src/forms/StockForms.tsx:735
#: src/pages/Index/Settings/SystemSettings.tsx:224
#: src/pages/part/PartDetail.tsx:804
#: src/pages/stock/LocationDetail.tsx:390
#: src/pages/stock/StockDetail.tsx:626
#: src/tables/stock/StockItemTable.tsx:86
msgid "Stock"
msgstr "Stock"

#: src/components/nav/NavigationDrawer.tsx:91
#: src/defaults/links.tsx:48
#: src/pages/build/BuildDetail.tsx:741
#: src/pages/build/BuildIndex.tsx:102
msgid "Manufacturing"
msgstr "Fabbricazione"

#: src/components/nav/NavigationDrawer.tsx:98
#: src/defaults/links.tsx:54
#: src/pages/company/ManufacturerDetail.tsx:9
#: src/pages/company/ManufacturerPartDetail.tsx:260
#: src/pages/company/SupplierDetail.tsx:9
#: src/pages/company/SupplierPartDetail.tsx:356
#: src/pages/purchasing/PurchaseOrderDetail.tsx:534
#: src/pages/purchasing/PurchasingIndex.tsx:122
msgid "Purchasing"
msgstr "Acquisto"

#: src/components/nav/NavigationDrawer.tsx:105
#: src/defaults/links.tsx:60
#: src/pages/company/CustomerDetail.tsx:9
#: src/pages/sales/ReturnOrderDetail.tsx:521
#: src/pages/sales/SalesIndex.tsx:139
#: src/pages/sales/SalesOrderDetail.tsx:591
#: src/pages/sales/SalesOrderShipmentDetail.tsx:360
msgid "Sales"
msgstr "Vendite"

#: src/components/nav/NavigationDrawer.tsx:147
#: src/components/nav/SettingsHeader.tsx:40
#: src/pages/Index/Settings/UserSettings.tsx:141
msgid "User Settings"
msgstr "Impostazioni Utente"

#: src/components/nav/NavigationDrawer.tsx:188
msgid "Navigation"
msgstr "Navigazione"

#: src/components/nav/NavigationDrawer.tsx:223
msgid "About"
msgstr "Info"

#: src/components/nav/NavigationTree.tsx:211
msgid "Error loading navigation tree."
msgstr "Errore nel caricare l'albero di navigazione."

#: src/components/nav/NotificationDrawer.tsx:183
#: src/pages/Notifications.tsx:74
msgid "Mark all as read"
msgstr "Segna tutti come già letti"

#: src/components/nav/NotificationDrawer.tsx:193
msgid "View all notifications"
msgstr "Visualizza tutte le notifiche"

#: src/components/nav/NotificationDrawer.tsx:216
msgid "You have no unread notifications."
msgstr "Non hai notifiche non lette."

#: src/components/nav/NotificationDrawer.tsx:238
msgid "Error loading notifications."
msgstr "Errore nel caricamento delle notifiche."

#: src/components/nav/SearchDrawer.tsx:106
msgid "No Overview Available"
msgstr "Nessuna Riepilogo Disponibile"

#: src/components/nav/SearchDrawer.tsx:107
msgid "No overview available for this model type"
msgstr "Nessuna riepilogo disponibile per questo tipo di modello"

#: src/components/nav/SearchDrawer.tsx:125
msgid "View all results"
msgstr "Visualizza tutti i risultati"

#: src/components/nav/SearchDrawer.tsx:140
msgid "results"
msgstr "risultati"

#: src/components/nav/SearchDrawer.tsx:144
msgid "Remove search group"
msgstr "Rimuovi gruppo di ricerca"

#: src/components/nav/SearchDrawer.tsx:288
#: src/pages/company/ManufacturerPartDetail.tsx:176
#: src/pages/part/PartDetail.tsx:861
#: src/pages/part/PartSupplierDetail.tsx:15
#: src/pages/purchasing/PurchasingIndex.tsx:81
msgid "Suppliers"
msgstr "Fornitori"

#: src/components/nav/SearchDrawer.tsx:298
#: src/pages/part/PartSupplierDetail.tsx:23
#: src/pages/purchasing/PurchasingIndex.tsx:98
msgid "Manufacturers"
msgstr "Produttori"

#: src/components/nav/SearchDrawer.tsx:308
#: src/pages/sales/SalesIndex.tsx:124
msgid "Customers"
msgstr "Clienti"

#: src/components/nav/SearchDrawer.tsx:462
#~ msgid "No results"
#~ msgstr "No results"

#: src/components/nav/SearchDrawer.tsx:477
msgid "Enter search text"
msgstr "Inserisci il testo della ricerca"

#: src/components/nav/SearchDrawer.tsx:488
msgid "Refresh search results"
msgstr "Aggiorna Risultati di Ricerca"

#: src/components/nav/SearchDrawer.tsx:499
#: src/components/nav/SearchDrawer.tsx:506
msgid "Search Options"
msgstr "Opzioni di Ricerca"

#: src/components/nav/SearchDrawer.tsx:509
msgid "Whole word search"
msgstr "Ricerca parole intere"

#: src/components/nav/SearchDrawer.tsx:518
msgid "Regex search"
msgstr "Ricerca con regex"

#: src/components/nav/SearchDrawer.tsx:527
msgid "Notes search"
msgstr "Ricerca note"

#: src/components/nav/SearchDrawer.tsx:575
msgid "An error occurred during search query"
msgstr "Si è verificato un errore durante la ricerca"

#: src/components/nav/SearchDrawer.tsx:586
#: src/tables/part/PartTestTemplateTable.tsx:82
msgid "No Results"
msgstr "Nessun risultato"

#: src/components/nav/SearchDrawer.tsx:589
msgid "No results available for search query"
msgstr "Nessun risultato disponibile per la ricerca"

#: src/components/panels/AttachmentPanel.tsx:18
msgid "Attachments"
msgstr "Allegati"

#: src/components/panels/NotesPanel.tsx:23
#: src/tables/build/BuildOrderTestTable.tsx:196
#: src/tables/stock/StockTrackingTable.tsx:212
msgid "Notes"
msgstr "Note"

#: src/components/panels/PanelGroup.tsx:158
msgid "Plugin Provided"
msgstr "Plugin Fornito"

#: src/components/panels/PanelGroup.tsx:275
msgid "Collapse panels"
msgstr "Comprimi pannelli"

#: src/components/panels/PanelGroup.tsx:275
msgid "Expand panels"
msgstr "Espandi pannelli"

#: src/components/plugins/LocateItemButton.tsx:68
#: src/components/plugins/LocateItemButton.tsx:88
msgid "Locate Item"
msgstr "Localizza Articolo"

#: src/components/plugins/LocateItemButton.tsx:70
msgid "Item location requested"
msgstr "Localizzazione articolo richiesta"

#: src/components/plugins/PluginDrawer.tsx:47
msgid "Plugin Inactive"
msgstr "Plugin Inattivo"

#: src/components/plugins/PluginDrawer.tsx:50
msgid "Plugin is not active"
msgstr "Il plugin non è attivo"

#: src/components/plugins/PluginDrawer.tsx:59
msgid "Plugin Information"
msgstr "Informazioni Plugin"

#: src/components/plugins/PluginDrawer.tsx:73
#: src/forms/selectionListFields.tsx:104
#: src/pages/build/BuildDetail.tsx:244
#: src/pages/company/CompanyDetail.tsx:93
#: src/pages/company/ManufacturerPartDetail.tsx:91
#: src/pages/company/ManufacturerPartDetail.tsx:118
#: src/pages/company/SupplierPartDetail.tsx:144
#: src/pages/part/CategoryDetail.tsx:106
#: src/pages/part/PartDetail.tsx:461
#: src/pages/purchasing/PurchaseOrderDetail.tsx:144
#: src/pages/sales/ReturnOrderDetail.tsx:109
#: src/pages/sales/SalesOrderDetail.tsx:118
#: src/pages/stock/LocationDetail.tsx:104
#: src/tables/ColumnRenderers.tsx:269
#: src/tables/build/BuildAllocatedStockTable.tsx:91
#: src/tables/machine/MachineTypeTable.tsx:128
#: src/tables/machine/MachineTypeTable.tsx:239
#: src/tables/plugin/PluginListTable.tsx:110
msgid "Description"
msgstr "Descrizione"

#: src/components/plugins/PluginDrawer.tsx:78
msgid "Author"
msgstr "Autore"

#: src/components/plugins/PluginDrawer.tsx:83
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:41
#: src/pages/part/pricing/SaleHistoryPanel.tsx:38
#: src/tables/ColumnRenderers.tsx:411
#: src/tables/build/BuildOrderTestTable.tsx:204
msgid "Date"
msgstr "Data"

#: src/components/plugins/PluginDrawer.tsx:93
#: src/forms/selectionListFields.tsx:105
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:68
#: src/pages/core/UserDetail.tsx:81
#: src/pages/core/UserDetail.tsx:209
#: src/pages/part/PartDetail.tsx:615
#: src/tables/bom/UsedInTable.tsx:90
#: src/tables/company/CompanyTable.tsx:57
#: src/tables/company/CompanyTable.tsx:91
#: src/tables/machine/MachineListTable.tsx:336
#: src/tables/machine/MachineListTable.tsx:606
#: src/tables/part/ParametricPartTable.tsx:350
#: src/tables/part/PartTable.tsx:184
#: src/tables/part/PartVariantTable.tsx:15
#: src/tables/plugin/PluginListTable.tsx:96
#: src/tables/plugin/PluginListTable.tsx:412
#: src/tables/purchasing/SupplierPartTable.tsx:85
#: src/tables/purchasing/SupplierPartTable.tsx:179
#: src/tables/settings/ApiTokenTable.tsx:63
#: src/tables/settings/UserTable.tsx:410
#: src/tables/stock/StockItemTable.tsx:323
msgid "Active"
msgstr "Attivo"

#: src/components/plugins/PluginDrawer.tsx:105
msgid "Package Name"
msgstr "Nome Pacchetto"

#: src/components/plugins/PluginDrawer.tsx:111
msgid "Installation Path"
msgstr "Percorso d'installazione"

#: src/components/plugins/PluginDrawer.tsx:116
#: src/tables/machine/MachineTypeTable.tsx:151
#: src/tables/machine/MachineTypeTable.tsx:275
#: src/tables/plugin/PluginListTable.tsx:101
#: src/tables/plugin/PluginListTable.tsx:417
msgid "Builtin"
msgstr "Integrato"

#: src/components/plugins/PluginDrawer.tsx:121
msgid "Package"
msgstr "Pacchetto"

#: src/components/plugins/PluginDrawer.tsx:133
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:55
#: src/pages/Index/Settings/SystemSettings.tsx:330
#: src/pages/Index/Settings/UserSettings.tsx:128
msgid "Plugin Settings"
msgstr "Impostazioni Plugin"

#: src/components/plugins/PluginPanel.tsx:87
#~ msgid "Error occurred while rendering plugin content"
#~ msgstr "Error occurred while rendering plugin content"

#: src/components/plugins/PluginPanel.tsx:91
#~ msgid "Plugin did not provide panel rendering function"
#~ msgstr "Plugin did not provide panel rendering function"

#: src/components/plugins/PluginPanel.tsx:103
#~ msgid "No content provided for this plugin"
#~ msgstr "No content provided for this plugin"

#: src/components/plugins/PluginPanel.tsx:116
#: src/components/plugins/PluginSettingsPanel.tsx:76
#~ msgid "Error Loading Plugin"
#~ msgstr "Error Loading Plugin"

#: src/components/plugins/PluginSettingsPanel.tsx:51
#~ msgid "Error occurred while rendering plugin settings"
#~ msgstr "Error occurred while rendering plugin settings"

#: src/components/plugins/PluginSettingsPanel.tsx:55
#~ msgid "Plugin did not provide settings rendering function"
#~ msgstr "Plugin did not provide settings rendering function"

#: src/components/plugins/PluginUIFeature.tsx:102
msgid "Error occurred while rendering the template editor."
msgstr "Errore durante il rendering dell'editor del modello."

#: src/components/plugins/PluginUIFeature.tsx:119
msgid "Error Loading Plugin Editor"
msgstr "Errore Nel Caricamento Dell'Editor Plugin"

#: src/components/plugins/PluginUIFeature.tsx:155
msgid "Error occurred while rendering the template preview."
msgstr "Errore durante il rendering dell'editor del modello."

#: src/components/plugins/PluginUIFeature.tsx:166
msgid "Error Loading Plugin Preview"
msgstr "Errore Nel Caricamento Dell'Anteprima Del Plugin"

#: src/components/plugins/RemoteComponent.tsx:111
msgid "Invalid source or function name"
msgstr "Sorgente o nome della funzione non valido"

#: src/components/plugins/RemoteComponent.tsx:143
msgid "Error Loading Content"
msgstr "Errore nel caricamento dei contenuti"

#: src/components/plugins/RemoteComponent.tsx:147
msgid "Error occurred while loading plugin content"
msgstr "Errore durante il caricamento del contenuto del plugin"

#: src/components/render/Instance.tsx:238
#~ msgid "Unknown model: {model}"
#~ msgstr "Unknown model: {model}"

#: src/components/render/Instance.tsx:246
msgid "Unknown model: {model_name}"
msgstr "Modello sconosciuto: {model_name}"

#: src/components/render/ModelType.tsx:234
#~ msgid "Purchase Order Line Item"
#~ msgstr "Purchase Order Line Item"

#: src/components/render/ModelType.tsx:264
#~ msgid "Unknown Model"
#~ msgstr "Unknown Model"

#: src/components/render/ModelType.tsx:307
#~ msgid "Purchase Order Line Items"
#~ msgstr "Purchase Order Line Items"

#: src/components/render/ModelType.tsx:337
#~ msgid "Unknown Models"
#~ msgstr "Unknown Models"

#: src/components/render/Order.tsx:122
#: src/tables/sales/SalesOrderAllocationTable.tsx:170
msgid "Shipment"
msgstr "Spedizione"

#: src/components/render/Part.tsx:28
#: src/components/render/Plugin.tsx:17
#: src/components/render/User.tsx:37
#: src/pages/company/CompanyDetail.tsx:325
#: src/pages/company/SupplierPartDetail.tsx:369
#: src/pages/core/UserDetail.tsx:211
#: src/pages/part/PartDetail.tsx:1012
msgid "Inactive"
msgstr "Inattivo"

#: src/components/render/Part.tsx:31
#: src/tables/bom/BomTable.tsx:289
#: src/tables/part/PartTable.tsx:139
msgid "No stock"
msgstr "Nessuno stock"

#: src/components/render/Part.tsx:74
#: src/pages/part/PartDetail.tsx:488
#: src/tables/ColumnRenderers.tsx:224
#: src/tables/ColumnRenderers.tsx:233
#: src/tables/notifications/NotificationTable.tsx:32
#: src/tables/part/PartCategoryTemplateTable.tsx:71
msgid "Category"
msgstr "Categoria"

#: src/components/render/Stock.tsx:36
#: src/components/render/Stock.tsx:107
#: src/components/render/Stock.tsx:125
#: src/forms/BuildForms.tsx:759
#: src/forms/PurchaseOrderForms.tsx:592
#: src/forms/StockForms.tsx:733
#: src/forms/StockForms.tsx:779
#: src/forms/StockForms.tsx:825
#: src/forms/StockForms.tsx:864
#: src/forms/StockForms.tsx:900
#: src/forms/StockForms.tsx:938
#: src/forms/StockForms.tsx:980
#: src/forms/StockForms.tsx:1028
#: src/forms/StockForms.tsx:1072
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:88
#: src/pages/core/UserDetail.tsx:158
#: src/pages/stock/StockDetail.tsx:295
#: src/tables/ColumnRenderers.tsx:176
#: src/tables/ColumnRenderers.tsx:185
#: src/tables/Filter.tsx:392
#: src/tables/stock/StockTrackingTable.tsx:98
msgid "Location"
msgstr "Posizione"

#: src/components/render/Stock.tsx:92
#: src/pages/stock/StockDetail.tsx:195
#: src/pages/stock/StockDetail.tsx:901
#: src/tables/build/BuildAllocatedStockTable.tsx:121
#: src/tables/build/BuildOutputTable.tsx:111
#: src/tables/sales/SalesOrderAllocationTable.tsx:139
msgid "Serial Number"
msgstr "Numero Seriale"

#: src/components/render/Stock.tsx:97
#: src/components/wizards/OrderPartsWizard.tsx:222
#: src/forms/BuildForms.tsx:243
#: src/forms/BuildForms.tsx:605
#: src/forms/BuildForms.tsx:761
#: src/forms/PurchaseOrderForms.tsx:794
#: src/forms/ReturnOrderForms.tsx:240
#: src/forms/SalesOrderForms.tsx:270
#: src/forms/StockForms.tsx:781
#: src/pages/part/PartStockHistoryDetail.tsx:56
#: src/pages/part/PartStockHistoryDetail.tsx:210
#: src/pages/part/PartStockHistoryDetail.tsx:234
#: src/pages/part/pricing/BomPricingPanel.tsx:146
#: src/pages/part/pricing/PriceBreakPanel.tsx:89
#: src/pages/part/pricing/PriceBreakPanel.tsx:172
#: src/pages/stock/StockDetail.tsx:255
#: src/pages/stock/StockDetail.tsx:907
#: src/tables/build/BuildLineTable.tsx:84
#: src/tables/build/BuildOrderTestTable.tsx:251
#: src/tables/part/PartPurchaseOrdersTable.tsx:94
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:168
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:199
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:69
#: src/tables/sales/ReturnOrderLineItemTable.tsx:121
#: src/tables/stock/StockTrackingTable.tsx:72
msgid "Quantity"
msgstr "Quantità"

#: src/components/render/Stock.tsx:110
#: src/forms/BuildForms.tsx:312
#: src/forms/BuildForms.tsx:386
#: src/forms/BuildForms.tsx:450
#: src/forms/StockForms.tsx:734
#: src/forms/StockForms.tsx:780
#: src/forms/StockForms.tsx:826
#: src/forms/StockForms.tsx:865
#: src/forms/StockForms.tsx:901
#: src/forms/StockForms.tsx:939
#: src/forms/StockForms.tsx:981
#: src/forms/StockForms.tsx:1029
#: src/forms/StockForms.tsx:1073
#: src/tables/build/BuildLineTable.tsx:94
msgid "Batch"
msgstr "Lotto"

#: src/components/settings/ConfigValueList.tsx:33
#~ msgid "<0>{0}</0> is set via {1} and was last set {2}"
#~ msgstr "<0>{0}</0> is set via {1} and was last set {2}"

#: src/components/settings/ConfigValueList.tsx:36
msgid "Setting"
msgstr "Impostazione"

#: src/components/settings/ConfigValueList.tsx:39
msgid "Source"
msgstr "Sorgente"

#: src/components/settings/SettingItem.tsx:47
#: src/components/settings/SettingItem.tsx:100
#~ msgid "{0} updated successfully"
#~ msgstr "{0} updated successfully"

#: src/components/settings/SettingList.tsx:78
msgid "Edit Setting"
msgstr "Modifica Impostazione"

#: src/components/settings/SettingList.tsx:91
msgid "Setting {key} updated successfully"
msgstr "Impostazione {key} aggiornata correttamente"

#: src/components/settings/SettingList.tsx:120
msgid "Setting updated"
msgstr "Impostazione aggiornata"

#. placeholder {0}: setting.key
#: src/components/settings/SettingList.tsx:121
msgid "Setting {0} updated successfully"
msgstr "Impostazione {0} aggiornata correttamente"

#: src/components/settings/SettingList.tsx:130
msgid "Error editing setting"
msgstr "Errore nella modifica dell'impostazione"

#: src/components/settings/SettingList.tsx:146
msgid "Error loading settings"
msgstr "Errore nel caricamento delle impostazioni"

#: src/components/settings/SettingList.tsx:187
msgid "No settings specified"
msgstr "Nessuna impostazione specificata"

#: src/components/tables/FilterGroup.tsx:29
#~ msgid "Add table filter"
#~ msgstr "Add table filter"

#: src/components/tables/FilterGroup.tsx:44
#~ msgid "Clear all filters"
#~ msgstr "Clear all filters"

#: src/components/tables/FilterGroup.tsx:51
#~ msgid "Add filter"
#~ msgstr "Add filter"

#: src/components/tables/FilterSelectModal.tsx:143
#~ msgid "Add Table Filter"
#~ msgstr "Add Table Filter"

#: src/components/tables/FilterSelectModal.tsx:145
#~ msgid "Select from the available filters"
#~ msgstr "Select from the available filters"

#: src/components/tables/bom/BomTable.tsx:200
#~ msgid "Validate"
#~ msgstr "Validate"

#: src/components/tables/bom/BomTable.tsx:250
#~ msgid "Has Available Stock"
#~ msgstr "Has Available Stock"

#: src/components/tables/bom/UsedInTable.tsx:40
#~ msgid "Required Part"
#~ msgstr "Required Part"

#: src/components/tables/build/BuildOrderTable.tsx:52
#~ msgid "Progress"
#~ msgstr "Progress"

#: src/components/tables/build/BuildOrderTable.tsx:65
#~ msgid "Priority"
#~ msgstr "Priority"

#: src/components/tables/company/AddressTable.tsx:68
#~ msgid "Postal Code"
#~ msgstr "Postal Code"

#: src/components/tables/company/AddressTable.tsx:74
#~ msgid "City"
#~ msgstr "City"

#: src/components/tables/company/AddressTable.tsx:80
#~ msgid "State / Province"
#~ msgstr "State / Province"

#: src/components/tables/company/AddressTable.tsx:86
#~ msgid "Country"
#~ msgstr "Country"

#: src/components/tables/company/AddressTable.tsx:92
#~ msgid "Courier Notes"
#~ msgstr "Courier Notes"

#: src/components/tables/company/AddressTable.tsx:98
#~ msgid "Internal Notes"
#~ msgstr "Internal Notes"

#: src/components/tables/company/AddressTable.tsx:130
#~ msgid "Address updated"
#~ msgstr "Address updated"

#: src/components/tables/company/AddressTable.tsx:142
#~ msgid "Address deleted"
#~ msgstr "Address deleted"

#: src/components/tables/company/CompanyTable.tsx:32
#~ msgid "Company Name"
#~ msgstr "Company Name"

#: src/components/tables/company/ContactTable.tsx:41
#~ msgid "Phone"
#~ msgstr "Phone"

#: src/components/tables/company/ContactTable.tsx:78
#~ msgid "Contact updated"
#~ msgstr "Contact updated"

#: src/components/tables/company/ContactTable.tsx:90
#~ msgid "Contact deleted"
#~ msgstr "Contact deleted"

#: src/components/tables/company/ContactTable.tsx:92
#~ msgid "Are you sure you want to delete this contact?"
#~ msgstr "Are you sure you want to delete this contact?"

#: src/components/tables/company/ContactTable.tsx:108
#~ msgid "Create Contact"
#~ msgstr "Create Contact"

#: src/components/tables/company/ContactTable.tsx:110
#~ msgid "Contact created"
#~ msgstr "Contact created"

#: src/components/tables/general/AttachmentTable.tsx:47
#~ msgid "Comment"
#~ msgstr "Comment"

#: src/components/tables/part/PartCategoryTable.tsx:122
#~ msgid "Part category updated"
#~ msgstr "Part category updated"

#: src/components/tables/part/PartParameterTable.tsx:41
#~ msgid "Parameter"
#~ msgstr "Parameter"

#: src/components/tables/part/PartParameterTable.tsx:114
#~ msgid "Part parameter updated"
#~ msgstr "Part parameter updated"

#: src/components/tables/part/PartParameterTable.tsx:130
#~ msgid "Part parameter deleted"
#~ msgstr "Part parameter deleted"

#: src/components/tables/part/PartParameterTable.tsx:132
#~ msgid "Are you sure you want to remove this parameter?"
#~ msgstr "Are you sure you want to remove this parameter?"

#: src/components/tables/part/PartParameterTable.tsx:159
#~ msgid "Part parameter added"
#~ msgstr "Part parameter added"

#: src/components/tables/part/PartParameterTemplateTable.tsx:67
#~ msgid "Choices"
#~ msgstr "Choices"

#: src/components/tables/part/PartParameterTemplateTable.tsx:83
#~ msgid "Remove parameter template"
#~ msgstr "Remove parameter template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:84
#~ msgid "Parameter template updated"
#~ msgstr "Parameter template updated"

#: src/components/tables/part/PartParameterTemplateTable.tsx:96
#~ msgid "Parameter template deleted"
#~ msgstr "Parameter template deleted"

#: src/components/tables/part/PartParameterTemplateTable.tsx:98
#~ msgid "Are you sure you want to remove this parameter template?"
#~ msgstr "Are you sure you want to remove this parameter template?"

#: src/components/tables/part/PartParameterTemplateTable.tsx:110
#~ msgid "Create Parameter Template"
#~ msgstr "Create Parameter Template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:112
#~ msgid "Parameter template created"
#~ msgstr "Parameter template created"

#: src/components/tables/part/PartTable.tsx:211
#~ msgid "Detail"
#~ msgstr "Detail"

#: src/components/tables/part/PartTestTemplateTable.tsx:30
#~ msgid "Test Name"
#~ msgstr "Test Name"

#: src/components/tables/part/PartTestTemplateTable.tsx:86
#~ msgid "Template updated"
#~ msgstr "Template updated"

#: src/components/tables/part/PartTestTemplateTable.tsx:98
#~ msgid "Test Template deleted"
#~ msgstr "Test Template deleted"

#: src/components/tables/part/PartTestTemplateTable.tsx:115
#~ msgid "Create Test Template"
#~ msgstr "Create Test Template"

#: src/components/tables/part/PartTestTemplateTable.tsx:117
#~ msgid "Template created"
#~ msgstr "Template created"

#: src/components/tables/part/RelatedPartTable.tsx:79
#~ msgid "Related Part"
#~ msgstr "Related Part"

#: src/components/tables/part/RelatedPartTable.tsx:82
#~ msgid "Related part added"
#~ msgstr "Related part added"

#: src/components/tables/part/RelatedPartTable.tsx:114
#~ msgid "Related part deleted"
#~ msgstr "Related part deleted"

#: src/components/tables/part/RelatedPartTable.tsx:115
#~ msgid "Are you sure you want to remove this relationship?"
#~ msgstr "Are you sure you want to remove this relationship?"

#: src/components/tables/plugin/PluginListTable.tsx:191
#~ msgid "Installation path"
#~ msgstr "Installation path"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:55
#~ msgid "Receive"
#~ msgstr "Receive"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:81
#~ msgid "Line item updated"
#~ msgstr "Line item updated"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:232
#~ msgid "Line item added"
#~ msgstr "Line item added"

#: src/components/tables/settings/CustomUnitsTable.tsx:37
#~ msgid "Definition"
#~ msgstr "Definition"

#: src/components/tables/settings/CustomUnitsTable.tsx:43
#~ msgid "Symbol"
#~ msgstr "Symbol"

#: src/components/tables/settings/CustomUnitsTable.tsx:59
#~ msgid "Edit custom unit"
#~ msgstr "Edit custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:66
#~ msgid "Custom unit updated"
#~ msgstr "Custom unit updated"

#: src/components/tables/settings/CustomUnitsTable.tsx:76
#~ msgid "Delete custom unit"
#~ msgstr "Delete custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:77
#~ msgid "Custom unit deleted"
#~ msgstr "Custom unit deleted"

#: src/components/tables/settings/CustomUnitsTable.tsx:79
#~ msgid "Are you sure you want to remove this custom unit?"
#~ msgstr "Are you sure you want to remove this custom unit?"

#: src/components/tables/settings/CustomUnitsTable.tsx:97
#~ msgid "Custom unit created"
#~ msgstr "Custom unit created"

#: src/components/tables/settings/GroupTable.tsx:45
#~ msgid "Group updated"
#~ msgstr "Group updated"

#: src/components/tables/settings/GroupTable.tsx:131
#~ msgid "Added group"
#~ msgstr "Added group"

#: src/components/tables/settings/ProjectCodeTable.tsx:49
#~ msgid "Edit project code"
#~ msgstr "Edit project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:56
#~ msgid "Project code updated"
#~ msgstr "Project code updated"

#: src/components/tables/settings/ProjectCodeTable.tsx:66
#~ msgid "Delete project code"
#~ msgstr "Delete project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:67
#~ msgid "Project code deleted"
#~ msgstr "Project code deleted"

#: src/components/tables/settings/ProjectCodeTable.tsx:69
#~ msgid "Are you sure you want to remove this project code?"
#~ msgstr "Are you sure you want to remove this project code?"

#: src/components/tables/settings/ProjectCodeTable.tsx:88
#~ msgid "Added project code"
#~ msgstr "Added project code"

#: src/components/tables/settings/UserDrawer.tsx:92
#~ msgid "User permission changed successfully"
#~ msgstr "User permission changed successfully"

#: src/components/tables/settings/UserDrawer.tsx:93
#~ msgid "Some changes might only take effect after the user refreshes their login."
#~ msgstr "Some changes might only take effect after the user refreshes their login."

#: src/components/tables/settings/UserDrawer.tsx:118
#~ msgid "Changed user active status successfully"
#~ msgstr "Changed user active status successfully"

#: src/components/tables/settings/UserDrawer.tsx:119
#~ msgid "Set to {active}"
#~ msgstr "Set to {active}"

#: src/components/tables/settings/UserDrawer.tsx:142
#~ msgid "User details for {0}"
#~ msgstr "User details for {0}"

#: src/components/tables/settings/UserDrawer.tsx:176
#~ msgid "Rights"
#~ msgstr "Rights"

#: src/components/tables/settings/UserTable.tsx:117
#~ msgid "user deleted"
#~ msgstr "user deleted"

#: src/components/tables/stock/StockItemTable.tsx:247
#~ msgid "Test Filter"
#~ msgstr "Test Filter"

#: src/components/tables/stock/StockItemTable.tsx:248
#~ msgid "This is a test filter"
#~ msgstr "This is a test filter"

#: src/components/tables/stock/StockLocationTable.tsx:145
#~ msgid "Stock location updated"
#~ msgstr "Stock location updated"

#: src/components/widgets/FeedbackWidget.tsx:19
#~ msgid "Something is new: Platform UI"
#~ msgstr "Something is new: Platform UI"

#: src/components/widgets/FeedbackWidget.tsx:21
#~ msgid "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."
#~ msgstr "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."

#: src/components/widgets/FeedbackWidget.tsx:32
#~ msgid "Provide Feedback"
#~ msgstr "Provide Feedback"

#: src/components/widgets/GetStartedWidget.tsx:11
#~ msgid "Getting started"
#~ msgstr "Getting started"

#: src/components/widgets/MarkdownEditor.tsx:108
#~ msgid "Failed to upload image"
#~ msgstr "Failed to upload image"

#: src/components/widgets/MarkdownEditor.tsx:146
#~ msgid "Notes saved"
#~ msgstr "Notes saved"

#: src/components/widgets/WidgetLayout.tsx:166
#~ msgid "Layout"
#~ msgstr "Layout"

#: src/components/widgets/WidgetLayout.tsx:172
#~ msgid "Reset Layout"
#~ msgstr "Reset Layout"

#: src/components/widgets/WidgetLayout.tsx:185
#~ msgid "Stop Edit"
#~ msgstr "Stop Edit"

#: src/components/widgets/WidgetLayout.tsx:191
#~ msgid "Appearance"
#~ msgstr "Appearance"

#: src/components/widgets/WidgetLayout.tsx:203
#~ msgid "Show Boxes"
#~ msgstr "Show Boxes"

#: src/components/wizards/OrderPartsWizard.tsx:61
msgid "New Purchase Order"
msgstr "Nuovo ordine d'acquisto"

#: src/components/wizards/OrderPartsWizard.tsx:63
msgid "Purchase order created"
msgstr "Ordine d'acquisto creato"

#: src/components/wizards/OrderPartsWizard.tsx:75
msgid "New Supplier Part"
msgstr "Nuovo articolo fornitore"

#: src/components/wizards/OrderPartsWizard.tsx:77
#: src/tables/purchasing/SupplierPartTable.tsx:161
msgid "Supplier part created"
msgstr "Articolo fornitore creato"

#: src/components/wizards/OrderPartsWizard.tsx:103
msgid "Add to Purchase Order"
msgstr "Aggiungi all'ordine d'acquisto"

#: src/components/wizards/OrderPartsWizard.tsx:115
msgid "Part added to purchase order"
msgstr "Articolo aggiunto all'ordine di acquisto"

#: src/components/wizards/OrderPartsWizard.tsx:156
msgid "Select supplier part"
msgstr "Seleziona l'articolo del fornitore"

#: src/components/wizards/OrderPartsWizard.tsx:171
msgid "New supplier part"
msgstr "Nuovo articolo fornitore"

#: src/components/wizards/OrderPartsWizard.tsx:195
msgid "Select purchase order"
msgstr "Seleziona ordine d'acquisto"

#: src/components/wizards/OrderPartsWizard.tsx:209
msgid "New purchase order"
msgstr "Nuovo ordine d'acquisto"

#: src/components/wizards/OrderPartsWizard.tsx:257
msgid "Add to selected purchase order"
msgstr "Aggiungi all'ordine di acquisto selezionato"

#: src/components/wizards/OrderPartsWizard.tsx:269
#: src/components/wizards/OrderPartsWizard.tsx:382
msgid "No parts selected"
msgstr "Nessun articolo selezionato"

#: src/components/wizards/OrderPartsWizard.tsx:270
msgid "No purchaseable parts selected"
msgstr "Nessun articolo acquistabile selezionato"

#: src/components/wizards/OrderPartsWizard.tsx:306
msgid "Parts Added"
msgstr "Articolo aggiunto"

#: src/components/wizards/OrderPartsWizard.tsx:307
msgid "All selected parts added to a purchase order"
msgstr "Tutte le parti selezionate aggiunte all'ordine di acquisto"

#: src/components/wizards/OrderPartsWizard.tsx:383
msgid "You must select at least one part to order"
msgstr "Devi selezionare almeno un articolo da ordinare"

#: src/components/wizards/OrderPartsWizard.tsx:394
msgid "Supplier part is required"
msgstr "L'articolo fornitore è richiesto"

#: src/components/wizards/OrderPartsWizard.tsx:398
msgid "Quantity is required"
msgstr "La quantità è richiesta"

#: src/components/wizards/OrderPartsWizard.tsx:411
msgid "Invalid part selection"
msgstr "Selezione articolo invalida"

#: src/components/wizards/OrderPartsWizard.tsx:413
msgid "Please correct the errors in the selected parts"
msgstr "Si prega di correggere gli errori negli articoli selezionati"

#: src/components/wizards/OrderPartsWizard.tsx:424
#: src/tables/build/BuildLineTable.tsx:794
#: src/tables/part/PartTable.tsx:407
#: src/tables/sales/SalesOrderLineItemTable.tsx:347
msgid "Order Parts"
msgstr "Ordine Articoli"

#: src/contexts/LanguageContext.tsx:22
msgid "Arabic"
msgstr "Arabo"

#: src/contexts/LanguageContext.tsx:23
msgid "Bulgarian"
msgstr "Bulgaro"

#: src/contexts/LanguageContext.tsx:24
msgid "Czech"
msgstr "Ceco"

#: src/contexts/LanguageContext.tsx:25
msgid "Danish"
msgstr "Danese"

#: src/contexts/LanguageContext.tsx:26
msgid "German"
msgstr "Tedesco"

#: src/contexts/LanguageContext.tsx:27
msgid "Greek"
msgstr "Greco"

#: src/contexts/LanguageContext.tsx:28
msgid "English"
msgstr "Inglese"

#: src/contexts/LanguageContext.tsx:29
msgid "Spanish"
msgstr "Spagnolo"

#: src/contexts/LanguageContext.tsx:30
msgid "Spanish (Mexican)"
msgstr "Spagnolo (Messicano)"

#: src/contexts/LanguageContext.tsx:31
msgid "Estonian"
msgstr "Estone"

#: src/contexts/LanguageContext.tsx:32
msgid "Farsi / Persian"
msgstr "Farsi / Persiano"

#: src/contexts/LanguageContext.tsx:33
msgid "Finnish"
msgstr "Finlandese"

#: src/contexts/LanguageContext.tsx:34
msgid "French"
msgstr "Francese"

#: src/contexts/LanguageContext.tsx:35
msgid "Hebrew"
msgstr "Ebraico"

#: src/contexts/LanguageContext.tsx:36
msgid "Hindi"
msgstr "Hindi"

#: src/contexts/LanguageContext.tsx:37
msgid "Hungarian"
msgstr "Ungherese"

#: src/contexts/LanguageContext.tsx:38
msgid "Italian"
msgstr "Italiano"

#: src/contexts/LanguageContext.tsx:39
msgid "Japanese"
msgstr "Giapponese"

#: src/contexts/LanguageContext.tsx:40
msgid "Korean"
msgstr "Coreano"

#: src/contexts/LanguageContext.tsx:41
msgid "Lithuanian"
msgstr "Lituano"

#: src/contexts/LanguageContext.tsx:42
msgid "Latvian"
msgstr "Lettone"

#: src/contexts/LanguageContext.tsx:43
msgid "Dutch"
msgstr "Olandese"

#: src/contexts/LanguageContext.tsx:44
msgid "Norwegian"
msgstr "Norvegese"

#: src/contexts/LanguageContext.tsx:45
msgid "Polish"
msgstr "Polacco"

#: src/contexts/LanguageContext.tsx:46
msgid "Portuguese"
msgstr "Portoghese"

#: src/contexts/LanguageContext.tsx:47
msgid "Portuguese (Brazilian)"
msgstr "Portoghese (Brasiliano)"

#: src/contexts/LanguageContext.tsx:48
msgid "Romanian"
msgstr "Rumeno"

#: src/contexts/LanguageContext.tsx:49
msgid "Russian"
msgstr "Russo"

#: src/contexts/LanguageContext.tsx:50
msgid "Slovak"
msgstr "Slovacco"

#: src/contexts/LanguageContext.tsx:51
msgid "Slovenian"
msgstr "Sloveno"

#: src/contexts/LanguageContext.tsx:52
msgid "Serbian"
msgstr "Serbo"

#: src/contexts/LanguageContext.tsx:53
msgid "Swedish"
msgstr "Svedese"

#: src/contexts/LanguageContext.tsx:54
msgid "Thai"
msgstr "Thailandese"

#: src/contexts/LanguageContext.tsx:55
msgid "Turkish"
msgstr "Turco"

#: src/contexts/LanguageContext.tsx:56
msgid "Ukrainian"
msgstr "Ucraino"

#: src/contexts/LanguageContext.tsx:57
msgid "Vietnamese"
msgstr "Vietnamese"

#: src/contexts/LanguageContext.tsx:58
msgid "Chinese (Simplified)"
msgstr "Cinese (Semplificato)"

#: src/contexts/LanguageContext.tsx:59
msgid "Chinese (Traditional)"
msgstr "Cinese (Tradizionale)"

#: src/defaults/actions.tsx:18
#: src/defaults/links.tsx:27
#: src/defaults/menuItems.tsx:9
#~ msgid "Home"
#~ msgstr "Home"

#: src/defaults/actions.tsx:29
msgid "Go to the InvenTree dashboard"
msgstr "Vai alla bacheca InvenTree"

#: src/defaults/actions.tsx:36
msgid "Visit the documentation to learn more about InvenTree"
msgstr "Visita la documentazione per saperne di più su InvenTree"

#: src/defaults/actions.tsx:41
#: src/defaults/links.tsx:118
#~ msgid "About this Inventree instance"
#~ msgstr "About this Inventree instance"

#: src/defaults/actions.tsx:44
#: src/defaults/links.tsx:140
#: src/defaults/links.tsx:186
msgid "About InvenTree"
msgstr "Informazioni su InvenTree"

#: src/defaults/actions.tsx:45
msgid "About the InvenTree org"
msgstr "Informazioni su InvenTree org"

#: src/defaults/actions.tsx:51
msgid "Server Information"
msgstr "Informazioni sul Server"

#: src/defaults/actions.tsx:52
#: src/defaults/links.tsx:169
msgid "About this InvenTree instance"
msgstr "Informazioni su questa istanza di Inventree"

#: src/defaults/actions.tsx:58
#: src/defaults/links.tsx:153
#: src/defaults/links.tsx:175
msgid "License Information"
msgstr "Informazioni sulla licenza"

#: src/defaults/actions.tsx:59
msgid "Licenses for dependencies of the service"
msgstr "Licenze per dipendenze del servizio"

#: src/defaults/actions.tsx:65
msgid "Open Navigation"
msgstr "Apri navigazione"

#: src/defaults/actions.tsx:66
msgid "Open the main navigation menu"
msgstr "Apri il menu di navigazione principale"

#: src/defaults/actions.tsx:73
msgid "Scan a barcode or QR code"
msgstr "Scansiona un codice a barre o un codice QR"

#: src/defaults/actions.tsx:84
msgid "Go to the Admin Center"
msgstr "Vai al centro di amministrazione"

#: src/defaults/dashboardItems.tsx:29
#~ msgid "Latest Parts"
#~ msgstr "Latest Parts"

#: src/defaults/dashboardItems.tsx:36
#~ msgid "BOM Waiting Validation"
#~ msgstr "BOM Waiting Validation"

#: src/defaults/dashboardItems.tsx:43
#~ msgid "Recently Updated"
#~ msgstr "Recently Updated"

#: src/defaults/dashboardItems.tsx:57
#~ msgid "Depleted Stock"
#~ msgstr "Depleted Stock"

#: src/defaults/dashboardItems.tsx:71
#~ msgid "Expired Stock"
#~ msgstr "Expired Stock"

#: src/defaults/dashboardItems.tsx:78
#~ msgid "Stale Stock"
#~ msgstr "Stale Stock"

#: src/defaults/dashboardItems.tsx:85
#~ msgid "Build Orders In Progress"
#~ msgstr "Build Orders In Progress"

#: src/defaults/dashboardItems.tsx:99
#~ msgid "Outstanding Purchase Orders"
#~ msgstr "Outstanding Purchase Orders"

#: src/defaults/dashboardItems.tsx:113
#~ msgid "Outstanding Sales Orders"
#~ msgstr "Outstanding Sales Orders"

#: src/defaults/dashboardItems.tsx:127
#~ msgid "Current News"
#~ msgstr "Current News"

#: src/defaults/defaultHostList.tsx:8
#~ msgid "InvenTree Demo"
#~ msgstr "InvenTree Demo"

#: src/defaults/defaultHostList.tsx:16
#~ msgid "Local Server"
#~ msgstr "Local Server"

#: src/defaults/links.tsx:17
#~ msgid "GitHub"
#~ msgstr "GitHub"

#: src/defaults/links.tsx:22
#~ msgid "Demo"
#~ msgstr "Demo"

#: src/defaults/links.tsx:41
#: src/defaults/menuItems.tsx:71
#: src/pages/Index/Playground.tsx:217
#~ msgid "Playground"
#~ msgstr "Playground"

#: src/defaults/links.tsx:76
#~ msgid "Instance"
#~ msgstr "Instance"

#: src/defaults/links.tsx:83
#~ msgid "InvenTree"
#~ msgstr "InvenTree"

#: src/defaults/links.tsx:93
msgid "API"
msgstr "API"

#: src/defaults/links.tsx:96
msgid "InvenTree API documentation"
msgstr "Documentazione API di InvenTree"

#: src/defaults/links.tsx:100
msgid "Developer Manual"
msgstr "Manuale Dello Sviluppatore"

#: src/defaults/links.tsx:103
msgid "InvenTree developer manual"
msgstr "Manuale dello sviluppatore di InvenTree"

#: src/defaults/links.tsx:107
msgid "FAQ"
msgstr "FAQ"

#: src/defaults/links.tsx:110
msgid "Frequently asked questions"
msgstr "Domande frequenti"

#: src/defaults/links.tsx:114
msgid "GitHub Repository"
msgstr "Repository GitHub"

#: src/defaults/links.tsx:117
msgid "InvenTree source code on GitHub"
msgstr "Codice sorgente InvenTree su GitHub"

#: src/defaults/links.tsx:117
#~ msgid "Licenses for packages used by InvenTree"
#~ msgstr "Licenses for packages used by InvenTree"

#: src/defaults/links.tsx:127
#: src/defaults/links.tsx:168
msgid "System Information"
msgstr "Informazioni Sistema"

#: src/defaults/links.tsx:134
#~ msgid "Licenses"
#~ msgstr "Licenses"

#: src/defaults/links.tsx:176
msgid "Licenses for dependencies of the InvenTree software"
msgstr "Licenze per dipendenze del software InvenTree"

#: src/defaults/links.tsx:187
msgid "About the InvenTree Project"
msgstr "A proposito del progetto InvenTree"

#: src/defaults/menuItems.tsx:7
#~ msgid "Open sourcea"
#~ msgstr "Open sourcea"

#: src/defaults/menuItems.tsx:9
#~ msgid "Open source"
#~ msgstr "Open source"

#: src/defaults/menuItems.tsx:10
#~ msgid "Start page of your instance."
#~ msgstr "Start page of your instance."

#: src/defaults/menuItems.tsx:10
#~ msgid "This Pokémon’s cry is very loud and distracting"
#~ msgstr "This Pokémon’s cry is very loud and distracting"

#: src/defaults/menuItems.tsx:12
#~ msgid "This Pokémon’s cry is very loud and distracting and more and more and more"
#~ msgstr "This Pokémon’s cry is very loud and distracting and more and more and more"

#: src/defaults/menuItems.tsx:15
#~ msgid "Profile page"
#~ msgstr "Profile page"

#: src/defaults/menuItems.tsx:17
#~ msgid "User attributes and design settings."
#~ msgstr "User attributes and design settings."

#: src/defaults/menuItems.tsx:21
#~ msgid "Free for everyone"
#~ msgstr "Free for everyone"

#: src/defaults/menuItems.tsx:22
#~ msgid "The fluid of Smeargle’s tail secretions changes"
#~ msgstr "The fluid of Smeargle’s tail secretions changes"

#: src/defaults/menuItems.tsx:23
#~ msgid "View for interactive scanning and multiple actions."
#~ msgstr "View for interactive scanning and multiple actions."

#: src/defaults/menuItems.tsx:24
#~ msgid "The fluid of Smeargle’s tail secretions changes in the intensity"
#~ msgstr "The fluid of Smeargle’s tail secretions changes in the intensity"

#: src/defaults/menuItems.tsx:32
#~ msgid "abc"
#~ msgstr "abc"

#: src/defaults/menuItems.tsx:37
#~ msgid "Random image"
#~ msgstr "Random image"

#: src/defaults/menuItems.tsx:40
#~ msgid "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"
#~ msgstr "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"

#: src/defaults/menuItems.tsx:105
#~ msgid "Yanma is capable of seeing 360 degrees without"
#~ msgstr "Yanma is capable of seeing 360 degrees without"

#: src/defaults/menuItems.tsx:111
#~ msgid "The shell’s rounded shape and the grooves on its."
#~ msgstr "The shell’s rounded shape and the grooves on its."

#: src/defaults/menuItems.tsx:116
#~ msgid "Analytics"
#~ msgstr "Analytics"

#: src/defaults/menuItems.tsx:118
#~ msgid "This Pokémon uses its flying ability to quickly chase"
#~ msgstr "This Pokémon uses its flying ability to quickly chase"

#: src/defaults/menuItems.tsx:125
#~ msgid "Combusken battles with the intensely hot flames it spews"
#~ msgstr "Combusken battles with the intensely hot flames it spews"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add File"
#~ msgstr "Add File"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add Link"
#~ msgstr "Add Link"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "File added"
#~ msgstr "File added"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "Link added"
#~ msgstr "Link added"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit File"
#~ msgstr "Edit File"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit Link"
#~ msgstr "Edit Link"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "File updated"
#~ msgstr "File updated"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "Link updated"
#~ msgstr "Link updated"

#: src/forms/AttachmentForms.tsx:125
#~ msgid "Attachment deleted"
#~ msgstr "Attachment deleted"

#: src/forms/AttachmentForms.tsx:128
#~ msgid "Are you sure you want to delete this attachment?"
#~ msgstr "Are you sure you want to delete this attachment?"

#: src/forms/BomForms.tsx:109
msgid "Substitute Part"
msgstr "Articolo Sostituivo"

#: src/forms/BomForms.tsx:126
msgid "Edit BOM Substitutes"
msgstr "Modifica Sostitutivi della Distinta Base"

#: src/forms/BomForms.tsx:133
msgid "Add Substitute"
msgstr "Aggiungi Sostitutivo"

#: src/forms/BomForms.tsx:134
msgid "Substitute added"
msgstr "Sostitutivo aggiunto"

#: src/forms/BuildForms.tsx:112
#: src/forms/BuildForms.tsx:217
#: src/forms/StockForms.tsx:197
msgid "Next batch code"
msgstr "Prossimo codice lotto"

#: src/forms/BuildForms.tsx:212
#: src/forms/StockForms.tsx:183
#: src/forms/StockForms.tsx:188
#: src/forms/StockForms.tsx:359
#: src/pages/stock/StockDetail.tsx:231
msgid "Next serial number"
msgstr "Prossimo Numero Di Serie"

#: src/forms/BuildForms.tsx:248
#~ msgid "Remove output"
#~ msgstr "Remove output"

#: src/forms/BuildForms.tsx:311
#: src/forms/BuildForms.tsx:656
#: src/tables/build/BuildAllocatedStockTable.tsx:150
#: src/tables/build/BuildOrderTestTable.tsx:230
#: src/tables/build/BuildOrderTestTable.tsx:254
#: src/tables/build/BuildOutputTable.tsx:592
msgid "Build Output"
msgstr "Output produzione"

#: src/forms/BuildForms.tsx:313
#: src/forms/BuildForms.tsx:387
#: src/forms/BuildForms.tsx:451
#: src/forms/PurchaseOrderForms.tsx:714
#: src/forms/ReturnOrderForms.tsx:194
#: src/forms/ReturnOrderForms.tsx:241
#: src/forms/StockForms.tsx:656
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:87
#: src/pages/build/BuildDetail.tsx:205
#: src/pages/core/UserDetail.tsx:151
#: src/pages/purchasing/PurchaseOrderDetail.tsx:150
#: src/pages/sales/ReturnOrderDetail.tsx:115
#: src/pages/sales/SalesOrderDetail.tsx:124
#: src/pages/stock/StockDetail.tsx:162
#: src/tables/Filter.tsx:266
#: src/tables/build/BuildOutputTable.tsx:408
#: src/tables/machine/MachineListTable.tsx:339
#: src/tables/part/PartPurchaseOrdersTable.tsx:38
#: src/tables/sales/ReturnOrderLineItemTable.tsx:135
#: src/tables/sales/ReturnOrderLineItemTable.tsx:172
#: src/tables/settings/CustomStateTable.tsx:79
#: src/tables/settings/EmailTable.tsx:73
#: src/tables/settings/ImportSessionTable.tsx:117
#: src/tables/stock/StockItemTable.tsx:328
#: src/tables/stock/StockTrackingTable.tsx:65
msgid "Status"
msgstr "Stato"

#: src/forms/BuildForms.tsx:335
msgid "Complete Build Outputs"
msgstr "Completa gli output di produzione"

#: src/forms/BuildForms.tsx:338
msgid "Build outputs have been completed"
msgstr "Gli ordini di produzione sono stati completati"

#: src/forms/BuildForms.tsx:405
#: src/forms/BuildForms.tsx:407
msgid "Scrap Build Outputs"
msgstr "Rimuovi gli output di produzione"

#: src/forms/BuildForms.tsx:408
#~ msgid "Selected build outputs will be deleted"
#~ msgstr "Selected build outputs will be deleted"

#: src/forms/BuildForms.tsx:410
msgid "Selected build outputs will be completed, but marked as scrapped"
msgstr "Gli ordini di produzione selezionati saranno completati, ma contrassegnati come scartati"

#: src/forms/BuildForms.tsx:412
msgid "Allocated stock items will be consumed"
msgstr "Gli articoli di magazzino assegnati verranno consumati"

#: src/forms/BuildForms.tsx:418
msgid "Build outputs have been scrapped"
msgstr "Gli output di produzione sono stati rimossi"

#: src/forms/BuildForms.tsx:461
#: src/forms/BuildForms.tsx:463
msgid "Cancel Build Outputs"
msgstr "Cancella gli output di produzione"

#: src/forms/BuildForms.tsx:465
msgid "Selected build outputs will be removed"
msgstr "Gli ordini di produzione verranno eliminati"

#: src/forms/BuildForms.tsx:467
msgid "Allocated stock items will be returned to stock"
msgstr "Gli articoli di magazzino assegnati saranno restituiti alle scorte"

#: src/forms/BuildForms.tsx:470
#~ msgid "Remove line"
#~ msgstr "Remove line"

#: src/forms/BuildForms.tsx:474
msgid "Build outputs have been cancelled"
msgstr "Gli output di produzione sono stati cancellati"

#: src/forms/BuildForms.tsx:603
#: src/forms/BuildForms.tsx:760
#: src/forms/BuildForms.tsx:861
#: src/forms/SalesOrderForms.tsx:268
#: src/tables/build/BuildAllocatedStockTable.tsx:139
#: src/tables/build/BuildLineTable.tsx:180
#: src/tables/sales/SalesOrderLineItemTable.tsx:319
#: src/tables/stock/StockItemTable.tsx:339
msgid "Allocated"
msgstr "Allocato"

#: src/forms/BuildForms.tsx:638
#: src/forms/SalesOrderForms.tsx:257
#: src/pages/build/BuildDetail.tsx:320
msgid "Source Location"
msgstr "Posizione sorgente"

#: src/forms/BuildForms.tsx:639
#: src/forms/SalesOrderForms.tsx:258
msgid "Select the source location for the stock allocation"
msgstr "Selezionare la posizione di origine per l'assegnazione dello stock"

#: src/forms/BuildForms.tsx:671
#: src/forms/SalesOrderForms.tsx:298
#: src/tables/build/BuildLineTable.tsx:555
#: src/tables/build/BuildLineTable.tsx:710
#: src/tables/build/BuildLineTable.tsx:809
#: src/tables/sales/SalesOrderLineItemTable.tsx:357
#: src/tables/sales/SalesOrderLineItemTable.tsx:388
msgid "Allocate Stock"
msgstr "Assegna Scorte"

#: src/forms/BuildForms.tsx:674
#: src/forms/SalesOrderForms.tsx:303
msgid "Stock items allocated"
msgstr "Articoli di stock assegnati"

#: src/forms/BuildForms.tsx:780
#: src/forms/BuildForms.tsx:881
#: src/tables/build/BuildAllocatedStockTable.tsx:233
#: src/tables/build/BuildAllocatedStockTable.tsx:265
#: src/tables/build/BuildLineTable.tsx:720
#: src/tables/build/BuildLineTable.tsx:843
msgid "Consume Stock"
msgstr ""

#: src/forms/BuildForms.tsx:781
#: src/forms/BuildForms.tsx:882
msgid "Stock items consumed"
msgstr ""

#: src/forms/BuildForms.tsx:817
#: src/tables/build/BuildLineTable.tsx:495
#: src/tables/part/PartBuildAllocationsTable.tsx:101
msgid "Fully consumed"
msgstr ""

#: src/forms/BuildForms.tsx:862
#: src/tables/build/BuildLineTable.tsx:185
#: src/tables/stock/StockItemTable.tsx:372
msgid "Consumed"
msgstr "Utilizzato"

#: src/forms/CompanyForms.tsx:150
#~ msgid "Company updated"
#~ msgstr "Company updated"

#: src/forms/PartForms.tsx:70
#: src/forms/PartForms.tsx:157
#: src/pages/part/CategoryDetail.tsx:122
#: src/pages/part/PartDetail.tsx:668
#: src/tables/part/PartCategoryTable.tsx:94
#: src/tables/part/PartTable.tsx:312
msgid "Subscribed"
msgstr "Sottoscritto"

#: src/forms/PartForms.tsx:71
msgid "Subscribe to notifications for this part"
msgstr "Sottoscrivi le notifiche per questo articolo"

#: src/forms/PartForms.tsx:106
#~ msgid "Create Part"
#~ msgstr "Create Part"

#: src/forms/PartForms.tsx:108
#~ msgid "Part created"
#~ msgstr "Part created"

#: src/forms/PartForms.tsx:129
#~ msgid "Part updated"
#~ msgstr "Part updated"

#: src/forms/PartForms.tsx:143
msgid "Parent part category"
msgstr "Categoria articolo principale"

#: src/forms/PartForms.tsx:158
msgid "Subscribe to notifications for this category"
msgstr "Sottoscrivi notifiche per questa categoria"

#: src/forms/PurchaseOrderForms.tsx:385
msgid "Assign Batch Code and Serial Numbers"
msgstr "Assegna codice lotto e numeri di serie"

#: src/forms/PurchaseOrderForms.tsx:387
msgid "Assign Batch Code"
msgstr "Assegna Codice Lotto"

#: src/forms/PurchaseOrderForms.tsx:407
msgid "Choose Location"
msgstr "Scegliere la posizione"

#: src/forms/PurchaseOrderForms.tsx:415
msgid "Item Destination selected"
msgstr "Destinazione oggetto selezionata"

#: src/forms/PurchaseOrderForms.tsx:421
#~ msgid "Assign Batch Code{0}"
#~ msgstr "Assign Batch Code{0}"

#: src/forms/PurchaseOrderForms.tsx:425
msgid "Part category default location selected"
msgstr "Posizione predefinita della categoria parte selezionata"

#: src/forms/PurchaseOrderForms.tsx:435
msgid "Received stock location selected"
msgstr "Posizione stock ricevuto selezionata"

#: src/forms/PurchaseOrderForms.tsx:443
msgid "Default location selected"
msgstr "Posizione predefinita selezionata"

#: src/forms/PurchaseOrderForms.tsx:444
#: src/forms/StockForms.tsx:428
#~ msgid "Remove item from list"
#~ msgstr "Remove item from list"

#: src/forms/PurchaseOrderForms.tsx:504
msgid "Set Location"
msgstr "Imposta Posizione"

#: src/forms/PurchaseOrderForms.tsx:521
msgid "Set Expiry Date"
msgstr "Impostare una Data di Scadenza"

#: src/forms/PurchaseOrderForms.tsx:529
#: src/forms/StockForms.tsx:637
msgid "Adjust Packaging"
msgstr "Regola Imballaggio"

#: src/forms/PurchaseOrderForms.tsx:537
#: src/forms/StockForms.tsx:628
#: src/hooks/UseStockAdjustActions.tsx:148
msgid "Change Status"
msgstr "Modifica Stato"

#: src/forms/PurchaseOrderForms.tsx:543
msgid "Add Note"
msgstr "Aggiungi Nota"

#: src/forms/PurchaseOrderForms.tsx:566
#~ msgid "Serial numbers"
#~ msgstr "Serial numbers"

#: src/forms/PurchaseOrderForms.tsx:582
#~ msgid "Store at line item destination"
#~ msgstr "Store at line item destination"

#: src/forms/PurchaseOrderForms.tsx:607
msgid "Store at default location"
msgstr "Memorizza nella posizione predefinita"

#: src/forms/PurchaseOrderForms.tsx:622
msgid "Store at line item destination "
msgstr "Salva alla destinazione dell'articolo"

#: src/forms/PurchaseOrderForms.tsx:634
msgid "Store with already received stock"
msgstr "Memorizza con stock già ricevuto"

#: src/forms/PurchaseOrderForms.tsx:658
#: src/pages/build/BuildDetail.tsx:334
#: src/pages/stock/StockDetail.tsx:277
#: src/pages/stock/StockDetail.tsx:923
#: src/tables/Filter.tsx:83
#: src/tables/build/BuildAllocatedStockTable.tsx:128
#: src/tables/build/BuildOrderTestTable.tsx:242
#: src/tables/build/BuildOutputTable.tsx:116
#: src/tables/sales/SalesOrderAllocationTable.tsx:146
msgid "Batch Code"
msgstr "Codice Lotto"

#: src/forms/PurchaseOrderForms.tsx:658
#~ msgid "Receive line items"
#~ msgstr "Receive line items"

#: src/forms/PurchaseOrderForms.tsx:659
msgid "Enter batch code for received items"
msgstr "Inserisci il codice lotto per gli articoli ricevuti"

#: src/forms/PurchaseOrderForms.tsx:672
#: src/forms/StockForms.tsx:176
msgid "Serial Numbers"
msgstr "Numeri di serie"

#: src/forms/PurchaseOrderForms.tsx:673
msgid "Enter serial numbers for received items"
msgstr "Inserisci i numeri di serie per gli elementi ricevuti"

#: src/forms/PurchaseOrderForms.tsx:687
#: src/pages/stock/StockDetail.tsx:379
#: src/tables/stock/StockItemTable.tsx:295
msgid "Expiry Date"
msgstr "Data di scadenza"

#: src/forms/PurchaseOrderForms.tsx:688
msgid "Enter an expiry date for received items"
msgstr "Inserisci una data di scadenza per gli articoli ricevuti"

#: src/forms/PurchaseOrderForms.tsx:700
#: src/forms/StockForms.tsx:672
#: src/pages/company/SupplierPartDetail.tsx:172
#: src/pages/company/SupplierPartDetail.tsx:236
#: src/pages/stock/StockDetail.tsx:416
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:220
msgid "Packaging"
msgstr "Imballaggio"

#: src/forms/PurchaseOrderForms.tsx:724
#: src/pages/company/SupplierPartDetail.tsx:119
#: src/tables/ColumnRenderers.tsx:323
msgid "Note"
msgstr "Nota"

#: src/forms/PurchaseOrderForms.tsx:792
#: src/pages/company/SupplierPartDetail.tsx:137
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:49
msgid "SKU"
msgstr "SKU"

#: src/forms/PurchaseOrderForms.tsx:793
#: src/tables/part/PartPurchaseOrdersTable.tsx:127
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:206
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:272
#: src/tables/sales/ReturnOrderLineItemTable.tsx:167
msgid "Received"
msgstr "Ricevuto"

#: src/forms/PurchaseOrderForms.tsx:810
msgid "Receive Line Items"
msgstr "Ricevi Elementi Riga"

#: src/forms/PurchaseOrderForms.tsx:816
msgid "Items received"
msgstr "Articoli ricevuti"

#: src/forms/ReturnOrderForms.tsx:254
msgid "Receive Items"
msgstr "Ricevi elementi"

#: src/forms/ReturnOrderForms.tsx:261
msgid "Item received into stock"
msgstr "Oggetto ricevuto in magazzino"

#: src/forms/StockForms.tsx:110
#~ msgid "Create Stock Item"
#~ msgstr "Create Stock Item"

#: src/forms/StockForms.tsx:155
msgid "Add given quantity as packs instead of individual items"
msgstr "Aggiungere la quantità data come pacchi invece di singoli articoli"

#: src/forms/StockForms.tsx:158
#~ msgid "Stock item updated"
#~ msgstr "Stock item updated"

#: src/forms/StockForms.tsx:169
msgid "Enter initial quantity for this stock item"
msgstr "Inserisci quantità iniziale per questo articolo in giacenza"

#: src/forms/StockForms.tsx:178
msgid "Enter serial numbers for new stock (or leave blank)"
msgstr "Inserire i numeri di serie per la nuova giacenza (o lasciare vuoto)"

#: src/forms/StockForms.tsx:200
msgid "Stock Status"
msgstr "Stato giacenza"

#: src/forms/StockForms.tsx:260
#: src/pages/stock/StockDetail.tsx:670
#: src/tables/stock/StockItemTable.tsx:525
#: src/tables/stock/StockItemTable.tsx:572
msgid "Add Stock Item"
msgstr "Aggiungi Elemento Magazzino"

#: src/forms/StockForms.tsx:304
msgid "Select the part to install"
msgstr "Selezionare l'articolo da installare"

#: src/forms/StockForms.tsx:431
msgid "Confirm Stock Transfer"
msgstr "Conferma trasferimento"

#: src/forms/StockForms.tsx:558
msgid "Loading..."
msgstr "Caricamento..."

#: src/forms/StockForms.tsx:616
msgid "Move to default location"
msgstr "Sposta nella posizione predefinita"

#: src/forms/StockForms.tsx:736
msgid "Move"
msgstr "Sposta"

#: src/forms/StockForms.tsx:782
msgid "Return"
msgstr "Reso"

#: src/forms/StockForms.tsx:827
#: src/forms/StockForms.tsx:866
#: src/forms/StockForms.tsx:902
#: src/forms/StockForms.tsx:940
#: src/forms/StockForms.tsx:982
#: src/forms/StockForms.tsx:1030
#: src/forms/StockForms.tsx:1074
#: src/pages/company/SupplierPartDetail.tsx:190
#: src/pages/company/SupplierPartDetail.tsx:374
#: src/pages/part/PartDetail.tsx:535
#: src/pages/part/PartDetail.tsx:970
#: src/tables/purchasing/SupplierPartTable.tsx:194
#: src/tables/stock/StockItemTable.tsx:359
msgid "In Stock"
msgstr "In giacenza"

#: src/forms/StockForms.tsx:903
#: src/pages/Index/Scan.tsx:182
msgid "Count"
msgstr "Conta"

#: src/forms/StockForms.tsx:1187
#: src/hooks/UseStockAdjustActions.tsx:108
msgid "Add Stock"
msgstr "Aggiungi Giacenza"

#: src/forms/StockForms.tsx:1188
msgid "Stock added"
msgstr "Scorte aggiunte"

#: src/forms/StockForms.tsx:1191
msgid "Increase the quantity of the selected stock items by a given amount."
msgstr "Aumenta la quantità degli articoli di magazzino selezionati di una data quantità."

#: src/forms/StockForms.tsx:1202
#: src/hooks/UseStockAdjustActions.tsx:118
msgid "Remove Stock"
msgstr "Rimuovi giacenza"

#: src/forms/StockForms.tsx:1203
msgid "Stock removed"
msgstr "Scorte rimosse"

#: src/forms/StockForms.tsx:1206
msgid "Decrease the quantity of the selected stock items by a given amount."
msgstr "Diminuisce la quantità degli articoli di magazzino selezionati di una data quantità."

#: src/forms/StockForms.tsx:1217
#: src/hooks/UseStockAdjustActions.tsx:128
msgid "Transfer Stock"
msgstr "Trasferisci giacenza"

#: src/forms/StockForms.tsx:1218
msgid "Stock transferred"
msgstr "Scorte trasferite"

#: src/forms/StockForms.tsx:1221
msgid "Transfer selected items to the specified location."
msgstr "Trasferisci gli elementi selezionati nella posizione specificata."

#: src/forms/StockForms.tsx:1232
#: src/hooks/UseStockAdjustActions.tsx:168
msgid "Return Stock"
msgstr "Restituisci Elemento a Magazzino"

#: src/forms/StockForms.tsx:1233
msgid "Stock returned"
msgstr "Reso a magazzino effettuato"

#: src/forms/StockForms.tsx:1236
msgid "Return selected items into stock, to the specified location."
msgstr "Restituisce gli articoli selezionati in magazzino, nella posizione specificata."

#: src/forms/StockForms.tsx:1247
#: src/hooks/UseStockAdjustActions.tsx:98
msgid "Count Stock"
msgstr "Conteggio Giacenze"

#: src/forms/StockForms.tsx:1248
msgid "Stock counted"
msgstr "Scorte contate"

#: src/forms/StockForms.tsx:1251
msgid "Count the selected stock items, and adjust the quantity accordingly."
msgstr "Contare gli articoli di magazzino selezionati e regolare la quantità di conseguenza."

#: src/forms/StockForms.tsx:1262
msgid "Change Stock Status"
msgstr "Modifica stato giacenze"

#: src/forms/StockForms.tsx:1263
msgid "Stock status changed"
msgstr "Stato delle scorte cambiato"

#: src/forms/StockForms.tsx:1266
msgid "Change the status of the selected stock items."
msgstr "Cambia lo stato degli articoli a magazzino selezionati."

#: src/forms/StockForms.tsx:1277
#: src/hooks/UseStockAdjustActions.tsx:138
msgid "Merge Stock"
msgstr "Unisci giacenze"

#: src/forms/StockForms.tsx:1278
msgid "Stock merged"
msgstr "Scorte unite"

#: src/forms/StockForms.tsx:1280
msgid "Merge Stock Items"
msgstr "Unisci gli articoli di magazzino"

#: src/forms/StockForms.tsx:1282
msgid "Merge operation cannot be reversed"
msgstr "L'operazione di unione non è reversibile"

#: src/forms/StockForms.tsx:1283
msgid "Tracking information may be lost when merging items"
msgstr "Le informazioni di tracciamento potrebbero essere perse durante l'unione degli articoli"

#: src/forms/StockForms.tsx:1284
msgid "Supplier information may be lost when merging items"
msgstr "Le informazioni sul fornitore potrebbero essere perse durante l'unione degli articoli"

#: src/forms/StockForms.tsx:1302
msgid "Assign Stock to Customer"
msgstr "Assegnare la scorta al cliente"

#: src/forms/StockForms.tsx:1303
msgid "Stock assigned to customer"
msgstr "Scorte assegnate al cliente"

#: src/forms/StockForms.tsx:1313
msgid "Delete Stock Items"
msgstr "Cancella Elemento di Magazzino"

#: src/forms/StockForms.tsx:1314
msgid "Stock deleted"
msgstr "Scorte cancellate"

#: src/forms/StockForms.tsx:1317
msgid "This operation will permanently delete the selected stock items."
msgstr "Questa operazione eliminerà definitivamente gli articoli a magazzino selezionati."

#: src/forms/StockForms.tsx:1326
msgid "Parent stock location"
msgstr "Posizione giacenza principale"

#: src/forms/StockForms.tsx:1453
msgid "Find Serial Number"
msgstr "Trova Numero Di Serie"

#: src/forms/StockForms.tsx:1464
msgid "No matching items"
msgstr "Nessun articolo corrispondente trovato"

#: src/forms/StockForms.tsx:1470
msgid "Multiple matching items"
msgstr "Più elementi corrispondenti trovati"

#: src/forms/StockForms.tsx:1479
msgid "Invalid response from server"
msgstr "Risposta non valida dal server"

#: src/forms/selectionListFields.tsx:97
msgid "Entries"
msgstr "Registrazioni"

#: src/forms/selectionListFields.tsx:98
msgid "List of entries to choose from"
msgstr "Elenco delle voci tra cui scegliere"

#: src/forms/selectionListFields.tsx:102
#: src/pages/part/PartStockHistoryDetail.tsx:59
#: src/tables/FilterSelectDrawer.tsx:114
#: src/tables/FilterSelectDrawer.tsx:137
#: src/tables/FilterSelectDrawer.tsx:149
#: src/tables/build/BuildOrderTestTable.tsx:188
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:201
msgid "Value"
msgstr "Valore"

#: src/forms/selectionListFields.tsx:103
msgid "Label"
msgstr "Etichetta"

#: src/functions/api.tsx:33
msgid "Bad request"
msgstr "Richiesta non valida"

#: src/functions/api.tsx:36
msgid "Unauthorized"
msgstr "Non Autorizzato"

#: src/functions/api.tsx:39
msgid "Forbidden"
msgstr "Vietato"

#: src/functions/api.tsx:42
msgid "Not found"
msgstr "Non trovato"

#: src/functions/api.tsx:45
msgid "Method not allowed"
msgstr "Metodo non permesso"

#: src/functions/api.tsx:48
msgid "Internal server error"
msgstr "Errore interno del server"

#: src/functions/auth.tsx:34
#~ msgid "Error fetching token from server."
#~ msgstr "Error fetching token from server."

#: src/functions/auth.tsx:36
#~ msgid "Logout successfull"
#~ msgstr "Logout successfull"

#: src/functions/auth.tsx:60
#~ msgid "See you soon."
#~ msgstr "See you soon."

#: src/functions/auth.tsx:70
#~ msgid "Logout successful"
#~ msgstr "Logout successful"

#: src/functions/auth.tsx:71
#~ msgid "You have been logged out"
#~ msgstr "You have been logged out"

#: src/functions/auth.tsx:113
#: src/functions/auth.tsx:313
msgid "Already logged in"
msgstr "Già connesso"

#: src/functions/auth.tsx:114
#: src/functions/auth.tsx:314
msgid "There is a conflicting session on the server for this browser. Please logout of that first."
msgstr "C'è una sessione in conflitto sul server per questo browser. Si prega di disconnettersi prima dalla precedente sessione."

#: src/functions/auth.tsx:142
#~ msgid "Found an existing login - using it to log you in."
#~ msgstr "Found an existing login - using it to log you in."

#: src/functions/auth.tsx:143
#~ msgid "Found an existing login - welcome back!"
#~ msgstr "Found an existing login - welcome back!"

#: src/functions/auth.tsx:150
msgid "MFA Login successful"
msgstr "Login MFA riuscito"

#: src/functions/auth.tsx:151
msgid "MFA details were automatically provided in the browser"
msgstr "I dettagli MFA sono stati forniti automaticamente nel browser"

#: src/functions/auth.tsx:179
msgid "Logged Out"
msgstr "Disconnesso"

#: src/functions/auth.tsx:180
msgid "Successfully logged out"
msgstr "Disconnesso con Successo"

#: src/functions/auth.tsx:218
msgid "Language changed"
msgstr "Lingua cambiata"

#: src/functions/auth.tsx:219
msgid "Your active language has been changed to the one set in your profile"
msgstr "La tua lingua attiva è stata cambiata in quella impostata nel tuo profilo"

#: src/functions/auth.tsx:239
msgid "Theme changed"
msgstr "Tema cambiato"

#: src/functions/auth.tsx:240
msgid "Your active theme has been changed to the one set in your profile"
msgstr "Il tuo tema attivo è stato cambiato con quello impostato nel tuo profilo"

#: src/functions/auth.tsx:274
msgid "Check your inbox for a reset link. This only works if you have an account. Check in spam too."
msgstr "Controlla la tua casella di posta per un link di reset. Funziona solo se hai un account. Controlla anche lo spam."

#: src/functions/auth.tsx:281
#: src/functions/auth.tsx:538
msgid "Reset failed"
msgstr "Ripristino fallito"

#: src/functions/auth.tsx:370
msgid "Logged In"
msgstr "Accesso effettuato"

#: src/functions/auth.tsx:371
msgid "Successfully logged in"
msgstr "Accesso effettuato con successo"

#: src/functions/auth.tsx:498
msgid "Failed to set up MFA"
msgstr "Impossibile impostare l'MFA"

#: src/functions/auth.tsx:528
msgid "Password set"
msgstr "Password impostata"

#: src/functions/auth.tsx:529
#: src/functions/auth.tsx:638
msgid "The password was set successfully. You can now login with your new password"
msgstr "La password è stata impostata con successo. Ora puoi accedere con la tua nuova password"

#: src/functions/auth.tsx:603
msgid "Password could not be changed"
msgstr "La password non può essere cambiata"

#: src/functions/auth.tsx:621
msgid "The two password fields didn’t match"
msgstr "Le due password inserite non corrispondono"

#: src/functions/auth.tsx:637
msgid "Password Changed"
msgstr "Password cambiata"

#: src/functions/forms.tsx:50
#~ msgid "Form method not provided"
#~ msgstr "Form method not provided"

#: src/functions/forms.tsx:59
#~ msgid "Response did not contain action data"
#~ msgstr "Response did not contain action data"

#: src/functions/forms.tsx:182
#~ msgid "Invalid Form"
#~ msgstr "Invalid Form"

#: src/functions/forms.tsx:183
#~ msgid "method parameter not supplied"
#~ msgstr "method parameter not supplied"

#: src/functions/notifications.tsx:13
msgid "Not implemented"
msgstr "Non implementato"

#: src/functions/notifications.tsx:14
msgid "This feature is not yet implemented"
msgstr "Questa funzione non è ancora stata implementata"

#: src/functions/notifications.tsx:24
#~ msgid "Permission denied"
#~ msgstr "Permission denied"

#: src/functions/notifications.tsx:26
msgid "You do not have permission to perform this action"
msgstr "Non disponi dell'autorizzazione per eseguire quest'azione"

#: src/functions/notifications.tsx:37
msgid "Invalid Return Code"
msgstr "Codice di Ritorno Non Valido"

#: src/functions/notifications.tsx:38
msgid "Server returned status {returnCode}"
msgstr "Il server ha restituito lo stato {returnCode}"

#: src/functions/notifications.tsx:48
msgid "Timeout"
msgstr "Timeout"

#: src/functions/notifications.tsx:49
msgid "The request timed out"
msgstr "La richiesta è scaduta"

#: src/hooks/UseDataExport.tsx:34
msgid "Exporting Data"
msgstr "Esportazione dati"

#: src/hooks/UseDataExport.tsx:109
msgid "Export Data"
msgstr "Esporta Dati"

#: src/hooks/UseDataExport.tsx:112
msgid "Export"
msgstr "Esporta"

#: src/hooks/UseDataOutput.tsx:57
#: src/hooks/UseDataOutput.tsx:111
msgid "Process failed"
msgstr "Processo fallito"

#: src/hooks/UseDataOutput.tsx:75
msgid "Process completed successfully"
msgstr "Operazione completata con successo"

#: src/hooks/UseForm.tsx:92
msgid "Item Created"
msgstr "Articolo Creato"

#: src/hooks/UseForm.tsx:112
msgid "Item Updated"
msgstr "Articolo Aggiornato"

#: src/hooks/UseForm.tsx:133
msgid "Items Updated"
msgstr "Articolo Aggiornato"

#: src/hooks/UseForm.tsx:135
msgid "Update multiple items"
msgstr "Aggiorna più articoli"

#: src/hooks/UseForm.tsx:165
msgid "Item Deleted"
msgstr "Articolo Eliminato"

#: src/hooks/UseForm.tsx:169
msgid "Are you sure you want to delete this item?"
msgstr "Sei sicuro di voler eliminare questo articolo?"

#: src/hooks/UsePlaceholder.tsx:59
#~ msgid "Latest serial number"
#~ msgstr "Latest serial number"

#: src/hooks/UseStockAdjustActions.tsx:100
msgid "Count selected stock items"
msgstr "Conta gli articoli a magazzino selezionati"

#: src/hooks/UseStockAdjustActions.tsx:110
msgid "Add to selected stock items"
msgstr "Aggiungi agli articoli a magazzino selezionati"

#: src/hooks/UseStockAdjustActions.tsx:120
msgid "Remove from selected stock items"
msgstr "Rimuovi dagli articoli a magazzino selezionati"

#: src/hooks/UseStockAdjustActions.tsx:130
msgid "Transfer selected stock items"
msgstr "Trasferisci gli articoli a magazzino selezionati"

#: src/hooks/UseStockAdjustActions.tsx:140
msgid "Merge selected stock items"
msgstr "Unisci gli articoli a magazzino selezionati"

#: src/hooks/UseStockAdjustActions.tsx:150
msgid "Change status of selected stock items"
msgstr "Cambia lo stato degli articoli a magazzino selezionati"

#: src/hooks/UseStockAdjustActions.tsx:158
msgid "Assign Stock"
msgstr "Assegna Giacenza"

#: src/hooks/UseStockAdjustActions.tsx:160
msgid "Assign selected stock items to a customer"
msgstr "Assegna gli articoli a magazzino selezionati a un cliente"

#: src/hooks/UseStockAdjustActions.tsx:170
msgid "Return selected items into stock"
msgstr "Restituisci gli articoli selezionati nel magazzino"

#: src/hooks/UseStockAdjustActions.tsx:178
msgid "Delete Stock"
msgstr "Elimina articolo a magazzino"

#: src/hooks/UseStockAdjustActions.tsx:180
msgid "Delete selected stock items"
msgstr "Elimina gli articoli a magazzino selezionati"

#: src/hooks/UseStockAdjustActions.tsx:205
#: src/pages/part/PartDetail.tsx:1144
msgid "Stock Actions"
msgstr "Azioni magazzino"

#: src/pages/Auth/ChangePassword.tsx:32
#: src/pages/Auth/Reset.tsx:14
msgid "Reset Password"
msgstr "Reimposta password"

#: src/pages/Auth/ChangePassword.tsx:46
msgid "Current Password"
msgstr "Password attuale"

#: src/pages/Auth/ChangePassword.tsx:47
msgid "Enter your current password"
msgstr "Inserisci la tua password attuale"

#: src/pages/Auth/ChangePassword.tsx:53
msgid "New Password"
msgstr "Nuova Password"

#: src/pages/Auth/ChangePassword.tsx:54
msgid "Enter your new password"
msgstr "Inserisci la tua nuova password"

#: src/pages/Auth/ChangePassword.tsx:60
msgid "Confirm New Password"
msgstr "Conferma la nuova password"

#: src/pages/Auth/ChangePassword.tsx:61
msgid "Confirm your new password"
msgstr "Conferma la tua nuova password"

#: src/pages/Auth/ChangePassword.tsx:80
msgid "Confirm"
msgstr "Conferma"

#: src/pages/Auth/Layout.tsx:59
msgid "Log off"
msgstr "Disconnetti"

#: src/pages/Auth/LoggedIn.tsx:19
msgid "Checking if you are already logged in"
msgstr "Verifica se si è già connessi"

#: src/pages/Auth/Login.tsx:32
msgid "No selection"
msgstr "Nessuna selezione"

#: src/pages/Auth/Login.tsx:91
#~ msgid "Welcome, log in below"
#~ msgstr "Welcome, log in below"

#: src/pages/Auth/Login.tsx:93
#~ msgid "Register below"
#~ msgstr "Register below"

#: src/pages/Auth/Login.tsx:100
msgid "Login"
msgstr "Accedi"

#: src/pages/Auth/Login.tsx:106
msgid "Logging you in"
msgstr "Accesso in corso"

#: src/pages/Auth/Login.tsx:113
msgid "Don't have an account?"
msgstr "Non hai un account?"

#: src/pages/Auth/Logout.tsx:22
#~ msgid "Logging out"
#~ msgstr "Logging out"

#: src/pages/Auth/MFA.tsx:16
#~ msgid "Multi-Factor Login"
#~ msgstr "Multi-Factor Login"

#: src/pages/Auth/MFA.tsx:17
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:79
msgid "Multi-Factor Authentication"
msgstr "Autenticazione a più fattori"

#: src/pages/Auth/MFA.tsx:20
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:693
msgid "TOTP Code"
msgstr "Codice TOTP"

#: src/pages/Auth/MFA.tsx:22
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:695
msgid "Enter your TOTP or recovery code"
msgstr "Inserisci il tuo TOTP o codice di recupero"

#: src/pages/Auth/MFA.tsx:27
msgid "Remember this device"
msgstr "Ricorda questo dispositivo"

#: src/pages/Auth/MFA.tsx:29
msgid "If enabled, you will not be asked for MFA on this device for 30 days."
msgstr "Se abilitato, non ti verrà richiesto MFA su questo dispositivo per 30 giorni."

#: src/pages/Auth/MFA.tsx:38
msgid "Log in"
msgstr "Accedi"

#: src/pages/Auth/MFASetup.tsx:23
msgid "MFA Setup Required"
msgstr "Impostazione MFA richiesta"

#: src/pages/Auth/MFASetup.tsx:34
msgid "Add TOTP"
msgstr "Aggiungi TOTP"

#: src/pages/Auth/Register.tsx:23
msgid "Go back to login"
msgstr "Torna alla pagina di accesso"

#: src/pages/Auth/Reset.tsx:41
#: src/pages/Auth/Set-Password.tsx:112
#~ msgid "Send mail"
#~ msgstr "Send mail"

#: src/pages/Auth/ResetPassword.tsx:22
#: src/pages/Auth/VerifyEmail.tsx:19
msgid "Key invalid"
msgstr "Chiave non valida"

#: src/pages/Auth/ResetPassword.tsx:23
msgid "You need to provide a valid key to set a new password. Check your inbox for a reset link."
msgstr "Devi fornire una chiave valida per impostare una nuova password. Controlla la tua casella di posta per un link di reset."

#: src/pages/Auth/ResetPassword.tsx:30
#~ msgid "Token invalid"
#~ msgstr "Token invalid"

#: src/pages/Auth/ResetPassword.tsx:31
msgid "Set new password"
msgstr "Imposta una nuova password"

#: src/pages/Auth/ResetPassword.tsx:31
#~ msgid "You need to provide a valid token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a valid token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/ResetPassword.tsx:35
msgid "The desired new password"
msgstr "La nuova password desiderata"

#: src/pages/Auth/ResetPassword.tsx:44
msgid "Send Password"
msgstr "Invia password"

#: src/pages/Auth/Set-Password.tsx:49
#~ msgid "No token provided"
#~ msgstr "No token provided"

#: src/pages/Auth/Set-Password.tsx:50
#~ msgid "You need to provide a token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/VerifyEmail.tsx:20
msgid "You need to provide a valid key."
msgstr "È necessario fornire una chiave valida."

#: src/pages/Auth/VerifyEmail.tsx:28
msgid "Verify Email"
msgstr "Verifica email"

#: src/pages/Auth/VerifyEmail.tsx:30
msgid "Verify"
msgstr "Verifica"

#. placeholder {0}: error.statusText
#: src/pages/ErrorPage.tsx:16
msgid "Error: {0}"
msgstr "Errore: {0}"

#: src/pages/ErrorPage.tsx:23
msgid "An unexpected error has occurred"
msgstr "Si è verificato un errore imprevisto"

#: src/pages/ErrorPage.tsx:28
#~ msgid "Sorry, an unexpected error has occurred."
#~ msgstr "Sorry, an unexpected error has occurred."

#: src/pages/Index/Dashboard.tsx:22
#~ msgid "Autoupdate"
#~ msgstr "Autoupdate"

#: src/pages/Index/Dashboard.tsx:26
#~ msgid "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."
#~ msgstr "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."

#: src/pages/Index/Home.tsx:58
#~ msgid "Welcome to your Dashboard{0}"
#~ msgstr "Welcome to your Dashboard{0}"

#: src/pages/Index/Playground.tsx:222
#~ msgid "This page is a showcase for the possibilities of Platform UI."
#~ msgstr "This page is a showcase for the possibilities of Platform UI."

#: src/pages/Index/Profile/Profile.tsx:30
#: src/pages/Index/Profile/Profile.tsx:141
#~ msgid "Notification Settings"
#~ msgstr "Notification Settings"

#: src/pages/Index/Profile/Profile.tsx:33
#~ msgid "Global Settings"
#~ msgstr "Global Settings"

#: src/pages/Index/Profile/Profile.tsx:47
#~ msgid "Settings for the current user"
#~ msgstr "Settings for the current user"

#: src/pages/Index/Profile/Profile.tsx:51
#~ msgid "Home Page Settings"
#~ msgstr "Home Page Settings"

#: src/pages/Index/Profile/Profile.tsx:76
#~ msgid "Search Settings"
#~ msgstr "Search Settings"

#: src/pages/Index/Profile/Profile.tsx:115
#: src/pages/Index/Profile/Profile.tsx:211
#~ msgid "Label Settings"
#~ msgstr "Label Settings"

#: src/pages/Index/Profile/Profile.tsx:120
#: src/pages/Index/Profile/Profile.tsx:219
#~ msgid "Report Settings"
#~ msgstr "Report Settings"

#: src/pages/Index/Profile/Profile.tsx:142
#~ msgid "Settings for the notifications"
#~ msgstr "Settings for the notifications"

#: src/pages/Index/Profile/Profile.tsx:148
#~ msgid "Global Server Settings"
#~ msgstr "Global Server Settings"

#: src/pages/Index/Profile/Profile.tsx:149
#~ msgid "Global Settings for this instance"
#~ msgstr "Global Settings for this instance"

#: src/pages/Index/Profile/Profile.tsx:153
#~ msgid "Server Settings"
#~ msgstr "Server Settings"

#: src/pages/Index/Profile/Profile.tsx:187
#~ msgid "Login Settings"
#~ msgstr "Login Settings"

#: src/pages/Index/Profile/Profile.tsx:202
#~ msgid "Barcode Settings"
#~ msgstr "Barcode Settings"

#: src/pages/Index/Profile/Profile.tsx:230
#~ msgid "Part Settings"
#~ msgstr "Part Settings"

#: src/pages/Index/Profile/Profile.tsx:255
#~ msgid "Pricing Settings"
#~ msgstr "Pricing Settings"

#: src/pages/Index/Profile/Profile.tsx:270
#~ msgid "Stock Settings"
#~ msgstr "Stock Settings"

#: src/pages/Index/Profile/Profile.tsx:284
#~ msgid "Build Order Settings"
#~ msgstr "Build Order Settings"

#: src/pages/Index/Profile/Profile.tsx:289
#~ msgid "Purchase Order Settings"
#~ msgstr "Purchase Order Settings"

#: src/pages/Index/Profile/Profile.tsx:300
#~ msgid "Sales Order Settings"
#~ msgstr "Sales Order Settings"

#: src/pages/Index/Profile/Profile.tsx:330
#~ msgid "Plugin Settings for this instance"
#~ msgstr "Plugin Settings for this instance"

#: src/pages/Index/Profile/SettingsPanel.tsx:27
#~ msgid "Data is current beeing loaded"
#~ msgstr "Data is current beeing loaded"

#: src/pages/Index/Profile/SettingsPanel.tsx:69
#: src/pages/Index/Profile/SettingsPanel.tsx:76
#~ msgid "Failed to load"
#~ msgstr "Failed to load"

#: src/pages/Index/Profile/SettingsPanel.tsx:100
#~ msgid "Show internal names"
#~ msgstr "Show internal names"

#: src/pages/Index/Profile/SettingsPanel.tsx:148
#~ msgid "Input {0} is not known"
#~ msgstr "Input {0} is not known"

#: src/pages/Index/Profile/SettingsPanel.tsx:161
#~ msgid "Saved changes {0}"
#~ msgstr "Saved changes {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:162
#~ msgid "Changed to {0}"
#~ msgstr "Changed to {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:176
#~ msgid "Error while saving {0}"
#~ msgstr "Error while saving {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:177
#~ msgid "Error was {err}"
#~ msgstr "Error was {err}"

#: src/pages/Index/Profile/SettingsPanel.tsx:257
#~ msgid "Plugin: {0}"
#~ msgstr "Plugin: {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:262
#~ msgid "Method: {0}"
#~ msgstr "Method: {0}"

#: src/pages/Index/Profile/UserPanel.tsx:85
#~ msgid "Userinfo"
#~ msgstr "Userinfo"

#: src/pages/Index/Profile/UserPanel.tsx:122
#~ msgid "Username: {0}"
#~ msgstr "Username: {0}"

#: src/pages/Index/Profile/UserTheme.tsx:83
#~ msgid "Design <0/>"
#~ msgstr "Design <0/>"

#: src/pages/Index/Scan.tsx:65
msgid "Item already scanned"
msgstr "Articolo già scansionato"

#: src/pages/Index/Scan.tsx:82
msgid "API Error"
msgstr "Errore API"

#: src/pages/Index/Scan.tsx:83
msgid "Failed to fetch instance data"
msgstr "Recupero dati istanza non riuscito"

#: src/pages/Index/Scan.tsx:130
msgid "Scan Error"
msgstr "Errore Di Scansione"

#: src/pages/Index/Scan.tsx:162
msgid "Selected elements are not known"
msgstr "Gli elementi selezionati non sono noti"

#: src/pages/Index/Scan.tsx:169
msgid "Multiple object types selected"
msgstr "Più tipi di oggetti selezionati"

#: src/pages/Index/Scan.tsx:175
#~ msgid "Actions ..."
#~ msgstr "Actions ..."

#: src/pages/Index/Scan.tsx:177
msgid "Actions ... "
msgstr "Azioni ... "

#: src/pages/Index/Scan.tsx:194
#: src/pages/Index/Scan.tsx:198
msgid "Barcode Scanning"
msgstr "Scansione Codice A Barre"

#: src/pages/Index/Scan.tsx:207
msgid "Barcode Input"
msgstr "Inserimento codice a barre"

#: src/pages/Index/Scan.tsx:214
msgid "Action"
msgstr "Azione"

#: src/pages/Index/Scan.tsx:217
msgid "No Items Selected"
msgstr "Nessun articolo selezionato"

#: src/pages/Index/Scan.tsx:217
#~ msgid "Manual input"
#~ msgstr "Manual input"

#: src/pages/Index/Scan.tsx:218
msgid "Scan and select items to perform actions"
msgstr "Scansiona e seleziona gli articoli per eseguire azioni"

#: src/pages/Index/Scan.tsx:218
#~ msgid "Image Barcode"
#~ msgstr "Image Barcode"

#. placeholder {0}: selection.length
#: src/pages/Index/Scan.tsx:223
msgid "{0} items selected"
msgstr "{0} elementi selezionati"

#: src/pages/Index/Scan.tsx:235
msgid "Scanned Items"
msgstr "Articoli Scansionati"

#: src/pages/Index/Scan.tsx:276
#~ msgid "Actions for {0}"
#~ msgstr "Actions for {0}"

#: src/pages/Index/Scan.tsx:298
#~ msgid "Scan Page"
#~ msgstr "Scan Page"

#: src/pages/Index/Scan.tsx:301
#~ msgid "This page can be used for continuously scanning items and taking actions on them."
#~ msgstr "This page can be used for continuously scanning items and taking actions on them."

#: src/pages/Index/Scan.tsx:308
#~ msgid "Toggle Fullscreen"
#~ msgstr "Toggle Fullscreen"

#: src/pages/Index/Scan.tsx:321
#~ msgid "Select the input method you want to use to scan items."
#~ msgstr "Select the input method you want to use to scan items."

#: src/pages/Index/Scan.tsx:323
#~ msgid "Input"
#~ msgstr "Input"

#: src/pages/Index/Scan.tsx:330
#~ msgid "Select input method"
#~ msgstr "Select input method"

#: src/pages/Index/Scan.tsx:331
#~ msgid "Nothing found"
#~ msgstr "Nothing found"

#: src/pages/Index/Scan.tsx:339
#~ msgid "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."
#~ msgstr "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."

#: src/pages/Index/Scan.tsx:353
#~ msgid "General Actions"
#~ msgstr "General Actions"

#: src/pages/Index/Scan.tsx:367
#~ msgid "Lookup part"
#~ msgstr "Lookup part"

#: src/pages/Index/Scan.tsx:375
#~ msgid "Open Link"
#~ msgstr "Open Link"

#: src/pages/Index/Scan.tsx:391
#~ msgid "History is locally kept in this browser."
#~ msgstr "History is locally kept in this browser."

#: src/pages/Index/Scan.tsx:392
#~ msgid "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."
#~ msgstr "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."

#: src/pages/Index/Scan.tsx:400
#~ msgid "Delete History"
#~ msgstr "Delete History"

#: src/pages/Index/Scan.tsx:465
#~ msgid "No history"
#~ msgstr "No history"

#: src/pages/Index/Scan.tsx:492
#~ msgid "Scanned at"
#~ msgstr "Scanned at"

#: src/pages/Index/Scan.tsx:549
#~ msgid "Enter item serial or data"
#~ msgstr "Enter item serial or data"

#: src/pages/Index/Scan.tsx:561
#~ msgid "Add dummy item"
#~ msgstr "Add dummy item"

#: src/pages/Index/Scan.tsx:652
#~ msgid "Error while getting camera"
#~ msgstr "Error while getting camera"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Scanning"
#~ msgstr "Scanning"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Not scanning"
#~ msgstr "Not scanning"

#: src/pages/Index/Scan.tsx:777
#~ msgid "Select Camera"
#~ msgstr "Select Camera"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:30
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:52
#~ msgid "Edit User Information"
#~ msgstr "Edit User Information"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:33
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:113
msgid "Edit Account Information"
msgstr "Modifica Informazioni Account"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:34
#~ msgid "User details updated"
#~ msgstr "User details updated"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:37
msgid "Account details updated"
msgstr "Dettagli account aggiornati"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:46
#~ msgid "User Actions"
#~ msgstr "User Actions"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:50
#~ msgid "First name"
#~ msgstr "First name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:136
msgid "Edit Profile Information"
msgstr "Modifica informazioni del profilo"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#~ msgid "Last name"
#~ msgstr "Last name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:56
#~ msgid "Set User Password"
#~ msgstr "Set User Password"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:58
#~ msgid "First name: {0}"
#~ msgstr "First name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:59
msgid "Profile details updated"
msgstr "Dettagli profilo aggiornati"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:61
#~ msgid "Last name: {0}"
#~ msgstr "Last name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:65
#: src/pages/core/UserDetail.tsx:55
msgid "First Name"
msgstr "Nome"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:66
#: src/pages/core/UserDetail.tsx:63
msgid "Last Name"
msgstr "Cognome"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:67
#~ msgid "First name:"
#~ msgstr "First name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:71
#~ msgid "Last name:"
#~ msgstr "Last name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:72
msgid "Staff Access"
msgstr "Accesso Al Personale"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:85
#: src/pages/core/UserDetail.tsx:119
#: src/tables/settings/CustomStateTable.tsx:101
msgid "Display Name"
msgstr "Nome Visualizzato"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:86
#: src/pages/core/UserDetail.tsx:127
msgid "Position"
msgstr "Posizione"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:90
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:447
msgid "Type"
msgstr "Tipo"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:91
#: src/pages/core/UserDetail.tsx:143
msgid "Organisation"
msgstr "Organizzazione"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:92
msgid "Primary Group"
msgstr "Gruppo Principale"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:104
msgid "Account Details"
msgstr "Dettagli account"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:107
msgid "Account Actions"
msgstr "Azioni Account"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:111
msgid "Edit Account"
msgstr "Modifica Account"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:117
#: src/tables/settings/UserTable.tsx:322
msgid "Change Password"
msgstr "Cambia password"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:119
msgid "Change User Password"
msgstr "Cambia Password Utente"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:131
msgid "Profile Details"
msgstr "Dettagli Profilo"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:134
msgid "Edit Profile"
msgstr "Modifica Profilo"

#. placeholder {0}: item.label
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:153
msgid "{0}"
msgstr "{0}"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:24
msgid "Secret"
msgstr "Password"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:30
msgid "One-Time Password"
msgstr "Password monouso"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:31
msgid "Enter the TOTP code to ensure it registered correctly"
msgstr "Inserisci il codice TOTP per assicurarti che sia registrato correttamente"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:53
msgid "Email Addresses"
msgstr "Indirizzo email"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:55
#~ msgid "Single Sign On Accounts"
#~ msgstr "Single Sign On Accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:61
msgid "Single Sign On"
msgstr "Accesso singolo"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
msgid "Not enabled"
msgstr "Non abilitato"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
#~ msgid "Multifactor"
#~ msgstr "Multifactor"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:71
#~ msgid "Single Sign On is not enabled for this server"
#~ msgstr "Single Sign On is not enabled for this server"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:72
msgid "Single Sign On is not enabled for this server "
msgstr "L'accesso singolo non è abilitato per questo server "

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:83
#~ msgid "Multifactor authentication is not configured for your account"
#~ msgstr "Multifactor authentication is not configured for your account"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:87
msgid "Access Tokens"
msgstr "Token Di Accesso"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:125
msgid "Error while updating email"
msgstr "Errore durante l'aggiornamento dell'email"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:139
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:297
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:435
msgid "Not Configured"
msgstr "Non Configurato"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:142
msgid "Currently no email addresses are registered."
msgstr "Attualmente nessun indirizzo email è registrato."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:150
msgid "The following email addresses are associated with your account:"
msgstr "I seguenti indirizzi email sono associati con il tuo account:"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:163
msgid "Primary"
msgstr "Principale"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:168
msgid "Verified"
msgstr "Verificato"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:172
msgid "Unverified"
msgstr "Non verificato"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:190
msgid "Make Primary"
msgstr "Rendi principale"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:196
msgid "Re-send Verification"
msgstr "Invia nuovamente il Codice di Verifica"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:210
msgid "Add Email Address"
msgstr "Aggiungi indirizzo email"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:212
msgid "E-Mail"
msgstr "E-mail"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:213
msgid "E-Mail address"
msgstr "Indirizzo e-mail"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:225
msgid "Error while adding email"
msgstr "Errore durante l'aggiunta della email"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:236
msgid "Add Email"
msgstr "Aggiungi Email"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:270
#~ msgid "Provider has not been configured"
#~ msgstr "Provider has not been configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:280
#~ msgid "Not configured"
#~ msgstr "Not configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:283
#~ msgid "There are no social network accounts connected to this account."
#~ msgstr "There are no social network accounts connected to this account."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:293
#~ msgid "You can sign in to your account using any of the following third party accounts"
#~ msgstr "You can sign in to your account using any of the following third party accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:300
msgid "There are no providers connected to this account."
msgstr "Non ci sono provider connessi a questo account."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:309
msgid "You can sign in to your account using any of the following providers"
msgstr "Puoi accedere al tuo account utilizzando uno dei seguenti provider"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:322
msgid "Remove Provider Link"
msgstr "Rimuovi Collegamento Provider"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:439
msgid "No multi-factor tokens configured for this account"
msgstr "Nessun token multi-fattore configurato per questo account"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:450
msgid "Last used at"
msgstr "Ultimo utilizzo"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:453
msgid "Created at"
msgstr "Creato il"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:474
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:579
msgid "Recovery Codes"
msgstr "Codici di recupero"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:478
msgid "Unused Codes"
msgstr "Codici Non Utilizzati"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:483
msgid "Used Codes"
msgstr "Codici usati"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:538
msgid "Error while registering recovery codes"
msgstr "Errore durante la registrazione dei codici di recupero"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:572
msgid "TOTP"
msgstr "TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:573
msgid "Time-based One-Time Password"
msgstr "Password monouso basata sul tempo"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:580
msgid "One-Time pre-generated recovery codes"
msgstr "Codici di recupero monouso pre-generati"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:594
msgid "Add Token"
msgstr "Aggiungi token"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:609
msgid "Register TOTP Token"
msgstr "Registra Token TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:634
msgid "Error registering TOTP token"
msgstr "Errore registrazione token TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:710
msgid "Enter your password"
msgstr "Inserisci la tua password"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:741
#~ msgid "Token is used - no actions"
#~ msgstr "Token is used - no actions"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:761
#~ msgid "No tokens configured"
#~ msgstr "No tokens configured"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:61
msgid "Display Settings"
msgstr "Impostazioni Schermo"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:65
#~ msgid "bars"
#~ msgstr "bars"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:66
#~ msgid "oval"
#~ msgstr "oval"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
msgid "Language"
msgstr "Lingua"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
#~ msgid "dots"
#~ msgstr "dots"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:78
msgid "Use pseudo language"
msgstr "Usa linguaggio pseudo"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:81
#~ msgid "Theme"
#~ msgstr "Theme"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:85
msgid "Color Mode"
msgstr "Modalità Colore"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:87
#~ msgid "Primary color"
#~ msgstr "Primary color"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:96
msgid "Highlight color"
msgstr "Colore di evidenziazione"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:110
msgid "Example"
msgstr "Esempio"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:116
msgid "White color"
msgstr "Colore bianco"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:139
msgid "Black color"
msgstr "Colore nero"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:162
msgid "Border Radius"
msgstr "Raggio del bordo"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:178
msgid "Loader"
msgstr "Caricatore"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:185
msgid "Bars"
msgstr "Barre"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:186
msgid "Oval"
msgstr "Ovali"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:187
msgid "Dots"
msgstr "Punti"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:93
msgid "Reauthentication"
msgstr "Riautenticazione"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:109
msgid "OK"
msgstr "OK"

#: src/pages/Index/Settings/AdminCenter.tsx:91
#~ msgid "Advanced Amininistrative Options for InvenTree"
#~ msgstr "Advanced Amininistrative Options for InvenTree"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:28
#: src/tables/ColumnRenderers.tsx:476
msgid "Currency"
msgstr "Valuta"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:33
msgid "Rate"
msgstr "Tasso"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:46
msgid "Exchange rates updated"
msgstr "Tassi di cambio aggiornati"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:53
msgid "Exchange rate update error"
msgstr "Errore aggiornamento tasso di cambio"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:63
msgid "Refresh currency exchange rates"
msgstr "Aggiorna i tassi di cambio"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:99
msgid "Last fetched"
msgstr "Ultimo recuperato"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:100
msgid "Base currency"
msgstr "Valuta predefinita"

#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:13
msgid "Email Messages"
msgstr "Messaggi email"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:107
#~ msgid "User Management"
#~ msgstr "User Management"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:112
msgid "Users / Access"
msgstr "Utenti / Accesso"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:126
msgid "Data Import"
msgstr "Importa dati"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:127
#~ msgid "Templates"
#~ msgstr "Templates"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:132
msgid "Data Export"
msgstr "Esportazione dati"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:138
msgid "Barcode Scans"
msgstr "Scansioni di codici a barre"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:144
msgid "Background Tasks"
msgstr "Attività in background"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:150
msgid "Error Reports"
msgstr "Report di errori"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:156
msgid "Currencies"
msgstr "Valute"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:170
#~ msgid "Location types"
#~ msgstr "Location types"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:173
msgid "Custom States"
msgstr "Stati Personalizzati"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:179
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:57
msgid "Custom Units"
msgstr "Unità Personalizzate"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:185
#: src/pages/part/CategoryDetail.tsx:302
msgid "Part Parameters"
msgstr "Parametri Articolo"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:192
msgid "Category Parameters"
msgstr "Parametri Categoria"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:211
msgid "Location Types"
msgstr "Tipi di posizione"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:221
#~ msgid "Quick Actions"
#~ msgstr "Quick Actions"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:225
#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:48
#: src/tables/machine/MachineTypeTable.tsx:307
msgid "Machines"
msgstr "Macchine"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:226
#~ msgid "Add a new user"
#~ msgstr "Add a new user"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:236
msgid "Operations"
msgstr "Operazioni"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:248
msgid "Data Management"
msgstr "Gestione Dati"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:259
#: src/pages/Index/Settings/SystemSettings.tsx:175
#: src/pages/Index/Settings/UserSettings.tsx:118
msgid "Reporting"
msgstr "Rapporti"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:264
msgid "PLM"
msgstr "PLM"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:274
msgid "Extend / Integrate"
msgstr "Estendi / Integra"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:288
msgid "Advanced Options"
msgstr "Opzioni avanzate"

#: src/pages/Index/Settings/AdminCenter/LabelTemplatePanel.tsx:40
#~ msgid "Generated Labels"
#~ msgstr "Generated Labels"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:43
#~ msgid "Machine types"
#~ msgstr "Machine types"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:53
#~ msgid "Machine Error Stack"
#~ msgstr "Machine Error Stack"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:56
msgid "Machine Types"
msgstr "Tipi Macchine"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:62
#~ msgid "There are no machine registry errors."
#~ msgstr "There are no machine registry errors."

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:64
msgid "Machine Errors"
msgstr "Errori Macchina"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:77
msgid "Registry Registry Errors"
msgstr "Registro degli Errori di Registro"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:80
msgid "There are machine registry errors"
msgstr "Ci sono errori del registro macchine"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:86
msgid "Machine Registry Errors"
msgstr "Errori Del Registro Macchine"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:89
msgid "There are no machine registry errors"
msgstr "Non ci sono errori del registro macchine"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#: src/tables/settings/UserTable.tsx:195
msgid "Info"
msgstr "Info"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#~ msgid "Plugin Error Stack"
#~ msgstr "Plugin Error Stack"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:37
msgid "External plugins are not enabled for this InvenTree installation."
msgstr "I plugin esterni non sono abilitati per questa installazione InvenTree."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:45
#~ msgid "Warning"
#~ msgstr "Warning"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:47
#~ msgid "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."
#~ msgstr "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:76
msgid "Plugin Errors"
msgstr "Errori Plugin"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:16
msgid "Page Size"
msgstr "Dimensioni pagina"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:19
msgid "Landscape"
msgstr "Paesaggio"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:25
msgid "Merge"
msgstr "Unisci"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:31
msgid "Attach to Model"
msgstr "Allega al modello"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:55
#~ msgid "Generated Reports"
#~ msgstr "Generated Reports"

#: src/pages/Index/Settings/AdminCenter/StocktakePanel.tsx:25
#~ msgid "Stocktake Reports"
#~ msgstr "Stocktake Reports"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:30
msgid "Background worker not running"
msgstr "Processo in background non in esecuzione"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:31
msgid "The background task manager service is not running. Contact your system administrator."
msgstr "Il servizio di gestione attività in background non è in esecuzione. Contatta l'amministratore di sistema."

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:35
#~ msgid "Background Worker Not Running"
#~ msgstr "Background Worker Not Running"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:38
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:47
msgid "Pending Tasks"
msgstr "Attività in sospeso"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:39
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:55
msgid "Scheduled Tasks"
msgstr "Operazioni pianificate"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:40
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:63
msgid "Failed Tasks"
msgstr "Attività Fallite"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:67
#~ msgid "Stock item"
#~ msgstr "Stock item"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:76
#~ msgid "Build line"
#~ msgstr "Build line"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:88
#~ msgid "Reports"
#~ msgstr "Reports"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:99
#~ msgid "Purchase order"
#~ msgstr "Purchase order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:108
#~ msgid "Sales order"
#~ msgstr "Sales order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:117
#~ msgid "Return order"
#~ msgstr "Return order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:145
#~ msgid "Tests"
#~ msgstr "Tests"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:154
#~ msgid "Stock location"
#~ msgstr "Stock location"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:21
msgid "Alias"
msgstr "Alias"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:22
msgid "Dimensionless"
msgstr "Adimensionale/Senza dimensione"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:65
msgid "All units"
msgstr "Tutte le unità"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:31
msgid "Tokens"
msgstr "Tokens"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:32
#~ msgid "Select settings relevant for user lifecycle. More available in"
#~ msgstr "Select settings relevant for user lifecycle. More available in"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:37
#~ msgid "System settings"
#~ msgstr "System settings"

#: src/pages/Index/Settings/PluginSettingsGroup.tsx:99
msgid "The settings below are specific to each available plugin"
msgstr "Le impostazioni sottostanti sono specifiche per ogni plugin disponibile"

#: src/pages/Index/Settings/SystemSettings.tsx:77
msgid "Authentication"
msgstr "Autenticazione"

#: src/pages/Index/Settings/SystemSettings.tsx:103
msgid "Barcodes"
msgstr "Codici a barre"

#: src/pages/Index/Settings/SystemSettings.tsx:118
#~ msgid "Physical Units"
#~ msgstr "Physical Units"

#: src/pages/Index/Settings/SystemSettings.tsx:119
#~ msgid "This panel is a placeholder."
#~ msgstr "This panel is a placeholder."

#: src/pages/Index/Settings/SystemSettings.tsx:127
#: src/pages/Index/Settings/UserSettings.tsx:112
msgid "The settings below are specific to each available notification method"
msgstr "Le impostazioni sottostanti sono specifiche per ogni metodo di notifica disponibile"

#: src/pages/Index/Settings/SystemSettings.tsx:133
msgid "Pricing"
msgstr "Prezzi"

#: src/pages/Index/Settings/SystemSettings.tsx:135
#~ msgid "Exchange Rates"
#~ msgstr "Exchange Rates"

#: src/pages/Index/Settings/SystemSettings.tsx:169
msgid "Labels"
msgstr "Etichette"

#: src/pages/Index/Settings/SystemSettings.tsx:317
#~ msgid "Switch to User Setting"
#~ msgstr "Switch to User Setting"

#: src/pages/Index/Settings/UserSettings.tsx:41
msgid "Account"
msgstr "Account"

#: src/pages/Index/Settings/UserSettings.tsx:47
msgid "Security"
msgstr "Sicurezza"

#: src/pages/Index/Settings/UserSettings.tsx:53
msgid "Display Options"
msgstr "Opzioni Visualizzazione"

#: src/pages/Index/Settings/UserSettings.tsx:159
#~ msgid "Switch to System Setting"
#~ msgstr "Switch to System Setting"

#: src/pages/Logged-In.tsx:24
#~ msgid "Found an exsisting login - using it to log you in."
#~ msgstr "Found an exsisting login - using it to log you in."

#: src/pages/NotFound.tsx:20
#~ msgid "Sorry, this page is not known or was moved."
#~ msgstr "Sorry, this page is not known or was moved."

#: src/pages/NotFound.tsx:27
#~ msgid "Go to the start page"
#~ msgstr "Go to the start page"

#: src/pages/Notifications.tsx:44
#~ msgid "Delete Notifications"
#~ msgstr "Delete Notifications"

#: src/pages/Notifications.tsx:83
msgid "History"
msgstr "Cronologia"

#: src/pages/Notifications.tsx:91
msgid "Mark as unread"
msgstr "Segna come non letto"

#: src/pages/Notifications.tsx:146
#~ msgid "Delete notifications"
#~ msgstr "Delete notifications"

#: src/pages/build/BuildDetail.tsx:68
msgid "No Required Items"
msgstr "Nessun Articolo Richiesto"

#: src/pages/build/BuildDetail.tsx:70
msgid "This build order does not have any required items."
msgstr "Questo ordine di produzione non ha alcun articolo richiesto."

#: src/pages/build/BuildDetail.tsx:71
msgid "The assembled part may not have a Bill of Materials (BOM) defined, or the BOM is empty."
msgstr "L'articolo assemblato non può avere una Distinta base (BOM) definita, o la BOM è vuota."

#: src/pages/build/BuildDetail.tsx:80
#~ msgid "Build Status"
#~ msgstr "Build Status"

#: src/pages/build/BuildDetail.tsx:185
#: src/pages/part/PartDetail.tsx:269
#: src/pages/stock/StockDetail.tsx:150
#~ msgid "View part barcode"
#~ msgstr "View part barcode"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/company/ManufacturerPartDetail.tsx:83
#: src/pages/company/SupplierPartDetail.tsx:95
#: src/pages/part/PartDetail.tsx:454
#: src/pages/stock/StockDetail.tsx:153
#: src/tables/bom/BomTable.tsx:134
#: src/tables/bom/UsedInTable.tsx:40
#: src/tables/build/BuildAllocatedStockTable.tsx:108
#: src/tables/build/BuildLineTable.tsx:328
#: src/tables/build/BuildOrderTable.tsx:78
#: src/tables/part/PartSalesAllocationsTable.tsx:61
#: src/tables/part/RelatedPartTable.tsx:73
#: src/tables/sales/SalesOrderAllocationTable.tsx:132
#: src/tables/sales/SalesOrderLineItemTable.tsx:96
#: src/tables/stock/StockItemTable.tsx:70
msgid "IPN"
msgstr "IPN"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/part/PartDetail.tsx:274
#~ msgid "Link custom barcode to part"
#~ msgstr "Link custom barcode to part"

#: src/pages/build/BuildDetail.tsx:196
#: src/pages/part/PartDetail.tsx:280
#~ msgid "Unlink custom barcode from part"
#~ msgstr "Unlink custom barcode from part"

#: src/pages/build/BuildDetail.tsx:198
#: src/pages/part/PartDetail.tsx:481
#: src/tables/bom/UsedInTable.tsx:44
#: src/tables/build/BuildOrderTable.tsx:82
#: src/tables/stock/StockItemTable.tsx:75
msgid "Revision"
msgstr "Revisione"

#: src/pages/build/BuildDetail.tsx:202
#~ msgid "Build Order updated"
#~ msgstr "Build Order updated"

#: src/pages/build/BuildDetail.tsx:211
#: src/pages/purchasing/PurchaseOrderDetail.tsx:156
#: src/pages/sales/ReturnOrderDetail.tsx:121
#: src/pages/sales/SalesOrderDetail.tsx:130
#: src/pages/stock/StockDetail.tsx:168
msgid "Custom Status"
msgstr "Stato Cliente"

#: src/pages/build/BuildDetail.tsx:220
#: src/pages/build/BuildDetail.tsx:716
#: src/pages/build/BuildIndex.tsx:28
#: src/pages/stock/LocationDetail.tsx:142
#: src/tables/build/BuildOrderTable.tsx:122
#: src/tables/build/BuildOrderTable.tsx:184
#: src/tables/stock/StockLocationTable.tsx:48
msgid "External"
msgstr "Esterna"

#: src/pages/build/BuildDetail.tsx:221
#~ msgid "Edit build order"
#~ msgstr "Edit build order"

#: src/pages/build/BuildDetail.tsx:226
#~ msgid "Duplicate build order"
#~ msgstr "Duplicate build order"

#: src/pages/build/BuildDetail.tsx:231
#~ msgid "Delete build order"
#~ msgstr "Delete build order"

#: src/pages/build/BuildDetail.tsx:238
#: src/pages/purchasing/PurchaseOrderDetail.tsx:123
#: src/pages/sales/ReturnOrderDetail.tsx:88
#: src/pages/sales/SalesOrderDetail.tsx:97
#: src/tables/ColumnRenderers.tsx:312
#: src/tables/build/BuildAllocatedStockTable.tsx:115
#: src/tables/build/BuildLineTable.tsx:337
msgid "Reference"
msgstr "Riferimento"

#: src/pages/build/BuildDetail.tsx:252
msgid "Parent Build"
msgstr "Produzione Genitore"

#: src/pages/build/BuildDetail.tsx:263
msgid "Build Quantity"
msgstr "Quantità Produzione"

#: src/pages/build/BuildDetail.tsx:269
#: src/pages/part/PartDetail.tsx:598
#: src/tables/bom/BomTable.tsx:347
#: src/tables/bom/BomTable.tsx:382
msgid "Can Build"
msgstr "Puoi produrre"

#: src/pages/build/BuildDetail.tsx:278
#: src/pages/build/BuildDetail.tsx:468
msgid "Completed Outputs"
msgstr "Output Completati"

#: src/pages/build/BuildDetail.tsx:295
#: src/tables/Filter.tsx:373
msgid "Issued By"
msgstr "Emesso da"

#: src/pages/build/BuildDetail.tsx:303
#: src/pages/part/PartDetail.tsx:691
#: src/pages/purchasing/PurchaseOrderDetail.tsx:243
#: src/pages/sales/ReturnOrderDetail.tsx:207
#: src/pages/sales/SalesOrderDetail.tsx:219
#: src/tables/Filter.tsx:311
msgid "Responsible"
msgstr "Responsabile"

#: src/pages/build/BuildDetail.tsx:321
msgid "Any location"
msgstr "Qualsiasi posizione"

#: src/pages/build/BuildDetail.tsx:328
msgid "Destination Location"
msgstr "Posizione Di Destinazione"

#: src/pages/build/BuildDetail.tsx:344
#: src/tables/settings/ApiTokenTable.tsx:98
#: src/tables/settings/PendingTasksTable.tsx:41
msgid "Created"
msgstr "Creato"

#: src/pages/build/BuildDetail.tsx:347
#: src/pages/part/PartDetail.tsx:727
#~ msgid "Test Statistics"
#~ msgstr "Test Statistics"

#: src/pages/build/BuildDetail.tsx:352
#: src/pages/purchasing/PurchaseOrderDetail.tsx:268
#: src/pages/sales/ReturnOrderDetail.tsx:233
#: src/pages/sales/SalesOrderDetail.tsx:244
#: src/tables/ColumnRenderers.tsx:424
msgid "Start Date"
msgstr "Data inizio"

#: src/pages/build/BuildDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:276
#: src/pages/sales/ReturnOrderDetail.tsx:241
#: src/pages/sales/SalesOrderDetail.tsx:252
#: src/tables/ColumnRenderers.tsx:432
#: src/tables/part/PartPurchaseOrdersTable.tsx:101
#: src/tables/sales/ReturnOrderLineItemTable.tsx:150
#: src/tables/sales/SalesOrderLineItemTable.tsx:130
msgid "Target Date"
msgstr "Data obiettivo"

#: src/pages/build/BuildDetail.tsx:368
#: src/tables/build/BuildOrderTable.tsx:92
#: src/tables/sales/SalesOrderLineItemTable.tsx:324
msgid "Completed"
msgstr "Completato"

#: src/pages/build/BuildDetail.tsx:368
#~ msgid "Reporting Actions"
#~ msgstr "Reporting Actions"

#: src/pages/build/BuildDetail.tsx:374
#~ msgid "Print build report"
#~ msgstr "Print build report"

#: src/pages/build/BuildDetail.tsx:404
msgid "Build Details"
msgstr "Dettagli della Produzione"

#: src/pages/build/BuildDetail.tsx:410
msgid "Required Parts"
msgstr "Articoli richiesti"

#: src/pages/build/BuildDetail.tsx:422
#: src/pages/sales/SalesOrderDetail.tsx:380
#: src/pages/sales/SalesOrderShipmentDetail.tsx:210
#: src/tables/part/PartSalesAllocationsTable.tsx:73
msgid "Allocated Stock"
msgstr "Scorte Assegnate"

#: src/pages/build/BuildDetail.tsx:438
msgid "Consumed Stock"
msgstr "Scorte Consumate"

#: src/pages/build/BuildDetail.tsx:455
msgid "Incomplete Outputs"
msgstr "Output Incompleti"

#: src/pages/build/BuildDetail.tsx:483
msgid "External Orders"
msgstr "Ordini Esterni"

#: src/pages/build/BuildDetail.tsx:497
msgid "Child Build Orders"
msgstr "Ordine di Produzione Subordinato"

#: src/pages/build/BuildDetail.tsx:507
#: src/tables/build/BuildOutputTable.tsx:664
#: src/tables/stock/StockItemTestResultTable.tsx:167
msgid "Test Results"
msgstr "Risultati Test"

#: src/pages/build/BuildDetail.tsx:544
msgid "Edit Build Order"
msgstr "Modifica Ordine di produzione"

#: src/pages/build/BuildDetail.tsx:566
#: src/tables/build/BuildOrderTable.tsx:208
#: src/tables/build/BuildOrderTable.tsx:224
msgid "Add Build Order"
msgstr "Nuovo Ordine di Produzione"

#: src/pages/build/BuildDetail.tsx:576
msgid "Cancel Build Order"
msgstr "Annulla Ordine Di Produzione"

#: src/pages/build/BuildDetail.tsx:578
#: src/pages/purchasing/PurchaseOrderDetail.tsx:398
#: src/pages/sales/ReturnOrderDetail.tsx:393
#: src/pages/sales/SalesOrderDetail.tsx:427
msgid "Order cancelled"
msgstr "Ordine annullato"

#: src/pages/build/BuildDetail.tsx:579
#: src/pages/purchasing/PurchaseOrderDetail.tsx:397
#: src/pages/sales/ReturnOrderDetail.tsx:392
#: src/pages/sales/SalesOrderDetail.tsx:426
msgid "Cancel this order"
msgstr "Annulla quest'ordine"

#: src/pages/build/BuildDetail.tsx:588
msgid "Hold Build Order"
msgstr "Sospendi Ordine di produzione"

#: src/pages/build/BuildDetail.tsx:590
#: src/pages/purchasing/PurchaseOrderDetail.tsx:405
#: src/pages/sales/ReturnOrderDetail.tsx:400
#: src/pages/sales/SalesOrderDetail.tsx:434
msgid "Place this order on hold"
msgstr "Metti questo ordine in sospeso"

#: src/pages/build/BuildDetail.tsx:591
#: src/pages/purchasing/PurchaseOrderDetail.tsx:406
#: src/pages/sales/ReturnOrderDetail.tsx:401
#: src/pages/sales/SalesOrderDetail.tsx:435
msgid "Order placed on hold"
msgstr "Ordine in sospeso"

#: src/pages/build/BuildDetail.tsx:596
msgid "Issue Build Order"
msgstr "Emetti ordine di produzione"

#: src/pages/build/BuildDetail.tsx:598
#: src/pages/purchasing/PurchaseOrderDetail.tsx:389
#: src/pages/sales/ReturnOrderDetail.tsx:384
#: src/pages/sales/SalesOrderDetail.tsx:418
msgid "Issue this order"
msgstr "Emetti questo ordine di produzione"

#: src/pages/build/BuildDetail.tsx:599
#: src/pages/purchasing/PurchaseOrderDetail.tsx:390
#: src/pages/sales/ReturnOrderDetail.tsx:385
#: src/pages/sales/SalesOrderDetail.tsx:419
msgid "Order issued"
msgstr "Ordine emesso"

#: src/pages/build/BuildDetail.tsx:618
msgid "Complete Build Order"
msgstr "Completa l'Ordine di Produzione"

#: src/pages/build/BuildDetail.tsx:624
#: src/pages/purchasing/PurchaseOrderDetail.tsx:418
#: src/pages/sales/ReturnOrderDetail.tsx:408
#: src/pages/sales/SalesOrderDetail.tsx:453
msgid "Mark this order as complete"
msgstr "Contrassegna questo ordine come completato"

#: src/pages/build/BuildDetail.tsx:627
#: src/pages/purchasing/PurchaseOrderDetail.tsx:412
#: src/pages/sales/ReturnOrderDetail.tsx:409
#: src/pages/sales/SalesOrderDetail.tsx:454
msgid "Order completed"
msgstr "Ordine completato"

#: src/pages/build/BuildDetail.tsx:654
#: src/pages/purchasing/PurchaseOrderDetail.tsx:441
#: src/pages/sales/ReturnOrderDetail.tsx:438
#: src/pages/sales/SalesOrderDetail.tsx:489
msgid "Issue Order"
msgstr "Emetti Ordine"

#: src/pages/build/BuildDetail.tsx:661
#: src/pages/purchasing/PurchaseOrderDetail.tsx:448
#: src/pages/sales/ReturnOrderDetail.tsx:445
#: src/pages/sales/SalesOrderDetail.tsx:503
msgid "Complete Order"
msgstr "Completa l'ordine"

#: src/pages/build/BuildDetail.tsx:679
msgid "Build Order Actions"
msgstr "Azioni Ordine di Produzione"

#: src/pages/build/BuildDetail.tsx:684
#: src/pages/purchasing/PurchaseOrderDetail.tsx:470
#: src/pages/sales/ReturnOrderDetail.tsx:467
#: src/pages/sales/SalesOrderDetail.tsx:526
msgid "Edit order"
msgstr "Modifica ordine"

#: src/pages/build/BuildDetail.tsx:688
#: src/pages/purchasing/PurchaseOrderDetail.tsx:478
#: src/pages/sales/ReturnOrderDetail.tsx:473
#: src/pages/sales/SalesOrderDetail.tsx:531
msgid "Duplicate order"
msgstr "Duplica Ordine"

#: src/pages/build/BuildDetail.tsx:692
#: src/pages/purchasing/PurchaseOrderDetail.tsx:481
#: src/pages/sales/ReturnOrderDetail.tsx:478
#: src/pages/sales/SalesOrderDetail.tsx:534
msgid "Hold order"
msgstr "Sospendi ordine"

#: src/pages/build/BuildDetail.tsx:697
#: src/pages/purchasing/PurchaseOrderDetail.tsx:486
#: src/pages/sales/ReturnOrderDetail.tsx:483
#: src/pages/sales/SalesOrderDetail.tsx:539
msgid "Cancel order"
msgstr "Annulla ordine"

#: src/pages/build/BuildDetail.tsx:735
#: src/pages/stock/StockDetail.tsx:341
#: src/tables/build/BuildAllocatedStockTable.tsx:85
#: src/tables/part/PartBuildAllocationsTable.tsx:45
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:150
#: src/tables/stock/StockTrackingTable.tsx:109
msgid "Build Order"
msgstr "Ordine di Produzione"

#: src/pages/build/BuildIndex.tsx:23
#~ msgid "Build order created"
#~ msgstr "Build order created"

#: src/pages/build/BuildIndex.tsx:29
#: src/tables/build/BuildOrderTable.tsx:185
msgid "Show external build orders"
msgstr "Mostra ordini di produzione esterni"

#: src/pages/build/BuildIndex.tsx:39
#~ msgid "New Build Order"
#~ msgstr "New Build Order"

#: src/pages/build/BuildIndex.tsx:83
#: src/pages/purchasing/PurchasingIndex.tsx:69
#: src/pages/sales/SalesIndex.tsx:90
#: src/pages/sales/SalesIndex.tsx:111
msgid "Table View"
msgstr "Vista Tabella"

#: src/pages/build/BuildIndex.tsx:86
#: src/pages/purchasing/PurchasingIndex.tsx:72
#: src/pages/sales/SalesIndex.tsx:93
#: src/pages/sales/SalesIndex.tsx:114
msgid "Calendar View"
msgstr "Visualizzazione calendario"

#: src/pages/company/CompanyDetail.tsx:99
msgid "Website"
msgstr "Sito Web"

#: src/pages/company/CompanyDetail.tsx:107
msgid "Phone Number"
msgstr "Numero di telefono"

#: src/pages/company/CompanyDetail.tsx:114
msgid "Email Address"
msgstr "Indirizzo email"

#: src/pages/company/CompanyDetail.tsx:121
msgid "Tax ID"
msgstr "Partita IVA"

#: src/pages/company/CompanyDetail.tsx:131
msgid "Default Currency"
msgstr "Valuta predefinita"

#: src/pages/company/CompanyDetail.tsx:136
#: src/pages/company/SupplierDetail.tsx:8
#: src/pages/company/SupplierPartDetail.tsx:129
#: src/pages/company/SupplierPartDetail.tsx:235
#: src/pages/company/SupplierPartDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:138
#: src/tables/Filter.tsx:352
#: src/tables/company/CompanyTable.tsx:96
#: src/tables/part/PartPurchaseOrdersTable.tsx:43
#: src/tables/purchasing/PurchaseOrderTable.tsx:109
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:40
msgid "Supplier"
msgstr "Fornitore"

#: src/pages/company/CompanyDetail.tsx:142
#: src/pages/company/ManufacturerDetail.tsx:8
#: src/pages/company/ManufacturerPartDetail.tsx:102
#: src/pages/company/ManufacturerPartDetail.tsx:264
#: src/pages/company/SupplierPartDetail.tsx:151
#: src/tables/Filter.tsx:339
#: src/tables/company/CompanyTable.tsx:101
#: src/tables/purchasing/SupplierPartTable.tsx:70
msgid "Manufacturer"
msgstr "Produttore"

#: src/pages/company/CompanyDetail.tsx:148
#: src/pages/company/CustomerDetail.tsx:8
#: src/pages/part/pricing/SaleHistoryPanel.tsx:31
#: src/pages/sales/ReturnOrderDetail.tsx:103
#: src/pages/sales/SalesOrderDetail.tsx:112
#: src/pages/sales/SalesOrderShipmentDetail.tsx:102
#: src/pages/stock/StockDetail.tsx:367
#: src/tables/company/CompanyTable.tsx:106
#: src/tables/sales/ReturnOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:153
msgid "Customer"
msgstr "Cliente"

#: src/pages/company/CompanyDetail.tsx:175
#~ msgid "Edit company"
#~ msgstr "Edit company"

#: src/pages/company/CompanyDetail.tsx:181
msgid "Company Details"
msgstr "Dettagli azienda"

#: src/pages/company/CompanyDetail.tsx:187
msgid "Supplied Parts"
msgstr "Articoli Forniti"

#: src/pages/company/CompanyDetail.tsx:189
#~ msgid "Delete company"
#~ msgstr "Delete company"

#: src/pages/company/CompanyDetail.tsx:196
msgid "Manufactured Parts"
msgstr "Articoli Prodotti"

#: src/pages/company/CompanyDetail.tsx:243
msgid "Assigned Stock"
msgstr "Elementi in Giacenza Assegnati"

#: src/pages/company/CompanyDetail.tsx:284
#: src/tables/company/CompanyTable.tsx:82
msgid "Edit Company"
msgstr "Modifica azienda"

#: src/pages/company/CompanyDetail.tsx:292
msgid "Delete Company"
msgstr "Elimina Azienda"

#: src/pages/company/CompanyDetail.tsx:307
msgid "Company Actions"
msgstr "Azioni Azienda"

#: src/pages/company/ManufacturerPartDetail.tsx:76
#: src/pages/company/SupplierPartDetail.tsx:88
msgid "Internal Part"
msgstr "Articolo interno"

#: src/pages/company/ManufacturerPartDetail.tsx:110
#: src/pages/company/SupplierPartDetail.tsx:160
msgid "Manufacturer Part Number"
msgstr "Codice articolo produttore"

#: src/pages/company/ManufacturerPartDetail.tsx:127
#: src/pages/company/SupplierPartDetail.tsx:112
msgid "External Link"
msgstr "Collegamento esterno"

#: src/pages/company/ManufacturerPartDetail.tsx:146
#: src/pages/company/SupplierPartDetail.tsx:232
#: src/pages/part/PartDetail.tsx:787
msgid "Part Details"
msgstr "Dettagli Articolo"

#: src/pages/company/ManufacturerPartDetail.tsx:149
msgid "Manufacturer Details"
msgstr "Dettagli Produttore"

#: src/pages/company/ManufacturerPartDetail.tsx:158
msgid "Manufacturer Part Details"
msgstr "Dettagli Articolo Produttore"

#: src/pages/company/ManufacturerPartDetail.tsx:164
#: src/pages/part/PartDetail.tsx:793
msgid "Parameters"
msgstr "Parametri"

#: src/pages/company/ManufacturerPartDetail.tsx:204
#: src/tables/purchasing/ManufacturerPartTable.tsx:84
msgid "Edit Manufacturer Part"
msgstr "Modifica Articolo Produttore"

#: src/pages/company/ManufacturerPartDetail.tsx:211
#: src/tables/purchasing/ManufacturerPartTable.tsx:72
#: src/tables/purchasing/ManufacturerPartTable.tsx:104
msgid "Add Manufacturer Part"
msgstr "Aggiungi Articolo Produttore"

#: src/pages/company/ManufacturerPartDetail.tsx:223
#: src/tables/purchasing/ManufacturerPartTable.tsx:92
msgid "Delete Manufacturer Part"
msgstr "Elimina Articolo Produttore"

#: src/pages/company/ManufacturerPartDetail.tsx:238
msgid "Manufacturer Part Actions"
msgstr "Azioni Articolo Produttore"

#: src/pages/company/ManufacturerPartDetail.tsx:281
msgid "ManufacturerPart"
msgstr "Articolo Produttore"

#: src/pages/company/SupplierPartDetail.tsx:103
#: src/tables/part/RelatedPartTable.tsx:82
msgid "Part Description"
msgstr "Descrizione Articolo"

#: src/pages/company/SupplierPartDetail.tsx:179
#: src/tables/part/PartPurchaseOrdersTable.tsx:73
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:184
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:226
#: src/tables/purchasing/SupplierPartTable.tsx:120
msgid "Pack Quantity"
msgstr "Quantità Confezione"

#: src/pages/company/SupplierPartDetail.tsx:197
#: src/pages/company/SupplierPartDetail.tsx:390
#: src/pages/part/PartDetail.tsx:1000
#: src/tables/bom/BomTable.tsx:414
#: src/tables/part/PartTable.tsx:95
msgid "On Order"
msgstr "In ordine"

#: src/pages/company/SupplierPartDetail.tsx:204
msgid "Supplier Availability"
msgstr "Disponibilità Fornitore"

#: src/pages/company/SupplierPartDetail.tsx:212
msgid "Availability Updated"
msgstr "Disponibilità Aggiornata"

#: src/pages/company/SupplierPartDetail.tsx:237
msgid "Availability"
msgstr "Disponibilità"

#: src/pages/company/SupplierPartDetail.tsx:246
msgid "Supplier Part Details"
msgstr "Dettagli Articolo Fornitore"

#: src/pages/company/SupplierPartDetail.tsx:252
#: src/pages/purchasing/PurchaseOrderDetail.tsx:361
msgid "Received Stock"
msgstr "Articolo Magazzino Ricevuto"

#: src/pages/company/SupplierPartDetail.tsx:279
#: src/pages/part/PartPricingPanel.tsx:113
#: src/pages/part/pricing/PricingOverviewPanel.tsx:232
msgid "Supplier Pricing"
msgstr "Prezzo Fornitore"

#: src/pages/company/SupplierPartDetail.tsx:304
msgid "Supplier Part Actions"
msgstr "Azioni Articolo Fornitore"

#: src/pages/company/SupplierPartDetail.tsx:328
#: src/tables/purchasing/SupplierPartTable.tsx:207
msgid "Edit Supplier Part"
msgstr "Modifica Articolo Fornitore"

#: src/pages/company/SupplierPartDetail.tsx:336
#: src/tables/purchasing/SupplierPartTable.tsx:215
msgid "Delete Supplier Part"
msgstr "Cancella Articolo Fornitore"

#: src/pages/company/SupplierPartDetail.tsx:344
#: src/tables/purchasing/SupplierPartTable.tsx:154
msgid "Add Supplier Part"
msgstr "Aggiungi articolo fornitore"

#: src/pages/company/SupplierPartDetail.tsx:384
#: src/pages/part/PartDetail.tsx:988
msgid "No Stock"
msgstr "Nessuna giacenza"

#: src/pages/core/CoreIndex.tsx:46
#: src/pages/core/GroupDetail.tsx:81
#: src/pages/core/UserDetail.tsx:224
msgid "System Overview"
msgstr "Panoramica Del Sistema"

#: src/pages/core/GroupDetail.tsx:45
msgid "Group Name"
msgstr "Nome Gruppo"

#: src/pages/core/GroupDetail.tsx:52
#: src/pages/core/GroupDetail.tsx:67
#: src/tables/settings/GroupTable.tsx:85
msgid "Group Details"
msgstr "Dettagli Gruppo"

#: src/pages/core/GroupDetail.tsx:55
#: src/tables/settings/GroupTable.tsx:112
msgid "Group Roles"
msgstr "Ruoli gruppo"

#: src/pages/core/UserDetail.tsx:175
msgid "User Information"
msgstr "Informazioni utente"

#: src/pages/core/UserDetail.tsx:176
msgid "User Permissions"
msgstr "Permessi Utente"

#: src/pages/core/UserDetail.tsx:178
msgid "User Profile"
msgstr "Profilo Utente"

#: src/pages/core/UserDetail.tsx:188
#: src/tables/settings/UserTable.tsx:164
msgid "User Details"
msgstr "Dettagli Utente"

#: src/pages/core/UserDetail.tsx:206
msgid "Basic user"
msgstr "Utente di base"

#: src/pages/part/CategoryDetail.tsx:98
#: src/pages/stock/LocationDetail.tsx:96
#: src/tables/settings/ErrorTable.tsx:63
#: src/tables/settings/ErrorTable.tsx:108
msgid "Path"
msgstr "Percorso"

#: src/pages/part/CategoryDetail.tsx:114
msgid "Parent Category"
msgstr "Categoria Superiore"

#: src/pages/part/CategoryDetail.tsx:137
#: src/pages/part/CategoryDetail.tsx:267
msgid "Subcategories"
msgstr "Sottocategorie"

#: src/pages/part/CategoryDetail.tsx:144
#: src/pages/stock/LocationDetail.tsx:136
#: src/tables/part/PartCategoryTable.tsx:89
#: src/tables/stock/StockLocationTable.tsx:43
msgid "Structural"
msgstr "Struttura"

#: src/pages/part/CategoryDetail.tsx:150
msgid "Parent default location"
msgstr "Posizione predefinita superiore"

#: src/pages/part/CategoryDetail.tsx:157
msgid "Default location"
msgstr "Posizione predefinita"

#: src/pages/part/CategoryDetail.tsx:168
msgid "Top level part category"
msgstr "Categoria articolo di livello superiore"

#: src/pages/part/CategoryDetail.tsx:178
#: src/pages/part/CategoryDetail.tsx:244
#: src/tables/part/PartCategoryTable.tsx:122
msgid "Edit Part Category"
msgstr "Modifica Categoria Articoli"

#: src/pages/part/CategoryDetail.tsx:187
msgid "Move items to parent category"
msgstr "Sposta articoli nella categoria superiore"

#: src/pages/part/CategoryDetail.tsx:191
#: src/pages/stock/LocationDetail.tsx:228
msgid "Delete items"
msgstr "Elimina articoli"

#: src/pages/part/CategoryDetail.tsx:199
#: src/pages/part/CategoryDetail.tsx:249
msgid "Delete Part Category"
msgstr "Elimina categoria articolo"

#: src/pages/part/CategoryDetail.tsx:202
msgid "Parts Action"
msgstr "Azioni articolo"

#: src/pages/part/CategoryDetail.tsx:203
msgid "Action for parts in this category"
msgstr "Azione articoli in questa categoria"

#: src/pages/part/CategoryDetail.tsx:208
msgid "Child Categories Action"
msgstr "Azione Categorie Figlio"

#: src/pages/part/CategoryDetail.tsx:209
msgid "Action for child categories in this category"
msgstr "Azione per categorie figli in questa categoria"

#: src/pages/part/CategoryDetail.tsx:240
#: src/tables/part/PartCategoryTable.tsx:143
msgid "Category Actions"
msgstr "Azioni Categoria"

#: src/pages/part/CategoryDetail.tsx:261
msgid "Category Details"
msgstr "Dettagli categoria"

#: src/pages/part/PartAllocationPanel.tsx:21
#: src/pages/stock/StockDetail.tsx:539
#: src/tables/part/PartTable.tsx:108
msgid "Build Order Allocations"
msgstr "Assegnazione ordine di produzione"

#: src/pages/part/PartAllocationPanel.tsx:31
#: src/pages/stock/StockDetail.tsx:554
#: src/tables/part/PartTable.tsx:116
msgid "Sales Order Allocations"
msgstr "Assegnazione Ordini Di Vendita"

#: src/pages/part/PartDetail.tsx:181
#: src/pages/part/PartDetail.tsx:184
#: src/pages/part/PartDetail.tsx:228
msgid "Validate BOM"
msgstr "Valida Distinta Base"

#: src/pages/part/PartDetail.tsx:185
msgid "Do you want to validate the bill of materials for this assembly?"
msgstr "Vuoi convalidare la distinta base per questo assemblaggio?"

#: src/pages/part/PartDetail.tsx:188
msgid "BOM validated"
msgstr "BOM convalidata"

#: src/pages/part/PartDetail.tsx:206
msgid "BOM Validated"
msgstr "Distinta base validata"

#: src/pages/part/PartDetail.tsx:207
msgid "The Bill of Materials for this part has been validated"
msgstr "La distinta base per questo articolo è stata validata"

#: src/pages/part/PartDetail.tsx:211
#: src/pages/part/PartDetail.tsx:216
msgid "BOM Not Validated"
msgstr "Distinta base non validata"

#: src/pages/part/PartDetail.tsx:212
msgid "The Bill of Materials for this part has previously been checked, but requires revalidation"
msgstr "La distinta base per questo articolo è stata precedentemente verificata, ma richiede la riconvalida"

#: src/pages/part/PartDetail.tsx:217
msgid "The Bill of Materials for this part has not yet been validated"
msgstr "La distinta base per questo articolo non è stata ancora validata"

#: src/pages/part/PartDetail.tsx:248
msgid "Validated On"
msgstr "Validata il"

#: src/pages/part/PartDetail.tsx:253
msgid "Validated By"
msgstr "Validata da"

#: src/pages/part/PartDetail.tsx:286
#~ msgid "Variant Stock"
#~ msgstr "Variant Stock"

#: src/pages/part/PartDetail.tsx:310
#~ msgid "Edit part"
#~ msgstr "Edit part"

#: src/pages/part/PartDetail.tsx:322
#~ msgid "Duplicate part"
#~ msgstr "Duplicate part"

#: src/pages/part/PartDetail.tsx:327
#~ msgid "Delete part"
#~ msgstr "Delete part"

#: src/pages/part/PartDetail.tsx:467
msgid "Variant of"
msgstr "Variante di"

#: src/pages/part/PartDetail.tsx:474
msgid "Revision of"
msgstr "Revisione di"

#: src/pages/part/PartDetail.tsx:494
#: src/tables/ColumnRenderers.tsx:200
#: src/tables/ColumnRenderers.tsx:209
msgid "Default Location"
msgstr "Posizione Predefinita"

#: src/pages/part/PartDetail.tsx:501
msgid "Category Default Location"
msgstr "Posizione Predefinita Della Categoria"

#: src/pages/part/PartDetail.tsx:508
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:48
msgid "Units"
msgstr "Unità"

#: src/pages/part/PartDetail.tsx:510
#~ msgid "Stocktake By"
#~ msgstr "Stocktake By"

#: src/pages/part/PartDetail.tsx:515
#: src/tables/settings/PendingTasksTable.tsx:51
msgid "Keywords"
msgstr "Parole Chiave"

#: src/pages/part/PartDetail.tsx:542
#: src/tables/bom/BomTable.tsx:409
#: src/tables/build/BuildLineTable.tsx:297
#: src/tables/part/PartTable.tsx:306
#: src/tables/sales/SalesOrderLineItemTable.tsx:134
msgid "Available Stock"
msgstr "Giacenza Disponibile"

#: src/pages/part/PartDetail.tsx:548
#: src/tables/bom/BomTable.tsx:323
#: src/tables/build/BuildLineTable.tsx:259
#: src/tables/sales/SalesOrderLineItemTable.tsx:172
msgid "On order"
msgstr "In ordine"

#: src/pages/part/PartDetail.tsx:555
msgid "Required for Orders"
msgstr "Richiesto per gli ordini"

#: src/pages/part/PartDetail.tsx:566
msgid "Allocated to Build Orders"
msgstr "Assegnato agli Ordini di Produzione"

#: src/pages/part/PartDetail.tsx:578
msgid "Allocated to Sales Orders"
msgstr "Assegnato agli Ordini di Vendita"

#: src/pages/part/PartDetail.tsx:587
#: src/pages/part/PartDetail.tsx:1006
#: src/pages/stock/StockDetail.tsx:896
#: src/tables/build/BuildOrderTestTable.tsx:273
#: src/tables/stock/StockItemTable.tsx:364
msgid "In Production"
msgstr "In Produzione"

#: src/pages/part/PartDetail.tsx:605
msgid "Minimum Stock"
msgstr "Scorta Minima"

#: src/pages/part/PartDetail.tsx:613
#~ msgid "Scheduling"
#~ msgstr "Scheduling"

#: src/pages/part/PartDetail.tsx:620
#: src/tables/part/ParametricPartTable.tsx:355
#: src/tables/part/PartTable.tsx:190
msgid "Locked"
msgstr "Bloccato"

#: src/pages/part/PartDetail.tsx:626
msgid "Template Part"
msgstr "Modello articolo"

#: src/pages/part/PartDetail.tsx:631
#: src/tables/bom/BomTable.tsx:404
msgid "Assembled Part"
msgstr "Articolo assemblato"

#: src/pages/part/PartDetail.tsx:636
msgid "Component Part"
msgstr "Articolo Componente"

#: src/pages/part/PartDetail.tsx:641
#: src/tables/bom/BomTable.tsx:394
msgid "Testable Part"
msgstr "Articolo Testabile"

#: src/pages/part/PartDetail.tsx:647
#: src/tables/bom/BomTable.tsx:399
msgid "Trackable Part"
msgstr "Articolo tracciabile"

#: src/pages/part/PartDetail.tsx:652
msgid "Purchaseable Part"
msgstr "Articolo Acquistabile"

#: src/pages/part/PartDetail.tsx:658
msgid "Saleable Part"
msgstr "Articolo Vendibile"

#: src/pages/part/PartDetail.tsx:663
msgid "Virtual Part"
msgstr "Articolo Virtuale"

#: src/pages/part/PartDetail.tsx:678
#: src/pages/purchasing/PurchaseOrderDetail.tsx:253
#: src/pages/sales/ReturnOrderDetail.tsx:217
#: src/pages/sales/SalesOrderDetail.tsx:229
#: src/tables/ColumnRenderers.tsx:440
msgid "Creation Date"
msgstr "Data di creazione"

#: src/pages/part/PartDetail.tsx:683
#: src/tables/ColumnRenderers.tsx:386
#: src/tables/Filter.tsx:365
msgid "Created By"
msgstr "Creato Da"

#: src/pages/part/PartDetail.tsx:698
msgid "Default Supplier"
msgstr "Fornitore predefinito"

#: src/pages/part/PartDetail.tsx:704
msgid "Default Expiry"
msgstr "Scadenza Predefinita"

#: src/pages/part/PartDetail.tsx:709
msgid "days"
msgstr "giorni"

#: src/pages/part/PartDetail.tsx:719
#: src/pages/part/pricing/BomPricingPanel.tsx:113
#: src/pages/part/pricing/VariantPricingPanel.tsx:95
#: src/tables/part/PartTable.tsx:166
msgid "Price Range"
msgstr "Fascia di Prezzo"

#: src/pages/part/PartDetail.tsx:729
msgid "Latest Serial Number"
msgstr "Ultimo Numero Di Serie"

#: src/pages/part/PartDetail.tsx:757
msgid "Select Part Revision"
msgstr "Seleziona Revisione Articolo"

#: src/pages/part/PartDetail.tsx:822
msgid "Variants"
msgstr "Varianti"

#: src/pages/part/PartDetail.tsx:829
#: src/pages/stock/StockDetail.tsx:526
msgid "Allocations"
msgstr "Allocazioni"

#: src/pages/part/PartDetail.tsx:836
msgid "Bill of Materials"
msgstr "Distinta base"

#: src/pages/part/PartDetail.tsx:848
msgid "Used In"
msgstr "Utilizzato In"

#: src/pages/part/PartDetail.tsx:855
msgid "Part Pricing"
msgstr "Prezzo Articolo"

#: src/pages/part/PartDetail.tsx:923
msgid "Test Templates"
msgstr "Modelli test"

#: src/pages/part/PartDetail.tsx:934
msgid "Related Parts"
msgstr "Articoli correlati"

#: src/pages/part/PartDetail.tsx:956
#~ msgid "Count part stock"
#~ msgstr "Count part stock"

#: src/pages/part/PartDetail.tsx:967
#~ msgid "Transfer part stock"
#~ msgstr "Transfer part stock"

#: src/pages/part/PartDetail.tsx:994
#: src/tables/part/PartTestTemplateTable.tsx:112
#: src/tables/stock/StockItemTestResultTable.tsx:401
msgid "Required"
msgstr "Richiesto"

#: src/pages/part/PartDetail.tsx:1025
#: src/tables/part/PartTable.tsx:355
msgid "Edit Part"
msgstr "Modifica Articolo"

#: src/pages/part/PartDetail.tsx:1065
#: src/tables/part/PartTable.tsx:343
#: src/tables/part/PartTable.tsx:420
msgid "Add Part"
msgstr "Aggiungi articolo"

#: src/pages/part/PartDetail.tsx:1079
msgid "Delete Part"
msgstr "Elimina Articolo"

#: src/pages/part/PartDetail.tsx:1088
msgid "Deleting this part cannot be reversed"
msgstr "L'eliminazione di questo articolo non è reversibile"

#: src/pages/part/PartDetail.tsx:1149
#: src/pages/stock/StockDetail.tsx:854
msgid "Order"
msgstr "Ordine"

#: src/pages/part/PartDetail.tsx:1150
#: src/pages/stock/StockDetail.tsx:855
#: src/tables/build/BuildLineTable.tsx:740
msgid "Order Stock"
msgstr "Ordine Stock"

#: src/pages/part/PartDetail.tsx:1162
msgid "Search by serial number"
msgstr "Cerca per numero di serie"

#: src/pages/part/PartDetail.tsx:1170
#: src/tables/part/PartTable.tsx:392
msgid "Part Actions"
msgstr "Azioni articolo"

#: src/pages/part/PartIndex.tsx:29
#~ msgid "Categories"
#~ msgstr "Categories"

#: src/pages/part/PartPricingPanel.tsx:72
msgid "No pricing data found for this part."
msgstr "Nessun prezzo trovato per questo articolo."

#: src/pages/part/PartPricingPanel.tsx:87
#: src/pages/part/pricing/PricingOverviewPanel.tsx:325
msgid "Pricing Overview"
msgstr "Panoramica prezzi"

#: src/pages/part/PartPricingPanel.tsx:93
msgid "Purchase History"
msgstr "Cronologia acquisti"

#: src/pages/part/PartPricingPanel.tsx:107
#: src/pages/part/pricing/PricingOverviewPanel.tsx:204
msgid "Internal Pricing"
msgstr "Prezzo interno"

#: src/pages/part/PartPricingPanel.tsx:122
#: src/pages/part/pricing/PricingOverviewPanel.tsx:214
msgid "BOM Pricing"
msgstr "Prezzi BOM"

#: src/pages/part/PartPricingPanel.tsx:129
#: src/pages/part/pricing/PricingOverviewPanel.tsx:242
msgid "Variant Pricing"
msgstr "Prezzi Varianti"

#: src/pages/part/PartPricingPanel.tsx:141
#: src/pages/part/pricing/PricingOverviewPanel.tsx:251
msgid "Sale Pricing"
msgstr "Prezzo di Vendita"

#: src/pages/part/PartPricingPanel.tsx:147
#: src/pages/part/pricing/PricingOverviewPanel.tsx:260
msgid "Sale History"
msgstr "Storico vendite"

#: src/pages/part/PartSchedulingDetail.tsx:51
#: src/pages/part/PartSchedulingDetail.tsx:291
#~ msgid "Scheduled"
#~ msgstr "Scheduled"

#: src/pages/part/PartSchedulingDetail.tsx:95
#~ msgid "Quantity is speculative"
#~ msgstr "Quantity is speculative"

#: src/pages/part/PartSchedulingDetail.tsx:104
#~ msgid "No date available for provided quantity"
#~ msgstr "No date available for provided quantity"

#: src/pages/part/PartSchedulingDetail.tsx:108
#~ msgid "Date is in the past"
#~ msgstr "Date is in the past"

#: src/pages/part/PartSchedulingDetail.tsx:115
#~ msgid "Scheduled Quantity"
#~ msgstr "Scheduled Quantity"

#: src/pages/part/PartSchedulingDetail.tsx:242
#~ msgid "No information available"
#~ msgstr "No information available"

#: src/pages/part/PartSchedulingDetail.tsx:243
#~ msgid "There is no scheduling information available for the selected part"
#~ msgstr "There is no scheduling information available for the selected part"

#: src/pages/part/PartSchedulingDetail.tsx:278
#~ msgid "Expected Quantity"
#~ msgstr "Expected Quantity"

#: src/pages/part/PartStockHistoryDetail.tsx:80
msgid "Edit Stocktake Entry"
msgstr "Modifica Voce Inventario"

#: src/pages/part/PartStockHistoryDetail.tsx:88
msgid "Delete Stocktake Entry"
msgstr "Elimina Voce Inventario"

#: src/pages/part/PartStockHistoryDetail.tsx:107
#: src/pages/part/PartStockHistoryDetail.tsx:211
#: src/pages/stock/StockDetail.tsx:399
#: src/tables/stock/StockItemTable.tsx:272
msgid "Stock Value"
msgstr "Valore Magazzino"

#: src/pages/part/PartStockHistoryDetail.tsx:240
#: src/pages/part/pricing/PricingOverviewPanel.tsx:327
msgid "Minimum Value"
msgstr "Valore minimo"

#: src/pages/part/PartStockHistoryDetail.tsx:246
#: src/pages/part/pricing/PricingOverviewPanel.tsx:328
msgid "Maximum Value"
msgstr "Valore massimo"

#: src/pages/part/PartStocktakeDetail.tsx:99
#: src/tables/settings/StocktakeReportTable.tsx:70
#~ msgid "Generate Stocktake Report"
#~ msgstr "Generate Stocktake Report"

#: src/pages/part/PartStocktakeDetail.tsx:104
#: src/tables/settings/StocktakeReportTable.tsx:72
#~ msgid "Stocktake report scheduled"
#~ msgstr "Stocktake report scheduled"

#: src/pages/part/PartStocktakeDetail.tsx:145
#: src/tables/settings/StocktakeReportTable.tsx:78
#~ msgid "New Stocktake Report"
#~ msgstr "New Stocktake Report"

#: src/pages/part/pricing/BomPricingPanel.tsx:87
#: src/pages/part/pricing/BomPricingPanel.tsx:175
#: src/tables/ColumnRenderers.tsx:490
#: src/tables/bom/BomTable.tsx:270
#: src/tables/general/ExtraLineItemTable.tsx:69
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:255
#: src/tables/purchasing/PurchaseOrderTable.tsx:138
#: src/tables/sales/ReturnOrderTable.tsx:139
#: src/tables/sales/SalesOrderLineItemTable.tsx:120
#: src/tables/sales/SalesOrderTable.tsx:175
msgid "Total Price"
msgstr "Prezzo Totale"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#: src/pages/part/pricing/BomPricingPanel.tsx:141
#: src/tables/bom/UsedInTable.tsx:54
#: src/tables/part/PartTable.tsx:214
msgid "Component"
msgstr "Componente"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#~ msgid "Minimum Total Price"
#~ msgstr "Minimum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:115
#: src/pages/part/pricing/VariantPricingPanel.tsx:35
#: src/pages/part/pricing/VariantPricingPanel.tsx:97
msgid "Minimum Price"
msgstr "Prezzo Minimo"

#: src/pages/part/pricing/BomPricingPanel.tsx:116
#: src/pages/part/pricing/VariantPricingPanel.tsx:43
#: src/pages/part/pricing/VariantPricingPanel.tsx:98
msgid "Maximum Price"
msgstr "Prezzo Massimo"

#: src/pages/part/pricing/BomPricingPanel.tsx:117
#~ msgid "Maximum Total Price"
#~ msgstr "Maximum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:166
#: src/pages/part/pricing/PriceBreakPanel.tsx:173
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:71
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:126
#: src/pages/part/pricing/SupplierPricingPanel.tsx:66
#: src/pages/stock/StockDetail.tsx:387
#: src/tables/bom/BomTable.tsx:260
#: src/tables/general/ExtraLineItemTable.tsx:61
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:250
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:84
#: src/tables/stock/StockItemTable.tsx:260
msgid "Unit Price"
msgstr "Prezzo Unitario"

#: src/pages/part/pricing/BomPricingPanel.tsx:256
msgid "Pie Chart"
msgstr "Diagramma a torta"

#: src/pages/part/pricing/BomPricingPanel.tsx:257
msgid "Bar Chart"
msgstr "Diagramma a barre"

#: src/pages/part/pricing/PriceBreakPanel.tsx:58
#: src/pages/part/pricing/PriceBreakPanel.tsx:111
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:134
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:162
msgid "Add Price Break"
msgstr "Aggiungi sconto"

#: src/pages/part/pricing/PriceBreakPanel.tsx:71
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:146
msgid "Edit Price Break"
msgstr "Modifica sconto"

#: src/pages/part/pricing/PriceBreakPanel.tsx:81
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:154
msgid "Delete Price Break"
msgstr "Elimina sconto"

#: src/pages/part/pricing/PriceBreakPanel.tsx:95
msgid "Price Break"
msgstr "Sconto"

#: src/pages/part/pricing/PriceBreakPanel.tsx:171
msgid "Price"
msgstr "Prezzo"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:72
msgid "Refreshing pricing data"
msgstr "Aggiornamento dati sui prezzi"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:92
msgid "Pricing data updated"
msgstr "Dati sui prezzi aggiornati"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:99
msgid "Failed to update pricing data"
msgstr "Impossibile aggiornare i dati dei prezzi"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:127
msgid "Edit Pricing"
msgstr "Modifica Prezzo"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:139
msgid "Pricing Category"
msgstr "Categoria di prezzo"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:158
msgid "Minimum"
msgstr "Minimo"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:170
msgid "Maximum"
msgstr "Massimo"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:188
msgid "Override Pricing"
msgstr "Ignora prezzi"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:196
msgid "Overall Pricing"
msgstr "Prezzi Complessivi"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:222
msgid "Purchase Pricing"
msgstr "Prezzo d'acquisto"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:281
#: src/pages/stock/StockDetail.tsx:179
#: src/tables/part/PartParameterTable.tsx:121
#: src/tables/stock/StockItemTable.tsx:301
msgid "Last Updated"
msgstr "Ultimo aggiornamento"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:285
msgid "Pricing Not Set"
msgstr "Prezzo Non Impostato"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:286
msgid "Pricing data has not been calculated for this part"
msgstr "I dati sui prezzi non sono stati calcolati per questo articolo"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:290
msgid "Pricing Actions"
msgstr "Azioni del Prezzo"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:293
msgid "Refresh"
msgstr "Aggiorna"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:294
msgid "Refresh pricing data"
msgstr "Aggiorna dati sui prezzi"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:309
msgid "Edit pricing data"
msgstr "Modifica i dati dei prezzi"

#: src/pages/part/pricing/PricingPanel.tsx:24
msgid "No data available"
msgstr "Nessun dato disponibile"

#: src/pages/part/pricing/PricingPanel.tsx:65
msgid "No Data"
msgstr "Nessun dato"

#: src/pages/part/pricing/PricingPanel.tsx:66
msgid "No pricing data available"
msgstr "Nessun prezzo disponibile"

#: src/pages/part/pricing/PricingPanel.tsx:77
msgid "Loading pricing data"
msgstr "Caricamento dati prezzi"

#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:48
msgid "Purchase Price"
msgstr "Prezzo d'acquisto"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#~ msgid "Sale Order"
#~ msgstr "Sale Order"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:44
#: src/pages/part/pricing/SaleHistoryPanel.tsx:87
msgid "Sale Price"
msgstr "Prezzo di Vendita"

#: src/pages/part/pricing/SupplierPricingPanel.tsx:69
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:75
msgid "Supplier Price"
msgstr "Prezzo Fornitore"

#: src/pages/part/pricing/VariantPricingPanel.tsx:29
#: src/pages/part/pricing/VariantPricingPanel.tsx:94
msgid "Variant Part"
msgstr "Variante Articolo"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:89
msgid "Edit Purchase Order"
msgstr "Modifica ordine d'acquisto"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:107
#: src/tables/purchasing/PurchaseOrderTable.tsx:154
#: src/tables/purchasing/PurchaseOrderTable.tsx:167
msgid "Add Purchase Order"
msgstr "Aggiungi ordine d'acquisto"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:129
msgid "Supplier Reference"
msgstr "Riferimento fornitore"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:159
#: src/pages/sales/ReturnOrderDetail.tsx:126
#: src/pages/sales/SalesOrderDetail.tsx:130
#~ msgid "Order Currency,"
#~ msgstr "Order Currency,"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:169
#: src/pages/sales/ReturnOrderDetail.tsx:140
#: src/pages/sales/SalesOrderDetail.tsx:143
msgid "Completed Line Items"
msgstr "Elementi Riga completati"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:178
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:261
msgid "Destination"
msgstr "Destinazione"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:184
#: src/pages/sales/ReturnOrderDetail.tsx:147
#: src/pages/sales/SalesOrderDetail.tsx:160
msgid "Order Currency"
msgstr "Valuta ordine"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:190
#: src/pages/sales/ReturnOrderDetail.tsx:154
#: src/pages/sales/SalesOrderDetail.tsx:166
msgid "Total Cost"
msgstr "Costo Totale"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:207
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:191
#~ msgid "Created On"
#~ msgstr "Created On"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:219
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:195
msgid "Contact Email"
msgstr "Email di contatto"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:227
#: src/pages/sales/ReturnOrderDetail.tsx:191
#: src/pages/sales/SalesOrderDetail.tsx:203
msgid "Contact Phone"
msgstr "Contatto telefonico"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:260
#: src/pages/sales/ReturnOrderDetail.tsx:225
#: src/pages/sales/SalesOrderDetail.tsx:236
msgid "Issue Date"
msgstr "Data di emissione"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:249
#: src/pages/sales/SalesOrderDetail.tsx:259
#: src/tables/ColumnRenderers.tsx:448
#: src/tables/build/BuildOrderTable.tsx:136
#: src/tables/part/PartPurchaseOrdersTable.tsx:106
msgid "Completion Date"
msgstr "Data di completamento"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:315
#: src/pages/sales/ReturnOrderDetail.tsx:279
#: src/pages/sales/SalesOrderDetail.tsx:325
msgid "Order Details"
msgstr "Dettagli dell'ordine"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:321
#: src/pages/purchasing/PurchaseOrderDetail.tsx:330
#: src/pages/sales/ReturnOrderDetail.tsx:133
#: src/pages/sales/ReturnOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:294
#: src/pages/sales/SalesOrderDetail.tsx:331
#: src/pages/sales/SalesOrderDetail.tsx:340
msgid "Line Items"
msgstr "Riga Articoli"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:344
#: src/pages/sales/ReturnOrderDetail.tsx:308
#: src/pages/sales/SalesOrderDetail.tsx:357
msgid "Extra Line Items"
msgstr "Voci di riga extra"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:387
msgid "Issue Purchase Order"
msgstr "Emettere ordine d'acquisto"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:395
msgid "Cancel Purchase Order"
msgstr "Annulla ordine d'acquisto"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:403
msgid "Hold Purchase Order"
msgstr "Sospendi ordine d'acquisto"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:411
msgid "Complete Purchase Order"
msgstr "Completa Ordine D'Acquisto"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:466
#: src/pages/sales/ReturnOrderDetail.tsx:463
#: src/pages/sales/SalesOrderDetail.tsx:521
msgid "Order Actions"
msgstr "Azioni Ordine"

#: src/pages/sales/ReturnOrderDetail.tsx:94
#: src/pages/sales/SalesOrderDetail.tsx:103
#: src/pages/sales/SalesOrderShipmentDetail.tsx:111
#: src/tables/sales/SalesOrderTable.tsx:141
msgid "Customer Reference"
msgstr "Riferimento cliente"

#: src/pages/sales/ReturnOrderDetail.tsx:349
#~ msgid "Order canceled"
#~ msgstr "Order canceled"

#: src/pages/sales/ReturnOrderDetail.tsx:355
msgid "Edit Return Order"
msgstr "Modifica Ordine Di Reso"

#: src/pages/sales/ReturnOrderDetail.tsx:373
#: src/tables/sales/ReturnOrderTable.tsx:154
#: src/tables/sales/ReturnOrderTable.tsx:167
msgid "Add Return Order"
msgstr "Aggiungi Ordine Di Reso"

#: src/pages/sales/ReturnOrderDetail.tsx:382
msgid "Issue Return Order"
msgstr "Emetti Ordine di Reso"

#: src/pages/sales/ReturnOrderDetail.tsx:390
msgid "Cancel Return Order"
msgstr "Annulla Ordine di Reso"

#: src/pages/sales/ReturnOrderDetail.tsx:398
msgid "Hold Return Order"
msgstr "Sospendi ordine di reso"

#: src/pages/sales/ReturnOrderDetail.tsx:406
msgid "Complete Return Order"
msgstr "Completa ordine di reso"

#: src/pages/sales/SalesOrderDetail.tsx:152
msgid "Completed Shipments"
msgstr "Spedizioni Completate"

#: src/pages/sales/SalesOrderDetail.tsx:256
#~ msgid "Pending Shipments"
#~ msgstr "Pending Shipments"

#: src/pages/sales/SalesOrderDetail.tsx:292
msgid "Edit Sales Order"
msgstr "Modifica Ordini di Vendita"

#: src/pages/sales/SalesOrderDetail.tsx:314
#: src/tables/sales/SalesOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:121
msgid "Add Sales Order"
msgstr "Aggiungi ordini di vendita"

#: src/pages/sales/SalesOrderDetail.tsx:374
#: src/tables/sales/SalesOrderTable.tsx:147
msgid "Shipments"
msgstr "Spedizioni"

#: src/pages/sales/SalesOrderDetail.tsx:416
msgid "Issue Sales Order"
msgstr "Emetti ordini di vendita"

#: src/pages/sales/SalesOrderDetail.tsx:424
msgid "Cancel Sales Order"
msgstr "Annulla Ordini di Vendita"

#: src/pages/sales/SalesOrderDetail.tsx:432
msgid "Hold Sales Order"
msgstr "Sospendi ordini di vendita"

#: src/pages/sales/SalesOrderDetail.tsx:440
msgid "Ship Sales Order"
msgstr "Spedizione ordini di vendita"

#: src/pages/sales/SalesOrderDetail.tsx:442
msgid "Ship this order?"
msgstr "Spedire questo ordine?"

#: src/pages/sales/SalesOrderDetail.tsx:443
msgid "Order shipped"
msgstr "Ordine spedito"

#: src/pages/sales/SalesOrderDetail.tsx:451
msgid "Complete Sales Order"
msgstr "Completa Ordini Di Vendita"

#: src/pages/sales/SalesOrderDetail.tsx:496
msgid "Ship Order"
msgstr "Spedisci l'ordine"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:119
#: src/tables/sales/SalesOrderShipmentTable.tsx:94
msgid "Shipment Reference"
msgstr "Riferimento della spedizione"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:126
msgid "Allocated Items"
msgstr "Elementi Assegnati"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:135
msgid "Tracking Number"
msgstr "Numero di monitoraggio"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:143
msgid "Invoice Number"
msgstr "Numero Fattura"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:151
#: src/tables/ColumnRenderers.tsx:456
#: src/tables/sales/SalesOrderAllocationTable.tsx:179
#: src/tables/sales/SalesOrderShipmentTable.tsx:113
msgid "Shipment Date"
msgstr "Data di spedizione"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:159
#: src/tables/sales/SalesOrderShipmentTable.tsx:117
msgid "Delivery Date"
msgstr "Data di consegna"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:204
msgid "Shipment Details"
msgstr "Dettagli spedizione"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:211
#~ msgid "Assigned Items"
#~ msgstr "Assigned Items"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:242
#: src/pages/sales/SalesOrderShipmentDetail.tsx:334
#: src/tables/sales/SalesOrderShipmentTable.tsx:73
msgid "Edit Shipment"
msgstr "Modifica spedizione"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:249
#: src/pages/sales/SalesOrderShipmentDetail.tsx:339
#: src/tables/sales/SalesOrderShipmentTable.tsx:65
msgid "Cancel Shipment"
msgstr "Annulla spedizione"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:262
#: src/tables/sales/SalesOrderShipmentTable.tsx:81
#: src/tables/sales/SalesOrderShipmentTable.tsx:144
msgid "Complete Shipment"
msgstr "Completa Spedizione"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:279
#: src/tables/part/PartPurchaseOrdersTable.tsx:122
msgid "Pending"
msgstr "In sospeso"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:285
#: src/tables/sales/SalesOrderShipmentTable.tsx:106
#: src/tables/sales/SalesOrderShipmentTable.tsx:190
msgid "Shipped"
msgstr "Spedito"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:291
#: src/tables/sales/SalesOrderShipmentTable.tsx:195
#: src/tables/settings/EmailTable.tsx:84
msgid "Delivered"
msgstr "Consegnato"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:306
msgid "Send Shipment"
msgstr "Invia Spedizione"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:329
msgid "Shipment Actions"
msgstr "Azioni Di Spedizione"

#: src/pages/stock/LocationDetail.tsx:112
msgid "Parent Location"
msgstr "Posizione principale"

#: src/pages/stock/LocationDetail.tsx:130
msgid "Sublocations"
msgstr "Sottoallocazioni"

#: src/pages/stock/LocationDetail.tsx:148
#: src/tables/stock/StockLocationTable.tsx:57
msgid "Location Type"
msgstr "Tipo di posizione"

#: src/pages/stock/LocationDetail.tsx:159
msgid "Top level stock location"
msgstr "Posizione delle scorte di primo livello"

#: src/pages/stock/LocationDetail.tsx:170
msgid "Location Details"
msgstr "Dettagli posizione"

#: src/pages/stock/LocationDetail.tsx:196
msgid "Default Parts"
msgstr "Articoli predefiniti"

#: src/pages/stock/LocationDetail.tsx:215
#: src/pages/stock/LocationDetail.tsx:374
#: src/tables/stock/StockLocationTable.tsx:121
msgid "Edit Stock Location"
msgstr "Modifica la posizione delle scorte"

#: src/pages/stock/LocationDetail.tsx:224
msgid "Move items to parent location"
msgstr "Sposta articoli nella categoria superiore"

#: src/pages/stock/LocationDetail.tsx:236
#: src/pages/stock/LocationDetail.tsx:379
msgid "Delete Stock Location"
msgstr "Elimina Posizione di Giacenza"

#: src/pages/stock/LocationDetail.tsx:239
msgid "Items Action"
msgstr "Azione Articoli"

#: src/pages/stock/LocationDetail.tsx:240
msgid "Action for stock items in this location"
msgstr "Scansiona gli elementi in magazzino in questa ubicazione"

#: src/pages/stock/LocationDetail.tsx:245
msgid "Child Locations Action"
msgstr "Azione Posizioni Figlio"

#: src/pages/stock/LocationDetail.tsx:246
msgid "Action for child locations in this location"
msgstr "Azione per le posizioni figlie in questa posizione"

#: src/pages/stock/LocationDetail.tsx:280
msgid "Scan Stock Item"
msgstr "Scansione articolo magazzino"

#: src/pages/stock/LocationDetail.tsx:298
#: src/pages/stock/StockDetail.tsx:783
msgid "Scanned stock item into location"
msgstr "Articolo di magazzino scansionato nella posizione"

#: src/pages/stock/LocationDetail.tsx:304
#: src/pages/stock/StockDetail.tsx:789
msgid "Error scanning stock item"
msgstr "Errore nella scansione dell'articolo a magazzino"

#: src/pages/stock/LocationDetail.tsx:311
msgid "Scan Stock Location"
msgstr "Scansiona Ubicazione magazzino"

#: src/pages/stock/LocationDetail.tsx:323
msgid "Scanned stock location into location"
msgstr "Posizione magazzino scansionata nella posizione"

#: src/pages/stock/LocationDetail.tsx:329
msgid "Error scanning stock location"
msgstr "Errore nella scansione della posizione a magazzino"

#: src/pages/stock/LocationDetail.tsx:370
#: src/tables/stock/StockLocationTable.tsx:142
msgid "Location Actions"
msgstr "Azioni posizione"

#: src/pages/stock/StockDetail.tsx:147
msgid "Base Part"
msgstr "Articolo base"

#: src/pages/stock/StockDetail.tsx:155
#~ msgid "Link custom barcode to stock item"
#~ msgstr "Link custom barcode to stock item"

#: src/pages/stock/StockDetail.tsx:156
#~ msgid "Completed Tests"
#~ msgstr "Completed Tests"

#: src/pages/stock/StockDetail.tsx:161
#~ msgid "Unlink custom barcode from stock item"
#~ msgstr "Unlink custom barcode from stock item"

#: src/pages/stock/StockDetail.tsx:185
msgid "Last Stocktake"
msgstr "Ultimo Inventario"

#: src/pages/stock/StockDetail.tsx:203
msgid "Previous serial number"
msgstr "Numero di serie precedente"

#: src/pages/stock/StockDetail.tsx:205
#~ msgid "Edit stock item"
#~ msgstr "Edit stock item"

#: src/pages/stock/StockDetail.tsx:217
#~ msgid "Delete stock item"
#~ msgstr "Delete stock item"

#: src/pages/stock/StockDetail.tsx:225
msgid "Find serial number"
msgstr "Cerca Numero Di Serie"

#: src/pages/stock/StockDetail.tsx:269
msgid "Allocated to Orders"
msgstr "Assegnato agli Ordini"

#: src/pages/stock/StockDetail.tsx:302
msgid "Installed In"
msgstr "Installato In"

#: src/pages/stock/StockDetail.tsx:322
msgid "Parent Item"
msgstr "Elemento principale"

#: src/pages/stock/StockDetail.tsx:326
msgid "Parent stock item"
msgstr "Elemento di magazzino principale"

#: src/pages/stock/StockDetail.tsx:332
msgid "Consumed By"
msgstr "Consumato Da"

#: src/pages/stock/StockDetail.tsx:433
#~ msgid "Duplicate stock item"
#~ msgstr "Duplicate stock item"

#: src/pages/stock/StockDetail.tsx:510
msgid "Stock Details"
msgstr "Dettagli stock"

#: src/pages/stock/StockDetail.tsx:516
msgid "Stock Tracking"
msgstr "Monitoraggio delle scorte"

#: src/pages/stock/StockDetail.tsx:571
msgid "Test Data"
msgstr "Dati di Test"

#: src/pages/stock/StockDetail.tsx:585
msgid "Installed Items"
msgstr "Articoli installati"

#: src/pages/stock/StockDetail.tsx:592
msgid "Child Items"
msgstr "Articoli secondari"

#: src/pages/stock/StockDetail.tsx:645
msgid "Edit Stock Item"
msgstr "Modifica elementi magazzino"

#: src/pages/stock/StockDetail.tsx:671
#: src/tables/stock/StockItemTable.tsx:452
#~ msgid "Add stock"
#~ msgstr "Add stock"

#: src/pages/stock/StockDetail.tsx:680
#: src/tables/stock/StockItemTable.tsx:461
#~ msgid "Remove stock"
#~ msgstr "Remove stock"

#: src/pages/stock/StockDetail.tsx:687
msgid "Items Created"
msgstr "Articolo Creato"

#: src/pages/stock/StockDetail.tsx:688
msgid "Created {n} stock items"
msgstr "Creato {n} articoli a magazzino"

#: src/pages/stock/StockDetail.tsx:698
#: src/tables/stock/StockItemTable.tsx:481
#~ msgid "Transfer stock"
#~ msgstr "Transfer stock"

#: src/pages/stock/StockDetail.tsx:705
msgid "Delete Stock Item"
msgstr "Elimina Elemento di Magazzino"

#: src/pages/stock/StockDetail.tsx:741
msgid "Serialize Stock Item"
msgstr "Serializza Elementi di Magazzino"

#: src/pages/stock/StockDetail.tsx:757
#: src/tables/stock/StockItemTable.tsx:539
msgid "Stock item serialized"
msgstr "Elemento di magazzino serializzato"

#: src/pages/stock/StockDetail.tsx:762
#~ msgid "Return Stock Item"
#~ msgstr "Return Stock Item"

#: src/pages/stock/StockDetail.tsx:765
msgid "Scan Into Location"
msgstr "Scansiona nella posizione"

#: src/pages/stock/StockDetail.tsx:765
#~ msgid "Return this item into stock. This will remove the customer assignment."
#~ msgstr "Return this item into stock. This will remove the customer assignment."

#: src/pages/stock/StockDetail.tsx:777
#~ msgid "Item returned to stock"
#~ msgstr "Item returned to stock"

#: src/pages/stock/StockDetail.tsx:823
msgid "Scan into location"
msgstr "Scansiona nella posizione"

#: src/pages/stock/StockDetail.tsx:825
msgid "Scan this item into a location"
msgstr "Scansiona questo articolo nella posizione"

#: src/pages/stock/StockDetail.tsx:837
msgid "Stock Operations"
msgstr "Operazioni Scorte"

#: src/pages/stock/StockDetail.tsx:842
#: src/tables/build/BuildOutputTable.tsx:532
msgid "Serialize"
msgstr "Serializza"

#: src/pages/stock/StockDetail.tsx:843
msgid "Serialize stock"
msgstr "Serializza magazzino"

#: src/pages/stock/StockDetail.tsx:868
msgid "Stock Item Actions"
msgstr "Azioni per le voci di magazzino"

#: src/pages/stock/StockDetail.tsx:868
#~ msgid "Count stock"
#~ msgstr "Count stock"

#: src/pages/stock/StockDetail.tsx:890
#~ msgid "Return from customer"
#~ msgstr "Return from customer"

#: src/pages/stock/StockDetail.tsx:900
#~ msgid "Transfer"
#~ msgstr "Transfer"

#: src/pages/stock/StockDetail.tsx:937
#: src/tables/stock/StockItemTable.tsx:409
msgid "Stale"
msgstr "Obsoleto"

#: src/pages/stock/StockDetail.tsx:943
#: src/tables/stock/StockItemTable.tsx:403
msgid "Expired"
msgstr "Scaduto"

#: src/pages/stock/StockDetail.tsx:949
msgid "Unavailable"
msgstr "Non disponibile"

#: src/pages/stock/StockDetail.tsx:950
#~ msgid "Assign to Customer"
#~ msgstr "Assign to Customer"

#: src/pages/stock/StockDetail.tsx:951
#~ msgid "Assign to a customer"
#~ msgstr "Assign to a customer"

#: src/states/IconState.tsx:47
#: src/states/IconState.tsx:77
msgid "Error loading icon package from server"
msgstr "Errore nel caricare il pacchetto icone dal server"

#: src/tables/ColumnRenderers.tsx:41
#~ msgid "Part is locked"
#~ msgstr "Part is locked"

#: src/tables/ColumnRenderers.tsx:59
msgid "Part is not active"
msgstr "L'articolo non è attivo"

#: src/tables/ColumnRenderers.tsx:64
#: src/tables/bom/BomTable.tsx:619
#: src/tables/part/PartParameterTable.tsx:235
#: src/tables/part/PartTestTemplateTable.tsx:258
msgid "Part is Locked"
msgstr "L'articolo è bloccato"

#: src/tables/ColumnRenderers.tsx:69
msgid "You are subscribed to notifications for this part"
msgstr "Sei iscritto alle notifiche per questo articolo"

#: src/tables/ColumnRenderers.tsx:93
#~ msgid "No location set"
#~ msgstr "No location set"

#: src/tables/ColumnSelect.tsx:16
#: src/tables/ColumnSelect.tsx:23
msgid "Select Columns"
msgstr "Seleziona Colonne"

#: src/tables/DownloadAction.tsx:13
#~ msgid "Excel"
#~ msgstr "Excel"

#: src/tables/DownloadAction.tsx:21
#~ msgid "CSV"
#~ msgstr "CSV"

#: src/tables/DownloadAction.tsx:21
#~ msgid "Download selected data"
#~ msgstr "Download selected data"

#: src/tables/DownloadAction.tsx:22
#~ msgid "TSV"
#~ msgstr "TSV"

#: src/tables/DownloadAction.tsx:23
#~ msgid "Excel (.xlsx)"
#~ msgstr "Excel (.xlsx)"

#: src/tables/DownloadAction.tsx:24
#~ msgid "Excel (.xls)"
#~ msgstr "Excel (.xls)"

#: src/tables/DownloadAction.tsx:36
#~ msgid "Download Data"
#~ msgstr "Download Data"

#: src/tables/Filter.tsx:75
msgid "Has Batch Code"
msgstr "Ha codice lotto"

#: src/tables/Filter.tsx:76
msgid "Show items which have a batch code"
msgstr "Mostra gli articoli che hanno un codice lotto"

#: src/tables/Filter.tsx:84
msgid "Filter items by batch code"
msgstr "Filtra gli articoli per codice lotto"

#: src/tables/Filter.tsx:92
msgid "Is Serialized"
msgstr "È Serializzato"

#: src/tables/Filter.tsx:93
msgid "Show items which have a serial number"
msgstr "Mostra gli articoli che hanno un numero di serie"

#: src/tables/Filter.tsx:100
msgid "Serial"
msgstr "Seriale"

#: src/tables/Filter.tsx:101
msgid "Filter items by serial number"
msgstr "Filtra gli articoli per numero di serie"

#: src/tables/Filter.tsx:106
#~ msgid "Show overdue orders"
#~ msgstr "Show overdue orders"

#: src/tables/Filter.tsx:109
msgid "Serial Below"
msgstr "Seriale Sotto"

#: src/tables/Filter.tsx:110
msgid "Show items with serial numbers less than or equal to a given value"
msgstr "Mostra gli articoli con numeri di serie inferiori o uguali a un determinato valore"

#: src/tables/Filter.tsx:118
msgid "Serial Above"
msgstr "Seriale Sopra"

#: src/tables/Filter.tsx:119
msgid "Show items with serial numbers greater than or equal to a given value"
msgstr "Mostra gli articoli con numeri di serie maggiori o uguali a un dato valore"

#: src/tables/Filter.tsx:128
msgid "Assigned to me"
msgstr "Assegnato a me"

#: src/tables/Filter.tsx:129
msgid "Show orders assigned to me"
msgstr "Mostra gli ordini assegnati a me"

#: src/tables/Filter.tsx:136
#: src/tables/sales/SalesOrderAllocationTable.tsx:85
msgid "Outstanding"
msgstr "Eccezionale"

#: src/tables/Filter.tsx:137
msgid "Show outstanding items"
msgstr "Mostra elementi inevasi"

#: src/tables/Filter.tsx:145
msgid "Show overdue items"
msgstr "Mostra elementi in ritardo"

#: src/tables/Filter.tsx:152
msgid "Minimum Date"
msgstr "Data minima"

#: src/tables/Filter.tsx:153
msgid "Show items after this date"
msgstr "Mostra gli elementi dopo questa data"

#: src/tables/Filter.tsx:161
msgid "Maximum Date"
msgstr "Data massima"

#: src/tables/Filter.tsx:162
msgid "Show items before this date"
msgstr "Mostra gli elementi dopo questa data"

#: src/tables/Filter.tsx:170
msgid "Created Before"
msgstr "Creato prima"

#: src/tables/Filter.tsx:171
msgid "Show items created before this date"
msgstr "Mostra gli articoli creati prima di questa data"

#: src/tables/Filter.tsx:179
msgid "Created After"
msgstr "Creato dopo"

#: src/tables/Filter.tsx:180
msgid "Show items created after this date"
msgstr "Mostra gli articoli creati dopo questa data"

#: src/tables/Filter.tsx:188
msgid "Start Date Before"
msgstr "Data di inizio Prima"

#: src/tables/Filter.tsx:189
msgid "Show items with a start date before this date"
msgstr "Mostra gli articoli con una data d'inizio prima di questa data"

#: src/tables/Filter.tsx:197
msgid "Start Date After"
msgstr "Data d'inizio dopo"

#: src/tables/Filter.tsx:198
msgid "Show items with a start date after this date"
msgstr "Mostra gli articoli con una data d'inizio dopo questa data"

#: src/tables/Filter.tsx:206
msgid "Target Date Before"
msgstr "Data obiettivo prima"

#: src/tables/Filter.tsx:207
msgid "Show items with a target date before this date"
msgstr "Mostra elementi con una data di destinazione prima di questa data"

#: src/tables/Filter.tsx:215
msgid "Target Date After"
msgstr "Data obiettivo dopo"

#: src/tables/Filter.tsx:216
msgid "Show items with a target date after this date"
msgstr "Mostra elementi con una data di destinazione dopo di questa data"

#: src/tables/Filter.tsx:224
msgid "Completed Before"
msgstr "Completato prima di"

#: src/tables/Filter.tsx:225
msgid "Show items completed before this date"
msgstr "Mostra gli elementi completati prima di questa data"

#: src/tables/Filter.tsx:233
msgid "Completed After"
msgstr "Completato dopo"

#: src/tables/Filter.tsx:234
msgid "Show items completed after this date"
msgstr "Mostra gli elementi completati dopo questa data"

#: src/tables/Filter.tsx:246
msgid "Has Project Code"
msgstr "Ha il codice progetto"

#: src/tables/Filter.tsx:247
msgid "Show orders with an assigned project code"
msgstr "Mostra gli ordini con un codice del progetto assegnato"

#: src/tables/Filter.tsx:256
msgid "Include Variants"
msgstr "Includi Varianti"

#: src/tables/Filter.tsx:257
msgid "Include results for part variants"
msgstr "Includi i risultati per le varianti dell'articolo"

#: src/tables/Filter.tsx:267
#: src/tables/part/PartPurchaseOrdersTable.tsx:133
msgid "Filter by order status"
msgstr "Filtra per stato ordine"

#: src/tables/Filter.tsx:279
msgid "Filter by project code"
msgstr "Filtra per codice progetto"

#: src/tables/Filter.tsx:312
msgid "Filter by responsible owner"
msgstr "Filtra per proprietario responsabile"

#: src/tables/Filter.tsx:328
#: src/tables/settings/ApiTokenTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:191
msgid "Filter by user"
msgstr "Filtra per utente"

#: src/tables/Filter.tsx:340
msgid "Filter by manufacturer"
msgstr "Filtra per produttore"

#: src/tables/Filter.tsx:353
msgid "Filter by supplier"
msgstr "Filtra per fornitore"

#: src/tables/Filter.tsx:366
msgid "Filter by user who created the order"
msgstr "Filtra per utente che ha creato l'ordine"

#: src/tables/Filter.tsx:374
msgid "Filter by user who issued the order"
msgstr "Filtra per utente che ha creato l'ordine"

#: src/tables/Filter.tsx:382
msgid "Filter by part category"
msgstr "Filtra per categoria articolo"

#: src/tables/Filter.tsx:393
msgid "Filter by stock location"
msgstr "Filtra per posizione magazzino"

#: src/tables/FilterSelectDrawer.tsx:59
msgid "Remove filter"
msgstr "Rimuovi filtro"

#: src/tables/FilterSelectDrawer.tsx:102
#: src/tables/FilterSelectDrawer.tsx:104
#: src/tables/FilterSelectDrawer.tsx:151
msgid "Select filter value"
msgstr "Seleziona valore filtro"

#: src/tables/FilterSelectDrawer.tsx:116
msgid "Enter filter value"
msgstr "Inserisci valore filtro"

#: src/tables/FilterSelectDrawer.tsx:138
msgid "Select date value"
msgstr "Seleziona valore data"

#: src/tables/FilterSelectDrawer.tsx:260
msgid "Select filter"
msgstr "Seleziona filtro"

#: src/tables/FilterSelectDrawer.tsx:261
msgid "Filter"
msgstr "Filtro"

#: src/tables/FilterSelectDrawer.tsx:313
#: src/tables/InvenTreeTableHeader.tsx:257
msgid "Table Filters"
msgstr "Filtri tabella"

#: src/tables/FilterSelectDrawer.tsx:346
msgid "Add Filter"
msgstr "Aggiungi filtro"

#: src/tables/FilterSelectDrawer.tsx:355
msgid "Clear Filters"
msgstr "Rimuovi filtri"

#: src/tables/InvenTreeTable.tsx:44
#: src/tables/InvenTreeTable.tsx:479
msgid "No records found"
msgstr "Nessun record trovato"

#: src/tables/InvenTreeTable.tsx:151
msgid "Error loading table options"
msgstr "Errore nel caricare le opzioni della tabella"

#: src/tables/InvenTreeTable.tsx:250
#~ msgid "Failed to load table options"
#~ msgstr "Failed to load table options"

#: src/tables/InvenTreeTable.tsx:510
#~ msgid "Are you sure you want to delete the selected records?"
#~ msgstr "Are you sure you want to delete the selected records?"

#: src/tables/InvenTreeTable.tsx:520
msgid "Server returned incorrect data type"
msgstr "Il server ha restituito un tipo di dati errato"

#: src/tables/InvenTreeTable.tsx:535
#~ msgid "Deleted records"
#~ msgstr "Deleted records"

#: src/tables/InvenTreeTable.tsx:536
#~ msgid "Records were deleted successfully"
#~ msgstr "Records were deleted successfully"

#: src/tables/InvenTreeTable.tsx:545
#~ msgid "Failed to delete records"
#~ msgstr "Failed to delete records"

#: src/tables/InvenTreeTable.tsx:552
#~ msgid "This action cannot be undone!"
#~ msgstr "This action cannot be undone!"

#: src/tables/InvenTreeTable.tsx:553
msgid "Error loading table data"
msgstr "Errore nel caricare i dati della tabella"

#: src/tables/InvenTreeTable.tsx:594
#: src/tables/InvenTreeTable.tsx:595
#~ msgid "Print actions"
#~ msgstr "Print actions"

#: src/tables/InvenTreeTable.tsx:655
#: src/tables/InvenTreeTable.tsx:656
#~ msgid "Barcode actions"
#~ msgstr "Barcode actions"

#: src/tables/InvenTreeTable.tsx:680
msgid "View details"
msgstr "Mostra dettagli"

#: src/tables/InvenTreeTable.tsx:712
#~ msgid "Table filters"
#~ msgstr "Table filters"

#: src/tables/InvenTreeTable.tsx:725
#~ msgid "Clear custom query filters"
#~ msgstr "Clear custom query filters"

#: src/tables/InvenTreeTableHeader.tsx:104
msgid "Delete Selected Items"
msgstr "Elimina elementi selezionati"

#: src/tables/InvenTreeTableHeader.tsx:108
msgid "Are you sure you want to delete the selected items?"
msgstr "Sei sicuro di voler eliminare gli elementi selezionati?"

#: src/tables/InvenTreeTableHeader.tsx:110
#: src/tables/plugin/PluginListTable.tsx:316
msgid "This action cannot be undone"
msgstr "Questa azione non può essere annullata"

#: src/tables/InvenTreeTableHeader.tsx:121
msgid "Items deleted"
msgstr "Elementi eliminati"

#: src/tables/InvenTreeTableHeader.tsx:126
msgid "Failed to delete items"
msgstr "Eliminazione degli elementi non riuscita"

#: src/tables/InvenTreeTableHeader.tsx:177
msgid "Custom table filters are active"
msgstr "I filtri tabella personalizzati sono attivi"

#: src/tables/InvenTreeTableHeader.tsx:203
#: src/tables/general/BarcodeScanTable.tsx:93
msgid "Delete selected records"
msgstr "Cancella record selezionati"

#: src/tables/InvenTreeTableHeader.tsx:223
msgid "Refresh data"
msgstr "Ricarica dati"

#: src/tables/InvenTreeTableHeader.tsx:269
msgid "Active Filters"
msgstr ""

#: src/tables/TableHoverCard.tsx:35
#~ msgid "item-{idx}"
#~ msgstr "item-{idx}"

#: src/tables/UploadAction.tsx:7
#~ msgid "Upload Data"
#~ msgstr "Upload Data"

#: src/tables/bom/BomTable.tsx:102
msgid "This BOM item is defined for a different parent"
msgstr "Questo elemento BOM è definito per un genitore diverso"

#: src/tables/bom/BomTable.tsx:118
msgid "Part Information"
msgstr "Informazioni Articolo"

#: src/tables/bom/BomTable.tsx:121
msgid "This BOM item has not been validated"
msgstr "Questo articolo della distinta base non è stato validato"

#: src/tables/bom/BomTable.tsx:228
msgid "Substitutes"
msgstr "Sostituti"

#: src/tables/bom/BomTable.tsx:297
#: src/tables/build/BuildLineTable.tsx:268
#: src/tables/part/PartTable.tsx:132
msgid "External stock"
msgstr "Scorte esterne"

#: src/tables/bom/BomTable.tsx:301
#~ msgid "Create BOM Item"
#~ msgstr "Create BOM Item"

#: src/tables/bom/BomTable.tsx:305
#: src/tables/build/BuildLineTable.tsx:231
msgid "Includes substitute stock"
msgstr "Comprende le scorte sostitutive"

#: src/tables/bom/BomTable.tsx:310
#~ msgid "Show asssmbled items"
#~ msgstr "Show asssmbled items"

#: src/tables/bom/BomTable.tsx:314
#: src/tables/build/BuildLineTable.tsx:241
#: src/tables/sales/SalesOrderLineItemTable.tsx:158
msgid "Includes variant stock"
msgstr "Comprende varianti scorte"

#: src/tables/bom/BomTable.tsx:331
#: src/tables/part/PartTable.tsx:101
msgid "Building"
msgstr "In produzione"

#: src/tables/bom/BomTable.tsx:331
#~ msgid "Edit Bom Item"
#~ msgstr "Edit Bom Item"

#: src/tables/bom/BomTable.tsx:333
#~ msgid "Bom item updated"
#~ msgstr "Bom item updated"

#: src/tables/bom/BomTable.tsx:340
#: src/tables/part/PartTable.tsx:158
#: src/tables/sales/SalesOrderLineItemTable.tsx:181
#: src/tables/stock/StockItemTable.tsx:223
msgid "Stock Information"
msgstr "Informazioni sulle scorte"

#: src/tables/bom/BomTable.tsx:348
#~ msgid "Delete Bom Item"
#~ msgstr "Delete Bom Item"

#: src/tables/bom/BomTable.tsx:349
#~ msgid "Bom item deleted"
#~ msgstr "Bom item deleted"

#: src/tables/bom/BomTable.tsx:351
#~ msgid "Are you sure you want to remove this BOM item?"
#~ msgstr "Are you sure you want to remove this BOM item?"

#: src/tables/bom/BomTable.tsx:354
#~ msgid "Validate BOM line"
#~ msgstr "Validate BOM line"

#: src/tables/bom/BomTable.tsx:374
#: src/tables/build/BuildLineTable.tsx:478
#: src/tables/build/BuildLineTable.tsx:518
msgid "Consumable item"
msgstr "Articolo consumabile"

#: src/tables/bom/BomTable.tsx:377
msgid "No available stock"
msgstr "Scorte non disponibili"

#: src/tables/bom/BomTable.tsx:395
#: src/tables/build/BuildLineTable.tsx:211
msgid "Show testable items"
msgstr "Mostra elementi testabili"

#: src/tables/bom/BomTable.tsx:400
msgid "Show trackable items"
msgstr "Mostra articoli tracciabili"

#: src/tables/bom/BomTable.tsx:405
#: src/tables/build/BuildLineTable.tsx:206
msgid "Show assembled items"
msgstr "Mostra articoli assemblati"

#: src/tables/bom/BomTable.tsx:410
#: src/tables/build/BuildLineTable.tsx:191
msgid "Show items with available stock"
msgstr "Mostra articoli con stock disponibile"

#: src/tables/bom/BomTable.tsx:415
msgid "Show items on order"
msgstr "Mostra gli articoli in ordine"

#: src/tables/bom/BomTable.tsx:419
msgid "Validated"
msgstr "Validato"

#: src/tables/bom/BomTable.tsx:420
msgid "Show validated items"
msgstr "Mostra articoli validati"

#: src/tables/bom/BomTable.tsx:424
#: src/tables/bom/UsedInTable.tsx:80
msgid "Inherited"
msgstr "Ereditato"

#: src/tables/bom/BomTable.tsx:425
#: src/tables/bom/UsedInTable.tsx:81
msgid "Show inherited items"
msgstr "Visualizza articoli ereditati"

#: src/tables/bom/BomTable.tsx:429
msgid "Allow Variants"
msgstr "Consenti Varianti"

#: src/tables/bom/BomTable.tsx:430
msgid "Show items which allow variant substitution"
msgstr "Mostra articoli che consentono la sostituzione della variante"

#: src/tables/bom/BomTable.tsx:434
#: src/tables/bom/UsedInTable.tsx:85
#: src/tables/build/BuildLineTable.tsx:200
msgid "Optional"
msgstr "Opzionale"

#: src/tables/bom/BomTable.tsx:435
#: src/tables/bom/UsedInTable.tsx:86
msgid "Show optional items"
msgstr "Mostra articoli opzionali"

#: src/tables/bom/BomTable.tsx:439
#: src/tables/build/BuildLineTable.tsx:195
msgid "Consumable"
msgstr "Consumabile"

#: src/tables/bom/BomTable.tsx:440
msgid "Show consumable items"
msgstr "Mostra articoli consumabili"

#: src/tables/bom/BomTable.tsx:444
#: src/tables/part/PartTable.tsx:300
msgid "Has Pricing"
msgstr "Ha Prezzi"

#: src/tables/bom/BomTable.tsx:445
msgid "Show items with pricing"
msgstr "Mostra articoli con prezzi"

#: src/tables/bom/BomTable.tsx:467
#: src/tables/bom/BomTable.tsx:596
msgid "Import BOM Data"
msgstr "Importa Dati BOM"

#: src/tables/bom/BomTable.tsx:477
#: src/tables/bom/BomTable.tsx:603
msgid "Add BOM Item"
msgstr "Aggiungi Articolo BOM"

#: src/tables/bom/BomTable.tsx:482
msgid "BOM item created"
msgstr "Articolo BOM creato"

#: src/tables/bom/BomTable.tsx:489
msgid "Edit BOM Item"
msgstr "Modifica Articolo BOM"

#: src/tables/bom/BomTable.tsx:491
msgid "BOM item updated"
msgstr "Articolo BOM aggiornato"

#: src/tables/bom/BomTable.tsx:498
msgid "Delete BOM Item"
msgstr "Elimina articolo BOM"

#: src/tables/bom/BomTable.tsx:499
msgid "BOM item deleted"
msgstr "Articolo BOM eliminato"

#: src/tables/bom/BomTable.tsx:519
msgid "BOM item validated"
msgstr "Articolo BOM validato"

#: src/tables/bom/BomTable.tsx:528
msgid "Failed to validate BOM item"
msgstr "Convalida dell'articolo BOM non riuscita"

#: src/tables/bom/BomTable.tsx:540
msgid "View BOM"
msgstr "Visualizza Distinta Base"

#: src/tables/bom/BomTable.tsx:551
msgid "Validate BOM Line"
msgstr "Valida Linea BOM"

#: src/tables/bom/BomTable.tsx:570
msgid "Edit Substitutes"
msgstr "Modifica Sostituti"

#: src/tables/bom/BomTable.tsx:624
msgid "Bill of materials cannot be edited, as the part is locked"
msgstr "La distinta base non può essere modificata, in quanto la parte è bloccata"

#: src/tables/bom/UsedInTable.tsx:34
#: src/tables/build/BuildLineTable.tsx:205
#: src/tables/part/ParametricPartTable.tsx:360
#: src/tables/part/PartBuildAllocationsTable.tsx:60
#: src/tables/part/PartTable.tsx:196
#: src/tables/stock/StockItemTable.tsx:334
msgid "Assembly"
msgstr "Assemblaggio"

#: src/tables/bom/UsedInTable.tsx:91
msgid "Show active assemblies"
msgstr "Mostra assemblaggi attivi"

#: src/tables/bom/UsedInTable.tsx:95
#: src/tables/part/PartTable.tsx:226
#: src/tables/part/PartVariantTable.tsx:30
msgid "Trackable"
msgstr "Tracciabile"

#: src/tables/bom/UsedInTable.tsx:96
msgid "Show trackable assemblies"
msgstr "Mostra assemblaggi tracciabili"

#: src/tables/build/BuildAllocatedStockTable.tsx:67
msgid "Allocated to Output"
msgstr "Assegnato all'output"

#: src/tables/build/BuildAllocatedStockTable.tsx:68
msgid "Show items allocated to a build output"
msgstr "Mostra gli articoli assegnati a un ordine di produzione"

#: src/tables/build/BuildAllocatedStockTable.tsx:73
#: src/tables/build/BuildOrderTable.tsx:197
#: src/tables/part/PartPurchaseOrdersTable.tsx:140
#: src/tables/sales/ReturnOrderTable.tsx:100
#: src/tables/sales/SalesOrderAllocationTable.tsx:101
#: src/tables/sales/SalesOrderTable.tsx:101
#~ msgid "Include orders for part variants"
#~ msgstr "Include orders for part variants"

#: src/tables/build/BuildAllocatedStockTable.tsx:97
#: src/tables/part/PartBuildAllocationsTable.tsx:84
#: src/tables/part/PartPurchaseOrdersTable.tsx:132
#: src/tables/part/PartSalesAllocationsTable.tsx:69
#: src/tables/sales/SalesOrderAllocationTable.tsx:119
msgid "Order Status"
msgstr "Stato dell'ordine"

#: src/tables/build/BuildAllocatedStockTable.tsx:164
#~ msgid "Edit Build Item"
#~ msgstr "Edit Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:167
#: src/tables/build/BuildLineTable.tsx:631
msgid "Edit Stock Allocation"
msgstr "Modifica Assegnazione Magazzino"

#: src/tables/build/BuildAllocatedStockTable.tsx:174
#~ msgid "Delete Build Item"
#~ msgstr "Delete Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:180
#: src/tables/build/BuildLineTable.tsx:644
msgid "Delete Stock Allocation"
msgstr "Elimina Assegnazione Magazzino"

#: src/tables/build/BuildAllocatedStockTable.tsx:232
msgid "Consume"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:59
#~ msgid "Show lines with available stock"
#~ msgstr "Show lines with available stock"

#: src/tables/build/BuildLineTable.tsx:106
msgid "View Stock Item"
msgstr "Vedi Elemento di Magazzino"

#: src/tables/build/BuildLineTable.tsx:181
msgid "Show fully allocated lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:186
msgid "Show fully consumed lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:189
#~ msgid "Show allocated lines"
#~ msgstr "Show allocated lines"

#: src/tables/build/BuildLineTable.tsx:196
msgid "Show consumable lines"
msgstr "Mostra linee consumabili"

#: src/tables/build/BuildLineTable.tsx:201
msgid "Show optional lines"
msgstr "Mostra linee opzionali"

#: src/tables/build/BuildLineTable.tsx:210
#: src/tables/part/PartTable.tsx:220
msgid "Testable"
msgstr "Testabile"

#: src/tables/build/BuildLineTable.tsx:215
#: src/tables/stock/StockItemTable.tsx:393
msgid "Tracked"
msgstr "Tracciato"

#: src/tables/build/BuildLineTable.tsx:216
msgid "Show tracked lines"
msgstr "Mostra linee tracciate"

#: src/tables/build/BuildLineTable.tsx:250
#: src/tables/sales/SalesOrderLineItemTable.tsx:164
msgid "In production"
msgstr "In produzione"

#: src/tables/build/BuildLineTable.tsx:278
msgid "Insufficient stock"
msgstr "Scorte insufficienti"

#: src/tables/build/BuildLineTable.tsx:294
#: src/tables/sales/SalesOrderLineItemTable.tsx:152
#: src/tables/stock/StockItemTable.tsx:192
msgid "No stock available"
msgstr "Nessuna Scorta Disponibile"

#: src/tables/build/BuildLineTable.tsx:360
msgid "Gets Inherited"
msgstr "Viene Ereditato"

#: src/tables/build/BuildLineTable.tsx:373
msgid "Unit Quantity"
msgstr "Quantità Unità"

#: src/tables/build/BuildLineTable.tsx:389
msgid "Required Quantity"
msgstr "Quantità richiesta"

#: src/tables/build/BuildLineTable.tsx:400
msgid "Setup Quantity"
msgstr "Imposta quantità"

#: src/tables/build/BuildLineTable.tsx:409
msgid "Attrition"
msgstr "Logoramento"

#: src/tables/build/BuildLineTable.tsx:417
msgid "Rounding Multiple"
msgstr "Arrotondamento Multiplo"

#: src/tables/build/BuildLineTable.tsx:426
msgid "BOM Information"
msgstr "Informazioni distinta base (BOM)"

#: src/tables/build/BuildLineTable.tsx:496
#: src/tables/part/PartBuildAllocationsTable.tsx:102
msgid "Fully allocated"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:544
#: src/tables/sales/SalesOrderLineItemTable.tsx:290
msgid "Create Build Order"
msgstr "Crea Ordine di Produzione"

#: src/tables/build/BuildLineTable.tsx:573
msgid "Auto allocation in progress"
msgstr "Assegnazione automatica in corso"

#: src/tables/build/BuildLineTable.tsx:576
#: src/tables/build/BuildLineTable.tsx:781
msgid "Auto Allocate Stock"
msgstr "Assegna Stock Automaticamente"

#: src/tables/build/BuildLineTable.tsx:577
msgid "Automatically allocate stock to this build according to the selected options"
msgstr "Assegna automaticamente lo stock a questa produzione in base alle opzioni selezionate"

#: src/tables/build/BuildLineTable.tsx:597
#: src/tables/build/BuildLineTable.tsx:611
#: src/tables/build/BuildLineTable.tsx:730
#: src/tables/build/BuildLineTable.tsx:831
#: src/tables/build/BuildOutputTable.tsx:359
#: src/tables/build/BuildOutputTable.tsx:364
msgid "Deallocate Stock"
msgstr "Disassegna Stock"

#: src/tables/build/BuildLineTable.tsx:613
msgid "Deallocate all untracked stock for this build order"
msgstr "Disassegna tutto lo stock non tracciato per questo ordine di produzione"

#: src/tables/build/BuildLineTable.tsx:615
msgid "Deallocate stock from the selected line item"
msgstr "Disassegna stock dalla riga selezionata"

#: src/tables/build/BuildLineTable.tsx:619
msgid "Stock has been deallocated"
msgstr "Lo stock è stato disassegnato"

#: src/tables/build/BuildLineTable.tsx:750
msgid "Build Stock"
msgstr "Crea scorta"

#: src/tables/build/BuildLineTable.tsx:763
#: src/tables/sales/SalesOrderLineItemTable.tsx:377
msgid "View Part"
msgstr "Mostra Articolo"

#: src/tables/build/BuildOrderTable.tsx:116
#~ msgid "Cascade"
#~ msgstr "Cascade"

#: src/tables/build/BuildOrderTable.tsx:117
#~ msgid "Display recursive child orders"
#~ msgstr "Display recursive child orders"

#: src/tables/build/BuildOrderTable.tsx:121
#~ msgid "Show active orders"
#~ msgstr "Show active orders"

#: src/tables/build/BuildOrderTable.tsx:122
#~ msgid "Show overdue status"
#~ msgstr "Show overdue status"

#: src/tables/build/BuildOrderTable.tsx:127
#~ msgid "Show outstanding orders"
#~ msgstr "Show outstanding orders"

#: src/tables/build/BuildOrderTable.tsx:139
#: src/tables/purchasing/PurchaseOrderTable.tsx:71
#: src/tables/sales/ReturnOrderTable.tsx:62
#: src/tables/sales/SalesOrderTable.tsx:69
#~ msgid "Filter by whether the purchase order has a project code"
#~ msgstr "Filter by whether the purchase order has a project code"

#: src/tables/build/BuildOrderTable.tsx:167
#: src/tables/purchasing/PurchaseOrderTable.tsx:83
#: src/tables/sales/ReturnOrderTable.tsx:79
#: src/tables/sales/SalesOrderTable.tsx:80
msgid "Has Target Date"
msgstr "Ha Data obiettivo"

#: src/tables/build/BuildOrderTable.tsx:168
#: src/tables/purchasing/PurchaseOrderTable.tsx:84
#: src/tables/sales/ReturnOrderTable.tsx:80
#: src/tables/sales/SalesOrderTable.tsx:81
msgid "Show orders with a target date"
msgstr "Mostra gli ordini con una data obiettivo"

#: src/tables/build/BuildOrderTable.tsx:173
#: src/tables/purchasing/PurchaseOrderTable.tsx:89
#: src/tables/sales/ReturnOrderTable.tsx:85
#: src/tables/sales/SalesOrderTable.tsx:86
msgid "Has Start Date"
msgstr "Ha data d'inizio"

#: src/tables/build/BuildOrderTable.tsx:174
#: src/tables/purchasing/PurchaseOrderTable.tsx:90
#: src/tables/sales/ReturnOrderTable.tsx:86
#: src/tables/sales/SalesOrderTable.tsx:87
msgid "Show orders with a start date"
msgstr "Mostra ordini con data d'inizio"

#: src/tables/build/BuildOrderTable.tsx:179
#~ msgid "Filter by user who issued this order"
#~ msgstr "Filter by user who issued this order"

#: src/tables/build/BuildOrderTestTable.tsx:85
#: src/tables/build/BuildOrderTestTable.tsx:163
#: src/tables/build/BuildOrderTestTable.tsx:283
#: src/tables/build/BuildOrderTestTable.tsx:297
#: src/tables/stock/StockItemTestResultTable.tsx:293
#: src/tables/stock/StockItemTestResultTable.tsx:365
#: src/tables/stock/StockItemTestResultTable.tsx:426
msgid "Add Test Result"
msgstr "Aggiungi risultato test"

#: src/tables/build/BuildOrderTestTable.tsx:92
#: src/tables/stock/StockItemTestResultTable.tsx:295
msgid "Test result added"
msgstr "Risultato del test aggiunto"

#: src/tables/build/BuildOrderTestTable.tsx:124
msgid "Add Test Results"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:134
msgid "Test results added"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:162
#: src/tables/stock/StockItemTestResultTable.tsx:191
msgid "No Result"
msgstr "Nessun risultato"

#: src/tables/build/BuildOrderTestTable.tsx:274
msgid "Show build outputs currently in production"
msgstr "Mostra gli ordini di produzione attualmente in produzione"

#: src/tables/build/BuildOutputTable.tsx:104
msgid "Build Output Stock Allocation"
msgstr "Assegnazione stock output di produzione"

#: src/tables/build/BuildOutputTable.tsx:161
#~ msgid "Delete build output"
#~ msgstr "Delete build output"

#: src/tables/build/BuildOutputTable.tsx:294
#: src/tables/build/BuildOutputTable.tsx:479
msgid "Add Build Output"
msgstr "Nuova Produzione"

#: src/tables/build/BuildOutputTable.tsx:297
msgid "Build output created"
msgstr "Ordine di produzione creato"

#: src/tables/build/BuildOutputTable.tsx:304
#~ msgid "Edit build output"
#~ msgstr "Edit build output"

#: src/tables/build/BuildOutputTable.tsx:350
#: src/tables/build/BuildOutputTable.tsx:553
msgid "Edit Build Output"
msgstr "Modifica Output di Produzione"

#: src/tables/build/BuildOutputTable.tsx:366
msgid "This action will deallocate all stock from the selected build output"
msgstr "Questa azione disallocherà tutto lo stock dall'output di produzione selezionato"

#: src/tables/build/BuildOutputTable.tsx:391
msgid "Serialize Build Output"
msgstr "Serializza ordine di produzione"

#: src/tables/build/BuildOutputTable.tsx:409
#: src/tables/stock/StockItemTable.tsx:329
msgid "Filter by stock status"
msgstr "Filtra per stato delle scorte"

#: src/tables/build/BuildOutputTable.tsx:446
msgid "Complete selected outputs"
msgstr "Completa la produzione selezionata"

#: src/tables/build/BuildOutputTable.tsx:457
msgid "Scrap selected outputs"
msgstr "Scarta gli output selezionati"

#: src/tables/build/BuildOutputTable.tsx:468
msgid "Cancel selected outputs"
msgstr "Annulla gli output selezionati"

#: src/tables/build/BuildOutputTable.tsx:498
msgid "View Build Output"
msgstr "Vedi Output di Produzione"

#: src/tables/build/BuildOutputTable.tsx:504
msgid "Allocate"
msgstr "Assegna"

#: src/tables/build/BuildOutputTable.tsx:505
msgid "Allocate stock to build output"
msgstr "Assegna gli elementi di magazzino a questo output di produzione"

#: src/tables/build/BuildOutputTable.tsx:518
msgid "Deallocate"
msgstr "Dealloca"

#: src/tables/build/BuildOutputTable.tsx:519
msgid "Deallocate stock from build output"
msgstr "Non assegnare stock all'output di produzione"

#: src/tables/build/BuildOutputTable.tsx:533
msgid "Serialize build output"
msgstr "Serializza ordine di produzione"

#: src/tables/build/BuildOutputTable.tsx:544
msgid "Complete build output"
msgstr "Completa output di produzione"

#: src/tables/build/BuildOutputTable.tsx:560
msgid "Scrap"
msgstr "Scarta"

#: src/tables/build/BuildOutputTable.tsx:561
msgid "Scrap build output"
msgstr "Scarta gli ordini di produzione"

#: src/tables/build/BuildOutputTable.tsx:571
msgid "Cancel build output"
msgstr "Cancella gli ordini di produzione"

#: src/tables/build/BuildOutputTable.tsx:620
msgid "Allocated Lines"
msgstr "Elementi Assegnati"

#: src/tables/build/BuildOutputTable.tsx:635
msgid "Required Tests"
msgstr "Test Richiesti"

#: src/tables/build/BuildOutputTable.tsx:710
msgid "External Build"
msgstr "Produzione Esterna"

#: src/tables/build/BuildOutputTable.tsx:712
msgid "This build order is fulfilled by an external purchase order"
msgstr "Questo ordine di produzione viene evaso tramite un ordine di acquisto esterno"

#: src/tables/company/AddressTable.tsx:122
#: src/tables/company/AddressTable.tsx:187
msgid "Add Address"
msgstr "Aggiungi indirizzo"

#: src/tables/company/AddressTable.tsx:127
msgid "Address created"
msgstr "Indirizzo creato"

#: src/tables/company/AddressTable.tsx:136
msgid "Edit Address"
msgstr "Modifica indirizzo"

#: src/tables/company/AddressTable.tsx:144
msgid "Delete Address"
msgstr "Elimina indirizzo"

#: src/tables/company/AddressTable.tsx:145
msgid "Are you sure you want to delete this address?"
msgstr "Sei sicuro di voler eliminare questo indirizzo?"

#: src/tables/company/CompanyTable.tsx:70
#: src/tables/company/CompanyTable.tsx:120
msgid "Add Company"
msgstr "Aggiungi azienda"

#: src/tables/company/CompanyTable.tsx:71
#~ msgid "New Company"
#~ msgstr "New Company"

#: src/tables/company/CompanyTable.tsx:92
msgid "Show active companies"
msgstr "Mostra aziende attive"

#: src/tables/company/CompanyTable.tsx:97
msgid "Show companies which are suppliers"
msgstr "Mostra le aziende che sono fornitori"

#: src/tables/company/CompanyTable.tsx:102
msgid "Show companies which are manufacturers"
msgstr "Mostra le aziende che sono produttori"

#: src/tables/company/CompanyTable.tsx:107
msgid "Show companies which are customers"
msgstr "Mostra le aziende che sono clienti"

#: src/tables/company/ContactTable.tsx:99
msgid "Edit Contact"
msgstr "Modifica contatto"

#: src/tables/company/ContactTable.tsx:106
msgid "Add Contact"
msgstr "Aggiungi contatto"

#: src/tables/company/ContactTable.tsx:117
msgid "Delete Contact"
msgstr "Elimina contatto"

#: src/tables/company/ContactTable.tsx:158
msgid "Add contact"
msgstr "Aggiungi contatto"

#: src/tables/general/AttachmentTable.tsx:108
msgid "Uploading file {filename}"
msgstr "Caricamento file {filename}"

#: src/tables/general/AttachmentTable.tsx:139
#~ msgid "File uploaded"
#~ msgstr "File uploaded"

#: src/tables/general/AttachmentTable.tsx:140
#~ msgid "File {0} uploaded successfully"
#~ msgstr "File {0} uploaded successfully"

#: src/tables/general/AttachmentTable.tsx:160
#: src/tables/general/AttachmentTable.tsx:174
msgid "Uploading File"
msgstr "Caricamento file"

#: src/tables/general/AttachmentTable.tsx:185
msgid "File Uploaded"
msgstr "File caricato"

#: src/tables/general/AttachmentTable.tsx:186
msgid "File {name} uploaded successfully"
msgstr "File {name} caricato con successo"

#: src/tables/general/AttachmentTable.tsx:202
msgid "File could not be uploaded"
msgstr "Non è stato possibile caricare il file"

#: src/tables/general/AttachmentTable.tsx:253
msgid "Upload Attachment"
msgstr "Carica allegato"

#: src/tables/general/AttachmentTable.tsx:254
#~ msgid "Upload attachment"
#~ msgstr "Upload attachment"

#: src/tables/general/AttachmentTable.tsx:263
msgid "Edit Attachment"
msgstr "Modifica allegato"

#: src/tables/general/AttachmentTable.tsx:277
msgid "Delete Attachment"
msgstr "Elimina allegato"

#: src/tables/general/AttachmentTable.tsx:287
msgid "Is Link"
msgstr "È un Collegamento"

#: src/tables/general/AttachmentTable.tsx:288
msgid "Show link attachments"
msgstr "Mostra link allegati"

#: src/tables/general/AttachmentTable.tsx:292
msgid "Is File"
msgstr "E' un file"

#: src/tables/general/AttachmentTable.tsx:293
msgid "Show file attachments"
msgstr "Mostra file allegati"

#: src/tables/general/AttachmentTable.tsx:302
msgid "Add attachment"
msgstr "Aggiungi allegato"

#: src/tables/general/AttachmentTable.tsx:313
msgid "Add external link"
msgstr "Aggiungi collegamento esterno"

#: src/tables/general/AttachmentTable.tsx:361
msgid "No attachments found"
msgstr "Nessun allegato trovato"

#: src/tables/general/AttachmentTable.tsx:400
msgid "Drag attachment file here to upload"
msgstr "Trascina qui il file allegato per caricare"

#: src/tables/general/BarcodeScanTable.tsx:35
msgid "Item"
msgstr "Articolo"

#: src/tables/general/BarcodeScanTable.tsx:50
msgid "Model"
msgstr "Modello"

#: src/tables/general/BarcodeScanTable.tsx:60
#: src/tables/settings/BarcodeScanHistoryTable.tsx:75
#: src/tables/settings/EmailTable.tsx:105
#: src/tables/settings/ErrorTable.tsx:59
msgid "Timestamp"
msgstr "Marca temporale"

#: src/tables/general/BarcodeScanTable.tsx:75
msgid "View Item"
msgstr "Visualizza Articolo"

#: src/tables/general/ExtraLineItemTable.tsx:91
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:288
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:397
#: src/tables/sales/ReturnOrderLineItemTable.tsx:80
#: src/tables/sales/ReturnOrderLineItemTable.tsx:183
#: src/tables/sales/SalesOrderLineItemTable.tsx:231
#: src/tables/sales/SalesOrderLineItemTable.tsx:334
msgid "Add Line Item"
msgstr "Aggiungi linea articolo"

#: src/tables/general/ExtraLineItemTable.tsx:104
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:309
#: src/tables/sales/ReturnOrderLineItemTable.tsx:93
#: src/tables/sales/SalesOrderLineItemTable.tsx:250
msgid "Edit Line Item"
msgstr "Modifica linea Articolo"

#: src/tables/general/ExtraLineItemTable.tsx:113
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:318
#: src/tables/sales/ReturnOrderLineItemTable.tsx:102
#: src/tables/sales/SalesOrderLineItemTable.tsx:259
msgid "Delete Line Item"
msgstr "Cancella Linea Articolo"

#: src/tables/general/ExtraLineItemTable.tsx:151
msgid "Add Extra Line Item"
msgstr "Aggiungi Voci di riga extra"

#: src/tables/machine/MachineListTable.tsx:206
msgid "Machine restarted"
msgstr "Macchina riavviata"

#: src/tables/machine/MachineListTable.tsx:216
#: src/tables/machine/MachineListTable.tsx:264
msgid "Edit machine"
msgstr "Modifica macchina"

#: src/tables/machine/MachineListTable.tsx:230
#: src/tables/machine/MachineListTable.tsx:268
msgid "Delete machine"
msgstr "Elimina macchina"

#: src/tables/machine/MachineListTable.tsx:231
msgid "Machine successfully deleted."
msgstr "Macchina eliminata con successo."

#. placeholder {0}: machine?.name ?? 'unknown'
#: src/tables/machine/MachineListTable.tsx:235
msgid "Are you sure you want to remove the machine \"{0}\"?"
msgstr "È sicuro di voler togliere la macchina \"{0}\"?"

#: src/tables/machine/MachineListTable.tsx:252
msgid "Machine"
msgstr "Macchina"

#: src/tables/machine/MachineListTable.tsx:257
#: src/tables/machine/MachineListTable.tsx:444
msgid "Restart required"
msgstr "Riavvio richiesto"

#: src/tables/machine/MachineListTable.tsx:261
msgid "Machine Actions"
msgstr "Azioni Macchina"

#: src/tables/machine/MachineListTable.tsx:273
msgid "Restart"
msgstr "Riavvia"

#: src/tables/machine/MachineListTable.tsx:275
msgid "Restart machine"
msgstr "Riavvia macchina"

#: src/tables/machine/MachineListTable.tsx:277
msgid "manual restart required"
msgstr "riavvio manuale richiesto"

#: src/tables/machine/MachineListTable.tsx:291
#~ msgid "Machine information"
#~ msgstr "Machine information"

#: src/tables/machine/MachineListTable.tsx:295
msgid "Machine Information"
msgstr "Informazioni sulla macchina"

#: src/tables/machine/MachineListTable.tsx:305
#: src/tables/machine/MachineListTable.tsx:611
msgid "Machine Type"
msgstr "Tipo di macchina"

#: src/tables/machine/MachineListTable.tsx:318
msgid "Machine Driver"
msgstr "Driver Macchina"

#: src/tables/machine/MachineListTable.tsx:333
msgid "Initialized"
msgstr "Inizializzato"

#: src/tables/machine/MachineListTable.tsx:362
#: src/tables/machine/MachineTypeTable.tsx:289
msgid "No errors reported"
msgstr "Nessun errore segnalato"

#: src/tables/machine/MachineListTable.tsx:381
msgid "Machine Settings"
msgstr "Impostazioni macchina"

#: src/tables/machine/MachineListTable.tsx:397
msgid "Driver Settings"
msgstr "Impostazioni driver"

#: src/tables/machine/MachineListTable.tsx:494
#~ msgid "Create machine"
#~ msgstr "Create machine"

#: src/tables/machine/MachineListTable.tsx:517
msgid "Add Machine"
msgstr "Aggiungi macchina"

#: src/tables/machine/MachineListTable.tsx:559
msgid "Add machine"
msgstr "Aggiungi macchina"

#: src/tables/machine/MachineListTable.tsx:561
#~ msgid "Machine detail"
#~ msgstr "Machine detail"

#: src/tables/machine/MachineListTable.tsx:573
msgid "Machine Detail"
msgstr "Dettagli Macchina"

#: src/tables/machine/MachineListTable.tsx:620
msgid "Driver"
msgstr "Driver"

#: src/tables/machine/MachineTypeTable.tsx:77
msgid "Builtin driver"
msgstr "Driver integrato"

#: src/tables/machine/MachineTypeTable.tsx:95
msgid "Not Found"
msgstr "Non Trovato"

#: src/tables/machine/MachineTypeTable.tsx:98
msgid "Machine type not found."
msgstr "Tipo di macchina non trovato."

#: src/tables/machine/MachineTypeTable.tsx:99
#~ msgid "Machine type information"
#~ msgstr "Machine type information"

#: src/tables/machine/MachineTypeTable.tsx:108
msgid "Machine Type Information"
msgstr "Informazioni sul tipo di macchina"

#: src/tables/machine/MachineTypeTable.tsx:123
#: src/tables/machine/MachineTypeTable.tsx:237
msgid "Slug"
msgstr "Slug"

#: src/tables/machine/MachineTypeTable.tsx:134
#: src/tables/machine/MachineTypeTable.tsx:258
msgid "Provider plugin"
msgstr "Plugin fornitore"

#: src/tables/machine/MachineTypeTable.tsx:146
#: src/tables/machine/MachineTypeTable.tsx:270
msgid "Provider file"
msgstr "File provider"

#: src/tables/machine/MachineTypeTable.tsx:148
#~ msgid "Available drivers"
#~ msgstr "Available drivers"

#: src/tables/machine/MachineTypeTable.tsx:161
msgid "Available Drivers"
msgstr "Driver disponibili"

#: src/tables/machine/MachineTypeTable.tsx:216
msgid "Machine driver not found."
msgstr "Driver macchina non trovato."

#: src/tables/machine/MachineTypeTable.tsx:224
msgid "Machine driver information"
msgstr "Informazioni driver macchina"

#: src/tables/machine/MachineTypeTable.tsx:244
msgid "Machine type"
msgstr "Tipo di macchina"

#: src/tables/machine/MachineTypeTable.tsx:338
#~ msgid "Machine type detail"
#~ msgstr "Machine type detail"

#: src/tables/machine/MachineTypeTable.tsx:344
msgid "Builtin type"
msgstr "Tipo incorporato"

#: src/tables/machine/MachineTypeTable.tsx:348
#~ msgid "Machine driver detail"
#~ msgstr "Machine driver detail"

#: src/tables/machine/MachineTypeTable.tsx:353
msgid "Machine Type Detail"
msgstr "Dettagli sul Tipo di Macchina"

#: src/tables/machine/MachineTypeTable.tsx:363
msgid "Machine Driver Detail"
msgstr "Dettagli sul Driver di Macchina"

#: src/tables/notifications/NotificationTable.tsx:26
msgid "Age"
msgstr "Età"

#: src/tables/notifications/NotificationTable.tsx:37
msgid "Notification"
msgstr "Notifiche"

#: src/tables/notifications/NotificationTable.tsx:41
#: src/tables/plugin/PluginErrorTable.tsx:37
#: src/tables/settings/ErrorTable.tsx:50
msgid "Message"
msgstr "Messaggio"

#: src/tables/part/ParametricPartTable.tsx:78
msgid "Click to edit"
msgstr "Clicca per modificare"

#: src/tables/part/ParametricPartTable.tsx:82
#~ msgid "Edit parameter"
#~ msgstr "Edit parameter"

#: src/tables/part/ParametricPartTable.tsx:240
msgid "Add Part Parameter"
msgstr "Aggiungi Parametro Articolo"

#: src/tables/part/ParametricPartTable.tsx:254
#: src/tables/part/PartParameterTable.tsx:172
#: src/tables/part/PartParameterTable.tsx:195
msgid "Edit Part Parameter"
msgstr "Modifica Parametro Articolo"

#: src/tables/part/ParametricPartTable.tsx:351
msgid "Show active parts"
msgstr "Mostra articoli attivi"

#: src/tables/part/ParametricPartTable.tsx:356
msgid "Show locked parts"
msgstr "Mostra articoli bloccati"

#: src/tables/part/ParametricPartTable.tsx:361
msgid "Show assembly parts"
msgstr "Mostra articoli assemblati"

#: src/tables/part/ParametricPartTableFilters.tsx:67
msgid "True"
msgstr "Vero"

#: src/tables/part/ParametricPartTableFilters.tsx:68
msgid "False"
msgstr "Falso"

#: src/tables/part/ParametricPartTableFilters.tsx:73
#: src/tables/part/ParametricPartTableFilters.tsx:97
msgid "Select a choice"
msgstr "Seleziona una scelta"

#: src/tables/part/ParametricPartTableFilters.tsx:116
msgid "Enter a value"
msgstr "Inserisci un valore"

#: src/tables/part/PartBuildAllocationsTable.tsx:64
msgid "Assembly IPN"
msgstr "IPN assemblato"

#: src/tables/part/PartBuildAllocationsTable.tsx:73
msgid "Part IPN"
msgstr "IPN Articolo"

#: src/tables/part/PartBuildAllocationsTable.tsx:91
msgid "Required Stock"
msgstr "Giacenza Richiesta"

#: src/tables/part/PartBuildAllocationsTable.tsx:124
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:354
msgid "View Build Order"
msgstr "Vedi Ordine di Produzione"

#: src/tables/part/PartCategoryTable.tsx:51
msgid "You are subscribed to notifications for this category"
msgstr "Sei iscritto alle notifiche per questa categoria"

#: src/tables/part/PartCategoryTable.tsx:84
#: src/tables/part/PartTable.tsx:208
msgid "Include Subcategories"
msgstr "Includi sottocategorie"

#: src/tables/part/PartCategoryTable.tsx:85
msgid "Include subcategories in results"
msgstr "Includi sottocategorie nei risultati"

#: src/tables/part/PartCategoryTable.tsx:90
msgid "Show structural categories"
msgstr "Mostra categorie strutturali"

#: src/tables/part/PartCategoryTable.tsx:95
msgid "Show categories to which the user is subscribed"
msgstr "Mostra le categorie a cui l'utente è iscritto"

#: src/tables/part/PartCategoryTable.tsx:104
msgid "New Part Category"
msgstr "Nuova categoria articolo"

#: src/tables/part/PartCategoryTable.tsx:130
msgid "Set Parent Category"
msgstr "Imposta categoria superiore"

#: src/tables/part/PartCategoryTable.tsx:148
#: src/tables/stock/StockLocationTable.tsx:147
msgid "Set Parent"
msgstr "Imposta genitore"

#: src/tables/part/PartCategoryTable.tsx:150
msgid "Set parent category for the selected items"
msgstr "Imposta la categoria superiore per gli elementi selezionati"

#: src/tables/part/PartCategoryTable.tsx:161
msgid "Add Part Category"
msgstr "Aggiungi Categoria Articolo"

#: src/tables/part/PartCategoryTemplateTable.tsx:42
#: src/tables/part/PartCategoryTemplateTable.tsx:136
msgid "Add Category Parameter"
msgstr "Aggiungi Parametro Categoria"

#: src/tables/part/PartCategoryTemplateTable.tsx:50
msgid "Edit Category Parameter"
msgstr "Modifica Parametro Categoria"

#: src/tables/part/PartCategoryTemplateTable.tsx:58
msgid "Delete Category Parameter"
msgstr "Elimina Parametro Categoria"

#: src/tables/part/PartCategoryTemplateTable.tsx:80
msgid "Parameter Template"
msgstr "Modello Parametro"

#: src/tables/part/PartCategoryTemplateTable.tsx:93
#~ msgid "[{0}]"
#~ msgstr "[{0}]"

#: src/tables/part/PartParameterTable.tsx:108
msgid "Internal Units"
msgstr "Unità Interne"

#: src/tables/part/PartParameterTable.tsx:127
#: src/tables/part/PartParameterTable.tsx:146
msgid "Updated By"
msgstr "Aggiornato da"

#: src/tables/part/PartParameterTable.tsx:147
msgid "Filter by user who last updated the parameter"
msgstr "Filtra per utente che per ultimo ha aggiornato il parametro"

#: src/tables/part/PartParameterTable.tsx:156
msgid "New Part Parameter"
msgstr "Nuovo Parametro Articolo"

#: src/tables/part/PartParameterTable.tsx:181
#: src/tables/part/PartParameterTable.tsx:203
msgid "Delete Part Parameter"
msgstr "Elimina Parametro Articolo"

#: src/tables/part/PartParameterTable.tsx:221
msgid "Add parameter"
msgstr "Aggiungi parametro"

#: src/tables/part/PartParameterTable.tsx:240
msgid "Part parameters cannot be edited, as the part is locked"
msgstr "I parametri dell'articolo non possono essere modificati, poiché l'articolo è bloccata"

#: src/tables/part/PartParameterTemplateTable.tsx:36
msgid "Checkbox"
msgstr "Casella di spunta"

#: src/tables/part/PartParameterTemplateTable.tsx:37
msgid "Show checkbox templates"
msgstr "Mostra i modelli di casella di spunta"

#: src/tables/part/PartParameterTemplateTable.tsx:41
msgid "Has choices"
msgstr "Ha scelte"

#: src/tables/part/PartParameterTemplateTable.tsx:42
msgid "Show templates with choices"
msgstr "Mostra modelli con scelte"

#: src/tables/part/PartParameterTemplateTable.tsx:46
#: src/tables/part/PartTable.tsx:232
msgid "Has Units"
msgstr "Ha Unità"

#: src/tables/part/PartParameterTemplateTable.tsx:47
msgid "Show templates with units"
msgstr "Mostra modelli con unità"

#: src/tables/part/PartParameterTemplateTable.tsx:91
#: src/tables/part/PartParameterTemplateTable.tsx:166
msgid "Add Parameter Template"
msgstr "Aggiungi Modello Parametro"

#: src/tables/part/PartParameterTemplateTable.tsx:105
msgid "Duplicate Parameter Template"
msgstr "Duplica Modello Parametro"

#: src/tables/part/PartParameterTemplateTable.tsx:117
msgid "Edit Parameter Template"
msgstr "Modifica Modello Parametro"

#: src/tables/part/PartParameterTemplateTable.tsx:128
msgid "Delete Parameter Template"
msgstr "Elimina Modello Parametro"

#: src/tables/part/PartParameterTemplateTable.tsx:141
#~ msgid "Add parameter template"
#~ msgstr "Add parameter template"

#: src/tables/part/PartPurchaseOrdersTable.tsx:79
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:190
msgid "Total Quantity"
msgstr "Quantità Totale"

#: src/tables/part/PartPurchaseOrdersTable.tsx:123
msgid "Show pending orders"
msgstr "Mostra ordini in sospeso"

#: src/tables/part/PartPurchaseOrdersTable.tsx:128
msgid "Show received items"
msgstr "Visualizza articoli ricevuti"

#: src/tables/part/PartSalesAllocationsTable.tsx:90
msgid "View Sales Order"
msgstr "Vedi Ordine di Vendita"

#: src/tables/part/PartTable.tsx:86
msgid "Minimum stock"
msgstr "Giacenza minima"

#: src/tables/part/PartTable.tsx:185
msgid "Filter by part active status"
msgstr "Filtra per stato attivo articolo"

#: src/tables/part/PartTable.tsx:191
msgid "Filter by part locked status"
msgstr "Filtra per stato di blocco articolo"

#: src/tables/part/PartTable.tsx:197
msgid "Filter by assembly attribute"
msgstr "Filtra per attributo assemblaggio"

#: src/tables/part/PartTable.tsx:202
msgid "BOM Valid"
msgstr "Distinta base valida"

#: src/tables/part/PartTable.tsx:203
msgid "Filter by parts with a valid BOM"
msgstr "Filtra per articoli che hanno una distinta base validata"

#: src/tables/part/PartTable.tsx:209
msgid "Include parts in subcategories"
msgstr "Includi articoli nelle sotto categorie"

#: src/tables/part/PartTable.tsx:215
msgid "Filter by component attribute"
msgstr "Filtra per attributo componente"

#: src/tables/part/PartTable.tsx:221
msgid "Filter by testable attribute"
msgstr "Filtra per attributo testabile"

#: src/tables/part/PartTable.tsx:227
msgid "Filter by trackable attribute"
msgstr "Filtra per attributo tracciabile"

#: src/tables/part/PartTable.tsx:233
msgid "Filter by parts which have units"
msgstr "Filtra per articoli che hanno unità"

#: src/tables/part/PartTable.tsx:238
msgid "Has IPN"
msgstr "Ha IPN"

#: src/tables/part/PartTable.tsx:239
msgid "Filter by parts which have an internal part number"
msgstr "Filtra per articoli che hanno un numero interno"

#: src/tables/part/PartTable.tsx:244
msgid "Has Stock"
msgstr "Ha Scorte"

#: src/tables/part/PartTable.tsx:245
msgid "Filter by parts which have stock"
msgstr "Filtra per articoli che hanno scorte"

#: src/tables/part/PartTable.tsx:251
msgid "Filter by parts which have low stock"
msgstr "Filtra per articoli che hanno bassa scorta"

#: src/tables/part/PartTable.tsx:256
msgid "Purchaseable"
msgstr "Acquistabile"

#: src/tables/part/PartTable.tsx:257
msgid "Filter by parts which are purchaseable"
msgstr "Filtra per articoli che sono acquistabili"

#: src/tables/part/PartTable.tsx:262
msgid "Salable"
msgstr "Vendibile"

#: src/tables/part/PartTable.tsx:263
msgid "Filter by parts which are salable"
msgstr "Filtra per articoli che sono vendibili"

#: src/tables/part/PartTable.tsx:268
#: src/tables/part/PartTable.tsx:272
#: src/tables/part/PartVariantTable.tsx:25
msgid "Virtual"
msgstr "Virtuale"

#: src/tables/part/PartTable.tsx:269
msgid "Filter by parts which are virtual"
msgstr "Filtra per articoli che sono virtuali"

#: src/tables/part/PartTable.tsx:273
msgid "Not Virtual"
msgstr "Non Virtuale"

#: src/tables/part/PartTable.tsx:278
msgid "Is Template"
msgstr "È Modello"

#: src/tables/part/PartTable.tsx:279
msgid "Filter by parts which are templates"
msgstr "Filtra per articoli che sono modelli"

#: src/tables/part/PartTable.tsx:284
msgid "Is Variant"
msgstr "È una Variante"

#: src/tables/part/PartTable.tsx:285
msgid "Filter by parts which are variants"
msgstr "Filtra per articoli che sono varianti"

#: src/tables/part/PartTable.tsx:290
msgid "Is Revision"
msgstr "È una revisione"

#: src/tables/part/PartTable.tsx:291
msgid "Filter by parts which are revisions"
msgstr "Filtra per articoli che sono revisioni"

#: src/tables/part/PartTable.tsx:295
msgid "Has Revisions"
msgstr "Ha revisioni"

#: src/tables/part/PartTable.tsx:296
msgid "Filter by parts which have revisions"
msgstr "Filtra per articoli che hanno revisioni"

#: src/tables/part/PartTable.tsx:301
msgid "Filter by parts which have pricing information"
msgstr "Filtra per articoli che hanno informazioni sui prezzi"

#: src/tables/part/PartTable.tsx:307
msgid "Filter by parts which have available stock"
msgstr "Filtra per articoli che hanno scorte disponibili"

#: src/tables/part/PartTable.tsx:313
msgid "Filter by parts to which the user is subscribed"
msgstr "Filtra per articoli a cui l'utente è iscritto"

#: src/tables/part/PartTable.tsx:322
#~ msgid "Has Stocktake"
#~ msgstr "Has Stocktake"

#: src/tables/part/PartTable.tsx:323
#~ msgid "Filter by parts which have stocktake information"
#~ msgstr "Filter by parts which have stocktake information"

#: src/tables/part/PartTable.tsx:363
#: src/tables/part/PartTable.tsx:397
msgid "Set Category"
msgstr "Imposta Categoria"

#: src/tables/part/PartTable.tsx:399
msgid "Set category for selected parts"
msgstr "Imposta categoria per gli articoli selezionati"

#: src/tables/part/PartTable.tsx:409
msgid "Order selected parts"
msgstr "Ordina articoli selezionati"

#: src/tables/part/PartTestTemplateTable.tsx:56
msgid "Test is defined for a parent template part"
msgstr "Il test è definito per un modello articolo superiore"

#: src/tables/part/PartTestTemplateTable.tsx:70
msgid "Template Details"
msgstr "Dettagli modello"

#: src/tables/part/PartTestTemplateTable.tsx:80
msgid "Results"
msgstr "Risultati"

#: src/tables/part/PartTestTemplateTable.tsx:113
msgid "Show required tests"
msgstr "Mostra i test richiesti"

#: src/tables/part/PartTestTemplateTable.tsx:118
msgid "Show enabled tests"
msgstr "Mostra test abilitati"

#: src/tables/part/PartTestTemplateTable.tsx:122
msgid "Requires Value"
msgstr "Valore richiesto"

#: src/tables/part/PartTestTemplateTable.tsx:123
msgid "Show tests that require a value"
msgstr "Mostra test che richiedono un valore"

#: src/tables/part/PartTestTemplateTable.tsx:127
msgid "Requires Attachment"
msgstr "Richiede Allegato"

#: src/tables/part/PartTestTemplateTable.tsx:128
msgid "Show tests that require an attachment"
msgstr "Mostra test che richiedono un allegato"

#: src/tables/part/PartTestTemplateTable.tsx:132
msgid "Include Inherited"
msgstr "Includi Ereditato"

#: src/tables/part/PartTestTemplateTable.tsx:133
msgid "Show tests from inherited templates"
msgstr "Mostra test da modelli ereditati"

#: src/tables/part/PartTestTemplateTable.tsx:137
msgid "Has Results"
msgstr "Ha Risultati"

#: src/tables/part/PartTestTemplateTable.tsx:138
msgid "Show tests which have recorded results"
msgstr "Mostra i test che hanno risultati registrati"

#: src/tables/part/PartTestTemplateTable.tsx:160
#: src/tables/part/PartTestTemplateTable.tsx:243
msgid "Add Test Template"
msgstr "Aggiungi Modello di Test"

#: src/tables/part/PartTestTemplateTable.tsx:176
msgid "Edit Test Template"
msgstr "Modifica Modello di Test"

#: src/tables/part/PartTestTemplateTable.tsx:187
msgid "Delete Test Template"
msgstr "Elimina Modello di Test"

#: src/tables/part/PartTestTemplateTable.tsx:189
msgid "This action cannot be reversed"
msgstr "Questa azione non può essere annullata"

#: src/tables/part/PartTestTemplateTable.tsx:191
msgid "Any tests results associated with this template will be deleted"
msgstr "Tutti i risultati dei test associati a questo modello verranno eliminati"

#: src/tables/part/PartTestTemplateTable.tsx:209
msgid "View Parent Part"
msgstr "Visualizza Articolo Genitore"

#: src/tables/part/PartTestTemplateTable.tsx:263
msgid "Part templates cannot be edited, as the part is locked"
msgstr "I modelli dell'articolo non possono essere modificati, poiché l'articolo è bloccato"

#: src/tables/part/PartThumbTable.tsx:224
msgid "Select"
msgstr "Seleziona"

#: src/tables/part/PartVariantTable.tsx:16
msgid "Show active variants"
msgstr "Mostra varianti attive"

#: src/tables/part/PartVariantTable.tsx:20
msgid "Template"
msgstr "Modello"

#: src/tables/part/PartVariantTable.tsx:21
msgid "Show template variants"
msgstr "Mostra varianti modello"

#: src/tables/part/PartVariantTable.tsx:26
msgid "Show virtual variants"
msgstr "Mostra varianti virtuali"

#: src/tables/part/PartVariantTable.tsx:31
msgid "Show trackable variants"
msgstr "Mostra varianti tracciabili"

#: src/tables/part/RelatedPartTable.tsx:104
#: src/tables/part/RelatedPartTable.tsx:137
msgid "Add Related Part"
msgstr "Aggiungi articolo correlato"

#: src/tables/part/RelatedPartTable.tsx:109
#~ msgid "Add related part"
#~ msgstr "Add related part"

#: src/tables/part/RelatedPartTable.tsx:119
msgid "Delete Related Part"
msgstr "Elimina Articolo Correlato"

#: src/tables/part/RelatedPartTable.tsx:126
msgid "Edit Related Part"
msgstr "Aggiungi articolo correlato"

#: src/tables/part/SelectionListTable.tsx:64
#: src/tables/part/SelectionListTable.tsx:115
msgid "Add Selection List"
msgstr "Aggiungi Elenco selezione"

#: src/tables/part/SelectionListTable.tsx:76
msgid "Edit Selection List"
msgstr "Modifica Elenco selezione"

#: src/tables/part/SelectionListTable.tsx:84
msgid "Delete Selection List"
msgstr "Elimina Elenco selezione"

#: src/tables/plugin/PluginErrorTable.tsx:29
msgid "Stage"
msgstr "Fase"

#: src/tables/plugin/PluginListTable.tsx:43
msgid "Plugin is active"
msgstr "Il plugin è attivo"

#: src/tables/plugin/PluginListTable.tsx:49
msgid "Plugin is inactive"
msgstr "Il plugin non è attivo"

#: src/tables/plugin/PluginListTable.tsx:56
msgid "Plugin is not installed"
msgstr "Il plugin non è installato"

#: src/tables/plugin/PluginListTable.tsx:78
#: src/tables/settings/ExportSessionTable.tsx:33
msgid "Plugin"
msgstr "Plugin"

#: src/tables/plugin/PluginListTable.tsx:95
#~ msgid "Plugin with key {pluginKey} not found"
#~ msgstr "Plugin with key {pluginKey} not found"

#: src/tables/plugin/PluginListTable.tsx:97
#~ msgid "An error occurred while fetching plugin details"
#~ msgstr "An error occurred while fetching plugin details"

#: src/tables/plugin/PluginListTable.tsx:106
#: src/tables/plugin/PluginListTable.tsx:422
msgid "Mandatory"
msgstr "Obbligatorio"

#: src/tables/plugin/PluginListTable.tsx:113
#~ msgid "Plugin with id {id} not found"
#~ msgstr "Plugin with id {id} not found"

#: src/tables/plugin/PluginListTable.tsx:120
msgid "Description not available"
msgstr "Descrizione non disponibile"

#: src/tables/plugin/PluginListTable.tsx:122
#~ msgid "Plugin information"
#~ msgstr "Plugin information"

#: src/tables/plugin/PluginListTable.tsx:134
#~ msgid "Plugin Actions"
#~ msgstr "Plugin Actions"

#: src/tables/plugin/PluginListTable.tsx:138
#: src/tables/plugin/PluginListTable.tsx:141
#~ msgid "Edit plugin"
#~ msgstr "Edit plugin"

#: src/tables/plugin/PluginListTable.tsx:152
#: src/tables/plugin/PluginListTable.tsx:153
#~ msgid "Reload"
#~ msgstr "Reload"

#: src/tables/plugin/PluginListTable.tsx:153
msgid "Confirm plugin activation"
msgstr "Conferma attivazione plugin"

#: src/tables/plugin/PluginListTable.tsx:154
msgid "Confirm plugin deactivation"
msgstr "Conferma disattivazione plugin"

#: src/tables/plugin/PluginListTable.tsx:159
msgid "The selected plugin will be activated"
msgstr "Il plugin selezionato verrà attivato"

#: src/tables/plugin/PluginListTable.tsx:160
msgid "The selected plugin will be deactivated"
msgstr "Il plugin selezionato verrà disattivato"

#: src/tables/plugin/PluginListTable.tsx:163
#~ msgid "Package information"
#~ msgstr "Package information"

#: src/tables/plugin/PluginListTable.tsx:178
msgid "Deactivate"
msgstr "Disattiva"

#: src/tables/plugin/PluginListTable.tsx:192
msgid "Activate"
msgstr "Attiva"

#: src/tables/plugin/PluginListTable.tsx:193
msgid "Activate selected plugin"
msgstr "Attiva plugin selezionato"

#: src/tables/plugin/PluginListTable.tsx:197
#~ msgid "Plugin settings"
#~ msgstr "Plugin settings"

#: src/tables/plugin/PluginListTable.tsx:205
msgid "Update selected plugin"
msgstr "Aggiorna il plugin selezionato"

#: src/tables/plugin/PluginListTable.tsx:224
#: src/tables/stock/InstalledItemsTable.tsx:106
msgid "Uninstall"
msgstr "Disinstalla"

#: src/tables/plugin/PluginListTable.tsx:225
msgid "Uninstall selected plugin"
msgstr "Disinstallare il plugin selezionato"

#: src/tables/plugin/PluginListTable.tsx:244
msgid "Delete selected plugin configuration"
msgstr "Elimina la configurazione del plugin selezionata"

#: src/tables/plugin/PluginListTable.tsx:260
msgid "Activate Plugin"
msgstr "Attiva Plugin"

#: src/tables/plugin/PluginListTable.tsx:267
msgid "The plugin was activated"
msgstr "Il plugin è stato attivato"

#: src/tables/plugin/PluginListTable.tsx:268
msgid "The plugin was deactivated"
msgstr "Il plugin è stato disattivato"

#: src/tables/plugin/PluginListTable.tsx:280
#~ msgid "Install plugin"
#~ msgstr "Install plugin"

#: src/tables/plugin/PluginListTable.tsx:281
#: src/tables/plugin/PluginListTable.tsx:368
msgid "Install Plugin"
msgstr "Installa Plugin"

#: src/tables/plugin/PluginListTable.tsx:294
msgid "Install"
msgstr "Installa"

#: src/tables/plugin/PluginListTable.tsx:295
msgid "Plugin installed successfully"
msgstr "Plugin installato con successo"

#: src/tables/plugin/PluginListTable.tsx:300
msgid "Uninstall Plugin"
msgstr "Disinstalla plugin"

#: src/tables/plugin/PluginListTable.tsx:308
#~ msgid "This action cannot be undone."
#~ msgstr "This action cannot be undone."

#: src/tables/plugin/PluginListTable.tsx:312
msgid "Confirm plugin uninstall"
msgstr "Conferma disinstallazione plugin"

#: src/tables/plugin/PluginListTable.tsx:315
msgid "The selected plugin will be uninstalled."
msgstr "Il plugin selezionato verrà disinstallato."

#: src/tables/plugin/PluginListTable.tsx:320
msgid "Plugin uninstalled successfully"
msgstr "Plugin disinstallato con successo"

#: src/tables/plugin/PluginListTable.tsx:328
msgid "Delete Plugin"
msgstr "Elimina Plugin"

#: src/tables/plugin/PluginListTable.tsx:329
msgid "Deleting this plugin configuration will remove all associated settings and data. Are you sure you want to delete this plugin?"
msgstr "L'eliminazione di questa configurazione del plugin rimuoverà tutte le impostazioni e i dati associati. Sei sicuro di voler eliminare questo plugin?"

#: src/tables/plugin/PluginListTable.tsx:338
#~ msgid "Deactivate Plugin"
#~ msgstr "Deactivate Plugin"

#: src/tables/plugin/PluginListTable.tsx:342
msgid "Plugins reloaded"
msgstr "Plugin ricaricati"

#: src/tables/plugin/PluginListTable.tsx:343
msgid "Plugins were reloaded successfully"
msgstr "I plugin sono stati ricaricati correttamente"

#: src/tables/plugin/PluginListTable.tsx:354
#~ msgid "The following plugin will be activated"
#~ msgstr "The following plugin will be activated"

#: src/tables/plugin/PluginListTable.tsx:355
#~ msgid "The following plugin will be deactivated"
#~ msgstr "The following plugin will be deactivated"

#: src/tables/plugin/PluginListTable.tsx:361
msgid "Reload Plugins"
msgstr "Ricarica i Plugin"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Activating plugin"
#~ msgstr "Activating plugin"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Deactivating plugin"
#~ msgstr "Deactivating plugin"

#: src/tables/plugin/PluginListTable.tsx:385
msgid "Plugin Detail"
msgstr "Dettagli Plugin"

#: src/tables/plugin/PluginListTable.tsx:392
#~ msgid "Plugin updated"
#~ msgstr "Plugin updated"

#: src/tables/plugin/PluginListTable.tsx:403
#~ msgid "Error updating plugin"
#~ msgstr "Error updating plugin"

#: src/tables/plugin/PluginListTable.tsx:427
msgid "Sample"
msgstr "Esempio"

#: src/tables/plugin/PluginListTable.tsx:432
#: src/tables/stock/StockItemTable.tsx:377
msgid "Installed"
msgstr "Installato"

#: src/tables/plugin/PluginListTable.tsx:615
#~ msgid "Plugin detail"
#~ msgstr "Plugin detail"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:60
#~ msgid "Parameter updated"
#~ msgstr "Parameter updated"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:63
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:112
msgid "Add Parameter"
msgstr "Aggiungi parametro"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:73
#~ msgid "Parameter deleted"
#~ msgstr "Parameter deleted"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
msgid "Edit Parameter"
msgstr "Modifica parametro"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
#~ msgid "Are you sure you want to delete this parameter?"
#~ msgstr "Are you sure you want to delete this parameter?"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:82
msgid "Delete Parameter"
msgstr "Elimina Parametro"

#: src/tables/purchasing/ManufacturerPartTable.tsx:56
#: src/tables/purchasing/SupplierPartTable.tsx:80
msgid "MPN"
msgstr "MPN"

#: src/tables/purchasing/ManufacturerPartTable.tsx:63
#~ msgid "Create Manufacturer Part"
#~ msgstr "Create Manufacturer Part"

#: src/tables/purchasing/ManufacturerPartTable.tsx:100
#~ msgid "Manufacturer part updated"
#~ msgstr "Manufacturer part updated"

#: src/tables/purchasing/ManufacturerPartTable.tsx:112
#~ msgid "Manufacturer part deleted"
#~ msgstr "Manufacturer part deleted"

#: src/tables/purchasing/ManufacturerPartTable.tsx:114
#~ msgid "Are you sure you want to remove this manufacturer part?"
#~ msgstr "Are you sure you want to remove this manufacturer part?"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:109
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:391
msgid "Import Line Items"
msgstr "Importa Elementi Riga"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:230
msgid "Supplier Code"
msgstr "Codice Fornitore"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:237
msgid "Supplier Link"
msgstr "Link Fornitore"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:244
msgid "Manufacturer Code"
msgstr "Codice produttore"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:273
msgid "Show line items which have been received"
msgstr "Mostra gli elementi di riga che sono stati ricevuti"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
msgid "Receive line item"
msgstr "Ricevi voce di riga"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
#: src/tables/sales/ReturnOrderLineItemTable.tsx:160
#: src/tables/sales/SalesOrderLineItemTable.tsx:258
#~ msgid "Add line item"
#~ msgstr "Add line item"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:408
msgid "Receive items"
msgstr "Ricevi articoli"

#: src/tables/purchasing/SupplierPartTable.tsx:111
msgid "Base units"
msgstr "Unità di base"

#: src/tables/purchasing/SupplierPartTable.tsx:168
msgid "Add supplier part"
msgstr "Aggiungi fornitore articolo"

#: src/tables/purchasing/SupplierPartTable.tsx:180
msgid "Show active supplier parts"
msgstr "Mostra articoli fornitore attive"

#: src/tables/purchasing/SupplierPartTable.tsx:184
msgid "Active Part"
msgstr "Articolo Attivo"

#: src/tables/purchasing/SupplierPartTable.tsx:185
msgid "Show active internal parts"
msgstr "Mostra articoli interni attivi"

#: src/tables/purchasing/SupplierPartTable.tsx:189
msgid "Active Supplier"
msgstr "Fornitore Attivo"

#: src/tables/purchasing/SupplierPartTable.tsx:190
msgid "Show active suppliers"
msgstr "Mostra fornitori attivi"

#: src/tables/purchasing/SupplierPartTable.tsx:193
#~ msgid "Supplier part updated"
#~ msgstr "Supplier part updated"

#: src/tables/purchasing/SupplierPartTable.tsx:195
msgid "Show supplier parts with stock"
msgstr "Mostra articoli fornitore con stock"

#: src/tables/purchasing/SupplierPartTable.tsx:205
#~ msgid "Supplier part deleted"
#~ msgstr "Supplier part deleted"

#: src/tables/purchasing/SupplierPartTable.tsx:207
#~ msgid "Are you sure you want to remove this supplier part?"
#~ msgstr "Are you sure you want to remove this supplier part?"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:154
msgid "Received Date"
msgstr "Data di ricezione"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:168
msgid "Show items which have been received"
msgstr "Mostra gli articoli ricevuti"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:173
msgid "Filter by line item status"
msgstr "Filtra per stato elemento riga"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:191
msgid "Receive selected items"
msgstr "Ricevi gli elementi selezionati"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:223
msgid "Receive Item"
msgstr "Ricevi Articolo"

#: src/tables/sales/SalesOrderAllocationTable.tsx:86
msgid "Show outstanding allocations"
msgstr "Mostra allocazioni in sospeso"

#: src/tables/sales/SalesOrderAllocationTable.tsx:90
msgid "Assigned to Shipment"
msgstr "Assegnato alla Spedizione"

#: src/tables/sales/SalesOrderAllocationTable.tsx:91
msgid "Show allocations assigned to a shipment"
msgstr "Mostra le allocazioni assegnate a una spedizione"

#: src/tables/sales/SalesOrderAllocationTable.tsx:153
msgid "Available Quantity"
msgstr "Quantità Disponibile"

#: src/tables/sales/SalesOrderAllocationTable.tsx:160
msgid "Allocated Quantity"
msgstr "Quantità assegnata"

#: src/tables/sales/SalesOrderAllocationTable.tsx:174
#: src/tables/sales/SalesOrderAllocationTable.tsx:188
msgid "No shipment"
msgstr "Nessuna spedizione"

#: src/tables/sales/SalesOrderAllocationTable.tsx:186
msgid "Not shipped"
msgstr "Non spedito"

#: src/tables/sales/SalesOrderAllocationTable.tsx:208
#: src/tables/sales/SalesOrderAllocationTable.tsx:230
msgid "Edit Allocation"
msgstr "Modifica Assegnazione"

#: src/tables/sales/SalesOrderAllocationTable.tsx:215
#: src/tables/sales/SalesOrderAllocationTable.tsx:238
msgid "Delete Allocation"
msgstr "Rimuovi Assegnazione"

#: src/tables/sales/SalesOrderAllocationTable.tsx:293
msgid "Assign to Shipment"
msgstr "Assegna alla spedizione"

#: src/tables/sales/SalesOrderAllocationTable.tsx:309
msgid "Assign to shipment"
msgstr "Assegna alla spedizione"

#: src/tables/sales/SalesOrderLineItemTable.tsx:272
msgid "Allocate Serial Numbers"
msgstr "Assegna Numeri di Serie"

#: src/tables/sales/SalesOrderLineItemTable.tsx:280
#~ msgid "Allocate stock"
#~ msgstr "Allocate stock"

#: src/tables/sales/SalesOrderLineItemTable.tsx:291
#~ msgid "Allocate Serials"
#~ msgstr "Allocate Serials"

#: src/tables/sales/SalesOrderLineItemTable.tsx:320
msgid "Show lines which are fully allocated"
msgstr "Mostra le righe che sono completamente assegnate"

#: src/tables/sales/SalesOrderLineItemTable.tsx:325
msgid "Show lines which are completed"
msgstr "Mostra le righe completate"

#: src/tables/sales/SalesOrderLineItemTable.tsx:402
msgid "Allocate serials"
msgstr "Alloca seriali"

#: src/tables/sales/SalesOrderLineItemTable.tsx:419
msgid "Build stock"
msgstr "Produzione articolo magazzino"

#: src/tables/sales/SalesOrderLineItemTable.tsx:436
msgid "Order stock"
msgstr "Ordine Articolo magazzino"

#: src/tables/sales/SalesOrderShipmentTable.tsx:51
#~ msgid "Delete Shipment"
#~ msgstr "Delete Shipment"

#: src/tables/sales/SalesOrderShipmentTable.tsx:55
msgid "Create Shipment"
msgstr "Crea spedizione"

#: src/tables/sales/SalesOrderShipmentTable.tsx:102
msgid "Items"
msgstr "Articoli"

#: src/tables/sales/SalesOrderShipmentTable.tsx:137
msgid "View Shipment"
msgstr "Vedi spedizione"

#: src/tables/sales/SalesOrderShipmentTable.tsx:154
msgid "Edit shipment"
msgstr "Modifica spedizione"

#: src/tables/sales/SalesOrderShipmentTable.tsx:162
msgid "Cancel shipment"
msgstr "Annulla spedizione"

#: src/tables/sales/SalesOrderShipmentTable.tsx:177
msgid "Add shipment"
msgstr "Aggiungi Spedizione"

#: src/tables/sales/SalesOrderShipmentTable.tsx:191
msgid "Show shipments which have been shipped"
msgstr "Mostra le spedizioni che sono state spedite"

#: src/tables/sales/SalesOrderShipmentTable.tsx:196
msgid "Show shipments which have been delivered"
msgstr "Mostra le spedizioni che sono state consegnate"

#: src/tables/settings/ApiTokenTable.tsx:31
#: src/tables/settings/ApiTokenTable.tsx:45
msgid "Generate Token"
msgstr "Genera token"

#: src/tables/settings/ApiTokenTable.tsx:33
msgid "Token generated"
msgstr "Token generato"

#: src/tables/settings/ApiTokenTable.tsx:68
#: src/tables/settings/ApiTokenTable.tsx:123
msgid "Revoked"
msgstr "Revocato"

#: src/tables/settings/ApiTokenTable.tsx:72
#: src/tables/settings/ApiTokenTable.tsx:185
msgid "Token"
msgstr "Token"

#: src/tables/settings/ApiTokenTable.tsx:79
msgid "In Use"
msgstr "In Uso"

#: src/tables/settings/ApiTokenTable.tsx:88
msgid "Last Seen"
msgstr "Ultimo accesso"

#: src/tables/settings/ApiTokenTable.tsx:93
msgid "Expiry"
msgstr "Scadenza"

#: src/tables/settings/ApiTokenTable.tsx:124
msgid "Show revoked tokens"
msgstr "Mostra token revocati"

#: src/tables/settings/ApiTokenTable.tsx:143
msgid "Revoke"
msgstr "Revoca"

#: src/tables/settings/ApiTokenTable.tsx:167
msgid "Error revoking token"
msgstr "Errore nel revocare il token"

#: src/tables/settings/ApiTokenTable.tsx:189
msgid "Tokens are only shown once - make sure to note it down."
msgstr "I token sono mostrati solo una volta - assicurati di annotarli."

#: src/tables/settings/BarcodeScanHistoryTable.tsx:60
msgid "Barcode Information"
msgstr "Informazione codice a barre"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:85
msgid "Endpoint"
msgstr "Endpoint"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:89
#: src/tables/settings/BarcodeScanHistoryTable.tsx:208
#: src/tables/stock/StockItemTestResultTable.tsx:185
msgid "Result"
msgstr "Risultato"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:97
msgid "Context"
msgstr "Contesto"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:118
msgid "Response"
msgstr "Risposta"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:209
msgid "Filter by result"
msgstr "Filtra per risultato"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:223
msgid "Delete Barcode Scan Record"
msgstr "Elimina Record Scansione Codice A Barre"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:249
msgid "Barcode Scan Details"
msgstr "Dettagli Di Scansione Codice A Barre"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:259
msgid "Logging Disabled"
msgstr "Logging Disattivato"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:261
msgid "Barcode logging is not enabled"
msgstr "Logging del codice a barre non è attivo"

#: src/tables/settings/CustomStateTable.tsx:63
msgid "Status Group"
msgstr "Gruppo Stato"

#: src/tables/settings/CustomStateTable.tsx:84
msgid "Logical State"
msgstr "Stato Logico"

#: src/tables/settings/CustomStateTable.tsx:96
msgid "Identifier"
msgstr "Identificativo"

#: src/tables/settings/CustomStateTable.tsx:115
#~ msgid "Add state"
#~ msgstr "Add state"

#: src/tables/settings/CustomStateTable.tsx:133
#: src/tables/settings/CustomStateTable.tsx:140
#: src/tables/settings/CustomStateTable.tsx:202
msgid "Add State"
msgstr "Aggiungi Stato"

#: src/tables/settings/CustomStateTable.tsx:153
msgid "Edit State"
msgstr "Modifica stato"

#: src/tables/settings/CustomStateTable.tsx:161
msgid "Delete State"
msgstr "Elimina stato"

#: src/tables/settings/CustomUnitsTable.tsx:54
msgid "Add Custom Unit"
msgstr "Aggiungi Unità Personalizzata"

#: src/tables/settings/CustomUnitsTable.tsx:64
msgid "Edit Custom Unit"
msgstr "Modifica Unità Personalizzata"

#: src/tables/settings/CustomUnitsTable.tsx:72
msgid "Delete Custom Unit"
msgstr "Elimina Unità Personalizzata"

#: src/tables/settings/CustomUnitsTable.tsx:103
msgid "Add custom unit"
msgstr "Aggiungi Unità Personalizzata"

#: src/tables/settings/EmailTable.tsx:21
#: src/tables/settings/EmailTable.tsx:36
msgid "Send Test Email"
msgstr "Invia email di prova"

#: src/tables/settings/EmailTable.tsx:23
msgid "Email sent successfully"
msgstr "Email inviata correttamente"

#: src/tables/settings/EmailTable.tsx:49
msgid "Delete Email"
msgstr "Elimina email"

#: src/tables/settings/EmailTable.tsx:50
msgid "Email deleted successfully"
msgstr "Email eliminata correttamente"

#: src/tables/settings/EmailTable.tsx:58
msgid "Subject"
msgstr "Oggetto"

#: src/tables/settings/EmailTable.tsx:63
msgid "To"
msgstr "A"

#: src/tables/settings/EmailTable.tsx:68
msgid "Sender"
msgstr "Mittente"

#: src/tables/settings/EmailTable.tsx:78
msgid "Announced"
msgstr "Annunciato"

#: src/tables/settings/EmailTable.tsx:80
msgid "Sent"
msgstr "Inviato"

#: src/tables/settings/EmailTable.tsx:82
msgid "Failed"
msgstr "Fallito"

#: src/tables/settings/EmailTable.tsx:86
msgid "Read"
msgstr "Letto"

#: src/tables/settings/EmailTable.tsx:88
msgid "Confirmed"
msgstr "Confermato"

#: src/tables/settings/EmailTable.tsx:96
msgid "Direction"
msgstr "Direzione"

#: src/tables/settings/EmailTable.tsx:99
msgid "Incoming"
msgstr "In arrivo"

#: src/tables/settings/EmailTable.tsx:99
msgid "Outgoing"
msgstr "In uscita"

#: src/tables/settings/ErrorTable.tsx:51
#~ msgid "Delete error report"
#~ msgstr "Delete error report"

#: src/tables/settings/ErrorTable.tsx:67
msgid "Traceback"
msgstr "Traceback"

#: src/tables/settings/ErrorTable.tsx:103
msgid "When"
msgstr "Quando"

#: src/tables/settings/ErrorTable.tsx:113
msgid "Error Information"
msgstr "Messaggio di errore"

#: src/tables/settings/ErrorTable.tsx:123
msgid "Delete Error Report"
msgstr "Elimina report Degli Errori"

#: src/tables/settings/ErrorTable.tsx:125
msgid "Are you sure you want to delete this error report?"
msgstr "Sei sicuro di voler eliminare questo report degli errori?"

#: src/tables/settings/ErrorTable.tsx:127
msgid "Error report deleted"
msgstr "Report degli errori eliminato"

#: src/tables/settings/ErrorTable.tsx:146
#: src/tables/settings/FailedTasksTable.tsx:65
msgid "Error Details"
msgstr "Dettagli errore"

#: src/tables/settings/ExportSessionTable.tsx:28
msgid "Output Type"
msgstr "Tipo di Output"

#: src/tables/settings/ExportSessionTable.tsx:38
msgid "Exported On"
msgstr "Esportato Su"

#: src/tables/settings/ExportSessionTable.tsx:59
msgid "Delete Output"
msgstr "Elimina Output"

#: src/tables/settings/FailedTasksTable.tsx:32
#: src/tables/settings/PendingTasksTable.tsx:28
#: src/tables/settings/ScheduledTasksTable.tsx:19
msgid "Task"
msgstr "Attività"

#: src/tables/settings/FailedTasksTable.tsx:38
#: src/tables/settings/PendingTasksTable.tsx:33
msgid "Task ID"
msgstr "ID Attività"

#: src/tables/settings/FailedTasksTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:230
msgid "Started"
msgstr "Avviato"

#: src/tables/settings/FailedTasksTable.tsx:54
msgid "Attempts"
msgstr "Tentativi"

#: src/tables/settings/FailedTasksTable.tsx:92
msgid "No Information"
msgstr "Nessuna Informazione"

#: src/tables/settings/FailedTasksTable.tsx:93
msgid "No error details are available for this task"
msgstr "Nessun dettaglio di errore disponibile per questa attività"

#: src/tables/settings/GroupTable.tsx:71
msgid "Group with id {id} not found"
msgstr "Gruppo con id {id} non trovato"

#: src/tables/settings/GroupTable.tsx:73
msgid "An error occurred while fetching group details"
msgstr "Si è verificato un errore durante il recupero dei dettagli del gruppo"

#: src/tables/settings/GroupTable.tsx:96
#: src/tables/settings/GroupTable.tsx:197
msgid "Name of the user group"
msgstr "Nome del gruppo utenti"

#: src/tables/settings/GroupTable.tsx:117
#~ msgid "Permission set"
#~ msgstr "Permission set"

#: src/tables/settings/GroupTable.tsx:170
#: src/tables/settings/UserTable.tsx:315
msgid "Open Profile"
msgstr "Apri Profilo"

#: src/tables/settings/GroupTable.tsx:185
msgid "Delete group"
msgstr "Elimina gruppo"

#: src/tables/settings/GroupTable.tsx:186
msgid "Group deleted"
msgstr "Gruppo eliminato"

#: src/tables/settings/GroupTable.tsx:188
msgid "Are you sure you want to delete this group?"
msgstr "Sei sicuro di voler eliminare questo gruppo?"

#: src/tables/settings/GroupTable.tsx:193
msgid "Add Group"
msgstr "Aggiungi Gruppo"

#: src/tables/settings/GroupTable.tsx:210
msgid "Add group"
msgstr "Aggiungi gruppo"

#: src/tables/settings/GroupTable.tsx:213
#~ msgid "Edit group"
#~ msgstr "Edit group"

#: src/tables/settings/GroupTable.tsx:231
msgid "Edit Group"
msgstr "Modifica Gruppo"

#: src/tables/settings/ImportSessionTable.tsx:38
msgid "Delete Import Session"
msgstr "Elimina Sessione d'Importazione"

#: src/tables/settings/ImportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:129
msgid "Create Import Session"
msgstr "Crea Sessione d'Importazione"

#: src/tables/settings/ImportSessionTable.tsx:72
msgid "Uploaded"
msgstr "Caricato"

#: src/tables/settings/ImportSessionTable.tsx:83
msgid "Imported Rows"
msgstr "Riga importate"

#: src/tables/settings/ImportSessionTable.tsx:111
#: src/tables/settings/TemplateTable.tsx:368
msgid "Model Type"
msgstr "Tipo Modello"

#: src/tables/settings/ImportSessionTable.tsx:112
#: src/tables/settings/TemplateTable.tsx:369
msgid "Filter by target model type"
msgstr "Filtra per tipo di modello di destinazione"

#: src/tables/settings/ImportSessionTable.tsx:118
msgid "Filter by import session status"
msgstr "Filtra per stato della sessione d'importazione"

#: src/tables/settings/PendingTasksTable.tsx:47
msgid "Arguments"
msgstr "Argomenti"

#: src/tables/settings/PendingTasksTable.tsx:61
msgid "Remove all pending tasks"
msgstr "Rimuovi tutte le attività in sospeso"

#: src/tables/settings/PendingTasksTable.tsx:69
msgid "All pending tasks deleted"
msgstr "Tutte le attività in sospeso eliminate"

#: src/tables/settings/PendingTasksTable.tsx:76
msgid "Error while deleting all pending tasks"
msgstr "Errore durante l'eliminazione di tutte le attività in sospeso"

#: src/tables/settings/ProjectCodeTable.tsx:46
msgid "Add Project Code"
msgstr "Aggiungi Codice Progetto"

#: src/tables/settings/ProjectCodeTable.tsx:58
msgid "Edit Project Code"
msgstr "Modifica Codice Progetto"

#: src/tables/settings/ProjectCodeTable.tsx:66
msgid "Delete Project Code"
msgstr "Elimina Codice Progetto"

#: src/tables/settings/ProjectCodeTable.tsx:97
msgid "Add project code"
msgstr "Aggiungi Codice Progetto"

#: src/tables/settings/ScheduledTasksTable.tsx:28
msgid "Last Run"
msgstr "Ultima esecuzione"

#: src/tables/settings/ScheduledTasksTable.tsx:50
msgid "Next Run"
msgstr "Prossima esecuzione"

#: src/tables/settings/StocktakeReportTable.tsx:28
#~ msgid "Report"
#~ msgstr "Report"

#: src/tables/settings/StocktakeReportTable.tsx:36
#~ msgid "Part Count"
#~ msgstr "Part Count"

#: src/tables/settings/StocktakeReportTable.tsx:59
#~ msgid "Delete Report"
#~ msgstr "Delete Report"

#: src/tables/settings/TemplateTable.tsx:120
#~ msgid "{templateTypeTranslation} with id {id} not found"
#~ msgstr "{templateTypeTranslation} with id {id} not found"

#: src/tables/settings/TemplateTable.tsx:124
#~ msgid "An error occurred while fetching {templateTypeTranslation} details"
#~ msgstr "An error occurred while fetching {templateTypeTranslation} details"

#: src/tables/settings/TemplateTable.tsx:146
#~ msgid "actions"
#~ msgstr "actions"

#: src/tables/settings/TemplateTable.tsx:165
msgid "Template not found"
msgstr "Modello non trovato"

#: src/tables/settings/TemplateTable.tsx:167
msgid "An error occurred while fetching template details"
msgstr "Si è verificato un errore durante il recupero dei dettagli del modello"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Add new"
#~ msgstr "Add new"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Create new"
#~ msgstr "Create new"

#: src/tables/settings/TemplateTable.tsx:261
msgid "Modify"
msgstr "Modifica"

#: src/tables/settings/TemplateTable.tsx:262
msgid "Modify template file"
msgstr "Modifica file modello"

#: src/tables/settings/TemplateTable.tsx:313
#: src/tables/settings/TemplateTable.tsx:381
msgid "Edit Template"
msgstr "Modifica modello"

#: src/tables/settings/TemplateTable.tsx:321
msgid "Delete template"
msgstr "Elimina modello"

#: src/tables/settings/TemplateTable.tsx:327
msgid "Add Template"
msgstr "Aggiungi modello"

#: src/tables/settings/TemplateTable.tsx:340
msgid "Add template"
msgstr "Aggiungi modello"

#: src/tables/settings/TemplateTable.tsx:363
msgid "Filter by enabled status"
msgstr "Filtra per stato abilitato"

#: src/tables/settings/TemplateTable.tsx:420
#~ msgid "Report Output"
#~ msgstr "Report Output"

#: src/tables/settings/UserTable.tsx:123
msgid "Groups updated"
msgstr "Gruppi aggiornati"

#: src/tables/settings/UserTable.tsx:124
msgid "User groups updated successfully"
msgstr "Gruppi utente aggiornati con successo"

#: src/tables/settings/UserTable.tsx:131
msgid "Error updating user groups"
msgstr "Errore nell'aggiornare i gruppi utente"

#: src/tables/settings/UserTable.tsx:150
msgid "User with id {id} not found"
msgstr "Utente con Id {id} non trovato"

#: src/tables/settings/UserTable.tsx:152
msgid "An error occurred while fetching user details"
msgstr "Si è verificato un errore durante il recupero dei dettagli dell'utente"

#: src/tables/settings/UserTable.tsx:154
#~ msgid "No groups"
#~ msgstr "No groups"

#: src/tables/settings/UserTable.tsx:178
msgid "Is Active"
msgstr "È attivo"

#: src/tables/settings/UserTable.tsx:179
msgid "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
msgstr "Indica se questo utente deve essere considerato attivo. Deseleziona questa opzione anziché eliminare gli account."

#: src/tables/settings/UserTable.tsx:183
msgid "Is Staff"
msgstr "È Staff"

#: src/tables/settings/UserTable.tsx:184
msgid "Designates whether the user can log into the django admin site."
msgstr "Indica se l'utente può accedere al sito di amministrazione django."

#: src/tables/settings/UserTable.tsx:188
msgid "Is Superuser"
msgstr "È Superuser"

#: src/tables/settings/UserTable.tsx:189
msgid "Designates that this user has all permissions without explicitly assigning them."
msgstr "Indica che questo utente ha tutti i permessi senza assegnarli esplicitamente."

#: src/tables/settings/UserTable.tsx:199
msgid "You cannot edit the rights for the currently logged-in user."
msgstr "Non è possibile modificare i diritti per l'utente attualmente loggato."

#: src/tables/settings/UserTable.tsx:218
msgid "User Groups"
msgstr "Gruppi Utente"

#: src/tables/settings/UserTable.tsx:305
#~ msgid "Edit user"
#~ msgstr "Edit user"

#: src/tables/settings/UserTable.tsx:332
msgid "Lock user"
msgstr "Blocca utente"

#: src/tables/settings/UserTable.tsx:342
msgid "Unlock user"
msgstr "Sblocca utente"

#: src/tables/settings/UserTable.tsx:358
msgid "Delete user"
msgstr "Elimina utente"

#: src/tables/settings/UserTable.tsx:359
msgid "User deleted"
msgstr "Utente eliminato"

#: src/tables/settings/UserTable.tsx:361
msgid "Are you sure you want to delete this user?"
msgstr "Sei sicuro di voler eliminare questo utente?"

#: src/tables/settings/UserTable.tsx:367
msgid "Add User"
msgstr "Aggiungi Utente"

#: src/tables/settings/UserTable.tsx:375
msgid "Added user"
msgstr "Utente aggiunto"

#: src/tables/settings/UserTable.tsx:382
msgid "Set Password"
msgstr "Imposta Password"

#: src/tables/settings/UserTable.tsx:387
msgid "Password updated"
msgstr "Password aggiornata"

#: src/tables/settings/UserTable.tsx:398
msgid "Add user"
msgstr "Aggiungi utente"

#: src/tables/settings/UserTable.tsx:411
msgid "Show active users"
msgstr "Mostra utenti attivi"

#: src/tables/settings/UserTable.tsx:416
msgid "Show staff users"
msgstr "Mostra utenti staff"

#: src/tables/settings/UserTable.tsx:421
msgid "Show superusers"
msgstr "Mostra superutenti"

#: src/tables/settings/UserTable.tsx:440
msgid "Edit User"
msgstr "Modifica Utente"

#: src/tables/settings/UserTable.tsx:476
msgid "User updated"
msgstr "Utente Aggiornato"

#: src/tables/settings/UserTable.tsx:477
msgid "User updated successfully"
msgstr "Utente aggiornato correttamente"

#: src/tables/settings/UserTable.tsx:483
msgid "Error updating user"
msgstr "Errore nell'aggiornare l'utente"

#: src/tables/stock/InstalledItemsTable.tsx:37
#: src/tables/stock/InstalledItemsTable.tsx:89
msgid "Install Item"
msgstr "Installa Elemento"

#: src/tables/stock/InstalledItemsTable.tsx:39
msgid "Item installed"
msgstr "Elemento installato"

#: src/tables/stock/InstalledItemsTable.tsx:50
msgid "Uninstall Item"
msgstr "Disinstalla Elemento"

#: src/tables/stock/InstalledItemsTable.tsx:52
msgid "Item uninstalled"
msgstr "Elemento disinstallato"

#: src/tables/stock/InstalledItemsTable.tsx:107
msgid "Uninstall stock item"
msgstr "Disinstallare l'elemento di magazzino"

#: src/tables/stock/LocationTypesTable.tsx:44
#: src/tables/stock/LocationTypesTable.tsx:111
msgid "Add Location Type"
msgstr "Aggiungi tipo di posizione"

#: src/tables/stock/LocationTypesTable.tsx:52
msgid "Edit Location Type"
msgstr "Modifica tipo di posizione"

#: src/tables/stock/LocationTypesTable.tsx:60
msgid "Delete Location Type"
msgstr "Elimina tipo di posizione"

#: src/tables/stock/LocationTypesTable.tsx:68
msgid "Icon"
msgstr "Icona"

#: src/tables/stock/StockItemTable.tsx:107
msgid "This stock item is in production"
msgstr "Questo elemento del magazzino è in produzione"

#: src/tables/stock/StockItemTable.tsx:114
msgid "This stock item has been assigned to a sales order"
msgstr "L'articolo a magazzino è stato assegnato a un ordine di vendita"

#: src/tables/stock/StockItemTable.tsx:121
msgid "This stock item has been assigned to a customer"
msgstr "L'articolo a magazzino è stato assegnato a un cliente"

#: src/tables/stock/StockItemTable.tsx:128
msgid "This stock item is installed in another stock item"
msgstr "Questo articolo in magazzino è installato in un altro articolo in magazzino"

#: src/tables/stock/StockItemTable.tsx:135
msgid "This stock item has been consumed by a build order"
msgstr "Questo articolo è stato consumato da un ordine di produzione"

#: src/tables/stock/StockItemTable.tsx:142
msgid "This stock item is unavailable"
msgstr "Elemento di magazzino non disponibile"

#: src/tables/stock/StockItemTable.tsx:151
msgid "This stock item has expired"
msgstr "Questo articolo a magazzino è scaduto"

#: src/tables/stock/StockItemTable.tsx:155
msgid "This stock item is stale"
msgstr "Questo articolo a magazzino è obsoleto"

#: src/tables/stock/StockItemTable.tsx:167
msgid "This stock item is fully allocated"
msgstr "Questo articolo di magazzino è completamente allocato"

#: src/tables/stock/StockItemTable.tsx:174
msgid "This stock item is partially allocated"
msgstr "Questo articolo di magazzino è parzialmente allocato"

#: src/tables/stock/StockItemTable.tsx:202
msgid "This stock item has been depleted"
msgstr "Questo articolo di magazzino è esaurito"

#: src/tables/stock/StockItemTable.tsx:301
#~ msgid "Show stock for assmebled parts"
#~ msgstr "Show stock for assmebled parts"

#: src/tables/stock/StockItemTable.tsx:306
msgid "Stocktake Date"
msgstr "Data dell'inventario"

#: src/tables/stock/StockItemTable.tsx:324
msgid "Show stock for active parts"
msgstr "Mostra articoli a magazzino per gli articoli attivi"

#: src/tables/stock/StockItemTable.tsx:335
msgid "Show stock for assembled parts"
msgstr "Mostra stock per gli articoli assemblati"

#: src/tables/stock/StockItemTable.tsx:340
msgid "Show items which have been allocated"
msgstr "Mostra gli articoli che sono stati assegnati"

#: src/tables/stock/StockItemTable.tsx:345
msgid "Show items which are available"
msgstr "Mostra gli articoli che sono disponibili"

#: src/tables/stock/StockItemTable.tsx:349
#: src/tables/stock/StockLocationTable.tsx:38
msgid "Include Sublocations"
msgstr "Includi sotto allocazioni"

#: src/tables/stock/StockItemTable.tsx:350
msgid "Include stock in sublocations"
msgstr "Includi articoli a magazzino nelle sotto allocazioni"

#: src/tables/stock/StockItemTable.tsx:354
msgid "Depleted"
msgstr "Esaurito"

#: src/tables/stock/StockItemTable.tsx:355
msgid "Show depleted stock items"
msgstr "Mostra gli articoli a magazzino esauriti"

#: src/tables/stock/StockItemTable.tsx:360
msgid "Show items which are in stock"
msgstr "Mostra gli articoli che sono a magazzino"

#: src/tables/stock/StockItemTable.tsx:362
#~ msgid "Include stock items for variant parts"
#~ msgstr "Include stock items for variant parts"

#: src/tables/stock/StockItemTable.tsx:365
msgid "Show items which are in production"
msgstr "Mostra gli articoli che sono in produzione"

#: src/tables/stock/StockItemTable.tsx:373
msgid "Show items which have been consumed by a build order"
msgstr "Mostra gli articoli che sono stati consumati da un ordine di produzione"

#: src/tables/stock/StockItemTable.tsx:378
msgid "Show stock items which are installed in other items"
msgstr "Mostra gli articoli a magazzino che sono installati in un altro articolo"

#: src/tables/stock/StockItemTable.tsx:382
msgid "Sent to Customer"
msgstr "Inviato al cliente"

#: src/tables/stock/StockItemTable.tsx:383
msgid "Show items which have been sent to a customer"
msgstr "Mostra gli articoli che sono stati inviati a un cliente"

#: src/tables/stock/StockItemTable.tsx:394
msgid "Show tracked items"
msgstr "Mostra articoli tracciabili"

#: src/tables/stock/StockItemTable.tsx:397
#~ msgid "Serial Number LTE"
#~ msgstr "Serial Number LTE"

#: src/tables/stock/StockItemTable.tsx:398
msgid "Has Purchase Price"
msgstr "Ha prezzo d'acquisto"

#: src/tables/stock/StockItemTable.tsx:399
msgid "Show items which have a purchase price"
msgstr "Mostra gli articoli che hanno un prezzo d'acquisto"

#: src/tables/stock/StockItemTable.tsx:403
#~ msgid "Serial Number GTE"
#~ msgstr "Serial Number GTE"

#: src/tables/stock/StockItemTable.tsx:404
msgid "Show items which have expired"
msgstr "Mostra gli articoli scaduti"

#: src/tables/stock/StockItemTable.tsx:410
msgid "Show items which are stale"
msgstr "Mostra gli articoli obsoleti"

#: src/tables/stock/StockItemTable.tsx:415
msgid "Expired Before"
msgstr "Scaduto Prima"

#: src/tables/stock/StockItemTable.tsx:416
msgid "Show items which expired before this date"
msgstr "Mostra gli articoli scaduti prima di questa data"

#: src/tables/stock/StockItemTable.tsx:422
msgid "Expired After"
msgstr "Scaduto dopo"

#: src/tables/stock/StockItemTable.tsx:423
msgid "Show items which expired after this date"
msgstr "Mostra gli articoli scaduti dopo questa data"

#: src/tables/stock/StockItemTable.tsx:429
msgid "Updated Before"
msgstr "Aggiornato prima"

#: src/tables/stock/StockItemTable.tsx:430
msgid "Show items updated before this date"
msgstr "Mostra gli articoli aggiornati prima di questa data"

#: src/tables/stock/StockItemTable.tsx:435
msgid "Updated After"
msgstr "Aggiornato dopo"

#: src/tables/stock/StockItemTable.tsx:436
msgid "Show items updated after this date"
msgstr "Mostra gli articoli aggiornati dopo questa data"

#: src/tables/stock/StockItemTable.tsx:441
msgid "Stocktake Before"
msgstr "Inventario Prima"

#: src/tables/stock/StockItemTable.tsx:442
msgid "Show items counted before this date"
msgstr "Mostra gli articoli contati prima di questa data"

#: src/tables/stock/StockItemTable.tsx:447
msgid "Stocktake After"
msgstr "Inventario Dopo"

#: src/tables/stock/StockItemTable.tsx:448
msgid "Show items counted after this date"
msgstr "Mostra gli articoli contati dopo questa data"

#: src/tables/stock/StockItemTable.tsx:453
msgid "External Location"
msgstr "Posizione Esterna"

#: src/tables/stock/StockItemTable.tsx:454
msgid "Show items in an external location"
msgstr "Mostra gli articoli in una posizione esterna"

#: src/tables/stock/StockItemTable.tsx:528
#~ msgid "Delete stock items"
#~ msgstr "Delete stock items"

#: src/tables/stock/StockItemTable.tsx:559
msgid "Order items"
msgstr "Ordina gli articoli"

#: src/tables/stock/StockItemTable.tsx:595
#~ msgid "Add a new stock item"
#~ msgstr "Add a new stock item"

#: src/tables/stock/StockItemTable.tsx:604
#~ msgid "Remove some quantity from a stock item"
#~ msgstr "Remove some quantity from a stock item"

#: src/tables/stock/StockItemTable.tsx:615
#~ msgid "Move Stock items to new locations"
#~ msgstr "Move Stock items to new locations"

#: src/tables/stock/StockItemTable.tsx:622
#~ msgid "Change stock status"
#~ msgstr "Change stock status"

#: src/tables/stock/StockItemTable.tsx:624
#~ msgid "Change the status of stock items"
#~ msgstr "Change the status of stock items"

#: src/tables/stock/StockItemTable.tsx:631
#~ msgid "Merge stock"
#~ msgstr "Merge stock"

#: src/tables/stock/StockItemTable.tsx:633
#~ msgid "Merge stock items"
#~ msgstr "Merge stock items"

#: src/tables/stock/StockItemTable.tsx:642
#~ msgid "Order new stock"
#~ msgstr "Order new stock"

#: src/tables/stock/StockItemTable.tsx:653
#~ msgid "Assign to customer"
#~ msgstr "Assign to customer"

#: src/tables/stock/StockItemTable.tsx:655
#~ msgid "Assign items to a customer"
#~ msgstr "Assign items to a customer"

#: src/tables/stock/StockItemTable.tsx:662
#~ msgid "Delete stock"
#~ msgstr "Delete stock"

#: src/tables/stock/StockItemTestResultTable.tsx:140
msgid "Test"
msgstr "Test"

#: src/tables/stock/StockItemTestResultTable.tsx:174
msgid "Test result for installed stock item"
msgstr "Risultato del test per l'articolo di magazzino installato"

#: src/tables/stock/StockItemTestResultTable.tsx:205
msgid "Attachment"
msgstr "Allegato"

#: src/tables/stock/StockItemTestResultTable.tsx:224
msgid "Test station"
msgstr "Stazione di prova"

#: src/tables/stock/StockItemTestResultTable.tsx:246
msgid "Finished"
msgstr "Finito"

#: src/tables/stock/StockItemTestResultTable.tsx:304
#: src/tables/stock/StockItemTestResultTable.tsx:375
msgid "Edit Test Result"
msgstr "Modifica risultato del test"

#: src/tables/stock/StockItemTestResultTable.tsx:306
msgid "Test result updated"
msgstr "Risultato del test aggiornato"

#: src/tables/stock/StockItemTestResultTable.tsx:312
#: src/tables/stock/StockItemTestResultTable.tsx:384
msgid "Delete Test Result"
msgstr "Cancellare il risultato del test"

#: src/tables/stock/StockItemTestResultTable.tsx:314
msgid "Test result deleted"
msgstr "Risultato del test eliminato"

#: src/tables/stock/StockItemTestResultTable.tsx:328
msgid "Test Passed"
msgstr "Test superato"

#: src/tables/stock/StockItemTestResultTable.tsx:329
msgid "Test result has been recorded"
msgstr "Il risultato del test è stato registrato"

#: src/tables/stock/StockItemTestResultTable.tsx:336
msgid "Failed to record test result"
msgstr "Impossibile registrare il risultato del test"

#: src/tables/stock/StockItemTestResultTable.tsx:353
msgid "Pass Test"
msgstr "Test Passato"

#: src/tables/stock/StockItemTestResultTable.tsx:402
msgid "Show results for required tests"
msgstr "Mostra i risultati per i test richiesti"

#: src/tables/stock/StockItemTestResultTable.tsx:406
msgid "Include Installed"
msgstr "Includi Elementi Installati"

#: src/tables/stock/StockItemTestResultTable.tsx:407
msgid "Show results for installed stock items"
msgstr "Mostra risultati per gli articoli a magazzino"

#: src/tables/stock/StockItemTestResultTable.tsx:411
msgid "Passed"
msgstr "Superato"

#: src/tables/stock/StockItemTestResultTable.tsx:412
msgid "Show only passed tests"
msgstr "Mostra solo i test superati"

#: src/tables/stock/StockItemTestResultTable.tsx:417
msgid "Show results for enabled tests"
msgstr "Mostra i risultati per i test abilitati"

#: src/tables/stock/StockLocationTable.tsx:38
#~ msgid "structural"
#~ msgstr "structural"

#: src/tables/stock/StockLocationTable.tsx:39
msgid "Include sublocations in results"
msgstr "Includi le sottoposizioni nei risultati"

#: src/tables/stock/StockLocationTable.tsx:43
#~ msgid "external"
#~ msgstr "external"

#: src/tables/stock/StockLocationTable.tsx:44
msgid "Show structural locations"
msgstr "Mostra posizioni strutturali"

#: src/tables/stock/StockLocationTable.tsx:49
msgid "Show external locations"
msgstr "Mostra posizioni esterne"

#: src/tables/stock/StockLocationTable.tsx:53
msgid "Has location type"
msgstr "Ha tipo di posizione"

#: src/tables/stock/StockLocationTable.tsx:58
msgid "Filter by location type"
msgstr "Filtra per tipo di posizione"

#: src/tables/stock/StockLocationTable.tsx:105
#: src/tables/stock/StockLocationTable.tsx:160
msgid "Add Stock Location"
msgstr "Aggiungi posizione giacenza"

#: src/tables/stock/StockLocationTable.tsx:129
msgid "Set Parent Location"
msgstr "Imposta posizione principale"

#: src/tables/stock/StockLocationTable.tsx:149
msgid "Set parent location for the selected items"
msgstr "Imposta la posizione superiore per gli elementi selezionati"

#: src/tables/stock/StockTrackingTable.tsx:77
msgid "Added"
msgstr "Aggiunto"

#: src/tables/stock/StockTrackingTable.tsx:82
msgid "Removed"
msgstr "Rimosso"

#: src/tables/stock/StockTrackingTable.tsx:206
msgid "Details"
msgstr "Dettagli"

#: src/tables/stock/StockTrackingTable.tsx:221
msgid "No user information"
msgstr "Nessuna informazione utente"

#: src/tables/stock/TestStatisticsTable.tsx:34
#: src/tables/stock/TestStatisticsTable.tsx:64
#~ msgid "Total"
#~ msgstr "Total"

#: src/views/MobileAppView.tsx:25
msgid "Mobile viewport detected"
msgstr "Rilevata la visualizzazione mobile"

#: src/views/MobileAppView.tsx:25
#~ msgid "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
#~ msgstr "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."

#: src/views/MobileAppView.tsx:28
msgid "InvenTree UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
msgstr "InvenTree UI è ottimizzato per tablet e desktop, è possibile utilizzare l'app ufficiale per un'esperienza mobile."

#: src/views/MobileAppView.tsx:34
msgid "Read the docs"
msgstr "Consulta la documentazione"

#: src/views/MobileAppView.tsx:38
msgid "Ignore and continue to Desktop view"
msgstr "Ignora e continua alla visualizzazione desktop"

