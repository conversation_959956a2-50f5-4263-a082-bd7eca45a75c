name: inventree
description: Open Source Inventory Management System
homepage: https://inventree.org
notifications: true
buildpack: https://github.com/mjmair/heroku-buildpack-python#v216-mjmair
env:
  - STACK=heroku-20
  - DISABLE_COLLECTSTATIC=1
  - INVENTREE_DB_ENGINE=sqlite3
  - INVENTREE_DB_NAME=database.sqlite3
  - INVENTREE_PLUGINS_ENABLED
  - INVENTREE_MEDIA_ROOT=/opt/inventree/media
  - INVENTREE_STATIC_ROOT=/opt/inventree/static
  - INVENTREE_BACKUP_DIR=/opt/inventree/backup
  - INVENTREE_PLUGIN_FILE=/opt/inventree/plugins.txt
  - INVENTREE_CONFIG_FILE=/opt/inventree/config.yaml
  - APP_REPO=inventree/InvenTree
before_install: contrib/packager.io/preinstall.sh
after_install: contrib/packager.io/postinstall.sh
before_remove: contrib/packager.io/preinstall.sh
before:
  - contrib/packager.io/before.sh
dependencies:
  - curl
  - "python3.9 | python3.10 | python3.11"
  - "python3.9-venv | python3.10-venv | python3.11-venv"
  - "python3.9-dev | python3.10-dev | python3.11-dev"
  - python3-pip
  - python3-cffi
  - python3-brotli
  - python3-wheel
  - libpango-1.0-0
  - libharfbuzz0b
  - libpangoft2-1.0-0
  - gettext
  - nginx
  - jq
  - "libffi7 | libffi8"
targets:
  ubuntu-20.04: true
  debian-11: true
