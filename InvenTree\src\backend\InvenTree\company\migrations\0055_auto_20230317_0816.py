# Generated by Django 3.2.18 on 2023-03-17 08:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0054_companyattachment'),
    ]

    operations = [
        migrations.AddField(
            model_name='manufacturerpart',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.AddField(
            model_name='supplierpart',
            name='metadata',
            field=models.J<PERSON><PERSON>ield(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
    ]
