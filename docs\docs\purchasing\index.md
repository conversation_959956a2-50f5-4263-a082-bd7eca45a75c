---
title: Purchasing
---

## Purchasing

InvenTree provides a comprehensive purchasing system designed to streamline the procurement process, manage suppliers, and track purchase orders. The purchasing system is designed to seamlessly control the ingestion of stock items into the InvenTree database, ensuring that all parts are properly accounted for and tracked throughout their lifecycle.

### Purchase Orders

The core of the InvenTree purchasing system is the purchase order (PO). A purchase order is a formal request to a supplier to provide specific parts or materials. Each purchase order is linked to one or more supplier parts, allowing users to easily track the procurement process and manage supplier relationships.

Read more about purchase orders in the [Purchase Order documentation](./purchase_order.md).

### Supplier Parts

InvenTree allows users to define supplier parts, which represent individual pieces or units that are procured from an external vendor. Supplier parts can be linked to specific parts in the InvenTree database, enabling users to easily track the procurement process and manage supplier relationships.

Read more about supplier parts in the [Supplier Parts documentation](./supplier.md).

### Manufacturer Parts

InvenTree also supports manufacturer parts, which represent parts that are manufactured by a specific vendor. Manufacturer parts may be available from multiple suppliers, and users can easily manage these relationships within the InvenTree system.

Read more about manufacturer parts in the [Manufacturer Parts documentation](./manufacturer.md).
