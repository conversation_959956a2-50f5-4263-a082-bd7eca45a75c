# Generated by Django 3.0.5 on 2020-04-25 12:43

import InvenTree.fields
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import mptt.fields


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0035_auto_20200406_0045'),
        ('stock', '0031_auto_20200422_0209'),
        ('order', '0029_auto_20200423_1042'),
        ('build', '0013_auto_20200425_0507'),
    ]

    operations = [
        migrations.AlterField(
            model_name='build',
            name='batch',
            field=models.CharField(blank=True, help_text='Batch code for this build output', max_length=100, null=True, verbose_name='Batch Code'),
        ),
        migrations.AlterField(
            model_name='build',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external URL', verbose_name='External Link'),
        ),
        migrations.AlterField(
            model_name='build',
            name='notes',
            field=models.TextField(blank=True, help_text='Extra build notes', verbose_name='Notes'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='build',
            name='parent',
            field=mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='children', to='build.Build', verbose_name='Parent Build'),
        ),
        migrations.AlterField(
            model_name='build',
            name='part',
            field=models.ForeignKey(help_text='Select part to build', limit_choices_to={'active': True, 'assembly': True, 'is_template': False, 'virtual': False}, on_delete=django.db.models.deletion.CASCADE, related_name='builds', to='part.Part', verbose_name='Part'),
        ),
        migrations.AlterField(
            model_name='build',
            name='quantity',
            field=models.PositiveIntegerField(default=1, help_text='Number of parts to build', validators=[django.core.validators.MinValueValidator(1)], verbose_name='Build Quantity'),
        ),
        migrations.AlterField(
            model_name='build',
            name='sales_order',
            field=models.ForeignKey(blank=True, help_text='SalesOrder to which this build is allocated', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='builds', to='order.SalesOrder', verbose_name='Sales Order Reference'),
        ),
        migrations.AlterField(
            model_name='build',
            name='status',
            field=models.PositiveIntegerField(choices=[(10, 'Pending'), (20, 'Allocated'), (30, 'Cancelled'), (40, 'Complete')], default=10, help_text='Build status code', validators=[django.core.validators.MinValueValidator(0)], verbose_name='Build Status'),
        ),
        migrations.AlterField(
            model_name='build',
            name='take_from',
            field=models.ForeignKey(blank=True, help_text='Select location to take stock from for this build (leave blank to take from any stock location)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sourcing_builds', to='stock.StockLocation', verbose_name='Source Location'),
        ),
        migrations.AlterField(
            model_name='build',
            name='title',
            field=models.CharField(help_text='Brief description of the build', max_length=100, verbose_name='Build Title'),
        ),
    ]
