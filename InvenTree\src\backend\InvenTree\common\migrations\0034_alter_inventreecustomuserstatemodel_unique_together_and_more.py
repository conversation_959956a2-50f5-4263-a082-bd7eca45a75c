# Generated by Django 4.2.17 on 2024-12-27 09:15

import common.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0033_delete_colortheme'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='inventreecustomuserstatemodel',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='inventreecustomuserstatemodel',
            name='key',
            field=models.IntegerField(help_text='Numerical value that will be saved in the models database', verbose_name='Value'),
        ),
        migrations.AlterField(
            model_name='inventreecustomuserstatemodel',
            name='name',
            field=models.CharField(help_text='Name of the state', max_length=250, validators=[common.validators.validate_uppercase, common.validators.validate_variable_string], verbose_name='Name'),
        ),
        migrations.AlterUniqueTogether(
            name='inventreecustomuserstatemodel',
            unique_together={('reference_status', 'name'), ('reference_status', 'key')},
        ),
    ]
