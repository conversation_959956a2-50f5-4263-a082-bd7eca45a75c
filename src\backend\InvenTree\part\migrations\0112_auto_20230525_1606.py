# Generated by Django 3.2.19 on 2023-05-31 12:05

"""
Note: This is a total hack method to delete columns (if they already exist).

Due to an improper set of migrations merges,
there may exist a situation where the columns (defined in the migrations below) already exist.

In this case, we want to delete the columns, and then re-add them.

Original error: https://github.com/inventree/InvenTree/pull/4898
1st fix: https://github.com/inventree/InvenTree/pull/4961
2nd fix: https://github.com/inventree/InvenTree/pull/4977
3rd fix: https://github.com/inventree/InvenTree/pull/4987
"""

from django.db import migrations, models


class RemoveFieldOrSkip(migrations.RemoveField):
    """Custom RemoveField operation which will fail gracefully if the field does not exist

    Ref: https://stackoverflow.com/questions/58518726/how-to-ignore-a-specific-migration
    """

    def database_backwards(self, app_label, schema_editor, from_state, to_state) -> None:
        # Backwards migration should not do anything
        pass

    def database_forwards(self, app_label, schema_editor, from_state, to_state) -> None:
        """Forwards migration *attempts* to remove existing fields, but will fail gracefully if they do not exist"""

        try:
            super().database_forwards(app_label, schema_editor, from_state, to_state)
            print(f'Removed field {self.name} from model {self.model_name}')
        except Exception as exc:
            pass

    def state_forwards(self, app_label, state) -> None:
        try:
            super().state_forwards(app_label, state)
        except Exception:
            pass

class AddFieldOrSkip(migrations.AddField):
    """Custom AddField operation which will fail gracefully if the field already exists

    Ref: https://stackoverflow.com/questions/58518726/how-to-ignore-a-specific-migration
    """

    def database_backwards(self, app_label, schema_editor, from_state, to_state) -> None:
        # Backwards migration should not do anything
        pass

    def database_forwards(self, app_label, schema_editor, from_state, to_state) -> None:
        """Forwards migration *attempts* to remove existing fields, but will fail gracefully if they do not exist"""

        try:
            super().database_forwards(app_label, schema_editor, from_state, to_state)
        except Exception as exc:
            pass

    def state_forwards(self, app_label, state) -> None:
        try:
            super().state_forwards(app_label, state)
        except Exception:
            pass


class Migration(migrations.Migration):

    atomic = False

    dependencies = [
        ('part', '0111_auto_20230521_1350'),
    ]

    operations = [
        RemoveFieldOrSkip(
            model_name='partparametertemplate',
            name='checkbox',
        ),
        RemoveFieldOrSkip(
            model_name='partparametertemplate',
            name='choices',
        ),
        AddFieldOrSkip(
            model_name='partparametertemplate',
            name='checkbox',
            field=models.BooleanField(default=False, help_text='Is this parameter a checkbox?', verbose_name='Checkbox'),
        ),
        AddFieldOrSkip(
            model_name='partparametertemplate',
            name='choices',
            field=models.CharField(blank=True, help_text='Valid choices for this parameter (comma-separated)', max_length=5000, verbose_name='Choices'),
        ),
    ]
