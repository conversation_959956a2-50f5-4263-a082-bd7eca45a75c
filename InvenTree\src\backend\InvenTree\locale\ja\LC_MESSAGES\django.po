msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"Language: ja_JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "二要素認証を有効にする必要があります。"

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "APIエンドポイントが見つかりません"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr "バルク運転には、品目またはフィルターのリストが必要です"

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr "項目はリストとして提供されなければなりません"

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "無効なアイテムリスト"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr "フィルタはディクショナリとして提供されなければなりません"

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "提供されたフィルタが無効"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr "指定された条件に一致する項目がありません"

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "ユーザーにこのモデルを表示する権限がありません"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "メールアドレス(確認用)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "メールアドレスの確認"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "毎回同じメールアドレスを入力する必要があります。"

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "指定されたプライマリEメールアドレスは無効です。"

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "指定されたメールドメインは承認されていません。"

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "無効な単位 ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "値がありません"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "{original}を{unit}に変換できませんでした。"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "数量コードが無効です"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "エラーの詳細は管理者パネルで確認できます"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "日付を入力する"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "無効な10進数値"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "メモ"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "値 '{name}' はパターン形式で表示されません"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "指定された値が必要なパターンと一致しません: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "一度に1000以上のアイテムをシリアライズすることはできません。"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "シリアル番号は空です"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "重複シリアル"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "無効なグループです：{group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "グループ範囲 {group} が許容数量を超過 ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "シリアル番号が見つかりません"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "この値からHTMLタグを削除"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "データに禁止されているマークダウン・コンテンツが含まれています。"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "接続エラー"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "サーバは無効なステータスコードで応答しました"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "例外が発生しました"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "サーバーが無効なContent-Length値で応答しました"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "画像サイズが大きすぎます"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "画像のダウンロードが最大サイズを超えました"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "リモートサーバーが空のレスポンスを返しました"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "指定されたURLは有効な画像ファイルではありません"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "アラビア語"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "ブルガリア語"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "チェコ語"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "デンマーク語"

#: InvenTree/locales.py:24
msgid "German"
msgstr "ドイツ語"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "ギリシャ語"

#: InvenTree/locales.py:26
msgid "English"
msgstr "英語"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "スペイン語"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "スペイン語(メキシコ)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "エストニア語"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "ペルシャ語"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "フィンランド語"

#: InvenTree/locales.py:32
msgid "French"
msgstr "フランス語"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "ヘブライ語"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "ヒンディー語"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "ハンガリー語"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "イタリア語"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "日本語"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "韓国語"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "リトアニア語"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "ラトビア語"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "オランダ語"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "ノルウェー語"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "ポーランド語"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "ポルトガル語"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "ポルトガル語 (ブラジル)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "ルーマニア語"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "ロシア語"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "スロバキア語"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "スロベニア語"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "セルビア語"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "スウェーデン語"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "タイ語"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "トルコ語"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "ウクライナ語"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "ベトナム語"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "中国語 (簡体字)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "中国語 (繁体字)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "アプリにログイン"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "メールアドレス"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "プラグイン検証の実行エラー"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "メタデータは python dict オブジェクトでなければなりません。"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "プラグインメタデータ"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "外部プラグインで使用するためのJSONメタデータフィールド"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "不適切な書式パターン"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "不明なフォーマットキーが指定されました"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "必要なフォーマットキーがありません"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "参照フィールドを空にすることはできません。"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "参照は必須パターンに一致する必要があります。"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "参照番号が大きすぎる"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "無効な選択です"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "お名前"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "説明"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "説明 (オプション)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "パス"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "同じ親に重複した名前は存在しません。"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "マークダウンメモ (オプション)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "バーコード情報"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "サードパーティ製バーコードデータ"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "バーコードハッシュ"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "バーコードデータのユニークなハッシュ"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "既存のバーコードが見つかりました"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "タスクの失敗"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "バックグラウンドワーカータスク'{f}'が{n}回試行した後に失敗しました"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "サーバーエラー"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "サーバーによってエラーが記録されました。"

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "有効な数字でなければなりません"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "通貨"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "利用可能なオプションから通貨を選択してください"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "無効な値です。"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "遠隔画像"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "外部画像ファイルのURL"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "外部URLからの画像ダウンロードは許可されていません"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "リモートURLからの画像ダウンロードに失敗しました"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "無効な物理単位"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "有効な通貨コードではありません。"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "注文ステータス"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "親ビルド"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "バリアントを含む"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "パーツ"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "カテゴリ"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "祖先ビルド"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "担当"

#: build/api.py:154
msgid "Assigned To"
msgstr "割り当て"

#: build/api.py:189
msgid "Created before"
msgstr "作成前"

#: build/api.py:193
msgid "Created after"
msgstr "の後に作成されました。"

#: build/api.py:197
msgid "Has start date"
msgstr "開始日あり"

#: build/api.py:205
msgid "Start date before"
msgstr "開始日 前"

#: build/api.py:209
msgid "Start date after"
msgstr "開始日 後"

#: build/api.py:213
msgid "Has target date"
msgstr "目標期日あり"

#: build/api.py:221
msgid "Target date before"
msgstr "目標期日"

#: build/api.py:225
msgid "Target date after"
msgstr "以降の目標日"

#: build/api.py:229
msgid "Completed before"
msgstr "完成前"

#: build/api.py:233
msgid "Completed after"
msgstr "終了後"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr "最小日付"

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr "最大日付"

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "ツリーを除く"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "削除するには、ビルドをキャンセルする必要があります。"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "消耗品"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "オプション"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "アセンブリ"

#: build/api.py:450
msgid "Tracked"
msgstr "追跡"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "テスト可能"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "受注残高"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "割り当てられた"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "利用可能"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "組立注文"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "場所"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "組立注文"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "アセンブリBOMが検証されていません"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "非アクティブな部品にビルドオーダーを作成できません。"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "ロックされていない部品にビルドオーダーを作成できません。"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "担当ユーザーまたはグループを指定する必要があります。"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "ビルドオーダー部品は変更できません"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr "目標期日は開始日以降であること"

#: build/models.py:244
msgid "Build Order Reference"
msgstr "ビルド・オーダー・リファレンス"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "参照"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "建築の簡単な説明（任意）"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "このビルドが割り当てられる ビルドオーダー"

#: build/models.py:273
msgid "Select part to build"
msgstr "製造する部品の選択"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "セールス・オーダー・リファレンス"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "このビルドが割り当てられる SalesOrder"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "ソース・ロケーション"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "このビルドで在庫を取得する場所を選択します（任意の在庫場所から取得する場合は空白のままにしてください）。"

#: build/models.py:300
msgid "External Build"
msgstr ""

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr "目的地"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "完成したアイテムの保管場所を選択"

#: build/models.py:315
msgid "Build Quantity"
msgstr "数量"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "構築するストックアイテムの数"

#: build/models.py:322
msgid "Completed items"
msgstr "完成品"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "完了した在庫アイテムの数"

#: build/models.py:328
msgid "Build Status"
msgstr "組立状況"

#: build/models.py:333
msgid "Build status code"
msgstr "ビルドステータスコード"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "バッチコード"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "このビルド出力のバッチコード"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "作成日時"

#: build/models.py:356
msgid "Build start date"
msgstr "ビルド開始日"

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr "この注文の開始予定日"

#: build/models.py:363
msgid "Target completion date"
msgstr "完成目標日"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "ビルド完了目標日。この日付を過ぎると、ビルドは期限切れになります。"

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "完了日"

#: build/models.py:378
msgid "completed by"
msgstr "完了者"

#: build/models.py:387
msgid "Issued by"
msgstr "発行者"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "このビルドオーダーを発行したユーザー"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "責任"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "このビルドオーダーを担当するユーザーまたはグループ"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "外部リンク"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "外部 サイト へのリンク"

#: build/models.py:410
msgid "Build Priority"
msgstr "組立優先度"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "建設順序の優先順位"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "プロジェクトコード"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "プロジェクトコード"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "ビルドの割り当てを完了するタスクのオフロードに失敗しました。"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "ビルドオーダー{build}が完了しました"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "建設発注が完了しました"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "追跡可能な部品については、シリアル番号の提示が必要です。"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "ビルド出力が指定されていません"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "ビルド出力はすでに完了しています"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "ビルド出力がビルド順序と一致しません"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "数量はゼロより大きくなければなりません"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "数量が出力数量を上回ることはできません"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "ビルド出力 {serial} は、必要なすべてのテストに合格していません。"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "ビルドオーダーラインアイテム"

#: build/models.py:1602
msgid "Build object"
msgstr "ビルドオブジェクト"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "数量"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "注文数量"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "ビルド項目は、ビルド出力を指定する必要があります。"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "割当数量({q})は在庫可能数量({a})を超えてはなりません。"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "在庫が過剰配分"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "割当数量はゼロより大きくなければなりません"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "シリアル在庫の場合、数量は1でなければなりません。"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "選択された在庫品目が部品表に一致しません。"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "在庫商品"

#: build/models.py:1905
msgid "Source stock item"
msgstr "ソース在庫品"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "建設に割り当てる在庫量"

#: build/models.py:1924
msgid "Install into"
msgstr "インストール"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "仕向け地在庫品"

#: build/serializers.py:115
msgid "Build Level"
msgstr "ビルドレベル"

#: build/serializers.py:124
msgid "Part Name"
msgstr "部品名"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "プロジェクトコードラベル"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "ビルド出力"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "ビルド出力が親ビルドと一致しません"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "出力部分が BuildOrder 部分と一致しません。"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "このビルド出力はすでに完了しています"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "このビルド出力は完全に割り当てられていません"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "ビルド出力の数量を入力"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "追跡可能な部品に必要な整数個数"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "部品表には追跡可能な部品が含まれるため、必要な数量は整数"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "シリアル番号"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "ビルド出力のためのシリアル番号の入力"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "ビルド出力のストック位置"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "シリアル番号の自動割り当て"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "シリアル番号が一致する必要なアイテムを自動的に割り当て"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "以下のシリアル番号は既に存在するか、無効です。"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "ビルド出力のリストを提供する必要があります。"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "スクラップされたアウトプットの在庫場所"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "廃棄割り当て"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "廃棄されたアウトプットに割り当てられた在庫の破棄"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "ビルドアウトプットを廃棄する理由"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "完成した建造物のアウトプットの場所"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "不完全割当の受入れ"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "在庫が完全に割り当てられていない場合は、出力を完了します。"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "割当在庫の消費"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "このビルドに割り当て済みのストックを消費します。"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "不完全な出力の削除"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "完了していないビルド出力を削除します。"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "不可"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "このビルド・オーダーで消費されるものとして受け入れます。"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "このビルドオーダーを完了する前に割り当てを解除します。"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "総合在庫"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "製造オーダーに割り当てられた余分な在庫品をどのように処理しますか？"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "一部の在庫品目は全体的に配分されています。"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "未割り当ての受け入れ"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "在庫アイテムがこのビルド・オーダーに完全に割り当てられていないことを受け入れます。"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "必要在庫の配分が完了していません"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "インコンプリートの受け入れ"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "必要な数のビルドアウトプットが完了していないことを受け入れます。"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "必要な構築数量が完了していません"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "ビルド・オーダーには未完成の子ビルド・オーダーがあります。"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "受注生産状態であること"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "ビルド・オーダーの出力が不完全"

#: build/serializers.py:869
msgid "Build Line"
msgstr "組立ライン"

#: build/serializers.py:877
msgid "Build output"
msgstr "ビルド出力"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "ビルド出力は同じビルド"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "ビルドラインアイテム"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.partは、ビルドオーダーと同じパーツを指す必要があります。"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "在庫があること"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "使用可能数量（{q}）を超過"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "追跡部品の割り当てには、ビルド出力を指定する必要があります。"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "追跡されていない部品の割り当てでは、ビルド出力を指定できません。"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "割り当て項目の提供"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "部品を調達する在庫場所（任意の場所から調達する場合は空白にしてください。）"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "場所を除く"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "この選択された場所から在庫商品を除外"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "交換可能ストック"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "複数の拠点にある在庫品を交換可能"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "代替ストック"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "代替部品の割り当て"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "オプション"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "オプションのBOMアイテムをビルドオーダーに割り当てます。"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "自動割り当てタスクの開始に失敗しました"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "BOMリファレンス"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "BOMパーツID"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "部品表 部品名"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "ビルド"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "サプライヤー"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "割当数量"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "ビルドリファレンス"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "部品分類名"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "追跡可能"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "継承"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "バリアントを許可"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "BOMアイテム"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "注文中"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "生産中"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "外部在庫"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "在庫状況"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "利用可能な代替ストック"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "在庫状況"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "処理待ち"

#: build/status_codes.py:12
msgid "Production"
msgstr "生産"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "保留中"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "キャンセル済"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "完了"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "受注生産に必要な在庫"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "期限切れ注文"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "ビルドオーダー{bo}は現在期限切れです"

#: common/api.py:688
msgid "Is Link"
msgstr "リンク"

#: common/api.py:696
msgid "Is File"
msgstr "ファイル"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "ユーザーにはこれらの添付ファイルを削除する権限がありません。"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "ユーザーにはこの添付ファイルを削除する権限がありません"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "無効な通貨コード"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "通貨コードの重複"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "有効な通貨コードはありません"

#: common/currency.py:146
msgid "No plugin"
msgstr "プラグインなし"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "更新しました"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "最終更新のタイムスタンプ"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "独自のプロジェクトコード"

#: common/models.py:171
msgid "Project description"
msgstr "プロジェクトの説明"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "このプロジェクトを担当するユーザーまたはグループ"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "設定キー"

#: common/models.py:780
msgid "Settings value"
msgstr "設定値"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "選択された値は有効なオプションではありません。"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "値はブール値でなければなりません。"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "値は整数値でなければなりません。"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr "値は有効な数値でなければなりません。"

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr "値がバリデーション・チェックに合格しない"

#: common/models.py:914
msgid "Key string must be unique"
msgstr "キー文字列は一意でなければなりません。"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "ユーザー"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "価格破壊数量"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "価格"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "指定数量での単価"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "エンドポイント"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "このウェブフックを受信するエンドポイント"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "このウェブフックの名前"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "有効"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "このウェブフックはアクティブですか"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "トークン"

#: common/models.py:1437
msgid "Token for access"
msgstr "アクセス用トークン"

#: common/models.py:1445
msgid "Secret"
msgstr "シークレット"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "HMACの共有秘密"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "メッセージ ID："

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "このメッセージの一意な識別子"

#: common/models.py:1563
msgid "Host"
msgstr "ホスト"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "このメッセージを受信したホスト"

#: common/models.py:1572
msgid "Header"
msgstr "ヘッダー"

#: common/models.py:1573
msgid "Header of this message"
msgstr "このメッセージのヘッダー"

#: common/models.py:1580
msgid "Body"
msgstr "本文"

#: common/models.py:1581
msgid "Body of this message"
msgstr "メッセージ本文"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "このメッセージを受信したエンドポイント"

#: common/models.py:1596
msgid "Worked on"
msgstr "作業内容"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "このメッセージに関する作業は終わったのですか？"

#: common/models.py:1723
msgid "Id"
msgstr "Id"

#: common/models.py:1725
msgid "Title"
msgstr "タイトル"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "リンク"

#: common/models.py:1729
msgid "Published"
msgstr "公開済み"

#: common/models.py:1731
msgid "Author"
msgstr "投稿者"

#: common/models.py:1733
msgid "Summary"
msgstr "概要"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "既読"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "このニュースは読まれましたか？"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "画像"

#: common/models.py:1753
msgid "Image file"
msgstr "画像ファイル"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "この画像の対象モデルタイプ"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "この画像の対象モデルID"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "カスタムユニット"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "単位記号は一意でなければなりません。"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "ユニット名は有効な識別子でなければなりません。"

#: common/models.py:1843
msgid "Unit name"
msgstr "ユニット名"

#: common/models.py:1850
msgid "Symbol"
msgstr "シンボル"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "オプションの単位記号"

#: common/models.py:1857
msgid "Definition"
msgstr "定義"

#: common/models.py:1858
msgid "Unit definition"
msgstr "ユニットの定義"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "添付ファイル"

#: common/models.py:1933
msgid "Missing file"
msgstr "ファイルがありません"

#: common/models.py:1934
msgid "Missing external link"
msgstr "外部リンクが見つかりません。"

#: common/models.py:1971
msgid "Model type"
msgstr "モデルタイプ"

#: common/models.py:1972
msgid "Target model type for image"
msgstr "画像の対象モデルタイプ"

#: common/models.py:1980
msgid "Select file to attach"
msgstr "添付ファイルを選択"

#: common/models.py:1996
msgid "Comment"
msgstr "コメント："

#: common/models.py:1997
msgid "Attachment comment"
msgstr "添付コメント"

#: common/models.py:2013
msgid "Upload date"
msgstr "アップロード日"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "ファイルがアップロードされた日付"

#: common/models.py:2018
msgid "File size"
msgstr "ファイルサイズ"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "ファイルサイズ（バイト"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "添付ファイルに指定されたモデルタイプが無効です"

#: common/models.py:2077
msgid "Custom State"
msgstr "カスタムステート"

#: common/models.py:2078
msgid "Custom States"
msgstr "カスタムステート"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "リファレンス・ステータス・セット"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "このカスタム状態で拡張されたステータスセット"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "論理キー"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "ビジネスロジックでこのカスタムステートに等しいステート論理キー"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "値"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr "モデルのデータベースに保存される数値"

#: common/models.py:2102
msgid "Name of the state"
msgstr "都道府県名"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "ラベル"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "フロントエンドに表示されるラベル"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "色"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "フロントエンドに表示される色"

#: common/models.py:2128
msgid "Model"
msgstr "モデル"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "この状態が関連するモデル"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "モデルを選択する必要があります"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "キーを選択する必要があります。"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "論理キーを選択する必要があります。"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "キーは論理キーと異なる必要があります。"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr "有効な参照ステータスクラスが提供されなければならない"

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "キーは、参照ステータスの論理キーとは異なる必要があります。"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "論理キーは、参照ステータスの論理キーに含まれていなければなりません。"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "リファレンス・ステータスの名前とは異なっていなければならない。"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "セレクションリスト"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "セレクション・リスト"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "選択リストの名前"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "選択リストの説明"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "ロック中"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "この選択リストはロックされていますか？"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "このセレクションリストは使えますか？"

#: common/models.py:2255
msgid "Source Plugin"
msgstr "ソースプラグイン"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr "選択リストを提供するプラグイン"

#: common/models.py:2261
msgid "Source String"
msgstr "ソースストリング"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr "このリストに使用されているソースを示すオプションの文字列"

#: common/models.py:2271
msgid "Default Entry"
msgstr "デフォルトエントリー"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr "この選択リストのデフォルト項目"

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "作成日"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr "選択リストが作成された日時"

#: common/models.py:2283
msgid "Last Updated"
msgstr "最終更新"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr "選択リストが最後に更新された日時"

#: common/models.py:2318
msgid "Selection List Entry"
msgstr "セレクションリスト入力"

#: common/models.py:2319
msgid "Selection List Entries"
msgstr "セレクションリスト"

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr "このエントリーが属する選択リスト"

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr "選択リストエントリーの値"

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr "選択リスト項目のラベル"

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr "選択リスト項目の説明"

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr "この選択リストはアクティブですか？"

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "バーコードスキャン"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "データ"

#: common/models.py:2377
msgid "Barcode data"
msgstr "バーコードデータ"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "バーコードをスキャンしたユーザー"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "タイムスタンプ"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "バーコードスキャンの日時"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "バーコードを処理したURLエンドポイント"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "コンテキスト"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "バーコードスキャンのコンテキストデータ"

#: common/models.py:2415
msgid "Response"
msgstr "返答"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "バーコードスキャンによるレスポンスデータ"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "結果"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "バーコードスキャンは成功しましたか？"

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr ""

#: common/models.py:2574
msgid "Email Messages"
msgstr ""

#: common/models.py:2581
msgid "Announced"
msgstr ""

#: common/models.py:2583
msgid "Sent"
msgstr ""

#: common/models.py:2584
msgid "Failed"
msgstr ""

#: common/models.py:2587
msgid "Delivered"
msgstr ""

#: common/models.py:2595
msgid "Confirmed"
msgstr ""

#: common/models.py:2601
msgid "Inbound"
msgstr ""

#: common/models.py:2602
msgid "Outbound"
msgstr ""

#: common/models.py:2607
msgid "No Reply"
msgstr ""

#: common/models.py:2608
msgid "Track Delivery"
msgstr ""

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr ""

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr ""

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr ""

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr ""

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "キー"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "新しい {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "新しい注文が作成され、お客様に割り当てられました。"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} キャンセル"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "あなたに割り当てられた注文がキャンセルされました。"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "受領品目"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "発注書と照らし合わせて商品を受領"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "返品注文に反して商品が届いた場合"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr ""

#: common/serializers.py:486
msgid "Is Running"
msgstr "走行中"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "保留タスク"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "スケジュールされたタスク"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "失敗したタスク"

#: common/serializers.py:519
msgid "Task ID"
msgstr "タスクID"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "ユニークなタスクID"

#: common/serializers.py:521
msgid "Lock"
msgstr "ロック"

#: common/serializers.py:521
msgid "Lock time"
msgstr "ロック時間"

#: common/serializers.py:523
msgid "Task name"
msgstr "タスク名"

#: common/serializers.py:525
msgid "Function"
msgstr "関数"

#: common/serializers.py:525
msgid "Function name"
msgstr "機能名"

#: common/serializers.py:527
msgid "Arguments"
msgstr "引数"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "タスク引数"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "キーワード論争"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "タスクキーワード引数"

#: common/serializers.py:640
msgid "Filename"
msgstr "ファイル名"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "モデルタイプ"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "このモデルの添付ファイルを作成または編集する権限がありません。"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr "選択リストがロックされています"

#: common/setting/system.py:97
msgid "No group"
msgstr "グループなし"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "サイトのURLが設定によってロックされています"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "再起動が必要"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "サーバーの再起動を必要とする設定が変更されました。"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "保留中の移行"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "保留中のデータベース移行数"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr "インスタンスID"

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr "このInvenTreeインスタンスの一意識別子"

#: common/setting/system.py:199
msgid "Announce ID"
msgstr "アナウンスID"

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr "サーバーのインスタンスIDをサーバーステータス情報でアナウンス（認証なし）"

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "サーバーインスタンス名"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "サーバーインスタンスの文字列記述子"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "インスタンス名を使用"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "タイトルバーにインスタンス名を使用"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "about`を表示する制限"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "スーパーユーザーにのみ `about` モーダルを表示します。"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "会社名"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "社内社名"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "ベース URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "サーバーインスタンスのベースURL"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "デフォルトの通貨"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "価格計算のベース通貨を選択"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "対応通貨"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "対応通貨コード一覧"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "通貨の更新間隔"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "為替レートの更新頻度 (ゼロに設定すると無効になります)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "日"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "通貨更新プラグイン"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "通貨更新プラグイン"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "URLからダウンロード"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "外部URLからの画像ダウンロードを許可する"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "ダウンロードサイズ制限"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "外部URL画像の最大サイズ"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "URLからのダウンロードに使用されるユーザーエージェント"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "外部URLから画像やファイルをダウンロードする際に使用するユーザーエージェントを上書きすることができます。"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "厳格なURLバリデーション"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "URL検証時にスキーマ指定を要求"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "更新チェック間隔"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "アップデートをチェックする頻度 (ゼロに設定すると無効になります)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "自動バックアップ"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "データベースとメディアファイルの自動バックアップ"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "自動バックアップ間隔"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "自動バックアップイベント間の日数を指定"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "タスク削除間隔"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "バックグラウンドタスクの結果は、指定した日数後に削除されます。"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "エラーログ削除間隔"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "エラーログは指定した日数後に削除されます。"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "通知削除間隔"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "ユーザー通知は指定された日数後に削除されます。"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "バーコードサポート"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "ウェブインターフェイスでバーコードスキャナのサポートを有効にします。"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "店舗バーコード結果"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "バーコードスキャン結果をデータベースに保存"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "バーコードスキャン最大カウント"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "バーコードスキャン結果の最大保存数"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "バーコード入力遅延"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "バーコード入力処理遅延時間"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "バーコードウェブカメラサポート"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "ブラウザのウェブカメラでバーコードのスキャンが可能"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "バーコード表示データ"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "バーコードデータをテキストとしてブラウザに表示"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "バーコード生成プラグイン"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "内部バーコードデータ生成に使用するプラグイン"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "部品改訂"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "パートのリビジョンフィールドを有効にします。"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "アセンブリ改訂のみ"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "組立部品のみ修正可能"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "アセンブリからの削除を許可"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "アセンブリで使用されている部品の削除を許可します。"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN 正規表現"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "部分IPNにマッチする正規表現パターン"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "IPNの重複を許可"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "複数のパートが同じIPNを共有できるようにします。"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "IPNの編集を許可"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "部品編集中にIPN値の変更を許可"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "部品表データのコピー"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "部品複製時にBOMデータをデフォルトでコピー"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "部品パラメータデータのコピー"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "部品複製時にデフォルトでパラメータデータをコピー"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "コピー部品テストデータ"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "部品複製時にテストデータをデフォルトでコピー"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "カテゴリー・パラメーター・テンプレートのコピー"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "部品作成時のカテゴリー・パラメーター・テンプレートのコピー"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "テンプレート"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "パーツはデフォルトのテンプレートです"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "パーツはデフォルトで他のコンポーネントから組み立てることができます"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "コンポーネント"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "パーツはデフォルトでサブコンポーネントとして使用できます"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "購入可能"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "パーツはデフォルトで購入可能です"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "販売可能"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "パーツはデフォルトで販売可能です"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "パーツはデフォルトで追跡可能です"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "バーチャル"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "パーツはデフォルトでバーチャル"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "関連部品を表示"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "部品の関連部品を表示"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "初期在庫データ"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "新規部品追加時に初期在庫を作成可能"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "サプライヤー初期データ"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "新しい部品を追加する際に、最初のサプライヤーデータを作成できるようにします。"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "部品名表示形式"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "部品名の表示形式"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "パーツカテゴリー デフォルトアイコン"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "パートカテゴリのデフォルトアイコン（空はアイコンがないことを意味します）"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "パラメータ単位の強制"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "単位が指定されている場合、パラメータ値は指定された単位に一致する必要があります。"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "価格の最小桁数"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "価格データのレンダリング時に表示する最小小数点以下の桁数"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "価格の小数点以下の桁数"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "価格データのレンダリング時に表示する小数点以下の桁数の最大値"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "サプライヤー価格の利用"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "全体的な価格計算にサプライヤーの価格破壊を含めること"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "購入履歴の上書き"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "過去の発注価格がサプライヤーの価格変動を上書き"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "ストックアイテム価格を使用"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "手動入力された在庫データから価格計算を行います。"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "在庫商品の価格設定年齢"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "この日数より古い在庫品を価格計算から除外します。"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "バリアント価格を使用"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "全体的な価格計算にバリアント価格を含む"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "アクティブバリアントのみ"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "バリアント価格の計算には、アクティブなバリアントパーツのみを使用します。"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "価格の再構築間隔"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "部品価格が自動的に更新されるまでの日数"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "社内価格"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "部品の内部価格の有効化"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "内部価格オーバーライド"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "利用可能な場合、内部価格は価格帯の計算より優先されます。"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "ラベル印刷の有効化"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "ウェブインターフェースからラベル印刷を有効にします。"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "ラベル画像DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "ラベル印刷プラグインに供給する画像ファイルを生成する際のDPI解像度"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "レポートの有効化"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "レポートの作成"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "デバッグモード"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "デバッグモードでのレポート生成（HTML出力）"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "ログレポートエラー"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "レポート生成時に発生するエラーのログ"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "ページサイズ"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "PDFレポートのデフォルトのページサイズ"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "世界的にユニークな連載"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "在庫品のシリアル番号はグローバルに一意でなければなりません。"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "枯渇在庫の削除"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "ストックアイテムが枯渇した場合のデフォルトの動作を決定します。"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "バッチコードテンプレート"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "ストックアイテムのデフォルトバッチコード生成用テンプレート"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "有効期限"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "在庫期限切れ機能の有効化"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "期限切れ株式の売却"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "期限切れ株式の売却を許可"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "在庫切れ時間"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "在庫品が期限切れとみなされるまでの日数"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "賞味期限切れ在庫の処理"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "期限切れの在庫を使用した建物の建築を許可"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "株式所有権"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "ストックロケーションとアイテムの所有権管理"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "在庫場所 デフォルトアイコン"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "在庫場所のデフォルトアイコン（空はアイコンがないことを意味します。）"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "インストール済みストックアイテムの表示"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "ストックテーブルにインストールされたストックアイテムを表示"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "アイテム取り付けの際はBOMをチェック"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "親部品のBOMには、インストールされたストックアイテムが存在する必要があります。"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "在庫切れの転送を許可"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "在庫のないストックアイテムをストックロケーション間で移動可能"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "ビルド・オーダー参照パターン"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Build Order参照フィールドの生成に必要なパターン"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "責任ある所有者を要求"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "各注文には、責任ある所有者を指定する必要があります。"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "アクティブパートが必要"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "非稼動部品の製造オーダー作成を防止"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "ロックされた部分を要求"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "ロックされていない部品の製造オーダー作成を防止"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "有効なBOMが必要"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "BOMが検証されない限り、製造オーダーが作成されないようにします。"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "クローズド・チャイルド・オーダー"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "すべてのチャイルドオーダーが終了するまで、ビルドオーダーの完了を防止します。"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "テストがパスするまでブロック"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "必要なテストがすべて合格するまで、ビルド出力が完了しないようにします。"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "返品注文の有効化"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "ユーザーインターフェイスで返品注文機能を有効にします。"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "リターンオーダー参照パターン"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "返品注文参照フィールドの生成に必要なパターン"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "完了した返品注文の編集"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "注文完了後の返品注文の編集が可能"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "販売注文参照パターン"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "販売注文参照フィールドの生成に必要なパターン"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "販売注文のデフォルト出荷"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "販売注文でデフォルト出荷を作成可能"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "完了した販売注文の編集"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "出荷または完了後の販売注文の編集を許可します。"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "出荷された注文を完了としてマーク"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "出荷済みと表示された販売注文は、「出荷済み」ステータスを回避して自動的に完了します。"

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "発注書参照パターン"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "発注書参照フィールドの生成に必要なパターン"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "完了した発注書の編集"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "出荷後または完了後の発注書の編集が可能"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr "通貨の変換"

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr "在庫を受け取る際、商品価値を基準通貨に変換"

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "自動発注"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "すべての品目を受領した時点で、発注書を完了として自動的にマーク"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "パスワード忘れ"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "ログインページでのパスワード忘れ防止機能の有効化"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "登録の有効化"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "ログインページでユーザーの自己登録を可能にします。"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "SSOの有効化"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "ログインページでSSOを有効化"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "SSO登録の有効化"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "ログインページでSSOによるユーザーの自己登録を可能にします。"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "SSOグループ同期の有効化"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "InvenTreeグループとIdPが提供するグループの同期を有効にします。"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "SSOグループキー"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "IdP が提供する groups claim 属性の名前。"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "SSOグループマップ"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "SSOグループからローカルのInvenTreeグループへのマッピング。ローカル・グループが存在しない場合は、作成されます。"

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "SSO外のグループを削除"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "ユーザーに割り当てられたグループがIdPによってバックエンドされていない場合に削除するかどうか。この設定を無効にすると、セキュリティ上の問題が発生する可能性があります。"

#: common/setting/system.py:959
msgid "Email required"
msgstr "メールアドレスは必須です"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "サインアップ時にメールの入力を要求"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "SSOユーザーの自動入力"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "SSOアカウントデータからユーザー詳細を自動入力"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "メール2回"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "サインアップの際、ユーザーに2度メールを尋ねます。"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "パスワード2回"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "サインアップ時にパスワードを2回要求"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "許可ドメイン"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "特定のドメイン(@で始まるカンマ区切り)へのサインアップを制限します。"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "登録時のグループ"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "新規ユーザ登録時に割り当てられるグループ。SSOグループ同期が有効な場合、このグループはIdPからグループを割り当てられない場合にのみ設定されます。"

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "MFAの実施"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "ユーザーは多要素セキュリティを使用する必要があります。"

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "起動時にプラグインをチェック"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "起動時にすべてのプラグインがインストールされていることを確認 - コンテナ環境では有効にします。"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "プラグインのアップデートの確認"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "インストールされているプラグインのアップデートを定期的にチェックします。"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "URL統合の有効化"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "プラグインがURLルートを追加できるようにします"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "ナビゲーション統合の有効化"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "プラグインをナビゲーションに統合可能"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "アプリとの統合"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "プラグインを有効にしてアプリを追加"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "スケジュール統合の有効化"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "スケジュールタスクを実行するプラグインの有効化"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "イベント統合の有効化"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "プラグインが内部イベントに応答できるようにします。"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "インターフェース統合の有効化"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "プラグインがユーザー・インターフェースに統合できるようにします。"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr "プロジェクトコードの有効化"

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr "プロジェクトを追跡するためのプロジェクトコードの有効化"

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "外部ロケーションを除く"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "自動引取期間"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "ユーザーのフルネームを表示"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "ユーザー名の代わりにフルネームを表示"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr "ユーザープロファイルの表示"

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr "プロフィールページにユーザーのプロフィールを表示"

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "テストステーションデータの有効化"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "テスト結果のテストステーションデータ収集の有効化"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "インラインラベル表示"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "ファイルとしてダウンロードする代わりに、ブラウザでPDFラベルを表示します。"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "デフォルトのラベルプリンター"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "デフォルトで選択されるラベルプリンタの設定"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "インラインレポート表示"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "ファイルとしてダウンロードする代わりに、ブラウザでPDFレポートを表示します。"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "部品検索"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "検索プレビューウィンドウに部品を表示"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "サプライヤー検索 部品"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "検索プレビューウィンドウにサプライヤー部品を表示"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "メーカー部品検索"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "検索プレビューウィンドウにメーカー部品を表示"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "非アクティブな部品を非表示"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "検索プレビューウィンドウから非アクティブ部分を除外"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "カテゴリーを検索"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "検索プレビューウィンドウに部品カテゴリーを表示"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "在庫検索"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "検索プレビューウィンドウに在庫アイテムを表示"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "在庫のない商品を隠す"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "検索プレビューウィンドウから利用できない在庫商品を除外します。"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "場所を検索"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "検索プレビューウィンドウに在庫場所を表示"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "企業検索"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "検索プレビューウィンドウに企業を表示"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "建設オーダーの検索"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "検索プレビューウィンドウに構築順を表示"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "注文書の検索"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "検索プレビューウィンドウに発注書を表示"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "非アクティブな購入注文の除外"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "検索プレビューウィンドウから非アクティブな発注書を除外"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "販売注文の検索"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "検索プレビューウィンドウに販売注文を表示"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "非アクティブな販売注文の除外"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "検索プレビューウィンドウから非アクティブな販売注文を除外"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "販売注文の出荷を検索"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "検索プレビューウィンドウに販売注文の出荷を表示"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "返品注文の検索"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "検索プレビューウインドウに返送オーダーを表示"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "非アクティブな返品注文の除外"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "検索プレビューウィンドウから非アクティブな返品注文を除外"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "検索プレビュー結果"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "検索プレビューウィンドウの各セクションに表示する結果の数"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "正規表現検索"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "検索クエリでの正規表現の有効化"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "全単語検索"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "検索クエリは、単語全体が一致した結果を返します。"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "メモの検索"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "検索クエリは、アイテムのノートから一致する結果を返します。"

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "エスケープキーでフォームを閉じる"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "モーダルフォームを閉じるには、エスケープキーを使用します。"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "固定ナビバー"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "ナビバーの位置は画面上部に固定されます。"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr "ナビゲーション・アイコン"

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr "ナビゲーションバーにアイコンを表示"

#: common/setting/user.py:207
msgid "Date Format"
msgstr "日付フォーマット"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "日付の表示形式"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr "最後のパンくずを表示"

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr "現在のページをパンくずで表示"

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "エラー・レポートの受信"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "システムエラーの通知を受信"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "最後の中古印刷機"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "ユーザーの最後に使用した印刷機を保存"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "アタッチメント型式なし"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "無効なアタッチメントモデルタイプ"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "最小プレースは最大プレースより大きくできません。"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "最大定員が最小定員を下回ることはありません。"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "空のドメインは使用できません。"

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "無効なドメイン名: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "値は大文字でなければならない"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "値は有効な変数識別名でなければならない。"

#: company/api.py:141
msgid "Part is Active"
msgstr "パートはアクティブ"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "メーカーはアクティブ"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "サプライヤーが活動中"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "内部はアクティブ"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "サプライヤーの活動"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "製造元"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "会社名"

#: company/api.py:316
msgid "Has Stock"
msgstr "在庫あり"

#: company/models.py:120
msgid "Companies"
msgstr "会社"

#: company/models.py:148
msgid "Company description"
msgstr "会社概要"

#: company/models.py:149
msgid "Description of the company"
msgstr "会社概要"

#: company/models.py:155
msgid "Website"
msgstr "ウェブサイト"

#: company/models.py:156
msgid "Company website URL"
msgstr "会社ホームページURL"

#: company/models.py:162
msgid "Phone number"
msgstr "電話番号"

#: company/models.py:164
msgid "Contact phone number"
msgstr "連絡先電話番号"

#: company/models.py:171
msgid "Contact email address"
msgstr "連絡先メールアドレス"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "お問い合わせ"

#: company/models.py:178
msgid "Point of contact"
msgstr "連絡先"

#: company/models.py:184
msgid "Link to external company information"
msgstr "外部企業情報へのリンク"

#: company/models.py:198
msgid "Is this company active?"
msgstr "この会社は活動していますか？"

#: company/models.py:203
msgid "Is customer"
msgstr "お客様"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "この会社に商品を販売していますか？"

#: company/models.py:209
msgid "Is supplier"
msgstr "サプライヤー"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "この会社から商品を購入しますか？"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "メーカーは"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "この会社は部品を製造しているのですか？"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "この会社で使用されるデフォルト通貨"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "住所"

#: company/models.py:355
msgid "Addresses"
msgstr "マイアカウント"

#: company/models.py:412
msgid "Select company"
msgstr "会社を選択"

#: company/models.py:417
msgid "Address title"
msgstr "住所"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "アドレスエントリを説明するタイトル"

#: company/models.py:424
msgid "Primary address"
msgstr "主な住所"

#: company/models.py:425
msgid "Set as primary address"
msgstr "プライマリアドレスに設定"

#: company/models.py:430
msgid "Line 1"
msgstr "1行目"

#: company/models.py:431
msgid "Address line 1"
msgstr "丁目、番地、号など"

#: company/models.py:437
msgid "Line 2"
msgstr "2行目"

#: company/models.py:438
msgid "Address line 2"
msgstr "建物名、部屋番号など"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "郵便番号"

#: company/models.py:451
msgid "City/Region"
msgstr "都市/地域"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "郵便番号 都市/地域"

#: company/models.py:458
msgid "State/Province"
msgstr "都道府県"

#: company/models.py:459
msgid "State or province"
msgstr "都道府県"

#: company/models.py:465
msgid "Country"
msgstr "国"

#: company/models.py:466
msgid "Address country"
msgstr "住所国"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "宅配便発送に関する注意事項"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "宅配便発送時の注意事項"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "社内出荷に関する注意事項"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "社内用出荷注意事項"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "住所情報へのリンク（外部）"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "メーカー・パーツ"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "ベース部"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "部品を選択"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "メーカー選択"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "MPN"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "メーカー品番"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "外部メーカー部品リンク用URL"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "メーカー部品説明"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "メーカー部品パラメーター"

#: company/models.py:635
msgid "Parameter name"
msgstr "パラメータ名"

#: company/models.py:642
msgid "Parameter value"
msgstr "パラメータ値"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "単位"

#: company/models.py:650
msgid "Parameter units"
msgstr "パラメータ単位"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "パックユニットは、ベースユニットと互換性がある必要があります。"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "パック単位はゼロより大きくなければなりません。"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "リンクされたメーカー部品は、同じベース部品を参照する必要があります。"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "仕入先"

#: company/models.py:829
msgid "Select supplier"
msgstr "サプライヤーを選択"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "サプライヤー在庫管理ユニット"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "このサプライヤーは活動していますか？"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "メーカー部品の選択"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "外部サプライヤー部品リンク用URL"

#: company/models.py:867
msgid "Supplier part description"
msgstr "サプライヤーの部品説明"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "備考"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "基本料金"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "ミニマムチャージ（例：仕入れ手数料）"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "パッケージング"

#: company/models.py:892
msgid "Part packaging"
msgstr "部品梱包"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "パック数量"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "1パックに供給される総量。単品の場合は空のままにしてください。"

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "複数"

#: company/models.py:919
msgid "Order multiple"
msgstr "複数注文"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "サプライヤーから入手可能な数量"

#: company/models.py:937
msgid "Availability Updated"
msgstr "空席状況更新"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "アベイラビリティ・データの最終更新日"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "サプライヤーの価格破壊"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr "プライマリ・アドレスの文字列表現を返します。このプロパティは後方互換性のために存在します。"

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "このサプライヤーで使用されるデフォルト通貨"

#: company/serializers.py:245
msgid "Company Name"
msgstr "会社名"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "在庫あり"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr "データのエクスポート中にエラーが発生しました"

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr "データエクスポートプラグインが不正なデータ形式を返しました"

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "エクスポート形式"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "エクスポートファイル形式の選択"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "エクスポートプラグイン"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "エクスポートプラグインを選択"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "この項目の追加ステータス情報"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "カスタムステータスキー"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "カスタム"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "クラス"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "値"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "設置済"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "無効なステータスコード"

#: importer/models.py:73
msgid "Data File"
msgstr "データファイル"

#: importer/models.py:74
msgid "Data file to import"
msgstr "インポートするデータファイル"

#: importer/models.py:83
msgid "Columns"
msgstr "カラム"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr "このインポートセッションのターゲットモデルタイプ"

#: importer/models.py:96
msgid "Import status"
msgstr "インポートの状態"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "フィールドのデフォルト"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "フィールドのオーバーライド"

#: importer/models.py:120
msgid "Field Filters"
msgstr "フィールドフィルター"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "一部の必須フィールドがマッピングされていません"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "カラムはすでにデータベースのフィールドにマッピングされています。"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "フィールドはすでにデータ列にマッピングされています。"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "カラムマッピングは有効なインポートセッションにリンクされている必要があります。"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "カラムがデータファイルに存在しません。"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "対象モデルにフィールドが存在しない"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "選択されたフィールドは読み取り専用です。"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "インポートセッション"

#: importer/models.py:471
msgid "Field"
msgstr "フィールド"

#: importer/models.py:473
msgid "Column"
msgstr "列"

#: importer/models.py:542
msgid "Row Index"
msgstr "行インデックス"

#: importer/models.py:545
msgid "Original row data"
msgstr "元の行データ"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "エラー"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "有効"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "サポートされていないデータファイル形式"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "データファイルを開けませんでした"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "無効なデータファイルの寸法"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "無効なフィールドのデフォルト"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "無効なフィールドのオーバーライド"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "無効なフィールドフィルタ"

#: importer/serializers.py:177
msgid "Rows"
msgstr "行"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "受け付ける行IDのリスト"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "列はありません"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "行はこのセッションに属していません"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "行に無効なデータが含まれています。"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "列はすでに完成しています"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "初期化"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "カラムのマッピング"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "データのインポート"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "加工データ"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "データファイルが最大サイズの制限を超えています"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "データファイルにはヘッダが含まれていません。"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "データファイルのカラム数が多すぎる"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "データファイルの行数が多すぎます。"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "値は有効な辞書オブジェクトでなければなりません。"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "コピー"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "各ラベルの印刷部数"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "接続済み"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "不明"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "印刷"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "メディアがありません"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "紙詰まり"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "ネットワーク接続なし"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "ラベルプリンター"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "さまざまなアイテムのラベルを直接印刷できます。"

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "プリンタの場所"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "プリンターを特定の場所に設置"

#: machine/models.py:25
msgid "Name of machine"
msgstr "機械名"

#: machine/models.py:29
msgid "Machine Type"
msgstr "機種"

#: machine/models.py:29
msgid "Type of machine"
msgstr "機械の種類"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "ドライバー"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "マシンに使用されるドライバ"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "マシンを無効にすることができます"

#: machine/models.py:95
msgid "Driver available"
msgstr "ドライバーあり"

#: machine/models.py:100
msgid "No errors"
msgstr "エラーなし"

#: machine/models.py:105
msgid "Initialized"
msgstr "初期化"

#: machine/models.py:117
msgid "Machine status"
msgstr "機械の状態"

#: machine/models.py:145
msgid "Machine"
msgstr "機械"

#: machine/models.py:157
msgid "Machine Config"
msgstr "マシン構成"

#: machine/models.py:162
msgid "Config type"
msgstr "設定タイプ"

#: order/api.py:121
msgid "Order Reference"
msgstr "注文参照"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "並外れた"

#: order/api.py:165
msgid "Has Project Code"
msgstr "プロジェクトコード"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "作成者"

#: order/api.py:183
msgid "Created Before"
msgstr "作成前"

#: order/api.py:187
msgid "Created After"
msgstr "の後に作成されました。"

#: order/api.py:191
msgid "Has Start Date"
msgstr "開始日あり"

#: order/api.py:199
msgid "Start Date Before"
msgstr "開始日 前"

#: order/api.py:203
msgid "Start Date After"
msgstr "開始日 後"

#: order/api.py:207
msgid "Has Target Date"
msgstr "目標期日あり"

#: order/api.py:215
msgid "Target Date Before"
msgstr "目標期日"

#: order/api.py:219
msgid "Target Date After"
msgstr "以降の目標日"

#: order/api.py:270
msgid "Has Pricing"
msgstr "価格"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr "完成前"

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "終了後"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "注文"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "注文完了"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "内部パーツ"

#: order/api.py:578
msgid "Order Pending"
msgstr "注文保留"

#: order/api.py:958
msgid "Completed"
msgstr "完了"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "出荷あり"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "注文"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "セールスオーダー"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "リターンオーダー"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "合計金額"

#: order/models.py:91
msgid "Total price for this order"
msgstr "この注文の合計金額"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "注文通貨"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "この注文の通貨（会社のデフォルトを使用する場合は空白のままにしてください。）"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr "この注文はロックされており、変更できません。"

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "連絡先が選択した会社と一致しません"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr "開始日は目標期日より前でなければなりません。"

#: order/models.py:436
msgid "Order description (optional)"
msgstr "ご注文内容（任意）"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "この注文のプロジェクトコードを選択してください。"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "外部ページへのリンク"

#: order/models.py:458
msgid "Start date"
msgstr "開始日"

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr "本注文の開始予定日"

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "終了日に達したら"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "お届け予定日この期日を過ぎますと延滞となります。"

#: order/models.py:487
msgid "Issue Date"
msgstr "発行日"

#: order/models.py:488
msgid "Date order was issued"
msgstr "オーダー発行日"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "この注文を担当するユーザーまたはグループ"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "本注文に関する連絡先"

#: order/models.py:517
msgid "Company address for this order"
msgstr "本注文の会社住所"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "注文参照"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "ステータス"

#: order/models.py:618
msgid "Purchase order status"
msgstr "発注状況"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "注文元の会社"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "サプライヤー・リファレンス"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "サプライヤー注文参照コード"

#: order/models.py:654
msgid "received by"
msgstr "受信"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "注文完了日"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "目的地"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr "入荷商品のお届け先"

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "部品サプライヤーは、POサプライヤーと一致する必要があります。"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "品目が発注書と一致しません"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "数量は正の数でなければなりません。"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "顧客"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "販売先"

#: order/models.py:1318
msgid "Sales order status"
msgstr "販売注文状況"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "お客様リファレンス"

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "顧客注文参照コード"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "出荷日"

#: order/models.py:1343
msgid "shipped by"
msgstr "出荷元"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "注文はすでに完了しています。"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "注文はすでにキャンセルされました"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "未完了の注文にのみ完了マークを付けることができます。"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "出荷に不備があるため、注文を完了できません。"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "割り当てに不備があるため、注文を完了できません。"

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "不完全な項目があるため、注文を完了できません。"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr "注文はロックされ、変更できません。"

#: order/models.py:1711
msgid "Item quantity"
msgstr "品目数量"

#: order/models.py:1728
msgid "Line item reference"
msgstr "行項目参照"

#: order/models.py:1735
msgid "Line item notes"
msgstr "項目"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "この行項目の目標期日（注文の目標期日を使用する場合は空白のままにしてください。）"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "行項目の説明（オプション）"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "この行の補足説明"

#: order/models.py:1788
msgid "Unit price"
msgstr "単価"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "発注書項目"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "サプライヤーの部品はサプライヤーと一致しなければなりません。"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr "サプライヤー"

#: order/models.py:1891
msgid "Received"
msgstr "受信"

#: order/models.py:1892
msgid "Number of items received"
msgstr "受領品目数"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "購入金額"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "購入単価"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "発注書追加行"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "販売注文明細"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "仮想部品を販売注文に割り当てることはできません"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "販売可能な部品のみを販売オーダーに割り当てることができます。"

#: order/models.py:2063
msgid "Sale Price"
msgstr "セール価格"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "販売単価"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "発送済み"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "出荷数量"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "販売注文の出荷"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "出荷日"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "配達日"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "貨物の引渡日"

#: order/models.py:2221
msgid "Checked By"
msgstr "チェック済み"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "この貨物をチェックしたユーザー"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "発送"

#: order/models.py:2230
msgid "Shipment number"
msgstr "出荷番号"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "追跡番号"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "貨物追跡情報"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "請求書番号"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "関連する請求書の参照番号"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "発送済み"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "出荷品目に割り当てられた在庫がありません"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "セールスオーダー追加ライン"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "販売注文の割り当て"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "在庫アイテムが割り当てられていません"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "在庫品を別部品のラインに割り当てることはできません。"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "部品のないラインに在庫を割り当てることはできません。"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "割当数量が在庫数量を超えることはできません"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "シリアル化された在庫品の場合、数量は1でなければなりません。"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "販売注文と出荷が一致しません"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "出荷が販売注文と一致しません"

#: order/models.py:2451
msgid "Line"
msgstr "ライン"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "販売注文の出荷参照"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "アイテム"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "割り当てるストックアイテムを選択"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "在庫割当数量の入力"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "リターンオーダー参照"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "返品元の会社"

#: order/models.py:2625
msgid "Return order status"
msgstr "返品状況"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "返品注文項目"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr "在庫品の指定が必要です。"

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr "返品数量が在庫数量を超える場合"

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr "返品数量はゼロより大きくなければなりません。"

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr "シリアル化されたストックアイテムの数量が無効です。"

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "お客様から返品する商品を選択"

#: order/models.py:2910
msgid "Received Date"
msgstr "受領日"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "この返品商品が届いた日付"

#: order/models.py:2923
msgid "Outcome"
msgstr "転帰"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "この項目の成果"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "この品目の返品または修理に関連する費用"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr "リターンオーダー追加ライン"

#: order/serializers.py:90
msgid "Order ID"
msgstr "注文ID"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "複製する注文のID"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "コピーライン"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr "元の注文から行項目をコピー"

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr "余分な行をコピー"

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr "元の注文から余分な項目をコピー"

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "ラインアイテム"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "完成路線"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "重複した注文"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr "この注文を複製するためのオプションを指定します。"

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "無効なオーダーID"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "サプライヤー名"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "ご注文のキャンセルはできません。"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "未完了の行項目で注文を閉じることができます。"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "注文に不備がある場合"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "ご注文は受け付けておりません。"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "自動車価格"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "サプライヤーの部品データに基づいて購入価格を自動計算"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "購入価格通貨"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "アイテムのマージ"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "同じ品目、同じ仕向け地、同じ日付の品目を1つの品目に統合します。"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "SKU"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "内部部品番号"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "内部部品名"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "サプライヤー部品の指定が必要"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "注文書の指定が必要"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "サプライヤーは発注書と一致しなければなりません。"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "発注書はサプライヤーと一致している必要があります。"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "明細"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "受取商品の配送先選択"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "入荷在庫品のバッチコード入力"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "有効期限"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr "入荷在庫の有効期限の入力"

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "入荷した在庫品のシリアル番号の入力"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr "入荷在庫品の包装情報の上書き"

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr "在庫品の入荷に関する注意事項"

#: order/serializers.py:826
msgid "Barcode"
msgstr "バーコード"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "スキャンされたバーコード"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "バーコードはすでに使用されています"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "項目は必ずご記入ください。"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "デスティネーション・ロケーションを指定する必要があります。"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "バーコードの値は一意でなければなりません。"

#: order/serializers.py:1066
msgid "Shipments"
msgstr "発送"

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "完了した出荷"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "販売価格通貨"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr "割当項目"

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "出荷の詳細は記載されていません"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "ラインアイテムは、この注文に関連付けられていません。"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "数量は正数でなければなりません。"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "割り当てるシリアル番号を入力"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "出荷済み"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "この注文には出荷が関連付けられていません"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "以下のシリアル番号に該当するものは見つかりませんでした。"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "以下のシリアル番号はご利用いただけません。"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "返品注文項目"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "ラインアイテムが返品オーダーと一致しません"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "ラインアイテムはすでに受領済み"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "商品の受け取りは、進行中の注文に対してのみ可能です。"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr "返品数量"

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "ライン価格通貨"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "紛失"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "返品済"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "処理中"

#: order/status_codes.py:105
msgid "Return"
msgstr "戻る"

#: order/status_codes.py:108
msgid "Repair"
msgstr "修理"

#: order/status_codes.py:111
msgid "Replace"
msgstr "置換"

#: order/status_codes.py:114
msgid "Refund"
msgstr "返金"

#: order/status_codes.py:117
msgid "Reject"
msgstr "拒否"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "期限切れ発注書"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "発注書{po}は現在期限切れです"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "期限切れ販売注文"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "セールスオーダー{so}は現在期限切れです。"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr "期限切れ返品注文"

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr "リターンオーダー{ro}は現在期限切れです"

#: part/api.py:111
msgid "Starred"
msgstr "スター付き"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "星の数で絞り込む"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "深さ"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "カテゴリの深さでフィルタリング"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "最多メンバーレベル"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "トップレベルカテゴリーによるフィルタリング"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "カスケード表示"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "フィルタリング結果にサブカテゴリーを含めることができます。"

#: part/api.py:185
msgid "Parent"
msgstr "親"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "親カテゴリーによる絞り込み"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "指定したカテゴリの下にあるサブカテゴリを除外します。"

#: part/api.py:434
msgid "Has Results"
msgstr "実績あり"

#: part/api.py:660
msgid "Is Variant"
msgstr "バリエーション？"

#: part/api.py:668
msgid "Is Revision"
msgstr "改訂版"

#: part/api.py:678
msgid "Has Revisions"
msgstr "改定あり"

#: part/api.py:859
msgid "BOM Valid"
msgstr "BOM有効"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "組み立て部分はテスト可能"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "コンポーネント部分はテスト可能"

#: part/api.py:1576
msgid "Uses"
msgstr "用途"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "パーツカテゴリ"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "パーツカテゴリ"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "デフォルトの場所"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "このカテゴリの部品のデフォルトの場所"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "構造に関するパターン"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "部品は構造カテゴリーに直接割り当てることはできませんが、子カテゴリーに割り当てることはできます。"

#: part/models.py:134
msgid "Default keywords"
msgstr "デフォルトキーワード"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "このカテゴリの部品のデフォルトキーワード"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "アイコン"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "アイコン (オプション)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "いくつかの部品がすでに割り当てられているため、この部品カテゴリを構造化することはできません！"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "パーツ"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "この部分はロックされているため削除できません"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "このパートはまだアクティブなので削除できません。"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "この部品はアセンブリで使用されているため、削除できません。"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "パート'{self}'は'{parent}'（再帰的）のBOMでは使用できません。"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "パート'{parent}'は'{self}'のBOMで使用（再帰的）"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPNは正規表現パターン{pattern}に一致しなければなりません。"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "パートはそれ自体の改訂にはなりえません"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "すでにリビジョンとなっている部分のリビジョンを作成することはできません。"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "リビジョンコードの指定が必要"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "修正が許されるのは組立部品のみ"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "テンプレート部品のリビジョンを作成できません"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "親部品は同じテンプレートを指す必要があります。"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "このシリアル番号の在庫品はすでに存在します"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "パート設定でIPNの重複が許可されていません。"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "重複する部品リビジョンが既に存在します。"

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "この名前、IPN、リビジョンを持つ部品は既に存在します。"

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "部品を構造部品のカテゴリーに割り当てることはできません！"

#: part/models.py:1051
msgid "Part name"
msgstr "部品名"

#: part/models.py:1056
msgid "Is Template"
msgstr "テンプレート"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "この部品はテンプレート部品ですか？"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "この部品は他の部品の変形ですか？"

#: part/models.py:1068
msgid "Variant Of"
msgstr "変種"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "部品の説明（オプション）"

#: part/models.py:1082
msgid "Keywords"
msgstr "キーワード"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "検索結果での視認性を向上させる部分キーワード"

#: part/models.py:1093
msgid "Part category"
msgstr "パーツカテゴリ"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "即時支払通知"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "部品のリビジョンまたはバージョン番号"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "リビジョン"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "この部品は他の部品の改訂版ですか？"

#: part/models.py:1119
msgid "Revision Of"
msgstr "改訂版"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "この商品は通常どこに保管されていますか？"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "デフォルト・サプライヤー"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "サプライヤーのデフォルト部品"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "デフォルトの有効期限"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "この部品の在庫品の有効期限（日単位"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "最小在庫"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "最低許容在庫量"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "この部品の単位"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "この部品は他の部品から作ることができますか？"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "この部品を使って他の部品を作ることはできますか？"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "このパーツはユニークなアイテムの追跡が可能ですか？"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "この部品にテスト結果を記録することはできますか？"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "この部品は外部のサプライヤーから購入できますか？"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "この部品は顧客に販売できますか？"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "この部分はアクティブですか？"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "ロックされた部分は編集できません"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "これは、ソフトウェア製品やライセンスなどの仮想部品ですか？"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "BOMチェックサム"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "保存されたBOMのチェックサム"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "BOMチェック済み"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "BOMチェック日"

#: part/models.py:1312
msgid "Creation User"
msgstr "作成ユーザー"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "この部分の責任者"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "複数販売"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "価格計算のキャッシュに使用される通貨"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "最小BOMコスト"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "構成部品の最低コスト"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "最大BOMコスト"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "構成部品の最大コスト"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "最低購入価格"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "過去の最低購入価額"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "最大購入費用"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "過去の最高購入価格"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "最低社内価格"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "社内価格ブレークに基づく最低コスト"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "社内最高価格"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "社内価格ブレークに基づく最大コスト"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "最低供給価格"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "外部サプライヤーからの部品の最低価格"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "サプライヤー最高価格"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "外部サプライヤーからの部品の最高価格"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "最小バリアントコスト"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "バリアントパーツの最小コストの計算"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "最大バリアントコスト"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "バリアント部品の最大コストの計算"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "最低料金"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "最低コストのオーバーライド"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "最大コスト"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "最大コストのオーバーライド"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "総合的な最小コストの計算"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "総合最大コストの計算"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "最低販売価格"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "価格破壊に基づく最低販売価格"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "最高販売価格"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "価格破壊に基づく最高販売価格"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "最低販売価格"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "過去の最低売却価格"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "最大販売価格"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "過去の最高売却価格"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "ストックテイク用部品"

#: part/models.py:3444
msgid "Item Count"
msgstr "個数"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "棚卸時の個別在庫数"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "ストックテイク時の在庫可能量"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "日付"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "ストックテイク実施日"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "最低在庫コスト"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "手元在庫の最低見積原価"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "最大在庫コスト"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "手元在庫の最大見積原価"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "パーツセール価格"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "部品試験テンプレート"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "無効なテンプレート名 - 英数字を1文字以上含む必要があります。"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "選択肢はユニークでなければなりません"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "テストテンプレートは、テスト可能な部分に対してのみ作成できます。"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "同じキーを持つテスト・テンプレートがパートに既に存在します。"

#: part/models.py:3684
msgid "Test Name"
msgstr "試験名"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "テストの名前を入力します。"

#: part/models.py:3691
msgid "Test Key"
msgstr "テストキー"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "テストの簡易キー"

#: part/models.py:3699
msgid "Test Description"
msgstr "試験内容"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "このテストの説明を入力してください。"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "有効"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "このテストは有効ですか？"

#: part/models.py:3709
msgid "Required"
msgstr "必須"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "このテストは合格するために必要ですか？"

#: part/models.py:3715
msgid "Requires Value"
msgstr "価値が必要"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "このテストは、テスト結果を追加する際に値を必要としますか？"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "アタッチメントが必要"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "この試験では、試験結果を追加する際にファイルの添付が必要ですか。"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "選択肢"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "このテストで有効な選択肢（カンマ区切り）"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "部品パラメータテンプレート"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "チェックボックスのパラメータに単位を指定することはできません。"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "チェックボックスパラメータに選択肢を持たせることはできません。"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "パラメータ・テンプレート名は一意でなければなりません。"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "パラメータ名"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "このパラメータの物理単位"

#: part/models.py:3865
msgid "Parameter description"
msgstr "パラメータの説明"

#: part/models.py:3871
msgid "Checkbox"
msgstr "チェックボックス"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "このパラメータはチェックボックスですか？"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "このパラメータの有効な選択肢（カンマ区切り）"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr "このパラメータの選択リスト"

#: part/models.py:3931
msgid "Part Parameter"
msgstr "部品パラメータ"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "パラメータは変更できません。"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "パラメータ値の選択が無効"

#: part/models.py:4046
msgid "Parent Part"
msgstr "親部分"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "パラメータテンプレート"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "パラメータ値"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "任意のメモ欄"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "部品分類パラメータテンプレート"

#: part/models.py:4176
msgid "Default Value"
msgstr "初期値"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "パラメータのデフォルト値"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "BOMアイテムは変更できません - アセンブリがロックされています。"

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "BOM アイテムは変更できません - バリアントアセンブリがロックされています。"

#: part/models.py:4363
msgid "Select parent part"
msgstr "親部品を選択"

#: part/models.py:4373
msgid "Sub part"
msgstr "サブパート"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "BOMで使用する部品を選択"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "このBOMアイテムのBOM数量"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "この部品表はオプションです。"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "このBOMアイテムは消耗品です。"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "BOMアイテムリファレンス"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "BOMアイテムノート"

#: part/models.py:4451
msgid "Checksum"
msgstr "チェックサムi"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "BOMラインのチェックサム"

#: part/models.py:4457
msgid "Validated"
msgstr "検証済み"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "このBOMアイテムは検証済みです"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "継承"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "この BOM アイテムは、バリアントパーツの BOM に継承されます。"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "このBOMアイテムには、バリアントパーツのストックアイテムを使用できます。"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "数量は追跡可能な部品の場合、整数値でなければなりません。"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "サブパーツの指定が必要"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "BOMアイテム代替"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "代用部品はマスター部品と同じにすることはできません。"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "親BOMアイテム"

#: part/models.py:4782
msgid "Substitute part"
msgstr "代用部品"

#: part/models.py:4798
msgid "Part 1"
msgstr "パート #1"

#: part/models.py:4806
msgid "Part 2"
msgstr "パート #2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "関連部品を選択"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr "この関係について"

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "部品とそれ自身との間に部品関係を作ることはできません。"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "重複する関係が既に存在します。"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "親カテゴリ"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "親部品カテゴリー"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "サブカテゴリ"

#: part/serializers.py:202
msgid "Results"
msgstr "結果"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "このテンプレートに対して記録された結果の数"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "この在庫商品の購入通貨"

#: part/serializers.py:275
msgid "File is not an image"
msgstr "ファイルが画像ではありません"

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "このテンプレートを使用する部品の数"

#: part/serializers.py:480
msgid "Original Part"
msgstr "オリジナルパート"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "複製する元の部品を選択"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "コピー画像"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "元の部分から画像をコピー"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "コピーBOM"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "元の部品から部品表をコピー"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "コピーパラメータ"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "元の部品からパラメータデータをコピー"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "コピーノート"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "元のパートからメモをコピー"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "初期在庫量"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "この部品の初期在庫数量を指定します。数量が0の場合、在庫は追加されません。"

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "初期在庫場所"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "この部品の初期在庫場所を指定してください。"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "サプライヤーを選択してください。"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "メーカーを選択してください。"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "メーカー品番"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "選択された企業は有効なサプライヤーではありません。"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "選択された会社は有効な製造業者ではありません。"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "このMPNに一致するメーカー部品はすでに存在します。"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "このSKUに一致するサプライヤー部品は既に存在します。"

#: part/serializers.py:907
msgid "Category Name"
msgstr "カテゴリ名"

#: part/serializers.py:936
msgid "Building"
msgstr "建物"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "在庫商品"

#: part/serializers.py:968
msgid "Revisions"
msgstr "リビジョン"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "仕入先"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "総在庫"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "未割当株式"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "バリアントストック"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "重複部分"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "別のパートから初期データをコピー"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "初期在庫"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "初期在庫数で部品を作成"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "サプライヤー情報"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "この部品の初期サプライヤー情報を追加します。"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "コピーカテゴリパラメータ"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "選択したパーツカテゴリーからパラメータテンプレートをコピー"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "既存イメージ"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "既存の部品画像のファイル名"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "画像ファイルが存在しません"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "部品表全体の検証"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "ビルド"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "最小価格"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "最低価格の計算値の上書き"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "最低価格通貨"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "最大価格"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "最高価格の計算値を上書き"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "最高価格通貨"

#: part/serializers.py:1498
msgid "Update"
msgstr "更新"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "この部品の価格を更新"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "提供された通貨から{default_currency}に変換できませんでした。"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "最低価格は最高価格を超えてはなりません。"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "最高価格は最低価格を下回ってはなりません。"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr "親アセンブリを選択"

#: part/serializers.py:1716
msgid "Select the component part"
msgstr "構成部品の選択"

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "BOMをコピーする部品を選択します。"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "既存データの削除"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "コピー前に既存のBOMアイテムを削除"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "インクルード継承"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "テンプレート化された部品から継承されたBOM項目を含めます。"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "無効な行をスキップ"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "無効な行をスキップするには、このオプションを有効にします。"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "コピー代用部品"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "BOMアイテムの重複時に代替部品をコピー"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "在庫不足通知"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "{part.name}の在庫が設定された最低レベルを下回りました。"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr "組み込み"

#: plugin/api.py:92
msgid "Mandatory"
msgstr "必須"

#: plugin/api.py:107
msgid "Sample"
msgstr "サンプル"

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "インストール済み"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "プラグインは現在アクティブなので削除できません。"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "アクションが指定されていません"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "一致するアクションが見つかりませんでした"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "バーコードデータが見つかりません"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "バーコードデータとの一致が確認されました。"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "モデルはサポートされていません"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "モデルインスタンスが見つかりません"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "バーコードが既存のアイテムと一致"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "一致する部品データが見つかりません"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "一致するサプライヤー部品は見つかりませんでした"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "一致するサプライヤー部品が複数見つかりました"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "バーコードデータに一致するプラグインは見つかりませんでした"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "適合部品"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "商品はすでに受領済みです。"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "サプライヤーのバーコードに一致するプラグインがない"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "複数の一致する行項目が見つかりました"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "該当する項目が見つかりません"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "販売注文はありません"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "バーコードが既存の在庫品と一致しません"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "在庫品目が行品目と一致しません"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "在庫不足"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "販売注文に割り当てられた在庫品目"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "情報が不足しています"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "一致するアイテムが見つかりました"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "サプライヤーの部品が品目と一致しない"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "ライン・アイテムはすでに完了している"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "ライン・アイテムを受け取るために必要な詳細情報"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "受領済み発注書項目"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "ラインアイテムの受信に失敗"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "スキャンされたバーコードデータ"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "バーコードを生成するモデル名"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "バーコードを生成するモデルオブジェクトの主キー"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "発注書"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr "注文書が未開封"

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "商品を受け取るサプライヤー"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "商品を受け取るPurchaseOrder"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "発注が行われていません"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "商品を受け取る場所"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "構造上の位置を選択できません"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr "物品を受け取るための購買発注項目"

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr "在庫アイテムを自動的に発注書に割り当てる"

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "セールスオーダー"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr "販売注文がオープンされていません"

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "商品を割り当てる販売注文項目"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "商品を割り当てる販売注文の出荷"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "出荷済み"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "配分数量"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "ラベル印刷に失敗しました"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "ラベルをPDFにレンダリングする際のエラー"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "ラベルをHTMLにレンダリングする際のエラー"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "印刷する項目はありません"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "プラグイン名"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "フィーチャー・タイプ"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "フィーチャー・レーベル"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "機能のタイトル"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "機能の説明"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "特長アイコン"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "機能オプション"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "フィーチャー・コンテキスト"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "フィーチャーソース（javascript）"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "バーコード"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "バーコードのネイティブサポートを提供"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "InvenTreeの貢献者"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "内部バーコードフォーマット"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "内部バーコードフォーマットの選択"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSONバーコード（可読）"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "短いバーコード（スペース最適化）"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "短いバーコードプレフィックス"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "複数のInvenTreeインスタンスがある環境で便利です。"

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr "レベル"

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr "在庫データ"

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr "部品在庫データを含む"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr "価格データ"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr "部品価格データを含む"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr "サプライヤーデータ"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr "サプライヤーデータを含む"

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr "メーカーデータ"

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr "メーカーデータを含む"

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr "代替データ"

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr "代替部品データを含む"

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr "パラメータデータ"

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr "部品パラメータデータを含む"

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr "マルチレベルBOMエクスポート"

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr "マルチレベルBOMのエクスポートをサポートします。"

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr "BOMレベル"

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr "代替{n}"

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr "サプライヤー {n}"

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr "サプライヤー {n} SKU"

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr "サプライヤー {n} MPN"

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr "メーカー {n}"

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr "メーカー {n} MPN"

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr "InvenTree ジェネリック・エクスポーター"

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr "InvenTreeからのデータエクスポートをサポートします。"

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr "部品パラメータエクスポーター"

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr "部品パラメータデータエクスポーター"

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Slack受信ウェブフックURL"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "slack チャンネルにメッセージを送信する際に使用する URL。"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "リンクを開く"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "インベンツリー両替所"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "デフォルトの為替統合"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr "部品通知"

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr "パーツ変更に関するユーザーへの通知"

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr "通知を送信する"

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr "パーツ変更の通知を購読ユーザーに送信"

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr "部品変更通知"

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr "パーツ `{part.name}` が `{part_action}` イベントでトリガーされました。"

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDFラベルプリンタ"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "PDFラベルの印刷をネイティブでサポート"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "デバッグモード"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "デバッグモードを有効にする - PDFの代わりに生のHTMLを返します。"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "ラベルプリンター"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "機械による印刷をサポートします。"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "最終使用"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "オプション"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "ラベルシートのページサイズ"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "ラベルをスキップ"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "ラベルシートの印刷時に、このラベル数をスキップします。"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "ボーダー"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "各ラベルの周囲に枠線を印刷します。"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "ランドスケープ"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "ラベルシートを横向きに印刷"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "InvenTree ラベルシート・プリンタ"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "複数のラベルを1枚のシートに配列"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "ラベルがページサイズに対して大きすぎる"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "ラベルは生成されませんでした"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "仕入先の統合 - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "DigiKeyバーコードのスキャンをサポートします。"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "「DigiKey」として活動するサプライヤー"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "サプライヤーの統合 - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "LCSCバーコードのスキャンをサポートします。"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "「LCSC」として活動するサプライヤー"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "サプライヤー統合 - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "マウザーバーコードのスキャンをサポートします。"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "「Mouser」として活動するサプライヤー"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "サプライヤー統合 - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "TMEバーコードのスキャンをサポートします。"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "「TME」として活動するサプライヤー"

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "スタッフユーザーのみがプラグインを管理できます"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "プラグインのインストールが無効"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "プラグインのインストールに成功しました"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "{path} にプラグインをインストール"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "プラグインがレジストリに見つかりませんでした"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "プラグインはパッケージ化されていません"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "プラグインパッケージ名が見つかりません"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "プラグインのアンインストールが無効"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "プラグインがアクティブなため、アンインストールできません。"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr "プラグインがインストールされていません"

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr "プラグインのインストールが見つかりません"

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "プラグインのアンインストールに成功"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "プラグインの設定"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "プラグインの設定"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "プラグインのキー"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "プラグインのプラグイン名"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "ご利用プラン"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "プラグインがPIP経由でインストールされた場合、インストールされたパッケージの名前"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "プラグインは有効ですか"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "サンプルプラグイン"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "組み込みプラグイン"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr "必須プラグイン"

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "パッケージプラグイン"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "プラグイン"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "著者は見つかりませんでした"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "プラグイン'{p}'は現在のInvenTreeバージョン{v}と互換性がありません。"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "プラグインには少なくともバージョン {v} が必要です。"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "プラグインに必要なバージョンは最大で{v}です。"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "POの有効化"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "InvenTreeインターフェイスでPO機能を有効にします。"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "APIキー"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "外部APIへのアクセスに必要なキー"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "日数"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "数値設定"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "選択肢の設定"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "複数の選択肢がある設定"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "サンプル為替プラグイン"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTreeの貢献者"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "パーツパネルの有効化"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "パートビューのカスタムパネルを有効化"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "発注パネルの有効化"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "発注書ビューのカスタムパネルを有効化"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "ブロークンパネルの有効化"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "壊れたパネルをテストできるようにします。"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "ダイナミックパネルの有効化"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "テスト用ダイナミックパネルの有効化"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "パートパネル"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "ダッシュボードの故障"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "これは壊れたダッシュボードアイテムです - レンダリングされません！"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "ダッシュボード項目例"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "これはダッシュボードのサンプルです。シンプルなHTML文字列をレンダリングします。"

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "コンテキスト・ダッシュボード項目"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "管理者ダッシュボード項目"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "これは管理者専用のダッシュボード項目です。"

#: plugin/serializers.py:86
msgid "Source File"
msgstr "ソースファイル"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "管理者統合用ソースファイルへのパス"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "管理者統合のためのオプションのコンテキストデータ"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "ソース URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "パッケージのソース - これはカスタムレジストリでも VCS パスでもかまいません。"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "プラグインパッケージの名前 - バージョンインジケータを含むこともできます。"

#: plugin/serializers.py:128
msgid "Version"
msgstr "バージョン"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "プラグインのバージョン指定子。最新バージョンの場合は空白にしてください。"

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "プラグインのインストールを確認"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "これでこのプラグインが現在のインスタンスにインストールされます。インスタンスはメンテナンスに入ります。"

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "設置未確認"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "URL のパッケージ名のどちらかを指定する必要があります。"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "フルリロード"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "プラグインレジストリのフルリロードを実行します。"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "強制リロード"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "プラグイン・レジストリがすでにロードされていても、強制的に再ロードします。"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "プラグインの収集"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "プラグインの収集とレジストリへの追加"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "プラグインを有効化"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "このプラグインを有効化します"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "設定の削除"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "データベースからプラグイン設定を削除します"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "アイテム"

#: report/api.py:114
msgid "Plugin not found"
msgstr "プラグインが見つかりません"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr "プラグインはラベル印刷をサポートしていません"

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr "無効なラベル寸法"

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr "テンプレートに有効な項目がありません"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "リーガル"

#: report/helpers.py:46
msgid "Letter"
msgstr "文字"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr "この名前のテンプレートファイルは既に存在します。"

#: report/models.py:217
msgid "Template name"
msgstr "テンプレート名"

#: report/models.py:223
msgid "Template description"
msgstr "テンプレート説明"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr "リビジョン番号（自動インクリメント）"

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr "プリントのモデルに装着"

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "印刷時に、リンクされたモデルインスタンスに対してレポート出力を添付ファイルとして保存"

#: report/models.py:281
msgid "Filename Pattern"
msgstr "ファイル名パターン"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr "ファイル名生成パターン"

#: report/models.py:287
msgid "Template is enabled"
msgstr "テンプレートが有効"

#: report/models.py:294
msgid "Target model type for template"
msgstr "テンプレートの対象モデルタイプ"

#: report/models.py:314
msgid "Filters"
msgstr "フィルター"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "テンプレートクエリフィルタ（key=valueペアのカンマ区切りリスト）"

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "テンプレートファイル"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "PDFレポートのページサイズ"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "レポートを横向きにレンダリング"

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr "テンプレート{self.name}から生成されたレポート"

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr "レポート生成エラー"

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "幅 [mm］"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "ラベル幅（mm単位）"

#: report/models.py:674
msgid "Height [mm]"
msgstr "高さ [mm］"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "ラベルの高さ（mm単位）"

#: report/models.py:780
msgid "Error printing labels"
msgstr "ラベル印刷エラー"

#: report/models.py:799
msgid "Snippet"
msgstr "スニペット"

#: report/models.py:800
msgid "Report snippet file"
msgstr "レポートスニペットファイル"

#: report/models.py:807
msgid "Snippet file description"
msgstr "スニペットファイルの説明"

#: report/models.py:825
msgid "Asset"
msgstr "資産"

#: report/models.py:826
msgid "Report asset file"
msgstr "レポート資産ファイル"

#: report/models.py:833
msgid "Asset file description"
msgstr "アセットファイルの説明"

#: report/serializers.py:96
msgid "Select report template"
msgstr "レポートテンプレートの選択"

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr "レポートに含める項目の主キーのリスト"

#: report/serializers.py:137
msgid "Select label template"
msgstr "ラベルテンプレートの選択"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr "印刷プラグイン"

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr "ラベル印刷に使用するプラグインを選択します。"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR コード"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR コード"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "部品表"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "必要な材料"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "部品画像"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "発行済"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "必須"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "発行者"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "サプライヤーが削除されました"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "単価"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "追加項目"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "合計"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "シリアル番号"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "割り当て"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "スクール機能"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "在庫場所項目"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "在庫品テストレポート"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "設置項目"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "シリアル"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "テストの結果"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "テスト"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "パス"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "失敗"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "結果なし（必須）"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "何も結果はありません"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "アセットファイルが存在しません"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "画像ファイルが見つかりません"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image タグには Part インスタンスが必要です。"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "company_image タグには Company インスタンスが必要です。"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "場所の深さによる絞り込み"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr "トップレベルのロケーションによるフィルタリング"

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "フィルタリング結果にサブロケーションを含めることができます。"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "親の位置"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "親の所在地でフィルタリング"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr "部品名（大文字・小文字を区別しません）"

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr "パート名に含まれるもの（大文字・小文字を区別しません）"

#: stock/api.py:594
msgid "Part name (regex)"
msgstr "部品名（正規表現）"

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr "パートIPN（大文字と小文字を区別しません）"

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr "パートIPNに含まれるもの（大文字と小文字は区別されません）"

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr "パートIPN（正規表現）"

#: stock/api.py:623
msgid "Minimum stock"
msgstr "最小在庫"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "最大在庫"

#: stock/api.py:630
msgid "Status Code"
msgstr "ステータスコード"

#: stock/api.py:670
msgid "External Location"
msgstr "外部ロケーション"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr "ビルド・オーダーで消費"

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr "その他在庫品に装着"

#: stock/api.py:868
msgid "Part Tree"
msgstr "パートツリー"

#: stock/api.py:890
msgid "Updated before"
msgstr "更新前"

#: stock/api.py:894
msgid "Updated after"
msgstr "更新後"

#: stock/api.py:898
msgid "Stocktake Before"
msgstr "ストックテイク前"

#: stock/api.py:902
msgid "Stocktake After"
msgstr "ストックテイク後"

#: stock/api.py:907
msgid "Expiry date before"
msgstr "有効期限"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "有効期限"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "期限失効"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "数量が必要です"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "有効な部品を供給する必要があります。"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "指定されたサプライヤの部品が存在しません。"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "サプライヤー部品にはパックサイズが定義されていますが、use_pack_sizeフラグが設定されていません。"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "追跡不可能な部品については、シリアル番号は提供できません。"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "在庫ロケーションタイプ"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "ストックロケーションの種類"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "アイコンが設定されていないすべての場所のデフォルトアイコン (オプション)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "ストックロケーション"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "在庫場所"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "所有者"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "所有者を選択"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "ストックアイテムは、構造的なストックロケーションに直接配置されることはありませんが、子ロケーションに配置されることはあります。"

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "外部"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "これは外部の在庫場所です。"

#: stock/models.py:233
msgid "Location type"
msgstr "ロケーションタイプ"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "このロケーションのロケーションタイプ"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "いくつかのストックアイテムがすでにストックロケーションに配置されているため、このストックロケーションを構造化することはできません！"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr "部品の指定が必要"

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "在庫品は、構造的な在庫場所に配置することはできません！"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "仮想部品にストックアイテムを作成できません"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "パートタイプ('{self.supplier_part.part}')は{self.part}でなければなりません。"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "シリアル番号のある商品は数量が1でなければなりません。"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "数量が1以上の場合、シリアル番号は設定できません。"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "アイテムはそれ自身に属することはできません"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "is_building=Trueの場合、アイテムはビルド・リファレンスを持っていなければならない。"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "ビルド参照が同じ部品オブジェクトを指していません。"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "親株式"

#: stock/models.py:1028
msgid "Base part"
msgstr "ベース部"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "この在庫品に一致するサプライヤー部品を選択してください"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "この在庫品はどこにありますか？"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "この在庫品は以下の梱包で保管されています。"

#: stock/models.py:1064
msgid "Installed In"
msgstr "設置場所"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "このアイテムは他のアイテムにインストールされていますか？"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "この商品のシリアル番号"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "このストックアイテムのバッチコード"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "在庫数"

#: stock/models.py:1120
msgid "Source Build"
msgstr "ソースビルド"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "このストックアイテムのビルド"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "消費者"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "このストックアイテムを消費したビルドオーダー"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "発注元"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "この在庫商品の購入注文"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "販売先オーダー"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "在庫品の有効期限。この日を過ぎると在庫は期限切れとなります。"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "枯渇時に削除"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "在庫がなくなったら、このストックアイテムを削除します。"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "購入時の単品購入価格"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "パートに変換"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "部品が追跡可能に設定されていません"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "数量は整数でなければなりません。"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "数量は在庫数 ({self.quantity}) を超えてはなりません。"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr "シリアル番号はリストとして提供されなければなりません"

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "数量がシリアル番号と一致しません"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "テストテンプレートが存在しません"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "在庫商品が販売注文に割り当てられました"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "ストックアイテムが他のアイテムに装着されている場合"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "在庫商品には他の商品が含まれています。"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "在庫商品が顧客に割り当てられました"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "在庫品は現在生産中です。"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "連番在庫の統合はできません"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "在庫品の重複"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "在庫品目は同じ部品を参照してください。"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "在庫品は同じサプライヤーの部品を参照する必要があります。"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "在庫状況コードが一致していること"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "在庫がないため移動できません。"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr "ストックアイテムのトラッキング"

#: stock/models.py:2847
msgid "Entry notes"
msgstr "記入上の注意"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr "在庫品テスト結果"

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "このテストには値を指定する必要があります。"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "このテストには添付ファイルをアップロードする必要があります。"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr "このテストでは無効な値です。"

#: stock/models.py:2951
msgid "Test result"
msgstr "試験結果"

#: stock/models.py:2958
msgid "Test output value"
msgstr "テスト出力値"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "試験結果添付"

#: stock/models.py:2970
msgid "Test notes"
msgstr "テストノート"

#: stock/models.py:2978
msgid "Test station"
msgstr "テストステーション"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "試験が実施された試験ステーションの識別子。"

#: stock/models.py:2985
msgid "Started"
msgstr "開始"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "テスト開始のタイムスタンプ"

#: stock/models.py:2992
msgid "Finished"
msgstr "修了済み"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "テスト終了のタイムスタンプ"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "生成バッチコード"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "製造順序の選択"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "バッチコードを生成するストックアイテムを選択します。"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "バッチコードを生成する場所を選択します。"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "バッチコードを生成する部品を選択します。"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "注文書の選択"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "バッチコードの数量を入力してください。"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "生成されたシリアル番号"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "シリアル番号を生成する部品を選択します。"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "生成するシリアル番号の数"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "この結果のテストテンプレート"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "テンプレートIDまたはテスト名が必要です。"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "試験終了時刻を試験開始時刻より早くすることはできません。"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "親アイテム"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr "親株式"

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "数量はパック数です。"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "新しい商品のシリアル番号の入力"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "サプライヤー品番"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "期限切れ"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "子供用品"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr "追跡項目"

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "この在庫品の購入価格、単位またはパックあたり"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "シリアル化するストックアイテムの数を入力"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "数量は在庫数 ({q}) を超えてはなりません。"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "仕向け地"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "この部品にシリアル番号を割り当てることはできません"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "シリアル番号が既に存在します"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "インストールするストックアイテムを選択"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "設置数量"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "インストールするアイテムの数量を入力してください。"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "取引メモの追加（オプション）"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "設置数量は1台以上"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "在庫がありません"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "選択した部品が部品表にない"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "設置する数量は、利用可能な数量を超えてはなりません。"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "アンインストール先の場所"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "在庫品を変換する部品を選択"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "選択された部分は、変換のための有効なオプションではありません。"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "SupplierPartが割り当てられている在庫品を変換できません。"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "在庫商品ステータスコード"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "ステータスを変更するストックアイテムを選択"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "ストックアイテムが選択されていません"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "サブロケーション"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "親株式所在地"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "パーツは販売可能でなければなりません"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "商品が販売オーダーに割り当てられています。"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "アイテムがビルドオーダーに割り当てられています。"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "在庫アイテムを割り当てるお客様"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "選択された企業は顧客ではありません"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "株式譲渡に関する注意事項"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "在庫品のリストが必要です。"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "株式併合に関する注意事項"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "不一致のサプライヤーを許可"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "異なるサプライヤの部品を持つ在庫品目をマージできるようにします。"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "不一致の状態を許可"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "異なるステータスコードを持つストックアイテムをマージすることができます。"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "少なくとも2つのストックアイテムを提供する必要があります。"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "変化なし"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "StockItem 主キー値"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr "在庫がありません"

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "株式取引に関する注記"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr "次のシリアル番号"

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr "以前のシリアル番号"

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "注意が必要です"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "破損"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "破壊されました"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "却下済み"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "隔離"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "レガシー在庫追跡入力"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "在庫商品を作成しました"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "在庫商品編集済み"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "割り当てられたシリアル番号"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "在庫数"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "手動在庫追加が完了しました"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "手動在庫削除が完了しました"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "ロケーションが変更されました"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "在庫更新"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "アセンブリへインストールしました"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "アセンブリから削除しました"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "インストール済みのコンポーネント項目"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "コンポーネント項目を削除しました"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "親アイテムから分割する"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "子項目を分割"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "商品在庫をマージしました"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "バリアントに変換"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "組立注文の出力が作成されました"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "組立注文の出力が完了しました"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "ビルドオーダーの出力が拒否されました"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "ビルド・オーダーで消費"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "販売注文に対して出荷"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "発注書との照合"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "リターンオーダーに反して返品"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "顧客に送信されました"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "顧客からの返品"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "権限がありません"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "このページを表示する権限がありません。"

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "認証失敗"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "InvenTreeからログアウトされました。"

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "お探しのページが見つかりません。"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "要求されたページは存在しません"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "サーバー側の内部エラー"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s サーバーで内部エラーが発生しました。"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "詳細については、管理インターフェイスのエラーログを参照してください。"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "サイトはメンテナンス中です"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "このサイトは現在メンテナンス中で、まもなく再開される予定です！"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "サーバーの再起動が必要"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "サーバーの再起動を必要とする設定オプションが変更されました。"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "詳細については、システム管理者にお問い合わせください。"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "保留中のデータベース移行"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "保留中のデータベース移行があり、注意が必要です。"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "このオーダーをご覧になるには、以下のリンクをクリックしてください。"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "在庫は以下の製造順に必要です。"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "ビルドオーダー %(build)s – ビルド中 %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "次のリンクをクリックして、このビルドオーダーをご覧ください。"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "以下の部品の在庫が不足しています。"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "必要数量"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "このパートに関する通知を購読しているため、このメールを受信しています。"

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "このパーツを表示するには、次のリンクをクリックしてください"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "最小在庫"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr "この部品またはその部品の一部であるカテゴリの通知を購読しているため、このメールを受信しています。"

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "ユーザー"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "このグループに割り当てるユーザーを選択します"

#: users/admin.py:137
msgid "Personal info"
msgstr "個人情報"

#: users/admin.py:139
msgid "Permissions"
msgstr "許可"

#: users/admin.py:142
msgid "Important dates"
msgstr "重要な日付"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "トークンは失効しました"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "トークンの有効期限が切れました"

#: users/models.py:100
msgid "API Token"
msgstr "APIトークン"

#: users/models.py:101
msgid "API Tokens"
msgstr "APIトークン"

#: users/models.py:137
msgid "Token Name"
msgstr "トークン名"

#: users/models.py:138
msgid "Custom token name"
msgstr "カスタムトークン名"

#: users/models.py:144
msgid "Token expiry date"
msgstr "トークンの有効期限"

#: users/models.py:152
msgid "Last Seen"
msgstr "最終表示"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "トークンが最後に使用された時間"

#: users/models.py:157
msgid "Revoked"
msgstr "失効"

#: users/models.py:235
msgid "Permission set"
msgstr "パーミッション設定"

#: users/models.py:244
msgid "Group"
msgstr "グループ"

#: users/models.py:248
msgid "View"
msgstr "表示"

#: users/models.py:248
msgid "Permission to view items"
msgstr "項目を表示する権限"

#: users/models.py:252
msgid "Add"
msgstr "追加"

#: users/models.py:252
msgid "Permission to add items"
msgstr "項目を追加する権限"

#: users/models.py:256
msgid "Change"
msgstr "変更"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "項目を編集する権限"

#: users/models.py:262
msgid "Delete"
msgstr "削除"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "項目を削除する権限"

#: users/models.py:501
msgid "Bot"
msgstr "ボット"

#: users/models.py:502
msgid "Internal"
msgstr "内部"

#: users/models.py:504
msgid "Guest"
msgstr "ゲスト"

#: users/models.py:513
msgid "Language"
msgstr "言語"

#: users/models.py:514
msgid "Preferred language for the user"
msgstr "ユーザーの希望言語"

#: users/models.py:519
msgid "Theme"
msgstr "テーマ"

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr "JSONとしてのウェブUI用の設定 - 手動で編集しないでください！"

#: users/models.py:525
msgid "Widgets"
msgstr "ウィジェット"

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr "JSONとしてのダッシュボード・ウィジェットの設定 - 手動で編集しないでください！"

#: users/models.py:534
msgid "Display Name"
msgstr "表示名"

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr "ユーザーの表示名選択"

#: users/models.py:541
msgid "Position"
msgstr "位置"

#: users/models.py:542
msgid "Main job title or position"
msgstr "主な役職名"

#: users/models.py:549
msgid "User status message"
msgstr "ユーザーのステータスメッセージ"

#: users/models.py:556
msgid "User location information"
msgstr "ユーザーの位置情報"

#: users/models.py:561
msgid "User is actively using the system"
msgstr "ユーザーが積極的にシステムを利用"

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr "希望の連絡先"

#: users/models.py:574
msgid "User Type"
msgstr "ユーザータイプ"

#: users/models.py:575
msgid "Which type of user is this?"
msgstr "どのようなユーザーですか？"

#: users/models.py:581
msgid "Organisation"
msgstr "組織"

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr "ユーザーの主な所属組織"

#: users/models.py:590
msgid "Primary Group"
msgstr "プライマリーグループ"

#: users/models.py:591
msgid "Primary group for the user"
msgstr "ユーザーのプライマリグループ"

#: users/ruleset.py:26
msgid "Admin"
msgstr "管理者"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "購入注文"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "セールスオーダー"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "返品注文"

#: users/serializers.py:196
msgid "Username"
msgstr "ユーザー名"

#: users/serializers.py:199
msgid "First Name"
msgstr "名"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "ユーザーの名"

#: users/serializers.py:203
msgid "Last Name"
msgstr "姓"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "ユーザーの姓"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "ユーザーのメールアドレス"

#: users/serializers.py:326
msgid "Staff"
msgstr "スタッフ"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "このユーザーにはスタッフ権限がありますか？"

#: users/serializers.py:332
msgid "Superuser"
msgstr "スーパーユーザー"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "このユーザーはスーパーユーザーですか？"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "このユーザーアカウントはアクティブですか"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr "このフィールドを調整できるのはスーパーユーザーのみです。"

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr "新しいユーザーを作成できるのはスタッフユーザーのみです。"

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr "ユーザーを作成する権限がありません"

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "アカウントが作成されました"

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "パスワードリセット機能を使ってログインしてください"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "InvenTreeへようこそ"

