# Generated by Django 3.2.5 on 2021-10-25 02:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('order', '0053_auto_20211128_0151'),
    ]

    operations = [
        migrations.CreateModel(
            name='SalesOrderShipment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shipment_date', models.DateField(blank=True, help_text='Date of shipment', null=True, verbose_name='Shipment Date')),
                ('reference', models.CharField(default='1', help_text='Shipment reference', max_length=100, verbose_name='Reference')),
                ('notes', models.TextField(blank=True, help_text='Shipment notes', verbose_name='Notes')),
                ('checked_by', models.ForeignKey(blank=True, help_text='User who checked this shipment', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Checked By')),
                ('order', models.ForeignKey(help_text='Sales Order', on_delete=django.db.models.deletion.CASCADE, related_name='shipments', to='order.salesorder', verbose_name='Order')),
            ],
            options={
                'verbose_name': 'Sales Order Shipment',
            },
        ),
    ]
