---
title: Release 0.3.0
---

## Release 0.3.0

[Release 0.3.0](https://github.com/inventree/InvenTree/releases/tag/0.3.0) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Version Numbering

Release 0.3.0 introduces a formalized release numbering scheme for the InvenTree project, according to the [semantic versioning specification](https://semver.org/). Each release will be numbered `MAJOR.MINOR.PATCH`:

#### MAJOR

Major release number will be incremented for large, incompatible changes to the API or other features.

#### MINOR

Minor release number will be incremented when new features are added in a backwards compatible manner

#### PATCH

Patch release number will be incremented when making bug fixes or small changes to existing features.

### API Forms

Many (not all) modal forms have been completely refactored to use the API. To support this, [#1716](https://github.com/inventree/InvenTree/pull/1716) provides extensive metadata for each API endpoint via the *OPTIONS* request. The new API-based forms are much faster, and provide greater front end flexibility.

### UX Improvements

Many UX improvements have been made, including [#1811](https://github.com/inventree/InvenTree/pull/1811) which allows dynamic switching between navbar items on a single page, without requiring a full page refresh.

### Pricing

Multiple new pricing features have been added:

#### Pricing View Consolidation

[#1712](https://github.com/inventree/InvenTree/pull/1712) combines all Part pricing views into a single consolidated display.

#### Purchase Price in BOM Table

[#1726](https://github.com/inventree/InvenTree/pull/1726) re-introduces display of purchase price information into the BOM table

### UI Improvements

Multiple user interface improvements have been implemented, as described below:

#### Depleted Stock View

[#1747](https://github.com/inventree/InvenTree/pull/1747) adds a "Depleted Stock" table to the index page.

#### Supplier Part Column

[#1750](https://github.com/inventree/InvenTree/pull/1750) introduces a "Supplier Part" column to the Stock Item table.

#### Purchase Price Filter

[#1744](https://github.com/inventree/InvenTree/pull/1744) adds a "Has Purchase Price" filter to the Stock Item table.

#### Hide Related Parts

[#1734](https://github.com/inventree/InvenTree/pull/1734) adds an option to hide "Related Parts" feature.

#### Sortable Allocations

[#1731](https://github.com/inventree/InvenTree/pull/1731) allows sorting by "allocated" quantity in Build Order display

### Admin Interface

[#1812](https://github.com/inventree/InvenTree/pull/1812) represents the first step towards a general speed overhaul for the admin interface.

## Major Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#1729](https://github.com/inventree/InvenTree/pull/1729) | Fixes a bug in the HTML browsable API |
| [#1736](https://github.com/inventree/InvenTree/pull/1736) | Fixes an ongoing issue with currency support |
| [#1759](https://github.com/inventree/InvenTree/pull/1759) | Fixes a filtering issue for the ManufacturerPart and SupplierPart tables |
| [#1761](https://github.com/inventree/InvenTree/pull/1761) | Fixes a bug in stock adjustment form |
| [#1768](https://github.com/inventree/InvenTree/pull/1768) | Fixes multiple issues displaying file attachment tables |
| [#1771](https://github.com/inventree/InvenTree/pull/1771) | Adds ability to search ManufacturerPart and SupplierPart objects by `IPN` field |
| [#1799](https://github.com/inventree/InvenTree/pull/1799) | Fixes bug exporting BOM to .xls |
