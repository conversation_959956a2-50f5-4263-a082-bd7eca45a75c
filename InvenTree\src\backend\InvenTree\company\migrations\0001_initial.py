# Generated by Django 2.2 on 2019-05-20 12:04

import company.models
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Company name', max_length=100, unique=True)),
                ('description', models.<PERSON>r<PERSON><PERSON>(help_text='Description of the company', max_length=500)),
                ('website', models.URLField(blank=True, help_text='Company website URL')),
                ('address', models.CharField(blank=True, help_text='Company address', max_length=200)),
                ('phone', models.CharField(blank=True, help_text='Contact phone number', max_length=50)),
                ('email', models.EmailField(blank=True, help_text='Contact email address', max_length=254)),
                ('contact', models.Char<PERSON>ield(blank=True, help_text='Point of contact', max_length=100)),
                ('URL', models.URLField(blank=True, help_text='Link to external company information')),
                ('image', models.ImageField(blank=True, max_length=255, null=True, upload_to=company.models.rename_company_image)),
                ('notes', models.TextField(blank=True)),
                ('is_customer', models.BooleanField(default=False, help_text='Do you sell items to this company?')),
                ('is_supplier', models.BooleanField(default=True, help_text='Do you purchase items from this company?')),
            ],
            options={
                'verbose_name': 'Company',
            }
        ),
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('phone', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('role', models.CharField(blank=True, max_length=100)),
            ],
            options={
                'verbose_name': 'Contact',
            }
        ),
        migrations.CreateModel(
            name='SupplierPart',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('SKU', models.CharField(help_text='Supplier stock keeping unit', max_length=100)),
                ('manufacturer', models.CharField(blank=True, help_text='Manufacturer', max_length=100)),
                ('MPN', models.CharField(blank=True, help_text='Manufacturer part number', max_length=100)),
                ('URL', models.URLField(blank=True, help_text='URL for external supplier part link')),
                ('description', models.CharField(blank=True, help_text='Supplier part description', max_length=250)),
                ('note', models.CharField(blank=True, help_text='Notes', max_length=100)),
                ('base_cost', models.DecimalField(decimal_places=3, default=0, help_text='Minimum charge (e.g. stocking fee)', max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('packaging', models.CharField(blank=True, help_text='Part packaging', max_length=50)),
                ('multiple', models.PositiveIntegerField(default=1, help_text='Order multiple', validators=[django.core.validators.MinValueValidator(1)])),
                ('minimum', models.PositiveIntegerField(default=1, help_text='Minimum order quantity (MOQ)', validators=[django.core.validators.MinValueValidator(1)])),
                ('lead_time', models.DurationField(blank=True, null=True)),
            ],
            options={
                'db_table': 'part_supplierpart',
                'verbose_name': 'Supplier Part',
            },
        ),
        migrations.CreateModel(
            name='SupplierPriceBreak',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('cost', models.DecimalField(decimal_places=5, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pricebreaks', to='company.SupplierPart')),
            ],
            options={
                'verbose_name': 'Supplier Price Break',
                'db_table': 'part_supplierpricebreak',
            },
        ),
    ]
