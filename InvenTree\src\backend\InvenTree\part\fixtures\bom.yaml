# Construct a BOM for item 100 'Bob'

# 10 x M2x4 LPHS
- model: part.bomitem
  pk: 1
  fields:
    part: 100
    sub_part: 1
    quantity: 10
    allow_variants: True

# 40 x R_2K2_0805
- model: part.bomitem
  pk: 2
  fields:
    part: 100
    sub_part: 3
    quantity: 40

# 25 x C_22N_0805
- model: part.bomitem
  pk: 3
  fields:
    part: 100
    sub_part: 5
    quantity: 25
    reference: ABCDE

# 3 x Orphan
- model: part.bomitem
  pk: 4
  fields:
    part: 100
    sub_part: 50
    quantity: 3
    reference: VWXYZ

- model: part.bomitem
  pk: 5
  fields:
    part: 1
    sub_part: 5
    quantity: 3
    reference: LMNOP

# Make "Assembly" from "Bob"
- model: part.bomitem
  pk: 6
  fields:
    part: 101
    sub_part: 100
    quantity: 10

- model: part.bomitemsubstitute
  pk: 1
  fields:
    part: 5
    bom_item: 6
