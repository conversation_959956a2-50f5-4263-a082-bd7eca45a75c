# Generated by Django 3.2.20 on 2023-07-18 11:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0019_projectcode_metadata'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUnit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Unit name', max_length=50, unique=True, verbose_name='Name')),
                ('symbol', models.Char<PERSON>ield(blank=True, help_text='Optional unit symbol', max_length=10, unique=True, verbose_name='Symbol')),
                ('definition', models.Char<PERSON><PERSON>(help_text='Unit definition', max_length=50, verbose_name='Definition')),
            ],
            options={
                'verbose_name': 'Custom Unit',
            },
        ),
    ]
