# Generated by Django 4.2.23 on 2025-07-15 02:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("part", "0135_alter_part_link"),
    ]

    operations = [
        migrations.AddField(
            model_name="partparameter",
            name="note",
            field=models.CharField(
                blank=True,
                help_text="Optional note field",
                max_length=500,
                verbose_name="Note",
            ),
        ),
        migrations.AddField(
            model_name="partparameter",
            name="updated",
            field=models.DateTimeField(
                blank=True,
                default=None,
                help_text="Timestamp of last update",
                null=True,
                verbose_name="Updated",
            ),
        ),
        migrations.AddField(
            model_name="partparameter",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who last updated this object",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_updated",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Update By",
            ),
        ),
    ]
