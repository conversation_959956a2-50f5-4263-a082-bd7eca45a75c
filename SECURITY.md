# Security Policy

The InvenTree team take all security vulnerabilities seriously. Thank you for improving the security of our open source software.

We appreciate your efforts and responsible disclosure and will make every effort to acknowledge your contributions.
The general project security policies and processes are documented in [our documentation](https://docs.inventree.org/en/stable/security/).

## Reporting a Vulnerability

Please report security vulnerabilities by emailing the InvenTree team at:

```
<EMAIL>
```

Someone from the InvenTree development team will acknowledge your email as soon as possible (normally within a week), and indicate the next steps in handling your security report.


The team will endeavour to keep you informed of the progress towards a fix for the issue, and subsequent release to the stable and development code branches. Where possible, the issue will be resolved within 90 days of reporting.

### Public Disclosure

Using GitHub's security advisory system, we will publish a public disclosure of the issue once it has been acknowledged, reproduced and resolved.
We support assigning CVEs to security issues where appropriate.
The project can be identified by the CPE code ``cpe:2.3:a:inventree_project:inventree:``.
