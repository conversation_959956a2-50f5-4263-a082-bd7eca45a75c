- model: order.salesorder
  pk: 1
  fields:
    reference: 'ABC123'
    description: "One sales order, please"
    customer: 4
    status: 10  # Pending

- model: order.salesorder
  pk: 2
  fields:
    reference: 'ABC124'
    description: "One sales order, please"
    customer: 4
    status: 10  # Pending

- model: order.salesorder
  pk: 3
  fields:
    reference: 'ABC125'
    description: "One sales order, please"
    customer: 4
    status: 10  # Pending

- model: order.salesorder
  pk: 4
  fields:
    reference: 'ABC126'
    description: "One sales order, please"
    customer: 5
    status: 20  # Shipped

- model: order.salesorder
  pk: 5
  fields:
    reference: 'ABC127'
    description: "One sales order, please"
    customer: 5
    status: 60  # Returned

# 1 x R_4K7_0603
- model: order.salesorderlineitem
  pk: 1
  fields:
    order: 5
    part: 5
    quantity: 1

# An extra line item
- model: order.salesorderextraline
  pk: 1
  fields:
    order: 5
    reference: 'Freight cost'
    quantity: 1

# Shipment
- model: order.salesordershipment
  pk: 1
  fields:
    order: 1
    reference: "Test Shipment, must be present for metadata test"
