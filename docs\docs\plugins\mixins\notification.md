---
title: Notification Mixin
---

## NotificationMixin

The `NotificationMixin` class provides a plugin with the ability to send notifications to users when certain events occur in the system.

Any notification which is generated by the InvenTree core system can be sent to users via a custom plugin which implements this mixin class.

### send_notification

The `send_notification` method is used to send a notification to users:

::: plugin.base.integration.NotificationMixin.NotificationMixin.send_notification
    options:
      show_bases: False
      show_root_heading: False
      show_root_toc_entry: False
      summary: False
      members: []
      extra:
        show_sources: True

### filter_targets

If desired, the plugin can implement the `filter_targets` method to filter the list of users who will receive the notification. This allows for more granular control over which users are notified based on specific criteria.

::: plugin.base.integration.NotificationMixin.NotificationMixin.filter_targets
    options:
      show_bases: False
      show_root_heading: False
      show_root_toc_entry: False
      summary: False
      members: []
      extra:
        show_sources: True

## Built-in Notifications

The following built-in notifications plugins are available:

- [UI Notifications](../builtin/ui_notification.md)
- [Email Notifications](../builtin/email_notification.md)
- [Slack Notifications](../builtin/slack_notification.md)
