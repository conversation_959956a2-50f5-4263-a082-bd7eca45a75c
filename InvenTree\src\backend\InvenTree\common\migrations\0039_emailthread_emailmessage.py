# Generated by Django 4.2.20 on 2025-05-07 00:17

import InvenTree.models
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0038_alter_attachment_model_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailThread',
            fields=[
                ('metadata', models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata')),
                ('key', models.CharField(blank=True, help_text='Unique key for this thread (used to identify the thread)', max_length=250, null=True, verbose_name='Key')),
                ('global_id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this thread', primary_key=True, serialize=False, verbose_name='Global ID')),
                ('started_internal', models.BooleanField(default=False, help_text='Was this thread started internally?', verbose_name='Started Internal')),
                ('created', models.DateTimeField(auto_now_add=True, help_text='Date and time that the thread was created', verbose_name='Created')),
                ('updated', models.DateTimeField(auto_now=True, help_text='Date and time that the thread was last updated', verbose_name='Updated')),
            ],
            options={
                'verbose_name': 'Email Thread',
                'verbose_name_plural': 'Email Threads',
                'ordering': ['-updated'],
                'unique_together': {('key', 'global_id')},
            },
            bases=(InvenTree.models.PluginValidationMixin, models.Model),
        ),
        migrations.CreateModel(
            name='EmailMessage',
            fields=[
                ('global_id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this message', primary_key=True, serialize=False, unique=True, verbose_name='Global ID')),
                ('message_id_key', models.CharField(blank=True, help_text='Identifier for this message (might be supplied by external system)', max_length=250, null=True, verbose_name='Message ID')),
                ('thread_id_key', models.CharField(blank=True, help_text='Identifier for this message thread (might be supplied by external system)', max_length=250, null=True, verbose_name='Thread ID')),
                ('subject', models.CharField(max_length=250)),
                ('body', models.TextField()),
                ('to', models.EmailField(max_length=254)),
                ('sender', models.EmailField(max_length=254)),
                ('status', models.CharField(blank=True, choices=[('A', 'Announced'), ('S', 'Sent'), ('F', 'Failed'), ('D', 'Delivered'), ('R', 'Read'), ('C', 'Confirmed')], max_length=50, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('headers', models.JSONField(blank=True, null=True)),
                ('full_message', models.TextField(blank=True, null=True)),
                ('direction', models.CharField(blank=True, choices=[('I', 'Inbound'), ('O', 'Outbound')], max_length=50, null=True)),
                ('priority', models.IntegerField(choices=[(0, 'None'), (1, 'Very High'), (2, 'High'), (3, 'Normal'), (4, 'Low'), (5, 'Very Low')], verbose_name='Prioriy')),
                ('delivery_options', models.JSONField(blank=True, null=True)),
                ('error_code', models.CharField(blank=True, max_length=50, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('error_timestamp', models.DateTimeField(blank=True, null=True)),
                ('thread', models.ForeignKey(blank=True, help_text='Linked thread for this message', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='messages', to='common.emailthread', verbose_name='Thread')),
            ],
            options={
                'verbose_name': 'Email Message',
                'verbose_name_plural': 'Email Messages',
            },
        ),
    ]
