msgid ""
msgstr ""
"POT-Creation-Date: 2023-06-09 22:10+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ru\n"
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-20 11:54\n"
"Last-Translator: \n"
"Language-Team: Russian\n"
"Plural-Forms: nplurals=4; plural=((n%10==1 && n%100!=11) ? 0 : ((n%10 >= 2 && n%10 <=4 && (n%100 < 12 || n%100 > 14)) ? 1 : ((n%10 == 0 || (n%10 >= 5 && n%10 <=9)) || (n%100 >= 11 && n%100 <= 14)) ? 2 : 3));\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: ru\n"
"X-Crowdin-File: /src/frontend/src/locales/en/messages.po\n"
"X-Crowdin-File-ID: 252\n"

#: lib/components/RowActions.tsx:36
#: src/components/items/ActionDropdown.tsx:287
#: src/pages/Index/Scan.tsx:64
msgid "Duplicate"
msgstr "Дублировать"

#: lib/components/RowActions.tsx:46
#: src/components/items/ActionDropdown.tsx:243
msgid "Edit"
msgstr "Редактировать"

#: lib/components/RowActions.tsx:56
#: src/components/forms/ApiForm.tsx:719
#: src/components/items/ActionDropdown.tsx:255
#: src/components/items/RoleTable.tsx:155
#: src/hooks/UseForm.tsx:160
#: src/pages/Notifications.tsx:109
#: src/tables/plugin/PluginListTable.tsx:243
msgid "Delete"
msgstr "Удалить"

#: lib/components/RowActions.tsx:66
#: src/components/details/DetailsImage.tsx:83
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:187
#: src/components/items/ActionDropdown.tsx:275
#: src/components/items/ActionDropdown.tsx:276
#: src/contexts/ThemeContext.tsx:45
#: src/hooks/UseForm.tsx:33
#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:106
#: src/tables/FilterSelectDrawer.tsx:336
#: src/tables/build/BuildOutputTable.tsx:570
msgid "Cancel"
msgstr "Отменить"

#: lib/components/RowActions.tsx:136
#: src/components/nav/NavigationDrawer.tsx:198
#: src/forms/PurchaseOrderForms.tsx:795
#: src/forms/StockForms.tsx:737
#: src/forms/StockForms.tsx:783
#: src/forms/StockForms.tsx:829
#: src/forms/StockForms.tsx:868
#: src/forms/StockForms.tsx:904
#: src/forms/StockForms.tsx:983
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:456
msgid "Actions"
msgstr "Действия"

#: lib/components/SearchInput.tsx:34
#: src/components/forms/fields/RelatedModelField.tsx:387
#: src/components/nav/Header.tsx:168
#: src/pages/Index/Settings/UserSettings.tsx:74
#: src/pages/part/PartDetail.tsx:1161
msgid "Search"
msgstr "Поиск"

#: lib/components/YesNoButton.tsx:20
msgid "Pass"
msgstr "Пропустить"

#: lib/components/YesNoButton.tsx:21
msgid "Fail"
msgstr "Сбой"

#: lib/components/YesNoButton.tsx:43
#: src/tables/Filter.tsx:35
msgid "Yes"
msgstr "Да"

#: lib/components/YesNoButton.tsx:44
#: src/tables/Filter.tsx:36
msgid "No"
msgstr "Нет"

#: lib/enums/ModelInformation.tsx:28
#: src/components/wizards/OrderPartsWizard.tsx:132
#: src/forms/BuildForms.tsx:310
#: src/forms/BuildForms.tsx:384
#: src/forms/BuildForms.tsx:448
#: src/forms/BuildForms.tsx:602
#: src/forms/BuildForms.tsx:757
#: src/forms/BuildForms.tsx:860
#: src/forms/PurchaseOrderForms.tsx:791
#: src/forms/ReturnOrderForms.tsx:239
#: src/forms/SalesOrderForms.tsx:267
#: src/forms/StockForms.tsx:303
#: src/forms/StockForms.tsx:732
#: src/forms/StockForms.tsx:778
#: src/forms/StockForms.tsx:824
#: src/forms/StockForms.tsx:863
#: src/forms/StockForms.tsx:899
#: src/forms/StockForms.tsx:937
#: src/forms/StockForms.tsx:979
#: src/forms/StockForms.tsx:1027
#: src/forms/StockForms.tsx:1071
#: src/pages/build/BuildDetail.tsx:183
#: src/pages/part/PartDetail.tsx:1213
#: src/tables/ColumnRenderers.tsx:82
#: src/tables/part/RelatedPartTable.tsx:53
#: src/tables/stock/StockTrackingTable.tsx:87
msgid "Part"
msgstr "Деталь"

#: lib/enums/ModelInformation.tsx:29
#: lib/enums/Roles.tsx:35
#: src/components/nav/NavigationDrawer.tsx:77
#: src/defaults/links.tsx:36
#: src/pages/Index/Settings/SystemSettings.tsx:190
#: src/pages/part/CategoryDetail.tsx:130
#: src/pages/part/CategoryDetail.tsx:273
#: src/pages/part/CategoryDetail.tsx:312
#: src/pages/part/PartDetail.tsx:951
msgid "Parts"
msgstr "Детали"

#: lib/enums/ModelInformation.tsx:37
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:13
msgid "Part Parameter Template"
msgstr "Шаблон параметров деталей"

#: lib/enums/ModelInformation.tsx:38
msgid "Part Parameter Templates"
msgstr "Шаблоны параметров деталей"

#: lib/enums/ModelInformation.tsx:45
msgid "Part Test Template"
msgstr "Шаблон теста детали"

#: lib/enums/ModelInformation.tsx:46
msgid "Part Test Templates"
msgstr "Шаблоны тестов детали"

#: lib/enums/ModelInformation.tsx:52
#: src/components/wizards/OrderPartsWizard.tsx:143
#: src/pages/company/SupplierPartDetail.tsx:409
#: src/pages/stock/StockDetail.tsx:286
#: src/tables/build/BuildAllocatedStockTable.tsx:155
#: src/tables/part/PartPurchaseOrdersTable.tsx:50
#: src/tables/purchasing/SupplierPartTable.tsx:64
#: src/tables/stock/StockItemTable.tsx:248
msgid "Supplier Part"
msgstr "Деталь поставщика"

#: lib/enums/ModelInformation.tsx:53
#: src/pages/purchasing/PurchasingIndex.tsx:92
msgid "Supplier Parts"
msgstr "Детали поставщиков"

#: lib/enums/ModelInformation.tsx:61
#: src/tables/part/PartPurchaseOrdersTable.tsx:56
#: src/tables/stock/StockItemTable.tsx:254
msgid "Manufacturer Part"
msgstr "Деталь производителя"

#: lib/enums/ModelInformation.tsx:62
#: src/pages/purchasing/PurchasingIndex.tsx:109
msgid "Manufacturer Parts"
msgstr "Детали производителей"

#: lib/enums/ModelInformation.tsx:70
#: src/pages/part/CategoryDetail.tsx:343
#: src/tables/Filter.tsx:381
msgid "Part Category"
msgstr "Категория детали"

#: lib/enums/ModelInformation.tsx:71
#: lib/enums/Roles.tsx:37
#: src/pages/part/CategoryDetail.tsx:334
#: src/pages/part/PartDetail.tsx:1202
msgid "Part Categories"
msgstr "Категории деталей"

#: lib/enums/ModelInformation.tsx:79
#: src/forms/BuildForms.tsx:385
#: src/forms/BuildForms.tsx:449
#: src/forms/BuildForms.tsx:604
#: src/forms/BuildForms.tsx:758
#: src/forms/SalesOrderForms.tsx:269
#: src/pages/stock/StockDetail.tsx:976
#: src/tables/stock/StockTrackingTable.tsx:48
#: src/tables/stock/StockTrackingTable.tsx:55
msgid "Stock Item"
msgstr "Складская позиция"

#: lib/enums/ModelInformation.tsx:80
#: lib/enums/Roles.tsx:45
#: src/pages/company/CompanyDetail.tsx:212
#: src/pages/part/CategoryDetail.tsx:287
#: src/pages/part/PartStockHistoryDetail.tsx:101
#: src/pages/stock/LocationDetail.tsx:123
#: src/pages/stock/LocationDetail.tsx:182
msgid "Stock Items"
msgstr "Складские позиции"

#: lib/enums/ModelInformation.tsx:88
#: lib/enums/Roles.tsx:47
#: src/pages/stock/LocationDetail.tsx:420
msgid "Stock Location"
msgstr "Место хранения"

#: lib/enums/ModelInformation.tsx:89
#: src/pages/stock/LocationDetail.tsx:176
#: src/pages/stock/LocationDetail.tsx:412
#: src/pages/stock/StockDetail.tsx:967
msgid "Stock Locations"
msgstr "Места хранения"

#: lib/enums/ModelInformation.tsx:97
msgid "Stock Location Type"
msgstr "Тип места хранения"

#: lib/enums/ModelInformation.tsx:98
msgid "Stock Location Types"
msgstr "Типы места хранения"

#: lib/enums/ModelInformation.tsx:103
#: src/pages/Index/Settings/SystemSettings.tsx:248
#: src/pages/part/PartDetail.tsx:910
msgid "Stock History"
msgstr "История склада"

#: lib/enums/ModelInformation.tsx:104
msgid "Stock Histories"
msgstr "История складов"

#: lib/enums/ModelInformation.tsx:109
msgid "Build"
msgstr "Сборка"

#: lib/enums/ModelInformation.tsx:110
msgid "Builds"
msgstr "Производство"

#: lib/enums/ModelInformation.tsx:118
msgid "Build Line"
msgstr "Линия производства"

#: lib/enums/ModelInformation.tsx:119
msgid "Build Lines"
msgstr "Линия производства"

#: lib/enums/ModelInformation.tsx:126
msgid "Build Item"
msgstr "Товар производства"

#: lib/enums/ModelInformation.tsx:127
msgid "Build Items"
msgstr "Товары производства"

#: lib/enums/ModelInformation.tsx:132
#: src/pages/company/CompanyDetail.tsx:342
#: src/tables/company/CompanyTable.tsx:47
#: src/tables/company/ContactTable.tsx:67
msgid "Company"
msgstr "Компания"

#: lib/enums/ModelInformation.tsx:133
msgid "Companies"
msgstr "Компании"

#: lib/enums/ModelInformation.tsx:140
#: src/pages/build/BuildDetail.tsx:310
#: src/pages/purchasing/PurchaseOrderDetail.tsx:235
#: src/pages/sales/ReturnOrderDetail.tsx:199
#: src/pages/sales/SalesOrderDetail.tsx:211
#: src/tables/ColumnRenderers.tsx:353
#: src/tables/Filter.tsx:278
#: src/tables/TableHoverCard.tsx:101
msgid "Project Code"
msgstr "Код проекта"

#: lib/enums/ModelInformation.tsx:141
#: src/pages/Index/Settings/AdminCenter/Index.tsx:162
msgid "Project Codes"
msgstr "Коды проекта"

#: lib/enums/ModelInformation.tsx:147
#: src/components/wizards/OrderPartsWizard.tsx:183
#: src/pages/build/BuildDetail.tsx:227
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:33
#: src/pages/purchasing/PurchaseOrderDetail.tsx:531
#: src/pages/stock/StockDetail.tsx:349
#: src/tables/part/PartPurchaseOrdersTable.tsx:32
#: src/tables/stock/StockItemTable.tsx:240
#: src/tables/stock/StockTrackingTable.tsx:120
msgid "Purchase Order"
msgstr "Заказ на закупку"

#: lib/enums/ModelInformation.tsx:148
#: lib/enums/Roles.tsx:39
#: src/pages/Index/Settings/SystemSettings.tsx:283
#: src/pages/company/CompanyDetail.tsx:205
#: src/pages/company/SupplierPartDetail.tsx:266
#: src/pages/part/PartDetail.tsx:874
#: src/pages/purchasing/PurchasingIndex.tsx:60
msgid "Purchase Orders"
msgstr "Заказы на закупку"

#: lib/enums/ModelInformation.tsx:156
msgid "Purchase Order Line"
msgstr "Позиция заказа на закупку"

#: lib/enums/ModelInformation.tsx:157
msgid "Purchase Order Lines"
msgstr "Позиции заказа на закупку"

#: lib/enums/ModelInformation.tsx:162
#: src/pages/build/BuildDetail.tsx:283
#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#: src/pages/sales/SalesOrderDetail.tsx:586
#: src/pages/sales/SalesOrderShipmentDetail.tsx:94
#: src/pages/sales/SalesOrderShipmentDetail.tsx:358
#: src/pages/stock/StockDetail.tsx:358
#: src/tables/part/PartSalesAllocationsTable.tsx:41
#: src/tables/sales/SalesOrderAllocationTable.tsx:107
#: src/tables/stock/StockTrackingTable.tsx:131
msgid "Sales Order"
msgstr "Заказ на продажу"

#: lib/enums/ModelInformation.tsx:163
#: lib/enums/Roles.tsx:43
#: src/pages/Index/Settings/SystemSettings.tsx:299
#: src/pages/company/CompanyDetail.tsx:225
#: src/pages/part/PartDetail.tsx:886
#: src/pages/sales/SalesIndex.tsx:82
msgid "Sales Orders"
msgstr "Заказы на продажу"

#: lib/enums/ModelInformation.tsx:171
#: src/pages/sales/SalesOrderShipmentDetail.tsx:357
msgid "Sales Order Shipment"
msgstr "Отправка заказа на продажу"

#: lib/enums/ModelInformation.tsx:172
msgid "Sales Order Shipments"
msgstr "Отгрузка заказа на продажу"

#: lib/enums/ModelInformation.tsx:178
#: src/pages/sales/ReturnOrderDetail.tsx:516
#: src/tables/stock/StockTrackingTable.tsx:142
msgid "Return Order"
msgstr "Заказ на возврат"

#: lib/enums/ModelInformation.tsx:179
#: lib/enums/Roles.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:315
#: src/pages/company/CompanyDetail.tsx:232
#: src/pages/part/PartDetail.tsx:893
#: src/pages/sales/SalesIndex.tsx:103
msgid "Return Orders"
msgstr "Заказы на возврат"

#: lib/enums/ModelInformation.tsx:187
msgid "Return Order Line Item"
msgstr "Позиция заказа на возврат"

#: lib/enums/ModelInformation.tsx:188
msgid "Return Order Line Items"
msgstr "Позиции заказа на возврат"

#: lib/enums/ModelInformation.tsx:193
#: src/tables/company/AddressTable.tsx:52
msgid "Address"
msgstr "Адрес"

#: lib/enums/ModelInformation.tsx:194
#: src/pages/company/CompanyDetail.tsx:266
msgid "Addresses"
msgstr "Адреса"

#: lib/enums/ModelInformation.tsx:200
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:89
#: src/pages/core/UserDetail.tsx:135
#: src/pages/purchasing/PurchaseOrderDetail.tsx:211
#: src/pages/sales/ReturnOrderDetail.tsx:175
#: src/pages/sales/SalesOrderDetail.tsx:187
msgid "Contact"
msgstr "Контакт"

#: lib/enums/ModelInformation.tsx:201
#: src/pages/company/CompanyDetail.tsx:260
#: src/pages/core/CoreIndex.tsx:33
msgid "Contacts"
msgstr "Контакты"

#: lib/enums/ModelInformation.tsx:207
msgid "Owner"
msgstr "Владелец"

#: lib/enums/ModelInformation.tsx:208
msgid "Owners"
msgstr "Владельцы"

#: lib/enums/ModelInformation.tsx:214
#: src/pages/Auth/ChangePassword.tsx:36
#: src/pages/core/UserDetail.tsx:220
#: src/tables/Filter.tsx:327
#: src/tables/settings/ApiTokenTable.tsx:105
#: src/tables/settings/ApiTokenTable.tsx:132
#: src/tables/settings/BarcodeScanHistoryTable.tsx:79
#: src/tables/settings/ExportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:77
#: src/tables/stock/StockItemTestResultTable.tsx:216
#: src/tables/stock/StockTrackingTable.tsx:190
#: src/tables/stock/StockTrackingTable.tsx:218
msgid "User"
msgstr "Пользователь"

#: lib/enums/ModelInformation.tsx:215
#: src/components/nav/NavigationDrawer.tsx:112
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:15
#: src/pages/core/CoreIndex.tsx:21
#: src/pages/core/UserDetail.tsx:226
msgid "Users"
msgstr "Пользователи"

#: lib/enums/ModelInformation.tsx:221
#: src/pages/core/GroupDetail.tsx:78
msgid "Group"
msgstr "Группа"

#: lib/enums/ModelInformation.tsx:222
#: src/components/nav/NavigationDrawer.tsx:118
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:23
#: src/pages/core/CoreIndex.tsx:27
#: src/pages/core/GroupDetail.tsx:82
#: src/pages/core/UserDetail.tsx:99
#: src/tables/settings/UserTable.tsx:276
msgid "Groups"
msgstr "Группы"

#: lib/enums/ModelInformation.tsx:229
msgid "Import Session"
msgstr "Сессия импорта"

#: lib/enums/ModelInformation.tsx:230
msgid "Import Sessions"
msgstr "Сессии импорта"

#: lib/enums/ModelInformation.tsx:237
msgid "Label Template"
msgstr "Шаблон этикетки"

#: lib/enums/ModelInformation.tsx:238
#: src/pages/Index/Settings/AdminCenter/Index.tsx:199
msgid "Label Templates"
msgstr "Шаблоны этикеток"

#: lib/enums/ModelInformation.tsx:245
msgid "Report Template"
msgstr "Шаблон отчёта"

#: lib/enums/ModelInformation.tsx:246
#: src/pages/Index/Settings/AdminCenter/Index.tsx:205
msgid "Report Templates"
msgstr "Шаблоны отчётов"

#: lib/enums/ModelInformation.tsx:253
#: src/components/plugins/PluginDrawer.tsx:145
msgid "Plugin Configuration"
msgstr "Настройка плагина"

#: lib/enums/ModelInformation.tsx:254
msgid "Plugin Configurations"
msgstr "Настройки плагинов"

#: lib/enums/ModelInformation.tsx:261
msgid "Content Type"
msgstr "Тип контента"

#: lib/enums/ModelInformation.tsx:262
msgid "Content Types"
msgstr "Типы контента"

#: lib/enums/ModelInformation.tsx:267
msgid "Selection List"
msgstr "Список выбора"

#: lib/enums/ModelInformation.tsx:268
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:21
msgid "Selection Lists"
msgstr "Списки выбора"

#: lib/enums/ModelInformation.tsx:274
#: src/components/barcodes/BarcodeInput.tsx:114
#: src/components/dashboard/DashboardLayout.tsx:224
#: src/components/editors/NotesEditor.tsx:74
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:157
#: src/components/forms/fields/ApiFormField.tsx:263
#: src/components/forms/fields/TableField.tsx:45
#: src/components/importer/ImportDataSelector.tsx:192
#: src/components/importer/ImporterColumnSelector.tsx:217
#: src/components/importer/ImporterDrawer.tsx:88
#: src/components/modals/LicenseModal.tsx:85
#: src/components/nav/NavigationTree.tsx:210
#: src/components/nav/NotificationDrawer.tsx:235
#: src/components/nav/SearchDrawer.tsx:572
#: src/components/settings/SettingList.tsx:145
#: src/forms/BomForms.tsx:69
#: src/functions/auth.tsx:612
#: src/pages/ErrorPage.tsx:11
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:124
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:643
#: src/pages/part/PartPricingPanel.tsx:71
#: src/states/IconState.tsx:46
#: src/states/IconState.tsx:76
#: src/tables/InvenTreeTableHeader.tsx:125
#: src/tables/bom/BomTable.tsx:527
#: src/tables/stock/StockItemTestResultTable.tsx:335
msgid "Error"
msgstr "Ошибка"

#: lib/enums/ModelInformation.tsx:275
#: src/tables/machine/MachineListTable.tsx:354
#: src/tables/machine/MachineTypeTable.tsx:281
msgid "Errors"
msgstr "Ошибки"

#: lib/enums/Roles.tsx:31
msgid "Admin"
msgstr "Администрирование пользователей"

#: lib/enums/Roles.tsx:33
#: src/pages/Index/Settings/SystemSettings.tsx:264
#: src/pages/build/BuildIndex.tsx:75
#: src/pages/part/PartDetail.tsx:903
#: src/pages/sales/SalesOrderDetail.tsx:394
msgid "Build Orders"
msgstr "Заказы на сборку"

#: lib/enums/Roles.tsx:50
#: src/pages/Index/Settings/AdminCenter/Index.tsx:202
#~ msgid "Stocktake"
#~ msgstr "Stocktake"

#: src/components/Boundary.tsx:12
msgid "Error rendering component"
msgstr "Ошибка при отображении компонента"

#: src/components/Boundary.tsx:14
msgid "An error occurred while rendering this component. Refer to the console for more information."
msgstr "Произошла ошибка при отрисовки этого компонента. Обратитесь к консоли для получения дополнительной информации."

#: src/components/DashboardItemProxy.tsx:34
#~ msgid "Title"
#~ msgstr "Title"

#: src/components/barcodes/BarcodeCameraInput.tsx:103
msgid "Error while scanning"
msgstr "Ошибка при сканировании"

#: src/components/barcodes/BarcodeCameraInput.tsx:117
msgid "Error while stopping"
msgstr "Ошибка при остановке"

#: src/components/barcodes/BarcodeCameraInput.tsx:159
msgid "Start scanning by selecting a camera and pressing the play button."
msgstr "Начните сканирование, выбрав камеру и нажав кнопку воспроизведения."

#: src/components/barcodes/BarcodeCameraInput.tsx:180
msgid "Stop scanning"
msgstr "Остановить сканирование"

#: src/components/barcodes/BarcodeCameraInput.tsx:190
msgid "Start scanning"
msgstr "Начать сканирование"

#: src/components/barcodes/BarcodeInput.tsx:34
#: src/tables/general/BarcodeScanTable.tsx:55
#: src/tables/settings/BarcodeScanHistoryTable.tsx:64
msgid "Barcode"
msgstr "Штрихкод"

#: src/components/barcodes/BarcodeInput.tsx:35
#: src/components/barcodes/BarcodeKeyboardInput.tsx:18
#: src/defaults/actions.tsx:72
msgid "Scan"
msgstr "Сканировать"

#: src/components/barcodes/BarcodeInput.tsx:53
msgid "Camera Input"
msgstr "Ввод с камеры"

#: src/components/barcodes/BarcodeInput.tsx:63
msgid "Scanner Input"
msgstr "Ввод со сканера"

#: src/components/barcodes/BarcodeInput.tsx:105
msgid "Barcode Data"
msgstr "Данные штрихкода"

#: src/components/barcodes/BarcodeInput.tsx:109
msgid "No barcode data"
msgstr "Нет данных штрихкода"

#: src/components/barcodes/BarcodeInput.tsx:110
msgid "Scan or enter barcode data"
msgstr "Отсканируйте штрихкод или введите данные"

#: src/components/barcodes/BarcodeKeyboardInput.tsx:64
msgid "Enter barcode data"
msgstr "Введите данные штрихкода"

#: src/components/barcodes/BarcodeScanDialog.tsx:49
#: src/components/buttons/ScanButton.tsx:15
#: src/components/nav/NavigationDrawer.tsx:129
#: src/forms/PurchaseOrderForms.tsx:454
#: src/forms/PurchaseOrderForms.tsx:560
msgid "Scan Barcode"
msgstr "Сканировать штрихкод"

#: src/components/barcodes/BarcodeScanDialog.tsx:105
msgid "No matching item found"
msgstr "Подходящих элементов не найдено"

#: src/components/barcodes/BarcodeScanDialog.tsx:134
msgid "Barcode does not match the expected model type"
msgstr "Штрихкод не соответствует ожидаемому типу модели"

#: src/components/barcodes/BarcodeScanDialog.tsx:145
#: src/components/editors/NotesEditor.tsx:84
#: src/components/editors/NotesEditor.tsx:118
#: src/components/forms/ApiForm.tsx:451
#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:45
#: src/tables/bom/BomTable.tsx:518
#: src/tables/settings/PendingTasksTable.tsx:68
msgid "Success"
msgstr "Успешно"

#: src/components/barcodes/BarcodeScanDialog.tsx:151
msgid "Failed to handle barcode"
msgstr "Не удалось обработать штрихкод"

#: src/components/barcodes/BarcodeScanDialog.tsx:167
#: src/pages/Index/Scan.tsx:129
msgid "Failed to scan barcode"
msgstr "Ошибка сканирования штрихкода"

#: src/components/barcodes/QRCode.tsx:94
msgid "Low (7%)"
msgstr "Низкий (7%)"

#: src/components/barcodes/QRCode.tsx:95
msgid "Medium (15%)"
msgstr "Средний (15%)"

#: src/components/barcodes/QRCode.tsx:96
msgid "Quartile (25%)"
msgstr "Четверть (25%)"

#: src/components/barcodes/QRCode.tsx:97
msgid "High (30%)"
msgstr "Высокий (30%)"

#: src/components/barcodes/QRCode.tsx:107
msgid "Custom barcode"
msgstr "Пользовательский штрихкод"

#: src/components/barcodes/QRCode.tsx:108
msgid "A custom barcode is registered for this item. The shown code is not that custom barcode."
msgstr "Для этого товара зарегистрирован пользовательский штрих-код. Показанный код не является штрих-кодом."

#: src/components/barcodes/QRCode.tsx:127
msgid "Barcode Data:"
msgstr "Данные штрих-кода:"

#: src/components/barcodes/QRCode.tsx:138
msgid "Select Error Correction Level"
msgstr "Выберите уровень исправления ошибок"

#: src/components/barcodes/QRCode.tsx:170
msgid "Failed to link barcode"
msgstr "Не удалось привязать штрихкод"

#: src/components/barcodes/QRCode.tsx:179
#: src/pages/part/PartDetail.tsx:522
#: src/pages/purchasing/PurchaseOrderDetail.tsx:204
#: src/pages/sales/ReturnOrderDetail.tsx:168
#: src/pages/sales/SalesOrderDetail.tsx:180
#: src/pages/sales/SalesOrderShipmentDetail.tsx:168
msgid "Link"
msgstr "Ссылка"

#: src/components/barcodes/QRCode.tsx:200
msgid "This will remove the link to the associated barcode"
msgstr "Это удалит ссылку на связанный штрих-код"

#: src/components/barcodes/QRCode.tsx:205
#: src/components/items/ActionDropdown.tsx:190
#: src/forms/PurchaseOrderForms.tsx:551
msgid "Unlink Barcode"
msgstr "Отвязать штрихкод"

#: src/components/buttons/AdminButton.tsx:86
msgid "Open in admin interface"
msgstr "Открыть в панели администратора"

#: src/components/buttons/CopyButton.tsx:18
#~ msgid "Copy to clipboard"
#~ msgstr "Copy to clipboard"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copied"
msgstr "Скопировано"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copy"
msgstr "Копировать"

#: src/components/buttons/PrintingActions.tsx:51
msgid "Printing Labels"
msgstr "Печать этикеток"

#: src/components/buttons/PrintingActions.tsx:56
msgid "Printing Reports"
msgstr "Печать отчётов"

#: src/components/buttons/PrintingActions.tsx:77
#~ msgid "Printing"
#~ msgstr "Printing"

#: src/components/buttons/PrintingActions.tsx:78
#~ msgid "Printing completed successfully"
#~ msgstr "Printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:114
#~ msgid "Label printing completed successfully"
#~ msgstr "Label printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:116
msgid "Print Label"
msgstr "Печать этикеток"

#: src/components/buttons/PrintingActions.tsx:121
#~ msgid "The label could not be generated"
#~ msgstr "The label could not be generated"

#: src/components/buttons/PrintingActions.tsx:122
#: src/components/buttons/PrintingActions.tsx:148
msgid "Print"
msgstr "Печать"

#: src/components/buttons/PrintingActions.tsx:131
msgid "Print Report"
msgstr "Печать отчета"

#: src/components/buttons/PrintingActions.tsx:152
#~ msgid "Generate"
#~ msgstr "Generate"

#: src/components/buttons/PrintingActions.tsx:153
#~ msgid "Report printing completed successfully"
#~ msgstr "Report printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:159
#~ msgid "The report could not be generated"
#~ msgstr "The report could not be generated"

#: src/components/buttons/PrintingActions.tsx:169
msgid "Printing Actions"
msgstr "Действия печати"

#: src/components/buttons/PrintingActions.tsx:174
msgid "Print Labels"
msgstr "Печать этикеток"

#: src/components/buttons/PrintingActions.tsx:180
msgid "Print Reports"
msgstr "Печать отчётов"

#: src/components/buttons/RemoveRowButton.tsx:8
msgid "Remove this row"
msgstr "Удалить эту строку"

#: src/components/buttons/SSOButton.tsx:40
msgid "You will be redirected to the provider for further actions."
msgstr "Вы будете перенаправлены на сайт поставщика для дальнейших действий."

#: src/components/buttons/SSOButton.tsx:44
#~ msgid "This provider is not full set up."
#~ msgstr "This provider is not full set up."

#: src/components/buttons/SSOButton.tsx:54
#~ msgid "Sign in redirect failed."
#~ msgstr "Sign in redirect failed."

#: src/components/buttons/ScanButton.tsx:15
#~ msgid "Scan QR code"
#~ msgstr "Scan QR code"

#: src/components/buttons/ScanButton.tsx:19
msgid "Open Barcode Scanner"
msgstr "Открыть сканер штрих-кодов"

#: src/components/buttons/ScanButton.tsx:20
#~ msgid "Open QR code scanner"
#~ msgstr "Open QR code scanner"

#: src/components/buttons/SpotlightButton.tsx:12
msgid "Open spotlight"
msgstr "Открыть всплывающее окно"

#: src/components/buttons/StarredToggleButton.tsx:36
msgid "Subscription Updated"
msgstr ""

#: src/components/buttons/StarredToggleButton.tsx:57
#~ msgid "Unsubscribe from part"
#~ msgstr "Unsubscribe from part"

#: src/components/buttons/StarredToggleButton.tsx:66
msgid "Unsubscribe from notifications"
msgstr "Отписаться от уведомлений"

#: src/components/buttons/StarredToggleButton.tsx:67
msgid "Subscribe to notifications"
msgstr "Подписаться на уведомления"

#: src/components/calendar/Calendar.tsx:99
#: src/components/calendar/Calendar.tsx:162
msgid "Calendar Filters"
msgstr "Фильтр календаря"

#: src/components/calendar/Calendar.tsx:114
msgid "Previous month"
msgstr "Предыдущий месяц"

#: src/components/calendar/Calendar.tsx:123
msgid "Select month"
msgstr "Выбрать месяц"

#: src/components/calendar/Calendar.tsx:144
msgid "Next month"
msgstr "Следующий месяц"

#: src/components/calendar/Calendar.tsx:175
#: src/tables/InvenTreeTableHeader.tsx:291
msgid "Download data"
msgstr "Загрузить данные"

#: src/components/calendar/OrderCalendar.tsx:132
msgid "Order Updated"
msgstr "Заказ обновлен"

#: src/components/calendar/OrderCalendar.tsx:142
msgid "Error updating order"
msgstr "Ошибка обновления заказа"

#: src/components/calendar/OrderCalendar.tsx:178
#: src/tables/Filter.tsx:144
msgid "Overdue"
msgstr "Просроченный"

#: src/components/dashboard/DashboardLayout.tsx:225
msgid "Failed to load dashboard widgets."
msgstr "Не удалось загрузить виджеты контрольной панели."

#: src/components/dashboard/DashboardLayout.tsx:235
msgid "No Widgets Selected"
msgstr "Виджеты не выбраны"

#: src/components/dashboard/DashboardLayout.tsx:238
msgid "Use the menu to add widgets to the dashboard"
msgstr "Используйте меню для добавления виджетов на панель"

#: src/components/dashboard/DashboardMenu.tsx:62
#: src/components/dashboard/DashboardMenu.tsx:138
msgid "Accept Layout"
msgstr "Сохранить макет"

#: src/components/dashboard/DashboardMenu.tsx:94
#: src/components/nav/NavigationDrawer.tsx:71
#: src/defaults/actions.tsx:28
#: src/defaults/links.tsx:31
#: src/pages/Index/Home.tsx:8
msgid "Dashboard"
msgstr "Контрольная панель"

#: src/components/dashboard/DashboardMenu.tsx:102
msgid "Edit Layout"
msgstr "Изменить макет"

#: src/components/dashboard/DashboardMenu.tsx:111
msgid "Add Widget"
msgstr "Добавить виджет"

#: src/components/dashboard/DashboardMenu.tsx:120
msgid "Remove Widgets"
msgstr "Удалить виджеты"

#: src/components/dashboard/DashboardMenu.tsx:129
msgid "Clear Widgets"
msgstr "Очистить макет"

#: src/components/dashboard/DashboardWidget.tsx:81
msgid "Remove this widget from the dashboard"
msgstr "Удалить этот виджет с панели"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:77
msgid "Filter dashboard widgets"
msgstr "Отфильтровать виджеты"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:98
msgid "Add this widget to the dashboard"
msgstr "Добавить этот виджет на панель"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:123
msgid "No Widgets Available"
msgstr "Нет доступных виджетов"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:124
msgid "There are no more widgets available for the dashboard"
msgstr "Больше нет виджетов, доступных для вывода на панель"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:24
msgid "Subscribed Parts"
msgstr "Отслеживаемые детали"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:25
msgid "Show the number of parts which you have subscribed to"
msgstr "Количество деталей, на которые вы подписаны"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:31
msgid "Subscribed Categories"
msgstr "Отслеживаемые категории"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:32
msgid "Show the number of part categories which you have subscribed to"
msgstr "Количество категорий деталей, на которые вы подписаны"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:38
msgid "Invalid BOMs"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:39
msgid "Assemblies requiring bill of materials validation"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:50
#: src/tables/part/PartTable.tsx:250
msgid "Low Stock"
msgstr "Низкий запас"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:52
msgid "Show the number of parts which are low on stock"
msgstr "Количество деталей с низким запасом"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:57
msgid "Required for Build Orders"
msgstr "Требуется для заказов на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:59
msgid "Show parts which are required for active build orders"
msgstr "Детали, необходимые для запущенных заказов на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:64
msgid "Expired Stock Items"
msgstr "Просроченные запасы"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:66
msgid "Show the number of stock items which have expired"
msgstr "Количество просроченных запасов"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:72
msgid "Stale Stock Items"
msgstr "Залежавшиеся запасы"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:74
msgid "Show the number of stock items which are stale"
msgstr "Количество залежавшихся запасов"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:80
msgid "Active Build Orders"
msgstr "Активные заказы на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:82
msgid "Show the number of build orders which are currently active"
msgstr "Количество активных заказов на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:87
msgid "Overdue Build Orders"
msgstr "Просроченные заказы на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:89
msgid "Show the number of build orders which are overdue"
msgstr "Количество просроченных заказов на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:94
msgid "Assigned Build Orders"
msgstr "Назначенные заказы на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:96
msgid "Show the number of build orders which are assigned to you"
msgstr "Количество назначенных на вас заказов на сборку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:101
msgid "Active Sales Orders"
msgstr "Активные сбытовые заказы"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:103
msgid "Show the number of sales orders which are currently active"
msgstr "Количество активных сбытовых заказов"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:108
msgid "Overdue Sales Orders"
msgstr "Просроченные заказы на продажу"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:110
msgid "Show the number of sales orders which are overdue"
msgstr "Количество просроченных заказов на продажу"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:115
msgid "Assigned Sales Orders"
msgstr "Назначенные сбытовые заказы"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:117
msgid "Show the number of sales orders which are assigned to you"
msgstr "Количество назначенных вам заказов на продажу"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:122
msgid "Active Purchase Orders"
msgstr "Активные заказы на поставку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:124
msgid "Show the number of purchase orders which are currently active"
msgstr "Количество активных заказов на поставку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:129
msgid "Overdue Purchase Orders"
msgstr "Просроченные заказы на закупку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:131
msgid "Show the number of purchase orders which are overdue"
msgstr "Количество просроченных заказов на поставку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:136
msgid "Assigned Purchase Orders"
msgstr "Назначенные заказы на поставку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:138
msgid "Show the number of purchase orders which are assigned to you"
msgstr "Количество назначенных на вас заказов на поставку"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:143
msgid "Active Return Orders"
msgstr "Активные заказы на возврат"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:145
msgid "Show the number of return orders which are currently active"
msgstr "Количество активных заказов на возврат"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:150
msgid "Overdue Return Orders"
msgstr "Просроченные заказы на возврат"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:152
msgid "Show the number of return orders which are overdue"
msgstr "Количество просроченных заказов на возврат"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:157
msgid "Assigned Return Orders"
msgstr "Назначенные заказы на возврат"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:159
msgid "Show the number of return orders which are assigned to you"
msgstr "Количество назначенных на вас заказов на возврат"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:179
#: src/components/dashboard/widgets/GetStartedWidget.tsx:15
#: src/defaults/links.tsx:86
msgid "Getting Started"
msgstr "Начать работу"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:180
#: src/defaults/links.tsx:89
msgid "Getting started with InvenTree"
msgstr "Начало работы с InvenTree"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:187
#: src/components/dashboard/widgets/NewsWidget.tsx:123
msgid "News Updates"
msgstr "Новости"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:188
msgid "The latest news from InvenTree"
msgstr "Свежие новости от InvenTree"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:18
#: src/components/nav/MainMenu.tsx:87
msgid "Change Color Mode"
msgstr "Изменить цветовой режим"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:23
msgid "Change the color mode of the user interface"
msgstr "Изменить цветовую схему"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:18
msgid "Change Language"
msgstr "Сменить язык"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:23
msgid "Change the language of the user interface"
msgstr "Сменить язык интерфейса"

#: src/components/dashboard/widgets/NewsWidget.tsx:60
#: src/components/nav/NotificationDrawer.tsx:94
#: src/pages/Notifications.tsx:53
msgid "Mark as read"
msgstr "Пометить как прочитанное"

#: src/components/dashboard/widgets/NewsWidget.tsx:115
msgid "Requires Superuser"
msgstr "Требует прав суперпользователя"

#: src/components/dashboard/widgets/NewsWidget.tsx:116
msgid "This widget requires superuser permissions"
msgstr "Этот виджет требует прав суперпользователя"

#: src/components/dashboard/widgets/NewsWidget.tsx:133
msgid "No News"
msgstr "Новостей нет"

#: src/components/dashboard/widgets/NewsWidget.tsx:134
msgid "There are no unread news items"
msgstr "Нет непрочитанных новостей"

#: src/components/details/Details.tsx:117
#~ msgid "Email:"
#~ msgstr "Email:"

#: src/components/details/Details.tsx:123
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:76
#: src/pages/core/UserDetail.tsx:93
#: src/pages/core/UserDetail.tsx:203
#: src/tables/settings/UserTable.tsx:420
msgid "Superuser"
msgstr "Суперпользователь"

#: src/components/details/Details.tsx:124
#: src/pages/core/UserDetail.tsx:87
#: src/pages/core/UserDetail.tsx:200
#: src/tables/settings/UserTable.tsx:415
msgid "Staff"
msgstr "Сотрудник"

#: src/components/details/Details.tsx:125
msgid "Email: "
msgstr "Электронная почта: "

#: src/components/details/Details.tsx:407
msgid "No name defined"
msgstr "Имя не определено"

#: src/components/details/DetailsImage.tsx:77
msgid "Remove Image"
msgstr "Убрать изображение"

#: src/components/details/DetailsImage.tsx:80
msgid "Remove the associated image from this item?"
msgstr "Удалить связанное изображение?"

#: src/components/details/DetailsImage.tsx:83
#: src/forms/StockForms.tsx:828
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:203
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:408
msgid "Remove"
msgstr "Удалить"

#: src/components/details/DetailsImage.tsx:109
msgid "Drag and drop to upload"
msgstr "Перетащите для загрузки"

#: src/components/details/DetailsImage.tsx:112
msgid "Click to select file(s)"
msgstr "Нажмите, чтобы выбрать файл(ы)"

#: src/components/details/DetailsImage.tsx:172
msgid "Image uploaded"
msgstr "Изображение загружено"

#: src/components/details/DetailsImage.tsx:173
msgid "Image has been uploaded successfully"
msgstr "Изображение успешно загружено"

#: src/components/details/DetailsImage.tsx:180
#: src/tables/general/AttachmentTable.tsx:201
msgid "Upload Error"
msgstr "Ошибка загрузки"

#: src/components/details/DetailsImage.tsx:250
msgid "Clear"
msgstr "Очистить"

#: src/components/details/DetailsImage.tsx:256
#: src/components/forms/ApiForm.tsx:661
#: src/contexts/ThemeContext.tsx:44
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:654
msgid "Submit"
msgstr "Подтвердить"

#: src/components/details/DetailsImage.tsx:300
msgid "Select from existing images"
msgstr "Выбрать из существующих изображений"

#: src/components/details/DetailsImage.tsx:308
msgid "Select Image"
msgstr "Выбрать изображение"

#: src/components/details/DetailsImage.tsx:324
msgid "Download remote image"
msgstr "Скачать изображение из удаленного источника"

#: src/components/details/DetailsImage.tsx:339
msgid "Upload new image"
msgstr "Загрузить новое изображение"

#: src/components/details/DetailsImage.tsx:346
msgid "Upload Image"
msgstr "Загрузить изображение"

#: src/components/details/DetailsImage.tsx:359
msgid "Delete image"
msgstr "Удалить изображение"

#: src/components/details/DetailsImage.tsx:393
msgid "Download Image"
msgstr "Скачать изображение"

#: src/components/details/DetailsImage.tsx:398
msgid "Image downloaded successfully"
msgstr "Изображение успешно скачано"

#: src/components/details/PartIcons.tsx:43
#~ msgid "Part is a template part (variants can be made from this part)"
#~ msgstr "Part is a template part (variants can be made from this part)"

#: src/components/details/PartIcons.tsx:49
#~ msgid "Part can be assembled from other parts"
#~ msgstr "Part can be assembled from other parts"

#: src/components/details/PartIcons.tsx:55
#~ msgid "Part can be used in assemblies"
#~ msgstr "Part can be used in assemblies"

#: src/components/details/PartIcons.tsx:61
#~ msgid "Part stock is tracked by serial number"
#~ msgstr "Part stock is tracked by serial number"

#: src/components/details/PartIcons.tsx:67
#~ msgid "Part can be purchased from external suppliers"
#~ msgstr "Part can be purchased from external suppliers"

#: src/components/details/PartIcons.tsx:73
#~ msgid "Part can be sold to customers"
#~ msgstr "Part can be sold to customers"

#: src/components/details/PartIcons.tsx:78
#~ msgid "Part is virtual (not a physical part)"
#~ msgstr "Part is virtual (not a physical part)"

#: src/components/editors/NotesEditor.tsx:75
msgid "Image upload failed"
msgstr "Не удалось загрузить изображение"

#: src/components/editors/NotesEditor.tsx:85
msgid "Image uploaded successfully"
msgstr "Изображение успешно загружено"

#: src/components/editors/NotesEditor.tsx:119
msgid "Notes saved successfully"
msgstr "Заметка успешно сохранена"

#: src/components/editors/NotesEditor.tsx:130
msgid "Failed to save notes"
msgstr "Не удалось сохранить заметки"

#: src/components/editors/NotesEditor.tsx:133
msgid "Error Saving Notes"
msgstr "Ошибка при сохранении заметок"

#: src/components/editors/NotesEditor.tsx:151
#~ msgid "Disable Editing"
#~ msgstr "Disable Editing"

#: src/components/editors/NotesEditor.tsx:153
msgid "Save Notes"
msgstr "Сохранить заметки"

#: src/components/editors/NotesEditor.tsx:172
msgid "Close Editor"
msgstr "Закрыть редактор"

#: src/components/editors/NotesEditor.tsx:179
msgid "Enable Editing"
msgstr "Разрешить редактирование"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Preview Notes"
#~ msgstr "Preview Notes"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Edit Notes"
#~ msgstr "Edit Notes"

#: src/components/editors/TemplateEditor/CodeEditor/index.tsx:9
msgid "Code"
msgstr "Код"

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:44
#~ msgid "Failed to parse error response from server."
#~ msgstr "Failed to parse error response from server."

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:50
msgid "Error rendering preview"
msgstr "Ошибка при отрисовке предпросмотра"

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:120
msgid "Preview not available, click \"Reload Preview\"."
msgstr "Предварительный просмотр недоступен, нажмите \"Перезагрузить предпросмотр\"."

#: src/components/editors/TemplateEditor/PdfPreview/index.tsx:9
msgid "PDF Preview"
msgstr "Предварительный просмотр в PDF"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:109
msgid "Error loading template"
msgstr "Ошибка загрузки шаблона"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:121
msgid "Error saving template"
msgstr "Ошибка при сохранении шаблона"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:151
#~ msgid "Save & Reload preview?"
#~ msgstr "Save & Reload preview?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:158
msgid "Could not load the template from the server."
msgstr "Не удалось загрузить шаблон с сервера"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:175
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:306
msgid "Save & Reload Preview"
msgstr "Сохранить и перезагрузить предпросмотр"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:180
msgid "Are you sure you want to Save & Reload the preview?"
msgstr "Вы уверены, что хотите сохранить и перезагрузить предпросмотр?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:182
msgid "To render the preview the current template needs to be replaced on the server with your modifications which may break the label if it is under active use. Do you want to proceed?"
msgstr "Для отображения предварительного просмотра текущий шаблон должен быть заменен на ваши модификации, которые могут нарушить метку, если она используется в активном режиме. Вы хотите продолжить?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:186
msgid "Save & Reload"
msgstr "Сохранить и перезагрузить"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:218
msgid "Preview updated"
msgstr "Предпросмотр обновлен"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:219
msgid "The preview has been updated successfully."
msgstr "Предварительный просмотр успешно обновлен."

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:263
#~ msgid "Save & Reload preview"
#~ msgstr "Save & Reload preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:298
msgid "Reload preview"
msgstr "Перезагрузить предварительный просмотр"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:299
msgid "Use the currently stored template from the server"
msgstr "Использовать текущий шаблон с сервера"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:307
msgid "Save the current template and reload the preview"
msgstr "Сохранить текущий шаблон и обновить предпросмотр"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:322
#~ msgid "to preview"
#~ msgstr "to preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:366
msgid "Select instance to preview"
msgstr "Выберите экземпляр для просмотра"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:410
msgid "Error rendering template"
msgstr "Ошибка отображения шаблона"

#: src/components/errors/ClientError.tsx:23
msgid "Client Error"
msgstr "Ошибка клиента"

#: src/components/errors/ClientError.tsx:24
msgid "Client error occurred"
msgstr "Произошла ошибка клиента"

#: src/components/errors/GenericErrorPage.tsx:50
msgid "Status Code"
msgstr "Код статуса"

#: src/components/errors/GenericErrorPage.tsx:63
msgid "Return to the index page"
msgstr "Вернуться на главную страницу"

#: src/components/errors/NotAuthenticated.tsx:8
msgid "Not Authenticated"
msgstr "Не аутентифицирован"

#: src/components/errors/NotAuthenticated.tsx:9
msgid "You are not logged in."
msgstr "Вы не авторизованы."

#: src/components/errors/NotFound.tsx:8
msgid "Page Not Found"
msgstr "Страница не найдена"

#: src/components/errors/NotFound.tsx:9
msgid "This page does not exist"
msgstr "Данной страницы не существует"

#: src/components/errors/PermissionDenied.tsx:8
#: src/functions/notifications.tsx:25
msgid "Permission Denied"
msgstr "Доступ запрещён"

#: src/components/errors/PermissionDenied.tsx:9
msgid "You do not have permission to view this page."
msgstr "У вас нет прав для просмотра этой страницы."

#: src/components/errors/ServerError.tsx:8
msgid "Server Error"
msgstr "Ошибка сервера"

#: src/components/errors/ServerError.tsx:9
msgid "A server error occurred"
msgstr "Произошла ошибка сервера"

#: src/components/forms/ApiForm.tsx:103
#: src/components/forms/ApiForm.tsx:580
msgid "Form Error"
msgstr "Ошибка формы"

#: src/components/forms/ApiForm.tsx:487
#~ msgid "Form Errors Exist"
#~ msgstr "Form Errors Exist"

#: src/components/forms/ApiForm.tsx:588
msgid "Errors exist for one or more form fields"
msgstr "Существуют ошибки для одного или нескольких полей формы"

#: src/components/forms/ApiForm.tsx:699
#: src/hooks/UseForm.tsx:129
#: src/tables/plugin/PluginListTable.tsx:204
msgid "Update"
msgstr "Обновить"

#: src/components/forms/AuthenticationForm.tsx:48
#: src/components/forms/AuthenticationForm.tsx:74
#: src/functions/auth.tsx:83
#~ msgid "Check your your input and try again."
#~ msgstr "Check your your input and try again."

#: src/components/forms/AuthenticationForm.tsx:52
#~ msgid "Welcome back!"
#~ msgstr "Welcome back!"

#: src/components/forms/AuthenticationForm.tsx:53
#~ msgid "Login successfull"
#~ msgstr "Login successfull"

#: src/components/forms/AuthenticationForm.tsx:65
#: src/functions/auth.tsx:74
#~ msgid "Mail delivery successfull"
#~ msgstr "Mail delivery successfull"

#: src/components/forms/AuthenticationForm.tsx:73
msgid "Login successful"
msgstr "Вы вошли"

#: src/components/forms/AuthenticationForm.tsx:74
msgid "Logged in successfully"
msgstr "Вы успешно вошли в систему"

#: src/components/forms/AuthenticationForm.tsx:81
#: src/components/forms/AuthenticationForm.tsx:89
msgid "Login failed"
msgstr "Ошибка входа"

#: src/components/forms/AuthenticationForm.tsx:82
#: src/components/forms/AuthenticationForm.tsx:90
#: src/components/forms/AuthenticationForm.tsx:106
#: src/functions/auth.tsx:282
msgid "Check your input and try again."
msgstr "Проверьте введенные данные и повторите попытку."

#: src/components/forms/AuthenticationForm.tsx:100
#: src/functions/auth.tsx:273
msgid "Mail delivery successful"
msgstr "Отправка почты прошла успешно"

#: src/components/forms/AuthenticationForm.tsx:101
msgid "Check your inbox for the login link. If you have an account, you will receive a login link. Check in spam too."
msgstr "Проверьте свой почтовый ящик на наличие ссылки для входа в систему. Если у вас есть учетная запись, вы получите ссылку для входа в систему. Проверьте также спам."

#: src/components/forms/AuthenticationForm.tsx:105
msgid "Mail delivery failed"
msgstr "Не удалось доставить почту"

#: src/components/forms/AuthenticationForm.tsx:125
msgid "Or continue with other methods"
msgstr "Или продолжить с другими методами"

#: src/components/forms/AuthenticationForm.tsx:136
#: src/components/forms/AuthenticationForm.tsx:296
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:64
#: src/pages/core/UserDetail.tsx:48
msgid "Username"
msgstr "Логин"

#: src/components/forms/AuthenticationForm.tsx:136
#~ msgid "I will use username and password"
#~ msgstr "I will use username and password"

#: src/components/forms/AuthenticationForm.tsx:138
#: src/components/forms/AuthenticationForm.tsx:298
msgid "Your username"
msgstr "Логин"

#: src/components/forms/AuthenticationForm.tsx:143
#: src/components/forms/AuthenticationForm.tsx:311
#: src/pages/Auth/ResetPassword.tsx:34
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:708
msgid "Password"
msgstr "Пароль"

#: src/components/forms/AuthenticationForm.tsx:145
#: src/components/forms/AuthenticationForm.tsx:313
msgid "Your password"
msgstr "Ваш пароль"

#: src/components/forms/AuthenticationForm.tsx:164
msgid "Reset password"
msgstr "Сбросить пароль"

#: src/components/forms/AuthenticationForm.tsx:173
#: src/components/forms/AuthenticationForm.tsx:303
#: src/pages/Auth/Reset.tsx:17
#: src/pages/core/UserDetail.tsx:71
msgid "Email"
msgstr "Электронная почта"

#: src/components/forms/AuthenticationForm.tsx:174
#: src/pages/Auth/Reset.tsx:18
msgid "We will send you a link to login - if you are registered"
msgstr "Мы вышлем вам ссылку для входа - если вы зарегистрированы"

#: src/components/forms/AuthenticationForm.tsx:190
msgid "Send me an email"
msgstr "Отправьте мне электронное письмо"

#: src/components/forms/AuthenticationForm.tsx:192
msgid "Use username and password"
msgstr "Логин и пароль"

#: src/components/forms/AuthenticationForm.tsx:201
msgid "Log In"
msgstr "Войти"

#: src/components/forms/AuthenticationForm.tsx:203
#: src/pages/Auth/Reset.tsx:26
msgid "Send Email"
msgstr "Отправить email"

#: src/components/forms/AuthenticationForm.tsx:239
msgid "Passwords do not match"
msgstr "Пароли не совпадают"

#: src/components/forms/AuthenticationForm.tsx:256
msgid "Registration successful"
msgstr "Регистрация выполнена успешно"

#: src/components/forms/AuthenticationForm.tsx:257
msgid "Please confirm your email address to complete the registration"
msgstr "Пожалуйста, подтвердите адрес электронной почты для завершения регистрации"

#: src/components/forms/AuthenticationForm.tsx:280
msgid "Input error"
msgstr "Ошибка ввода"

#: src/components/forms/AuthenticationForm.tsx:281
msgid "Check your input and try again. "
msgstr "Проверьте введенные данные и повторите попытку. "

#: src/components/forms/AuthenticationForm.tsx:305
msgid "This will be used for a confirmation"
msgstr "Это будет использовано для подтверждения"

#: src/components/forms/AuthenticationForm.tsx:318
msgid "Password repeat"
msgstr "Повторите пароль"

#: src/components/forms/AuthenticationForm.tsx:320
msgid "Repeat password"
msgstr "Введите пароль еще раз"

#: src/components/forms/AuthenticationForm.tsx:332
#: src/pages/Auth/Login.tsx:121
#: src/pages/Auth/Register.tsx:13
msgid "Register"
msgstr "Регистрация"

#: src/components/forms/AuthenticationForm.tsx:338
msgid "Or use SSO"
msgstr "Или используйте SSO"

#: src/components/forms/AuthenticationForm.tsx:348
msgid "Registration not active"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:349
msgid "This might be related to missing mail settings or could be a deliberate decision."
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:36
#: src/components/forms/HostOptionsForm.tsx:67
msgid "Host"
msgstr "Узел"

#: src/components/forms/HostOptionsForm.tsx:42
#: src/components/forms/HostOptionsForm.tsx:70
#: src/components/forms/InstanceOptions.tsx:124
#: src/components/plugins/PluginDrawer.tsx:68
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:19
#: src/pages/part/CategoryDetail.tsx:86
#: src/pages/part/PartDetail.tsx:447
#: src/pages/stock/LocationDetail.tsx:84
#: src/tables/machine/MachineTypeTable.tsx:72
#: src/tables/machine/MachineTypeTable.tsx:118
#: src/tables/machine/MachineTypeTable.tsx:236
#: src/tables/machine/MachineTypeTable.tsx:339
#: src/tables/plugin/PluginErrorTable.tsx:33
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:36
#: src/tables/settings/ApiTokenTable.tsx:58
#: src/tables/settings/GroupTable.tsx:95
#: src/tables/settings/GroupTable.tsx:148
#: src/tables/settings/GroupTable.tsx:196
#: src/tables/settings/PendingTasksTable.tsx:37
#: src/tables/stock/LocationTypesTable.tsx:74
msgid "Name"
msgstr "Название"

#: src/components/forms/HostOptionsForm.tsx:75
msgid "No one here..."
msgstr "Здесь никого..."

#: src/components/forms/HostOptionsForm.tsx:86
msgid "Add Host"
msgstr "Добавить узел"

#: src/components/forms/HostOptionsForm.tsx:90
#: src/components/items/RoleTable.tsx:224
#: src/components/items/TransferList.tsx:215
#: src/components/items/TransferList.tsx:223
msgid "Save"
msgstr "Сохранить"

#: src/components/forms/InstanceOptions.tsx:43
#~ msgid "Select destination instance"
#~ msgstr "Select destination instance"

#: src/components/forms/InstanceOptions.tsx:58
msgid "Select Server"
msgstr "Выбор сервера"

#: src/components/forms/InstanceOptions.tsx:68
#: src/components/forms/InstanceOptions.tsx:92
msgid "Edit host options"
msgstr "Изменение параметров хостов"

#: src/components/forms/InstanceOptions.tsx:71
#~ msgid "Edit possible host options"
#~ msgstr "Edit possible host options"

#: src/components/forms/InstanceOptions.tsx:76
msgid "Save host selection"
msgstr "Сохранить выбор хоста"

#: src/components/forms/InstanceOptions.tsx:98
#~ msgid "Version: {0}"
#~ msgstr "Version: {0}"

#: src/components/forms/InstanceOptions.tsx:100
#~ msgid "API:{0}"
#~ msgstr "API:{0}"

#: src/components/forms/InstanceOptions.tsx:102
#~ msgid "Name: {0}"
#~ msgstr "Name: {0}"

#: src/components/forms/InstanceOptions.tsx:104
#~ msgid "State: <0>worker</0> ({0}), <1>plugins</1>{1}"
#~ msgstr "State: <0>worker</0> ({0}), <1>plugins</1>{1}"

#: src/components/forms/InstanceOptions.tsx:118
#: src/pages/Index/Settings/SystemSettings.tsx:45
msgid "Server"
msgstr "Сервер"

#: src/components/forms/InstanceOptions.tsx:130
#: src/components/plugins/PluginDrawer.tsx:88
#: src/tables/plugin/PluginListTable.tsx:127
msgid "Version"
msgstr "Версия"

#: src/components/forms/InstanceOptions.tsx:136
#: src/components/modals/AboutInvenTreeModal.tsx:122
#: src/components/modals/ServerInfoModal.tsx:34
msgid "API Version"
msgstr "Версия API"

#: src/components/forms/InstanceOptions.tsx:142
#: src/components/nav/NavigationDrawer.tsx:205
#: src/pages/Index/Settings/AdminCenter/Index.tsx:218
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:46
msgid "Plugins"
msgstr "Плагины"

#: src/components/forms/InstanceOptions.tsx:143
#: src/tables/part/PartTestTemplateTable.tsx:117
#: src/tables/settings/TemplateTable.tsx:251
#: src/tables/settings/TemplateTable.tsx:362
#: src/tables/stock/StockItemTestResultTable.tsx:416
msgid "Enabled"
msgstr "Включено"

#: src/components/forms/InstanceOptions.tsx:143
msgid "Disabled"
msgstr "Отключено"

#: src/components/forms/InstanceOptions.tsx:149
msgid "Worker"
msgstr "Рабочий процесс"

#: src/components/forms/InstanceOptions.tsx:150
#: src/tables/settings/FailedTasksTable.tsx:48
msgid "Stopped"
msgstr "Остановлен"

#: src/components/forms/InstanceOptions.tsx:150
msgid "Running"
msgstr "Работает"

#: src/components/forms/fields/IconField.tsx:83
msgid "No icon selected"
msgstr "Значок не выбран"

#: src/components/forms/fields/IconField.tsx:161
msgid "Uncategorized"
msgstr "Без категории"

#: src/components/forms/fields/IconField.tsx:211
#: src/components/nav/Layout.tsx:80
#: src/tables/part/PartThumbTable.tsx:201
msgid "Search..."
msgstr "Поиск..."

#: src/components/forms/fields/IconField.tsx:225
msgid "Select category"
msgstr "Выберите категорию"

#: src/components/forms/fields/IconField.tsx:234
msgid "Select pack"
msgstr "Выбрать набор"

#. placeholder {0}: filteredIcons.length
#: src/components/forms/fields/IconField.tsx:239
msgid "{0} icons"
msgstr "{0} значков"

#: src/components/forms/fields/RelatedModelField.tsx:388
#: src/components/modals/AboutInvenTreeModal.tsx:94
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:332
msgid "Loading"
msgstr "Загрузка"

#: src/components/forms/fields/RelatedModelField.tsx:390
msgid "No results found"
msgstr "Ничего не найдено"

#: src/components/forms/fields/TableField.tsx:46
msgid "modelRenderer entry required for tables"
msgstr "запись modelRenderer необходима для таблиц"

#: src/components/forms/fields/TableField.tsx:187
msgid "No entries available"
msgstr "Нет доступных записей"

#: src/components/forms/fields/TableField.tsx:198
msgid "Add new row"
msgstr "Добавить строку"

#: src/components/images/DetailsImage.tsx:252
#~ msgid "Select image"
#~ msgstr "Select image"

#: src/components/images/Thumbnail.tsx:12
msgid "Thumbnail"
msgstr "Миниатюра"

#: src/components/importer/ImportDataSelector.tsx:175
msgid "Importing Rows"
msgstr "Импорт строк"

#: src/components/importer/ImportDataSelector.tsx:176
msgid "Please wait while the data is imported"
msgstr "Пожалуйста, подождите, пока данные импортируются"

#: src/components/importer/ImportDataSelector.tsx:193
msgid "An error occurred while importing data"
msgstr "Произошла ошибка при импорте данных"

#: src/components/importer/ImportDataSelector.tsx:214
msgid "Edit Data"
msgstr "Изменить данные"

#: src/components/importer/ImportDataSelector.tsx:246
msgid "Delete Row"
msgstr "Удалить строку"

#: src/components/importer/ImportDataSelector.tsx:276
msgid "Row"
msgstr "Строка"

#: src/components/importer/ImportDataSelector.tsx:294
msgid "Row contains errors"
msgstr "Строка содержит ошибки"

#: src/components/importer/ImportDataSelector.tsx:335
msgid "Accept"
msgstr "Принять"

#: src/components/importer/ImportDataSelector.tsx:368
msgid "Valid"
msgstr "Верно"

#: src/components/importer/ImportDataSelector.tsx:369
msgid "Filter by row validation status"
msgstr "Фильтр по статусу проверки строк"

#: src/components/importer/ImportDataSelector.tsx:374
#: src/components/wizards/WizardDrawer.tsx:101
#: src/tables/build/BuildOutputTable.tsx:543
msgid "Complete"
msgstr "Готово"

#: src/components/importer/ImportDataSelector.tsx:375
msgid "Filter by row completion status"
msgstr "Фильтровать по статусу завершения строк"

#: src/components/importer/ImportDataSelector.tsx:393
msgid "Import selected rows"
msgstr "Импорт выделенных строк"

#: src/components/importer/ImportDataSelector.tsx:408
msgid "Processing Data"
msgstr "Обработка данных"

#: src/components/importer/ImporterColumnSelector.tsx:55
#: src/components/importer/ImporterColumnSelector.tsx:186
#: src/components/items/ErrorItem.tsx:12
#: src/functions/api.tsx:60
#: src/functions/auth.tsx:333
msgid "An error occurred"
msgstr "Произошла ошибка"

#: src/components/importer/ImporterColumnSelector.tsx:68
msgid "Select column, or leave blank to ignore this field."
msgstr "Выберите столбец, или оставьте пустым, чтобы игнорировать это поле."

#: src/components/importer/ImporterColumnSelector.tsx:91
#~ msgid "Select a column from the data file"
#~ msgstr "Select a column from the data file"

#: src/components/importer/ImporterColumnSelector.tsx:104
#~ msgid "Map data columns to database fields"
#~ msgstr "Map data columns to database fields"

#: src/components/importer/ImporterColumnSelector.tsx:119
#~ msgid "Imported Column Name"
#~ msgstr "Imported Column Name"

#: src/components/importer/ImporterColumnSelector.tsx:192
msgid "Ignore this field"
msgstr "Игнорировать это поле"

#: src/components/importer/ImporterColumnSelector.tsx:206
msgid "Mapping data columns to database fields"
msgstr "Сопоставление столбцов данных с полями базы данных"

#: src/components/importer/ImporterColumnSelector.tsx:211
msgid "Accept Column Mapping"
msgstr "Принять сопоставление колонок"

#: src/components/importer/ImporterColumnSelector.tsx:224
msgid "Database Field"
msgstr "Поле базы данных"

#: src/components/importer/ImporterColumnSelector.tsx:225
msgid "Field Description"
msgstr "Описание поля"

#: src/components/importer/ImporterColumnSelector.tsx:226
msgid "Imported Column"
msgstr "Импортированный столбец"

#: src/components/importer/ImporterColumnSelector.tsx:227
msgid "Default Value"
msgstr "Значение по умолчанию"

#: src/components/importer/ImporterDrawer.tsx:43
msgid "Upload File"
msgstr "Загрузить файл"

#: src/components/importer/ImporterDrawer.tsx:44
msgid "Map Columns"
msgstr "Сопоставить столбцы"

#: src/components/importer/ImporterDrawer.tsx:45
msgid "Import Data"
msgstr "Импорт данных"

#: src/components/importer/ImporterDrawer.tsx:46
msgid "Process Data"
msgstr "Обработать данные"

#: src/components/importer/ImporterDrawer.tsx:47
msgid "Complete Import"
msgstr "Завершить импорт"

#: src/components/importer/ImporterDrawer.tsx:89
msgid "Failed to fetch import session data"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:97
#~ msgid "Cancel import session"
#~ msgstr "Cancel import session"

#: src/components/importer/ImporterDrawer.tsx:104
msgid "Import Complete"
msgstr "Импорт завершён"

#: src/components/importer/ImporterDrawer.tsx:107
msgid "Data has been imported successfully"
msgstr "Данные успешно импортированы"

#: src/components/importer/ImporterDrawer.tsx:109
#: src/components/modals/AboutInvenTreeModal.tsx:197
#: src/components/modals/ServerInfoModal.tsx:134
#: src/forms/BomForms.tsx:132
msgid "Close"
msgstr "Закрыть"

#: src/components/importer/ImporterDrawer.tsx:119
#~ msgid "Import session has unknown status"
#~ msgstr "Import session has unknown status"

#: src/components/importer/ImporterDrawer.tsx:128
msgid "Importing Data"
msgstr "Импортирование данных"

#: src/components/importer/ImporterImportProgress.tsx:36
#~ msgid "Importing Records"
#~ msgstr "Importing Records"

#: src/components/importer/ImporterImportProgress.tsx:39
#~ msgid "Imported rows"
#~ msgstr "Imported rows"

#: src/components/importer/ImporterStatus.tsx:19
msgid "Unknown Status"
msgstr "Неизвестный статус"

#: src/components/items/ActionDropdown.tsx:133
msgid "Options"
msgstr "Опции"

#: src/components/items/ActionDropdown.tsx:162
#~ msgid "Link custom barcode"
#~ msgstr "Link custom barcode"

#: src/components/items/ActionDropdown.tsx:169
#: src/tables/InvenTreeTableHeader.tsx:193
#: src/tables/InvenTreeTableHeader.tsx:194
msgid "Barcode Actions"
msgstr "Действия со штрихкодом"

#: src/components/items/ActionDropdown.tsx:174
msgid "View Barcode"
msgstr "Показать штрихкод"

#: src/components/items/ActionDropdown.tsx:176
msgid "View barcode"
msgstr "Показать штрихкод"

#: src/components/items/ActionDropdown.tsx:182
msgid "Link Barcode"
msgstr "Привязать штрих-код"

#: src/components/items/ActionDropdown.tsx:184
msgid "Link a custom barcode to this item"
msgstr "Привязать индивидуальный штрих-код к этому предмету."

#: src/components/items/ActionDropdown.tsx:192
msgid "Unlink custom barcode"
msgstr "Отвязать пользовательский штрих-код"

#: src/components/items/ActionDropdown.tsx:244
msgid "Edit item"
msgstr "Редактировать элемент"

#: src/components/items/ActionDropdown.tsx:256
msgid "Delete item"
msgstr "Удалить элемент"

#: src/components/items/ActionDropdown.tsx:264
#: src/components/items/ActionDropdown.tsx:265
msgid "Hold"
msgstr "Отложить"

#: src/components/items/ActionDropdown.tsx:288
msgid "Duplicate item"
msgstr "Дублировать элемент"

#: src/components/items/BarcodeInput.tsx:24
#~ msgid "Scan barcode data here using barcode scanner"
#~ msgstr "Scan barcode data here using barcode scanner"

#: src/components/items/ColorToggle.tsx:17
msgid "Toggle color scheme"
msgstr "Переключить цветовую схему"

#: src/components/items/DocTooltip.tsx:92
#: src/components/items/GettingStartedCarousel.tsx:20
msgid "Read More"
msgstr "Подробнее"

#: src/components/items/ErrorItem.tsx:8
#: src/functions/api.tsx:51
#: src/tables/settings/PendingTasksTable.tsx:80
msgid "Unknown error"
msgstr "Неизвестная ошибка"

#: src/components/items/ErrorItem.tsx:13
#~ msgid "An error occurred:"
#~ msgstr "An error occurred:"

#: src/components/items/GettingStartedCarousel.tsx:27
#~ msgid "Read more"
#~ msgstr "Read more"

#: src/components/items/InfoItem.tsx:27
msgid "None"
msgstr "None"

#: src/components/items/InvenTreeLogo.tsx:23
msgid "InvenTree Logo"
msgstr "Логотип InvenTree"

#: src/components/items/LanguageToggle.tsx:21
msgid "Select language"
msgstr "Сменить язык"

#: src/components/items/OnlyStaff.tsx:10
#: src/components/modals/AboutInvenTreeModal.tsx:50
msgid "This information is only available for staff users"
msgstr "Эта информация доступна только для сотрудников"

#: src/components/items/Placeholder.tsx:14
#~ msgid "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."
#~ msgstr "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."

#: src/components/items/Placeholder.tsx:17
#~ msgid "PLH"
#~ msgstr "PLH"

#: src/components/items/RoleTable.tsx:81
msgid "Updating"
msgstr "Обновление"

#: src/components/items/RoleTable.tsx:82
msgid "Updating group roles"
msgstr "Обновление ролей группы"

#: src/components/items/RoleTable.tsx:118
#: src/components/settings/ConfigValueList.tsx:42
#: src/pages/part/pricing/BomPricingPanel.tsx:191
#: src/pages/part/pricing/VariantPricingPanel.tsx:51
#: src/tables/purchasing/SupplierPartTable.tsx:137
msgid "Updated"
msgstr "Обновлено"

#: src/components/items/RoleTable.tsx:119
msgid "Group roles updated"
msgstr "Роли группы обновлены"

#: src/components/items/RoleTable.tsx:135
msgid "Role"
msgstr "Роль"

#: src/components/items/RoleTable.tsx:140
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:413
msgid "View"
msgstr "Просмотр"

#: src/components/items/RoleTable.tsx:145
msgid "Change"
msgstr "Редактировать"

#: src/components/items/RoleTable.tsx:150
#: src/forms/StockForms.tsx:867
#: src/tables/stock/StockItemTestResultTable.tsx:364
msgid "Add"
msgstr "Добавить"

#: src/components/items/RoleTable.tsx:203
msgid "Reset group roles"
msgstr "Сбросить роли группы"

#: src/components/items/RoleTable.tsx:212
msgid "Reset"
msgstr "Сброс"

#: src/components/items/RoleTable.tsx:215
msgid "Save group roles"
msgstr "Сохранить роли группы"

#: src/components/items/TransferList.tsx:65
msgid "No items"
msgstr "Нет элементов"

#: src/components/items/TransferList.tsx:161
#: src/components/render/Stock.tsx:95
#: src/pages/part/PartDetail.tsx:980
#: src/pages/stock/StockDetail.tsx:262
#: src/pages/stock/StockDetail.tsx:913
#: src/tables/build/BuildAllocatedStockTable.tsx:135
#: src/tables/build/BuildLineTable.tsx:190
#: src/tables/part/PartTable.tsx:124
#: src/tables/stock/StockItemTable.tsx:183
#: src/tables/stock/StockItemTable.tsx:344
msgid "Available"
msgstr "Доступно"

#: src/components/items/TransferList.tsx:162
msgid "Selected"
msgstr "Выбрано"

#: src/components/modals/AboutInvenTreeModal.tsx:103
#~ msgid "Your InvenTree version status is"
#~ msgstr "Your InvenTree version status is"

#: src/components/modals/AboutInvenTreeModal.tsx:116
msgid "InvenTree Version"
msgstr "Версия InvenTree"

#: src/components/modals/AboutInvenTreeModal.tsx:128
msgid "Python Version"
msgstr "Версия Python"

#: src/components/modals/AboutInvenTreeModal.tsx:133
msgid "Django Version"
msgstr "Версия Django"

#: src/components/modals/AboutInvenTreeModal.tsx:142
msgid "Commit Hash"
msgstr "Хеш коммита"

#: src/components/modals/AboutInvenTreeModal.tsx:147
msgid "Commit Date"
msgstr "Дата коммита"

#: src/components/modals/AboutInvenTreeModal.tsx:152
msgid "Commit Branch"
msgstr "Ветка коммита"

#: src/components/modals/AboutInvenTreeModal.tsx:163
msgid "Version Information"
msgstr "Информация о версии"

#: src/components/modals/AboutInvenTreeModal.tsx:165
#~ msgid "Credits"
#~ msgstr "Credits"

#: src/components/modals/AboutInvenTreeModal.tsx:168
#~ msgid "InvenTree Documentation"
#~ msgstr "InvenTree Documentation"

#: src/components/modals/AboutInvenTreeModal.tsx:169
#~ msgid "View Code on GitHub"
#~ msgstr "View Code on GitHub"

#: src/components/modals/AboutInvenTreeModal.tsx:172
msgid "Links"
msgstr "Ссылки"

#: src/components/modals/AboutInvenTreeModal.tsx:178
#: src/components/nav/NavigationDrawer.tsx:217
#: src/defaults/actions.tsx:35
msgid "Documentation"
msgstr "Документация"

#: src/components/modals/AboutInvenTreeModal.tsx:179
msgid "Source Code"
msgstr "Исходный код"

#: src/components/modals/AboutInvenTreeModal.tsx:180
msgid "Mobile App"
msgstr "Мобильное Приложение"

#: src/components/modals/AboutInvenTreeModal.tsx:181
msgid "Submit Bug Report"
msgstr "Сообщить об ошибке"

#: src/components/modals/AboutInvenTreeModal.tsx:189
#: src/components/modals/ServerInfoModal.tsx:147
#~ msgid "Dismiss"
#~ msgstr "Dismiss"

#: src/components/modals/AboutInvenTreeModal.tsx:190
msgid "Copy version information"
msgstr "Копировать информацию о версии"

#: src/components/modals/AboutInvenTreeModal.tsx:207
msgid "Development Version"
msgstr "Версия разработки"

#: src/components/modals/AboutInvenTreeModal.tsx:209
msgid "Up to Date"
msgstr "Последняя версия"

#: src/components/modals/AboutInvenTreeModal.tsx:211
msgid "Update Available"
msgstr "Доступно обновление"

#: src/components/modals/LicenseModal.tsx:41
msgid "No license text available"
msgstr "Нет доступного текста лицензии"

#: src/components/modals/LicenseModal.tsx:48
msgid "No Information provided - this is likely a server issue"
msgstr "Нет информации - это, скорее всего, проблема с сервером"

#: src/components/modals/LicenseModal.tsx:81
msgid "Loading license information"
msgstr "Загрузка информации о лицензии"

#: src/components/modals/LicenseModal.tsx:87
msgid "Failed to fetch license information"
msgstr "Не удалось получить информацию о лицензии"

#: src/components/modals/LicenseModal.tsx:99
msgid "{key} Packages"
msgstr "{key} Packages"

#: src/components/modals/QrCodeModal.tsx:24
#~ msgid "Unknown response"
#~ msgstr "Unknown response"

#: src/components/modals/QrCodeModal.tsx:39
#~ msgid "No scans yet!"
#~ msgstr "No scans yet!"

#: src/components/modals/QrCodeModal.tsx:57
#~ msgid "Close modal"
#~ msgstr "Close modal"

#: src/components/modals/ServerInfoModal.tsx:22
msgid "Instance Name"
msgstr "Имя экземпляра"

#: src/components/modals/ServerInfoModal.tsx:28
msgid "Server Version"
msgstr "Версия сервера"

#: src/components/modals/ServerInfoModal.tsx:38
#~ msgid "Bebug Mode"
#~ msgstr "Bebug Mode"

#: src/components/modals/ServerInfoModal.tsx:40
msgid "Database"
msgstr "База данных"

#: src/components/modals/ServerInfoModal.tsx:49
#: src/components/nav/Alerts.tsx:41
msgid "Debug Mode"
msgstr "Режим отладки"

#: src/components/modals/ServerInfoModal.tsx:54
msgid "Server is running in debug mode"
msgstr "Сервер запущен в режиме отладки"

#: src/components/modals/ServerInfoModal.tsx:62
msgid "Docker Mode"
msgstr "Режим Docker"

#: src/components/modals/ServerInfoModal.tsx:65
msgid "Server is deployed using docker"
msgstr "Сервер развернут с помощью docker"

#: src/components/modals/ServerInfoModal.tsx:71
msgid "Plugin Support"
msgstr "Поддержка плагина"

#: src/components/modals/ServerInfoModal.tsx:76
msgid "Plugin support enabled"
msgstr "Поддержка плагинов включена"

#: src/components/modals/ServerInfoModal.tsx:78
msgid "Plugin support disabled"
msgstr "Поддержка плагинов отключена"

#: src/components/modals/ServerInfoModal.tsx:85
msgid "Server status"
msgstr "Состояние сервера"

#: src/components/modals/ServerInfoModal.tsx:91
msgid "Healthy"
msgstr "Исправен"

#: src/components/modals/ServerInfoModal.tsx:93
msgid "Issues detected"
msgstr "Обнаружены проблемы"

#: src/components/modals/ServerInfoModal.tsx:102
#: src/components/nav/Alerts.tsx:50
msgid "Background Worker"
msgstr "Фоновый процесс"

#: src/components/modals/ServerInfoModal.tsx:107
msgid "The background worker process is not running"
msgstr "Фоновый рабочий процесс не запущен"

#: src/components/modals/ServerInfoModal.tsx:107
#~ msgid "The Background worker process is not running."
#~ msgstr "The Background worker process is not running."

#: src/components/modals/ServerInfoModal.tsx:115
#: src/pages/Index/Settings/AdminCenter/Index.tsx:119
msgid "Email Settings"
msgstr "Настройки Email"

#: src/components/modals/ServerInfoModal.tsx:118
#~ msgid "Email settings not configured"
#~ msgstr "Email settings not configured"

#: src/components/modals/ServerInfoModal.tsx:120
#: src/components/nav/Alerts.tsx:61
msgid "Email settings not configured."
msgstr "Электронная почта не настроена."

#: src/components/nav/Alerts.tsx:43
msgid "The server is running in debug mode."
msgstr "Сервер запущен в режиме отладки."

#: src/components/nav/Alerts.tsx:52
msgid "The background worker process is not running."
msgstr "Фоновый рабочий процесс не запущен."

#: src/components/nav/Alerts.tsx:59
msgid "Email settings"
msgstr "Настройка электронной почты"

#: src/components/nav/Alerts.tsx:68
msgid "Server Restart"
msgstr "Перезапуск сервера"

#: src/components/nav/Alerts.tsx:70
msgid "The server requires a restart to apply changes."
msgstr "Для применения изменений необходим перезапуск сервера."

#: src/components/nav/Alerts.tsx:80
msgid "Database Migrations"
msgstr "Миграции базы данных"

#: src/components/nav/Alerts.tsx:82
msgid "There are pending database migrations."
msgstr "Требуется применить миграции базы данных."

#: src/components/nav/Alerts.tsx:98
msgid "Alerts"
msgstr "Предупреждения"

#: src/components/nav/Alerts.tsx:141
msgid "Learn more about {code}"
msgstr "Подробнее о {code}"

#: src/components/nav/Header.tsx:187
#: src/components/nav/NavigationDrawer.tsx:141
#: src/components/nav/NotificationDrawer.tsx:181
#: src/pages/Index/Settings/SystemSettings.tsx:121
#: src/pages/Index/Settings/UserSettings.tsx:106
#: src/pages/Notifications.tsx:45
#: src/pages/Notifications.tsx:130
msgid "Notifications"
msgstr "Уведомления"

#: src/components/nav/Layout.tsx:83
msgid "Nothing found..."
msgstr "Ничего не найдено..."

#: src/components/nav/MainMenu.tsx:40
#: src/pages/Index/Profile/Profile.tsx:15
#~ msgid "Profile"
#~ msgstr "Profile"

#: src/components/nav/MainMenu.tsx:52
#: src/components/nav/NavigationDrawer.tsx:193
#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:21
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:39
msgid "Settings"
msgstr "Настройки"

#: src/components/nav/MainMenu.tsx:59
#: src/pages/Index/Settings/UserSettings.tsx:145
msgid "Account Settings"
msgstr "Настройки учётной записи"

#: src/components/nav/MainMenu.tsx:59
#: src/defaults/menuItems.tsx:15
#~ msgid "Account settings"
#~ msgstr "Account settings"

#: src/components/nav/MainMenu.tsx:67
#: src/components/nav/NavigationDrawer.tsx:153
#: src/components/nav/SettingsHeader.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:347
#: src/pages/Index/Settings/SystemSettings.tsx:352
msgid "System Settings"
msgstr "Системные настройки"

#: src/components/nav/MainMenu.tsx:68
#~ msgid "Current language {locale}"
#~ msgstr "Current language {locale}"

#: src/components/nav/MainMenu.tsx:71
#~ msgid "Switch to pseudo language"
#~ msgstr "Switch to pseudo language"

#: src/components/nav/MainMenu.tsx:76
#: src/components/nav/NavigationDrawer.tsx:160
#: src/components/nav/SettingsHeader.tsx:42
#: src/defaults/actions.tsx:83
#: src/pages/Index/Settings/AdminCenter/Index.tsx:282
#: src/pages/Index/Settings/AdminCenter/Index.tsx:287
msgid "Admin Center"
msgstr "Админ центр"

#: src/components/nav/MainMenu.tsx:96
msgid "Logout"
msgstr "Выход"

#: src/components/nav/NavHoverMenu.tsx:84
#~ msgid "View all"
#~ msgstr "View all"

#: src/components/nav/NavHoverMenu.tsx:100
#: src/components/nav/NavHoverMenu.tsx:110
#~ msgid "Get started"
#~ msgstr "Get started"

#: src/components/nav/NavHoverMenu.tsx:103
#~ msgid "Overview over high-level objects, functions and possible usecases."
#~ msgstr "Overview over high-level objects, functions and possible usecases."

#: src/components/nav/NavigationDrawer.tsx:60
#~ msgid "Pages"
#~ msgstr "Pages"

#: src/components/nav/NavigationDrawer.tsx:84
#: src/components/render/Part.tsx:33
#: src/defaults/links.tsx:42
#: src/forms/StockForms.tsx:735
#: src/pages/Index/Settings/SystemSettings.tsx:224
#: src/pages/part/PartDetail.tsx:804
#: src/pages/stock/LocationDetail.tsx:390
#: src/pages/stock/StockDetail.tsx:626
#: src/tables/stock/StockItemTable.tsx:86
msgid "Stock"
msgstr "Склад"

#: src/components/nav/NavigationDrawer.tsx:91
#: src/defaults/links.tsx:48
#: src/pages/build/BuildDetail.tsx:741
#: src/pages/build/BuildIndex.tsx:102
msgid "Manufacturing"
msgstr "Производство"

#: src/components/nav/NavigationDrawer.tsx:98
#: src/defaults/links.tsx:54
#: src/pages/company/ManufacturerDetail.tsx:9
#: src/pages/company/ManufacturerPartDetail.tsx:260
#: src/pages/company/SupplierDetail.tsx:9
#: src/pages/company/SupplierPartDetail.tsx:356
#: src/pages/purchasing/PurchaseOrderDetail.tsx:534
#: src/pages/purchasing/PurchasingIndex.tsx:122
msgid "Purchasing"
msgstr "Закупки"

#: src/components/nav/NavigationDrawer.tsx:105
#: src/defaults/links.tsx:60
#: src/pages/company/CustomerDetail.tsx:9
#: src/pages/sales/ReturnOrderDetail.tsx:521
#: src/pages/sales/SalesIndex.tsx:139
#: src/pages/sales/SalesOrderDetail.tsx:591
#: src/pages/sales/SalesOrderShipmentDetail.tsx:360
msgid "Sales"
msgstr "Продажи"

#: src/components/nav/NavigationDrawer.tsx:147
#: src/components/nav/SettingsHeader.tsx:40
#: src/pages/Index/Settings/UserSettings.tsx:141
msgid "User Settings"
msgstr "Пользовательские настройки"

#: src/components/nav/NavigationDrawer.tsx:188
msgid "Navigation"
msgstr "Панель навигации"

#: src/components/nav/NavigationDrawer.tsx:223
msgid "About"
msgstr "О проекте"

#: src/components/nav/NavigationTree.tsx:211
msgid "Error loading navigation tree."
msgstr "Ошибка загрузки дерева навигации."

#: src/components/nav/NotificationDrawer.tsx:183
#: src/pages/Notifications.tsx:74
msgid "Mark all as read"
msgstr "Пометить как прочитанное"

#: src/components/nav/NotificationDrawer.tsx:193
msgid "View all notifications"
msgstr "Просмотреть все уведомления"

#: src/components/nav/NotificationDrawer.tsx:216
msgid "You have no unread notifications."
msgstr "У вас нет непрочитанных уведомлений."

#: src/components/nav/NotificationDrawer.tsx:238
msgid "Error loading notifications."
msgstr "Ошибка загрузки уведомлений."

#: src/components/nav/SearchDrawer.tsx:106
msgid "No Overview Available"
msgstr "Обзор недоступен"

#: src/components/nav/SearchDrawer.tsx:107
msgid "No overview available for this model type"
msgstr "Для данного типа модели обзор недоступен"

#: src/components/nav/SearchDrawer.tsx:125
msgid "View all results"
msgstr "Показать все результаты"

#: src/components/nav/SearchDrawer.tsx:140
msgid "results"
msgstr "результаты"

#: src/components/nav/SearchDrawer.tsx:144
msgid "Remove search group"
msgstr "Удалить группу из поиска"

#: src/components/nav/SearchDrawer.tsx:288
#: src/pages/company/ManufacturerPartDetail.tsx:176
#: src/pages/part/PartDetail.tsx:861
#: src/pages/part/PartSupplierDetail.tsx:15
#: src/pages/purchasing/PurchasingIndex.tsx:81
msgid "Suppliers"
msgstr "Поставщики"

#: src/components/nav/SearchDrawer.tsx:298
#: src/pages/part/PartSupplierDetail.tsx:23
#: src/pages/purchasing/PurchasingIndex.tsx:98
msgid "Manufacturers"
msgstr "Производители"

#: src/components/nav/SearchDrawer.tsx:308
#: src/pages/sales/SalesIndex.tsx:124
msgid "Customers"
msgstr "Покупатели"

#: src/components/nav/SearchDrawer.tsx:462
#~ msgid "No results"
#~ msgstr "No results"

#: src/components/nav/SearchDrawer.tsx:477
msgid "Enter search text"
msgstr "Введите слова для поиска"

#: src/components/nav/SearchDrawer.tsx:488
msgid "Refresh search results"
msgstr "Обновить результаты поиска"

#: src/components/nav/SearchDrawer.tsx:499
#: src/components/nav/SearchDrawer.tsx:506
msgid "Search Options"
msgstr "Параметры поиска"

#: src/components/nav/SearchDrawer.tsx:509
msgid "Whole word search"
msgstr "Поиск полного слова"

#: src/components/nav/SearchDrawer.tsx:518
msgid "Regex search"
msgstr "Поиск по выражению"

#: src/components/nav/SearchDrawer.tsx:527
msgid "Notes search"
msgstr "Поиск по заметкам"

#: src/components/nav/SearchDrawer.tsx:575
msgid "An error occurred during search query"
msgstr "Произошла ошибка во время поиска запроса"

#: src/components/nav/SearchDrawer.tsx:586
#: src/tables/part/PartTestTemplateTable.tsx:82
msgid "No Results"
msgstr "Нет результатов"

#: src/components/nav/SearchDrawer.tsx:589
msgid "No results available for search query"
msgstr "Нет доступных результатов для поискового запроса"

#: src/components/panels/AttachmentPanel.tsx:18
msgid "Attachments"
msgstr "Вложения"

#: src/components/panels/NotesPanel.tsx:23
#: src/tables/build/BuildOrderTestTable.tsx:196
#: src/tables/stock/StockTrackingTable.tsx:212
msgid "Notes"
msgstr "Заметки"

#: src/components/panels/PanelGroup.tsx:158
msgid "Plugin Provided"
msgstr "Плагин предоставлен"

#: src/components/panels/PanelGroup.tsx:275
msgid "Collapse panels"
msgstr "Свернуть панели"

#: src/components/panels/PanelGroup.tsx:275
msgid "Expand panels"
msgstr "Развернуть панели"

#: src/components/plugins/LocateItemButton.tsx:68
#: src/components/plugins/LocateItemButton.tsx:88
msgid "Locate Item"
msgstr "Определение расположения"

#: src/components/plugins/LocateItemButton.tsx:70
msgid "Item location requested"
msgstr "Запрошено определение расположения"

#: src/components/plugins/PluginDrawer.tsx:47
msgid "Plugin Inactive"
msgstr "Плагин неактивен"

#: src/components/plugins/PluginDrawer.tsx:50
msgid "Plugin is not active"
msgstr "Плагин неактивен"

#: src/components/plugins/PluginDrawer.tsx:59
msgid "Plugin Information"
msgstr "Информация о плагине"

#: src/components/plugins/PluginDrawer.tsx:73
#: src/forms/selectionListFields.tsx:104
#: src/pages/build/BuildDetail.tsx:244
#: src/pages/company/CompanyDetail.tsx:93
#: src/pages/company/ManufacturerPartDetail.tsx:91
#: src/pages/company/ManufacturerPartDetail.tsx:118
#: src/pages/company/SupplierPartDetail.tsx:144
#: src/pages/part/CategoryDetail.tsx:106
#: src/pages/part/PartDetail.tsx:461
#: src/pages/purchasing/PurchaseOrderDetail.tsx:144
#: src/pages/sales/ReturnOrderDetail.tsx:109
#: src/pages/sales/SalesOrderDetail.tsx:118
#: src/pages/stock/LocationDetail.tsx:104
#: src/tables/ColumnRenderers.tsx:269
#: src/tables/build/BuildAllocatedStockTable.tsx:91
#: src/tables/machine/MachineTypeTable.tsx:128
#: src/tables/machine/MachineTypeTable.tsx:239
#: src/tables/plugin/PluginListTable.tsx:110
msgid "Description"
msgstr "Описание"

#: src/components/plugins/PluginDrawer.tsx:78
msgid "Author"
msgstr "Автор"

#: src/components/plugins/PluginDrawer.tsx:83
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:41
#: src/pages/part/pricing/SaleHistoryPanel.tsx:38
#: src/tables/ColumnRenderers.tsx:411
#: src/tables/build/BuildOrderTestTable.tsx:204
msgid "Date"
msgstr "Дата"

#: src/components/plugins/PluginDrawer.tsx:93
#: src/forms/selectionListFields.tsx:105
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:68
#: src/pages/core/UserDetail.tsx:81
#: src/pages/core/UserDetail.tsx:209
#: src/pages/part/PartDetail.tsx:615
#: src/tables/bom/UsedInTable.tsx:90
#: src/tables/company/CompanyTable.tsx:57
#: src/tables/company/CompanyTable.tsx:91
#: src/tables/machine/MachineListTable.tsx:336
#: src/tables/machine/MachineListTable.tsx:606
#: src/tables/part/ParametricPartTable.tsx:350
#: src/tables/part/PartTable.tsx:184
#: src/tables/part/PartVariantTable.tsx:15
#: src/tables/plugin/PluginListTable.tsx:96
#: src/tables/plugin/PluginListTable.tsx:412
#: src/tables/purchasing/SupplierPartTable.tsx:85
#: src/tables/purchasing/SupplierPartTable.tsx:179
#: src/tables/settings/ApiTokenTable.tsx:63
#: src/tables/settings/UserTable.tsx:410
#: src/tables/stock/StockItemTable.tsx:323
msgid "Active"
msgstr "Активно"

#: src/components/plugins/PluginDrawer.tsx:105
msgid "Package Name"
msgstr "Название пакета"

#: src/components/plugins/PluginDrawer.tsx:111
msgid "Installation Path"
msgstr "Путь установки"

#: src/components/plugins/PluginDrawer.tsx:116
#: src/tables/machine/MachineTypeTable.tsx:151
#: src/tables/machine/MachineTypeTable.tsx:275
#: src/tables/plugin/PluginListTable.tsx:101
#: src/tables/plugin/PluginListTable.tsx:417
msgid "Builtin"
msgstr "Встроенный"

#: src/components/plugins/PluginDrawer.tsx:121
msgid "Package"
msgstr "Пакет"

#: src/components/plugins/PluginDrawer.tsx:133
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:55
#: src/pages/Index/Settings/SystemSettings.tsx:330
#: src/pages/Index/Settings/UserSettings.tsx:128
msgid "Plugin Settings"
msgstr "Настройки плагинов"

#: src/components/plugins/PluginPanel.tsx:87
#~ msgid "Error occurred while rendering plugin content"
#~ msgstr "Error occurred while rendering plugin content"

#: src/components/plugins/PluginPanel.tsx:91
#~ msgid "Plugin did not provide panel rendering function"
#~ msgstr "Plugin did not provide panel rendering function"

#: src/components/plugins/PluginPanel.tsx:103
#~ msgid "No content provided for this plugin"
#~ msgstr "No content provided for this plugin"

#: src/components/plugins/PluginPanel.tsx:116
#: src/components/plugins/PluginSettingsPanel.tsx:76
#~ msgid "Error Loading Plugin"
#~ msgstr "Error Loading Plugin"

#: src/components/plugins/PluginSettingsPanel.tsx:51
#~ msgid "Error occurred while rendering plugin settings"
#~ msgstr "Error occurred while rendering plugin settings"

#: src/components/plugins/PluginSettingsPanel.tsx:55
#~ msgid "Plugin did not provide settings rendering function"
#~ msgstr "Plugin did not provide settings rendering function"

#: src/components/plugins/PluginUIFeature.tsx:102
msgid "Error occurred while rendering the template editor."
msgstr "Произошла ошибка при отрисовке редактора шаблонов."

#: src/components/plugins/PluginUIFeature.tsx:119
msgid "Error Loading Plugin Editor"
msgstr "Ошибка загрузки редактора плагинов"

#: src/components/plugins/PluginUIFeature.tsx:155
msgid "Error occurred while rendering the template preview."
msgstr "Произошла ошибка при отрисовке предпросмотра шаблона."

#: src/components/plugins/PluginUIFeature.tsx:166
msgid "Error Loading Plugin Preview"
msgstr "Ошибка загрузки предпросмотра плагина"

#: src/components/plugins/RemoteComponent.tsx:111
msgid "Invalid source or function name"
msgstr "Неверный источник или имя функции"

#: src/components/plugins/RemoteComponent.tsx:143
msgid "Error Loading Content"
msgstr "Ошибка при загрузке содержимого"

#: src/components/plugins/RemoteComponent.tsx:147
msgid "Error occurred while loading plugin content"
msgstr "Произошла ошибка при загрузке содержимого плагина"

#: src/components/render/Instance.tsx:238
#~ msgid "Unknown model: {model}"
#~ msgstr "Unknown model: {model}"

#: src/components/render/Instance.tsx:246
msgid "Unknown model: {model_name}"
msgstr "Неизвестная модель: {model_name}"

#: src/components/render/ModelType.tsx:234
#~ msgid "Purchase Order Line Item"
#~ msgstr "Purchase Order Line Item"

#: src/components/render/ModelType.tsx:264
#~ msgid "Unknown Model"
#~ msgstr "Unknown Model"

#: src/components/render/ModelType.tsx:307
#~ msgid "Purchase Order Line Items"
#~ msgstr "Purchase Order Line Items"

#: src/components/render/ModelType.tsx:337
#~ msgid "Unknown Models"
#~ msgstr "Unknown Models"

#: src/components/render/Order.tsx:122
#: src/tables/sales/SalesOrderAllocationTable.tsx:170
msgid "Shipment"
msgstr "Отгрузка"

#: src/components/render/Part.tsx:28
#: src/components/render/Plugin.tsx:17
#: src/components/render/User.tsx:37
#: src/pages/company/CompanyDetail.tsx:325
#: src/pages/company/SupplierPartDetail.tsx:369
#: src/pages/core/UserDetail.tsx:211
#: src/pages/part/PartDetail.tsx:1012
msgid "Inactive"
msgstr "Неактивный"

#: src/components/render/Part.tsx:31
#: src/tables/bom/BomTable.tsx:289
#: src/tables/part/PartTable.tsx:139
msgid "No stock"
msgstr "Нет склада"

#: src/components/render/Part.tsx:74
#: src/pages/part/PartDetail.tsx:488
#: src/tables/ColumnRenderers.tsx:224
#: src/tables/ColumnRenderers.tsx:233
#: src/tables/notifications/NotificationTable.tsx:32
#: src/tables/part/PartCategoryTemplateTable.tsx:71
msgid "Category"
msgstr "Категория"

#: src/components/render/Stock.tsx:36
#: src/components/render/Stock.tsx:107
#: src/components/render/Stock.tsx:125
#: src/forms/BuildForms.tsx:759
#: src/forms/PurchaseOrderForms.tsx:592
#: src/forms/StockForms.tsx:733
#: src/forms/StockForms.tsx:779
#: src/forms/StockForms.tsx:825
#: src/forms/StockForms.tsx:864
#: src/forms/StockForms.tsx:900
#: src/forms/StockForms.tsx:938
#: src/forms/StockForms.tsx:980
#: src/forms/StockForms.tsx:1028
#: src/forms/StockForms.tsx:1072
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:88
#: src/pages/core/UserDetail.tsx:158
#: src/pages/stock/StockDetail.tsx:295
#: src/tables/ColumnRenderers.tsx:176
#: src/tables/ColumnRenderers.tsx:185
#: src/tables/Filter.tsx:392
#: src/tables/stock/StockTrackingTable.tsx:98
msgid "Location"
msgstr "Расположение"

#: src/components/render/Stock.tsx:92
#: src/pages/stock/StockDetail.tsx:195
#: src/pages/stock/StockDetail.tsx:901
#: src/tables/build/BuildAllocatedStockTable.tsx:121
#: src/tables/build/BuildOutputTable.tsx:111
#: src/tables/sales/SalesOrderAllocationTable.tsx:139
msgid "Serial Number"
msgstr "Серийный номер"

#: src/components/render/Stock.tsx:97
#: src/components/wizards/OrderPartsWizard.tsx:222
#: src/forms/BuildForms.tsx:243
#: src/forms/BuildForms.tsx:605
#: src/forms/BuildForms.tsx:761
#: src/forms/PurchaseOrderForms.tsx:794
#: src/forms/ReturnOrderForms.tsx:240
#: src/forms/SalesOrderForms.tsx:270
#: src/forms/StockForms.tsx:781
#: src/pages/part/PartStockHistoryDetail.tsx:56
#: src/pages/part/PartStockHistoryDetail.tsx:210
#: src/pages/part/PartStockHistoryDetail.tsx:234
#: src/pages/part/pricing/BomPricingPanel.tsx:146
#: src/pages/part/pricing/PriceBreakPanel.tsx:89
#: src/pages/part/pricing/PriceBreakPanel.tsx:172
#: src/pages/stock/StockDetail.tsx:255
#: src/pages/stock/StockDetail.tsx:907
#: src/tables/build/BuildLineTable.tsx:84
#: src/tables/build/BuildOrderTestTable.tsx:251
#: src/tables/part/PartPurchaseOrdersTable.tsx:94
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:168
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:199
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:69
#: src/tables/sales/ReturnOrderLineItemTable.tsx:121
#: src/tables/stock/StockTrackingTable.tsx:72
msgid "Quantity"
msgstr "Количество"

#: src/components/render/Stock.tsx:110
#: src/forms/BuildForms.tsx:312
#: src/forms/BuildForms.tsx:386
#: src/forms/BuildForms.tsx:450
#: src/forms/StockForms.tsx:734
#: src/forms/StockForms.tsx:780
#: src/forms/StockForms.tsx:826
#: src/forms/StockForms.tsx:865
#: src/forms/StockForms.tsx:901
#: src/forms/StockForms.tsx:939
#: src/forms/StockForms.tsx:981
#: src/forms/StockForms.tsx:1029
#: src/forms/StockForms.tsx:1073
#: src/tables/build/BuildLineTable.tsx:94
msgid "Batch"
msgstr "Партия"

#: src/components/settings/ConfigValueList.tsx:33
#~ msgid "<0>{0}</0> is set via {1} and was last set {2}"
#~ msgstr "<0>{0}</0> is set via {1} and was last set {2}"

#: src/components/settings/ConfigValueList.tsx:36
msgid "Setting"
msgstr ""

#: src/components/settings/ConfigValueList.tsx:39
msgid "Source"
msgstr ""

#: src/components/settings/SettingItem.tsx:47
#: src/components/settings/SettingItem.tsx:100
#~ msgid "{0} updated successfully"
#~ msgstr "{0} updated successfully"

#: src/components/settings/SettingList.tsx:78
msgid "Edit Setting"
msgstr "Редактирование настроек"

#: src/components/settings/SettingList.tsx:91
msgid "Setting {key} updated successfully"
msgstr "Значение {key} успешно обновлено"

#: src/components/settings/SettingList.tsx:120
msgid "Setting updated"
msgstr "Настройки обновлены"

#. placeholder {0}: setting.key
#: src/components/settings/SettingList.tsx:121
msgid "Setting {0} updated successfully"
msgstr "Настройки {0} успешно обновлены"

#: src/components/settings/SettingList.tsx:130
msgid "Error editing setting"
msgstr "Ошибка при редактировании настроек"

#: src/components/settings/SettingList.tsx:146
msgid "Error loading settings"
msgstr "Ошибка загрузки настроек"

#: src/components/settings/SettingList.tsx:187
msgid "No settings specified"
msgstr "Настройки не указаны"

#: src/components/tables/FilterGroup.tsx:29
#~ msgid "Add table filter"
#~ msgstr "Add table filter"

#: src/components/tables/FilterGroup.tsx:44
#~ msgid "Clear all filters"
#~ msgstr "Clear all filters"

#: src/components/tables/FilterGroup.tsx:51
#~ msgid "Add filter"
#~ msgstr "Add filter"

#: src/components/tables/FilterSelectModal.tsx:143
#~ msgid "Add Table Filter"
#~ msgstr "Add Table Filter"

#: src/components/tables/FilterSelectModal.tsx:145
#~ msgid "Select from the available filters"
#~ msgstr "Select from the available filters"

#: src/components/tables/bom/BomTable.tsx:200
#~ msgid "Validate"
#~ msgstr "Validate"

#: src/components/tables/bom/BomTable.tsx:250
#~ msgid "Has Available Stock"
#~ msgstr "Has Available Stock"

#: src/components/tables/bom/UsedInTable.tsx:40
#~ msgid "Required Part"
#~ msgstr "Required Part"

#: src/components/tables/build/BuildOrderTable.tsx:52
#~ msgid "Progress"
#~ msgstr "Progress"

#: src/components/tables/build/BuildOrderTable.tsx:65
#~ msgid "Priority"
#~ msgstr "Priority"

#: src/components/tables/company/AddressTable.tsx:68
#~ msgid "Postal Code"
#~ msgstr "Postal Code"

#: src/components/tables/company/AddressTable.tsx:74
#~ msgid "City"
#~ msgstr "City"

#: src/components/tables/company/AddressTable.tsx:80
#~ msgid "State / Province"
#~ msgstr "State / Province"

#: src/components/tables/company/AddressTable.tsx:86
#~ msgid "Country"
#~ msgstr "Country"

#: src/components/tables/company/AddressTable.tsx:92
#~ msgid "Courier Notes"
#~ msgstr "Courier Notes"

#: src/components/tables/company/AddressTable.tsx:98
#~ msgid "Internal Notes"
#~ msgstr "Internal Notes"

#: src/components/tables/company/AddressTable.tsx:130
#~ msgid "Address updated"
#~ msgstr "Address updated"

#: src/components/tables/company/AddressTable.tsx:142
#~ msgid "Address deleted"
#~ msgstr "Address deleted"

#: src/components/tables/company/CompanyTable.tsx:32
#~ msgid "Company Name"
#~ msgstr "Company Name"

#: src/components/tables/company/ContactTable.tsx:41
#~ msgid "Phone"
#~ msgstr "Phone"

#: src/components/tables/company/ContactTable.tsx:78
#~ msgid "Contact updated"
#~ msgstr "Contact updated"

#: src/components/tables/company/ContactTable.tsx:90
#~ msgid "Contact deleted"
#~ msgstr "Contact deleted"

#: src/components/tables/company/ContactTable.tsx:92
#~ msgid "Are you sure you want to delete this contact?"
#~ msgstr "Are you sure you want to delete this contact?"

#: src/components/tables/company/ContactTable.tsx:108
#~ msgid "Create Contact"
#~ msgstr "Create Contact"

#: src/components/tables/company/ContactTable.tsx:110
#~ msgid "Contact created"
#~ msgstr "Contact created"

#: src/components/tables/general/AttachmentTable.tsx:47
#~ msgid "Comment"
#~ msgstr "Comment"

#: src/components/tables/part/PartCategoryTable.tsx:122
#~ msgid "Part category updated"
#~ msgstr "Part category updated"

#: src/components/tables/part/PartParameterTable.tsx:41
#~ msgid "Parameter"
#~ msgstr "Parameter"

#: src/components/tables/part/PartParameterTable.tsx:114
#~ msgid "Part parameter updated"
#~ msgstr "Part parameter updated"

#: src/components/tables/part/PartParameterTable.tsx:130
#~ msgid "Part parameter deleted"
#~ msgstr "Part parameter deleted"

#: src/components/tables/part/PartParameterTable.tsx:132
#~ msgid "Are you sure you want to remove this parameter?"
#~ msgstr "Are you sure you want to remove this parameter?"

#: src/components/tables/part/PartParameterTable.tsx:159
#~ msgid "Part parameter added"
#~ msgstr "Part parameter added"

#: src/components/tables/part/PartParameterTemplateTable.tsx:67
#~ msgid "Choices"
#~ msgstr "Choices"

#: src/components/tables/part/PartParameterTemplateTable.tsx:83
#~ msgid "Remove parameter template"
#~ msgstr "Remove parameter template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:84
#~ msgid "Parameter template updated"
#~ msgstr "Parameter template updated"

#: src/components/tables/part/PartParameterTemplateTable.tsx:96
#~ msgid "Parameter template deleted"
#~ msgstr "Parameter template deleted"

#: src/components/tables/part/PartParameterTemplateTable.tsx:98
#~ msgid "Are you sure you want to remove this parameter template?"
#~ msgstr "Are you sure you want to remove this parameter template?"

#: src/components/tables/part/PartParameterTemplateTable.tsx:110
#~ msgid "Create Parameter Template"
#~ msgstr "Create Parameter Template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:112
#~ msgid "Parameter template created"
#~ msgstr "Parameter template created"

#: src/components/tables/part/PartTable.tsx:211
#~ msgid "Detail"
#~ msgstr "Detail"

#: src/components/tables/part/PartTestTemplateTable.tsx:30
#~ msgid "Test Name"
#~ msgstr "Test Name"

#: src/components/tables/part/PartTestTemplateTable.tsx:86
#~ msgid "Template updated"
#~ msgstr "Template updated"

#: src/components/tables/part/PartTestTemplateTable.tsx:98
#~ msgid "Test Template deleted"
#~ msgstr "Test Template deleted"

#: src/components/tables/part/PartTestTemplateTable.tsx:115
#~ msgid "Create Test Template"
#~ msgstr "Create Test Template"

#: src/components/tables/part/PartTestTemplateTable.tsx:117
#~ msgid "Template created"
#~ msgstr "Template created"

#: src/components/tables/part/RelatedPartTable.tsx:79
#~ msgid "Related Part"
#~ msgstr "Related Part"

#: src/components/tables/part/RelatedPartTable.tsx:82
#~ msgid "Related part added"
#~ msgstr "Related part added"

#: src/components/tables/part/RelatedPartTable.tsx:114
#~ msgid "Related part deleted"
#~ msgstr "Related part deleted"

#: src/components/tables/part/RelatedPartTable.tsx:115
#~ msgid "Are you sure you want to remove this relationship?"
#~ msgstr "Are you sure you want to remove this relationship?"

#: src/components/tables/plugin/PluginListTable.tsx:191
#~ msgid "Installation path"
#~ msgstr "Installation path"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:55
#~ msgid "Receive"
#~ msgstr "Receive"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:81
#~ msgid "Line item updated"
#~ msgstr "Line item updated"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:232
#~ msgid "Line item added"
#~ msgstr "Line item added"

#: src/components/tables/settings/CustomUnitsTable.tsx:37
#~ msgid "Definition"
#~ msgstr "Definition"

#: src/components/tables/settings/CustomUnitsTable.tsx:43
#~ msgid "Symbol"
#~ msgstr "Symbol"

#: src/components/tables/settings/CustomUnitsTable.tsx:59
#~ msgid "Edit custom unit"
#~ msgstr "Edit custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:66
#~ msgid "Custom unit updated"
#~ msgstr "Custom unit updated"

#: src/components/tables/settings/CustomUnitsTable.tsx:76
#~ msgid "Delete custom unit"
#~ msgstr "Delete custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:77
#~ msgid "Custom unit deleted"
#~ msgstr "Custom unit deleted"

#: src/components/tables/settings/CustomUnitsTable.tsx:79
#~ msgid "Are you sure you want to remove this custom unit?"
#~ msgstr "Are you sure you want to remove this custom unit?"

#: src/components/tables/settings/CustomUnitsTable.tsx:97
#~ msgid "Custom unit created"
#~ msgstr "Custom unit created"

#: src/components/tables/settings/GroupTable.tsx:45
#~ msgid "Group updated"
#~ msgstr "Group updated"

#: src/components/tables/settings/GroupTable.tsx:131
#~ msgid "Added group"
#~ msgstr "Added group"

#: src/components/tables/settings/ProjectCodeTable.tsx:49
#~ msgid "Edit project code"
#~ msgstr "Edit project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:56
#~ msgid "Project code updated"
#~ msgstr "Project code updated"

#: src/components/tables/settings/ProjectCodeTable.tsx:66
#~ msgid "Delete project code"
#~ msgstr "Delete project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:67
#~ msgid "Project code deleted"
#~ msgstr "Project code deleted"

#: src/components/tables/settings/ProjectCodeTable.tsx:69
#~ msgid "Are you sure you want to remove this project code?"
#~ msgstr "Are you sure you want to remove this project code?"

#: src/components/tables/settings/ProjectCodeTable.tsx:88
#~ msgid "Added project code"
#~ msgstr "Added project code"

#: src/components/tables/settings/UserDrawer.tsx:92
#~ msgid "User permission changed successfully"
#~ msgstr "User permission changed successfully"

#: src/components/tables/settings/UserDrawer.tsx:93
#~ msgid "Some changes might only take effect after the user refreshes their login."
#~ msgstr "Some changes might only take effect after the user refreshes their login."

#: src/components/tables/settings/UserDrawer.tsx:118
#~ msgid "Changed user active status successfully"
#~ msgstr "Changed user active status successfully"

#: src/components/tables/settings/UserDrawer.tsx:119
#~ msgid "Set to {active}"
#~ msgstr "Set to {active}"

#: src/components/tables/settings/UserDrawer.tsx:142
#~ msgid "User details for {0}"
#~ msgstr "User details for {0}"

#: src/components/tables/settings/UserDrawer.tsx:176
#~ msgid "Rights"
#~ msgstr "Rights"

#: src/components/tables/settings/UserTable.tsx:117
#~ msgid "user deleted"
#~ msgstr "user deleted"

#: src/components/tables/stock/StockItemTable.tsx:247
#~ msgid "Test Filter"
#~ msgstr "Test Filter"

#: src/components/tables/stock/StockItemTable.tsx:248
#~ msgid "This is a test filter"
#~ msgstr "This is a test filter"

#: src/components/tables/stock/StockLocationTable.tsx:145
#~ msgid "Stock location updated"
#~ msgstr "Stock location updated"

#: src/components/widgets/FeedbackWidget.tsx:19
#~ msgid "Something is new: Platform UI"
#~ msgstr "Something is new: Platform UI"

#: src/components/widgets/FeedbackWidget.tsx:21
#~ msgid "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."
#~ msgstr "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."

#: src/components/widgets/FeedbackWidget.tsx:32
#~ msgid "Provide Feedback"
#~ msgstr "Provide Feedback"

#: src/components/widgets/GetStartedWidget.tsx:11
#~ msgid "Getting started"
#~ msgstr "Getting started"

#: src/components/widgets/MarkdownEditor.tsx:108
#~ msgid "Failed to upload image"
#~ msgstr "Failed to upload image"

#: src/components/widgets/MarkdownEditor.tsx:146
#~ msgid "Notes saved"
#~ msgstr "Notes saved"

#: src/components/widgets/WidgetLayout.tsx:166
#~ msgid "Layout"
#~ msgstr "Layout"

#: src/components/widgets/WidgetLayout.tsx:172
#~ msgid "Reset Layout"
#~ msgstr "Reset Layout"

#: src/components/widgets/WidgetLayout.tsx:185
#~ msgid "Stop Edit"
#~ msgstr "Stop Edit"

#: src/components/widgets/WidgetLayout.tsx:191
#~ msgid "Appearance"
#~ msgstr "Appearance"

#: src/components/widgets/WidgetLayout.tsx:203
#~ msgid "Show Boxes"
#~ msgstr "Show Boxes"

#: src/components/wizards/OrderPartsWizard.tsx:61
msgid "New Purchase Order"
msgstr "Создать заказ на закупку"

#: src/components/wizards/OrderPartsWizard.tsx:63
msgid "Purchase order created"
msgstr "Заказ на закупку создан"

#: src/components/wizards/OrderPartsWizard.tsx:75
msgid "New Supplier Part"
msgstr "Создать деталь поставщика"

#: src/components/wizards/OrderPartsWizard.tsx:77
#: src/tables/purchasing/SupplierPartTable.tsx:161
msgid "Supplier part created"
msgstr "Деталь поставщика создана"

#: src/components/wizards/OrderPartsWizard.tsx:103
msgid "Add to Purchase Order"
msgstr "Добавить в заказ на закупку"

#: src/components/wizards/OrderPartsWizard.tsx:115
msgid "Part added to purchase order"
msgstr "Детали добавлены в заказ на закупку"

#: src/components/wizards/OrderPartsWizard.tsx:156
msgid "Select supplier part"
msgstr "Выберите деталь поставщика"

#: src/components/wizards/OrderPartsWizard.tsx:171
msgid "New supplier part"
msgstr "Создать деталь поставщика"

#: src/components/wizards/OrderPartsWizard.tsx:195
msgid "Select purchase order"
msgstr "Выберите заказ на закупку"

#: src/components/wizards/OrderPartsWizard.tsx:209
msgid "New purchase order"
msgstr "Создать заказ на закупку"

#: src/components/wizards/OrderPartsWizard.tsx:257
msgid "Add to selected purchase order"
msgstr "Добавить в выбранный заказ на закупку"

#: src/components/wizards/OrderPartsWizard.tsx:269
#: src/components/wizards/OrderPartsWizard.tsx:382
msgid "No parts selected"
msgstr "Не выбраны детали"

#: src/components/wizards/OrderPartsWizard.tsx:270
msgid "No purchaseable parts selected"
msgstr "Не выбраны детали, которые можно закупить"

#: src/components/wizards/OrderPartsWizard.tsx:306
msgid "Parts Added"
msgstr "Детали добавлены"

#: src/components/wizards/OrderPartsWizard.tsx:307
msgid "All selected parts added to a purchase order"
msgstr "Все выбранные детали добавлены в заказ на закупку"

#: src/components/wizards/OrderPartsWizard.tsx:383
msgid "You must select at least one part to order"
msgstr "Необходимо выбрать хотя бы одну деталь для закупки"

#: src/components/wizards/OrderPartsWizard.tsx:394
msgid "Supplier part is required"
msgstr "Необходимо указать деталь поставщика"

#: src/components/wizards/OrderPartsWizard.tsx:398
msgid "Quantity is required"
msgstr "Необходимо указать количество"

#: src/components/wizards/OrderPartsWizard.tsx:411
msgid "Invalid part selection"
msgstr "Неправильный выбор деталей"

#: src/components/wizards/OrderPartsWizard.tsx:413
msgid "Please correct the errors in the selected parts"
msgstr "Исправьте ошибки в выбранных деталях"

#: src/components/wizards/OrderPartsWizard.tsx:424
#: src/tables/build/BuildLineTable.tsx:794
#: src/tables/part/PartTable.tsx:407
#: src/tables/sales/SalesOrderLineItemTable.tsx:347
msgid "Order Parts"
msgstr "Закупить детали"

#: src/contexts/LanguageContext.tsx:22
msgid "Arabic"
msgstr "Arabic"

#: src/contexts/LanguageContext.tsx:23
msgid "Bulgarian"
msgstr "Bulgarian"

#: src/contexts/LanguageContext.tsx:24
msgid "Czech"
msgstr "Czech"

#: src/contexts/LanguageContext.tsx:25
msgid "Danish"
msgstr "Danish"

#: src/contexts/LanguageContext.tsx:26
msgid "German"
msgstr "German"

#: src/contexts/LanguageContext.tsx:27
msgid "Greek"
msgstr "Greek"

#: src/contexts/LanguageContext.tsx:28
msgid "English"
msgstr "English"

#: src/contexts/LanguageContext.tsx:29
msgid "Spanish"
msgstr "Spanish"

#: src/contexts/LanguageContext.tsx:30
msgid "Spanish (Mexican)"
msgstr "Spanish (Mexican)"

#: src/contexts/LanguageContext.tsx:31
msgid "Estonian"
msgstr "Estonian"

#: src/contexts/LanguageContext.tsx:32
msgid "Farsi / Persian"
msgstr "Farsi / Persian"

#: src/contexts/LanguageContext.tsx:33
msgid "Finnish"
msgstr "Finnish"

#: src/contexts/LanguageContext.tsx:34
msgid "French"
msgstr "French"

#: src/contexts/LanguageContext.tsx:35
msgid "Hebrew"
msgstr "Hebrew"

#: src/contexts/LanguageContext.tsx:36
msgid "Hindi"
msgstr "Hindi"

#: src/contexts/LanguageContext.tsx:37
msgid "Hungarian"
msgstr "Hungarian"

#: src/contexts/LanguageContext.tsx:38
msgid "Italian"
msgstr "Italian"

#: src/contexts/LanguageContext.tsx:39
msgid "Japanese"
msgstr "Japanese"

#: src/contexts/LanguageContext.tsx:40
msgid "Korean"
msgstr "Korean"

#: src/contexts/LanguageContext.tsx:41
msgid "Lithuanian"
msgstr "Lithuanian"

#: src/contexts/LanguageContext.tsx:42
msgid "Latvian"
msgstr "Latvian"

#: src/contexts/LanguageContext.tsx:43
msgid "Dutch"
msgstr "Dutch"

#: src/contexts/LanguageContext.tsx:44
msgid "Norwegian"
msgstr "Norwegian"

#: src/contexts/LanguageContext.tsx:45
msgid "Polish"
msgstr "Polish"

#: src/contexts/LanguageContext.tsx:46
msgid "Portuguese"
msgstr "Portuguese"

#: src/contexts/LanguageContext.tsx:47
msgid "Portuguese (Brazilian)"
msgstr "Portuguese (Brazilian)"

#: src/contexts/LanguageContext.tsx:48
msgid "Romanian"
msgstr "Romanian"

#: src/contexts/LanguageContext.tsx:49
msgid "Russian"
msgstr "Russian"

#: src/contexts/LanguageContext.tsx:50
msgid "Slovak"
msgstr "Slovak"

#: src/contexts/LanguageContext.tsx:51
msgid "Slovenian"
msgstr "Slovenian"

#: src/contexts/LanguageContext.tsx:52
msgid "Serbian"
msgstr "Serbian"

#: src/contexts/LanguageContext.tsx:53
msgid "Swedish"
msgstr "Swedish"

#: src/contexts/LanguageContext.tsx:54
msgid "Thai"
msgstr "Thai"

#: src/contexts/LanguageContext.tsx:55
msgid "Turkish"
msgstr "Turkish"

#: src/contexts/LanguageContext.tsx:56
msgid "Ukrainian"
msgstr "Ukrainian"

#: src/contexts/LanguageContext.tsx:57
msgid "Vietnamese"
msgstr "Vietnamese"

#: src/contexts/LanguageContext.tsx:58
msgid "Chinese (Simplified)"
msgstr "Chinese (Simplified)"

#: src/contexts/LanguageContext.tsx:59
msgid "Chinese (Traditional)"
msgstr "Chinese (Traditional)"

#: src/defaults/actions.tsx:18
#: src/defaults/links.tsx:27
#: src/defaults/menuItems.tsx:9
#~ msgid "Home"
#~ msgstr "Home"

#: src/defaults/actions.tsx:29
msgid "Go to the InvenTree dashboard"
msgstr "Перейти к панели InvenTree"

#: src/defaults/actions.tsx:36
msgid "Visit the documentation to learn more about InvenTree"
msgstr "Посетите документацию, чтобы узнать больше о InvenTree"

#: src/defaults/actions.tsx:41
#: src/defaults/links.tsx:118
#~ msgid "About this Inventree instance"
#~ msgstr "About this Inventree instance"

#: src/defaults/actions.tsx:44
#: src/defaults/links.tsx:140
#: src/defaults/links.tsx:186
msgid "About InvenTree"
msgstr "О программе InvenTree"

#: src/defaults/actions.tsx:45
msgid "About the InvenTree org"
msgstr "О программе InvenTree org"

#: src/defaults/actions.tsx:51
msgid "Server Information"
msgstr "Информация о сервере"

#: src/defaults/actions.tsx:52
#: src/defaults/links.tsx:169
msgid "About this InvenTree instance"
msgstr "Об этом сервере InvenTree"

#: src/defaults/actions.tsx:58
#: src/defaults/links.tsx:153
#: src/defaults/links.tsx:175
msgid "License Information"
msgstr "Информация о лицензии"

#: src/defaults/actions.tsx:59
msgid "Licenses for dependencies of the service"
msgstr "Лицензии на зависимостей сервиса"

#: src/defaults/actions.tsx:65
msgid "Open Navigation"
msgstr "Открыть панель навигации"

#: src/defaults/actions.tsx:66
msgid "Open the main navigation menu"
msgstr "Открыть главное меню навигации"

#: src/defaults/actions.tsx:73
msgid "Scan a barcode or QR code"
msgstr "Сканировать штрихкод или QR-код"

#: src/defaults/actions.tsx:84
msgid "Go to the Admin Center"
msgstr "Перейти в админ центр"

#: src/defaults/dashboardItems.tsx:29
#~ msgid "Latest Parts"
#~ msgstr "Latest Parts"

#: src/defaults/dashboardItems.tsx:36
#~ msgid "BOM Waiting Validation"
#~ msgstr "BOM Waiting Validation"

#: src/defaults/dashboardItems.tsx:43
#~ msgid "Recently Updated"
#~ msgstr "Recently Updated"

#: src/defaults/dashboardItems.tsx:57
#~ msgid "Depleted Stock"
#~ msgstr "Depleted Stock"

#: src/defaults/dashboardItems.tsx:71
#~ msgid "Expired Stock"
#~ msgstr "Expired Stock"

#: src/defaults/dashboardItems.tsx:78
#~ msgid "Stale Stock"
#~ msgstr "Stale Stock"

#: src/defaults/dashboardItems.tsx:85
#~ msgid "Build Orders In Progress"
#~ msgstr "Build Orders In Progress"

#: src/defaults/dashboardItems.tsx:99
#~ msgid "Outstanding Purchase Orders"
#~ msgstr "Outstanding Purchase Orders"

#: src/defaults/dashboardItems.tsx:113
#~ msgid "Outstanding Sales Orders"
#~ msgstr "Outstanding Sales Orders"

#: src/defaults/dashboardItems.tsx:127
#~ msgid "Current News"
#~ msgstr "Current News"

#: src/defaults/defaultHostList.tsx:8
#~ msgid "InvenTree Demo"
#~ msgstr "InvenTree Demo"

#: src/defaults/defaultHostList.tsx:16
#~ msgid "Local Server"
#~ msgstr "Local Server"

#: src/defaults/links.tsx:17
#~ msgid "GitHub"
#~ msgstr "GitHub"

#: src/defaults/links.tsx:22
#~ msgid "Demo"
#~ msgstr "Demo"

#: src/defaults/links.tsx:41
#: src/defaults/menuItems.tsx:71
#: src/pages/Index/Playground.tsx:217
#~ msgid "Playground"
#~ msgstr "Playground"

#: src/defaults/links.tsx:76
#~ msgid "Instance"
#~ msgstr "Instance"

#: src/defaults/links.tsx:83
#~ msgid "InvenTree"
#~ msgstr "InvenTree"

#: src/defaults/links.tsx:93
msgid "API"
msgstr "API"

#: src/defaults/links.tsx:96
msgid "InvenTree API documentation"
msgstr "Документация по API InvenTree"

#: src/defaults/links.tsx:100
msgid "Developer Manual"
msgstr "Руководство разработчика"

#: src/defaults/links.tsx:103
msgid "InvenTree developer manual"
msgstr "Инструкция по разработке InvenTree"

#: src/defaults/links.tsx:107
msgid "FAQ"
msgstr "FAQ"

#: src/defaults/links.tsx:110
msgid "Frequently asked questions"
msgstr "Часто задаваемые вопросы"

#: src/defaults/links.tsx:114
msgid "GitHub Repository"
msgstr "Репозиторий GitHub"

#: src/defaults/links.tsx:117
msgid "InvenTree source code on GitHub"
msgstr "Исходный код InvenTree на GitHub"

#: src/defaults/links.tsx:117
#~ msgid "Licenses for packages used by InvenTree"
#~ msgstr "Licenses for packages used by InvenTree"

#: src/defaults/links.tsx:127
#: src/defaults/links.tsx:168
msgid "System Information"
msgstr "Информация о системе"

#: src/defaults/links.tsx:134
#~ msgid "Licenses"
#~ msgstr "Licenses"

#: src/defaults/links.tsx:176
msgid "Licenses for dependencies of the InvenTree software"
msgstr "Лицензии зависимостей программы InvenTree"

#: src/defaults/links.tsx:187
msgid "About the InvenTree Project"
msgstr "О проекте InvenTree"

#: src/defaults/menuItems.tsx:7
#~ msgid "Open sourcea"
#~ msgstr "Open sourcea"

#: src/defaults/menuItems.tsx:9
#~ msgid "Open source"
#~ msgstr "Open source"

#: src/defaults/menuItems.tsx:10
#~ msgid "Start page of your instance."
#~ msgstr "Start page of your instance."

#: src/defaults/menuItems.tsx:10
#~ msgid "This Pokémon’s cry is very loud and distracting"
#~ msgstr "This Pokémon’s cry is very loud and distracting"

#: src/defaults/menuItems.tsx:12
#~ msgid "This Pokémon’s cry is very loud and distracting and more and more and more"
#~ msgstr "This Pokémon’s cry is very loud and distracting and more and more and more"

#: src/defaults/menuItems.tsx:15
#~ msgid "Profile page"
#~ msgstr "Profile page"

#: src/defaults/menuItems.tsx:17
#~ msgid "User attributes and design settings."
#~ msgstr "User attributes and design settings."

#: src/defaults/menuItems.tsx:21
#~ msgid "Free for everyone"
#~ msgstr "Free for everyone"

#: src/defaults/menuItems.tsx:22
#~ msgid "The fluid of Smeargle’s tail secretions changes"
#~ msgstr "The fluid of Smeargle’s tail secretions changes"

#: src/defaults/menuItems.tsx:23
#~ msgid "View for interactive scanning and multiple actions."
#~ msgstr "View for interactive scanning and multiple actions."

#: src/defaults/menuItems.tsx:24
#~ msgid "The fluid of Smeargle’s tail secretions changes in the intensity"
#~ msgstr "The fluid of Smeargle’s tail secretions changes in the intensity"

#: src/defaults/menuItems.tsx:32
#~ msgid "abc"
#~ msgstr "abc"

#: src/defaults/menuItems.tsx:37
#~ msgid "Random image"
#~ msgstr "Random image"

#: src/defaults/menuItems.tsx:40
#~ msgid "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"
#~ msgstr "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"

#: src/defaults/menuItems.tsx:105
#~ msgid "Yanma is capable of seeing 360 degrees without"
#~ msgstr "Yanma is capable of seeing 360 degrees without"

#: src/defaults/menuItems.tsx:111
#~ msgid "The shell’s rounded shape and the grooves on its."
#~ msgstr "The shell’s rounded shape and the grooves on its."

#: src/defaults/menuItems.tsx:116
#~ msgid "Analytics"
#~ msgstr "Analytics"

#: src/defaults/menuItems.tsx:118
#~ msgid "This Pokémon uses its flying ability to quickly chase"
#~ msgstr "This Pokémon uses its flying ability to quickly chase"

#: src/defaults/menuItems.tsx:125
#~ msgid "Combusken battles with the intensely hot flames it spews"
#~ msgstr "Combusken battles with the intensely hot flames it spews"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add File"
#~ msgstr "Add File"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add Link"
#~ msgstr "Add Link"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "File added"
#~ msgstr "File added"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "Link added"
#~ msgstr "Link added"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit File"
#~ msgstr "Edit File"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit Link"
#~ msgstr "Edit Link"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "File updated"
#~ msgstr "File updated"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "Link updated"
#~ msgstr "Link updated"

#: src/forms/AttachmentForms.tsx:125
#~ msgid "Attachment deleted"
#~ msgstr "Attachment deleted"

#: src/forms/AttachmentForms.tsx:128
#~ msgid "Are you sure you want to delete this attachment?"
#~ msgstr "Are you sure you want to delete this attachment?"

#: src/forms/BomForms.tsx:109
msgid "Substitute Part"
msgstr "Детали для замены"

#: src/forms/BomForms.tsx:126
msgid "Edit BOM Substitutes"
msgstr "Редактировать варианты замены позиции спецификации"

#: src/forms/BomForms.tsx:133
msgid "Add Substitute"
msgstr "Создать замену"

#: src/forms/BomForms.tsx:134
msgid "Substitute added"
msgstr "Замена создана"

#: src/forms/BuildForms.tsx:112
#: src/forms/BuildForms.tsx:217
#: src/forms/StockForms.tsx:197
msgid "Next batch code"
msgstr "Следующий код партии"

#: src/forms/BuildForms.tsx:212
#: src/forms/StockForms.tsx:183
#: src/forms/StockForms.tsx:188
#: src/forms/StockForms.tsx:359
#: src/pages/stock/StockDetail.tsx:231
msgid "Next serial number"
msgstr "Следующий серийный номер"

#: src/forms/BuildForms.tsx:248
#~ msgid "Remove output"
#~ msgstr "Remove output"

#: src/forms/BuildForms.tsx:311
#: src/forms/BuildForms.tsx:656
#: src/tables/build/BuildAllocatedStockTable.tsx:150
#: src/tables/build/BuildOrderTestTable.tsx:230
#: src/tables/build/BuildOrderTestTable.tsx:254
#: src/tables/build/BuildOutputTable.tsx:592
msgid "Build Output"
msgstr "Продукция"

#: src/forms/BuildForms.tsx:313
#: src/forms/BuildForms.tsx:387
#: src/forms/BuildForms.tsx:451
#: src/forms/PurchaseOrderForms.tsx:714
#: src/forms/ReturnOrderForms.tsx:194
#: src/forms/ReturnOrderForms.tsx:241
#: src/forms/StockForms.tsx:656
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:87
#: src/pages/build/BuildDetail.tsx:205
#: src/pages/core/UserDetail.tsx:151
#: src/pages/purchasing/PurchaseOrderDetail.tsx:150
#: src/pages/sales/ReturnOrderDetail.tsx:115
#: src/pages/sales/SalesOrderDetail.tsx:124
#: src/pages/stock/StockDetail.tsx:162
#: src/tables/Filter.tsx:266
#: src/tables/build/BuildOutputTable.tsx:408
#: src/tables/machine/MachineListTable.tsx:339
#: src/tables/part/PartPurchaseOrdersTable.tsx:38
#: src/tables/sales/ReturnOrderLineItemTable.tsx:135
#: src/tables/sales/ReturnOrderLineItemTable.tsx:172
#: src/tables/settings/CustomStateTable.tsx:79
#: src/tables/settings/EmailTable.tsx:73
#: src/tables/settings/ImportSessionTable.tsx:117
#: src/tables/stock/StockItemTable.tsx:328
#: src/tables/stock/StockTrackingTable.tsx:65
msgid "Status"
msgstr "Статус"

#: src/forms/BuildForms.tsx:335
msgid "Complete Build Outputs"
msgstr "Завершить производство"

#: src/forms/BuildForms.tsx:338
msgid "Build outputs have been completed"
msgstr "Производство завершено"

#: src/forms/BuildForms.tsx:405
#: src/forms/BuildForms.tsx:407
msgid "Scrap Build Outputs"
msgstr "Списать Продукцию"

#: src/forms/BuildForms.tsx:408
#~ msgid "Selected build outputs will be deleted"
#~ msgstr "Selected build outputs will be deleted"

#: src/forms/BuildForms.tsx:410
msgid "Selected build outputs will be completed, but marked as scrapped"
msgstr "Выбранная продукция будет завершена, но помечена списанной"

#: src/forms/BuildForms.tsx:412
msgid "Allocated stock items will be consumed"
msgstr "Зарезервированные складские позиции будут израсходованы"

#: src/forms/BuildForms.tsx:418
msgid "Build outputs have been scrapped"
msgstr "Продукция списана"

#: src/forms/BuildForms.tsx:461
#: src/forms/BuildForms.tsx:463
msgid "Cancel Build Outputs"
msgstr "Отменить продукцию"

#: src/forms/BuildForms.tsx:465
msgid "Selected build outputs will be removed"
msgstr "Выбранная продукция будет удалена"

#: src/forms/BuildForms.tsx:467
msgid "Allocated stock items will be returned to stock"
msgstr "Зарезервированные складские позиции будут возвращены на склад"

#: src/forms/BuildForms.tsx:470
#~ msgid "Remove line"
#~ msgstr "Remove line"

#: src/forms/BuildForms.tsx:474
msgid "Build outputs have been cancelled"
msgstr "Производство отменено"

#: src/forms/BuildForms.tsx:603
#: src/forms/BuildForms.tsx:760
#: src/forms/BuildForms.tsx:861
#: src/forms/SalesOrderForms.tsx:268
#: src/tables/build/BuildAllocatedStockTable.tsx:139
#: src/tables/build/BuildLineTable.tsx:180
#: src/tables/sales/SalesOrderLineItemTable.tsx:319
#: src/tables/stock/StockItemTable.tsx:339
msgid "Allocated"
msgstr "Зарезервировано"

#: src/forms/BuildForms.tsx:638
#: src/forms/SalesOrderForms.tsx:257
#: src/pages/build/BuildDetail.tsx:320
msgid "Source Location"
msgstr "Место хранения комплектующих"

#: src/forms/BuildForms.tsx:639
#: src/forms/SalesOrderForms.tsx:258
msgid "Select the source location for the stock allocation"
msgstr "Выберите исходное расположение для распределения запасов"

#: src/forms/BuildForms.tsx:671
#: src/forms/SalesOrderForms.tsx:298
#: src/tables/build/BuildLineTable.tsx:555
#: src/tables/build/BuildLineTable.tsx:710
#: src/tables/build/BuildLineTable.tsx:809
#: src/tables/sales/SalesOrderLineItemTable.tsx:357
#: src/tables/sales/SalesOrderLineItemTable.tsx:388
msgid "Allocate Stock"
msgstr "Зарезервировать остатки"

#: src/forms/BuildForms.tsx:674
#: src/forms/SalesOrderForms.tsx:303
msgid "Stock items allocated"
msgstr "Запасы назначены"

#: src/forms/BuildForms.tsx:780
#: src/forms/BuildForms.tsx:881
#: src/tables/build/BuildAllocatedStockTable.tsx:233
#: src/tables/build/BuildAllocatedStockTable.tsx:265
#: src/tables/build/BuildLineTable.tsx:720
#: src/tables/build/BuildLineTable.tsx:843
msgid "Consume Stock"
msgstr ""

#: src/forms/BuildForms.tsx:781
#: src/forms/BuildForms.tsx:882
msgid "Stock items consumed"
msgstr ""

#: src/forms/BuildForms.tsx:817
#: src/tables/build/BuildLineTable.tsx:495
#: src/tables/part/PartBuildAllocationsTable.tsx:101
msgid "Fully consumed"
msgstr ""

#: src/forms/BuildForms.tsx:862
#: src/tables/build/BuildLineTable.tsx:185
#: src/tables/stock/StockItemTable.tsx:372
msgid "Consumed"
msgstr "Израсходовано"

#: src/forms/CompanyForms.tsx:150
#~ msgid "Company updated"
#~ msgstr "Company updated"

#: src/forms/PartForms.tsx:70
#: src/forms/PartForms.tsx:157
#: src/pages/part/CategoryDetail.tsx:122
#: src/pages/part/PartDetail.tsx:668
#: src/tables/part/PartCategoryTable.tsx:94
#: src/tables/part/PartTable.tsx:312
msgid "Subscribed"
msgstr "Получать уведомления"

#: src/forms/PartForms.tsx:71
msgid "Subscribe to notifications for this part"
msgstr "Подписаться на уведомления для этой детали"

#: src/forms/PartForms.tsx:106
#~ msgid "Create Part"
#~ msgstr "Create Part"

#: src/forms/PartForms.tsx:108
#~ msgid "Part created"
#~ msgstr "Part created"

#: src/forms/PartForms.tsx:129
#~ msgid "Part updated"
#~ msgstr "Part updated"

#: src/forms/PartForms.tsx:143
msgid "Parent part category"
msgstr "Родительская категория"

#: src/forms/PartForms.tsx:158
msgid "Subscribe to notifications for this category"
msgstr "Подписаться на уведомления для этой категории"

#: src/forms/PurchaseOrderForms.tsx:385
msgid "Assign Batch Code and Serial Numbers"
msgstr "Назначить код партии и серийные номера"

#: src/forms/PurchaseOrderForms.tsx:387
msgid "Assign Batch Code"
msgstr "Назначить код партии"

#: src/forms/PurchaseOrderForms.tsx:407
msgid "Choose Location"
msgstr "Выберите место хранения"

#: src/forms/PurchaseOrderForms.tsx:415
msgid "Item Destination selected"
msgstr "Пункт назначения товара выбран"

#: src/forms/PurchaseOrderForms.tsx:421
#~ msgid "Assign Batch Code{0}"
#~ msgstr "Assign Batch Code{0}"

#: src/forms/PurchaseOrderForms.tsx:425
msgid "Part category default location selected"
msgstr "Выбрано расположение категории по умолчанию"

#: src/forms/PurchaseOrderForms.tsx:435
msgid "Received stock location selected"
msgstr "Выбрано место получения запасов"

#: src/forms/PurchaseOrderForms.tsx:443
msgid "Default location selected"
msgstr "Выбрано местоположение по умолчанию"

#: src/forms/PurchaseOrderForms.tsx:444
#: src/forms/StockForms.tsx:428
#~ msgid "Remove item from list"
#~ msgstr "Remove item from list"

#: src/forms/PurchaseOrderForms.tsx:504
msgid "Set Location"
msgstr "Задать место хранения"

#: src/forms/PurchaseOrderForms.tsx:521
msgid "Set Expiry Date"
msgstr "Задать срок годности"

#: src/forms/PurchaseOrderForms.tsx:529
#: src/forms/StockForms.tsx:637
msgid "Adjust Packaging"
msgstr "Настройка упаковки"

#: src/forms/PurchaseOrderForms.tsx:537
#: src/forms/StockForms.tsx:628
#: src/hooks/UseStockAdjustActions.tsx:148
msgid "Change Status"
msgstr "Изменить статус"

#: src/forms/PurchaseOrderForms.tsx:543
msgid "Add Note"
msgstr "Добавить заметку"

#: src/forms/PurchaseOrderForms.tsx:566
#~ msgid "Serial numbers"
#~ msgstr "Serial numbers"

#: src/forms/PurchaseOrderForms.tsx:582
#~ msgid "Store at line item destination"
#~ msgstr "Store at line item destination"

#: src/forms/PurchaseOrderForms.tsx:607
msgid "Store at default location"
msgstr "Использовать место хранения по умолчанию"

#: src/forms/PurchaseOrderForms.tsx:622
msgid "Store at line item destination "
msgstr "Использовать место хранения позиции заказа "

#: src/forms/PurchaseOrderForms.tsx:634
msgid "Store with already received stock"
msgstr "Использовать место хранения уже полученных запасов"

#: src/forms/PurchaseOrderForms.tsx:658
#: src/pages/build/BuildDetail.tsx:334
#: src/pages/stock/StockDetail.tsx:277
#: src/pages/stock/StockDetail.tsx:923
#: src/tables/Filter.tsx:83
#: src/tables/build/BuildAllocatedStockTable.tsx:128
#: src/tables/build/BuildOrderTestTable.tsx:242
#: src/tables/build/BuildOutputTable.tsx:116
#: src/tables/sales/SalesOrderAllocationTable.tsx:146
msgid "Batch Code"
msgstr "Код партии"

#: src/forms/PurchaseOrderForms.tsx:658
#~ msgid "Receive line items"
#~ msgstr "Receive line items"

#: src/forms/PurchaseOrderForms.tsx:659
msgid "Enter batch code for received items"
msgstr "Введите код партии для полученных запасов"

#: src/forms/PurchaseOrderForms.tsx:672
#: src/forms/StockForms.tsx:176
msgid "Serial Numbers"
msgstr "Серийные номера"

#: src/forms/PurchaseOrderForms.tsx:673
msgid "Enter serial numbers for received items"
msgstr "Введите серийные номера для полученных запасов"

#: src/forms/PurchaseOrderForms.tsx:687
#: src/pages/stock/StockDetail.tsx:379
#: src/tables/stock/StockItemTable.tsx:295
msgid "Expiry Date"
msgstr "Срок годности"

#: src/forms/PurchaseOrderForms.tsx:688
msgid "Enter an expiry date for received items"
msgstr "Введите дату истечения срока годности полученных элементов"

#: src/forms/PurchaseOrderForms.tsx:700
#: src/forms/StockForms.tsx:672
#: src/pages/company/SupplierPartDetail.tsx:172
#: src/pages/company/SupplierPartDetail.tsx:236
#: src/pages/stock/StockDetail.tsx:416
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:220
msgid "Packaging"
msgstr "Упаковка"

#: src/forms/PurchaseOrderForms.tsx:724
#: src/pages/company/SupplierPartDetail.tsx:119
#: src/tables/ColumnRenderers.tsx:323
msgid "Note"
msgstr "Заметка"

#: src/forms/PurchaseOrderForms.tsx:792
#: src/pages/company/SupplierPartDetail.tsx:137
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:49
msgid "SKU"
msgstr "Артикул поставщика"

#: src/forms/PurchaseOrderForms.tsx:793
#: src/tables/part/PartPurchaseOrdersTable.tsx:127
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:206
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:272
#: src/tables/sales/ReturnOrderLineItemTable.tsx:167
msgid "Received"
msgstr "Получено"

#: src/forms/PurchaseOrderForms.tsx:810
msgid "Receive Line Items"
msgstr "Получить позиции"

#: src/forms/PurchaseOrderForms.tsx:816
msgid "Items received"
msgstr "Элементы получены"

#: src/forms/ReturnOrderForms.tsx:254
msgid "Receive Items"
msgstr "Возврат позиций"

#: src/forms/ReturnOrderForms.tsx:261
msgid "Item received into stock"
msgstr "Товар получен на складе"

#: src/forms/StockForms.tsx:110
#~ msgid "Create Stock Item"
#~ msgstr "Create Stock Item"

#: src/forms/StockForms.tsx:155
msgid "Add given quantity as packs instead of individual items"
msgstr "Ввести количество упаковок вместо количества отдельных элементов"

#: src/forms/StockForms.tsx:158
#~ msgid "Stock item updated"
#~ msgstr "Stock item updated"

#: src/forms/StockForms.tsx:169
msgid "Enter initial quantity for this stock item"
msgstr "Введите начальное количество этой детали на складе"

#: src/forms/StockForms.tsx:178
msgid "Enter serial numbers for new stock (or leave blank)"
msgstr "Введите серийные номера для нового склада (или оставьте пустым)"

#: src/forms/StockForms.tsx:200
msgid "Stock Status"
msgstr "Состояние складской позиции"

#: src/forms/StockForms.tsx:260
#: src/pages/stock/StockDetail.tsx:670
#: src/tables/stock/StockItemTable.tsx:525
#: src/tables/stock/StockItemTable.tsx:572
msgid "Add Stock Item"
msgstr "Создать складскую позицию"

#: src/forms/StockForms.tsx:304
msgid "Select the part to install"
msgstr "Выберите часть для установки"

#: src/forms/StockForms.tsx:431
msgid "Confirm Stock Transfer"
msgstr "Подтвердить перемещение запаса"

#: src/forms/StockForms.tsx:558
msgid "Loading..."
msgstr "Загрузка..."

#: src/forms/StockForms.tsx:616
msgid "Move to default location"
msgstr "Переместить в местоположение по умолчанию"

#: src/forms/StockForms.tsx:736
msgid "Move"
msgstr "Переместить"

#: src/forms/StockForms.tsx:782
msgid "Return"
msgstr ""

#: src/forms/StockForms.tsx:827
#: src/forms/StockForms.tsx:866
#: src/forms/StockForms.tsx:902
#: src/forms/StockForms.tsx:940
#: src/forms/StockForms.tsx:982
#: src/forms/StockForms.tsx:1030
#: src/forms/StockForms.tsx:1074
#: src/pages/company/SupplierPartDetail.tsx:190
#: src/pages/company/SupplierPartDetail.tsx:374
#: src/pages/part/PartDetail.tsx:535
#: src/pages/part/PartDetail.tsx:970
#: src/tables/purchasing/SupplierPartTable.tsx:194
#: src/tables/stock/StockItemTable.tsx:359
msgid "In Stock"
msgstr "На складе"

#: src/forms/StockForms.tsx:903
#: src/pages/Index/Scan.tsx:182
msgid "Count"
msgstr "Количество"

#: src/forms/StockForms.tsx:1187
#: src/hooks/UseStockAdjustActions.tsx:108
msgid "Add Stock"
msgstr "Увеличить склад"

#: src/forms/StockForms.tsx:1188
msgid "Stock added"
msgstr "Запас добавлен"

#: src/forms/StockForms.tsx:1191
msgid "Increase the quantity of the selected stock items by a given amount."
msgstr ""

#: src/forms/StockForms.tsx:1202
#: src/hooks/UseStockAdjustActions.tsx:118
msgid "Remove Stock"
msgstr "Уменьшить склад"

#: src/forms/StockForms.tsx:1203
msgid "Stock removed"
msgstr "Запас удален"

#: src/forms/StockForms.tsx:1206
msgid "Decrease the quantity of the selected stock items by a given amount."
msgstr ""

#: src/forms/StockForms.tsx:1217
#: src/hooks/UseStockAdjustActions.tsx:128
msgid "Transfer Stock"
msgstr "Переместить склад"

#: src/forms/StockForms.tsx:1218
msgid "Stock transferred"
msgstr "Запас перемещен"

#: src/forms/StockForms.tsx:1221
msgid "Transfer selected items to the specified location."
msgstr ""

#: src/forms/StockForms.tsx:1232
#: src/hooks/UseStockAdjustActions.tsx:168
msgid "Return Stock"
msgstr ""

#: src/forms/StockForms.tsx:1233
msgid "Stock returned"
msgstr ""

#: src/forms/StockForms.tsx:1236
msgid "Return selected items into stock, to the specified location."
msgstr ""

#: src/forms/StockForms.tsx:1247
#: src/hooks/UseStockAdjustActions.tsx:98
msgid "Count Stock"
msgstr "Подсчёт склада"

#: src/forms/StockForms.tsx:1248
msgid "Stock counted"
msgstr "Запас посчитан"

#: src/forms/StockForms.tsx:1251
msgid "Count the selected stock items, and adjust the quantity accordingly."
msgstr ""

#: src/forms/StockForms.tsx:1262
msgid "Change Stock Status"
msgstr "Изменить статус запасов"

#: src/forms/StockForms.tsx:1263
msgid "Stock status changed"
msgstr "Состояние запаса изменено"

#: src/forms/StockForms.tsx:1266
msgid "Change the status of the selected stock items."
msgstr ""

#: src/forms/StockForms.tsx:1277
#: src/hooks/UseStockAdjustActions.tsx:138
msgid "Merge Stock"
msgstr "Объединить склад"

#: src/forms/StockForms.tsx:1278
msgid "Stock merged"
msgstr "Запасы объединены"

#: src/forms/StockForms.tsx:1280
msgid "Merge Stock Items"
msgstr "Объединить складские позиции"

#: src/forms/StockForms.tsx:1282
msgid "Merge operation cannot be reversed"
msgstr "Операция объединения не может быть отменена"

#: src/forms/StockForms.tsx:1283
msgid "Tracking information may be lost when merging items"
msgstr "При объединении позиций информация об отслеживании может быть потеряна"

#: src/forms/StockForms.tsx:1284
msgid "Supplier information may be lost when merging items"
msgstr "При объединении может быть потеряна информация о поставщиках"

#: src/forms/StockForms.tsx:1302
msgid "Assign Stock to Customer"
msgstr "Передать запас клиенту"

#: src/forms/StockForms.tsx:1303
msgid "Stock assigned to customer"
msgstr "Запас передан клиенту"

#: src/forms/StockForms.tsx:1313
msgid "Delete Stock Items"
msgstr "Удалить складскую позицию"

#: src/forms/StockForms.tsx:1314
msgid "Stock deleted"
msgstr "Запас удален"

#: src/forms/StockForms.tsx:1317
msgid "This operation will permanently delete the selected stock items."
msgstr ""

#: src/forms/StockForms.tsx:1326
msgid "Parent stock location"
msgstr "Расположение основного склада"

#: src/forms/StockForms.tsx:1453
msgid "Find Serial Number"
msgstr "Поиск по серийному номеру"

#: src/forms/StockForms.tsx:1464
msgid "No matching items"
msgstr "Нет подходящих элементов"

#: src/forms/StockForms.tsx:1470
msgid "Multiple matching items"
msgstr "Несколько подходящих элементов"

#: src/forms/StockForms.tsx:1479
msgid "Invalid response from server"
msgstr "Неверный ответ сервера"

#: src/forms/selectionListFields.tsx:97
msgid "Entries"
msgstr "Варианты"

#: src/forms/selectionListFields.tsx:98
msgid "List of entries to choose from"
msgstr "Список вариантов для выбора"

#: src/forms/selectionListFields.tsx:102
#: src/pages/part/PartStockHistoryDetail.tsx:59
#: src/tables/FilterSelectDrawer.tsx:114
#: src/tables/FilterSelectDrawer.tsx:137
#: src/tables/FilterSelectDrawer.tsx:149
#: src/tables/build/BuildOrderTestTable.tsx:188
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:201
msgid "Value"
msgstr "Значение"

#: src/forms/selectionListFields.tsx:103
msgid "Label"
msgstr "Метка"

#: src/functions/api.tsx:33
msgid "Bad request"
msgstr "Некорректный запрос"

#: src/functions/api.tsx:36
msgid "Unauthorized"
msgstr "Не авторизован"

#: src/functions/api.tsx:39
msgid "Forbidden"
msgstr "Запрещено"

#: src/functions/api.tsx:42
msgid "Not found"
msgstr "Не найдено"

#: src/functions/api.tsx:45
msgid "Method not allowed"
msgstr "Метод не поддерживается"

#: src/functions/api.tsx:48
msgid "Internal server error"
msgstr "Внутренняя ошибка сервера"

#: src/functions/auth.tsx:34
#~ msgid "Error fetching token from server."
#~ msgstr "Error fetching token from server."

#: src/functions/auth.tsx:36
#~ msgid "Logout successfull"
#~ msgstr "Logout successfull"

#: src/functions/auth.tsx:60
#~ msgid "See you soon."
#~ msgstr "See you soon."

#: src/functions/auth.tsx:70
#~ msgid "Logout successful"
#~ msgstr "Logout successful"

#: src/functions/auth.tsx:71
#~ msgid "You have been logged out"
#~ msgstr "You have been logged out"

#: src/functions/auth.tsx:113
#: src/functions/auth.tsx:313
msgid "Already logged in"
msgstr "Вход уже выполнен"

#: src/functions/auth.tsx:114
#: src/functions/auth.tsx:314
msgid "There is a conflicting session on the server for this browser. Please logout of that first."
msgstr "На сервере есть конфликтующие сессии для данного браузера. Пожалуйста, выйдите из системы."

#: src/functions/auth.tsx:142
#~ msgid "Found an existing login - using it to log you in."
#~ msgstr "Found an existing login - using it to log you in."

#: src/functions/auth.tsx:143
#~ msgid "Found an existing login - welcome back!"
#~ msgstr "Found an existing login - welcome back!"

#: src/functions/auth.tsx:150
msgid "MFA Login successful"
msgstr "Успешный вход с помощью МФА"

#: src/functions/auth.tsx:151
msgid "MFA details were automatically provided in the browser"
msgstr "Данные МФА автоматически переданы в браузер"

#: src/functions/auth.tsx:179
msgid "Logged Out"
msgstr "Выход"

#: src/functions/auth.tsx:180
msgid "Successfully logged out"
msgstr "Успешный выход из системы"

#: src/functions/auth.tsx:218
msgid "Language changed"
msgstr "Язык изменён"

#: src/functions/auth.tsx:219
msgid "Your active language has been changed to the one set in your profile"
msgstr "Язык изменён на заданный в вашем профиле"

#: src/functions/auth.tsx:239
msgid "Theme changed"
msgstr "Тема изменена"

#: src/functions/auth.tsx:240
msgid "Your active theme has been changed to the one set in your profile"
msgstr "Тема интерфейса изменена на заданную в профиле"

#: src/functions/auth.tsx:274
msgid "Check your inbox for a reset link. This only works if you have an account. Check in spam too."
msgstr "Проверьте свой почтовый ящик, чтобы получить ссылку на сброс. Это работает только в том случае, если у вас есть учетная запись. Проверьте также спам."

#: src/functions/auth.tsx:281
#: src/functions/auth.tsx:538
msgid "Reset failed"
msgstr "Сброс не удался"

#: src/functions/auth.tsx:370
msgid "Logged In"
msgstr "Войти в систему"

#: src/functions/auth.tsx:371
msgid "Successfully logged in"
msgstr "Вход выполнен успешно"

#: src/functions/auth.tsx:498
msgid "Failed to set up MFA"
msgstr "Не удалось настроить МФА"

#: src/functions/auth.tsx:528
msgid "Password set"
msgstr "Пароль установлен"

#: src/functions/auth.tsx:529
#: src/functions/auth.tsx:638
msgid "The password was set successfully. You can now login with your new password"
msgstr "Пароль был установлен успешно. Теперь вы можете войти в систему с новым паролем"

#: src/functions/auth.tsx:603
msgid "Password could not be changed"
msgstr "Пароль не может быть изменён"

#: src/functions/auth.tsx:621
msgid "The two password fields didn’t match"
msgstr "Пароли не совпадают"

#: src/functions/auth.tsx:637
msgid "Password Changed"
msgstr "Пароль изменен"

#: src/functions/forms.tsx:50
#~ msgid "Form method not provided"
#~ msgstr "Form method not provided"

#: src/functions/forms.tsx:59
#~ msgid "Response did not contain action data"
#~ msgstr "Response did not contain action data"

#: src/functions/forms.tsx:182
#~ msgid "Invalid Form"
#~ msgstr "Invalid Form"

#: src/functions/forms.tsx:183
#~ msgid "method parameter not supplied"
#~ msgstr "method parameter not supplied"

#: src/functions/notifications.tsx:13
msgid "Not implemented"
msgstr "Не реализовано"

#: src/functions/notifications.tsx:14
msgid "This feature is not yet implemented"
msgstr "Эта функция еще не реализована"

#: src/functions/notifications.tsx:24
#~ msgid "Permission denied"
#~ msgstr "Permission denied"

#: src/functions/notifications.tsx:26
msgid "You do not have permission to perform this action"
msgstr "У вас нет прав на выполнение данного действия"

#: src/functions/notifications.tsx:37
msgid "Invalid Return Code"
msgstr "Неверный код возврата"

#: src/functions/notifications.tsx:38
msgid "Server returned status {returnCode}"
msgstr "Сервер вернул статус {returnCode}"

#: src/functions/notifications.tsx:48
msgid "Timeout"
msgstr "Таймаут"

#: src/functions/notifications.tsx:49
msgid "The request timed out"
msgstr "Превышено время выполнения запроса"

#: src/hooks/UseDataExport.tsx:34
msgid "Exporting Data"
msgstr "Экспортирование данных"

#: src/hooks/UseDataExport.tsx:109
msgid "Export Data"
msgstr "Экспорт данных"

#: src/hooks/UseDataExport.tsx:112
msgid "Export"
msgstr "Экспорт"

#: src/hooks/UseDataOutput.tsx:57
#: src/hooks/UseDataOutput.tsx:111
msgid "Process failed"
msgstr "Не удалось выполнить процесс"

#: src/hooks/UseDataOutput.tsx:75
msgid "Process completed successfully"
msgstr "Процесс успешно завершён"

#: src/hooks/UseForm.tsx:92
msgid "Item Created"
msgstr "Элемент создан"

#: src/hooks/UseForm.tsx:112
msgid "Item Updated"
msgstr "Товар обновлен"

#: src/hooks/UseForm.tsx:133
msgid "Items Updated"
msgstr "Элементы обновлены"

#: src/hooks/UseForm.tsx:135
msgid "Update multiple items"
msgstr "Обновить несколько элементов"

#: src/hooks/UseForm.tsx:165
msgid "Item Deleted"
msgstr "Товар удален"

#: src/hooks/UseForm.tsx:169
msgid "Are you sure you want to delete this item?"
msgstr "Вы уверены, что хотите удалить этот элемент?"

#: src/hooks/UsePlaceholder.tsx:59
#~ msgid "Latest serial number"
#~ msgstr "Latest serial number"

#: src/hooks/UseStockAdjustActions.tsx:100
msgid "Count selected stock items"
msgstr "Подсчёт выбранных складских позиций"

#: src/hooks/UseStockAdjustActions.tsx:110
msgid "Add to selected stock items"
msgstr "Увеличить выбранные складские позиции"

#: src/hooks/UseStockAdjustActions.tsx:120
msgid "Remove from selected stock items"
msgstr "Уменьшить выбранные складские позиции"

#: src/hooks/UseStockAdjustActions.tsx:130
msgid "Transfer selected stock items"
msgstr "Переместить выбранные складские позиции"

#: src/hooks/UseStockAdjustActions.tsx:140
msgid "Merge selected stock items"
msgstr "Объединить выбранные складские позиции"

#: src/hooks/UseStockAdjustActions.tsx:150
msgid "Change status of selected stock items"
msgstr "Изменить статус выбранных складских позиций"

#: src/hooks/UseStockAdjustActions.tsx:158
msgid "Assign Stock"
msgstr "Передать склад"

#: src/hooks/UseStockAdjustActions.tsx:160
msgid "Assign selected stock items to a customer"
msgstr "Передать складские позиции клиенту"

#: src/hooks/UseStockAdjustActions.tsx:170
msgid "Return selected items into stock"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:178
msgid "Delete Stock"
msgstr "Удалить склад"

#: src/hooks/UseStockAdjustActions.tsx:180
msgid "Delete selected stock items"
msgstr "Удалить выбранные складские позиции"

#: src/hooks/UseStockAdjustActions.tsx:205
#: src/pages/part/PartDetail.tsx:1144
msgid "Stock Actions"
msgstr "Действия со складом"

#: src/pages/Auth/ChangePassword.tsx:32
#: src/pages/Auth/Reset.tsx:14
msgid "Reset Password"
msgstr "Сброс пароля"

#: src/pages/Auth/ChangePassword.tsx:46
msgid "Current Password"
msgstr "Текущий пароль"

#: src/pages/Auth/ChangePassword.tsx:47
msgid "Enter your current password"
msgstr "Введите текущий пароль"

#: src/pages/Auth/ChangePassword.tsx:53
msgid "New Password"
msgstr "Новый пароль"

#: src/pages/Auth/ChangePassword.tsx:54
msgid "Enter your new password"
msgstr "Введите новый пароль"

#: src/pages/Auth/ChangePassword.tsx:60
msgid "Confirm New Password"
msgstr "Подтвердите новый пароль"

#: src/pages/Auth/ChangePassword.tsx:61
msgid "Confirm your new password"
msgstr "Подтвердите новый пароль"

#: src/pages/Auth/ChangePassword.tsx:80
msgid "Confirm"
msgstr "Подтвердить"

#: src/pages/Auth/Layout.tsx:59
msgid "Log off"
msgstr "Выйти"

#: src/pages/Auth/LoggedIn.tsx:19
msgid "Checking if you are already logged in"
msgstr "Проверка того, что вы уже вошли в систему"

#: src/pages/Auth/Login.tsx:32
msgid "No selection"
msgstr "Ничего не выбрано"

#: src/pages/Auth/Login.tsx:91
#~ msgid "Welcome, log in below"
#~ msgstr "Welcome, log in below"

#: src/pages/Auth/Login.tsx:93
#~ msgid "Register below"
#~ msgstr "Register below"

#: src/pages/Auth/Login.tsx:100
msgid "Login"
msgstr "Войти"

#: src/pages/Auth/Login.tsx:106
msgid "Logging you in"
msgstr "Вход в систему"

#: src/pages/Auth/Login.tsx:113
msgid "Don't have an account?"
msgstr "Нет аккаунта?"

#: src/pages/Auth/Logout.tsx:22
#~ msgid "Logging out"
#~ msgstr "Logging out"

#: src/pages/Auth/MFA.tsx:16
#~ msgid "Multi-Factor Login"
#~ msgstr "Multi-Factor Login"

#: src/pages/Auth/MFA.tsx:17
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:79
msgid "Multi-Factor Authentication"
msgstr "Многофакторная аутентификация"

#: src/pages/Auth/MFA.tsx:20
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:693
msgid "TOTP Code"
msgstr "Код TOTP"

#: src/pages/Auth/MFA.tsx:22
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:695
msgid "Enter your TOTP or recovery code"
msgstr "Введите пароль TOTP или код восстановления"

#: src/pages/Auth/MFA.tsx:27
msgid "Remember this device"
msgstr "Запомнить это устройство"

#: src/pages/Auth/MFA.tsx:29
msgid "If enabled, you will not be asked for MFA on this device for 30 days."
msgstr "Если включить, то на этом устройстве многофакторная аутентификация не будет запрашиваться в течение 30 дней."

#: src/pages/Auth/MFA.tsx:38
msgid "Log in"
msgstr "Войти"

#: src/pages/Auth/MFASetup.tsx:23
msgid "MFA Setup Required"
msgstr "Требуется настройка MFA"

#: src/pages/Auth/MFASetup.tsx:34
msgid "Add TOTP"
msgstr "Добавить ТОТР"

#: src/pages/Auth/Register.tsx:23
msgid "Go back to login"
msgstr "Вернуться к логину"

#: src/pages/Auth/Reset.tsx:41
#: src/pages/Auth/Set-Password.tsx:112
#~ msgid "Send mail"
#~ msgstr "Send mail"

#: src/pages/Auth/ResetPassword.tsx:22
#: src/pages/Auth/VerifyEmail.tsx:19
msgid "Key invalid"
msgstr "Неверный ключ"

#: src/pages/Auth/ResetPassword.tsx:23
msgid "You need to provide a valid key to set a new password. Check your inbox for a reset link."
msgstr "Для установки нового пароля необходимо предоставить действующий токен. Проверьте свой почтовый ящик, чтобы получить ссылку на сброс пароля."

#: src/pages/Auth/ResetPassword.tsx:30
#~ msgid "Token invalid"
#~ msgstr "Token invalid"

#: src/pages/Auth/ResetPassword.tsx:31
msgid "Set new password"
msgstr "Установить новый пароль"

#: src/pages/Auth/ResetPassword.tsx:31
#~ msgid "You need to provide a valid token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a valid token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/ResetPassword.tsx:35
msgid "The desired new password"
msgstr "Желаемый новый пароль"

#: src/pages/Auth/ResetPassword.tsx:44
msgid "Send Password"
msgstr "Отправить пароль"

#: src/pages/Auth/Set-Password.tsx:49
#~ msgid "No token provided"
#~ msgstr "No token provided"

#: src/pages/Auth/Set-Password.tsx:50
#~ msgid "You need to provide a token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/VerifyEmail.tsx:20
msgid "You need to provide a valid key."
msgstr "Вы должны предоставить правильный ключ."

#: src/pages/Auth/VerifyEmail.tsx:28
msgid "Verify Email"
msgstr "Подтвердить электронную почту"

#: src/pages/Auth/VerifyEmail.tsx:30
msgid "Verify"
msgstr "Подтвердить"

#. placeholder {0}: error.statusText
#: src/pages/ErrorPage.tsx:16
msgid "Error: {0}"
msgstr "Ошибка: {0}"

#: src/pages/ErrorPage.tsx:23
msgid "An unexpected error has occurred"
msgstr "Произошла неожиданная ошибка"

#: src/pages/ErrorPage.tsx:28
#~ msgid "Sorry, an unexpected error has occurred."
#~ msgstr "Sorry, an unexpected error has occurred."

#: src/pages/Index/Dashboard.tsx:22
#~ msgid "Autoupdate"
#~ msgstr "Autoupdate"

#: src/pages/Index/Dashboard.tsx:26
#~ msgid "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."
#~ msgstr "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."

#: src/pages/Index/Home.tsx:58
#~ msgid "Welcome to your Dashboard{0}"
#~ msgstr "Welcome to your Dashboard{0}"

#: src/pages/Index/Playground.tsx:222
#~ msgid "This page is a showcase for the possibilities of Platform UI."
#~ msgstr "This page is a showcase for the possibilities of Platform UI."

#: src/pages/Index/Profile/Profile.tsx:30
#: src/pages/Index/Profile/Profile.tsx:141
#~ msgid "Notification Settings"
#~ msgstr "Notification Settings"

#: src/pages/Index/Profile/Profile.tsx:33
#~ msgid "Global Settings"
#~ msgstr "Global Settings"

#: src/pages/Index/Profile/Profile.tsx:47
#~ msgid "Settings for the current user"
#~ msgstr "Settings for the current user"

#: src/pages/Index/Profile/Profile.tsx:51
#~ msgid "Home Page Settings"
#~ msgstr "Home Page Settings"

#: src/pages/Index/Profile/Profile.tsx:76
#~ msgid "Search Settings"
#~ msgstr "Search Settings"

#: src/pages/Index/Profile/Profile.tsx:115
#: src/pages/Index/Profile/Profile.tsx:211
#~ msgid "Label Settings"
#~ msgstr "Label Settings"

#: src/pages/Index/Profile/Profile.tsx:120
#: src/pages/Index/Profile/Profile.tsx:219
#~ msgid "Report Settings"
#~ msgstr "Report Settings"

#: src/pages/Index/Profile/Profile.tsx:142
#~ msgid "Settings for the notifications"
#~ msgstr "Settings for the notifications"

#: src/pages/Index/Profile/Profile.tsx:148
#~ msgid "Global Server Settings"
#~ msgstr "Global Server Settings"

#: src/pages/Index/Profile/Profile.tsx:149
#~ msgid "Global Settings for this instance"
#~ msgstr "Global Settings for this instance"

#: src/pages/Index/Profile/Profile.tsx:153
#~ msgid "Server Settings"
#~ msgstr "Server Settings"

#: src/pages/Index/Profile/Profile.tsx:187
#~ msgid "Login Settings"
#~ msgstr "Login Settings"

#: src/pages/Index/Profile/Profile.tsx:202
#~ msgid "Barcode Settings"
#~ msgstr "Barcode Settings"

#: src/pages/Index/Profile/Profile.tsx:230
#~ msgid "Part Settings"
#~ msgstr "Part Settings"

#: src/pages/Index/Profile/Profile.tsx:255
#~ msgid "Pricing Settings"
#~ msgstr "Pricing Settings"

#: src/pages/Index/Profile/Profile.tsx:270
#~ msgid "Stock Settings"
#~ msgstr "Stock Settings"

#: src/pages/Index/Profile/Profile.tsx:284
#~ msgid "Build Order Settings"
#~ msgstr "Build Order Settings"

#: src/pages/Index/Profile/Profile.tsx:289
#~ msgid "Purchase Order Settings"
#~ msgstr "Purchase Order Settings"

#: src/pages/Index/Profile/Profile.tsx:300
#~ msgid "Sales Order Settings"
#~ msgstr "Sales Order Settings"

#: src/pages/Index/Profile/Profile.tsx:330
#~ msgid "Plugin Settings for this instance"
#~ msgstr "Plugin Settings for this instance"

#: src/pages/Index/Profile/SettingsPanel.tsx:27
#~ msgid "Data is current beeing loaded"
#~ msgstr "Data is current beeing loaded"

#: src/pages/Index/Profile/SettingsPanel.tsx:69
#: src/pages/Index/Profile/SettingsPanel.tsx:76
#~ msgid "Failed to load"
#~ msgstr "Failed to load"

#: src/pages/Index/Profile/SettingsPanel.tsx:100
#~ msgid "Show internal names"
#~ msgstr "Show internal names"

#: src/pages/Index/Profile/SettingsPanel.tsx:148
#~ msgid "Input {0} is not known"
#~ msgstr "Input {0} is not known"

#: src/pages/Index/Profile/SettingsPanel.tsx:161
#~ msgid "Saved changes {0}"
#~ msgstr "Saved changes {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:162
#~ msgid "Changed to {0}"
#~ msgstr "Changed to {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:176
#~ msgid "Error while saving {0}"
#~ msgstr "Error while saving {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:177
#~ msgid "Error was {err}"
#~ msgstr "Error was {err}"

#: src/pages/Index/Profile/SettingsPanel.tsx:257
#~ msgid "Plugin: {0}"
#~ msgstr "Plugin: {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:262
#~ msgid "Method: {0}"
#~ msgstr "Method: {0}"

#: src/pages/Index/Profile/UserPanel.tsx:85
#~ msgid "Userinfo"
#~ msgstr "Userinfo"

#: src/pages/Index/Profile/UserPanel.tsx:122
#~ msgid "Username: {0}"
#~ msgstr "Username: {0}"

#: src/pages/Index/Profile/UserTheme.tsx:83
#~ msgid "Design <0/>"
#~ msgstr "Design <0/>"

#: src/pages/Index/Scan.tsx:65
msgid "Item already scanned"
msgstr "Элемент уже отсканирован"

#: src/pages/Index/Scan.tsx:82
msgid "API Error"
msgstr "Ошибка API"

#: src/pages/Index/Scan.tsx:83
msgid "Failed to fetch instance data"
msgstr "Не удалось получить данные"

#: src/pages/Index/Scan.tsx:130
msgid "Scan Error"
msgstr "Ошибка сканирования"

#: src/pages/Index/Scan.tsx:162
msgid "Selected elements are not known"
msgstr "Выбранные элементы не известны"

#: src/pages/Index/Scan.tsx:169
msgid "Multiple object types selected"
msgstr "Выбрано несколько типов объектов"

#: src/pages/Index/Scan.tsx:175
#~ msgid "Actions ..."
#~ msgstr "Actions ..."

#: src/pages/Index/Scan.tsx:177
msgid "Actions ... "
msgstr "Действия ... "

#: src/pages/Index/Scan.tsx:194
#: src/pages/Index/Scan.tsx:198
msgid "Barcode Scanning"
msgstr "Сканирование штрихкодов"

#: src/pages/Index/Scan.tsx:207
msgid "Barcode Input"
msgstr "Ввод штрихкода"

#: src/pages/Index/Scan.tsx:214
msgid "Action"
msgstr "Действие"

#: src/pages/Index/Scan.tsx:217
msgid "No Items Selected"
msgstr "Объекты не выбраны"

#: src/pages/Index/Scan.tsx:217
#~ msgid "Manual input"
#~ msgstr "Manual input"

#: src/pages/Index/Scan.tsx:218
msgid "Scan and select items to perform actions"
msgstr "Отсканируйте и выберите объекты для выполнения действий"

#: src/pages/Index/Scan.tsx:218
#~ msgid "Image Barcode"
#~ msgstr "Image Barcode"

#. placeholder {0}: selection.length
#: src/pages/Index/Scan.tsx:223
msgid "{0} items selected"
msgstr "Выбрано объектов: {0}"

#: src/pages/Index/Scan.tsx:235
msgid "Scanned Items"
msgstr "Отсканированные объекты"

#: src/pages/Index/Scan.tsx:276
#~ msgid "Actions for {0}"
#~ msgstr "Actions for {0}"

#: src/pages/Index/Scan.tsx:298
#~ msgid "Scan Page"
#~ msgstr "Scan Page"

#: src/pages/Index/Scan.tsx:301
#~ msgid "This page can be used for continuously scanning items and taking actions on them."
#~ msgstr "This page can be used for continuously scanning items and taking actions on them."

#: src/pages/Index/Scan.tsx:308
#~ msgid "Toggle Fullscreen"
#~ msgstr "Toggle Fullscreen"

#: src/pages/Index/Scan.tsx:321
#~ msgid "Select the input method you want to use to scan items."
#~ msgstr "Select the input method you want to use to scan items."

#: src/pages/Index/Scan.tsx:323
#~ msgid "Input"
#~ msgstr "Input"

#: src/pages/Index/Scan.tsx:330
#~ msgid "Select input method"
#~ msgstr "Select input method"

#: src/pages/Index/Scan.tsx:331
#~ msgid "Nothing found"
#~ msgstr "Nothing found"

#: src/pages/Index/Scan.tsx:339
#~ msgid "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."
#~ msgstr "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."

#: src/pages/Index/Scan.tsx:353
#~ msgid "General Actions"
#~ msgstr "General Actions"

#: src/pages/Index/Scan.tsx:367
#~ msgid "Lookup part"
#~ msgstr "Lookup part"

#: src/pages/Index/Scan.tsx:375
#~ msgid "Open Link"
#~ msgstr "Open Link"

#: src/pages/Index/Scan.tsx:391
#~ msgid "History is locally kept in this browser."
#~ msgstr "History is locally kept in this browser."

#: src/pages/Index/Scan.tsx:392
#~ msgid "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."
#~ msgstr "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."

#: src/pages/Index/Scan.tsx:400
#~ msgid "Delete History"
#~ msgstr "Delete History"

#: src/pages/Index/Scan.tsx:465
#~ msgid "No history"
#~ msgstr "No history"

#: src/pages/Index/Scan.tsx:492
#~ msgid "Scanned at"
#~ msgstr "Scanned at"

#: src/pages/Index/Scan.tsx:549
#~ msgid "Enter item serial or data"
#~ msgstr "Enter item serial or data"

#: src/pages/Index/Scan.tsx:561
#~ msgid "Add dummy item"
#~ msgstr "Add dummy item"

#: src/pages/Index/Scan.tsx:652
#~ msgid "Error while getting camera"
#~ msgstr "Error while getting camera"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Scanning"
#~ msgstr "Scanning"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Not scanning"
#~ msgstr "Not scanning"

#: src/pages/Index/Scan.tsx:777
#~ msgid "Select Camera"
#~ msgstr "Select Camera"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:30
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:52
#~ msgid "Edit User Information"
#~ msgstr "Edit User Information"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:33
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:113
msgid "Edit Account Information"
msgstr "Редактировать данные аккаунта"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:34
#~ msgid "User details updated"
#~ msgstr "User details updated"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:37
msgid "Account details updated"
msgstr "Данные аккаунта обновлены"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:46
#~ msgid "User Actions"
#~ msgstr "User Actions"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:50
#~ msgid "First name"
#~ msgstr "First name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:136
msgid "Edit Profile Information"
msgstr "Редактировать данные профиля"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#~ msgid "Last name"
#~ msgstr "Last name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:56
#~ msgid "Set User Password"
#~ msgstr "Set User Password"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:58
#~ msgid "First name: {0}"
#~ msgstr "First name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:59
msgid "Profile details updated"
msgstr "Данные профиля обновлены"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:61
#~ msgid "Last name: {0}"
#~ msgstr "Last name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:65
#: src/pages/core/UserDetail.tsx:55
msgid "First Name"
msgstr "Имя"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:66
#: src/pages/core/UserDetail.tsx:63
msgid "Last Name"
msgstr "Фамилия"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:67
#~ msgid "First name:"
#~ msgstr "First name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:71
#~ msgid "Last name:"
#~ msgstr "Last name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:72
msgid "Staff Access"
msgstr "Доступ сотрудника"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:85
#: src/pages/core/UserDetail.tsx:119
#: src/tables/settings/CustomStateTable.tsx:101
msgid "Display Name"
msgstr "Отображаемое имя"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:86
#: src/pages/core/UserDetail.tsx:127
msgid "Position"
msgstr "Должность"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:90
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:447
msgid "Type"
msgstr "Тип"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:91
#: src/pages/core/UserDetail.tsx:143
msgid "Organisation"
msgstr "Организация"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:92
msgid "Primary Group"
msgstr "Основная группа"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:104
msgid "Account Details"
msgstr "Сведения об аккаунте"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:107
msgid "Account Actions"
msgstr "Действия с аккаунтом"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:111
msgid "Edit Account"
msgstr "Редактировать аккаунт"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:117
#: src/tables/settings/UserTable.tsx:322
msgid "Change Password"
msgstr "Изменить пароль"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:119
msgid "Change User Password"
msgstr "Изменить пароль пользователя"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:131
msgid "Profile Details"
msgstr "Сведения о профиле"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:134
msgid "Edit Profile"
msgstr "Редактировать профиль"

#. placeholder {0}: item.label
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:153
msgid "{0}"
msgstr "{0}"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:24
msgid "Secret"
msgstr "Секрет"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:30
msgid "One-Time Password"
msgstr "Одноразовый пароль"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:31
msgid "Enter the TOTP code to ensure it registered correctly"
msgstr "Введите код TOTP, чтобы убедиться, что он зарегистрирован правильно"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:53
msgid "Email Addresses"
msgstr "Адреса электронной почты"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:55
#~ msgid "Single Sign On Accounts"
#~ msgstr "Single Sign On Accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:61
msgid "Single Sign On"
msgstr "Технология единого входа (SSO)"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
msgid "Not enabled"
msgstr "Не включен"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
#~ msgid "Multifactor"
#~ msgstr "Multifactor"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:71
#~ msgid "Single Sign On is not enabled for this server"
#~ msgstr "Single Sign On is not enabled for this server"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:72
msgid "Single Sign On is not enabled for this server "
msgstr "Технология единого входа (SSO) не включена на данном сервере"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:83
#~ msgid "Multifactor authentication is not configured for your account"
#~ msgstr "Multifactor authentication is not configured for your account"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:87
msgid "Access Tokens"
msgstr "Токены доступа"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:125
msgid "Error while updating email"
msgstr "Ошибка обновления электронной почты"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:139
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:297
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:435
msgid "Not Configured"
msgstr "Не настроен"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:142
msgid "Currently no email addresses are registered."
msgstr "В настоящее время не зарегистрирован ни один адрес электронной почты."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:150
msgid "The following email addresses are associated with your account:"
msgstr "С вашей учетной записью связаны следующие адреса электронной почты:"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:163
msgid "Primary"
msgstr "Основной"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:168
msgid "Verified"
msgstr "Проверено"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:172
msgid "Unverified"
msgstr "Непроверенный"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:190
msgid "Make Primary"
msgstr "Сделать основным"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:196
msgid "Re-send Verification"
msgstr "Отправить подтверждение повторно"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:210
msgid "Add Email Address"
msgstr "Добавить адрес электронной почты"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:212
msgid "E-Mail"
msgstr "Электронная почта"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:213
msgid "E-Mail address"
msgstr "Адрес электронной почты"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:225
msgid "Error while adding email"
msgstr "Ошибка добавления электронной почты"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:236
msgid "Add Email"
msgstr "Добавить Email"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:270
#~ msgid "Provider has not been configured"
#~ msgstr "Provider has not been configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:280
#~ msgid "Not configured"
#~ msgstr "Not configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:283
#~ msgid "There are no social network accounts connected to this account."
#~ msgstr "There are no social network accounts connected to this account."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:293
#~ msgid "You can sign in to your account using any of the following third party accounts"
#~ msgstr "You can sign in to your account using any of the following third party accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:300
msgid "There are no providers connected to this account."
msgstr "Нет провайдеров, подключенных к этой учетной записи."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:309
msgid "You can sign in to your account using any of the following providers"
msgstr "Вы можете войти в свою учётную запись, используя любой из следующих провайдеров"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:322
msgid "Remove Provider Link"
msgstr "Удалить ссылку провайдера"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:439
msgid "No multi-factor tokens configured for this account"
msgstr "Токены многофакторной аутентификации для данного аккаунта не настроены"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:450
msgid "Last used at"
msgstr "Последнее использование"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:453
msgid "Created at"
msgstr "Создан"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:474
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:579
msgid "Recovery Codes"
msgstr "Коды восстановления"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:478
msgid "Unused Codes"
msgstr "Неиспользованные коды"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:483
msgid "Used Codes"
msgstr "Использованные коды"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:538
msgid "Error while registering recovery codes"
msgstr "Ошибка регистрации кодов восстановления"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:572
msgid "TOTP"
msgstr "TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:573
msgid "Time-based One-Time Password"
msgstr "Алгоритм одноразового пароля на основе времени"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:580
msgid "One-Time pre-generated recovery codes"
msgstr "Коды восстановления доступа используя TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:594
msgid "Add Token"
msgstr "Добавить токен"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:609
msgid "Register TOTP Token"
msgstr "Зарегестрировать токен TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:634
msgid "Error registering TOTP token"
msgstr "Ошибка регистрации токена TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:710
msgid "Enter your password"
msgstr "Введите пароль"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:741
#~ msgid "Token is used - no actions"
#~ msgstr "Token is used - no actions"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:761
#~ msgid "No tokens configured"
#~ msgstr "No tokens configured"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:61
msgid "Display Settings"
msgstr "Настройки отображения"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:65
#~ msgid "bars"
#~ msgstr "bars"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:66
#~ msgid "oval"
#~ msgstr "oval"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
msgid "Language"
msgstr "Язык"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
#~ msgid "dots"
#~ msgstr "dots"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:78
msgid "Use pseudo language"
msgstr "Использовать псевдолокализацию"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:81
#~ msgid "Theme"
#~ msgstr "Theme"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:85
msgid "Color Mode"
msgstr "Цветовое оформление"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:87
#~ msgid "Primary color"
#~ msgstr "Primary color"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:96
msgid "Highlight color"
msgstr "Цвет выделения"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:110
msgid "Example"
msgstr "Пример"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:116
msgid "White color"
msgstr "Белый цвет"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:139
msgid "Black color"
msgstr "Черный цвет"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:162
msgid "Border Radius"
msgstr "Радиус границы"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:178
msgid "Loader"
msgstr "Значок загрузки"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:185
msgid "Bars"
msgstr "Полоски"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:186
msgid "Oval"
msgstr "Овал"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:187
msgid "Dots"
msgstr "Точки"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:93
msgid "Reauthentication"
msgstr "Повторная аутентификация"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:109
msgid "OK"
msgstr "OK"

#: src/pages/Index/Settings/AdminCenter.tsx:91
#~ msgid "Advanced Amininistrative Options for InvenTree"
#~ msgstr "Advanced Amininistrative Options for InvenTree"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:28
#: src/tables/ColumnRenderers.tsx:476
msgid "Currency"
msgstr "Валюта"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:33
msgid "Rate"
msgstr "Курс"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:46
msgid "Exchange rates updated"
msgstr "Курсы обмена валют обновлены"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:53
msgid "Exchange rate update error"
msgstr "Ошибка обновления курсов обмена валют"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:63
msgid "Refresh currency exchange rates"
msgstr "Обновить курсы обмена валют"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:99
msgid "Last fetched"
msgstr "Последнее обновление"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:100
msgid "Base currency"
msgstr "Основная валюта"

#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:13
msgid "Email Messages"
msgstr "Сообщения электронной почты"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:107
#~ msgid "User Management"
#~ msgstr "User Management"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:112
msgid "Users / Access"
msgstr "Пользователи / доступ"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:126
msgid "Data Import"
msgstr "Импорт данных"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:127
#~ msgid "Templates"
#~ msgstr "Templates"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:132
msgid "Data Export"
msgstr "Экспорт данных"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:138
msgid "Barcode Scans"
msgstr "Сканирование штрихкодов"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:144
msgid "Background Tasks"
msgstr "Фоновые задачи"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:150
msgid "Error Reports"
msgstr "Отчёты об ошибках"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:156
msgid "Currencies"
msgstr "Курсы валют"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:170
#~ msgid "Location types"
#~ msgstr "Location types"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:173
msgid "Custom States"
msgstr "Пользовательские состояния"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:179
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:57
msgid "Custom Units"
msgstr "Единицы измерения"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:185
#: src/pages/part/CategoryDetail.tsx:302
msgid "Part Parameters"
msgstr "Параметры деталей"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:192
msgid "Category Parameters"
msgstr "Параметры категории"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:211
msgid "Location Types"
msgstr "Типы мест хранения"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:221
#~ msgid "Quick Actions"
#~ msgstr "Quick Actions"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:225
#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:48
#: src/tables/machine/MachineTypeTable.tsx:307
msgid "Machines"
msgstr "Оборудование"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:226
#~ msgid "Add a new user"
#~ msgstr "Add a new user"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:236
msgid "Operations"
msgstr "Действия"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:248
msgid "Data Management"
msgstr "Управление данными"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:259
#: src/pages/Index/Settings/SystemSettings.tsx:175
#: src/pages/Index/Settings/UserSettings.tsx:118
msgid "Reporting"
msgstr "Отчёты"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:264
msgid "PLM"
msgstr "PLM"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:274
msgid "Extend / Integrate"
msgstr "Расширения / интеграции"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:288
msgid "Advanced Options"
msgstr "Расширенные настройки"

#: src/pages/Index/Settings/AdminCenter/LabelTemplatePanel.tsx:40
#~ msgid "Generated Labels"
#~ msgstr "Generated Labels"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:43
#~ msgid "Machine types"
#~ msgstr "Machine types"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:53
#~ msgid "Machine Error Stack"
#~ msgstr "Machine Error Stack"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:56
msgid "Machine Types"
msgstr "Типы оборудования"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:62
#~ msgid "There are no machine registry errors."
#~ msgstr "There are no machine registry errors."

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:64
msgid "Machine Errors"
msgstr "Ошибки оборудования"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:77
msgid "Registry Registry Errors"
msgstr "Ошибки реестра"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:80
msgid "There are machine registry errors"
msgstr "Есть ошибки реестра оборудования"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:86
msgid "Machine Registry Errors"
msgstr "Ошибки реестра оборудования"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:89
msgid "There are no machine registry errors"
msgstr "Нет ошибок реестра оборудования"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#: src/tables/settings/UserTable.tsx:195
msgid "Info"
msgstr "Информация"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#~ msgid "Plugin Error Stack"
#~ msgstr "Plugin Error Stack"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:37
msgid "External plugins are not enabled for this InvenTree installation."
msgstr "Сторонние плагины на разрешены для данной установки InvenTree."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:45
#~ msgid "Warning"
#~ msgstr "Warning"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:47
#~ msgid "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."
#~ msgstr "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:76
msgid "Plugin Errors"
msgstr "Ошибки плагинов"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:16
msgid "Page Size"
msgstr "Размер страницы"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:19
msgid "Landscape"
msgstr "Альбомный"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:25
msgid "Merge"
msgstr "Объединить"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:31
msgid "Attach to Model"
msgstr "Прикрепить к модели"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:55
#~ msgid "Generated Reports"
#~ msgstr "Generated Reports"

#: src/pages/Index/Settings/AdminCenter/StocktakePanel.tsx:25
#~ msgid "Stocktake Reports"
#~ msgstr "Stocktake Reports"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:30
msgid "Background worker not running"
msgstr "Фоновый процесс не запущен"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:31
msgid "The background task manager service is not running. Contact your system administrator."
msgstr "Служба управления фоновыми задачами не запущена. Обратитесь к системному администратору."

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:35
#~ msgid "Background Worker Not Running"
#~ msgstr "Background Worker Not Running"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:38
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:47
msgid "Pending Tasks"
msgstr "Ожидающие задачи"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:39
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:55
msgid "Scheduled Tasks"
msgstr "Запланированные задачи"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:40
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:63
msgid "Failed Tasks"
msgstr "Невыполненные задачи"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:67
#~ msgid "Stock item"
#~ msgstr "Stock item"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:76
#~ msgid "Build line"
#~ msgstr "Build line"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:88
#~ msgid "Reports"
#~ msgstr "Reports"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:99
#~ msgid "Purchase order"
#~ msgstr "Purchase order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:108
#~ msgid "Sales order"
#~ msgstr "Sales order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:117
#~ msgid "Return order"
#~ msgstr "Return order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:145
#~ msgid "Tests"
#~ msgstr "Tests"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:154
#~ msgid "Stock location"
#~ msgstr "Stock location"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:21
msgid "Alias"
msgstr "Псевдоним"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:22
msgid "Dimensionless"
msgstr "Безразмерная"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:65
msgid "All units"
msgstr "Все единицы измерения"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:31
msgid "Tokens"
msgstr "Токены"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:32
#~ msgid "Select settings relevant for user lifecycle. More available in"
#~ msgstr "Select settings relevant for user lifecycle. More available in"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:37
#~ msgid "System settings"
#~ msgstr "System settings"

#: src/pages/Index/Settings/PluginSettingsGroup.tsx:99
msgid "The settings below are specific to each available plugin"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:77
msgid "Authentication"
msgstr "Аутентификация"

#: src/pages/Index/Settings/SystemSettings.tsx:103
msgid "Barcodes"
msgstr "Штрих-коды"

#: src/pages/Index/Settings/SystemSettings.tsx:118
#~ msgid "Physical Units"
#~ msgstr "Physical Units"

#: src/pages/Index/Settings/SystemSettings.tsx:119
#~ msgid "This panel is a placeholder."
#~ msgstr "This panel is a placeholder."

#: src/pages/Index/Settings/SystemSettings.tsx:127
#: src/pages/Index/Settings/UserSettings.tsx:112
msgid "The settings below are specific to each available notification method"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:133
msgid "Pricing"
msgstr "Цены"

#: src/pages/Index/Settings/SystemSettings.tsx:135
#~ msgid "Exchange Rates"
#~ msgstr "Exchange Rates"

#: src/pages/Index/Settings/SystemSettings.tsx:169
msgid "Labels"
msgstr "Метки"

#: src/pages/Index/Settings/SystemSettings.tsx:317
#~ msgid "Switch to User Setting"
#~ msgstr "Switch to User Setting"

#: src/pages/Index/Settings/UserSettings.tsx:41
msgid "Account"
msgstr "Аккаунт"

#: src/pages/Index/Settings/UserSettings.tsx:47
msgid "Security"
msgstr "Безопасность"

#: src/pages/Index/Settings/UserSettings.tsx:53
msgid "Display Options"
msgstr "Параметры отображения"

#: src/pages/Index/Settings/UserSettings.tsx:159
#~ msgid "Switch to System Setting"
#~ msgstr "Switch to System Setting"

#: src/pages/Logged-In.tsx:24
#~ msgid "Found an exsisting login - using it to log you in."
#~ msgstr "Found an exsisting login - using it to log you in."

#: src/pages/NotFound.tsx:20
#~ msgid "Sorry, this page is not known or was moved."
#~ msgstr "Sorry, this page is not known or was moved."

#: src/pages/NotFound.tsx:27
#~ msgid "Go to the start page"
#~ msgstr "Go to the start page"

#: src/pages/Notifications.tsx:44
#~ msgid "Delete Notifications"
#~ msgstr "Delete Notifications"

#: src/pages/Notifications.tsx:83
msgid "History"
msgstr "История"

#: src/pages/Notifications.tsx:91
msgid "Mark as unread"
msgstr "Пометить как непрочитанное"

#: src/pages/Notifications.tsx:146
#~ msgid "Delete notifications"
#~ msgstr "Delete notifications"

#: src/pages/build/BuildDetail.tsx:68
msgid "No Required Items"
msgstr "Детали не требуются"

#: src/pages/build/BuildDetail.tsx:70
msgid "This build order does not have any required items."
msgstr "Для заказа на сборку не требуется никаких деталей."

#: src/pages/build/BuildDetail.tsx:71
msgid "The assembled part may not have a Bill of Materials (BOM) defined, or the BOM is empty."
msgstr "У собираемой детали возможно не задана спецификация, или спецификация пустая."

#: src/pages/build/BuildDetail.tsx:80
#~ msgid "Build Status"
#~ msgstr "Build Status"

#: src/pages/build/BuildDetail.tsx:185
#: src/pages/part/PartDetail.tsx:269
#: src/pages/stock/StockDetail.tsx:150
#~ msgid "View part barcode"
#~ msgstr "View part barcode"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/company/ManufacturerPartDetail.tsx:83
#: src/pages/company/SupplierPartDetail.tsx:95
#: src/pages/part/PartDetail.tsx:454
#: src/pages/stock/StockDetail.tsx:153
#: src/tables/bom/BomTable.tsx:134
#: src/tables/bom/UsedInTable.tsx:40
#: src/tables/build/BuildAllocatedStockTable.tsx:108
#: src/tables/build/BuildLineTable.tsx:328
#: src/tables/build/BuildOrderTable.tsx:78
#: src/tables/part/PartSalesAllocationsTable.tsx:61
#: src/tables/part/RelatedPartTable.tsx:73
#: src/tables/sales/SalesOrderAllocationTable.tsx:132
#: src/tables/sales/SalesOrderLineItemTable.tsx:96
#: src/tables/stock/StockItemTable.tsx:70
msgid "IPN"
msgstr "Внутренний артикул"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/part/PartDetail.tsx:274
#~ msgid "Link custom barcode to part"
#~ msgstr "Link custom barcode to part"

#: src/pages/build/BuildDetail.tsx:196
#: src/pages/part/PartDetail.tsx:280
#~ msgid "Unlink custom barcode from part"
#~ msgstr "Unlink custom barcode from part"

#: src/pages/build/BuildDetail.tsx:198
#: src/pages/part/PartDetail.tsx:481
#: src/tables/bom/UsedInTable.tsx:44
#: src/tables/build/BuildOrderTable.tsx:82
#: src/tables/stock/StockItemTable.tsx:75
msgid "Revision"
msgstr "Ревизия"

#: src/pages/build/BuildDetail.tsx:202
#~ msgid "Build Order updated"
#~ msgstr "Build Order updated"

#: src/pages/build/BuildDetail.tsx:211
#: src/pages/purchasing/PurchaseOrderDetail.tsx:156
#: src/pages/sales/ReturnOrderDetail.tsx:121
#: src/pages/sales/SalesOrderDetail.tsx:130
#: src/pages/stock/StockDetail.tsx:168
msgid "Custom Status"
msgstr "Пользовательский статус"

#: src/pages/build/BuildDetail.tsx:220
#: src/pages/build/BuildDetail.tsx:716
#: src/pages/build/BuildIndex.tsx:28
#: src/pages/stock/LocationDetail.tsx:142
#: src/tables/build/BuildOrderTable.tsx:122
#: src/tables/build/BuildOrderTable.tsx:184
#: src/tables/stock/StockLocationTable.tsx:48
msgid "External"
msgstr "Сторонний"

#: src/pages/build/BuildDetail.tsx:221
#~ msgid "Edit build order"
#~ msgstr "Edit build order"

#: src/pages/build/BuildDetail.tsx:226
#~ msgid "Duplicate build order"
#~ msgstr "Duplicate build order"

#: src/pages/build/BuildDetail.tsx:231
#~ msgid "Delete build order"
#~ msgstr "Delete build order"

#: src/pages/build/BuildDetail.tsx:238
#: src/pages/purchasing/PurchaseOrderDetail.tsx:123
#: src/pages/sales/ReturnOrderDetail.tsx:88
#: src/pages/sales/SalesOrderDetail.tsx:97
#: src/tables/ColumnRenderers.tsx:312
#: src/tables/build/BuildAllocatedStockTable.tsx:115
#: src/tables/build/BuildLineTable.tsx:337
msgid "Reference"
msgstr "Ссылка"

#: src/pages/build/BuildDetail.tsx:252
msgid "Parent Build"
msgstr "Родительский заказ"

#: src/pages/build/BuildDetail.tsx:263
msgid "Build Quantity"
msgstr "Количество производимых деталей"

#: src/pages/build/BuildDetail.tsx:269
#: src/pages/part/PartDetail.tsx:598
#: src/tables/bom/BomTable.tsx:347
#: src/tables/bom/BomTable.tsx:382
msgid "Can Build"
msgstr "Можно произвести"

#: src/pages/build/BuildDetail.tsx:278
#: src/pages/build/BuildDetail.tsx:468
msgid "Completed Outputs"
msgstr "Завершённая продукция"

#: src/pages/build/BuildDetail.tsx:295
#: src/tables/Filter.tsx:373
msgid "Issued By"
msgstr "Создал"

#: src/pages/build/BuildDetail.tsx:303
#: src/pages/part/PartDetail.tsx:691
#: src/pages/purchasing/PurchaseOrderDetail.tsx:243
#: src/pages/sales/ReturnOrderDetail.tsx:207
#: src/pages/sales/SalesOrderDetail.tsx:219
#: src/tables/Filter.tsx:311
msgid "Responsible"
msgstr "Ответственный"

#: src/pages/build/BuildDetail.tsx:321
msgid "Any location"
msgstr "Любое расположение"

#: src/pages/build/BuildDetail.tsx:328
msgid "Destination Location"
msgstr "Место назначения"

#: src/pages/build/BuildDetail.tsx:344
#: src/tables/settings/ApiTokenTable.tsx:98
#: src/tables/settings/PendingTasksTable.tsx:41
msgid "Created"
msgstr "Создано"

#: src/pages/build/BuildDetail.tsx:347
#: src/pages/part/PartDetail.tsx:727
#~ msgid "Test Statistics"
#~ msgstr "Test Statistics"

#: src/pages/build/BuildDetail.tsx:352
#: src/pages/purchasing/PurchaseOrderDetail.tsx:268
#: src/pages/sales/ReturnOrderDetail.tsx:233
#: src/pages/sales/SalesOrderDetail.tsx:244
#: src/tables/ColumnRenderers.tsx:424
msgid "Start Date"
msgstr "Начальная дата"

#: src/pages/build/BuildDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:276
#: src/pages/sales/ReturnOrderDetail.tsx:241
#: src/pages/sales/SalesOrderDetail.tsx:252
#: src/tables/ColumnRenderers.tsx:432
#: src/tables/part/PartPurchaseOrdersTable.tsx:101
#: src/tables/sales/ReturnOrderLineItemTable.tsx:150
#: src/tables/sales/SalesOrderLineItemTable.tsx:130
msgid "Target Date"
msgstr "Целевая дата"

#: src/pages/build/BuildDetail.tsx:368
#: src/tables/build/BuildOrderTable.tsx:92
#: src/tables/sales/SalesOrderLineItemTable.tsx:324
msgid "Completed"
msgstr "Завершено"

#: src/pages/build/BuildDetail.tsx:368
#~ msgid "Reporting Actions"
#~ msgstr "Reporting Actions"

#: src/pages/build/BuildDetail.tsx:374
#~ msgid "Print build report"
#~ msgstr "Print build report"

#: src/pages/build/BuildDetail.tsx:404
msgid "Build Details"
msgstr "Сведения о заказе"

#: src/pages/build/BuildDetail.tsx:410
msgid "Required Parts"
msgstr "Необходимые детали"

#: src/pages/build/BuildDetail.tsx:422
#: src/pages/sales/SalesOrderDetail.tsx:380
#: src/pages/sales/SalesOrderShipmentDetail.tsx:210
#: src/tables/part/PartSalesAllocationsTable.tsx:73
msgid "Allocated Stock"
msgstr "Зарезервированные остатки"

#: src/pages/build/BuildDetail.tsx:438
msgid "Consumed Stock"
msgstr "Израсходованные остатки"

#: src/pages/build/BuildDetail.tsx:455
msgid "Incomplete Outputs"
msgstr "Незавершённая продукция"

#: src/pages/build/BuildDetail.tsx:483
msgid "External Orders"
msgstr "Сторонние заказы"

#: src/pages/build/BuildDetail.tsx:497
msgid "Child Build Orders"
msgstr "Дочерние заказы на сборку"

#: src/pages/build/BuildDetail.tsx:507
#: src/tables/build/BuildOutputTable.tsx:664
#: src/tables/stock/StockItemTestResultTable.tsx:167
msgid "Test Results"
msgstr "Результаты тестов"

#: src/pages/build/BuildDetail.tsx:544
msgid "Edit Build Order"
msgstr "Редактировать заказ на производство"

#: src/pages/build/BuildDetail.tsx:566
#: src/tables/build/BuildOrderTable.tsx:208
#: src/tables/build/BuildOrderTable.tsx:224
msgid "Add Build Order"
msgstr "Создать заказ на сборку"

#: src/pages/build/BuildDetail.tsx:576
msgid "Cancel Build Order"
msgstr "Отменить заказ для производства"

#: src/pages/build/BuildDetail.tsx:578
#: src/pages/purchasing/PurchaseOrderDetail.tsx:398
#: src/pages/sales/ReturnOrderDetail.tsx:393
#: src/pages/sales/SalesOrderDetail.tsx:427
msgid "Order cancelled"
msgstr "Заказ отменён"

#: src/pages/build/BuildDetail.tsx:579
#: src/pages/purchasing/PurchaseOrderDetail.tsx:397
#: src/pages/sales/ReturnOrderDetail.tsx:392
#: src/pages/sales/SalesOrderDetail.tsx:426
msgid "Cancel this order"
msgstr "Отменить заказ"

#: src/pages/build/BuildDetail.tsx:588
msgid "Hold Build Order"
msgstr "Отложить заказ на сборку"

#: src/pages/build/BuildDetail.tsx:590
#: src/pages/purchasing/PurchaseOrderDetail.tsx:405
#: src/pages/sales/ReturnOrderDetail.tsx:400
#: src/pages/sales/SalesOrderDetail.tsx:434
msgid "Place this order on hold"
msgstr "Отложите этот заказ"

#: src/pages/build/BuildDetail.tsx:591
#: src/pages/purchasing/PurchaseOrderDetail.tsx:406
#: src/pages/sales/ReturnOrderDetail.tsx:401
#: src/pages/sales/SalesOrderDetail.tsx:435
msgid "Order placed on hold"
msgstr "Заказ отложен"

#: src/pages/build/BuildDetail.tsx:596
msgid "Issue Build Order"
msgstr "Оформить заказ на сборку"

#: src/pages/build/BuildDetail.tsx:598
#: src/pages/purchasing/PurchaseOrderDetail.tsx:389
#: src/pages/sales/ReturnOrderDetail.tsx:384
#: src/pages/sales/SalesOrderDetail.tsx:418
msgid "Issue this order"
msgstr "Оформить данный заказ"

#: src/pages/build/BuildDetail.tsx:599
#: src/pages/purchasing/PurchaseOrderDetail.tsx:390
#: src/pages/sales/ReturnOrderDetail.tsx:385
#: src/pages/sales/SalesOrderDetail.tsx:419
msgid "Order issued"
msgstr "Заказ оформлен"

#: src/pages/build/BuildDetail.tsx:618
msgid "Complete Build Order"
msgstr "Завершить заказ на сборку"

#: src/pages/build/BuildDetail.tsx:624
#: src/pages/purchasing/PurchaseOrderDetail.tsx:418
#: src/pages/sales/ReturnOrderDetail.tsx:408
#: src/pages/sales/SalesOrderDetail.tsx:453
msgid "Mark this order as complete"
msgstr "Отметить данный заказ как завершённый"

#: src/pages/build/BuildDetail.tsx:627
#: src/pages/purchasing/PurchaseOrderDetail.tsx:412
#: src/pages/sales/ReturnOrderDetail.tsx:409
#: src/pages/sales/SalesOrderDetail.tsx:454
msgid "Order completed"
msgstr "Заказ завершён"

#: src/pages/build/BuildDetail.tsx:654
#: src/pages/purchasing/PurchaseOrderDetail.tsx:441
#: src/pages/sales/ReturnOrderDetail.tsx:438
#: src/pages/sales/SalesOrderDetail.tsx:489
msgid "Issue Order"
msgstr "Оформить заказ"

#: src/pages/build/BuildDetail.tsx:661
#: src/pages/purchasing/PurchaseOrderDetail.tsx:448
#: src/pages/sales/ReturnOrderDetail.tsx:445
#: src/pages/sales/SalesOrderDetail.tsx:503
msgid "Complete Order"
msgstr "Завершить заказ"

#: src/pages/build/BuildDetail.tsx:679
msgid "Build Order Actions"
msgstr "Действия с заказом на сборку"

#: src/pages/build/BuildDetail.tsx:684
#: src/pages/purchasing/PurchaseOrderDetail.tsx:470
#: src/pages/sales/ReturnOrderDetail.tsx:467
#: src/pages/sales/SalesOrderDetail.tsx:526
msgid "Edit order"
msgstr "Редактировать заказ"

#: src/pages/build/BuildDetail.tsx:688
#: src/pages/purchasing/PurchaseOrderDetail.tsx:478
#: src/pages/sales/ReturnOrderDetail.tsx:473
#: src/pages/sales/SalesOrderDetail.tsx:531
msgid "Duplicate order"
msgstr "Дублировать заказ"

#: src/pages/build/BuildDetail.tsx:692
#: src/pages/purchasing/PurchaseOrderDetail.tsx:481
#: src/pages/sales/ReturnOrderDetail.tsx:478
#: src/pages/sales/SalesOrderDetail.tsx:534
msgid "Hold order"
msgstr "Отложить заказ"

#: src/pages/build/BuildDetail.tsx:697
#: src/pages/purchasing/PurchaseOrderDetail.tsx:486
#: src/pages/sales/ReturnOrderDetail.tsx:483
#: src/pages/sales/SalesOrderDetail.tsx:539
msgid "Cancel order"
msgstr "Отменить заказ"

#: src/pages/build/BuildDetail.tsx:735
#: src/pages/stock/StockDetail.tsx:341
#: src/tables/build/BuildAllocatedStockTable.tsx:85
#: src/tables/part/PartBuildAllocationsTable.tsx:45
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:150
#: src/tables/stock/StockTrackingTable.tsx:109
msgid "Build Order"
msgstr "Заказ на сборку"

#: src/pages/build/BuildIndex.tsx:23
#~ msgid "Build order created"
#~ msgstr "Build order created"

#: src/pages/build/BuildIndex.tsx:29
#: src/tables/build/BuildOrderTable.tsx:185
msgid "Show external build orders"
msgstr "Показать сторонние заказы на сборку"

#: src/pages/build/BuildIndex.tsx:39
#~ msgid "New Build Order"
#~ msgstr "New Build Order"

#: src/pages/build/BuildIndex.tsx:83
#: src/pages/purchasing/PurchasingIndex.tsx:69
#: src/pages/sales/SalesIndex.tsx:90
#: src/pages/sales/SalesIndex.tsx:111
msgid "Table View"
msgstr "В виде таблицы"

#: src/pages/build/BuildIndex.tsx:86
#: src/pages/purchasing/PurchasingIndex.tsx:72
#: src/pages/sales/SalesIndex.tsx:93
#: src/pages/sales/SalesIndex.tsx:114
msgid "Calendar View"
msgstr "В виде календаря"

#: src/pages/company/CompanyDetail.tsx:99
msgid "Website"
msgstr "Веб-сайт"

#: src/pages/company/CompanyDetail.tsx:107
msgid "Phone Number"
msgstr "Номер телефона"

#: src/pages/company/CompanyDetail.tsx:114
msgid "Email Address"
msgstr "Адрес электронной почты"

#: src/pages/company/CompanyDetail.tsx:121
msgid "Tax ID"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:131
msgid "Default Currency"
msgstr "Валюта по умолчанию"

#: src/pages/company/CompanyDetail.tsx:136
#: src/pages/company/SupplierDetail.tsx:8
#: src/pages/company/SupplierPartDetail.tsx:129
#: src/pages/company/SupplierPartDetail.tsx:235
#: src/pages/company/SupplierPartDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:138
#: src/tables/Filter.tsx:352
#: src/tables/company/CompanyTable.tsx:96
#: src/tables/part/PartPurchaseOrdersTable.tsx:43
#: src/tables/purchasing/PurchaseOrderTable.tsx:109
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:40
msgid "Supplier"
msgstr "Поставщик"

#: src/pages/company/CompanyDetail.tsx:142
#: src/pages/company/ManufacturerDetail.tsx:8
#: src/pages/company/ManufacturerPartDetail.tsx:102
#: src/pages/company/ManufacturerPartDetail.tsx:264
#: src/pages/company/SupplierPartDetail.tsx:151
#: src/tables/Filter.tsx:339
#: src/tables/company/CompanyTable.tsx:101
#: src/tables/purchasing/SupplierPartTable.tsx:70
msgid "Manufacturer"
msgstr "Производитель"

#: src/pages/company/CompanyDetail.tsx:148
#: src/pages/company/CustomerDetail.tsx:8
#: src/pages/part/pricing/SaleHistoryPanel.tsx:31
#: src/pages/sales/ReturnOrderDetail.tsx:103
#: src/pages/sales/SalesOrderDetail.tsx:112
#: src/pages/sales/SalesOrderShipmentDetail.tsx:102
#: src/pages/stock/StockDetail.tsx:367
#: src/tables/company/CompanyTable.tsx:106
#: src/tables/sales/ReturnOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:153
msgid "Customer"
msgstr "Покупатель"

#: src/pages/company/CompanyDetail.tsx:175
#~ msgid "Edit company"
#~ msgstr "Edit company"

#: src/pages/company/CompanyDetail.tsx:181
msgid "Company Details"
msgstr "Сведения о компании"

#: src/pages/company/CompanyDetail.tsx:187
msgid "Supplied Parts"
msgstr "Поставляемые детали"

#: src/pages/company/CompanyDetail.tsx:189
#~ msgid "Delete company"
#~ msgstr "Delete company"

#: src/pages/company/CompanyDetail.tsx:196
msgid "Manufactured Parts"
msgstr "Детали производителя"

#: src/pages/company/CompanyDetail.tsx:243
msgid "Assigned Stock"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:284
#: src/tables/company/CompanyTable.tsx:82
msgid "Edit Company"
msgstr "Редактирование компании"

#: src/pages/company/CompanyDetail.tsx:292
msgid "Delete Company"
msgstr "Удалить компанию"

#: src/pages/company/CompanyDetail.tsx:307
msgid "Company Actions"
msgstr "Действия с компанией"

#: src/pages/company/ManufacturerPartDetail.tsx:76
#: src/pages/company/SupplierPartDetail.tsx:88
msgid "Internal Part"
msgstr "Деталь"

#: src/pages/company/ManufacturerPartDetail.tsx:110
#: src/pages/company/SupplierPartDetail.tsx:160
msgid "Manufacturer Part Number"
msgstr "Артикул производителя"

#: src/pages/company/ManufacturerPartDetail.tsx:127
#: src/pages/company/SupplierPartDetail.tsx:112
msgid "External Link"
msgstr "Внешняя ссылка"

#: src/pages/company/ManufacturerPartDetail.tsx:146
#: src/pages/company/SupplierPartDetail.tsx:232
#: src/pages/part/PartDetail.tsx:787
msgid "Part Details"
msgstr "Сведения о детали"

#: src/pages/company/ManufacturerPartDetail.tsx:149
msgid "Manufacturer Details"
msgstr "Сведения о производителе"

#: src/pages/company/ManufacturerPartDetail.tsx:158
msgid "Manufacturer Part Details"
msgstr "Сведения о детали производителя"

#: src/pages/company/ManufacturerPartDetail.tsx:164
#: src/pages/part/PartDetail.tsx:793
msgid "Parameters"
msgstr "Параметры"

#: src/pages/company/ManufacturerPartDetail.tsx:204
#: src/tables/purchasing/ManufacturerPartTable.tsx:84
msgid "Edit Manufacturer Part"
msgstr "Редактировать делать производителя"

#: src/pages/company/ManufacturerPartDetail.tsx:211
#: src/tables/purchasing/ManufacturerPartTable.tsx:72
#: src/tables/purchasing/ManufacturerPartTable.tsx:104
msgid "Add Manufacturer Part"
msgstr "Создать деталь производителя"

#: src/pages/company/ManufacturerPartDetail.tsx:223
#: src/tables/purchasing/ManufacturerPartTable.tsx:92
msgid "Delete Manufacturer Part"
msgstr "Удалить деталь производителя"

#: src/pages/company/ManufacturerPartDetail.tsx:238
msgid "Manufacturer Part Actions"
msgstr "Действия с деталью производителя"

#: src/pages/company/ManufacturerPartDetail.tsx:281
msgid "ManufacturerPart"
msgstr "Деталь производителя"

#: src/pages/company/SupplierPartDetail.tsx:103
#: src/tables/part/RelatedPartTable.tsx:82
msgid "Part Description"
msgstr "Описание детали"

#: src/pages/company/SupplierPartDetail.tsx:179
#: src/tables/part/PartPurchaseOrdersTable.tsx:73
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:184
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:226
#: src/tables/purchasing/SupplierPartTable.tsx:120
msgid "Pack Quantity"
msgstr "Количество в упаковке"

#: src/pages/company/SupplierPartDetail.tsx:197
#: src/pages/company/SupplierPartDetail.tsx:390
#: src/pages/part/PartDetail.tsx:1000
#: src/tables/bom/BomTable.tsx:414
#: src/tables/part/PartTable.tsx:95
msgid "On Order"
msgstr "В заказе"

#: src/pages/company/SupplierPartDetail.tsx:204
msgid "Supplier Availability"
msgstr "Наличие у поставщика"

#: src/pages/company/SupplierPartDetail.tsx:212
msgid "Availability Updated"
msgstr "Наличие обновлено"

#: src/pages/company/SupplierPartDetail.tsx:237
msgid "Availability"
msgstr "Наличие"

#: src/pages/company/SupplierPartDetail.tsx:246
msgid "Supplier Part Details"
msgstr "Сведения о детали поставщика"

#: src/pages/company/SupplierPartDetail.tsx:252
#: src/pages/purchasing/PurchaseOrderDetail.tsx:361
msgid "Received Stock"
msgstr "Полученные позиции"

#: src/pages/company/SupplierPartDetail.tsx:279
#: src/pages/part/PartPricingPanel.tsx:113
#: src/pages/part/pricing/PricingOverviewPanel.tsx:232
msgid "Supplier Pricing"
msgstr "Цены закупок"

#: src/pages/company/SupplierPartDetail.tsx:304
msgid "Supplier Part Actions"
msgstr "Действия с деталью поставщика"

#: src/pages/company/SupplierPartDetail.tsx:328
#: src/tables/purchasing/SupplierPartTable.tsx:207
msgid "Edit Supplier Part"
msgstr "Редактировать деталь поставщика"

#: src/pages/company/SupplierPartDetail.tsx:336
#: src/tables/purchasing/SupplierPartTable.tsx:215
msgid "Delete Supplier Part"
msgstr "Удалить деталь поставщика"

#: src/pages/company/SupplierPartDetail.tsx:344
#: src/tables/purchasing/SupplierPartTable.tsx:154
msgid "Add Supplier Part"
msgstr "Создать деталь поставщика"

#: src/pages/company/SupplierPartDetail.tsx:384
#: src/pages/part/PartDetail.tsx:988
msgid "No Stock"
msgstr "Нет на складе"

#: src/pages/core/CoreIndex.tsx:46
#: src/pages/core/GroupDetail.tsx:81
#: src/pages/core/UserDetail.tsx:224
msgid "System Overview"
msgstr "Обзор системы"

#: src/pages/core/GroupDetail.tsx:45
msgid "Group Name"
msgstr "Название группы"

#: src/pages/core/GroupDetail.tsx:52
#: src/pages/core/GroupDetail.tsx:67
#: src/tables/settings/GroupTable.tsx:85
msgid "Group Details"
msgstr "Сведения о группе"

#: src/pages/core/GroupDetail.tsx:55
#: src/tables/settings/GroupTable.tsx:112
msgid "Group Roles"
msgstr "Роли группы"

#: src/pages/core/UserDetail.tsx:175
msgid "User Information"
msgstr "Информация о пользователе"

#: src/pages/core/UserDetail.tsx:176
msgid "User Permissions"
msgstr "Права пользователя"

#: src/pages/core/UserDetail.tsx:178
msgid "User Profile"
msgstr "Профиль пользователя"

#: src/pages/core/UserDetail.tsx:188
#: src/tables/settings/UserTable.tsx:164
msgid "User Details"
msgstr "Сведения о пользователе"

#: src/pages/core/UserDetail.tsx:206
msgid "Basic user"
msgstr "Базовый пользователь"

#: src/pages/part/CategoryDetail.tsx:98
#: src/pages/stock/LocationDetail.tsx:96
#: src/tables/settings/ErrorTable.tsx:63
#: src/tables/settings/ErrorTable.tsx:108
msgid "Path"
msgstr "Путь"

#: src/pages/part/CategoryDetail.tsx:114
msgid "Parent Category"
msgstr "Родительская категория"

#: src/pages/part/CategoryDetail.tsx:137
#: src/pages/part/CategoryDetail.tsx:267
msgid "Subcategories"
msgstr "Подкатегории"

#: src/pages/part/CategoryDetail.tsx:144
#: src/pages/stock/LocationDetail.tsx:136
#: src/tables/part/PartCategoryTable.tsx:89
#: src/tables/stock/StockLocationTable.tsx:43
msgid "Structural"
msgstr "Структура"

#: src/pages/part/CategoryDetail.tsx:150
msgid "Parent default location"
msgstr "Расположение по умолчанию"

#: src/pages/part/CategoryDetail.tsx:157
msgid "Default location"
msgstr "Место хранения по-умолчанию"

#: src/pages/part/CategoryDetail.tsx:168
msgid "Top level part category"
msgstr "Категория детали верхнего уровня"

#: src/pages/part/CategoryDetail.tsx:178
#: src/pages/part/CategoryDetail.tsx:244
#: src/tables/part/PartCategoryTable.tsx:122
msgid "Edit Part Category"
msgstr "Редактировать категорию деталей"

#: src/pages/part/CategoryDetail.tsx:187
msgid "Move items to parent category"
msgstr "Перенести элементы в родительскую категорию"

#: src/pages/part/CategoryDetail.tsx:191
#: src/pages/stock/LocationDetail.tsx:228
msgid "Delete items"
msgstr "Удалить товар"

#: src/pages/part/CategoryDetail.tsx:199
#: src/pages/part/CategoryDetail.tsx:249
msgid "Delete Part Category"
msgstr "Удалить категорию деталей"

#: src/pages/part/CategoryDetail.tsx:202
msgid "Parts Action"
msgstr "Действие с деталями"

#: src/pages/part/CategoryDetail.tsx:203
msgid "Action for parts in this category"
msgstr "Что делать с деталями этой категории"

#: src/pages/part/CategoryDetail.tsx:208
msgid "Child Categories Action"
msgstr "Действие с дочерними категориями"

#: src/pages/part/CategoryDetail.tsx:209
msgid "Action for child categories in this category"
msgstr "Что делать с дочерними категориями этой категории"

#: src/pages/part/CategoryDetail.tsx:240
#: src/tables/part/PartCategoryTable.tsx:143
msgid "Category Actions"
msgstr "Действия с категорией"

#: src/pages/part/CategoryDetail.tsx:261
msgid "Category Details"
msgstr "Сведения о категории"

#: src/pages/part/PartAllocationPanel.tsx:21
#: src/pages/stock/StockDetail.tsx:539
#: src/tables/part/PartTable.tsx:108
msgid "Build Order Allocations"
msgstr "Резервирование в заказах на сборку"

#: src/pages/part/PartAllocationPanel.tsx:31
#: src/pages/stock/StockDetail.tsx:554
#: src/tables/part/PartTable.tsx:116
msgid "Sales Order Allocations"
msgstr "Резервирование в заказах на продажу"

#: src/pages/part/PartDetail.tsx:181
#: src/pages/part/PartDetail.tsx:184
#: src/pages/part/PartDetail.tsx:228
msgid "Validate BOM"
msgstr "Утвердить спецификацию"

#: src/pages/part/PartDetail.tsx:185
msgid "Do you want to validate the bill of materials for this assembly?"
msgstr "Вы хотите утвердить спецификацию для данной сборочной детали?"

#: src/pages/part/PartDetail.tsx:188
msgid "BOM validated"
msgstr "Спецификация утверждена"

#: src/pages/part/PartDetail.tsx:206
msgid "BOM Validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:207
msgid "The Bill of Materials for this part has been validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:211
#: src/pages/part/PartDetail.tsx:216
msgid "BOM Not Validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:212
msgid "The Bill of Materials for this part has previously been checked, but requires revalidation"
msgstr ""

#: src/pages/part/PartDetail.tsx:217
msgid "The Bill of Materials for this part has not yet been validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:248
msgid "Validated On"
msgstr ""

#: src/pages/part/PartDetail.tsx:253
msgid "Validated By"
msgstr ""

#: src/pages/part/PartDetail.tsx:286
#~ msgid "Variant Stock"
#~ msgstr "Variant Stock"

#: src/pages/part/PartDetail.tsx:310
#~ msgid "Edit part"
#~ msgstr "Edit part"

#: src/pages/part/PartDetail.tsx:322
#~ msgid "Duplicate part"
#~ msgstr "Duplicate part"

#: src/pages/part/PartDetail.tsx:327
#~ msgid "Delete part"
#~ msgstr "Delete part"

#: src/pages/part/PartDetail.tsx:467
msgid "Variant of"
msgstr "Разновидность детали"

#: src/pages/part/PartDetail.tsx:474
msgid "Revision of"
msgstr "Ревизия"

#: src/pages/part/PartDetail.tsx:494
#: src/tables/ColumnRenderers.tsx:200
#: src/tables/ColumnRenderers.tsx:209
msgid "Default Location"
msgstr "Расположение по умолчанию"

#: src/pages/part/PartDetail.tsx:501
msgid "Category Default Location"
msgstr ""

#: src/pages/part/PartDetail.tsx:508
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:48
msgid "Units"
msgstr "Единица измерения"

#: src/pages/part/PartDetail.tsx:510
#~ msgid "Stocktake By"
#~ msgstr "Stocktake By"

#: src/pages/part/PartDetail.tsx:515
#: src/tables/settings/PendingTasksTable.tsx:51
msgid "Keywords"
msgstr "Ключевые слова"

#: src/pages/part/PartDetail.tsx:542
#: src/tables/bom/BomTable.tsx:409
#: src/tables/build/BuildLineTable.tsx:297
#: src/tables/part/PartTable.tsx:306
#: src/tables/sales/SalesOrderLineItemTable.tsx:134
msgid "Available Stock"
msgstr "Доступно"

#: src/pages/part/PartDetail.tsx:548
#: src/tables/bom/BomTable.tsx:323
#: src/tables/build/BuildLineTable.tsx:259
#: src/tables/sales/SalesOrderLineItemTable.tsx:172
msgid "On order"
msgstr "В заказе"

#: src/pages/part/PartDetail.tsx:555
msgid "Required for Orders"
msgstr "Требуется для заказов"

#: src/pages/part/PartDetail.tsx:566
msgid "Allocated to Build Orders"
msgstr "Зарезервировано в заказах на сборку"

#: src/pages/part/PartDetail.tsx:578
msgid "Allocated to Sales Orders"
msgstr "Зарезервировано в заказах на продажу"

#: src/pages/part/PartDetail.tsx:587
#: src/pages/part/PartDetail.tsx:1006
#: src/pages/stock/StockDetail.tsx:896
#: src/tables/build/BuildOrderTestTable.tsx:273
#: src/tables/stock/StockItemTable.tsx:364
msgid "In Production"
msgstr "В производстве"

#: src/pages/part/PartDetail.tsx:605
msgid "Minimum Stock"
msgstr "Минимальный запас"

#: src/pages/part/PartDetail.tsx:613
#~ msgid "Scheduling"
#~ msgstr "Scheduling"

#: src/pages/part/PartDetail.tsx:620
#: src/tables/part/ParametricPartTable.tsx:355
#: src/tables/part/PartTable.tsx:190
msgid "Locked"
msgstr "Заблокировано"

#: src/pages/part/PartDetail.tsx:626
msgid "Template Part"
msgstr "Шаблон детали"

#: src/pages/part/PartDetail.tsx:631
#: src/tables/bom/BomTable.tsx:404
msgid "Assembled Part"
msgstr "Сборная деталь"

#: src/pages/part/PartDetail.tsx:636
msgid "Component Part"
msgstr "Компонент для сборки"

#: src/pages/part/PartDetail.tsx:641
#: src/tables/bom/BomTable.tsx:394
msgid "Testable Part"
msgstr "Тестируемая деталь"

#: src/pages/part/PartDetail.tsx:647
#: src/tables/bom/BomTable.tsx:399
msgid "Trackable Part"
msgstr "Отслеживаемая деталь"

#: src/pages/part/PartDetail.tsx:652
msgid "Purchaseable Part"
msgstr "Можно закупать"

#: src/pages/part/PartDetail.tsx:658
msgid "Saleable Part"
msgstr "Можно продавать"

#: src/pages/part/PartDetail.tsx:663
msgid "Virtual Part"
msgstr "Виртуальная деталь"

#: src/pages/part/PartDetail.tsx:678
#: src/pages/purchasing/PurchaseOrderDetail.tsx:253
#: src/pages/sales/ReturnOrderDetail.tsx:217
#: src/pages/sales/SalesOrderDetail.tsx:229
#: src/tables/ColumnRenderers.tsx:440
msgid "Creation Date"
msgstr "Дата создания"

#: src/pages/part/PartDetail.tsx:683
#: src/tables/ColumnRenderers.tsx:386
#: src/tables/Filter.tsx:365
msgid "Created By"
msgstr "Создал"

#: src/pages/part/PartDetail.tsx:698
msgid "Default Supplier"
msgstr "Поставщик по умолчанию"

#: src/pages/part/PartDetail.tsx:704
msgid "Default Expiry"
msgstr "Срок годности по умолчанию"

#: src/pages/part/PartDetail.tsx:709
msgid "days"
msgstr "дней"

#: src/pages/part/PartDetail.tsx:719
#: src/pages/part/pricing/BomPricingPanel.tsx:113
#: src/pages/part/pricing/VariantPricingPanel.tsx:95
#: src/tables/part/PartTable.tsx:166
msgid "Price Range"
msgstr "Ценовой диапазон"

#: src/pages/part/PartDetail.tsx:729
msgid "Latest Serial Number"
msgstr "Последний серийный номер"

#: src/pages/part/PartDetail.tsx:757
msgid "Select Part Revision"
msgstr ""

#: src/pages/part/PartDetail.tsx:822
msgid "Variants"
msgstr "Разновидности"

#: src/pages/part/PartDetail.tsx:829
#: src/pages/stock/StockDetail.tsx:526
msgid "Allocations"
msgstr "Резервирование"

#: src/pages/part/PartDetail.tsx:836
msgid "Bill of Materials"
msgstr "Спецификация"

#: src/pages/part/PartDetail.tsx:848
msgid "Used In"
msgstr "Используется в"

#: src/pages/part/PartDetail.tsx:855
msgid "Part Pricing"
msgstr "Цены на деталь"

#: src/pages/part/PartDetail.tsx:923
msgid "Test Templates"
msgstr "Шаблоны тестов"

#: src/pages/part/PartDetail.tsx:934
msgid "Related Parts"
msgstr "Связанные детали"

#: src/pages/part/PartDetail.tsx:956
#~ msgid "Count part stock"
#~ msgstr "Count part stock"

#: src/pages/part/PartDetail.tsx:967
#~ msgid "Transfer part stock"
#~ msgstr "Transfer part stock"

#: src/pages/part/PartDetail.tsx:994
#: src/tables/part/PartTestTemplateTable.tsx:112
#: src/tables/stock/StockItemTestResultTable.tsx:401
msgid "Required"
msgstr "Требуется"

#: src/pages/part/PartDetail.tsx:1025
#: src/tables/part/PartTable.tsx:355
msgid "Edit Part"
msgstr "Редактировать деталь"

#: src/pages/part/PartDetail.tsx:1065
#: src/tables/part/PartTable.tsx:343
#: src/tables/part/PartTable.tsx:420
msgid "Add Part"
msgstr "Создать деталь"

#: src/pages/part/PartDetail.tsx:1079
msgid "Delete Part"
msgstr "Удалить деталь"

#: src/pages/part/PartDetail.tsx:1088
msgid "Deleting this part cannot be reversed"
msgstr "Удаление этой детали нельзя отменить"

#: src/pages/part/PartDetail.tsx:1149
#: src/pages/stock/StockDetail.tsx:854
msgid "Order"
msgstr "Закупить"

#: src/pages/part/PartDetail.tsx:1150
#: src/pages/stock/StockDetail.tsx:855
#: src/tables/build/BuildLineTable.tsx:740
msgid "Order Stock"
msgstr "Закупить на склад"

#: src/pages/part/PartDetail.tsx:1162
msgid "Search by serial number"
msgstr "Поиск по серийному номеру"

#: src/pages/part/PartDetail.tsx:1170
#: src/tables/part/PartTable.tsx:392
msgid "Part Actions"
msgstr "Действия с деталью"

#: src/pages/part/PartIndex.tsx:29
#~ msgid "Categories"
#~ msgstr "Categories"

#: src/pages/part/PartPricingPanel.tsx:72
msgid "No pricing data found for this part."
msgstr "Для этой детали не найдены данные о ценах."

#: src/pages/part/PartPricingPanel.tsx:87
#: src/pages/part/pricing/PricingOverviewPanel.tsx:325
msgid "Pricing Overview"
msgstr "Обзор цен"

#: src/pages/part/PartPricingPanel.tsx:93
msgid "Purchase History"
msgstr "История закупок"

#: src/pages/part/PartPricingPanel.tsx:107
#: src/pages/part/pricing/PricingOverviewPanel.tsx:204
msgid "Internal Pricing"
msgstr "Внутренние цены"

#: src/pages/part/PartPricingPanel.tsx:122
#: src/pages/part/pricing/PricingOverviewPanel.tsx:214
msgid "BOM Pricing"
msgstr "Цена по спецификации"

#: src/pages/part/PartPricingPanel.tsx:129
#: src/pages/part/pricing/PricingOverviewPanel.tsx:242
msgid "Variant Pricing"
msgstr "Цены вариантов"

#: src/pages/part/PartPricingPanel.tsx:141
#: src/pages/part/pricing/PricingOverviewPanel.tsx:251
msgid "Sale Pricing"
msgstr "Цены продаж"

#: src/pages/part/PartPricingPanel.tsx:147
#: src/pages/part/pricing/PricingOverviewPanel.tsx:260
msgid "Sale History"
msgstr "История продаж"

#: src/pages/part/PartSchedulingDetail.tsx:51
#: src/pages/part/PartSchedulingDetail.tsx:291
#~ msgid "Scheduled"
#~ msgstr "Scheduled"

#: src/pages/part/PartSchedulingDetail.tsx:95
#~ msgid "Quantity is speculative"
#~ msgstr "Quantity is speculative"

#: src/pages/part/PartSchedulingDetail.tsx:104
#~ msgid "No date available for provided quantity"
#~ msgstr "No date available for provided quantity"

#: src/pages/part/PartSchedulingDetail.tsx:108
#~ msgid "Date is in the past"
#~ msgstr "Date is in the past"

#: src/pages/part/PartSchedulingDetail.tsx:115
#~ msgid "Scheduled Quantity"
#~ msgstr "Scheduled Quantity"

#: src/pages/part/PartSchedulingDetail.tsx:242
#~ msgid "No information available"
#~ msgstr "No information available"

#: src/pages/part/PartSchedulingDetail.tsx:243
#~ msgid "There is no scheduling information available for the selected part"
#~ msgstr "There is no scheduling information available for the selected part"

#: src/pages/part/PartSchedulingDetail.tsx:278
#~ msgid "Expected Quantity"
#~ msgstr "Expected Quantity"

#: src/pages/part/PartStockHistoryDetail.tsx:80
msgid "Edit Stocktake Entry"
msgstr "Редактировать запись инвентаризации"

#: src/pages/part/PartStockHistoryDetail.tsx:88
msgid "Delete Stocktake Entry"
msgstr "Удалить запись инвентаризации"

#: src/pages/part/PartStockHistoryDetail.tsx:107
#: src/pages/part/PartStockHistoryDetail.tsx:211
#: src/pages/stock/StockDetail.tsx:399
#: src/tables/stock/StockItemTable.tsx:272
msgid "Stock Value"
msgstr "Стоимость склада"

#: src/pages/part/PartStockHistoryDetail.tsx:240
#: src/pages/part/pricing/PricingOverviewPanel.tsx:327
msgid "Minimum Value"
msgstr "Минимальное значение"

#: src/pages/part/PartStockHistoryDetail.tsx:246
#: src/pages/part/pricing/PricingOverviewPanel.tsx:328
msgid "Maximum Value"
msgstr "Максимальное значение"

#: src/pages/part/PartStocktakeDetail.tsx:99
#: src/tables/settings/StocktakeReportTable.tsx:70
#~ msgid "Generate Stocktake Report"
#~ msgstr "Generate Stocktake Report"

#: src/pages/part/PartStocktakeDetail.tsx:104
#: src/tables/settings/StocktakeReportTable.tsx:72
#~ msgid "Stocktake report scheduled"
#~ msgstr "Stocktake report scheduled"

#: src/pages/part/PartStocktakeDetail.tsx:145
#: src/tables/settings/StocktakeReportTable.tsx:78
#~ msgid "New Stocktake Report"
#~ msgstr "New Stocktake Report"

#: src/pages/part/pricing/BomPricingPanel.tsx:87
#: src/pages/part/pricing/BomPricingPanel.tsx:175
#: src/tables/ColumnRenderers.tsx:490
#: src/tables/bom/BomTable.tsx:270
#: src/tables/general/ExtraLineItemTable.tsx:69
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:255
#: src/tables/purchasing/PurchaseOrderTable.tsx:138
#: src/tables/sales/ReturnOrderTable.tsx:139
#: src/tables/sales/SalesOrderLineItemTable.tsx:120
#: src/tables/sales/SalesOrderTable.tsx:175
msgid "Total Price"
msgstr "Общая стоимость"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#: src/pages/part/pricing/BomPricingPanel.tsx:141
#: src/tables/bom/UsedInTable.tsx:54
#: src/tables/part/PartTable.tsx:214
msgid "Component"
msgstr "Компонент"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#~ msgid "Minimum Total Price"
#~ msgstr "Minimum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:115
#: src/pages/part/pricing/VariantPricingPanel.tsx:35
#: src/pages/part/pricing/VariantPricingPanel.tsx:97
msgid "Minimum Price"
msgstr "Минимальная цена"

#: src/pages/part/pricing/BomPricingPanel.tsx:116
#: src/pages/part/pricing/VariantPricingPanel.tsx:43
#: src/pages/part/pricing/VariantPricingPanel.tsx:98
msgid "Maximum Price"
msgstr "Максимальная цена"

#: src/pages/part/pricing/BomPricingPanel.tsx:117
#~ msgid "Maximum Total Price"
#~ msgstr "Maximum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:166
#: src/pages/part/pricing/PriceBreakPanel.tsx:173
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:71
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:126
#: src/pages/part/pricing/SupplierPricingPanel.tsx:66
#: src/pages/stock/StockDetail.tsx:387
#: src/tables/bom/BomTable.tsx:260
#: src/tables/general/ExtraLineItemTable.tsx:61
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:250
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:84
#: src/tables/stock/StockItemTable.tsx:260
msgid "Unit Price"
msgstr "Цена за единицу"

#: src/pages/part/pricing/BomPricingPanel.tsx:256
msgid "Pie Chart"
msgstr "Круговая диаграмма"

#: src/pages/part/pricing/BomPricingPanel.tsx:257
msgid "Bar Chart"
msgstr "Гистограмма"

#: src/pages/part/pricing/PriceBreakPanel.tsx:58
#: src/pages/part/pricing/PriceBreakPanel.tsx:111
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:134
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:162
msgid "Add Price Break"
msgstr "Создать цену со скидкой"

#: src/pages/part/pricing/PriceBreakPanel.tsx:71
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:146
msgid "Edit Price Break"
msgstr "Редактировать цену со скидкой"

#: src/pages/part/pricing/PriceBreakPanel.tsx:81
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:154
msgid "Delete Price Break"
msgstr "Удалить цену со скидкой"

#: src/pages/part/pricing/PriceBreakPanel.tsx:95
msgid "Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:171
msgid "Price"
msgstr "Цена"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:72
msgid "Refreshing pricing data"
msgstr "Обновление данных о ценах"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:92
msgid "Pricing data updated"
msgstr "Данные о ценах обновлены"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:99
msgid "Failed to update pricing data"
msgstr "Ошибка обновления данных о ценах"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:127
msgid "Edit Pricing"
msgstr "Редактировать цены"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:139
msgid "Pricing Category"
msgstr "Категории цен"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:158
msgid "Minimum"
msgstr "Минимум"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:170
msgid "Maximum"
msgstr "Максимум"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:188
msgid "Override Pricing"
msgstr "Переопределённые цены"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:196
msgid "Overall Pricing"
msgstr "Общая цена"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:222
msgid "Purchase Pricing"
msgstr "Закупочные цены"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:281
#: src/pages/stock/StockDetail.tsx:179
#: src/tables/part/PartParameterTable.tsx:121
#: src/tables/stock/StockItemTable.tsx:301
msgid "Last Updated"
msgstr "Последнее обновление"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:285
msgid "Pricing Not Set"
msgstr "Цены не заданы"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:286
msgid "Pricing data has not been calculated for this part"
msgstr "Для этой детали цены рассчитывались"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:290
msgid "Pricing Actions"
msgstr "Действия с ценами"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:293
msgid "Refresh"
msgstr "Обновить"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:294
msgid "Refresh pricing data"
msgstr "Обновить данные о ценах"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:309
msgid "Edit pricing data"
msgstr "Редактировать данные о ценах"

#: src/pages/part/pricing/PricingPanel.tsx:24
msgid "No data available"
msgstr "Нет данных"

#: src/pages/part/pricing/PricingPanel.tsx:65
msgid "No Data"
msgstr "Нет данных"

#: src/pages/part/pricing/PricingPanel.tsx:66
msgid "No pricing data available"
msgstr "Нет данных о ценах"

#: src/pages/part/pricing/PricingPanel.tsx:77
msgid "Loading pricing data"
msgstr "Загрузка данных о ценах"

#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:48
msgid "Purchase Price"
msgstr "Закупочная цена"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#~ msgid "Sale Order"
#~ msgstr "Sale Order"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:44
#: src/pages/part/pricing/SaleHistoryPanel.tsx:87
msgid "Sale Price"
msgstr "Цена продажи"

#: src/pages/part/pricing/SupplierPricingPanel.tsx:69
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:75
msgid "Supplier Price"
msgstr "Цена у поставщика"

#: src/pages/part/pricing/VariantPricingPanel.tsx:29
#: src/pages/part/pricing/VariantPricingPanel.tsx:94
msgid "Variant Part"
msgstr "Разновидности детали"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:89
msgid "Edit Purchase Order"
msgstr "Редактирование заказа на закупку"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:107
#: src/tables/purchasing/PurchaseOrderTable.tsx:154
#: src/tables/purchasing/PurchaseOrderTable.tsx:167
msgid "Add Purchase Order"
msgstr "Создать заказ на закупку"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:129
msgid "Supplier Reference"
msgstr "Номер у поставщика"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:159
#: src/pages/sales/ReturnOrderDetail.tsx:126
#: src/pages/sales/SalesOrderDetail.tsx:130
#~ msgid "Order Currency,"
#~ msgstr "Order Currency,"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:169
#: src/pages/sales/ReturnOrderDetail.tsx:140
#: src/pages/sales/SalesOrderDetail.tsx:143
msgid "Completed Line Items"
msgstr "Завершенные позиции"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:178
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:261
msgid "Destination"
msgstr "Место хранения"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:184
#: src/pages/sales/ReturnOrderDetail.tsx:147
#: src/pages/sales/SalesOrderDetail.tsx:160
msgid "Order Currency"
msgstr "Валюта заказа"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:190
#: src/pages/sales/ReturnOrderDetail.tsx:154
#: src/pages/sales/SalesOrderDetail.tsx:166
msgid "Total Cost"
msgstr "Общая стоимость"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:207
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:191
#~ msgid "Created On"
#~ msgstr "Created On"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:219
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:195
msgid "Contact Email"
msgstr "Электронная почта контакта"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:227
#: src/pages/sales/ReturnOrderDetail.tsx:191
#: src/pages/sales/SalesOrderDetail.tsx:203
msgid "Contact Phone"
msgstr "Телефон контакта"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:260
#: src/pages/sales/ReturnOrderDetail.tsx:225
#: src/pages/sales/SalesOrderDetail.tsx:236
msgid "Issue Date"
msgstr "Дата оформления"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:249
#: src/pages/sales/SalesOrderDetail.tsx:259
#: src/tables/ColumnRenderers.tsx:448
#: src/tables/build/BuildOrderTable.tsx:136
#: src/tables/part/PartPurchaseOrdersTable.tsx:106
msgid "Completion Date"
msgstr "Дата завершения"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:315
#: src/pages/sales/ReturnOrderDetail.tsx:279
#: src/pages/sales/SalesOrderDetail.tsx:325
msgid "Order Details"
msgstr "Сведения о заказе"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:321
#: src/pages/purchasing/PurchaseOrderDetail.tsx:330
#: src/pages/sales/ReturnOrderDetail.tsx:133
#: src/pages/sales/ReturnOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:294
#: src/pages/sales/SalesOrderDetail.tsx:331
#: src/pages/sales/SalesOrderDetail.tsx:340
msgid "Line Items"
msgstr "Позиции"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:344
#: src/pages/sales/ReturnOrderDetail.tsx:308
#: src/pages/sales/SalesOrderDetail.tsx:357
msgid "Extra Line Items"
msgstr "Дополнительные позиции"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:387
msgid "Issue Purchase Order"
msgstr "Оформить заказ на закупку"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:395
msgid "Cancel Purchase Order"
msgstr "Отмена заказа на закупку"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:403
msgid "Hold Purchase Order"
msgstr "Отложить заказ на закупку"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:411
msgid "Complete Purchase Order"
msgstr "Завершить заказ на закупку"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:466
#: src/pages/sales/ReturnOrderDetail.tsx:463
#: src/pages/sales/SalesOrderDetail.tsx:521
msgid "Order Actions"
msgstr "Действия с заказом"

#: src/pages/sales/ReturnOrderDetail.tsx:94
#: src/pages/sales/SalesOrderDetail.tsx:103
#: src/pages/sales/SalesOrderShipmentDetail.tsx:111
#: src/tables/sales/SalesOrderTable.tsx:141
msgid "Customer Reference"
msgstr "Номер у клиента"

#: src/pages/sales/ReturnOrderDetail.tsx:349
#~ msgid "Order canceled"
#~ msgstr "Order canceled"

#: src/pages/sales/ReturnOrderDetail.tsx:355
msgid "Edit Return Order"
msgstr "Редактировать заказ на возврат"

#: src/pages/sales/ReturnOrderDetail.tsx:373
#: src/tables/sales/ReturnOrderTable.tsx:154
#: src/tables/sales/ReturnOrderTable.tsx:167
msgid "Add Return Order"
msgstr "Создать заказ на возврат"

#: src/pages/sales/ReturnOrderDetail.tsx:382
msgid "Issue Return Order"
msgstr "Оформить заказ на возврат"

#: src/pages/sales/ReturnOrderDetail.tsx:390
msgid "Cancel Return Order"
msgstr "Отменить заказ на возврат"

#: src/pages/sales/ReturnOrderDetail.tsx:398
msgid "Hold Return Order"
msgstr "Отложить заказ на возврат"

#: src/pages/sales/ReturnOrderDetail.tsx:406
msgid "Complete Return Order"
msgstr "Завершить заказ на возврат"

#: src/pages/sales/SalesOrderDetail.tsx:152
msgid "Completed Shipments"
msgstr "Доставлено"

#: src/pages/sales/SalesOrderDetail.tsx:256
#~ msgid "Pending Shipments"
#~ msgstr "Pending Shipments"

#: src/pages/sales/SalesOrderDetail.tsx:292
msgid "Edit Sales Order"
msgstr "Редактировать заказ на продажу"

#: src/pages/sales/SalesOrderDetail.tsx:314
#: src/tables/sales/SalesOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:121
msgid "Add Sales Order"
msgstr "Создать заказ на продажу"

#: src/pages/sales/SalesOrderDetail.tsx:374
#: src/tables/sales/SalesOrderTable.tsx:147
msgid "Shipments"
msgstr "Доставка"

#: src/pages/sales/SalesOrderDetail.tsx:416
msgid "Issue Sales Order"
msgstr "Оформить заказ на продажу"

#: src/pages/sales/SalesOrderDetail.tsx:424
msgid "Cancel Sales Order"
msgstr "Отменить заказ на продажу"

#: src/pages/sales/SalesOrderDetail.tsx:432
msgid "Hold Sales Order"
msgstr "Отложить заказ на продажу"

#: src/pages/sales/SalesOrderDetail.tsx:440
msgid "Ship Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:442
msgid "Ship this order?"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:443
msgid "Order shipped"
msgstr "Заказ отгружен"

#: src/pages/sales/SalesOrderDetail.tsx:451
msgid "Complete Sales Order"
msgstr "Завершить заказ на продажу"

#: src/pages/sales/SalesOrderDetail.tsx:496
msgid "Ship Order"
msgstr "Отгрузить заказ"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:119
#: src/tables/sales/SalesOrderShipmentTable.tsx:94
msgid "Shipment Reference"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:126
msgid "Allocated Items"
msgstr "Выбранные запасы"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:135
msgid "Tracking Number"
msgstr "Номер отслеживания"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:143
msgid "Invoice Number"
msgstr "Номер счета"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:151
#: src/tables/ColumnRenderers.tsx:456
#: src/tables/sales/SalesOrderAllocationTable.tsx:179
#: src/tables/sales/SalesOrderShipmentTable.tsx:113
msgid "Shipment Date"
msgstr "Дата отгрузки"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:159
#: src/tables/sales/SalesOrderShipmentTable.tsx:117
msgid "Delivery Date"
msgstr "Дата доставки"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:204
msgid "Shipment Details"
msgstr "Данные отгрузки"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:211
#~ msgid "Assigned Items"
#~ msgstr "Assigned Items"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:242
#: src/pages/sales/SalesOrderShipmentDetail.tsx:334
#: src/tables/sales/SalesOrderShipmentTable.tsx:73
msgid "Edit Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:249
#: src/pages/sales/SalesOrderShipmentDetail.tsx:339
#: src/tables/sales/SalesOrderShipmentTable.tsx:65
msgid "Cancel Shipment"
msgstr "Отменить отгрузку"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:262
#: src/tables/sales/SalesOrderShipmentTable.tsx:81
#: src/tables/sales/SalesOrderShipmentTable.tsx:144
msgid "Complete Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:279
#: src/tables/part/PartPurchaseOrdersTable.tsx:122
msgid "Pending"
msgstr "В обработке"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:285
#: src/tables/sales/SalesOrderShipmentTable.tsx:106
#: src/tables/sales/SalesOrderShipmentTable.tsx:190
msgid "Shipped"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:291
#: src/tables/sales/SalesOrderShipmentTable.tsx:195
#: src/tables/settings/EmailTable.tsx:84
msgid "Delivered"
msgstr "Доставлено"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:306
msgid "Send Shipment"
msgstr "Отправить отгрузку"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:329
msgid "Shipment Actions"
msgstr "Действия с отгрузкой"

#: src/pages/stock/LocationDetail.tsx:112
msgid "Parent Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:130
msgid "Sublocations"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:148
#: src/tables/stock/StockLocationTable.tsx:57
msgid "Location Type"
msgstr "Тип места хранения"

#: src/pages/stock/LocationDetail.tsx:159
msgid "Top level stock location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:170
msgid "Location Details"
msgstr "Сведения о месте"

#: src/pages/stock/LocationDetail.tsx:196
msgid "Default Parts"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:215
#: src/pages/stock/LocationDetail.tsx:374
#: src/tables/stock/StockLocationTable.tsx:121
msgid "Edit Stock Location"
msgstr "Редактировать место хранения"

#: src/pages/stock/LocationDetail.tsx:224
msgid "Move items to parent location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:236
#: src/pages/stock/LocationDetail.tsx:379
msgid "Delete Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:239
msgid "Items Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:240
msgid "Action for stock items in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:245
msgid "Child Locations Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:246
msgid "Action for child locations in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:280
msgid "Scan Stock Item"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:298
#: src/pages/stock/StockDetail.tsx:783
msgid "Scanned stock item into location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:304
#: src/pages/stock/StockDetail.tsx:789
msgid "Error scanning stock item"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:311
msgid "Scan Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:323
msgid "Scanned stock location into location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:329
msgid "Error scanning stock location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:370
#: src/tables/stock/StockLocationTable.tsx:142
msgid "Location Actions"
msgstr "Действия с местом хранения"

#: src/pages/stock/StockDetail.tsx:147
msgid "Base Part"
msgstr "Базовая деталь"

#: src/pages/stock/StockDetail.tsx:155
#~ msgid "Link custom barcode to stock item"
#~ msgstr "Link custom barcode to stock item"

#: src/pages/stock/StockDetail.tsx:156
#~ msgid "Completed Tests"
#~ msgstr "Completed Tests"

#: src/pages/stock/StockDetail.tsx:161
#~ msgid "Unlink custom barcode from stock item"
#~ msgstr "Unlink custom barcode from stock item"

#: src/pages/stock/StockDetail.tsx:185
msgid "Last Stocktake"
msgstr "Последняя инвентаризация"

#: src/pages/stock/StockDetail.tsx:203
msgid "Previous serial number"
msgstr "Предыдущий серийный номер"

#: src/pages/stock/StockDetail.tsx:205
#~ msgid "Edit stock item"
#~ msgstr "Edit stock item"

#: src/pages/stock/StockDetail.tsx:217
#~ msgid "Delete stock item"
#~ msgstr "Delete stock item"

#: src/pages/stock/StockDetail.tsx:225
msgid "Find serial number"
msgstr "Поиск по серийному номеру"

#: src/pages/stock/StockDetail.tsx:269
msgid "Allocated to Orders"
msgstr "Зарезервировано в заказах"

#: src/pages/stock/StockDetail.tsx:302
msgid "Installed In"
msgstr ""

#: src/pages/stock/StockDetail.tsx:322
msgid "Parent Item"
msgstr "Родительский элемент"

#: src/pages/stock/StockDetail.tsx:326
msgid "Parent stock item"
msgstr "Запас-родитель"

#: src/pages/stock/StockDetail.tsx:332
msgid "Consumed By"
msgstr ""

#: src/pages/stock/StockDetail.tsx:433
#~ msgid "Duplicate stock item"
#~ msgstr "Duplicate stock item"

#: src/pages/stock/StockDetail.tsx:510
msgid "Stock Details"
msgstr "Сведения о складе"

#: src/pages/stock/StockDetail.tsx:516
msgid "Stock Tracking"
msgstr "Движение остатков"

#: src/pages/stock/StockDetail.tsx:571
msgid "Test Data"
msgstr "Данные тестов"

#: src/pages/stock/StockDetail.tsx:585
msgid "Installed Items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:592
msgid "Child Items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:645
msgid "Edit Stock Item"
msgstr "Редактировать складскую позицию"

#: src/pages/stock/StockDetail.tsx:671
#: src/tables/stock/StockItemTable.tsx:452
#~ msgid "Add stock"
#~ msgstr "Add stock"

#: src/pages/stock/StockDetail.tsx:680
#: src/tables/stock/StockItemTable.tsx:461
#~ msgid "Remove stock"
#~ msgstr "Remove stock"

#: src/pages/stock/StockDetail.tsx:687
msgid "Items Created"
msgstr ""

#: src/pages/stock/StockDetail.tsx:688
msgid "Created {n} stock items"
msgstr "Создано {n} складских позиций"

#: src/pages/stock/StockDetail.tsx:698
#: src/tables/stock/StockItemTable.tsx:481
#~ msgid "Transfer stock"
#~ msgstr "Transfer stock"

#: src/pages/stock/StockDetail.tsx:705
msgid "Delete Stock Item"
msgstr "Удалить складскую позицию"

#: src/pages/stock/StockDetail.tsx:741
msgid "Serialize Stock Item"
msgstr "Присвоить запасу серийный номер"

#: src/pages/stock/StockDetail.tsx:757
#: src/tables/stock/StockItemTable.tsx:539
msgid "Stock item serialized"
msgstr "Запасу присвоен серийный номер"

#: src/pages/stock/StockDetail.tsx:762
#~ msgid "Return Stock Item"
#~ msgstr "Return Stock Item"

#: src/pages/stock/StockDetail.tsx:765
msgid "Scan Into Location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:765
#~ msgid "Return this item into stock. This will remove the customer assignment."
#~ msgstr "Return this item into stock. This will remove the customer assignment."

#: src/pages/stock/StockDetail.tsx:777
#~ msgid "Item returned to stock"
#~ msgstr "Item returned to stock"

#: src/pages/stock/StockDetail.tsx:823
msgid "Scan into location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:825
msgid "Scan this item into a location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:837
msgid "Stock Operations"
msgstr "Действия со складом"

#: src/pages/stock/StockDetail.tsx:842
#: src/tables/build/BuildOutputTable.tsx:532
msgid "Serialize"
msgstr ""

#: src/pages/stock/StockDetail.tsx:843
msgid "Serialize stock"
msgstr ""

#: src/pages/stock/StockDetail.tsx:868
msgid "Stock Item Actions"
msgstr ""

#: src/pages/stock/StockDetail.tsx:868
#~ msgid "Count stock"
#~ msgstr "Count stock"

#: src/pages/stock/StockDetail.tsx:890
#~ msgid "Return from customer"
#~ msgstr "Return from customer"

#: src/pages/stock/StockDetail.tsx:900
#~ msgid "Transfer"
#~ msgstr "Transfer"

#: src/pages/stock/StockDetail.tsx:937
#: src/tables/stock/StockItemTable.tsx:409
msgid "Stale"
msgstr "Залежалый"

#: src/pages/stock/StockDetail.tsx:943
#: src/tables/stock/StockItemTable.tsx:403
msgid "Expired"
msgstr "Просрочен"

#: src/pages/stock/StockDetail.tsx:949
msgid "Unavailable"
msgstr "Недоступно"

#: src/pages/stock/StockDetail.tsx:950
#~ msgid "Assign to Customer"
#~ msgstr "Assign to Customer"

#: src/pages/stock/StockDetail.tsx:951
#~ msgid "Assign to a customer"
#~ msgstr "Assign to a customer"

#: src/states/IconState.tsx:47
#: src/states/IconState.tsx:77
msgid "Error loading icon package from server"
msgstr "Ошибка загрузки пакета значков с сервера"

#: src/tables/ColumnRenderers.tsx:41
#~ msgid "Part is locked"
#~ msgstr "Part is locked"

#: src/tables/ColumnRenderers.tsx:59
msgid "Part is not active"
msgstr "Деталь не активна"

#: src/tables/ColumnRenderers.tsx:64
#: src/tables/bom/BomTable.tsx:619
#: src/tables/part/PartParameterTable.tsx:235
#: src/tables/part/PartTestTemplateTable.tsx:258
msgid "Part is Locked"
msgstr "Деталь заблокирована"

#: src/tables/ColumnRenderers.tsx:69
msgid "You are subscribed to notifications for this part"
msgstr "Вы подписаны на получение уведомлений для этой детали"

#: src/tables/ColumnRenderers.tsx:93
#~ msgid "No location set"
#~ msgstr "No location set"

#: src/tables/ColumnSelect.tsx:16
#: src/tables/ColumnSelect.tsx:23
msgid "Select Columns"
msgstr "Выбрать столбцы"

#: src/tables/DownloadAction.tsx:13
#~ msgid "Excel"
#~ msgstr "Excel"

#: src/tables/DownloadAction.tsx:21
#~ msgid "CSV"
#~ msgstr "CSV"

#: src/tables/DownloadAction.tsx:21
#~ msgid "Download selected data"
#~ msgstr "Download selected data"

#: src/tables/DownloadAction.tsx:22
#~ msgid "TSV"
#~ msgstr "TSV"

#: src/tables/DownloadAction.tsx:23
#~ msgid "Excel (.xlsx)"
#~ msgstr "Excel (.xlsx)"

#: src/tables/DownloadAction.tsx:24
#~ msgid "Excel (.xls)"
#~ msgstr "Excel (.xls)"

#: src/tables/DownloadAction.tsx:36
#~ msgid "Download Data"
#~ msgstr "Download Data"

#: src/tables/Filter.tsx:75
msgid "Has Batch Code"
msgstr "Есть код партии"

#: src/tables/Filter.tsx:76
msgid "Show items which have a batch code"
msgstr "Показать элементы, которым присвоен код партии"

#: src/tables/Filter.tsx:84
msgid "Filter items by batch code"
msgstr "Фильтр по коду партии"

#: src/tables/Filter.tsx:92
msgid "Is Serialized"
msgstr "Есть серийный номер"

#: src/tables/Filter.tsx:93
msgid "Show items which have a serial number"
msgstr "Показать элементы, которым присвоен серийный номер"

#: src/tables/Filter.tsx:100
msgid "Serial"
msgstr "Серийный номер"

#: src/tables/Filter.tsx:101
msgid "Filter items by serial number"
msgstr "Фильтр по серийному номеру"

#: src/tables/Filter.tsx:106
#~ msgid "Show overdue orders"
#~ msgstr "Show overdue orders"

#: src/tables/Filter.tsx:109
msgid "Serial Below"
msgstr "Серийный номер меньше"

#: src/tables/Filter.tsx:110
msgid "Show items with serial numbers less than or equal to a given value"
msgstr "Показать элементы, у которых серийный номер меньше или равен заданному значению"

#: src/tables/Filter.tsx:118
msgid "Serial Above"
msgstr "Серийный номер больше"

#: src/tables/Filter.tsx:119
msgid "Show items with serial numbers greater than or equal to a given value"
msgstr "Показать элементы, у которых серийный номер больше или равен заданному значению"

#: src/tables/Filter.tsx:128
msgid "Assigned to me"
msgstr "Назначено мне"

#: src/tables/Filter.tsx:129
msgid "Show orders assigned to me"
msgstr "Показать заказы, назначенные мне"

#: src/tables/Filter.tsx:136
#: src/tables/sales/SalesOrderAllocationTable.tsx:85
msgid "Outstanding"
msgstr "Незавершено"

#: src/tables/Filter.tsx:137
msgid "Show outstanding items"
msgstr "Показать незавершённые элементы"

#: src/tables/Filter.tsx:145
msgid "Show overdue items"
msgstr ""

#: src/tables/Filter.tsx:152
msgid "Minimum Date"
msgstr "Минимальная дата"

#: src/tables/Filter.tsx:153
msgid "Show items after this date"
msgstr "Показать элементы после указанной даты"

#: src/tables/Filter.tsx:161
msgid "Maximum Date"
msgstr "Максимальная дата"

#: src/tables/Filter.tsx:162
msgid "Show items before this date"
msgstr "Показать элементы до указанной даты"

#: src/tables/Filter.tsx:170
msgid "Created Before"
msgstr "Созданы до"

#: src/tables/Filter.tsx:171
msgid "Show items created before this date"
msgstr "Показать заказы, созданные до указанной даты"

#: src/tables/Filter.tsx:179
msgid "Created After"
msgstr "Созданы после"

#: src/tables/Filter.tsx:180
msgid "Show items created after this date"
msgstr "Показать заказы, созданные после указанной даты"

#: src/tables/Filter.tsx:188
msgid "Start Date Before"
msgstr "Начальная дата до"

#: src/tables/Filter.tsx:189
msgid "Show items with a start date before this date"
msgstr "Показать элементы, начальная дата которых раньше указанной"

#: src/tables/Filter.tsx:197
msgid "Start Date After"
msgstr "Начальная дата после"

#: src/tables/Filter.tsx:198
msgid "Show items with a start date after this date"
msgstr "Показать элементы, начальная дата которых после указанной"

#: src/tables/Filter.tsx:206
msgid "Target Date Before"
msgstr "Целевая дата до"

#: src/tables/Filter.tsx:207
msgid "Show items with a target date before this date"
msgstr "Показать элементы, целевая дата которых раньше указанной"

#: src/tables/Filter.tsx:215
msgid "Target Date After"
msgstr "Целевая дата после"

#: src/tables/Filter.tsx:216
msgid "Show items with a target date after this date"
msgstr "Показать элементы, целевая дата которых после указанной"

#: src/tables/Filter.tsx:224
msgid "Completed Before"
msgstr "Завершен до"

#: src/tables/Filter.tsx:225
msgid "Show items completed before this date"
msgstr "Показать заказы, завершенные до указанной даты"

#: src/tables/Filter.tsx:233
msgid "Completed After"
msgstr "Завершен после"

#: src/tables/Filter.tsx:234
msgid "Show items completed after this date"
msgstr "Показать заказы, завершенные после указанной даты"

#: src/tables/Filter.tsx:246
msgid "Has Project Code"
msgstr "Указан код проекта"

#: src/tables/Filter.tsx:247
msgid "Show orders with an assigned project code"
msgstr "Показать заказы с указанным кодом проекта"

#: src/tables/Filter.tsx:256
msgid "Include Variants"
msgstr "Включая разновидности"

#: src/tables/Filter.tsx:257
msgid "Include results for part variants"
msgstr ""

#: src/tables/Filter.tsx:267
#: src/tables/part/PartPurchaseOrdersTable.tsx:133
msgid "Filter by order status"
msgstr "Фильтр по статусу заказа"

#: src/tables/Filter.tsx:279
msgid "Filter by project code"
msgstr "Фильтр по коду проекта"

#: src/tables/Filter.tsx:312
msgid "Filter by responsible owner"
msgstr "Фильтр по ответственному"

#: src/tables/Filter.tsx:328
#: src/tables/settings/ApiTokenTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:191
msgid "Filter by user"
msgstr "Фильтр по пользователю"

#: src/tables/Filter.tsx:340
msgid "Filter by manufacturer"
msgstr ""

#: src/tables/Filter.tsx:353
msgid "Filter by supplier"
msgstr ""

#: src/tables/Filter.tsx:366
msgid "Filter by user who created the order"
msgstr "Фильтр по пользователю, который создал заказ"

#: src/tables/Filter.tsx:374
msgid "Filter by user who issued the order"
msgstr "Фильтр по пользователю, создавшему заказ"

#: src/tables/Filter.tsx:382
msgid "Filter by part category"
msgstr "Фильтр по категории"

#: src/tables/Filter.tsx:393
msgid "Filter by stock location"
msgstr "Фильтр по месту хранения"

#: src/tables/FilterSelectDrawer.tsx:59
msgid "Remove filter"
msgstr "Убрать фильтрацию"

#: src/tables/FilterSelectDrawer.tsx:102
#: src/tables/FilterSelectDrawer.tsx:104
#: src/tables/FilterSelectDrawer.tsx:151
msgid "Select filter value"
msgstr "Выберите значение фильтра"

#: src/tables/FilterSelectDrawer.tsx:116
msgid "Enter filter value"
msgstr "Введите значение фильтра"

#: src/tables/FilterSelectDrawer.tsx:138
msgid "Select date value"
msgstr "Выберите дату"

#: src/tables/FilterSelectDrawer.tsx:260
msgid "Select filter"
msgstr "Выбрать фильтр"

#: src/tables/FilterSelectDrawer.tsx:261
msgid "Filter"
msgstr "Отфильтровать"

#: src/tables/FilterSelectDrawer.tsx:313
#: src/tables/InvenTreeTableHeader.tsx:257
msgid "Table Filters"
msgstr "Фильтр таблицы"

#: src/tables/FilterSelectDrawer.tsx:346
msgid "Add Filter"
msgstr "Добавить фильтр"

#: src/tables/FilterSelectDrawer.tsx:355
msgid "Clear Filters"
msgstr "Очистить фильтр"

#: src/tables/InvenTreeTable.tsx:44
#: src/tables/InvenTreeTable.tsx:479
msgid "No records found"
msgstr "Записи не найдены"

#: src/tables/InvenTreeTable.tsx:151
msgid "Error loading table options"
msgstr "Ошибка загрузки параметров таблицы"

#: src/tables/InvenTreeTable.tsx:250
#~ msgid "Failed to load table options"
#~ msgstr "Failed to load table options"

#: src/tables/InvenTreeTable.tsx:510
#~ msgid "Are you sure you want to delete the selected records?"
#~ msgstr "Are you sure you want to delete the selected records?"

#: src/tables/InvenTreeTable.tsx:520
msgid "Server returned incorrect data type"
msgstr "Сервер вернул неверный тип данных"

#: src/tables/InvenTreeTable.tsx:535
#~ msgid "Deleted records"
#~ msgstr "Deleted records"

#: src/tables/InvenTreeTable.tsx:536
#~ msgid "Records were deleted successfully"
#~ msgstr "Records were deleted successfully"

#: src/tables/InvenTreeTable.tsx:545
#~ msgid "Failed to delete records"
#~ msgstr "Failed to delete records"

#: src/tables/InvenTreeTable.tsx:552
#~ msgid "This action cannot be undone!"
#~ msgstr "This action cannot be undone!"

#: src/tables/InvenTreeTable.tsx:553
msgid "Error loading table data"
msgstr "Ошибка загрузки данных таблицы"

#: src/tables/InvenTreeTable.tsx:594
#: src/tables/InvenTreeTable.tsx:595
#~ msgid "Print actions"
#~ msgstr "Print actions"

#: src/tables/InvenTreeTable.tsx:655
#: src/tables/InvenTreeTable.tsx:656
#~ msgid "Barcode actions"
#~ msgstr "Barcode actions"

#: src/tables/InvenTreeTable.tsx:680
msgid "View details"
msgstr "Показать сведения"

#: src/tables/InvenTreeTable.tsx:712
#~ msgid "Table filters"
#~ msgstr "Table filters"

#: src/tables/InvenTreeTable.tsx:725
#~ msgid "Clear custom query filters"
#~ msgstr "Clear custom query filters"

#: src/tables/InvenTreeTableHeader.tsx:104
msgid "Delete Selected Items"
msgstr "Удалить выбранные элементы"

#: src/tables/InvenTreeTableHeader.tsx:108
msgid "Are you sure you want to delete the selected items?"
msgstr "Вы уверены, что хотите удалить выбранные элементы?"

#: src/tables/InvenTreeTableHeader.tsx:110
#: src/tables/plugin/PluginListTable.tsx:316
msgid "This action cannot be undone"
msgstr "Это действие нельзя будет отменить"

#: src/tables/InvenTreeTableHeader.tsx:121
msgid "Items deleted"
msgstr "Элементы удалены"

#: src/tables/InvenTreeTableHeader.tsx:126
msgid "Failed to delete items"
msgstr "Не удалось удалить элементы"

#: src/tables/InvenTreeTableHeader.tsx:177
msgid "Custom table filters are active"
msgstr "Используется пользовательский фильтр таблицы"

#: src/tables/InvenTreeTableHeader.tsx:203
#: src/tables/general/BarcodeScanTable.tsx:93
msgid "Delete selected records"
msgstr "Удалить выбранные записи"

#: src/tables/InvenTreeTableHeader.tsx:223
msgid "Refresh data"
msgstr "Обновить данные"

#: src/tables/InvenTreeTableHeader.tsx:269
msgid "Active Filters"
msgstr ""

#: src/tables/TableHoverCard.tsx:35
#~ msgid "item-{idx}"
#~ msgstr "item-{idx}"

#: src/tables/UploadAction.tsx:7
#~ msgid "Upload Data"
#~ msgstr "Upload Data"

#: src/tables/bom/BomTable.tsx:102
msgid "This BOM item is defined for a different parent"
msgstr "Эта позиция в спецификации унаследована от родительской детали"

#: src/tables/bom/BomTable.tsx:118
msgid "Part Information"
msgstr "Информация о детали"

#: src/tables/bom/BomTable.tsx:121
msgid "This BOM item has not been validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:228
msgid "Substitutes"
msgstr "Замены"

#: src/tables/bom/BomTable.tsx:297
#: src/tables/build/BuildLineTable.tsx:268
#: src/tables/part/PartTable.tsx:132
msgid "External stock"
msgstr "Сторонний склад"

#: src/tables/bom/BomTable.tsx:301
#~ msgid "Create BOM Item"
#~ msgstr "Create BOM Item"

#: src/tables/bom/BomTable.tsx:305
#: src/tables/build/BuildLineTable.tsx:231
msgid "Includes substitute stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:310
#~ msgid "Show asssmbled items"
#~ msgstr "Show asssmbled items"

#: src/tables/bom/BomTable.tsx:314
#: src/tables/build/BuildLineTable.tsx:241
#: src/tables/sales/SalesOrderLineItemTable.tsx:158
msgid "Includes variant stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:331
#: src/tables/part/PartTable.tsx:101
msgid "Building"
msgstr ""

#: src/tables/bom/BomTable.tsx:331
#~ msgid "Edit Bom Item"
#~ msgstr "Edit Bom Item"

#: src/tables/bom/BomTable.tsx:333
#~ msgid "Bom item updated"
#~ msgstr "Bom item updated"

#: src/tables/bom/BomTable.tsx:340
#: src/tables/part/PartTable.tsx:158
#: src/tables/sales/SalesOrderLineItemTable.tsx:181
#: src/tables/stock/StockItemTable.tsx:223
msgid "Stock Information"
msgstr "Информация о складе"

#: src/tables/bom/BomTable.tsx:348
#~ msgid "Delete Bom Item"
#~ msgstr "Delete Bom Item"

#: src/tables/bom/BomTable.tsx:349
#~ msgid "Bom item deleted"
#~ msgstr "Bom item deleted"

#: src/tables/bom/BomTable.tsx:351
#~ msgid "Are you sure you want to remove this BOM item?"
#~ msgstr "Are you sure you want to remove this BOM item?"

#: src/tables/bom/BomTable.tsx:354
#~ msgid "Validate BOM line"
#~ msgstr "Validate BOM line"

#: src/tables/bom/BomTable.tsx:374
#: src/tables/build/BuildLineTable.tsx:478
#: src/tables/build/BuildLineTable.tsx:518
msgid "Consumable item"
msgstr ""

#: src/tables/bom/BomTable.tsx:377
msgid "No available stock"
msgstr "Нет на складе"

#: src/tables/bom/BomTable.tsx:395
#: src/tables/build/BuildLineTable.tsx:211
msgid "Show testable items"
msgstr "Показать тестируемые элементы"

#: src/tables/bom/BomTable.tsx:400
msgid "Show trackable items"
msgstr "Показать отслеживаемые позиции"

#: src/tables/bom/BomTable.tsx:405
#: src/tables/build/BuildLineTable.tsx:206
msgid "Show assembled items"
msgstr "Показать сборные детали"

#: src/tables/bom/BomTable.tsx:410
#: src/tables/build/BuildLineTable.tsx:191
msgid "Show items with available stock"
msgstr "Показать элементы, которые есть в наличии на складе"

#: src/tables/bom/BomTable.tsx:415
msgid "Show items on order"
msgstr "Показать элементы, которые находятся в заказе"

#: src/tables/bom/BomTable.tsx:419
msgid "Validated"
msgstr "Утверждено"

#: src/tables/bom/BomTable.tsx:420
msgid "Show validated items"
msgstr "Показать утверждённые элементы"

#: src/tables/bom/BomTable.tsx:424
#: src/tables/bom/UsedInTable.tsx:80
msgid "Inherited"
msgstr "Унаследовано"

#: src/tables/bom/BomTable.tsx:425
#: src/tables/bom/UsedInTable.tsx:81
msgid "Show inherited items"
msgstr "Показать элементы, которые унаследованы"

#: src/tables/bom/BomTable.tsx:429
msgid "Allow Variants"
msgstr "Разрешить разновидности"

#: src/tables/bom/BomTable.tsx:430
msgid "Show items which allow variant substitution"
msgstr "Показать элементы, в которых разрешено использовать разновидности для замены"

#: src/tables/bom/BomTable.tsx:434
#: src/tables/bom/UsedInTable.tsx:85
#: src/tables/build/BuildLineTable.tsx:200
msgid "Optional"
msgstr "Необязательно"

#: src/tables/bom/BomTable.tsx:435
#: src/tables/bom/UsedInTable.tsx:86
msgid "Show optional items"
msgstr "Показать необязательные элементы"

#: src/tables/bom/BomTable.tsx:439
#: src/tables/build/BuildLineTable.tsx:195
msgid "Consumable"
msgstr "Расходник"

#: src/tables/bom/BomTable.tsx:440
msgid "Show consumable items"
msgstr "Показать элементы, которые являются расходниками"

#: src/tables/bom/BomTable.tsx:444
#: src/tables/part/PartTable.tsx:300
msgid "Has Pricing"
msgstr "Есть цена"

#: src/tables/bom/BomTable.tsx:445
msgid "Show items with pricing"
msgstr "Показать позиции с ценой"

#: src/tables/bom/BomTable.tsx:467
#: src/tables/bom/BomTable.tsx:596
msgid "Import BOM Data"
msgstr "Импортировать данные спецификации"

#: src/tables/bom/BomTable.tsx:477
#: src/tables/bom/BomTable.tsx:603
msgid "Add BOM Item"
msgstr "Создать позицию в спецификации"

#: src/tables/bom/BomTable.tsx:482
msgid "BOM item created"
msgstr "Создана позиция в спецификации"

#: src/tables/bom/BomTable.tsx:489
msgid "Edit BOM Item"
msgstr "Редактировать позицию спецификации"

#: src/tables/bom/BomTable.tsx:491
msgid "BOM item updated"
msgstr "Позиция в спецификации обновлена"

#: src/tables/bom/BomTable.tsx:498
msgid "Delete BOM Item"
msgstr "Удалить позицию в спецификации"

#: src/tables/bom/BomTable.tsx:499
msgid "BOM item deleted"
msgstr "Позиция в спецификации удалена"

#: src/tables/bom/BomTable.tsx:519
msgid "BOM item validated"
msgstr "Позиция в спецификации утверждена"

#: src/tables/bom/BomTable.tsx:528
msgid "Failed to validate BOM item"
msgstr "При утверждении позиции в спецификации произошла ошибка"

#: src/tables/bom/BomTable.tsx:540
msgid "View BOM"
msgstr "Показать спецификацию"

#: src/tables/bom/BomTable.tsx:551
msgid "Validate BOM Line"
msgstr "Утвердить позицию в спецификации"

#: src/tables/bom/BomTable.tsx:570
msgid "Edit Substitutes"
msgstr "Редактировать варианты замены"

#: src/tables/bom/BomTable.tsx:624
msgid "Bill of materials cannot be edited, as the part is locked"
msgstr "Невозможно отредактировать спецификацию, поскольку деталь заблокирована"

#: src/tables/bom/UsedInTable.tsx:34
#: src/tables/build/BuildLineTable.tsx:205
#: src/tables/part/ParametricPartTable.tsx:360
#: src/tables/part/PartBuildAllocationsTable.tsx:60
#: src/tables/part/PartTable.tsx:196
#: src/tables/stock/StockItemTable.tsx:334
msgid "Assembly"
msgstr "Сборная деталь"

#: src/tables/bom/UsedInTable.tsx:91
msgid "Show active assemblies"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:95
#: src/tables/part/PartTable.tsx:226
#: src/tables/part/PartVariantTable.tsx:30
msgid "Trackable"
msgstr "Отслеживаемая"

#: src/tables/bom/UsedInTable.tsx:96
msgid "Show trackable assemblies"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:67
msgid "Allocated to Output"
msgstr "Зарезервировано"

#: src/tables/build/BuildAllocatedStockTable.tsx:68
msgid "Show items allocated to a build output"
msgstr "Показать позиции, зарезервированные для продукции"

#: src/tables/build/BuildAllocatedStockTable.tsx:73
#: src/tables/build/BuildOrderTable.tsx:197
#: src/tables/part/PartPurchaseOrdersTable.tsx:140
#: src/tables/sales/ReturnOrderTable.tsx:100
#: src/tables/sales/SalesOrderAllocationTable.tsx:101
#: src/tables/sales/SalesOrderTable.tsx:101
#~ msgid "Include orders for part variants"
#~ msgstr "Include orders for part variants"

#: src/tables/build/BuildAllocatedStockTable.tsx:97
#: src/tables/part/PartBuildAllocationsTable.tsx:84
#: src/tables/part/PartPurchaseOrdersTable.tsx:132
#: src/tables/part/PartSalesAllocationsTable.tsx:69
#: src/tables/sales/SalesOrderAllocationTable.tsx:119
msgid "Order Status"
msgstr "Статус заказа"

#: src/tables/build/BuildAllocatedStockTable.tsx:164
#~ msgid "Edit Build Item"
#~ msgstr "Edit Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:167
#: src/tables/build/BuildLineTable.tsx:631
msgid "Edit Stock Allocation"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:174
#~ msgid "Delete Build Item"
#~ msgstr "Delete Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:180
#: src/tables/build/BuildLineTable.tsx:644
msgid "Delete Stock Allocation"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:232
msgid "Consume"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:59
#~ msgid "Show lines with available stock"
#~ msgstr "Show lines with available stock"

#: src/tables/build/BuildLineTable.tsx:106
msgid "View Stock Item"
msgstr "Показать складскую позицию"

#: src/tables/build/BuildLineTable.tsx:181
msgid "Show fully allocated lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:186
msgid "Show fully consumed lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:189
#~ msgid "Show allocated lines"
#~ msgstr "Show allocated lines"

#: src/tables/build/BuildLineTable.tsx:196
msgid "Show consumable lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:201
msgid "Show optional lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:210
#: src/tables/part/PartTable.tsx:220
msgid "Testable"
msgstr "Тестируемая"

#: src/tables/build/BuildLineTable.tsx:215
#: src/tables/stock/StockItemTable.tsx:393
msgid "Tracked"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:216
msgid "Show tracked lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:250
#: src/tables/sales/SalesOrderLineItemTable.tsx:164
msgid "In production"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:278
msgid "Insufficient stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:294
#: src/tables/sales/SalesOrderLineItemTable.tsx:152
#: src/tables/stock/StockItemTable.tsx:192
msgid "No stock available"
msgstr "Нет на складе"

#: src/tables/build/BuildLineTable.tsx:360
msgid "Gets Inherited"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:373
msgid "Unit Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:389
msgid "Required Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:400
msgid "Setup Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:409
msgid "Attrition"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:417
msgid "Rounding Multiple"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:426
msgid "BOM Information"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:496
#: src/tables/part/PartBuildAllocationsTable.tsx:102
msgid "Fully allocated"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:544
#: src/tables/sales/SalesOrderLineItemTable.tsx:290
msgid "Create Build Order"
msgstr "Создать заказ на сборку"

#: src/tables/build/BuildLineTable.tsx:573
msgid "Auto allocation in progress"
msgstr "Выполняется автоматическое распределение"

#: src/tables/build/BuildLineTable.tsx:576
#: src/tables/build/BuildLineTable.tsx:781
msgid "Auto Allocate Stock"
msgstr "Автоматическое резервирование остатков"

#: src/tables/build/BuildLineTable.tsx:577
msgid "Automatically allocate stock to this build according to the selected options"
msgstr "Автоматически выделять запасы на эту сборку в соответствии с выбранными параметрами"

#: src/tables/build/BuildLineTable.tsx:597
#: src/tables/build/BuildLineTable.tsx:611
#: src/tables/build/BuildLineTable.tsx:730
#: src/tables/build/BuildLineTable.tsx:831
#: src/tables/build/BuildOutputTable.tsx:359
#: src/tables/build/BuildOutputTable.tsx:364
msgid "Deallocate Stock"
msgstr "Отменить резервирование остатков"

#: src/tables/build/BuildLineTable.tsx:613
msgid "Deallocate all untracked stock for this build order"
msgstr "Начислить все неотслеживаемые запасы для этого заказа на сборку"

#: src/tables/build/BuildLineTable.tsx:615
msgid "Deallocate stock from the selected line item"
msgstr "Отменить резервирование остатков для выбранной позиции"

#: src/tables/build/BuildLineTable.tsx:619
msgid "Stock has been deallocated"
msgstr "Склад был распродан"

#: src/tables/build/BuildLineTable.tsx:750
msgid "Build Stock"
msgstr "Собрать"

#: src/tables/build/BuildLineTable.tsx:763
#: src/tables/sales/SalesOrderLineItemTable.tsx:377
msgid "View Part"
msgstr "Показать деталь"

#: src/tables/build/BuildOrderTable.tsx:116
#~ msgid "Cascade"
#~ msgstr "Cascade"

#: src/tables/build/BuildOrderTable.tsx:117
#~ msgid "Display recursive child orders"
#~ msgstr "Display recursive child orders"

#: src/tables/build/BuildOrderTable.tsx:121
#~ msgid "Show active orders"
#~ msgstr "Show active orders"

#: src/tables/build/BuildOrderTable.tsx:122
#~ msgid "Show overdue status"
#~ msgstr "Show overdue status"

#: src/tables/build/BuildOrderTable.tsx:127
#~ msgid "Show outstanding orders"
#~ msgstr "Show outstanding orders"

#: src/tables/build/BuildOrderTable.tsx:139
#: src/tables/purchasing/PurchaseOrderTable.tsx:71
#: src/tables/sales/ReturnOrderTable.tsx:62
#: src/tables/sales/SalesOrderTable.tsx:69
#~ msgid "Filter by whether the purchase order has a project code"
#~ msgstr "Filter by whether the purchase order has a project code"

#: src/tables/build/BuildOrderTable.tsx:167
#: src/tables/purchasing/PurchaseOrderTable.tsx:83
#: src/tables/sales/ReturnOrderTable.tsx:79
#: src/tables/sales/SalesOrderTable.tsx:80
msgid "Has Target Date"
msgstr "Есть целевая дата"

#: src/tables/build/BuildOrderTable.tsx:168
#: src/tables/purchasing/PurchaseOrderTable.tsx:84
#: src/tables/sales/ReturnOrderTable.tsx:80
#: src/tables/sales/SalesOrderTable.tsx:81
msgid "Show orders with a target date"
msgstr "Показать заказы с указанной целевой датой"

#: src/tables/build/BuildOrderTable.tsx:173
#: src/tables/purchasing/PurchaseOrderTable.tsx:89
#: src/tables/sales/ReturnOrderTable.tsx:85
#: src/tables/sales/SalesOrderTable.tsx:86
msgid "Has Start Date"
msgstr "Есть начальная дата"

#: src/tables/build/BuildOrderTable.tsx:174
#: src/tables/purchasing/PurchaseOrderTable.tsx:90
#: src/tables/sales/ReturnOrderTable.tsx:86
#: src/tables/sales/SalesOrderTable.tsx:87
msgid "Show orders with a start date"
msgstr "Показать заказы с указанной начальной датой"

#: src/tables/build/BuildOrderTable.tsx:179
#~ msgid "Filter by user who issued this order"
#~ msgstr "Filter by user who issued this order"

#: src/tables/build/BuildOrderTestTable.tsx:85
#: src/tables/build/BuildOrderTestTable.tsx:163
#: src/tables/build/BuildOrderTestTable.tsx:283
#: src/tables/build/BuildOrderTestTable.tsx:297
#: src/tables/stock/StockItemTestResultTable.tsx:293
#: src/tables/stock/StockItemTestResultTable.tsx:365
#: src/tables/stock/StockItemTestResultTable.tsx:426
msgid "Add Test Result"
msgstr "Добавить результат тестирования"

#: src/tables/build/BuildOrderTestTable.tsx:92
#: src/tables/stock/StockItemTestResultTable.tsx:295
msgid "Test result added"
msgstr "Результат тестирования добавлен"

#: src/tables/build/BuildOrderTestTable.tsx:124
msgid "Add Test Results"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:134
msgid "Test results added"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:162
#: src/tables/stock/StockItemTestResultTable.tsx:191
msgid "No Result"
msgstr "Нет результатов"

#: src/tables/build/BuildOrderTestTable.tsx:274
msgid "Show build outputs currently in production"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:104
msgid "Build Output Stock Allocation"
msgstr "Резервирование складских позиций для продукции"

#: src/tables/build/BuildOutputTable.tsx:161
#~ msgid "Delete build output"
#~ msgstr "Delete build output"

#: src/tables/build/BuildOutputTable.tsx:294
#: src/tables/build/BuildOutputTable.tsx:479
msgid "Add Build Output"
msgstr "Создать продукцию"

#: src/tables/build/BuildOutputTable.tsx:297
msgid "Build output created"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:304
#~ msgid "Edit build output"
#~ msgstr "Edit build output"

#: src/tables/build/BuildOutputTable.tsx:350
#: src/tables/build/BuildOutputTable.tsx:553
msgid "Edit Build Output"
msgstr "Редактировать продукцию"

#: src/tables/build/BuildOutputTable.tsx:366
msgid "This action will deallocate all stock from the selected build output"
msgstr "Это действие отменит резервирование всех складских позиций для выбранной продукции"

#: src/tables/build/BuildOutputTable.tsx:391
msgid "Serialize Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:409
#: src/tables/stock/StockItemTable.tsx:329
msgid "Filter by stock status"
msgstr "Фильтр по статусу склада"

#: src/tables/build/BuildOutputTable.tsx:446
msgid "Complete selected outputs"
msgstr "Завершить выбранную продукцию"

#: src/tables/build/BuildOutputTable.tsx:457
msgid "Scrap selected outputs"
msgstr "Списать выбранную продукцию"

#: src/tables/build/BuildOutputTable.tsx:468
msgid "Cancel selected outputs"
msgstr "Отменить выбранную продукцию"

#: src/tables/build/BuildOutputTable.tsx:498
msgid "View Build Output"
msgstr "Показать продукцию"

#: src/tables/build/BuildOutputTable.tsx:504
msgid "Allocate"
msgstr "Зарезервировать"

#: src/tables/build/BuildOutputTable.tsx:505
msgid "Allocate stock to build output"
msgstr "Зарезервировать остатки для выбранной продукции"

#: src/tables/build/BuildOutputTable.tsx:518
msgid "Deallocate"
msgstr "Отменить резервирование"

#: src/tables/build/BuildOutputTable.tsx:519
msgid "Deallocate stock from build output"
msgstr "Отменить резервирование остатков для выбранной продукции"

#: src/tables/build/BuildOutputTable.tsx:533
msgid "Serialize build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:544
msgid "Complete build output"
msgstr "Завершить продукцию"

#: src/tables/build/BuildOutputTable.tsx:560
msgid "Scrap"
msgstr "Списать"

#: src/tables/build/BuildOutputTable.tsx:561
msgid "Scrap build output"
msgstr "Списать продукцию"

#: src/tables/build/BuildOutputTable.tsx:571
msgid "Cancel build output"
msgstr "Отменить продукцию"

#: src/tables/build/BuildOutputTable.tsx:620
msgid "Allocated Lines"
msgstr "Зарезервированные позиции"

#: src/tables/build/BuildOutputTable.tsx:635
msgid "Required Tests"
msgstr "Обязательные тесты"

#: src/tables/build/BuildOutputTable.tsx:710
msgid "External Build"
msgstr "Сторонняя сборка"

#: src/tables/build/BuildOutputTable.tsx:712
msgid "This build order is fulfilled by an external purchase order"
msgstr "Этот заказ на сборку выполнен внешними заказами на закупку"

#: src/tables/company/AddressTable.tsx:122
#: src/tables/company/AddressTable.tsx:187
msgid "Add Address"
msgstr "Добавить адрес"

#: src/tables/company/AddressTable.tsx:127
msgid "Address created"
msgstr "Адрес создан"

#: src/tables/company/AddressTable.tsx:136
msgid "Edit Address"
msgstr "Редактировать адрес"

#: src/tables/company/AddressTable.tsx:144
msgid "Delete Address"
msgstr "Удалить адрес"

#: src/tables/company/AddressTable.tsx:145
msgid "Are you sure you want to delete this address?"
msgstr "Вы уверены, что хотите удалить этот адрес?"

#: src/tables/company/CompanyTable.tsx:70
#: src/tables/company/CompanyTable.tsx:120
msgid "Add Company"
msgstr "Создать компанию"

#: src/tables/company/CompanyTable.tsx:71
#~ msgid "New Company"
#~ msgstr "New Company"

#: src/tables/company/CompanyTable.tsx:92
msgid "Show active companies"
msgstr ""

#: src/tables/company/CompanyTable.tsx:97
msgid "Show companies which are suppliers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:102
msgid "Show companies which are manufacturers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:107
msgid "Show companies which are customers"
msgstr ""

#: src/tables/company/ContactTable.tsx:99
msgid "Edit Contact"
msgstr "Редактировать контакт"

#: src/tables/company/ContactTable.tsx:106
msgid "Add Contact"
msgstr "Создать контакт"

#: src/tables/company/ContactTable.tsx:117
msgid "Delete Contact"
msgstr "Удалить контакт"

#: src/tables/company/ContactTable.tsx:158
msgid "Add contact"
msgstr "Создать контакт"

#: src/tables/general/AttachmentTable.tsx:108
msgid "Uploading file {filename}"
msgstr "Загрузка файла {filename}"

#: src/tables/general/AttachmentTable.tsx:139
#~ msgid "File uploaded"
#~ msgstr "File uploaded"

#: src/tables/general/AttachmentTable.tsx:140
#~ msgid "File {0} uploaded successfully"
#~ msgstr "File {0} uploaded successfully"

#: src/tables/general/AttachmentTable.tsx:160
#: src/tables/general/AttachmentTable.tsx:174
msgid "Uploading File"
msgstr "Загрузка файла"

#: src/tables/general/AttachmentTable.tsx:185
msgid "File Uploaded"
msgstr "Файл загружен"

#: src/tables/general/AttachmentTable.tsx:186
msgid "File {name} uploaded successfully"
msgstr "Файл {name} успешно загружен"

#: src/tables/general/AttachmentTable.tsx:202
msgid "File could not be uploaded"
msgstr "Файл не может быть загружен"

#: src/tables/general/AttachmentTable.tsx:253
msgid "Upload Attachment"
msgstr "Загрузить вложение"

#: src/tables/general/AttachmentTable.tsx:254
#~ msgid "Upload attachment"
#~ msgstr "Upload attachment"

#: src/tables/general/AttachmentTable.tsx:263
msgid "Edit Attachment"
msgstr "Редактировать вложение"

#: src/tables/general/AttachmentTable.tsx:277
msgid "Delete Attachment"
msgstr "Удалить вложение"

#: src/tables/general/AttachmentTable.tsx:287
msgid "Is Link"
msgstr "Это ссылка"

#: src/tables/general/AttachmentTable.tsx:288
msgid "Show link attachments"
msgstr "Показать вложения, которые являются ссылками"

#: src/tables/general/AttachmentTable.tsx:292
msgid "Is File"
msgstr "Это файл"

#: src/tables/general/AttachmentTable.tsx:293
msgid "Show file attachments"
msgstr "Показать вложения, которые являются файлами"

#: src/tables/general/AttachmentTable.tsx:302
msgid "Add attachment"
msgstr "Добавить вложение"

#: src/tables/general/AttachmentTable.tsx:313
msgid "Add external link"
msgstr "Добавить внешнюю ссылку"

#: src/tables/general/AttachmentTable.tsx:361
msgid "No attachments found"
msgstr "Вложений не найдено"

#: src/tables/general/AttachmentTable.tsx:400
msgid "Drag attachment file here to upload"
msgstr "Перетащите файл для загрузки"

#: src/tables/general/BarcodeScanTable.tsx:35
msgid "Item"
msgstr "Элемент"

#: src/tables/general/BarcodeScanTable.tsx:50
msgid "Model"
msgstr "Модель"

#: src/tables/general/BarcodeScanTable.tsx:60
#: src/tables/settings/BarcodeScanHistoryTable.tsx:75
#: src/tables/settings/EmailTable.tsx:105
#: src/tables/settings/ErrorTable.tsx:59
msgid "Timestamp"
msgstr "Метка времени"

#: src/tables/general/BarcodeScanTable.tsx:75
msgid "View Item"
msgstr "Показать элемент"

#: src/tables/general/ExtraLineItemTable.tsx:91
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:288
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:397
#: src/tables/sales/ReturnOrderLineItemTable.tsx:80
#: src/tables/sales/ReturnOrderLineItemTable.tsx:183
#: src/tables/sales/SalesOrderLineItemTable.tsx:231
#: src/tables/sales/SalesOrderLineItemTable.tsx:334
msgid "Add Line Item"
msgstr "Создать позицию"

#: src/tables/general/ExtraLineItemTable.tsx:104
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:309
#: src/tables/sales/ReturnOrderLineItemTable.tsx:93
#: src/tables/sales/SalesOrderLineItemTable.tsx:250
msgid "Edit Line Item"
msgstr "Редактировать позицию"

#: src/tables/general/ExtraLineItemTable.tsx:113
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:318
#: src/tables/sales/ReturnOrderLineItemTable.tsx:102
#: src/tables/sales/SalesOrderLineItemTable.tsx:259
msgid "Delete Line Item"
msgstr "Удалить позицию"

#: src/tables/general/ExtraLineItemTable.tsx:151
msgid "Add Extra Line Item"
msgstr "Создать дополнительные позиции"

#: src/tables/machine/MachineListTable.tsx:206
msgid "Machine restarted"
msgstr "Оборудование перезагружено"

#: src/tables/machine/MachineListTable.tsx:216
#: src/tables/machine/MachineListTable.tsx:264
msgid "Edit machine"
msgstr "Редактировать оборудование"

#: src/tables/machine/MachineListTable.tsx:230
#: src/tables/machine/MachineListTable.tsx:268
msgid "Delete machine"
msgstr "Удалить оборудование"

#: src/tables/machine/MachineListTable.tsx:231
msgid "Machine successfully deleted."
msgstr "Оборудование успешно удалено."

#. placeholder {0}: machine?.name ?? 'unknown'
#: src/tables/machine/MachineListTable.tsx:235
msgid "Are you sure you want to remove the machine \"{0}\"?"
msgstr "Вы уверены, что хотите удалить это оборудование \"{0}\"?"

#: src/tables/machine/MachineListTable.tsx:252
msgid "Machine"
msgstr "Оборудование"

#: src/tables/machine/MachineListTable.tsx:257
#: src/tables/machine/MachineListTable.tsx:444
msgid "Restart required"
msgstr "Требуется перезагрузка"

#: src/tables/machine/MachineListTable.tsx:261
msgid "Machine Actions"
msgstr "Действия с оборудованием"

#: src/tables/machine/MachineListTable.tsx:273
msgid "Restart"
msgstr "Перезагрузка"

#: src/tables/machine/MachineListTable.tsx:275
msgid "Restart machine"
msgstr "Перезагрузить оборудование"

#: src/tables/machine/MachineListTable.tsx:277
msgid "manual restart required"
msgstr "требуется ручная перезагрузка"

#: src/tables/machine/MachineListTable.tsx:291
#~ msgid "Machine information"
#~ msgstr "Machine information"

#: src/tables/machine/MachineListTable.tsx:295
msgid "Machine Information"
msgstr "Сведения об оборудовании"

#: src/tables/machine/MachineListTable.tsx:305
#: src/tables/machine/MachineListTable.tsx:611
msgid "Machine Type"
msgstr "Тип оборудования"

#: src/tables/machine/MachineListTable.tsx:318
msgid "Machine Driver"
msgstr "Драйвер оборудования"

#: src/tables/machine/MachineListTable.tsx:333
msgid "Initialized"
msgstr "Инициализировано"

#: src/tables/machine/MachineListTable.tsx:362
#: src/tables/machine/MachineTypeTable.tsx:289
msgid "No errors reported"
msgstr "Ошибки не обнаружены"

#: src/tables/machine/MachineListTable.tsx:381
msgid "Machine Settings"
msgstr "Настройки оборудования"

#: src/tables/machine/MachineListTable.tsx:397
msgid "Driver Settings"
msgstr "Настройки драйвера"

#: src/tables/machine/MachineListTable.tsx:494
#~ msgid "Create machine"
#~ msgstr "Create machine"

#: src/tables/machine/MachineListTable.tsx:517
msgid "Add Machine"
msgstr "Создать оборудование"

#: src/tables/machine/MachineListTable.tsx:559
msgid "Add machine"
msgstr "Создать оборудование"

#: src/tables/machine/MachineListTable.tsx:561
#~ msgid "Machine detail"
#~ msgstr "Machine detail"

#: src/tables/machine/MachineListTable.tsx:573
msgid "Machine Detail"
msgstr "Сведения об оборудовании"

#: src/tables/machine/MachineListTable.tsx:620
msgid "Driver"
msgstr "Драйвер"

#: src/tables/machine/MachineTypeTable.tsx:77
msgid "Builtin driver"
msgstr "Встроенный драйвер"

#: src/tables/machine/MachineTypeTable.tsx:95
msgid "Not Found"
msgstr "Не найдено"

#: src/tables/machine/MachineTypeTable.tsx:98
msgid "Machine type not found."
msgstr "Тип оборудования не найден."

#: src/tables/machine/MachineTypeTable.tsx:99
#~ msgid "Machine type information"
#~ msgstr "Machine type information"

#: src/tables/machine/MachineTypeTable.tsx:108
msgid "Machine Type Information"
msgstr "Информация о типе оборудования"

#: src/tables/machine/MachineTypeTable.tsx:123
#: src/tables/machine/MachineTypeTable.tsx:237
msgid "Slug"
msgstr "Часть URL-адреса"

#: src/tables/machine/MachineTypeTable.tsx:134
#: src/tables/machine/MachineTypeTable.tsx:258
msgid "Provider plugin"
msgstr "Плагин поставщика"

#: src/tables/machine/MachineTypeTable.tsx:146
#: src/tables/machine/MachineTypeTable.tsx:270
msgid "Provider file"
msgstr "Файл поставщика"

#: src/tables/machine/MachineTypeTable.tsx:148
#~ msgid "Available drivers"
#~ msgstr "Available drivers"

#: src/tables/machine/MachineTypeTable.tsx:161
msgid "Available Drivers"
msgstr "Доступные драйверы"

#: src/tables/machine/MachineTypeTable.tsx:216
msgid "Machine driver not found."
msgstr "Драйвер оборудования не найден."

#: src/tables/machine/MachineTypeTable.tsx:224
msgid "Machine driver information"
msgstr "Информация о драйвере оборудования"

#: src/tables/machine/MachineTypeTable.tsx:244
msgid "Machine type"
msgstr "Тип оборудования"

#: src/tables/machine/MachineTypeTable.tsx:338
#~ msgid "Machine type detail"
#~ msgstr "Machine type detail"

#: src/tables/machine/MachineTypeTable.tsx:344
msgid "Builtin type"
msgstr "Встроенный тип"

#: src/tables/machine/MachineTypeTable.tsx:348
#~ msgid "Machine driver detail"
#~ msgstr "Machine driver detail"

#: src/tables/machine/MachineTypeTable.tsx:353
msgid "Machine Type Detail"
msgstr "Сведения о типе оборудования"

#: src/tables/machine/MachineTypeTable.tsx:363
msgid "Machine Driver Detail"
msgstr "Сведения о драйвере оборудования"

#: src/tables/notifications/NotificationTable.tsx:26
msgid "Age"
msgstr "Срок"

#: src/tables/notifications/NotificationTable.tsx:37
msgid "Notification"
msgstr "Уведомление"

#: src/tables/notifications/NotificationTable.tsx:41
#: src/tables/plugin/PluginErrorTable.tsx:37
#: src/tables/settings/ErrorTable.tsx:50
msgid "Message"
msgstr "Сообщение"

#: src/tables/part/ParametricPartTable.tsx:78
msgid "Click to edit"
msgstr "Нажмите для редактирования"

#: src/tables/part/ParametricPartTable.tsx:82
#~ msgid "Edit parameter"
#~ msgstr "Edit parameter"

#: src/tables/part/ParametricPartTable.tsx:240
msgid "Add Part Parameter"
msgstr "Создать параметр детали"

#: src/tables/part/ParametricPartTable.tsx:254
#: src/tables/part/PartParameterTable.tsx:172
#: src/tables/part/PartParameterTable.tsx:195
msgid "Edit Part Parameter"
msgstr "Редактировать параметр детали"

#: src/tables/part/ParametricPartTable.tsx:351
msgid "Show active parts"
msgstr "Показать активные детали"

#: src/tables/part/ParametricPartTable.tsx:356
msgid "Show locked parts"
msgstr "Показать заблокированные детали"

#: src/tables/part/ParametricPartTable.tsx:361
msgid "Show assembly parts"
msgstr "Показать сборные детали"

#: src/tables/part/ParametricPartTableFilters.tsx:67
msgid "True"
msgstr "Да"

#: src/tables/part/ParametricPartTableFilters.tsx:68
msgid "False"
msgstr "Нет"

#: src/tables/part/ParametricPartTableFilters.tsx:73
#: src/tables/part/ParametricPartTableFilters.tsx:97
msgid "Select a choice"
msgstr "Выберите вариант"

#: src/tables/part/ParametricPartTableFilters.tsx:116
msgid "Enter a value"
msgstr "Введите значение"

#: src/tables/part/PartBuildAllocationsTable.tsx:64
msgid "Assembly IPN"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:73
msgid "Part IPN"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:91
msgid "Required Stock"
msgstr "Требуемый запас"

#: src/tables/part/PartBuildAllocationsTable.tsx:124
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:354
msgid "View Build Order"
msgstr "Показать заказ на сборку"

#: src/tables/part/PartCategoryTable.tsx:51
msgid "You are subscribed to notifications for this category"
msgstr "Вы подписаны на получение уведомлений для этой категории"

#: src/tables/part/PartCategoryTable.tsx:84
#: src/tables/part/PartTable.tsx:208
msgid "Include Subcategories"
msgstr "Включая подкатегории"

#: src/tables/part/PartCategoryTable.tsx:85
msgid "Include subcategories in results"
msgstr "Включить подкатегории в результаты"

#: src/tables/part/PartCategoryTable.tsx:90
msgid "Show structural categories"
msgstr "Показать категории, которые являются структурами"

#: src/tables/part/PartCategoryTable.tsx:95
msgid "Show categories to which the user is subscribed"
msgstr "Показать категории, на которые пользователь подписан"

#: src/tables/part/PartCategoryTable.tsx:104
msgid "New Part Category"
msgstr "Создать категорию деталей"

#: src/tables/part/PartCategoryTable.tsx:130
msgid "Set Parent Category"
msgstr "Задать родительскую категорию"

#: src/tables/part/PartCategoryTable.tsx:148
#: src/tables/stock/StockLocationTable.tsx:147
msgid "Set Parent"
msgstr "Установить родителя"

#: src/tables/part/PartCategoryTable.tsx:150
msgid "Set parent category for the selected items"
msgstr "Задать родительскую категорию для выбранных элементов"

#: src/tables/part/PartCategoryTable.tsx:161
msgid "Add Part Category"
msgstr "Создать категорию деталей"

#: src/tables/part/PartCategoryTemplateTable.tsx:42
#: src/tables/part/PartCategoryTemplateTable.tsx:136
msgid "Add Category Parameter"
msgstr "Создать параметр категории"

#: src/tables/part/PartCategoryTemplateTable.tsx:50
msgid "Edit Category Parameter"
msgstr "Редактировать параметр категории"

#: src/tables/part/PartCategoryTemplateTable.tsx:58
msgid "Delete Category Parameter"
msgstr "Удалить параметр категории"

#: src/tables/part/PartCategoryTemplateTable.tsx:80
msgid "Parameter Template"
msgstr "Шаблон параметра"

#: src/tables/part/PartCategoryTemplateTable.tsx:93
#~ msgid "[{0}]"
#~ msgstr "[{0}]"

#: src/tables/part/PartParameterTable.tsx:108
msgid "Internal Units"
msgstr "Внутренние ед. измерения"

#: src/tables/part/PartParameterTable.tsx:127
#: src/tables/part/PartParameterTable.tsx:146
msgid "Updated By"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:147
msgid "Filter by user who last updated the parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:156
msgid "New Part Parameter"
msgstr "Создание параметра детали"

#: src/tables/part/PartParameterTable.tsx:181
#: src/tables/part/PartParameterTable.tsx:203
msgid "Delete Part Parameter"
msgstr "Удалить параметр детали"

#: src/tables/part/PartParameterTable.tsx:221
msgid "Add parameter"
msgstr "Создать параметр"

#: src/tables/part/PartParameterTable.tsx:240
msgid "Part parameters cannot be edited, as the part is locked"
msgstr "Параметры детали нельзя редактировать, поскольку деталь заблокирована"

#: src/tables/part/PartParameterTemplateTable.tsx:36
msgid "Checkbox"
msgstr "Чекбокс"

#: src/tables/part/PartParameterTemplateTable.tsx:37
msgid "Show checkbox templates"
msgstr "Показать шаблоны-переключатели"

#: src/tables/part/PartParameterTemplateTable.tsx:41
msgid "Has choices"
msgstr "Есть варианты"

#: src/tables/part/PartParameterTemplateTable.tsx:42
msgid "Show templates with choices"
msgstr "Показать шаблоны с вариантами"

#: src/tables/part/PartParameterTemplateTable.tsx:46
#: src/tables/part/PartTable.tsx:232
msgid "Has Units"
msgstr "Имеет единицу измерения"

#: src/tables/part/PartParameterTemplateTable.tsx:47
msgid "Show templates with units"
msgstr "Показать шаблоны с единицами измерения"

#: src/tables/part/PartParameterTemplateTable.tsx:91
#: src/tables/part/PartParameterTemplateTable.tsx:166
msgid "Add Parameter Template"
msgstr "Создать шаблон параметра"

#: src/tables/part/PartParameterTemplateTable.tsx:105
msgid "Duplicate Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:117
msgid "Edit Parameter Template"
msgstr "Редактировать шаблон параметра"

#: src/tables/part/PartParameterTemplateTable.tsx:128
msgid "Delete Parameter Template"
msgstr "Удалить шаблон параметра"

#: src/tables/part/PartParameterTemplateTable.tsx:141
#~ msgid "Add parameter template"
#~ msgstr "Add parameter template"

#: src/tables/part/PartPurchaseOrdersTable.tsx:79
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:190
msgid "Total Quantity"
msgstr "Общее количество"

#: src/tables/part/PartPurchaseOrdersTable.tsx:123
msgid "Show pending orders"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:128
msgid "Show received items"
msgstr ""

#: src/tables/part/PartSalesAllocationsTable.tsx:90
msgid "View Sales Order"
msgstr "Показать заказ на продажу"

#: src/tables/part/PartTable.tsx:86
msgid "Minimum stock"
msgstr ""

#: src/tables/part/PartTable.tsx:185
msgid "Filter by part active status"
msgstr "Показать детали с активным статусом"

#: src/tables/part/PartTable.tsx:191
msgid "Filter by part locked status"
msgstr "Показать детали с заблокированным статусом"

#: src/tables/part/PartTable.tsx:197
msgid "Filter by assembly attribute"
msgstr "Показать сборные детали"

#: src/tables/part/PartTable.tsx:202
msgid "BOM Valid"
msgstr ""

#: src/tables/part/PartTable.tsx:203
msgid "Filter by parts with a valid BOM"
msgstr ""

#: src/tables/part/PartTable.tsx:209
msgid "Include parts in subcategories"
msgstr "Включить детали в подкатегориях"

#: src/tables/part/PartTable.tsx:215
msgid "Filter by component attribute"
msgstr "Показать детали, которые могут быть компонентом для сборки"

#: src/tables/part/PartTable.tsx:221
msgid "Filter by testable attribute"
msgstr "Показать тестируемые детали"

#: src/tables/part/PartTable.tsx:227
msgid "Filter by trackable attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:233
msgid "Filter by parts which have units"
msgstr "Фильтр по деталям, в которых задана единица измерения"

#: src/tables/part/PartTable.tsx:238
msgid "Has IPN"
msgstr "Есть внутренний артикул"

#: src/tables/part/PartTable.tsx:239
msgid "Filter by parts which have an internal part number"
msgstr "Показать детали с заданным внутренним артикулом"

#: src/tables/part/PartTable.tsx:244
msgid "Has Stock"
msgstr ""

#: src/tables/part/PartTable.tsx:245
msgid "Filter by parts which have stock"
msgstr ""

#: src/tables/part/PartTable.tsx:251
msgid "Filter by parts which have low stock"
msgstr "Показать детали с низким складским запасом"

#: src/tables/part/PartTable.tsx:256
msgid "Purchaseable"
msgstr "Можно закупать"

#: src/tables/part/PartTable.tsx:257
msgid "Filter by parts which are purchaseable"
msgstr "Показать детали, которые можно закупать"

#: src/tables/part/PartTable.tsx:262
msgid "Salable"
msgstr "Можно продавать"

#: src/tables/part/PartTable.tsx:263
msgid "Filter by parts which are salable"
msgstr "Показать детали, которые можно продавать"

#: src/tables/part/PartTable.tsx:268
#: src/tables/part/PartTable.tsx:272
#: src/tables/part/PartVariantTable.tsx:25
msgid "Virtual"
msgstr "Виртуальная"

#: src/tables/part/PartTable.tsx:269
msgid "Filter by parts which are virtual"
msgstr "Показать виртуальные детали"

#: src/tables/part/PartTable.tsx:273
msgid "Not Virtual"
msgstr "Не виртуальная"

#: src/tables/part/PartTable.tsx:278
msgid "Is Template"
msgstr "Шаблон"

#: src/tables/part/PartTable.tsx:279
msgid "Filter by parts which are templates"
msgstr "Показать шаблоны деталей"

#: src/tables/part/PartTable.tsx:284
msgid "Is Variant"
msgstr "Разновидности"

#: src/tables/part/PartTable.tsx:285
msgid "Filter by parts which are variants"
msgstr "Показать детали, которые являются разновидностями"

#: src/tables/part/PartTable.tsx:290
msgid "Is Revision"
msgstr ""

#: src/tables/part/PartTable.tsx:291
msgid "Filter by parts which are revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:295
msgid "Has Revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:296
msgid "Filter by parts which have revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:301
msgid "Filter by parts which have pricing information"
msgstr ""

#: src/tables/part/PartTable.tsx:307
msgid "Filter by parts which have available stock"
msgstr ""

#: src/tables/part/PartTable.tsx:313
msgid "Filter by parts to which the user is subscribed"
msgstr "Показать детали, на которые пользователь подписан"

#: src/tables/part/PartTable.tsx:322
#~ msgid "Has Stocktake"
#~ msgstr "Has Stocktake"

#: src/tables/part/PartTable.tsx:323
#~ msgid "Filter by parts which have stocktake information"
#~ msgstr "Filter by parts which have stocktake information"

#: src/tables/part/PartTable.tsx:363
#: src/tables/part/PartTable.tsx:397
msgid "Set Category"
msgstr ""

#: src/tables/part/PartTable.tsx:399
msgid "Set category for selected parts"
msgstr ""

#: src/tables/part/PartTable.tsx:409
msgid "Order selected parts"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:56
msgid "Test is defined for a parent template part"
msgstr "Тест определён в шаблонах тестов родителя"

#: src/tables/part/PartTestTemplateTable.tsx:70
msgid "Template Details"
msgstr "Сведения о шаблоне"

#: src/tables/part/PartTestTemplateTable.tsx:80
msgid "Results"
msgstr "Результаты"

#: src/tables/part/PartTestTemplateTable.tsx:113
msgid "Show required tests"
msgstr "Показать необходимые тесты"

#: src/tables/part/PartTestTemplateTable.tsx:118
msgid "Show enabled tests"
msgstr "Показать активные тесты"

#: src/tables/part/PartTestTemplateTable.tsx:122
msgid "Requires Value"
msgstr "Требуется значение"

#: src/tables/part/PartTestTemplateTable.tsx:123
msgid "Show tests that require a value"
msgstr "Показать тесты, в которых требуется значение"

#: src/tables/part/PartTestTemplateTable.tsx:127
msgid "Requires Attachment"
msgstr "Требуются вложения"

#: src/tables/part/PartTestTemplateTable.tsx:128
msgid "Show tests that require an attachment"
msgstr "Показать тесты, в которых требуется вложение"

#: src/tables/part/PartTestTemplateTable.tsx:132
msgid "Include Inherited"
msgstr "Включая унаследованные"

#: src/tables/part/PartTestTemplateTable.tsx:133
msgid "Show tests from inherited templates"
msgstr "Показать унаследованные тесты"

#: src/tables/part/PartTestTemplateTable.tsx:137
msgid "Has Results"
msgstr "Есть результат"

#: src/tables/part/PartTestTemplateTable.tsx:138
msgid "Show tests which have recorded results"
msgstr "Показать тесты, в которых есть записанные результаты"

#: src/tables/part/PartTestTemplateTable.tsx:160
#: src/tables/part/PartTestTemplateTable.tsx:243
msgid "Add Test Template"
msgstr "Создать шаблон теста"

#: src/tables/part/PartTestTemplateTable.tsx:176
msgid "Edit Test Template"
msgstr "Редактировать шаблон теста"

#: src/tables/part/PartTestTemplateTable.tsx:187
msgid "Delete Test Template"
msgstr "Удалить шаблон теста"

#: src/tables/part/PartTestTemplateTable.tsx:189
msgid "This action cannot be reversed"
msgstr "Это действие нельзя будет отменить"

#: src/tables/part/PartTestTemplateTable.tsx:191
msgid "Any tests results associated with this template will be deleted"
msgstr "Все результаты тестов, связанные с этим шаблоном будут удалены"

#: src/tables/part/PartTestTemplateTable.tsx:209
msgid "View Parent Part"
msgstr "Показать родительскую деталь"

#: src/tables/part/PartTestTemplateTable.tsx:263
msgid "Part templates cannot be edited, as the part is locked"
msgstr "Шаблоны нельзя редактировать, поскольку деталь заблокирована"

#: src/tables/part/PartThumbTable.tsx:224
msgid "Select"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:16
msgid "Show active variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:20
msgid "Template"
msgstr "Шаблон"

#: src/tables/part/PartVariantTable.tsx:21
msgid "Show template variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:26
msgid "Show virtual variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:31
msgid "Show trackable variants"
msgstr ""

#: src/tables/part/RelatedPartTable.tsx:104
#: src/tables/part/RelatedPartTable.tsx:137
msgid "Add Related Part"
msgstr "Создать связанную деталь"

#: src/tables/part/RelatedPartTable.tsx:109
#~ msgid "Add related part"
#~ msgstr "Add related part"

#: src/tables/part/RelatedPartTable.tsx:119
msgid "Delete Related Part"
msgstr "Удалить связанную деталь"

#: src/tables/part/RelatedPartTable.tsx:126
msgid "Edit Related Part"
msgstr "Редактировать связанную деталь"

#: src/tables/part/SelectionListTable.tsx:64
#: src/tables/part/SelectionListTable.tsx:115
msgid "Add Selection List"
msgstr "Создать список выбора"

#: src/tables/part/SelectionListTable.tsx:76
msgid "Edit Selection List"
msgstr "Редактировать список выбора"

#: src/tables/part/SelectionListTable.tsx:84
msgid "Delete Selection List"
msgstr "Удалить список выбора"

#: src/tables/plugin/PluginErrorTable.tsx:29
msgid "Stage"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:43
msgid "Plugin is active"
msgstr "Плагин активен"

#: src/tables/plugin/PluginListTable.tsx:49
msgid "Plugin is inactive"
msgstr "Плагин неактивен"

#: src/tables/plugin/PluginListTable.tsx:56
msgid "Plugin is not installed"
msgstr "Плагин не установлен"

#: src/tables/plugin/PluginListTable.tsx:78
#: src/tables/settings/ExportSessionTable.tsx:33
msgid "Plugin"
msgstr "Плагин"

#: src/tables/plugin/PluginListTable.tsx:95
#~ msgid "Plugin with key {pluginKey} not found"
#~ msgstr "Plugin with key {pluginKey} not found"

#: src/tables/plugin/PluginListTable.tsx:97
#~ msgid "An error occurred while fetching plugin details"
#~ msgstr "An error occurred while fetching plugin details"

#: src/tables/plugin/PluginListTable.tsx:106
#: src/tables/plugin/PluginListTable.tsx:422
msgid "Mandatory"
msgstr "Обязательно"

#: src/tables/plugin/PluginListTable.tsx:113
#~ msgid "Plugin with id {id} not found"
#~ msgstr "Plugin with id {id} not found"

#: src/tables/plugin/PluginListTable.tsx:120
msgid "Description not available"
msgstr "Описание недоступно"

#: src/tables/plugin/PluginListTable.tsx:122
#~ msgid "Plugin information"
#~ msgstr "Plugin information"

#: src/tables/plugin/PluginListTable.tsx:134
#~ msgid "Plugin Actions"
#~ msgstr "Plugin Actions"

#: src/tables/plugin/PluginListTable.tsx:138
#: src/tables/plugin/PluginListTable.tsx:141
#~ msgid "Edit plugin"
#~ msgstr "Edit plugin"

#: src/tables/plugin/PluginListTable.tsx:152
#: src/tables/plugin/PluginListTable.tsx:153
#~ msgid "Reload"
#~ msgstr "Reload"

#: src/tables/plugin/PluginListTable.tsx:153
msgid "Confirm plugin activation"
msgstr "Подтвердите активацию плагина"

#: src/tables/plugin/PluginListTable.tsx:154
msgid "Confirm plugin deactivation"
msgstr "Подтвердите деактивацию плагина"

#: src/tables/plugin/PluginListTable.tsx:159
msgid "The selected plugin will be activated"
msgstr "Выбранный плагин будет активирован"

#: src/tables/plugin/PluginListTable.tsx:160
msgid "The selected plugin will be deactivated"
msgstr "Выбранный плагин будет деактивирован"

#: src/tables/plugin/PluginListTable.tsx:163
#~ msgid "Package information"
#~ msgstr "Package information"

#: src/tables/plugin/PluginListTable.tsx:178
msgid "Deactivate"
msgstr "Деактивировать"

#: src/tables/plugin/PluginListTable.tsx:192
msgid "Activate"
msgstr "Активировать"

#: src/tables/plugin/PluginListTable.tsx:193
msgid "Activate selected plugin"
msgstr "Активировать выбранный плагин"

#: src/tables/plugin/PluginListTable.tsx:197
#~ msgid "Plugin settings"
#~ msgstr "Plugin settings"

#: src/tables/plugin/PluginListTable.tsx:205
msgid "Update selected plugin"
msgstr "Обновить выбранный плагин"

#: src/tables/plugin/PluginListTable.tsx:224
#: src/tables/stock/InstalledItemsTable.tsx:106
msgid "Uninstall"
msgstr "Удалить"

#: src/tables/plugin/PluginListTable.tsx:225
msgid "Uninstall selected plugin"
msgstr "Удалить выбранный плагин"

#: src/tables/plugin/PluginListTable.tsx:244
msgid "Delete selected plugin configuration"
msgstr "Удалить конфигурацию выбранного плагина"

#: src/tables/plugin/PluginListTable.tsx:260
msgid "Activate Plugin"
msgstr "Активировать плагин"

#: src/tables/plugin/PluginListTable.tsx:267
msgid "The plugin was activated"
msgstr "Плагин был активирован"

#: src/tables/plugin/PluginListTable.tsx:268
msgid "The plugin was deactivated"
msgstr "Плагин был деактивирован"

#: src/tables/plugin/PluginListTable.tsx:280
#~ msgid "Install plugin"
#~ msgstr "Install plugin"

#: src/tables/plugin/PluginListTable.tsx:281
#: src/tables/plugin/PluginListTable.tsx:368
msgid "Install Plugin"
msgstr "Установить плагин"

#: src/tables/plugin/PluginListTable.tsx:294
msgid "Install"
msgstr "Установить"

#: src/tables/plugin/PluginListTable.tsx:295
msgid "Plugin installed successfully"
msgstr "Плагин успешно установлен"

#: src/tables/plugin/PluginListTable.tsx:300
msgid "Uninstall Plugin"
msgstr "Удалить плагин"

#: src/tables/plugin/PluginListTable.tsx:308
#~ msgid "This action cannot be undone."
#~ msgstr "This action cannot be undone."

#: src/tables/plugin/PluginListTable.tsx:312
msgid "Confirm plugin uninstall"
msgstr "Подтвердите удаление плагина"

#: src/tables/plugin/PluginListTable.tsx:315
msgid "The selected plugin will be uninstalled."
msgstr "Выбранный плагин будет удалён."

#: src/tables/plugin/PluginListTable.tsx:320
msgid "Plugin uninstalled successfully"
msgstr "Плагин успешно удалён"

#: src/tables/plugin/PluginListTable.tsx:328
msgid "Delete Plugin"
msgstr "Удалить плагин"

#: src/tables/plugin/PluginListTable.tsx:329
msgid "Deleting this plugin configuration will remove all associated settings and data. Are you sure you want to delete this plugin?"
msgstr "Удаление этого плагина приведет к удалению всех связанных настроек и данных. Вы уверены, что хотите удалить этот плагин?"

#: src/tables/plugin/PluginListTable.tsx:338
#~ msgid "Deactivate Plugin"
#~ msgstr "Deactivate Plugin"

#: src/tables/plugin/PluginListTable.tsx:342
msgid "Plugins reloaded"
msgstr "Плагины перезагружены"

#: src/tables/plugin/PluginListTable.tsx:343
msgid "Plugins were reloaded successfully"
msgstr "Плагины были успешно перезагружены"

#: src/tables/plugin/PluginListTable.tsx:354
#~ msgid "The following plugin will be activated"
#~ msgstr "The following plugin will be activated"

#: src/tables/plugin/PluginListTable.tsx:355
#~ msgid "The following plugin will be deactivated"
#~ msgstr "The following plugin will be deactivated"

#: src/tables/plugin/PluginListTable.tsx:361
msgid "Reload Plugins"
msgstr "Перезагрузить плагины"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Activating plugin"
#~ msgstr "Activating plugin"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Deactivating plugin"
#~ msgstr "Deactivating plugin"

#: src/tables/plugin/PluginListTable.tsx:385
msgid "Plugin Detail"
msgstr "Сведения о плагине"

#: src/tables/plugin/PluginListTable.tsx:392
#~ msgid "Plugin updated"
#~ msgstr "Plugin updated"

#: src/tables/plugin/PluginListTable.tsx:403
#~ msgid "Error updating plugin"
#~ msgstr "Error updating plugin"

#: src/tables/plugin/PluginListTable.tsx:427
msgid "Sample"
msgstr "Пример"

#: src/tables/plugin/PluginListTable.tsx:432
#: src/tables/stock/StockItemTable.tsx:377
msgid "Installed"
msgstr "Установлено"

#: src/tables/plugin/PluginListTable.tsx:615
#~ msgid "Plugin detail"
#~ msgstr "Plugin detail"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:60
#~ msgid "Parameter updated"
#~ msgstr "Parameter updated"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:63
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:112
msgid "Add Parameter"
msgstr "Создать параметр"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:73
#~ msgid "Parameter deleted"
#~ msgstr "Parameter deleted"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
msgid "Edit Parameter"
msgstr "Редактировать параметр"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
#~ msgid "Are you sure you want to delete this parameter?"
#~ msgstr "Are you sure you want to delete this parameter?"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:82
msgid "Delete Parameter"
msgstr "Удалить параметр"

#: src/tables/purchasing/ManufacturerPartTable.tsx:56
#: src/tables/purchasing/SupplierPartTable.tsx:80
msgid "MPN"
msgstr "Артикул производителя"

#: src/tables/purchasing/ManufacturerPartTable.tsx:63
#~ msgid "Create Manufacturer Part"
#~ msgstr "Create Manufacturer Part"

#: src/tables/purchasing/ManufacturerPartTable.tsx:100
#~ msgid "Manufacturer part updated"
#~ msgstr "Manufacturer part updated"

#: src/tables/purchasing/ManufacturerPartTable.tsx:112
#~ msgid "Manufacturer part deleted"
#~ msgstr "Manufacturer part deleted"

#: src/tables/purchasing/ManufacturerPartTable.tsx:114
#~ msgid "Are you sure you want to remove this manufacturer part?"
#~ msgstr "Are you sure you want to remove this manufacturer part?"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:109
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:391
msgid "Import Line Items"
msgstr "Импортировать позиции"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:230
msgid "Supplier Code"
msgstr "Код поставщика"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:237
msgid "Supplier Link"
msgstr "Ссылка поставщика"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:244
msgid "Manufacturer Code"
msgstr "Код производителя"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:273
msgid "Show line items which have been received"
msgstr "Показать полученные позиции"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
msgid "Receive line item"
msgstr "Получить позицию"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
#: src/tables/sales/ReturnOrderLineItemTable.tsx:160
#: src/tables/sales/SalesOrderLineItemTable.tsx:258
#~ msgid "Add line item"
#~ msgstr "Add line item"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:408
msgid "Receive items"
msgstr "Получить позиции"

#: src/tables/purchasing/SupplierPartTable.tsx:111
msgid "Base units"
msgstr "Базовая единица измерения"

#: src/tables/purchasing/SupplierPartTable.tsx:168
msgid "Add supplier part"
msgstr "Создать деталь поставщика"

#: src/tables/purchasing/SupplierPartTable.tsx:180
msgid "Show active supplier parts"
msgstr "Показать активные детали поставщиков"

#: src/tables/purchasing/SupplierPartTable.tsx:184
msgid "Active Part"
msgstr "Активная деталь"

#: src/tables/purchasing/SupplierPartTable.tsx:185
msgid "Show active internal parts"
msgstr "Показать активные детали"

#: src/tables/purchasing/SupplierPartTable.tsx:189
msgid "Active Supplier"
msgstr "Активный поставщик"

#: src/tables/purchasing/SupplierPartTable.tsx:190
msgid "Show active suppliers"
msgstr "Показать активных поставщиков"

#: src/tables/purchasing/SupplierPartTable.tsx:193
#~ msgid "Supplier part updated"
#~ msgstr "Supplier part updated"

#: src/tables/purchasing/SupplierPartTable.tsx:195
msgid "Show supplier parts with stock"
msgstr "Показать детали поставщиков в наличии"

#: src/tables/purchasing/SupplierPartTable.tsx:205
#~ msgid "Supplier part deleted"
#~ msgstr "Supplier part deleted"

#: src/tables/purchasing/SupplierPartTable.tsx:207
#~ msgid "Are you sure you want to remove this supplier part?"
#~ msgstr "Are you sure you want to remove this supplier part?"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:154
msgid "Received Date"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:168
msgid "Show items which have been received"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:173
msgid "Filter by line item status"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:191
msgid "Receive selected items"
msgstr "Получить выбранные элементы"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:223
msgid "Receive Item"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:86
msgid "Show outstanding allocations"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:90
msgid "Assigned to Shipment"
msgstr "Назначить на доставку"

#: src/tables/sales/SalesOrderAllocationTable.tsx:91
msgid "Show allocations assigned to a shipment"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:153
msgid "Available Quantity"
msgstr "Доступное количество"

#: src/tables/sales/SalesOrderAllocationTable.tsx:160
msgid "Allocated Quantity"
msgstr "Зарезервированное количество"

#: src/tables/sales/SalesOrderAllocationTable.tsx:174
#: src/tables/sales/SalesOrderAllocationTable.tsx:188
msgid "No shipment"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:186
msgid "Not shipped"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:208
#: src/tables/sales/SalesOrderAllocationTable.tsx:230
msgid "Edit Allocation"
msgstr "Редактировать резервирование"

#: src/tables/sales/SalesOrderAllocationTable.tsx:215
#: src/tables/sales/SalesOrderAllocationTable.tsx:238
msgid "Delete Allocation"
msgstr "Отменить резервирование"

#: src/tables/sales/SalesOrderAllocationTable.tsx:293
msgid "Assign to Shipment"
msgstr "Назначить на доставку"

#: src/tables/sales/SalesOrderAllocationTable.tsx:309
msgid "Assign to shipment"
msgstr "Назначить на доставку"

#: src/tables/sales/SalesOrderLineItemTable.tsx:272
msgid "Allocate Serial Numbers"
msgstr "Выделить серийные номера"

#: src/tables/sales/SalesOrderLineItemTable.tsx:280
#~ msgid "Allocate stock"
#~ msgstr "Allocate stock"

#: src/tables/sales/SalesOrderLineItemTable.tsx:291
#~ msgid "Allocate Serials"
#~ msgstr "Allocate Serials"

#: src/tables/sales/SalesOrderLineItemTable.tsx:320
msgid "Show lines which are fully allocated"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:325
msgid "Show lines which are completed"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:402
msgid "Allocate serials"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:419
msgid "Build stock"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:436
msgid "Order stock"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:51
#~ msgid "Delete Shipment"
#~ msgstr "Delete Shipment"

#: src/tables/sales/SalesOrderShipmentTable.tsx:55
msgid "Create Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:102
msgid "Items"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:137
msgid "View Shipment"
msgstr "Показать доставку"

#: src/tables/sales/SalesOrderShipmentTable.tsx:154
msgid "Edit shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:162
msgid "Cancel shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:177
msgid "Add shipment"
msgstr "Создать доставку"

#: src/tables/sales/SalesOrderShipmentTable.tsx:191
msgid "Show shipments which have been shipped"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:196
msgid "Show shipments which have been delivered"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:31
#: src/tables/settings/ApiTokenTable.tsx:45
msgid "Generate Token"
msgstr "Создать токен"

#: src/tables/settings/ApiTokenTable.tsx:33
msgid "Token generated"
msgstr "Токен создан"

#: src/tables/settings/ApiTokenTable.tsx:68
#: src/tables/settings/ApiTokenTable.tsx:123
msgid "Revoked"
msgstr "Отменен"

#: src/tables/settings/ApiTokenTable.tsx:72
#: src/tables/settings/ApiTokenTable.tsx:185
msgid "Token"
msgstr "Токен"

#: src/tables/settings/ApiTokenTable.tsx:79
msgid "In Use"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:88
msgid "Last Seen"
msgstr "Последнее использование"

#: src/tables/settings/ApiTokenTable.tsx:93
msgid "Expiry"
msgstr "Истекает"

#: src/tables/settings/ApiTokenTable.tsx:124
msgid "Show revoked tokens"
msgstr "Показать отменённые токены"

#: src/tables/settings/ApiTokenTable.tsx:143
msgid "Revoke"
msgstr "Отменить"

#: src/tables/settings/ApiTokenTable.tsx:167
msgid "Error revoking token"
msgstr "Ошибка при отмене токена"

#: src/tables/settings/ApiTokenTable.tsx:189
msgid "Tokens are only shown once - make sure to note it down."
msgstr "Токены показываются только один раз - обязательно запишите его."

#: src/tables/settings/BarcodeScanHistoryTable.tsx:60
msgid "Barcode Information"
msgstr "Информация о штрихкоде"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:85
msgid "Endpoint"
msgstr "Конечная точка"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:89
#: src/tables/settings/BarcodeScanHistoryTable.tsx:208
#: src/tables/stock/StockItemTestResultTable.tsx:185
msgid "Result"
msgstr "Результат"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:97
msgid "Context"
msgstr "Контекст"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:118
msgid "Response"
msgstr "Ответ"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:209
msgid "Filter by result"
msgstr "Фильтр по результату"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:223
msgid "Delete Barcode Scan Record"
msgstr "Удалить запись сканирования штрихкода"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:249
msgid "Barcode Scan Details"
msgstr "Детали сканирования штрихкода"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:259
msgid "Logging Disabled"
msgstr "Журналирование отключено"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:261
msgid "Barcode logging is not enabled"
msgstr "Журналирование штрихкода отключено"

#: src/tables/settings/CustomStateTable.tsx:63
msgid "Status Group"
msgstr "Группа статусов"

#: src/tables/settings/CustomStateTable.tsx:84
msgid "Logical State"
msgstr "Логическое состояние"

#: src/tables/settings/CustomStateTable.tsx:96
msgid "Identifier"
msgstr "Идентификатор"

#: src/tables/settings/CustomStateTable.tsx:115
#~ msgid "Add state"
#~ msgstr "Add state"

#: src/tables/settings/CustomStateTable.tsx:133
#: src/tables/settings/CustomStateTable.tsx:140
#: src/tables/settings/CustomStateTable.tsx:202
msgid "Add State"
msgstr "Создать состояние"

#: src/tables/settings/CustomStateTable.tsx:153
msgid "Edit State"
msgstr "Редактировать состояние"

#: src/tables/settings/CustomStateTable.tsx:161
msgid "Delete State"
msgstr "Удалить состояние"

#: src/tables/settings/CustomUnitsTable.tsx:54
msgid "Add Custom Unit"
msgstr "Создать единицу измерения"

#: src/tables/settings/CustomUnitsTable.tsx:64
msgid "Edit Custom Unit"
msgstr "Редактировать единицу измерения"

#: src/tables/settings/CustomUnitsTable.tsx:72
msgid "Delete Custom Unit"
msgstr "Удалить единицу измерения"

#: src/tables/settings/CustomUnitsTable.tsx:103
msgid "Add custom unit"
msgstr "Создать единицу измерения"

#: src/tables/settings/EmailTable.tsx:21
#: src/tables/settings/EmailTable.tsx:36
msgid "Send Test Email"
msgstr "Отправить тестовое электронное письмо"

#: src/tables/settings/EmailTable.tsx:23
msgid "Email sent successfully"
msgstr "Электронное письмо успешно отправлено"

#: src/tables/settings/EmailTable.tsx:49
msgid "Delete Email"
msgstr ""

#: src/tables/settings/EmailTable.tsx:50
msgid "Email deleted successfully"
msgstr ""

#: src/tables/settings/EmailTable.tsx:58
msgid "Subject"
msgstr "Тема"

#: src/tables/settings/EmailTable.tsx:63
msgid "To"
msgstr "Кому"

#: src/tables/settings/EmailTable.tsx:68
msgid "Sender"
msgstr "Отправитель"

#: src/tables/settings/EmailTable.tsx:78
msgid "Announced"
msgstr "Анонсировано"

#: src/tables/settings/EmailTable.tsx:80
msgid "Sent"
msgstr "Отправлено"

#: src/tables/settings/EmailTable.tsx:82
msgid "Failed"
msgstr "Ошибка"

#: src/tables/settings/EmailTable.tsx:86
msgid "Read"
msgstr "Прочитано"

#: src/tables/settings/EmailTable.tsx:88
msgid "Confirmed"
msgstr "Подтверждено"

#: src/tables/settings/EmailTable.tsx:96
msgid "Direction"
msgstr "Направление"

#: src/tables/settings/EmailTable.tsx:99
msgid "Incoming"
msgstr "Входящее"

#: src/tables/settings/EmailTable.tsx:99
msgid "Outgoing"
msgstr "Исходящее"

#: src/tables/settings/ErrorTable.tsx:51
#~ msgid "Delete error report"
#~ msgstr "Delete error report"

#: src/tables/settings/ErrorTable.tsx:67
msgid "Traceback"
msgstr "Трассировка"

#: src/tables/settings/ErrorTable.tsx:103
msgid "When"
msgstr "Когда"

#: src/tables/settings/ErrorTable.tsx:113
msgid "Error Information"
msgstr "Сведения об ошибке"

#: src/tables/settings/ErrorTable.tsx:123
msgid "Delete Error Report"
msgstr "Удалить отчет об ошибках"

#: src/tables/settings/ErrorTable.tsx:125
msgid "Are you sure you want to delete this error report?"
msgstr "Вы уверены, что хотите удалить этот отчёт об ошибках?"

#: src/tables/settings/ErrorTable.tsx:127
msgid "Error report deleted"
msgstr "Отчёт об ошибках удалён"

#: src/tables/settings/ErrorTable.tsx:146
#: src/tables/settings/FailedTasksTable.tsx:65
msgid "Error Details"
msgstr "Подробнее об ошибке"

#: src/tables/settings/ExportSessionTable.tsx:28
msgid "Output Type"
msgstr ""

#: src/tables/settings/ExportSessionTable.tsx:38
msgid "Exported On"
msgstr ""

#: src/tables/settings/ExportSessionTable.tsx:59
msgid "Delete Output"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:32
#: src/tables/settings/PendingTasksTable.tsx:28
#: src/tables/settings/ScheduledTasksTable.tsx:19
msgid "Task"
msgstr "Задача"

#: src/tables/settings/FailedTasksTable.tsx:38
#: src/tables/settings/PendingTasksTable.tsx:33
msgid "Task ID"
msgstr "Идентификатор задачи"

#: src/tables/settings/FailedTasksTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:230
msgid "Started"
msgstr "Запущен"

#: src/tables/settings/FailedTasksTable.tsx:54
msgid "Attempts"
msgstr "Попыток"

#: src/tables/settings/FailedTasksTable.tsx:92
msgid "No Information"
msgstr "Нет информации"

#: src/tables/settings/FailedTasksTable.tsx:93
msgid "No error details are available for this task"
msgstr "Нет подробностей об ошибке для этой задачи"

#: src/tables/settings/GroupTable.tsx:71
msgid "Group with id {id} not found"
msgstr "Группа с идентификатором {id} не найдена"

#: src/tables/settings/GroupTable.tsx:73
msgid "An error occurred while fetching group details"
msgstr "Произошла ошибка при получении данных группы"

#: src/tables/settings/GroupTable.tsx:96
#: src/tables/settings/GroupTable.tsx:197
msgid "Name of the user group"
msgstr "Название группы пользователей"

#: src/tables/settings/GroupTable.tsx:117
#~ msgid "Permission set"
#~ msgstr "Permission set"

#: src/tables/settings/GroupTable.tsx:170
#: src/tables/settings/UserTable.tsx:315
msgid "Open Profile"
msgstr "Открыть профиль"

#: src/tables/settings/GroupTable.tsx:185
msgid "Delete group"
msgstr "Удалить группу"

#: src/tables/settings/GroupTable.tsx:186
msgid "Group deleted"
msgstr "Группа удалена"

#: src/tables/settings/GroupTable.tsx:188
msgid "Are you sure you want to delete this group?"
msgstr "Вы уверены, что хотите удалить эту группу?"

#: src/tables/settings/GroupTable.tsx:193
msgid "Add Group"
msgstr "Добавить группу"

#: src/tables/settings/GroupTable.tsx:210
msgid "Add group"
msgstr "Добавить группу"

#: src/tables/settings/GroupTable.tsx:213
#~ msgid "Edit group"
#~ msgstr "Edit group"

#: src/tables/settings/GroupTable.tsx:231
msgid "Edit Group"
msgstr "Редактировать группу"

#: src/tables/settings/ImportSessionTable.tsx:38
msgid "Delete Import Session"
msgstr "Удалить сессию импорта"

#: src/tables/settings/ImportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:129
msgid "Create Import Session"
msgstr "Создать сессию импорта"

#: src/tables/settings/ImportSessionTable.tsx:72
msgid "Uploaded"
msgstr "Загружено"

#: src/tables/settings/ImportSessionTable.tsx:83
msgid "Imported Rows"
msgstr "Импортированные строки"

#: src/tables/settings/ImportSessionTable.tsx:111
#: src/tables/settings/TemplateTable.tsx:368
msgid "Model Type"
msgstr "Тип модели"

#: src/tables/settings/ImportSessionTable.tsx:112
#: src/tables/settings/TemplateTable.tsx:369
msgid "Filter by target model type"
msgstr "Фильтр по типу модели"

#: src/tables/settings/ImportSessionTable.tsx:118
msgid "Filter by import session status"
msgstr "Показать по статусу сессии импорта"

#: src/tables/settings/PendingTasksTable.tsx:47
msgid "Arguments"
msgstr "Аргументы"

#: src/tables/settings/PendingTasksTable.tsx:61
msgid "Remove all pending tasks"
msgstr "Удалить все ожидающие задачи"

#: src/tables/settings/PendingTasksTable.tsx:69
msgid "All pending tasks deleted"
msgstr "Все ожидающие задачи удалены"

#: src/tables/settings/PendingTasksTable.tsx:76
msgid "Error while deleting all pending tasks"
msgstr "Ошибка при удалении всех ожидающих задач"

#: src/tables/settings/ProjectCodeTable.tsx:46
msgid "Add Project Code"
msgstr "Создать код проекта"

#: src/tables/settings/ProjectCodeTable.tsx:58
msgid "Edit Project Code"
msgstr "Редактировать код проекта"

#: src/tables/settings/ProjectCodeTable.tsx:66
msgid "Delete Project Code"
msgstr "Удалить код проекта"

#: src/tables/settings/ProjectCodeTable.tsx:97
msgid "Add project code"
msgstr "Создать код проекта"

#: src/tables/settings/ScheduledTasksTable.tsx:28
msgid "Last Run"
msgstr "Последний запуск"

#: src/tables/settings/ScheduledTasksTable.tsx:50
msgid "Next Run"
msgstr "Следующий запуск"

#: src/tables/settings/StocktakeReportTable.tsx:28
#~ msgid "Report"
#~ msgstr "Report"

#: src/tables/settings/StocktakeReportTable.tsx:36
#~ msgid "Part Count"
#~ msgstr "Part Count"

#: src/tables/settings/StocktakeReportTable.tsx:59
#~ msgid "Delete Report"
#~ msgstr "Delete Report"

#: src/tables/settings/TemplateTable.tsx:120
#~ msgid "{templateTypeTranslation} with id {id} not found"
#~ msgstr "{templateTypeTranslation} with id {id} not found"

#: src/tables/settings/TemplateTable.tsx:124
#~ msgid "An error occurred while fetching {templateTypeTranslation} details"
#~ msgstr "An error occurred while fetching {templateTypeTranslation} details"

#: src/tables/settings/TemplateTable.tsx:146
#~ msgid "actions"
#~ msgstr "actions"

#: src/tables/settings/TemplateTable.tsx:165
msgid "Template not found"
msgstr "Шаблон не найден"

#: src/tables/settings/TemplateTable.tsx:167
msgid "An error occurred while fetching template details"
msgstr "Произошла ошибка при получении сведений о шаблоне"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Add new"
#~ msgstr "Add new"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Create new"
#~ msgstr "Create new"

#: src/tables/settings/TemplateTable.tsx:261
msgid "Modify"
msgstr "Изменить"

#: src/tables/settings/TemplateTable.tsx:262
msgid "Modify template file"
msgstr "Изменить файл шаблона"

#: src/tables/settings/TemplateTable.tsx:313
#: src/tables/settings/TemplateTable.tsx:381
msgid "Edit Template"
msgstr "Редактировать шаблон"

#: src/tables/settings/TemplateTable.tsx:321
msgid "Delete template"
msgstr "Удалить шаблон"

#: src/tables/settings/TemplateTable.tsx:327
msgid "Add Template"
msgstr "Создать шаблон"

#: src/tables/settings/TemplateTable.tsx:340
msgid "Add template"
msgstr "Создать шаблон"

#: src/tables/settings/TemplateTable.tsx:363
msgid "Filter by enabled status"
msgstr "Фильтр по статусу"

#: src/tables/settings/TemplateTable.tsx:420
#~ msgid "Report Output"
#~ msgstr "Report Output"

#: src/tables/settings/UserTable.tsx:123
msgid "Groups updated"
msgstr "Группы обновлены"

#: src/tables/settings/UserTable.tsx:124
msgid "User groups updated successfully"
msgstr "Группы пользователя обновлены"

#: src/tables/settings/UserTable.tsx:131
msgid "Error updating user groups"
msgstr "Ошибка обновления групп пользователя"

#: src/tables/settings/UserTable.tsx:150
msgid "User with id {id} not found"
msgstr "Пользователь с идентификатором {id} не найден"

#: src/tables/settings/UserTable.tsx:152
msgid "An error occurred while fetching user details"
msgstr "Произошла ошибка при получении данных пользователя"

#: src/tables/settings/UserTable.tsx:154
#~ msgid "No groups"
#~ msgstr "No groups"

#: src/tables/settings/UserTable.tsx:178
msgid "Is Active"
msgstr "Активен"

#: src/tables/settings/UserTable.tsx:179
msgid "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
msgstr "Указывает, следует ли рассматривать этого пользователя как активного. Отмените этот выбор вместо удаления учетных записей."

#: src/tables/settings/UserTable.tsx:183
msgid "Is Staff"
msgstr "Сотрудник"

#: src/tables/settings/UserTable.tsx:184
msgid "Designates whether the user can log into the django admin site."
msgstr "Определяет, может ли пользователь войти в админ-панель django."

#: src/tables/settings/UserTable.tsx:188
msgid "Is Superuser"
msgstr "Суперпользователь"

#: src/tables/settings/UserTable.tsx:189
msgid "Designates that this user has all permissions without explicitly assigning them."
msgstr "Определяет, что у пользователя есть все разрешения без их явного назначения."

#: src/tables/settings/UserTable.tsx:199
msgid "You cannot edit the rights for the currently logged-in user."
msgstr "Вы не можете редактировать права пользователя, вошедшего в систему."

#: src/tables/settings/UserTable.tsx:218
msgid "User Groups"
msgstr "Группы пользователей"

#: src/tables/settings/UserTable.tsx:305
#~ msgid "Edit user"
#~ msgstr "Edit user"

#: src/tables/settings/UserTable.tsx:332
msgid "Lock user"
msgstr "Заблокировать пользователя"

#: src/tables/settings/UserTable.tsx:342
msgid "Unlock user"
msgstr "Разблокировать пользователя"

#: src/tables/settings/UserTable.tsx:358
msgid "Delete user"
msgstr "Удалить пользователя"

#: src/tables/settings/UserTable.tsx:359
msgid "User deleted"
msgstr "Пользователь удалён"

#: src/tables/settings/UserTable.tsx:361
msgid "Are you sure you want to delete this user?"
msgstr "Вы уверены, что хотите удалить этого пользователя?"

#: src/tables/settings/UserTable.tsx:367
msgid "Add User"
msgstr "Создать пользователя"

#: src/tables/settings/UserTable.tsx:375
msgid "Added user"
msgstr "Пользователь добавлен"

#: src/tables/settings/UserTable.tsx:382
msgid "Set Password"
msgstr "Установить пароль"

#: src/tables/settings/UserTable.tsx:387
msgid "Password updated"
msgstr "Пароль обновлён"

#: src/tables/settings/UserTable.tsx:398
msgid "Add user"
msgstr "Создать пользователя"

#: src/tables/settings/UserTable.tsx:411
msgid "Show active users"
msgstr "Показать активных пользователей"

#: src/tables/settings/UserTable.tsx:416
msgid "Show staff users"
msgstr "Показать сотрудников"

#: src/tables/settings/UserTable.tsx:421
msgid "Show superusers"
msgstr "Показать суперпользователей"

#: src/tables/settings/UserTable.tsx:440
msgid "Edit User"
msgstr "Редактировать пользователя"

#: src/tables/settings/UserTable.tsx:476
msgid "User updated"
msgstr "Пользователь обновлён"

#: src/tables/settings/UserTable.tsx:477
msgid "User updated successfully"
msgstr "Пользователь успешно обновлён"

#: src/tables/settings/UserTable.tsx:483
msgid "Error updating user"
msgstr "Ошибка обновления пользователя"

#: src/tables/stock/InstalledItemsTable.tsx:37
#: src/tables/stock/InstalledItemsTable.tsx:89
msgid "Install Item"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:39
msgid "Item installed"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:50
msgid "Uninstall Item"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:52
msgid "Item uninstalled"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:107
msgid "Uninstall stock item"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:44
#: src/tables/stock/LocationTypesTable.tsx:111
msgid "Add Location Type"
msgstr "Добавить тип места хранения"

#: src/tables/stock/LocationTypesTable.tsx:52
msgid "Edit Location Type"
msgstr "Редактировать тип места хранения"

#: src/tables/stock/LocationTypesTable.tsx:60
msgid "Delete Location Type"
msgstr "Удалить тип места хранения"

#: src/tables/stock/LocationTypesTable.tsx:68
msgid "Icon"
msgstr "Значок"

#: src/tables/stock/StockItemTable.tsx:107
msgid "This stock item is in production"
msgstr "Складская позиция в процессе производства"

#: src/tables/stock/StockItemTable.tsx:114
msgid "This stock item has been assigned to a sales order"
msgstr "Складская позиция зарезервирована в заказе на продажу"

#: src/tables/stock/StockItemTable.tsx:121
msgid "This stock item has been assigned to a customer"
msgstr "Складская позиция передана клиенту"

#: src/tables/stock/StockItemTable.tsx:128
msgid "This stock item is installed in another stock item"
msgstr "Складская позиция установлена в другой складской позиции"

#: src/tables/stock/StockItemTable.tsx:135
msgid "This stock item has been consumed by a build order"
msgstr "Складская позиция израсходована в заказе на сборку"

#: src/tables/stock/StockItemTable.tsx:142
msgid "This stock item is unavailable"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:151
msgid "This stock item has expired"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:155
msgid "This stock item is stale"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:167
msgid "This stock item is fully allocated"
msgstr "Складская позиция полностью зарезервирована"

#: src/tables/stock/StockItemTable.tsx:174
msgid "This stock item is partially allocated"
msgstr "Складская позиция частично зарезервирована"

#: src/tables/stock/StockItemTable.tsx:202
msgid "This stock item has been depleted"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:301
#~ msgid "Show stock for assmebled parts"
#~ msgstr "Show stock for assmebled parts"

#: src/tables/stock/StockItemTable.tsx:306
msgid "Stocktake Date"
msgstr "Дата инвентаризации"

#: src/tables/stock/StockItemTable.tsx:324
msgid "Show stock for active parts"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:335
msgid "Show stock for assembled parts"
msgstr "Показать запасы для собранных частей"

#: src/tables/stock/StockItemTable.tsx:340
msgid "Show items which have been allocated"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:345
msgid "Show items which are available"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:349
#: src/tables/stock/StockLocationTable.tsx:38
msgid "Include Sublocations"
msgstr "Включая вложенные склады"

#: src/tables/stock/StockItemTable.tsx:350
msgid "Include stock in sublocations"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:354
msgid "Depleted"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:355
msgid "Show depleted stock items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:360
msgid "Show items which are in stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:362
#~ msgid "Include stock items for variant parts"
#~ msgstr "Include stock items for variant parts"

#: src/tables/stock/StockItemTable.tsx:365
msgid "Show items which are in production"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:373
msgid "Show items which have been consumed by a build order"
msgstr "Показать элементы, которые были израсходованы в заказе на сборку"

#: src/tables/stock/StockItemTable.tsx:378
msgid "Show stock items which are installed in other items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:382
msgid "Sent to Customer"
msgstr "Отправлены покупателю"

#: src/tables/stock/StockItemTable.tsx:383
msgid "Show items which have been sent to a customer"
msgstr "Показать элементы, которые были отправлены покупателю"

#: src/tables/stock/StockItemTable.tsx:394
msgid "Show tracked items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:397
#~ msgid "Serial Number LTE"
#~ msgstr "Serial Number LTE"

#: src/tables/stock/StockItemTable.tsx:398
msgid "Has Purchase Price"
msgstr "Есть цена закупки"

#: src/tables/stock/StockItemTable.tsx:399
msgid "Show items which have a purchase price"
msgstr "Показать элементы, у которых есть цена закупки"

#: src/tables/stock/StockItemTable.tsx:403
#~ msgid "Serial Number GTE"
#~ msgstr "Serial Number GTE"

#: src/tables/stock/StockItemTable.tsx:404
msgid "Show items which have expired"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:410
msgid "Show items which are stale"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:415
msgid "Expired Before"
msgstr "Срок годности раньше"

#: src/tables/stock/StockItemTable.tsx:416
msgid "Show items which expired before this date"
msgstr "Показать элементы, срок годности которых истекает раньше указанной даты"

#: src/tables/stock/StockItemTable.tsx:422
msgid "Expired After"
msgstr "Срок годности позже"

#: src/tables/stock/StockItemTable.tsx:423
msgid "Show items which expired after this date"
msgstr "Показать элементы, срок годности которых истекает позже указанной даты"

#: src/tables/stock/StockItemTable.tsx:429
msgid "Updated Before"
msgstr "Обновлены раньше"

#: src/tables/stock/StockItemTable.tsx:430
msgid "Show items updated before this date"
msgstr "Показать элементы, которые обновлены раньше указанной даты"

#: src/tables/stock/StockItemTable.tsx:435
msgid "Updated After"
msgstr "Обновлены позже"

#: src/tables/stock/StockItemTable.tsx:436
msgid "Show items updated after this date"
msgstr "Показать элементы, которые обновлены позже указанной  даты"

#: src/tables/stock/StockItemTable.tsx:441
msgid "Stocktake Before"
msgstr "Инвентаризация раньше"

#: src/tables/stock/StockItemTable.tsx:442
msgid "Show items counted before this date"
msgstr "Показать позиции с проведенной инвентаризацией раньше указанной даты"

#: src/tables/stock/StockItemTable.tsx:447
msgid "Stocktake After"
msgstr "Инвентаризация позже"

#: src/tables/stock/StockItemTable.tsx:448
msgid "Show items counted after this date"
msgstr "Показать позиции с проведенной инвентаризацией позже указанной даты"

#: src/tables/stock/StockItemTable.tsx:453
msgid "External Location"
msgstr "Сторонний склад"

#: src/tables/stock/StockItemTable.tsx:454
msgid "Show items in an external location"
msgstr "Показать элементы со стороннего склада"

#: src/tables/stock/StockItemTable.tsx:528
#~ msgid "Delete stock items"
#~ msgstr "Delete stock items"

#: src/tables/stock/StockItemTable.tsx:559
msgid "Order items"
msgstr "Закупить детали"

#: src/tables/stock/StockItemTable.tsx:595
#~ msgid "Add a new stock item"
#~ msgstr "Add a new stock item"

#: src/tables/stock/StockItemTable.tsx:604
#~ msgid "Remove some quantity from a stock item"
#~ msgstr "Remove some quantity from a stock item"

#: src/tables/stock/StockItemTable.tsx:615
#~ msgid "Move Stock items to new locations"
#~ msgstr "Move Stock items to new locations"

#: src/tables/stock/StockItemTable.tsx:622
#~ msgid "Change stock status"
#~ msgstr "Change stock status"

#: src/tables/stock/StockItemTable.tsx:624
#~ msgid "Change the status of stock items"
#~ msgstr "Change the status of stock items"

#: src/tables/stock/StockItemTable.tsx:631
#~ msgid "Merge stock"
#~ msgstr "Merge stock"

#: src/tables/stock/StockItemTable.tsx:633
#~ msgid "Merge stock items"
#~ msgstr "Merge stock items"

#: src/tables/stock/StockItemTable.tsx:642
#~ msgid "Order new stock"
#~ msgstr "Order new stock"

#: src/tables/stock/StockItemTable.tsx:653
#~ msgid "Assign to customer"
#~ msgstr "Assign to customer"

#: src/tables/stock/StockItemTable.tsx:655
#~ msgid "Assign items to a customer"
#~ msgstr "Assign items to a customer"

#: src/tables/stock/StockItemTable.tsx:662
#~ msgid "Delete stock"
#~ msgstr "Delete stock"

#: src/tables/stock/StockItemTestResultTable.tsx:140
msgid "Test"
msgstr "Тест"

#: src/tables/stock/StockItemTestResultTable.tsx:174
msgid "Test result for installed stock item"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:205
msgid "Attachment"
msgstr "Вложение"

#: src/tables/stock/StockItemTestResultTable.tsx:224
msgid "Test station"
msgstr "Испытательное оборудование"

#: src/tables/stock/StockItemTestResultTable.tsx:246
msgid "Finished"
msgstr "Завершён"

#: src/tables/stock/StockItemTestResultTable.tsx:304
#: src/tables/stock/StockItemTestResultTable.tsx:375
msgid "Edit Test Result"
msgstr "Редактировать результаты теста"

#: src/tables/stock/StockItemTestResultTable.tsx:306
msgid "Test result updated"
msgstr "Результаты теста обновлены"

#: src/tables/stock/StockItemTestResultTable.tsx:312
#: src/tables/stock/StockItemTestResultTable.tsx:384
msgid "Delete Test Result"
msgstr "Удалить результат теста"

#: src/tables/stock/StockItemTestResultTable.tsx:314
msgid "Test result deleted"
msgstr "Результаты теста удалены"

#: src/tables/stock/StockItemTestResultTable.tsx:328
msgid "Test Passed"
msgstr "Тест пройден"

#: src/tables/stock/StockItemTestResultTable.tsx:329
msgid "Test result has been recorded"
msgstr "Результат теста записан"

#: src/tables/stock/StockItemTestResultTable.tsx:336
msgid "Failed to record test result"
msgstr "Не удалось записать результат теста"

#: src/tables/stock/StockItemTestResultTable.tsx:353
msgid "Pass Test"
msgstr "Тест пройден"

#: src/tables/stock/StockItemTestResultTable.tsx:402
msgid "Show results for required tests"
msgstr "Показать результаты обязательных тестов"

#: src/tables/stock/StockItemTestResultTable.tsx:406
msgid "Include Installed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:407
msgid "Show results for installed stock items"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:411
msgid "Passed"
msgstr "Пройден"

#: src/tables/stock/StockItemTestResultTable.tsx:412
msgid "Show only passed tests"
msgstr "Показать только пройденные тесты"

#: src/tables/stock/StockItemTestResultTable.tsx:417
msgid "Show results for enabled tests"
msgstr "Показать результаты активных тестов"

#: src/tables/stock/StockLocationTable.tsx:38
#~ msgid "structural"
#~ msgstr "structural"

#: src/tables/stock/StockLocationTable.tsx:39
msgid "Include sublocations in results"
msgstr "Включить вложенные склады в результат"

#: src/tables/stock/StockLocationTable.tsx:43
#~ msgid "external"
#~ msgstr "external"

#: src/tables/stock/StockLocationTable.tsx:44
msgid "Show structural locations"
msgstr "Показать структурные места хранения"

#: src/tables/stock/StockLocationTable.tsx:49
msgid "Show external locations"
msgstr "Показать сторонние места хранения"

#: src/tables/stock/StockLocationTable.tsx:53
msgid "Has location type"
msgstr "Задан тип места хранения"

#: src/tables/stock/StockLocationTable.tsx:58
msgid "Filter by location type"
msgstr "Фильтр по типу места хранения"

#: src/tables/stock/StockLocationTable.tsx:105
#: src/tables/stock/StockLocationTable.tsx:160
msgid "Add Stock Location"
msgstr "Создать место хранения"

#: src/tables/stock/StockLocationTable.tsx:129
msgid "Set Parent Location"
msgstr "Задать вышестоящий склад"

#: src/tables/stock/StockLocationTable.tsx:149
msgid "Set parent location for the selected items"
msgstr "Задать вышестоящий склад для выбранных элементов"

#: src/tables/stock/StockTrackingTable.tsx:77
msgid "Added"
msgstr "Добавлено"

#: src/tables/stock/StockTrackingTable.tsx:82
msgid "Removed"
msgstr "Удалено"

#: src/tables/stock/StockTrackingTable.tsx:206
msgid "Details"
msgstr "Сведения"

#: src/tables/stock/StockTrackingTable.tsx:221
msgid "No user information"
msgstr "Нет информации о пользователе"

#: src/tables/stock/TestStatisticsTable.tsx:34
#: src/tables/stock/TestStatisticsTable.tsx:64
#~ msgid "Total"
#~ msgstr "Total"

#: src/views/MobileAppView.tsx:25
msgid "Mobile viewport detected"
msgstr "Обнаружено мобильное устройство"

#: src/views/MobileAppView.tsx:25
#~ msgid "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
#~ msgstr "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."

#: src/views/MobileAppView.tsx:28
msgid "InvenTree UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
msgstr "Интерфейс InvenTree оптимизирован для планшетов и компьютеров, для телефонов можно использовать официальное приложение."

#: src/views/MobileAppView.tsx:34
msgid "Read the docs"
msgstr "Читать документацию"

#: src/views/MobileAppView.tsx:38
msgid "Ignore and continue to Desktop view"
msgstr "Игнорировать и продолжить в интерфейсе для рабочего стола"

