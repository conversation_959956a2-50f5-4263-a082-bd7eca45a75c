# Generated by Django 3.2.4 on 2021-06-20 07:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0037_supplierpart_update_3'),
    ]

    operations = [
        migrations.CreateModel(
            name='ManufacturerPartParameter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Parameter name', max_length=500, verbose_name='Name')),
                ('value', models.CharField(help_text='Parameter value', max_length=500, verbose_name='Value')),
                ('units', models.CharField(blank=True, help_text='Parameter units', max_length=64, null=True, verbose_name='Units')),
                ('manufacturer_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parameters', to='company.manufacturerpart', verbose_name='Manufacturer Part')),
            ],
            options={
                'verbose_name': 'Manufacturer Part Parameter',
                'unique_together': {('manufacturer_part', 'name')},
            },
        ),
    ]
