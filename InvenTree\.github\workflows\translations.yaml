name: Update Translation Files

on:
  push:
    branches:
      - master

env:
  python_version: 3.9
  node_version: 20

permissions:
  contents: read

jobs:
  synchronize-with-crowdin:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      INVENTREE_DB_NAME: "./test_db.sqlite"
      INVENTREE_DB_ENGINE: django.db.backends.sqlite3
      INVENTREE_DEBUG: true
      INVENTREE_LOG_LEVEL: INFO
      INVENTREE_MEDIA_ROOT: ./media
      INVENTREE_STATIC_ROOT: ./static
      INVENTREE_BACKUP_DIR: ./backup
      INVENTREE_SITE_URL: http://localhost:8000

    steps:
      - name: Checkout Code
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # pin@v5.0.0
        with:
          persist-credentials: true
      - name: Environment Setup
        uses: ./.github/actions/setup
        with:
          install: true
          apt-dependency: gettext
      - name: Make Translations
        run: invoke dev.translate
      - name: Remove compiled static files
        run: rm -rf src/backend/InvenTree/static
      - name: Remove all local changes that are not *.po files
        run: |
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add src/backend/InvenTree/locale/en/LC_MESSAGES/django.po src/frontend/src/locales/en/messages.po
          echo "Adding commit (or ignoring if no changes)"
          git commit -m "add translations" || true
          echo "Removing all other changes"
          git reset --hard
          echo "Resetting to HEAD~"
          git reset HEAD~ || true
      - name: crowdin action
        uses: crowdin/github-action@590c05e09a29f392b203faf4d6aa8e0cd32c7835 # pin@v2
        with:
          upload_sources: true
          upload_translations: false
          download_translations: true
          localization_branch_name: l10_crowdin
          create_pull_request: true
          pull_request_title: 'New Crowdin updates'
          pull_request_body: 'New Crowdin translations by [Crowdin GH Action](https://github.com/crowdin/github-action)'
          pull_request_base_branch_name: 'master'
          pull_request_labels: 'translations'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
