# Generated by Django 3.0.7 on 2021-01-04 12:31

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0031_auto_20210103_2215'),
        ('part', '0060_merge_20201112_1722'),
    ]

    operations = [
        migrations.AddField(
            model_name='part',
            name='default_expiry',
            field=models.PositiveIntegerField(default=0, help_text='Expiry time (in days) for stock items of this part', validators=[django.core.validators.MinValueValidator(0)], verbose_name='Default Expiry'),
        ),
        migrations.AlterField(
            model_name='part',
            name='default_supplier',
            field=models.ForeignKey(blank=True, help_text='Default supplier part', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_parts', to='company.SupplierPart', verbose_name='Default Supplier'),
        ),
        migrations.AlterField(
            model_name='part',
            name='minimum_stock',
            field=models.PositiveIntegerField(default=0, help_text='Minimum allowed stock level', validators=[django.core.validators.MinValueValidator(0)], verbose_name='Minimum Stock'),
        ),
        migrations.AlterField(
            model_name='part',
            name='units',
            field=models.CharField(blank=True, default='', help_text='Stock keeping units for this part', max_length=20, null=True, verbose_name='Units'),
        ),
    ]
