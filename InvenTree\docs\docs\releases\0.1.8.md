---
title: Release 0.1.8
---

## Release 0.1.8

[Release 0.1.8](https://github.com/inventree/InvenTree/releases/tag/0.1.8) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Order Responsibility

[#1395](https://github.com/inventree/InvenTree/pull/1395) adds the concept of *responsibility* for Purchase Orders and Sales Orders. Orders can be assigned to either an individual user, or an entire group.

### Order Reports

[#1397](https://github.com/inventree/InvenTree/pull/1397) adds the ability to generate PDF Reports against Purchase Orders and Sales Orders. This new feature provides a framework for generating reports such as invoices, sales orders, packing lists, etc. While it provides the framework for such reports, generic templates for these report types have not yet been created.

### Global Setting for Part IPN Edit
[#1400](https://github.com/inventree/InvenTree/pull/1400) adds the ability to disable IPN field when editing part information for **all** parts and **all** users. This global setting is located in the "Part" section of the InvenTree settings. Toggling it off means the IPN field cannot be edited manually anymore after a part is created (the field is greyed out). Only admin users retain the ability to edit this field.

### Image Download

[#1410](https://github.com/inventree/InvenTree/pull/1410) introduces a new feature which allows thumbnail images (e.g. for *Part* and *Company* objects) to be downloaded from a remote URL (by the server). This feature is disabled by default, and must be enabled in the *Global Settings* menu.

### Assign by Serial Number

[#1426](https://github.com/inventree/InvenTree/pull/1426) introduces a new feature which allows stock items to be allocated to a sales order using serial number references. This provides a much more streamlined user experience. Refer to the [sales order documentation](../sales/sales_order.md) for further information.

## Major Bug Fixes

| PR | Description |
| --- | --- |
| [#1407](https://github.com/inventree/InvenTree/pull/1407) | Fixes unnecessary column requirement when importing BOM |
| [#1430](https://github.com/inventree/InvenTree/pull/1430) | Fixes error thrown when migrating from very old data set |
| [#1441](https://github.com/inventree/InvenTree/pull/1441) | Fixes template rendering error if git not available on system path |
| [#1446](https://github.com/inventree/InvenTree/pull/1446) | Fixes bug exporting BOM to YAML format |
| [#1449](https://github.com/inventree/InvenTree/pull/1449) | Fixes bug which prevented transfer of serialized stock items |
