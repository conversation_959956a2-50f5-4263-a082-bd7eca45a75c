# Generated by Django 4.2.11 on 2024-07-16 12:58

import InvenTree.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0070_remove_manufacturerpartattachment_manufacturer_part_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='manufacturerpart',
            name='notes',
            field=InvenTree.fields.InvenTreeNotesField(blank=True, help_text='Markdown notes (optional)', max_length=50000, null=True, verbose_name='Notes'),
        ),
        migrations.AddField(
            model_name='supplierpart',
            name='notes',
            field=InvenTree.fields.InvenTreeNotesField(blank=True, help_text='Markdown notes (optional)', max_length=50000, null=True, verbose_name='Notes'),
        ),
    ]
