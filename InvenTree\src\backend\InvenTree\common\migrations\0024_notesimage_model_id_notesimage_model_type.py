# Generated by Django 4.2.12 on 2024-05-22 12:27

import common.validators
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0023_auto_20240602_1332'),
    ]

    operations = [
        migrations.AddField(
            model_name='notesimage',
            name='model_id',
            field=models.IntegerField(blank=True, default=None, help_text='Target model ID for this image', null=True),
        ),
        migrations.AddField(
            model_name='notesimage',
            name='model_type',
            field=models.CharField(blank=True, null=True, help_text='Target model type for this image', max_length=100, validators=[common.validators.validate_notes_model_type]),
        ),
    ]
