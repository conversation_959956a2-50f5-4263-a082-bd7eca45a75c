
<div class='md-typeset__scrollwrap'>
    <div class='md-typeset__table'>
        <table>
            <thead>
                <tr>
                    <th>{{ icon("clipboard") }}</span> Release</th>
                    <th>{{ icon("calendar") }} Date</th>
                    <th>{{ icon("brand-github") }} GitHub</th>
                    <th>{{ icon("brand-docker") }} Docker</th>
                </tr>
            </thead>
            <tbody>

{% for release in config.releases %}
<tr>
    <td>
        {% if release.local_path %}
        <a href="../{{ release.tag_name }}">{{ release.tag_name }}</a>
        {% else %}
        {{ release.tag_name }}
        {% endif %}
    </td>
    <td>{{ release.date }}</td>
    <td>
        <a href="https://github.com/inventree/InvenTree/releases/tag/{{ release.tag_name }}">{{ release.tag_name }}</a>
    </td>
    <td>
        {% if release.docker %}
        <a href="https://hub.docker.com/r/inventree/inventree/tags">inventree/inventree:{{ release.tag_name }}</a>
        {% else %}
        <em>Not available for this release</em>
        {% endif %}
    </td>
</tr>
{% endfor %}
</tbody>
</table>
</div>
</div>
