# Generated by Django 3.2.23 on 2023-11-20 04:57

import InvenTree.fields
from django.db import migrations
import djmoney.models.fields
import djmoney.models.validators


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0118_auto_20231024_1844'),
    ]

    operations = [
        migrations.AddField(
            model_name='partpricing',
            name='override_max',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Override maximum cost', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Maximum Cost'),
        ),
        migrations.AddField(
            model_name='partpricing',
            name='override_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AddField(
            model_name='partpricing',
            name='override_min',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Override minimum cost', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Minimum Cost'),
        ),
        migrations.AddField(
            model_name='partpricing',
            name='override_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
    ]
