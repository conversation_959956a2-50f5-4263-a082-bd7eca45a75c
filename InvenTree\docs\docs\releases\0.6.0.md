---
title: Release 0.6.0
---

## Release 0.6.0

0.6.0 is a major feature release of the InvenTree software project. For a comprehensive list of changes associated with this release, refer to the [InvenTree GitHub page](https://github.com/inventree/InvenTree/milestone/8).

### Highlights

A few highlights for this release:
-	New and improved workflows for orders
-	SSO, MFA and session management support
-	Substitution for BOMs
-	A major UI overhaul
-	A new / improved plugin ecosystem

If you are as excited as we are set up your own instance, check out the [demo](https://inventree.readthedocs.io/en/latest/demo/) or browse the [documentation](https://inventree.readthedocs.io/en/latest/)!

### Release Survey

With this release we are also issuing the first InvenTree community survey.
We do not have any tracking or mandatory signup in InvenTree, so we depend on you filling this out to gather insights into the user base. Please take 3-5 minutes to tell us a bit more about your deployment [here](https://s.surveyplanet.com/y0dw92gg).

The results will be released in a few weeks in [GitHub Discussions](https://github.com/inventree/InvenTree/discussions/2561).

## New Features

### Build Order Allocation

PR [#2094](https://github.com/inventree/InvenTree/pull/2094) represents a significant improvement to the workflow for allocating stock items against a build order. A new API endpoint has been created allowing stock items to be allocated against builds, with dynamic validation and feedback of error messages.

Additionally the workflow and UX has been simplified, allowing greater efficiency of stock allocation against a build order.

### Receiving Purchase Orders

PR [#2102](https://github.com/inventree/InvenTree/pull/2102) provides a major refactor of the process by which stock items are received against a purchase order. The receiving process now makes use of the REST API for a more responsive and intuitive user experience.

### Stock Adjustments

PR [#2103](https://github.com/inventree/InvenTree/pull/2103) provides a major improvement to the stock adjustment API. While remaining backwards compatible, the API endpoints for stock adjustments (stocktake, quantity adjustment, stock transfer) have been updated to use the DRF serializer framework. This provides a more streamlined user experience with improved error checking and handling.

### Sales Order Allocations

PR [#2110](https://github.com/inventree/InvenTree/pull/2110) provides a major refactor of the process by which stock items are allocated against a sales order. The process now makes use of the REST API for a more responsive and intuitive user experience.

### Delete Old Error Messages

PR [#2113](https://github.com/inventree/InvenTree/pull/2113) adds a background task to periodically remove old error messages from the database. Error messages are retained in the database for 30 days, after which point they are removed.

### Sales Order Export

PR [#2119](https://github.com/inventree/InvenTree/pull/2119) adds the ability to export sales order information in a variety of supported file formats.

### SSO Support

PR [#2017](https://github.com/inventree/InvenTree/pull/2017) adds support for SSO (single sign on) authentication. SSO integration requires configuration by the system administrator. Refer to the [SSO documentation](../settings/SSO.md) for further information.

### BOM Substitution

PR [#2150](https://github.com/inventree/InvenTree/pull/2150) adds support for *Bill of Materials Substitution*. This allows substitute parts to be individually specified against BOM line items, allowing for stock of the substituted parts to be used for build order allocation if the original part is not available.

### Build Completion

PR [#2159](https://github.com/inventree/InvenTree/pull/2159) provides a major improvement to the process by which build outputs are completed. The build output completion process now uses the API and provides a much more intuitive and efficient user interface.

### Bootstrap Yourselves In

PR [#2205](https://github.com/inventree/InvenTree/pull/2205) represents a major visual overhaul of the user interface, updating to bootstrap 5.

### Low Stock Notifications

PR [#2208](https://github.com/inventree/InvenTree/pull/2208) provides notification emails when the stock level for a particular part falls below the configured "minimum stock" threshold for that part. An email is automatically sent to any users who are subscribed to notifications for that part.

### MFA Support

PR [#2221](https://github.com/inventree/InvenTree/pull/2221) adds support for MFA (multi factor authentication). This enables admins to require all users to enable MFA as a second auth step. Refer to the [documentation](../settings/MFA.md) for further information.
### Stock Item Forms

PR [#2198](https://github.com/inventree/InvenTree/pull/2198) provides a major refactor of stock item forms, for creating and editing stock items. These forms have been migrated to the REST API, providing a much more responsive user experience.

### Session Management

PR [#2371](https://github.com/inventree/InvenTree/pull/2371) adds management of user settings via the settings page

### Sales Order Shipments

PR [#2199](https://github.com/inventree/InvenTree/pull/2199) adds the ability to perform multiple shipments against a given Sales Order. This allows split-shipments and partial fulfillment of Sales Orders

### Plugin Ecosystem

PR [#2074](https://github.com/inventree/InvenTree/pull/2074) adds a plugin ecosystem, allowing future development of first-party and third-party plugins to extend upon core InvenTree functionality. Watch this space!

### Assign Stock Items to Customer

PR [#2436](https://github.com/inventree/InvenTree/pull/2436) refactors the process by which stock items can be manually assigned to a customer, separate to the Sales Order process.

### Merging Stock Items

PR [#2468](https://github.com/inventree/InvenTree/pull/2468) adds the ability to merge multiple stock items together.

### Scheduled Tasks for Plugins

PR [#2512](https://github.com/inventree/InvenTree/pull/2512) adds a "scheduled task" mixin for plugins, allowing custom plugins to run periodically scheduled tasks.

### Triggered Events for Plugins

PR [#2515](https://github.com/inventree/InvenTree/pull/2515) adds a "triggered events" mixin for plugins, allowing custom plugins to run code when a particular event occurs.

## Translations
We would like to thank all members of our [translation team](https://crowdin.com/project/inventree). For this release the most active contributors were:
- carlos-r [@carlos-riquelme](https://github.com/carlos-riquelme)
- davisteph45
- fto [@fichterto](https://github.com/fichterto)
- RebeccaW
