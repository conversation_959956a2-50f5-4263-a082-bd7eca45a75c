---
title: Terminology
---

## Terminology

There are different systems in the industry for the management of getting, storing and making parts. An overview what they are for and what the acronyms mean.

**InvenTree** is mainly focused on [**IMS**](#inventory-management-system-ims) and [**PLM**](#part-library-management-plm) functionality.

### Inventory Management System *(IMS)*
Evolves around manufacturing of parts out of other parts. It keeps track of stock, part origin, orders, shelf live and more.

### Part Library Management *(PLM)*
Keeps track of BOMs, part variants, possible substitutions, versions, IPNs and further part parameters.
PLM can also mean product lifecycle management – those systems manage all stages from design through manufacturing up to customer support and recycling.

A similar system is [Partkeepr](https://partkeepr.org/) (seems mostly inactive - there is a 3rd party importer).

### Material Resource Planning *(MRP)*

Material Requirements Planning software, is designed to assist businesses in effectively managing their manufacturing processes. It helps with the planning and control of inventory, production schedules, and procurement activities. MRP software utilizes various algorithms and data inputs, such as sales forecasts, production capacity, and bill of materials, to generate material requirements plans, schedule production orders, and optimize inventory levels.

### Asset Management *(AM)*
Manages many unique items, which need tracking per part and are assignable to users / groups / locations. These systems often include features like item states, refurbishing / maintenance / reservation, or request-flows.
Often these systems are used for IT-Hardware (then they are called *ITAM*).
A good open-source example would be [Snipe-IT](https://snipeitapp.com/).

### Enterprise Resource Planning *(ERP)*

Is the centre of your business. It manages timesheets, warehousing, finances (prices, taxes, …), customer relations and more. InvenTree covers parts of this but aims to keep an intuitive and simple user interface.
Popular, fully fledged ERPs are [ERPNext](https://erpnext.com/) or [odoo](https://www.odoo.com).

### Customer Relationship Manager *(CRM)*

Customer relationship management (CRM) is a technology for managing all your company's relationships and interactions with customers and potential customers.

### Manufacturing Execution System *(MES)*

A Manufacturing Execution System (MES), oversees, monitors, records, and manages the entire manufacturing process from raw materials to finalized products.
