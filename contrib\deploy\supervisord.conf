; # Supervisor Config File
; Example configuration file for running InvenTree using supervisor
; There are two separate processes which must be managed:
;
; ## Web Server
; The InvenTree server must be launched and managed as a process
; The recommended way to handle the web server is to use gunicorn
;
; ## Background Tasks
; A background task manager processes long-running and periodic tasks
; InvenTree uses django-q for this purpose

[supervisord]
; Change this path if log files are stored elsewhere
logfile=/home/<USER>/log/supervisor.log
user=inventree

[supervisorctl]

[inet_http_server]
port = 127.0.0.1:9001

; InvenTree Web Server Process
[program:inventree-server]
user=inventree
directory=/home/<USER>/src/src/backend/InvenTree
command=/home/<USER>/env/bin/gunicorn -c gunicorn.conf.py InvenTree.wsgi
startsecs=10
autostart=true
autorestart=true
startretries=3
; Change these paths if log files are stored elsewhere
stderr_logfile=/home/<USER>/log/server.err.log
stdout_logfile=/home/<USER>/log/server.out.log

; InvenTree Background Worker Process
[program:inventree-cluster]
user=inventree
directory=/home/<USER>/src/src/backend/InvenTree
command=/home/<USER>/env/bin/python manage.py qcluster
startsecs=10
autostart=true
autorestart=true
; Change these paths if log files are stored elsewhere
stderr_logfile=/home/<USER>/log/cluster.err.log
stdout_logfile=/home/<USER>/log/cluster.out.log
