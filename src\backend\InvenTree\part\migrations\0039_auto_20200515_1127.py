# Generated by Django 3.0.5 on 2020-05-15 11:27

from django.db import migrations, models


class Migration(migrations.Migration):

    atomic = False

    dependencies = [
        ('part', '0038_auto_20200513_0016'),
    ]

    operations = [
        migrations.AddField(
            model_name='part',
            name='level',
            field=models.PositiveIntegerField(default=0, editable=False),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='part',
            name='lft',
            field=models.PositiveIntegerField(default=0, editable=False),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='part',
            name='rght',
            field=models.PositiveIntegerField(default=0, editable=False),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='part',
            name='tree_id',
            field=models.PositiveIntegerField(db_index=True, default=0, editable=False),
            preserve_default=False,
        ),
    ]
