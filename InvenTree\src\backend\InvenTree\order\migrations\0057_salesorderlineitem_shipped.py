# Generated by Django 3.2.5 on 2021-11-26 12:06

import InvenTree.fields
import django.core.validators
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0056_alter_salesorderallocation_shipment'),
    ]

    operations = [
        migrations.AddField(
            model_name='salesorderlineitem',
            name='shipped',
            field=InvenTree.fields.RoundingDecimalField(decimal_places=5, default=0, help_text='Shipped quantity', max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Shipped'),
        ),
    ]
