---
title: Release 0.5.0
---

## Release 0.5.0

### BOM Purchase Price

[#1957](https://github.com/inventree/InvenTree/pull/1957) adds integration of stock purchase price history into BOM pricing for assemblies.

### Disable Navbar Items

[#1999](https://github.com/inventree/InvenTree/pull/1999) adds the ability to optionally hide main InvenTree features

### Edit Purchase Price via API

[#2003](https://github.com/inventree/InvenTree/pull/2003) allows the `purchase_price` and `purchase_price_currency` fields (on the StockItem model) to be edited via the API

### Receive Purchase Order Items via API

[#2013](https://github.com/inventree/InvenTree/pull/2013) adds an API endpoint for receiving purchase order line items.

### Build Completion Scheduling

[#2034](https://github.com/inventree/InvenTree/pull/2034) moves some of the build completion logic to the background worker thread. This allows UI interactions to run smoothly, and long-running database actions (build order cleanup) to be performed offline.

### Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#1997](https://github.com/inventree/InvenTree/pull/1997) | Fixes multiple bugs in "order parts" wizard |
| [#2011](https://github.com/inventree/InvenTree/pull/2011) | Fixes issue with "edit part" form |
| [#2018](https://github.com/inventree/InvenTree/pull/2018) | Fixes bug which caused server exception when a *large* image was uploaded |
| [#2020](https://github.com/inventree/InvenTree/pull/2020) | Fixes table ordering bug for purchase order line item table |
| [#2021](https://github.com/inventree/InvenTree/pull/2021) | Fixes naming collision between two tables |
| [#2043](https://github.com/inventree/InvenTree/pull/2041) | Fixes bug which caused extremely long delays when creating a new BOM item |
| [#2082](https://github.com/inventree/InvenTree/pull/2082) | Significantly reduces time taken to delete a single StockItem object |
