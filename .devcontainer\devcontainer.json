// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.241.1/containers/python-3
{
  "name": "InvenTree devcontainer",
  "dockerComposeFile": "docker-compose.yml",
  "service": "inventree",
  "overrideCommand": true,
  "workspaceFolder": "/home/<USER>/",

  // Configure tool-specific properties.
  "customizations": {
    // Configure properties specific to VS Code.
    "vscode": {
      // Set *default* container specific settings.json values on container create.
      "settings": {
        "python.defaultInterpreterPath": "${containerWorkspaceFolder}/dev/venv/bin/python",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": false,
        "python.formatting.autopep8Path": "/usr/local/py-utils/bin/autopep8",
        "python.formatting.blackPath": "/usr/local/py-utils/bin/black",
        "python.formatting.yapfPath": "/usr/local/py-utils/bin/yapf",
        "python.linting.banditPath": "/usr/local/py-utils/bin/bandit",
        "python.linting.mypyPath": "/usr/local/py-utils/bin/mypy",
        "python.linting.pycodestylePath": "/usr/local/py-utils/bin/pycodestyle",
        "python.linting.pydocstylePath": "/usr/local/py-utils/bin/pydocstyle",
        "python.linting.pylintPath": "/usr/local/py-utils/bin/pylint"
      },

      // Add the IDs of extensions you want installed when the container is created.
      "extensions": [
        "ms-python.python",
        "ms-python.vscode-pylance",
        "batisteo.vscode-django",
        "eamodio.gitlens",
        "biomejs.biome"
      ]
    }
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [5173, 5432, 6379, 8000, 8080],
  "portsAttributes": {
    "5173": {
      "label": "Vite Server"
    },
    "5432": {
      "label": "PostgreSQL Database"
    },
    "6379": {
      "label": "Redis Server"
    },
    "8000": {
      "label": "InvenTree Server"
    },
    "8080": {
      "label": "mkdocs server"
    }
  },

  // Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": ".devcontainer/postCreateCommand.sh",

  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
  "remoteUser": "vscode",
  "containerUser": "vscode",

  "remoteEnv": {

    // Python config
    "PIP_USER": "no",

    // used to load the venv into the PATH and activate it
    // Ref: https://stackoverflow.com/a/56286534
    "VIRTUAL_ENV": "${containerWorkspaceFolder}/dev/venv",
    "PATH": "${containerWorkspaceFolder}/dev/venv/bin:${containerEnv:PATH}"
  }
}
