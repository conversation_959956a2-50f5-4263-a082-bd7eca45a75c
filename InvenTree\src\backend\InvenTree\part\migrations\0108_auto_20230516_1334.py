# Generated by Django 3.2.19 on 2023-05-16 13:34

import InvenTree.validators
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0107_alter_part_tags'),
    ]

    operations = [
        migrations.AddField(
            model_name='partparameter',
            name='data_numeric',
            field=models.FloatField(default=None, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='partparameter',
            name='data',
            field=models.CharField(help_text='Parameter Value', max_length=500, validators=[django.core.validators.MinLengthValidator(1)], verbose_name='Data'),
        ),
        migrations.AlterField(
            model_name='partparametertemplate',
            name='units',
            field=models.CharField(blank=True, help_text='Physical units for this parameter', max_length=25, validators=[InvenTree.validators.validate_physical_units], verbose_name='Units'),
        ),
    ]
