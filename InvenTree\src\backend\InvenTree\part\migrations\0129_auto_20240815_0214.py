# Generated by Django 4.2.15 on 2024-08-15 02:14

from django.db import migrations


def set_testable(apps, schema_editor):
    """Set the 'testable' status to True for certain parts.

    Prior to migration part.0128, the 'trackable' attribute
    was used to determine if parts could have tests associated with them.

    However, 'trackable' comes with other restrictions
    (such as requiring a unique serial number).

    So, we have added a new field 'testable' to the Part model,
    which is updated in this migration to match the value of the 'trackable' field.
    """

    Part = apps.get_model('part', 'Part')

    # By default, 'testable' is False - so we only need to update parts marked as 'trackable'
    trackable_parts = Part.objects.filter(trackable=True)

    if trackable_parts.count() > 0:
        print(f"\nMarking {trackable_parts.count()} Part objects as 'testable'")
        trackable_parts.update(testable=True)


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0128_part_testable'),
    ]

    operations = [
        migrations.RunPython(set_testable, reverse_code=migrations.RunPython.noop)
    ]
