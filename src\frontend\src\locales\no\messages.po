msgid ""
msgstr ""
"POT-Creation-Date: 2023-06-09 22:10+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: no\n"
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-20 11:54\n"
"Last-Translator: \n"
"Language-Team: Norwegian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: no\n"
"X-Crowdin-File: /src/frontend/src/locales/en/messages.po\n"
"X-Crowdin-File-ID: 252\n"

#: lib/components/RowActions.tsx:36
#: src/components/items/ActionDropdown.tsx:287
#: src/pages/Index/Scan.tsx:64
msgid "Duplicate"
msgstr "Dupliser"

#: lib/components/RowActions.tsx:46
#: src/components/items/ActionDropdown.tsx:243
msgid "Edit"
msgstr "Rediger"

#: lib/components/RowActions.tsx:56
#: src/components/forms/ApiForm.tsx:719
#: src/components/items/ActionDropdown.tsx:255
#: src/components/items/RoleTable.tsx:155
#: src/hooks/UseForm.tsx:160
#: src/pages/Notifications.tsx:109
#: src/tables/plugin/PluginListTable.tsx:243
msgid "Delete"
msgstr "Slett"

#: lib/components/RowActions.tsx:66
#: src/components/details/DetailsImage.tsx:83
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:187
#: src/components/items/ActionDropdown.tsx:275
#: src/components/items/ActionDropdown.tsx:276
#: src/contexts/ThemeContext.tsx:45
#: src/hooks/UseForm.tsx:33
#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:106
#: src/tables/FilterSelectDrawer.tsx:336
#: src/tables/build/BuildOutputTable.tsx:570
msgid "Cancel"
msgstr "Avbryt"

#: lib/components/RowActions.tsx:136
#: src/components/nav/NavigationDrawer.tsx:198
#: src/forms/PurchaseOrderForms.tsx:795
#: src/forms/StockForms.tsx:737
#: src/forms/StockForms.tsx:783
#: src/forms/StockForms.tsx:829
#: src/forms/StockForms.tsx:868
#: src/forms/StockForms.tsx:904
#: src/forms/StockForms.tsx:983
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:456
msgid "Actions"
msgstr "Handlinger"

#: lib/components/SearchInput.tsx:34
#: src/components/forms/fields/RelatedModelField.tsx:387
#: src/components/nav/Header.tsx:168
#: src/pages/Index/Settings/UserSettings.tsx:74
#: src/pages/part/PartDetail.tsx:1161
msgid "Search"
msgstr "Søk"

#: lib/components/YesNoButton.tsx:20
msgid "Pass"
msgstr ""

#: lib/components/YesNoButton.tsx:21
msgid "Fail"
msgstr ""

#: lib/components/YesNoButton.tsx:43
#: src/tables/Filter.tsx:35
msgid "Yes"
msgstr "Ja"

#: lib/components/YesNoButton.tsx:44
#: src/tables/Filter.tsx:36
msgid "No"
msgstr "Nei"

#: lib/enums/ModelInformation.tsx:28
#: src/components/wizards/OrderPartsWizard.tsx:132
#: src/forms/BuildForms.tsx:310
#: src/forms/BuildForms.tsx:384
#: src/forms/BuildForms.tsx:448
#: src/forms/BuildForms.tsx:602
#: src/forms/BuildForms.tsx:757
#: src/forms/BuildForms.tsx:860
#: src/forms/PurchaseOrderForms.tsx:791
#: src/forms/ReturnOrderForms.tsx:239
#: src/forms/SalesOrderForms.tsx:267
#: src/forms/StockForms.tsx:303
#: src/forms/StockForms.tsx:732
#: src/forms/StockForms.tsx:778
#: src/forms/StockForms.tsx:824
#: src/forms/StockForms.tsx:863
#: src/forms/StockForms.tsx:899
#: src/forms/StockForms.tsx:937
#: src/forms/StockForms.tsx:979
#: src/forms/StockForms.tsx:1027
#: src/forms/StockForms.tsx:1071
#: src/pages/build/BuildDetail.tsx:183
#: src/pages/part/PartDetail.tsx:1213
#: src/tables/ColumnRenderers.tsx:82
#: src/tables/part/RelatedPartTable.tsx:53
#: src/tables/stock/StockTrackingTable.tsx:87
msgid "Part"
msgstr "Del"

#: lib/enums/ModelInformation.tsx:29
#: lib/enums/Roles.tsx:35
#: src/components/nav/NavigationDrawer.tsx:77
#: src/defaults/links.tsx:36
#: src/pages/Index/Settings/SystemSettings.tsx:190
#: src/pages/part/CategoryDetail.tsx:130
#: src/pages/part/CategoryDetail.tsx:273
#: src/pages/part/CategoryDetail.tsx:312
#: src/pages/part/PartDetail.tsx:951
msgid "Parts"
msgstr "Deler"

#: lib/enums/ModelInformation.tsx:37
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:13
msgid "Part Parameter Template"
msgstr "Mal for Delparameter"

#: lib/enums/ModelInformation.tsx:38
msgid "Part Parameter Templates"
msgstr "Maler for Delparameter"

#: lib/enums/ModelInformation.tsx:45
msgid "Part Test Template"
msgstr ""

#: lib/enums/ModelInformation.tsx:46
msgid "Part Test Templates"
msgstr ""

#: lib/enums/ModelInformation.tsx:52
#: src/components/wizards/OrderPartsWizard.tsx:143
#: src/pages/company/SupplierPartDetail.tsx:409
#: src/pages/stock/StockDetail.tsx:286
#: src/tables/build/BuildAllocatedStockTable.tsx:155
#: src/tables/part/PartPurchaseOrdersTable.tsx:50
#: src/tables/purchasing/SupplierPartTable.tsx:64
#: src/tables/stock/StockItemTable.tsx:248
msgid "Supplier Part"
msgstr "Leverandørdel"

#: lib/enums/ModelInformation.tsx:53
#: src/pages/purchasing/PurchasingIndex.tsx:92
msgid "Supplier Parts"
msgstr "Leverandørdeler"

#: lib/enums/ModelInformation.tsx:61
#: src/tables/part/PartPurchaseOrdersTable.tsx:56
#: src/tables/stock/StockItemTable.tsx:254
msgid "Manufacturer Part"
msgstr "Produsentdel"

#: lib/enums/ModelInformation.tsx:62
#: src/pages/purchasing/PurchasingIndex.tsx:109
msgid "Manufacturer Parts"
msgstr "Produsentdeler"

#: lib/enums/ModelInformation.tsx:70
#: src/pages/part/CategoryDetail.tsx:343
#: src/tables/Filter.tsx:381
msgid "Part Category"
msgstr "Delkategori"

#: lib/enums/ModelInformation.tsx:71
#: lib/enums/Roles.tsx:37
#: src/pages/part/CategoryDetail.tsx:334
#: src/pages/part/PartDetail.tsx:1202
msgid "Part Categories"
msgstr "Delkategorier"

#: lib/enums/ModelInformation.tsx:79
#: src/forms/BuildForms.tsx:385
#: src/forms/BuildForms.tsx:449
#: src/forms/BuildForms.tsx:604
#: src/forms/BuildForms.tsx:758
#: src/forms/SalesOrderForms.tsx:269
#: src/pages/stock/StockDetail.tsx:976
#: src/tables/stock/StockTrackingTable.tsx:48
#: src/tables/stock/StockTrackingTable.tsx:55
msgid "Stock Item"
msgstr "Lagervare"

#: lib/enums/ModelInformation.tsx:80
#: lib/enums/Roles.tsx:45
#: src/pages/company/CompanyDetail.tsx:212
#: src/pages/part/CategoryDetail.tsx:287
#: src/pages/part/PartStockHistoryDetail.tsx:101
#: src/pages/stock/LocationDetail.tsx:123
#: src/pages/stock/LocationDetail.tsx:182
msgid "Stock Items"
msgstr "Lagervarer"

#: lib/enums/ModelInformation.tsx:88
#: lib/enums/Roles.tsx:47
#: src/pages/stock/LocationDetail.tsx:420
msgid "Stock Location"
msgstr "Lagerplassering"

#: lib/enums/ModelInformation.tsx:89
#: src/pages/stock/LocationDetail.tsx:176
#: src/pages/stock/LocationDetail.tsx:412
#: src/pages/stock/StockDetail.tsx:967
msgid "Stock Locations"
msgstr "Lagerplasseringer"

#: lib/enums/ModelInformation.tsx:97
msgid "Stock Location Type"
msgstr ""

#: lib/enums/ModelInformation.tsx:98
msgid "Stock Location Types"
msgstr ""

#: lib/enums/ModelInformation.tsx:103
#: src/pages/Index/Settings/SystemSettings.tsx:248
#: src/pages/part/PartDetail.tsx:910
msgid "Stock History"
msgstr "Lagerhistorikk"

#: lib/enums/ModelInformation.tsx:104
msgid "Stock Histories"
msgstr "Lagerhistorikk"

#: lib/enums/ModelInformation.tsx:109
msgid "Build"
msgstr "Produksjon"

#: lib/enums/ModelInformation.tsx:110
msgid "Builds"
msgstr "Produksjoner"

#: lib/enums/ModelInformation.tsx:118
msgid "Build Line"
msgstr ""

#: lib/enums/ModelInformation.tsx:119
msgid "Build Lines"
msgstr ""

#: lib/enums/ModelInformation.tsx:126
msgid "Build Item"
msgstr ""

#: lib/enums/ModelInformation.tsx:127
msgid "Build Items"
msgstr ""

#: lib/enums/ModelInformation.tsx:132
#: src/pages/company/CompanyDetail.tsx:342
#: src/tables/company/CompanyTable.tsx:47
#: src/tables/company/ContactTable.tsx:67
msgid "Company"
msgstr "Firma"

#: lib/enums/ModelInformation.tsx:133
msgid "Companies"
msgstr "Firma"

#: lib/enums/ModelInformation.tsx:140
#: src/pages/build/BuildDetail.tsx:310
#: src/pages/purchasing/PurchaseOrderDetail.tsx:235
#: src/pages/sales/ReturnOrderDetail.tsx:199
#: src/pages/sales/SalesOrderDetail.tsx:211
#: src/tables/ColumnRenderers.tsx:353
#: src/tables/Filter.tsx:278
#: src/tables/TableHoverCard.tsx:101
msgid "Project Code"
msgstr "Prosjektkode"

#: lib/enums/ModelInformation.tsx:141
#: src/pages/Index/Settings/AdminCenter/Index.tsx:162
msgid "Project Codes"
msgstr "Prosjektkoder"

#: lib/enums/ModelInformation.tsx:147
#: src/components/wizards/OrderPartsWizard.tsx:183
#: src/pages/build/BuildDetail.tsx:227
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:33
#: src/pages/purchasing/PurchaseOrderDetail.tsx:531
#: src/pages/stock/StockDetail.tsx:349
#: src/tables/part/PartPurchaseOrdersTable.tsx:32
#: src/tables/stock/StockItemTable.tsx:240
#: src/tables/stock/StockTrackingTable.tsx:120
msgid "Purchase Order"
msgstr "Innkjøpsordre"

#: lib/enums/ModelInformation.tsx:148
#: lib/enums/Roles.tsx:39
#: src/pages/Index/Settings/SystemSettings.tsx:283
#: src/pages/company/CompanyDetail.tsx:205
#: src/pages/company/SupplierPartDetail.tsx:266
#: src/pages/part/PartDetail.tsx:874
#: src/pages/purchasing/PurchasingIndex.tsx:60
msgid "Purchase Orders"
msgstr "Innkjøpsordrer"

#: lib/enums/ModelInformation.tsx:156
msgid "Purchase Order Line"
msgstr "Ordrelinje for innkjøpsordre"

#: lib/enums/ModelInformation.tsx:157
msgid "Purchase Order Lines"
msgstr "Ordrelinjer for innkjøpsordre"

#: lib/enums/ModelInformation.tsx:162
#: src/pages/build/BuildDetail.tsx:283
#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#: src/pages/sales/SalesOrderDetail.tsx:586
#: src/pages/sales/SalesOrderShipmentDetail.tsx:94
#: src/pages/sales/SalesOrderShipmentDetail.tsx:358
#: src/pages/stock/StockDetail.tsx:358
#: src/tables/part/PartSalesAllocationsTable.tsx:41
#: src/tables/sales/SalesOrderAllocationTable.tsx:107
#: src/tables/stock/StockTrackingTable.tsx:131
msgid "Sales Order"
msgstr "Salgsordre"

#: lib/enums/ModelInformation.tsx:163
#: lib/enums/Roles.tsx:43
#: src/pages/Index/Settings/SystemSettings.tsx:299
#: src/pages/company/CompanyDetail.tsx:225
#: src/pages/part/PartDetail.tsx:886
#: src/pages/sales/SalesIndex.tsx:82
msgid "Sales Orders"
msgstr "Salgsordrer"

#: lib/enums/ModelInformation.tsx:171
#: src/pages/sales/SalesOrderShipmentDetail.tsx:357
msgid "Sales Order Shipment"
msgstr "Salgsordreforsendelse"

#: lib/enums/ModelInformation.tsx:172
msgid "Sales Order Shipments"
msgstr "Salgsordreforsendelser"

#: lib/enums/ModelInformation.tsx:178
#: src/pages/sales/ReturnOrderDetail.tsx:516
#: src/tables/stock/StockTrackingTable.tsx:142
msgid "Return Order"
msgstr "Returordre"

#: lib/enums/ModelInformation.tsx:179
#: lib/enums/Roles.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:315
#: src/pages/company/CompanyDetail.tsx:232
#: src/pages/part/PartDetail.tsx:893
#: src/pages/sales/SalesIndex.tsx:103
msgid "Return Orders"
msgstr "Returordrer"

#: lib/enums/ModelInformation.tsx:187
msgid "Return Order Line Item"
msgstr ""

#: lib/enums/ModelInformation.tsx:188
msgid "Return Order Line Items"
msgstr ""

#: lib/enums/ModelInformation.tsx:193
#: src/tables/company/AddressTable.tsx:52
msgid "Address"
msgstr "Adresse"

#: lib/enums/ModelInformation.tsx:194
#: src/pages/company/CompanyDetail.tsx:266
msgid "Addresses"
msgstr "Adresser"

#: lib/enums/ModelInformation.tsx:200
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:89
#: src/pages/core/UserDetail.tsx:135
#: src/pages/purchasing/PurchaseOrderDetail.tsx:211
#: src/pages/sales/ReturnOrderDetail.tsx:175
#: src/pages/sales/SalesOrderDetail.tsx:187
msgid "Contact"
msgstr "Kontakt"

#: lib/enums/ModelInformation.tsx:201
#: src/pages/company/CompanyDetail.tsx:260
#: src/pages/core/CoreIndex.tsx:33
msgid "Contacts"
msgstr "Kontakter"

#: lib/enums/ModelInformation.tsx:207
msgid "Owner"
msgstr "Eier"

#: lib/enums/ModelInformation.tsx:208
msgid "Owners"
msgstr "Eiere"

#: lib/enums/ModelInformation.tsx:214
#: src/pages/Auth/ChangePassword.tsx:36
#: src/pages/core/UserDetail.tsx:220
#: src/tables/Filter.tsx:327
#: src/tables/settings/ApiTokenTable.tsx:105
#: src/tables/settings/ApiTokenTable.tsx:132
#: src/tables/settings/BarcodeScanHistoryTable.tsx:79
#: src/tables/settings/ExportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:77
#: src/tables/stock/StockItemTestResultTable.tsx:216
#: src/tables/stock/StockTrackingTable.tsx:190
#: src/tables/stock/StockTrackingTable.tsx:218
msgid "User"
msgstr "Bruker"

#: lib/enums/ModelInformation.tsx:215
#: src/components/nav/NavigationDrawer.tsx:112
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:15
#: src/pages/core/CoreIndex.tsx:21
#: src/pages/core/UserDetail.tsx:226
msgid "Users"
msgstr "Brukere"

#: lib/enums/ModelInformation.tsx:221
#: src/pages/core/GroupDetail.tsx:78
msgid "Group"
msgstr ""

#: lib/enums/ModelInformation.tsx:222
#: src/components/nav/NavigationDrawer.tsx:118
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:23
#: src/pages/core/CoreIndex.tsx:27
#: src/pages/core/GroupDetail.tsx:82
#: src/pages/core/UserDetail.tsx:99
#: src/tables/settings/UserTable.tsx:276
msgid "Groups"
msgstr "Grupper"

#: lib/enums/ModelInformation.tsx:229
msgid "Import Session"
msgstr ""

#: lib/enums/ModelInformation.tsx:230
msgid "Import Sessions"
msgstr ""

#: lib/enums/ModelInformation.tsx:237
msgid "Label Template"
msgstr ""

#: lib/enums/ModelInformation.tsx:238
#: src/pages/Index/Settings/AdminCenter/Index.tsx:199
msgid "Label Templates"
msgstr ""

#: lib/enums/ModelInformation.tsx:245
msgid "Report Template"
msgstr ""

#: lib/enums/ModelInformation.tsx:246
#: src/pages/Index/Settings/AdminCenter/Index.tsx:205
msgid "Report Templates"
msgstr ""

#: lib/enums/ModelInformation.tsx:253
#: src/components/plugins/PluginDrawer.tsx:145
msgid "Plugin Configuration"
msgstr ""

#: lib/enums/ModelInformation.tsx:254
msgid "Plugin Configurations"
msgstr ""

#: lib/enums/ModelInformation.tsx:261
msgid "Content Type"
msgstr ""

#: lib/enums/ModelInformation.tsx:262
msgid "Content Types"
msgstr ""

#: lib/enums/ModelInformation.tsx:267
msgid "Selection List"
msgstr ""

#: lib/enums/ModelInformation.tsx:268
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:21
msgid "Selection Lists"
msgstr ""

#: lib/enums/ModelInformation.tsx:274
#: src/components/barcodes/BarcodeInput.tsx:114
#: src/components/dashboard/DashboardLayout.tsx:224
#: src/components/editors/NotesEditor.tsx:74
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:157
#: src/components/forms/fields/ApiFormField.tsx:263
#: src/components/forms/fields/TableField.tsx:45
#: src/components/importer/ImportDataSelector.tsx:192
#: src/components/importer/ImporterColumnSelector.tsx:217
#: src/components/importer/ImporterDrawer.tsx:88
#: src/components/modals/LicenseModal.tsx:85
#: src/components/nav/NavigationTree.tsx:210
#: src/components/nav/NotificationDrawer.tsx:235
#: src/components/nav/SearchDrawer.tsx:572
#: src/components/settings/SettingList.tsx:145
#: src/forms/BomForms.tsx:69
#: src/functions/auth.tsx:612
#: src/pages/ErrorPage.tsx:11
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:124
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:643
#: src/pages/part/PartPricingPanel.tsx:71
#: src/states/IconState.tsx:46
#: src/states/IconState.tsx:76
#: src/tables/InvenTreeTableHeader.tsx:125
#: src/tables/bom/BomTable.tsx:527
#: src/tables/stock/StockItemTestResultTable.tsx:335
msgid "Error"
msgstr "Feil"

#: lib/enums/ModelInformation.tsx:275
#: src/tables/machine/MachineListTable.tsx:354
#: src/tables/machine/MachineTypeTable.tsx:281
msgid "Errors"
msgstr ""

#: lib/enums/Roles.tsx:31
msgid "Admin"
msgstr ""

#: lib/enums/Roles.tsx:33
#: src/pages/Index/Settings/SystemSettings.tsx:264
#: src/pages/build/BuildIndex.tsx:75
#: src/pages/part/PartDetail.tsx:903
#: src/pages/sales/SalesOrderDetail.tsx:394
msgid "Build Orders"
msgstr "Produksjonsordrer"

#: lib/enums/Roles.tsx:50
#: src/pages/Index/Settings/AdminCenter/Index.tsx:202
#~ msgid "Stocktake"
#~ msgstr "Stocktake"

#: src/components/Boundary.tsx:12
msgid "Error rendering component"
msgstr ""

#: src/components/Boundary.tsx:14
msgid "An error occurred while rendering this component. Refer to the console for more information."
msgstr ""

#: src/components/DashboardItemProxy.tsx:34
#~ msgid "Title"
#~ msgstr "Title"

#: src/components/barcodes/BarcodeCameraInput.tsx:103
msgid "Error while scanning"
msgstr "Feil under skanning"

#: src/components/barcodes/BarcodeCameraInput.tsx:117
msgid "Error while stopping"
msgstr "Feil under stans"

#: src/components/barcodes/BarcodeCameraInput.tsx:159
msgid "Start scanning by selecting a camera and pressing the play button."
msgstr ""

#: src/components/barcodes/BarcodeCameraInput.tsx:180
msgid "Stop scanning"
msgstr "Stopp skanning"

#: src/components/barcodes/BarcodeCameraInput.tsx:190
msgid "Start scanning"
msgstr "Start skanningen"

#: src/components/barcodes/BarcodeInput.tsx:34
#: src/tables/general/BarcodeScanTable.tsx:55
#: src/tables/settings/BarcodeScanHistoryTable.tsx:64
msgid "Barcode"
msgstr "Strekkode"

#: src/components/barcodes/BarcodeInput.tsx:35
#: src/components/barcodes/BarcodeKeyboardInput.tsx:18
#: src/defaults/actions.tsx:72
msgid "Scan"
msgstr "Skann"

#: src/components/barcodes/BarcodeInput.tsx:53
msgid "Camera Input"
msgstr ""

#: src/components/barcodes/BarcodeInput.tsx:63
msgid "Scanner Input"
msgstr "Skanner input"

#: src/components/barcodes/BarcodeInput.tsx:105
msgid "Barcode Data"
msgstr "Strekkodedata"

#: src/components/barcodes/BarcodeInput.tsx:109
msgid "No barcode data"
msgstr "Ingen strekkodedata"

#: src/components/barcodes/BarcodeInput.tsx:110
msgid "Scan or enter barcode data"
msgstr "Skann eller skriv strekkodedata"

#: src/components/barcodes/BarcodeKeyboardInput.tsx:64
msgid "Enter barcode data"
msgstr ""

#: src/components/barcodes/BarcodeScanDialog.tsx:49
#: src/components/buttons/ScanButton.tsx:15
#: src/components/nav/NavigationDrawer.tsx:129
#: src/forms/PurchaseOrderForms.tsx:454
#: src/forms/PurchaseOrderForms.tsx:560
msgid "Scan Barcode"
msgstr "Skann strekkode"

#: src/components/barcodes/BarcodeScanDialog.tsx:105
msgid "No matching item found"
msgstr ""

#: src/components/barcodes/BarcodeScanDialog.tsx:134
msgid "Barcode does not match the expected model type"
msgstr ""

#: src/components/barcodes/BarcodeScanDialog.tsx:145
#: src/components/editors/NotesEditor.tsx:84
#: src/components/editors/NotesEditor.tsx:118
#: src/components/forms/ApiForm.tsx:451
#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:45
#: src/tables/bom/BomTable.tsx:518
#: src/tables/settings/PendingTasksTable.tsx:68
msgid "Success"
msgstr "Suksess"

#: src/components/barcodes/BarcodeScanDialog.tsx:151
msgid "Failed to handle barcode"
msgstr ""

#: src/components/barcodes/BarcodeScanDialog.tsx:167
#: src/pages/Index/Scan.tsx:129
msgid "Failed to scan barcode"
msgstr ""

#: src/components/barcodes/QRCode.tsx:94
msgid "Low (7%)"
msgstr "Lav (7%)"

#: src/components/barcodes/QRCode.tsx:95
msgid "Medium (15%)"
msgstr "Medium (15%)"

#: src/components/barcodes/QRCode.tsx:96
msgid "Quartile (25%)"
msgstr "Kvartil (25 %)"

#: src/components/barcodes/QRCode.tsx:97
msgid "High (30%)"
msgstr "Høy (30%)"

#: src/components/barcodes/QRCode.tsx:107
msgid "Custom barcode"
msgstr "Egendefinert strekkode"

#: src/components/barcodes/QRCode.tsx:108
msgid "A custom barcode is registered for this item. The shown code is not that custom barcode."
msgstr ""

#: src/components/barcodes/QRCode.tsx:127
msgid "Barcode Data:"
msgstr "Strekkodedata:"

#: src/components/barcodes/QRCode.tsx:138
msgid "Select Error Correction Level"
msgstr ""

#: src/components/barcodes/QRCode.tsx:170
msgid "Failed to link barcode"
msgstr ""

#: src/components/barcodes/QRCode.tsx:179
#: src/pages/part/PartDetail.tsx:522
#: src/pages/purchasing/PurchaseOrderDetail.tsx:204
#: src/pages/sales/ReturnOrderDetail.tsx:168
#: src/pages/sales/SalesOrderDetail.tsx:180
#: src/pages/sales/SalesOrderShipmentDetail.tsx:168
msgid "Link"
msgstr "Lenke"

#: src/components/barcodes/QRCode.tsx:200
msgid "This will remove the link to the associated barcode"
msgstr ""

#: src/components/barcodes/QRCode.tsx:205
#: src/components/items/ActionDropdown.tsx:190
#: src/forms/PurchaseOrderForms.tsx:551
msgid "Unlink Barcode"
msgstr "Fjern strekkodekobling"

#: src/components/buttons/AdminButton.tsx:86
msgid "Open in admin interface"
msgstr ""

#: src/components/buttons/CopyButton.tsx:18
#~ msgid "Copy to clipboard"
#~ msgstr "Copy to clipboard"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copied"
msgstr "Kopiert"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copy"
msgstr "Kopi"

#: src/components/buttons/PrintingActions.tsx:51
msgid "Printing Labels"
msgstr "Skriver etikett"

#: src/components/buttons/PrintingActions.tsx:56
msgid "Printing Reports"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:77
#~ msgid "Printing"
#~ msgstr "Printing"

#: src/components/buttons/PrintingActions.tsx:78
#~ msgid "Printing completed successfully"
#~ msgstr "Printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:114
#~ msgid "Label printing completed successfully"
#~ msgstr "Label printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:116
msgid "Print Label"
msgstr "Skriv ut etikett"

#: src/components/buttons/PrintingActions.tsx:121
#~ msgid "The label could not be generated"
#~ msgstr "The label could not be generated"

#: src/components/buttons/PrintingActions.tsx:122
#: src/components/buttons/PrintingActions.tsx:148
msgid "Print"
msgstr "Skriv ut"

#: src/components/buttons/PrintingActions.tsx:131
msgid "Print Report"
msgstr "Skriv ut rapport"

#: src/components/buttons/PrintingActions.tsx:152
#~ msgid "Generate"
#~ msgstr "Generate"

#: src/components/buttons/PrintingActions.tsx:153
#~ msgid "Report printing completed successfully"
#~ msgstr "Report printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:159
#~ msgid "The report could not be generated"
#~ msgstr "The report could not be generated"

#: src/components/buttons/PrintingActions.tsx:169
msgid "Printing Actions"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:174
msgid "Print Labels"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:180
msgid "Print Reports"
msgstr ""

#: src/components/buttons/RemoveRowButton.tsx:8
msgid "Remove this row"
msgstr ""

#: src/components/buttons/SSOButton.tsx:40
msgid "You will be redirected to the provider for further actions."
msgstr ""

#: src/components/buttons/SSOButton.tsx:44
#~ msgid "This provider is not full set up."
#~ msgstr "This provider is not full set up."

#: src/components/buttons/SSOButton.tsx:54
#~ msgid "Sign in redirect failed."
#~ msgstr "Sign in redirect failed."

#: src/components/buttons/ScanButton.tsx:15
#~ msgid "Scan QR code"
#~ msgstr "Scan QR code"

#: src/components/buttons/ScanButton.tsx:19
msgid "Open Barcode Scanner"
msgstr ""

#: src/components/buttons/ScanButton.tsx:20
#~ msgid "Open QR code scanner"
#~ msgstr "Open QR code scanner"

#: src/components/buttons/SpotlightButton.tsx:12
msgid "Open spotlight"
msgstr ""

#: src/components/buttons/StarredToggleButton.tsx:36
msgid "Subscription Updated"
msgstr ""

#: src/components/buttons/StarredToggleButton.tsx:57
#~ msgid "Unsubscribe from part"
#~ msgstr "Unsubscribe from part"

#: src/components/buttons/StarredToggleButton.tsx:66
msgid "Unsubscribe from notifications"
msgstr ""

#: src/components/buttons/StarredToggleButton.tsx:67
msgid "Subscribe to notifications"
msgstr ""

#: src/components/calendar/Calendar.tsx:99
#: src/components/calendar/Calendar.tsx:162
msgid "Calendar Filters"
msgstr ""

#: src/components/calendar/Calendar.tsx:114
msgid "Previous month"
msgstr "Forrige måned"

#: src/components/calendar/Calendar.tsx:123
msgid "Select month"
msgstr "Velg måned"

#: src/components/calendar/Calendar.tsx:144
msgid "Next month"
msgstr "Neste måned"

#: src/components/calendar/Calendar.tsx:175
#: src/tables/InvenTreeTableHeader.tsx:291
msgid "Download data"
msgstr "Last ned data"

#: src/components/calendar/OrderCalendar.tsx:132
msgid "Order Updated"
msgstr ""

#: src/components/calendar/OrderCalendar.tsx:142
msgid "Error updating order"
msgstr ""

#: src/components/calendar/OrderCalendar.tsx:178
#: src/tables/Filter.tsx:144
msgid "Overdue"
msgstr "Forfalt"

#: src/components/dashboard/DashboardLayout.tsx:225
msgid "Failed to load dashboard widgets."
msgstr ""

#: src/components/dashboard/DashboardLayout.tsx:235
msgid "No Widgets Selected"
msgstr ""

#: src/components/dashboard/DashboardLayout.tsx:238
msgid "Use the menu to add widgets to the dashboard"
msgstr ""

#: src/components/dashboard/DashboardMenu.tsx:62
#: src/components/dashboard/DashboardMenu.tsx:138
msgid "Accept Layout"
msgstr ""

#: src/components/dashboard/DashboardMenu.tsx:94
#: src/components/nav/NavigationDrawer.tsx:71
#: src/defaults/actions.tsx:28
#: src/defaults/links.tsx:31
#: src/pages/Index/Home.tsx:8
msgid "Dashboard"
msgstr "Dashbord"

#: src/components/dashboard/DashboardMenu.tsx:102
msgid "Edit Layout"
msgstr "Rediger oppsett"

#: src/components/dashboard/DashboardMenu.tsx:111
msgid "Add Widget"
msgstr ""

#: src/components/dashboard/DashboardMenu.tsx:120
msgid "Remove Widgets"
msgstr ""

#: src/components/dashboard/DashboardMenu.tsx:129
msgid "Clear Widgets"
msgstr ""

#: src/components/dashboard/DashboardWidget.tsx:81
msgid "Remove this widget from the dashboard"
msgstr ""

#: src/components/dashboard/DashboardWidgetDrawer.tsx:77
msgid "Filter dashboard widgets"
msgstr ""

#: src/components/dashboard/DashboardWidgetDrawer.tsx:98
msgid "Add this widget to the dashboard"
msgstr ""

#: src/components/dashboard/DashboardWidgetDrawer.tsx:123
msgid "No Widgets Available"
msgstr ""

#: src/components/dashboard/DashboardWidgetDrawer.tsx:124
msgid "There are no more widgets available for the dashboard"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:24
msgid "Subscribed Parts"
msgstr "Abonnerte deler"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:25
msgid "Show the number of parts which you have subscribed to"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:31
msgid "Subscribed Categories"
msgstr "Abonnerte kategorier"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:32
msgid "Show the number of part categories which you have subscribed to"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:38
msgid "Invalid BOMs"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:39
msgid "Assemblies requiring bill of materials validation"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:50
#: src/tables/part/PartTable.tsx:250
msgid "Low Stock"
msgstr "Lav lagerbeholdning"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:52
msgid "Show the number of parts which are low on stock"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:57
msgid "Required for Build Orders"
msgstr "Nødvendig for produksjonsordre"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:59
msgid "Show parts which are required for active build orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:64
msgid "Expired Stock Items"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:66
msgid "Show the number of stock items which have expired"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:72
msgid "Stale Stock Items"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:74
msgid "Show the number of stock items which are stale"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:80
msgid "Active Build Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:82
msgid "Show the number of build orders which are currently active"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:87
msgid "Overdue Build Orders"
msgstr "Forfalte Produksjonsordre"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:89
msgid "Show the number of build orders which are overdue"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:94
msgid "Assigned Build Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:96
msgid "Show the number of build orders which are assigned to you"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:101
msgid "Active Sales Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:103
msgid "Show the number of sales orders which are currently active"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:108
msgid "Overdue Sales Orders"
msgstr "Forfalte salgsordre"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:110
msgid "Show the number of sales orders which are overdue"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:115
msgid "Assigned Sales Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:117
msgid "Show the number of sales orders which are assigned to you"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:122
msgid "Active Purchase Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:124
msgid "Show the number of purchase orders which are currently active"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:129
msgid "Overdue Purchase Orders"
msgstr "Forfalte innkjøpsordre"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:131
msgid "Show the number of purchase orders which are overdue"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:136
msgid "Assigned Purchase Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:138
msgid "Show the number of purchase orders which are assigned to you"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:143
msgid "Active Return Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:145
msgid "Show the number of return orders which are currently active"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:150
msgid "Overdue Return Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:152
msgid "Show the number of return orders which are overdue"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:157
msgid "Assigned Return Orders"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:159
msgid "Show the number of return orders which are assigned to you"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:179
#: src/components/dashboard/widgets/GetStartedWidget.tsx:15
#: src/defaults/links.tsx:86
msgid "Getting Started"
msgstr "Komme i gang"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:180
#: src/defaults/links.tsx:89
msgid "Getting started with InvenTree"
msgstr "Komme i gang med InvenTree"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:187
#: src/components/dashboard/widgets/NewsWidget.tsx:123
msgid "News Updates"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:188
msgid "The latest news from InvenTree"
msgstr ""

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:18
#: src/components/nav/MainMenu.tsx:87
msgid "Change Color Mode"
msgstr ""

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:23
msgid "Change the color mode of the user interface"
msgstr ""

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:18
msgid "Change Language"
msgstr ""

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:23
msgid "Change the language of the user interface"
msgstr ""

#: src/components/dashboard/widgets/NewsWidget.tsx:60
#: src/components/nav/NotificationDrawer.tsx:94
#: src/pages/Notifications.tsx:53
msgid "Mark as read"
msgstr "Merk som lest"

#: src/components/dashboard/widgets/NewsWidget.tsx:115
msgid "Requires Superuser"
msgstr ""

#: src/components/dashboard/widgets/NewsWidget.tsx:116
msgid "This widget requires superuser permissions"
msgstr ""

#: src/components/dashboard/widgets/NewsWidget.tsx:133
msgid "No News"
msgstr ""

#: src/components/dashboard/widgets/NewsWidget.tsx:134
msgid "There are no unread news items"
msgstr ""

#: src/components/details/Details.tsx:117
#~ msgid "Email:"
#~ msgstr "Email:"

#: src/components/details/Details.tsx:123
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:76
#: src/pages/core/UserDetail.tsx:93
#: src/pages/core/UserDetail.tsx:203
#: src/tables/settings/UserTable.tsx:420
msgid "Superuser"
msgstr "Superbruker"

#: src/components/details/Details.tsx:124
#: src/pages/core/UserDetail.tsx:87
#: src/pages/core/UserDetail.tsx:200
#: src/tables/settings/UserTable.tsx:415
msgid "Staff"
msgstr ""

#: src/components/details/Details.tsx:125
msgid "Email: "
msgstr "Epost: "

#: src/components/details/Details.tsx:407
msgid "No name defined"
msgstr ""

#: src/components/details/DetailsImage.tsx:77
msgid "Remove Image"
msgstr "Fjern bilde"

#: src/components/details/DetailsImage.tsx:80
msgid "Remove the associated image from this item?"
msgstr ""

#: src/components/details/DetailsImage.tsx:83
#: src/forms/StockForms.tsx:828
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:203
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:408
msgid "Remove"
msgstr "Fjern"

#: src/components/details/DetailsImage.tsx:109
msgid "Drag and drop to upload"
msgstr ""

#: src/components/details/DetailsImage.tsx:112
msgid "Click to select file(s)"
msgstr ""

#: src/components/details/DetailsImage.tsx:172
msgid "Image uploaded"
msgstr ""

#: src/components/details/DetailsImage.tsx:173
msgid "Image has been uploaded successfully"
msgstr ""

#: src/components/details/DetailsImage.tsx:180
#: src/tables/general/AttachmentTable.tsx:201
msgid "Upload Error"
msgstr "Opplastningsfeil"

#: src/components/details/DetailsImage.tsx:250
msgid "Clear"
msgstr ""

#: src/components/details/DetailsImage.tsx:256
#: src/components/forms/ApiForm.tsx:661
#: src/contexts/ThemeContext.tsx:44
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:654
msgid "Submit"
msgstr "Send"

#: src/components/details/DetailsImage.tsx:300
msgid "Select from existing images"
msgstr ""

#: src/components/details/DetailsImage.tsx:308
msgid "Select Image"
msgstr "Velg bilde"

#: src/components/details/DetailsImage.tsx:324
msgid "Download remote image"
msgstr ""

#: src/components/details/DetailsImage.tsx:339
msgid "Upload new image"
msgstr ""

#: src/components/details/DetailsImage.tsx:346
msgid "Upload Image"
msgstr ""

#: src/components/details/DetailsImage.tsx:359
msgid "Delete image"
msgstr "Slett bilde"

#: src/components/details/DetailsImage.tsx:393
msgid "Download Image"
msgstr "Last ned bilde"

#: src/components/details/DetailsImage.tsx:398
msgid "Image downloaded successfully"
msgstr ""

#: src/components/details/PartIcons.tsx:43
#~ msgid "Part is a template part (variants can be made from this part)"
#~ msgstr "Part is a template part (variants can be made from this part)"

#: src/components/details/PartIcons.tsx:49
#~ msgid "Part can be assembled from other parts"
#~ msgstr "Part can be assembled from other parts"

#: src/components/details/PartIcons.tsx:55
#~ msgid "Part can be used in assemblies"
#~ msgstr "Part can be used in assemblies"

#: src/components/details/PartIcons.tsx:61
#~ msgid "Part stock is tracked by serial number"
#~ msgstr "Part stock is tracked by serial number"

#: src/components/details/PartIcons.tsx:67
#~ msgid "Part can be purchased from external suppliers"
#~ msgstr "Part can be purchased from external suppliers"

#: src/components/details/PartIcons.tsx:73
#~ msgid "Part can be sold to customers"
#~ msgstr "Part can be sold to customers"

#: src/components/details/PartIcons.tsx:78
#~ msgid "Part is virtual (not a physical part)"
#~ msgstr "Part is virtual (not a physical part)"

#: src/components/editors/NotesEditor.tsx:75
msgid "Image upload failed"
msgstr ""

#: src/components/editors/NotesEditor.tsx:85
msgid "Image uploaded successfully"
msgstr ""

#: src/components/editors/NotesEditor.tsx:119
msgid "Notes saved successfully"
msgstr ""

#: src/components/editors/NotesEditor.tsx:130
msgid "Failed to save notes"
msgstr "Kunne ikke lagre notater"

#: src/components/editors/NotesEditor.tsx:133
msgid "Error Saving Notes"
msgstr ""

#: src/components/editors/NotesEditor.tsx:151
#~ msgid "Disable Editing"
#~ msgstr "Disable Editing"

#: src/components/editors/NotesEditor.tsx:153
msgid "Save Notes"
msgstr ""

#: src/components/editors/NotesEditor.tsx:172
msgid "Close Editor"
msgstr ""

#: src/components/editors/NotesEditor.tsx:179
msgid "Enable Editing"
msgstr ""

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Preview Notes"
#~ msgstr "Preview Notes"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Edit Notes"
#~ msgstr "Edit Notes"

#: src/components/editors/TemplateEditor/CodeEditor/index.tsx:9
msgid "Code"
msgstr ""

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:44
#~ msgid "Failed to parse error response from server."
#~ msgstr "Failed to parse error response from server."

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:50
msgid "Error rendering preview"
msgstr ""

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:120
msgid "Preview not available, click \"Reload Preview\"."
msgstr ""

#: src/components/editors/TemplateEditor/PdfPreview/index.tsx:9
msgid "PDF Preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:109
msgid "Error loading template"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:121
msgid "Error saving template"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:151
#~ msgid "Save & Reload preview?"
#~ msgstr "Save & Reload preview?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:158
msgid "Could not load the template from the server."
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:175
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:306
msgid "Save & Reload Preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:180
msgid "Are you sure you want to Save & Reload the preview?"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:182
msgid "To render the preview the current template needs to be replaced on the server with your modifications which may break the label if it is under active use. Do you want to proceed?"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:186
msgid "Save & Reload"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:218
msgid "Preview updated"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:219
msgid "The preview has been updated successfully."
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:263
#~ msgid "Save & Reload preview"
#~ msgstr "Save & Reload preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:298
msgid "Reload preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:299
msgid "Use the currently stored template from the server"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:307
msgid "Save the current template and reload the preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:322
#~ msgid "to preview"
#~ msgstr "to preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:366
msgid "Select instance to preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:410
msgid "Error rendering template"
msgstr ""

#: src/components/errors/ClientError.tsx:23
msgid "Client Error"
msgstr ""

#: src/components/errors/ClientError.tsx:24
msgid "Client error occurred"
msgstr ""

#: src/components/errors/GenericErrorPage.tsx:50
msgid "Status Code"
msgstr ""

#: src/components/errors/GenericErrorPage.tsx:63
msgid "Return to the index page"
msgstr ""

#: src/components/errors/NotAuthenticated.tsx:8
msgid "Not Authenticated"
msgstr ""

#: src/components/errors/NotAuthenticated.tsx:9
msgid "You are not logged in."
msgstr ""

#: src/components/errors/NotFound.tsx:8
msgid "Page Not Found"
msgstr ""

#: src/components/errors/NotFound.tsx:9
msgid "This page does not exist"
msgstr ""

#: src/components/errors/PermissionDenied.tsx:8
#: src/functions/notifications.tsx:25
msgid "Permission Denied"
msgstr ""

#: src/components/errors/PermissionDenied.tsx:9
msgid "You do not have permission to view this page."
msgstr ""

#: src/components/errors/ServerError.tsx:8
msgid "Server Error"
msgstr ""

#: src/components/errors/ServerError.tsx:9
msgid "A server error occurred"
msgstr ""

#: src/components/forms/ApiForm.tsx:103
#: src/components/forms/ApiForm.tsx:580
msgid "Form Error"
msgstr "Skjemafeil"

#: src/components/forms/ApiForm.tsx:487
#~ msgid "Form Errors Exist"
#~ msgstr "Form Errors Exist"

#: src/components/forms/ApiForm.tsx:588
msgid "Errors exist for one or more form fields"
msgstr ""

#: src/components/forms/ApiForm.tsx:699
#: src/hooks/UseForm.tsx:129
#: src/tables/plugin/PluginListTable.tsx:204
msgid "Update"
msgstr "Oppdater"

#: src/components/forms/AuthenticationForm.tsx:48
#: src/components/forms/AuthenticationForm.tsx:74
#: src/functions/auth.tsx:83
#~ msgid "Check your your input and try again."
#~ msgstr "Check your your input and try again."

#: src/components/forms/AuthenticationForm.tsx:52
#~ msgid "Welcome back!"
#~ msgstr "Welcome back!"

#: src/components/forms/AuthenticationForm.tsx:53
#~ msgid "Login successfull"
#~ msgstr "Login successfull"

#: src/components/forms/AuthenticationForm.tsx:65
#: src/functions/auth.tsx:74
#~ msgid "Mail delivery successfull"
#~ msgstr "Mail delivery successfull"

#: src/components/forms/AuthenticationForm.tsx:73
msgid "Login successful"
msgstr "Innlogging vellykket"

#: src/components/forms/AuthenticationForm.tsx:74
msgid "Logged in successfully"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:81
#: src/components/forms/AuthenticationForm.tsx:89
msgid "Login failed"
msgstr "Innloggingen mislyktes"

#: src/components/forms/AuthenticationForm.tsx:82
#: src/components/forms/AuthenticationForm.tsx:90
#: src/components/forms/AuthenticationForm.tsx:106
#: src/functions/auth.tsx:282
msgid "Check your input and try again."
msgstr "Kontroller inndataene og prøv igjen."

#: src/components/forms/AuthenticationForm.tsx:100
#: src/functions/auth.tsx:273
msgid "Mail delivery successful"
msgstr "Levering av e-post vellykket"

#: src/components/forms/AuthenticationForm.tsx:101
msgid "Check your inbox for the login link. If you have an account, you will receive a login link. Check in spam too."
msgstr "Sjekk innboksen din for innloggingslenken. Hvis du har en konto, får du en innloggingslenke. Sjekk også i spam."

#: src/components/forms/AuthenticationForm.tsx:105
msgid "Mail delivery failed"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:125
msgid "Or continue with other methods"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:136
#: src/components/forms/AuthenticationForm.tsx:296
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:64
#: src/pages/core/UserDetail.tsx:48
msgid "Username"
msgstr "Brukernavn"

#: src/components/forms/AuthenticationForm.tsx:136
#~ msgid "I will use username and password"
#~ msgstr "I will use username and password"

#: src/components/forms/AuthenticationForm.tsx:138
#: src/components/forms/AuthenticationForm.tsx:298
msgid "Your username"
msgstr "Your username"

#: src/components/forms/AuthenticationForm.tsx:143
#: src/components/forms/AuthenticationForm.tsx:311
#: src/pages/Auth/ResetPassword.tsx:34
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:708
msgid "Password"
msgstr "Passord"

#: src/components/forms/AuthenticationForm.tsx:145
#: src/components/forms/AuthenticationForm.tsx:313
msgid "Your password"
msgstr "Ditt passord"

#: src/components/forms/AuthenticationForm.tsx:164
msgid "Reset password"
msgstr "Tilbakestill passord"

#: src/components/forms/AuthenticationForm.tsx:173
#: src/components/forms/AuthenticationForm.tsx:303
#: src/pages/Auth/Reset.tsx:17
#: src/pages/core/UserDetail.tsx:71
msgid "Email"
msgstr "E-post"

#: src/components/forms/AuthenticationForm.tsx:174
#: src/pages/Auth/Reset.tsx:18
msgid "We will send you a link to login - if you are registered"
msgstr "Vi sender deg en lenke for å logge inn - hvis du er registrert"

#: src/components/forms/AuthenticationForm.tsx:190
msgid "Send me an email"
msgstr "Send meg en e-post"

#: src/components/forms/AuthenticationForm.tsx:192
msgid "Use username and password"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:201
msgid "Log In"
msgstr "Logg inn"

#: src/components/forms/AuthenticationForm.tsx:203
#: src/pages/Auth/Reset.tsx:26
msgid "Send Email"
msgstr "Send e-post"

#: src/components/forms/AuthenticationForm.tsx:239
msgid "Passwords do not match"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:256
msgid "Registration successful"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:257
msgid "Please confirm your email address to complete the registration"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:280
msgid "Input error"
msgstr "Inndatafeil"

#: src/components/forms/AuthenticationForm.tsx:281
msgid "Check your input and try again. "
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:305
msgid "This will be used for a confirmation"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:318
msgid "Password repeat"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:320
msgid "Repeat password"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:332
#: src/pages/Auth/Login.tsx:121
#: src/pages/Auth/Register.tsx:13
msgid "Register"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:338
msgid "Or use SSO"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:348
msgid "Registration not active"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:349
msgid "This might be related to missing mail settings or could be a deliberate decision."
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:36
#: src/components/forms/HostOptionsForm.tsx:67
msgid "Host"
msgstr "Vert"

#: src/components/forms/HostOptionsForm.tsx:42
#: src/components/forms/HostOptionsForm.tsx:70
#: src/components/forms/InstanceOptions.tsx:124
#: src/components/plugins/PluginDrawer.tsx:68
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:19
#: src/pages/part/CategoryDetail.tsx:86
#: src/pages/part/PartDetail.tsx:447
#: src/pages/stock/LocationDetail.tsx:84
#: src/tables/machine/MachineTypeTable.tsx:72
#: src/tables/machine/MachineTypeTable.tsx:118
#: src/tables/machine/MachineTypeTable.tsx:236
#: src/tables/machine/MachineTypeTable.tsx:339
#: src/tables/plugin/PluginErrorTable.tsx:33
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:36
#: src/tables/settings/ApiTokenTable.tsx:58
#: src/tables/settings/GroupTable.tsx:95
#: src/tables/settings/GroupTable.tsx:148
#: src/tables/settings/GroupTable.tsx:196
#: src/tables/settings/PendingTasksTable.tsx:37
#: src/tables/stock/LocationTypesTable.tsx:74
msgid "Name"
msgstr "Navn"

#: src/components/forms/HostOptionsForm.tsx:75
msgid "No one here..."
msgstr "Ingen her..."

#: src/components/forms/HostOptionsForm.tsx:86
msgid "Add Host"
msgstr "Legg til vert"

#: src/components/forms/HostOptionsForm.tsx:90
#: src/components/items/RoleTable.tsx:224
#: src/components/items/TransferList.tsx:215
#: src/components/items/TransferList.tsx:223
msgid "Save"
msgstr "Lagre"

#: src/components/forms/InstanceOptions.tsx:43
#~ msgid "Select destination instance"
#~ msgstr "Select destination instance"

#: src/components/forms/InstanceOptions.tsx:58
msgid "Select Server"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:68
#: src/components/forms/InstanceOptions.tsx:92
msgid "Edit host options"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:71
#~ msgid "Edit possible host options"
#~ msgstr "Edit possible host options"

#: src/components/forms/InstanceOptions.tsx:76
msgid "Save host selection"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:98
#~ msgid "Version: {0}"
#~ msgstr "Version: {0}"

#: src/components/forms/InstanceOptions.tsx:100
#~ msgid "API:{0}"
#~ msgstr "API:{0}"

#: src/components/forms/InstanceOptions.tsx:102
#~ msgid "Name: {0}"
#~ msgstr "Name: {0}"

#: src/components/forms/InstanceOptions.tsx:104
#~ msgid "State: <0>worker</0> ({0}), <1>plugins</1>{1}"
#~ msgstr "State: <0>worker</0> ({0}), <1>plugins</1>{1}"

#: src/components/forms/InstanceOptions.tsx:118
#: src/pages/Index/Settings/SystemSettings.tsx:45
msgid "Server"
msgstr "Server"

#: src/components/forms/InstanceOptions.tsx:130
#: src/components/plugins/PluginDrawer.tsx:88
#: src/tables/plugin/PluginListTable.tsx:127
msgid "Version"
msgstr "Versjon"

#: src/components/forms/InstanceOptions.tsx:136
#: src/components/modals/AboutInvenTreeModal.tsx:122
#: src/components/modals/ServerInfoModal.tsx:34
msgid "API Version"
msgstr "API-versjon"

#: src/components/forms/InstanceOptions.tsx:142
#: src/components/nav/NavigationDrawer.tsx:205
#: src/pages/Index/Settings/AdminCenter/Index.tsx:218
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:46
msgid "Plugins"
msgstr "Utvidelser"

#: src/components/forms/InstanceOptions.tsx:143
#: src/tables/part/PartTestTemplateTable.tsx:117
#: src/tables/settings/TemplateTable.tsx:251
#: src/tables/settings/TemplateTable.tsx:362
#: src/tables/stock/StockItemTestResultTable.tsx:416
msgid "Enabled"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:143
msgid "Disabled"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:149
msgid "Worker"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:150
#: src/tables/settings/FailedTasksTable.tsx:48
msgid "Stopped"
msgstr "Stoppet"

#: src/components/forms/InstanceOptions.tsx:150
msgid "Running"
msgstr ""

#: src/components/forms/fields/IconField.tsx:83
msgid "No icon selected"
msgstr ""

#: src/components/forms/fields/IconField.tsx:161
msgid "Uncategorized"
msgstr ""

#: src/components/forms/fields/IconField.tsx:211
#: src/components/nav/Layout.tsx:80
#: src/tables/part/PartThumbTable.tsx:201
msgid "Search..."
msgstr "Søk..."

#: src/components/forms/fields/IconField.tsx:225
msgid "Select category"
msgstr ""

#: src/components/forms/fields/IconField.tsx:234
msgid "Select pack"
msgstr ""

#. placeholder {0}: filteredIcons.length
#: src/components/forms/fields/IconField.tsx:239
msgid "{0} icons"
msgstr ""

#: src/components/forms/fields/RelatedModelField.tsx:388
#: src/components/modals/AboutInvenTreeModal.tsx:94
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:332
msgid "Loading"
msgstr "Laster"

#: src/components/forms/fields/RelatedModelField.tsx:390
msgid "No results found"
msgstr "Ingen resultater funnet"

#: src/components/forms/fields/TableField.tsx:46
msgid "modelRenderer entry required for tables"
msgstr ""

#: src/components/forms/fields/TableField.tsx:187
msgid "No entries available"
msgstr ""

#: src/components/forms/fields/TableField.tsx:198
msgid "Add new row"
msgstr ""

#: src/components/images/DetailsImage.tsx:252
#~ msgid "Select image"
#~ msgstr "Select image"

#: src/components/images/Thumbnail.tsx:12
msgid "Thumbnail"
msgstr "Miniatyrbilde"

#: src/components/importer/ImportDataSelector.tsx:175
msgid "Importing Rows"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:176
msgid "Please wait while the data is imported"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:193
msgid "An error occurred while importing data"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:214
msgid "Edit Data"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:246
msgid "Delete Row"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:276
msgid "Row"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:294
msgid "Row contains errors"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:335
msgid "Accept"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:368
msgid "Valid"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:369
msgid "Filter by row validation status"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:374
#: src/components/wizards/WizardDrawer.tsx:101
#: src/tables/build/BuildOutputTable.tsx:543
msgid "Complete"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:375
msgid "Filter by row completion status"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:393
msgid "Import selected rows"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:408
msgid "Processing Data"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:55
#: src/components/importer/ImporterColumnSelector.tsx:186
#: src/components/items/ErrorItem.tsx:12
#: src/functions/api.tsx:60
#: src/functions/auth.tsx:333
msgid "An error occurred"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:68
msgid "Select column, or leave blank to ignore this field."
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:91
#~ msgid "Select a column from the data file"
#~ msgstr "Select a column from the data file"

#: src/components/importer/ImporterColumnSelector.tsx:104
#~ msgid "Map data columns to database fields"
#~ msgstr "Map data columns to database fields"

#: src/components/importer/ImporterColumnSelector.tsx:119
#~ msgid "Imported Column Name"
#~ msgstr "Imported Column Name"

#: src/components/importer/ImporterColumnSelector.tsx:192
msgid "Ignore this field"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:206
msgid "Mapping data columns to database fields"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:211
msgid "Accept Column Mapping"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:224
msgid "Database Field"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:225
msgid "Field Description"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:226
msgid "Imported Column"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:227
msgid "Default Value"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:43
msgid "Upload File"
msgstr "Last opp fil"

#: src/components/importer/ImporterDrawer.tsx:44
msgid "Map Columns"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:45
msgid "Import Data"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:46
msgid "Process Data"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:47
msgid "Complete Import"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:89
msgid "Failed to fetch import session data"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:97
#~ msgid "Cancel import session"
#~ msgstr "Cancel import session"

#: src/components/importer/ImporterDrawer.tsx:104
msgid "Import Complete"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:107
msgid "Data has been imported successfully"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:109
#: src/components/modals/AboutInvenTreeModal.tsx:197
#: src/components/modals/ServerInfoModal.tsx:134
#: src/forms/BomForms.tsx:132
msgid "Close"
msgstr "Lukk"

#: src/components/importer/ImporterDrawer.tsx:119
#~ msgid "Import session has unknown status"
#~ msgstr "Import session has unknown status"

#: src/components/importer/ImporterDrawer.tsx:128
msgid "Importing Data"
msgstr ""

#: src/components/importer/ImporterImportProgress.tsx:36
#~ msgid "Importing Records"
#~ msgstr "Importing Records"

#: src/components/importer/ImporterImportProgress.tsx:39
#~ msgid "Imported rows"
#~ msgstr "Imported rows"

#: src/components/importer/ImporterStatus.tsx:19
msgid "Unknown Status"
msgstr ""

#: src/components/items/ActionDropdown.tsx:133
msgid "Options"
msgstr ""

#: src/components/items/ActionDropdown.tsx:162
#~ msgid "Link custom barcode"
#~ msgstr "Link custom barcode"

#: src/components/items/ActionDropdown.tsx:169
#: src/tables/InvenTreeTableHeader.tsx:193
#: src/tables/InvenTreeTableHeader.tsx:194
msgid "Barcode Actions"
msgstr "Strekkodehandlinger"

#: src/components/items/ActionDropdown.tsx:174
msgid "View Barcode"
msgstr "Se strekkode"

#: src/components/items/ActionDropdown.tsx:176
msgid "View barcode"
msgstr "Vis strekkode"

#: src/components/items/ActionDropdown.tsx:182
msgid "Link Barcode"
msgstr "Koble mot strekkode"

#: src/components/items/ActionDropdown.tsx:184
msgid "Link a custom barcode to this item"
msgstr ""

#: src/components/items/ActionDropdown.tsx:192
msgid "Unlink custom barcode"
msgstr "Koble fra egendefinert strekkode"

#: src/components/items/ActionDropdown.tsx:244
msgid "Edit item"
msgstr ""

#: src/components/items/ActionDropdown.tsx:256
msgid "Delete item"
msgstr "Slett element"

#: src/components/items/ActionDropdown.tsx:264
#: src/components/items/ActionDropdown.tsx:265
msgid "Hold"
msgstr ""

#: src/components/items/ActionDropdown.tsx:288
msgid "Duplicate item"
msgstr "Dupliser element"

#: src/components/items/BarcodeInput.tsx:24
#~ msgid "Scan barcode data here using barcode scanner"
#~ msgstr "Scan barcode data here using barcode scanner"

#: src/components/items/ColorToggle.tsx:17
msgid "Toggle color scheme"
msgstr ""

#: src/components/items/DocTooltip.tsx:92
#: src/components/items/GettingStartedCarousel.tsx:20
msgid "Read More"
msgstr "Les mer"

#: src/components/items/ErrorItem.tsx:8
#: src/functions/api.tsx:51
#: src/tables/settings/PendingTasksTable.tsx:80
msgid "Unknown error"
msgstr "Ukjent feil"

#: src/components/items/ErrorItem.tsx:13
#~ msgid "An error occurred:"
#~ msgstr "An error occurred:"

#: src/components/items/GettingStartedCarousel.tsx:27
#~ msgid "Read more"
#~ msgstr "Read more"

#: src/components/items/InfoItem.tsx:27
msgid "None"
msgstr "Ingen"

#: src/components/items/InvenTreeLogo.tsx:23
msgid "InvenTree Logo"
msgstr "InvenTree-logo"

#: src/components/items/LanguageToggle.tsx:21
msgid "Select language"
msgstr ""

#: src/components/items/OnlyStaff.tsx:10
#: src/components/modals/AboutInvenTreeModal.tsx:50
msgid "This information is only available for staff users"
msgstr "Denne informasjonen er bare tilgjengelig for ansatte"

#: src/components/items/Placeholder.tsx:14
#~ msgid "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."
#~ msgstr "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."

#: src/components/items/Placeholder.tsx:17
#~ msgid "PLH"
#~ msgstr "PLH"

#: src/components/items/RoleTable.tsx:81
msgid "Updating"
msgstr ""

#: src/components/items/RoleTable.tsx:82
msgid "Updating group roles"
msgstr ""

#: src/components/items/RoleTable.tsx:118
#: src/components/settings/ConfigValueList.tsx:42
#: src/pages/part/pricing/BomPricingPanel.tsx:191
#: src/pages/part/pricing/VariantPricingPanel.tsx:51
#: src/tables/purchasing/SupplierPartTable.tsx:137
msgid "Updated"
msgstr "Oppdatert"

#: src/components/items/RoleTable.tsx:119
msgid "Group roles updated"
msgstr ""

#: src/components/items/RoleTable.tsx:135
msgid "Role"
msgstr ""

#: src/components/items/RoleTable.tsx:140
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:413
msgid "View"
msgstr "Vis"

#: src/components/items/RoleTable.tsx:145
msgid "Change"
msgstr "Endre"

#: src/components/items/RoleTable.tsx:150
#: src/forms/StockForms.tsx:867
#: src/tables/stock/StockItemTestResultTable.tsx:364
msgid "Add"
msgstr "Legg til"

#: src/components/items/RoleTable.tsx:203
msgid "Reset group roles"
msgstr ""

#: src/components/items/RoleTable.tsx:212
msgid "Reset"
msgstr ""

#: src/components/items/RoleTable.tsx:215
msgid "Save group roles"
msgstr ""

#: src/components/items/TransferList.tsx:65
msgid "No items"
msgstr ""

#: src/components/items/TransferList.tsx:161
#: src/components/render/Stock.tsx:95
#: src/pages/part/PartDetail.tsx:980
#: src/pages/stock/StockDetail.tsx:262
#: src/pages/stock/StockDetail.tsx:913
#: src/tables/build/BuildAllocatedStockTable.tsx:135
#: src/tables/build/BuildLineTable.tsx:190
#: src/tables/part/PartTable.tsx:124
#: src/tables/stock/StockItemTable.tsx:183
#: src/tables/stock/StockItemTable.tsx:344
msgid "Available"
msgstr "Tilgjengelig"

#: src/components/items/TransferList.tsx:162
msgid "Selected"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:103
#~ msgid "Your InvenTree version status is"
#~ msgstr "Your InvenTree version status is"

#: src/components/modals/AboutInvenTreeModal.tsx:116
msgid "InvenTree Version"
msgstr "InvenTree-versjon"

#: src/components/modals/AboutInvenTreeModal.tsx:128
msgid "Python Version"
msgstr "Python-versjon"

#: src/components/modals/AboutInvenTreeModal.tsx:133
msgid "Django Version"
msgstr "Django-versjon"

#: src/components/modals/AboutInvenTreeModal.tsx:142
msgid "Commit Hash"
msgstr "Commit-hash"

#: src/components/modals/AboutInvenTreeModal.tsx:147
msgid "Commit Date"
msgstr "Commit-dato"

#: src/components/modals/AboutInvenTreeModal.tsx:152
msgid "Commit Branch"
msgstr "Commit Branch"

#: src/components/modals/AboutInvenTreeModal.tsx:163
msgid "Version Information"
msgstr "Versjoninformasjon"

#: src/components/modals/AboutInvenTreeModal.tsx:165
#~ msgid "Credits"
#~ msgstr "Credits"

#: src/components/modals/AboutInvenTreeModal.tsx:168
#~ msgid "InvenTree Documentation"
#~ msgstr "InvenTree Documentation"

#: src/components/modals/AboutInvenTreeModal.tsx:169
#~ msgid "View Code on GitHub"
#~ msgstr "View Code on GitHub"

#: src/components/modals/AboutInvenTreeModal.tsx:172
msgid "Links"
msgstr "Lenker"

#: src/components/modals/AboutInvenTreeModal.tsx:178
#: src/components/nav/NavigationDrawer.tsx:217
#: src/defaults/actions.tsx:35
msgid "Documentation"
msgstr "Dokumentasjon"

#: src/components/modals/AboutInvenTreeModal.tsx:179
msgid "Source Code"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:180
msgid "Mobile App"
msgstr "Mobilapp"

#: src/components/modals/AboutInvenTreeModal.tsx:181
msgid "Submit Bug Report"
msgstr "Send feilrapport"

#: src/components/modals/AboutInvenTreeModal.tsx:189
#: src/components/modals/ServerInfoModal.tsx:147
#~ msgid "Dismiss"
#~ msgstr "Dismiss"

#: src/components/modals/AboutInvenTreeModal.tsx:190
msgid "Copy version information"
msgstr "Kopiér versjonsinformasjon"

#: src/components/modals/AboutInvenTreeModal.tsx:207
msgid "Development Version"
msgstr "Utviklingsversjon"

#: src/components/modals/AboutInvenTreeModal.tsx:209
msgid "Up to Date"
msgstr "Oppdatert"

#: src/components/modals/AboutInvenTreeModal.tsx:211
msgid "Update Available"
msgstr "Oppdatering er tilgjengelig"

#: src/components/modals/LicenseModal.tsx:41
msgid "No license text available"
msgstr ""

#: src/components/modals/LicenseModal.tsx:48
msgid "No Information provided - this is likely a server issue"
msgstr ""

#: src/components/modals/LicenseModal.tsx:81
msgid "Loading license information"
msgstr ""

#: src/components/modals/LicenseModal.tsx:87
msgid "Failed to fetch license information"
msgstr ""

#: src/components/modals/LicenseModal.tsx:99
msgid "{key} Packages"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:24
#~ msgid "Unknown response"
#~ msgstr "Unknown response"

#: src/components/modals/QrCodeModal.tsx:39
#~ msgid "No scans yet!"
#~ msgstr "No scans yet!"

#: src/components/modals/QrCodeModal.tsx:57
#~ msgid "Close modal"
#~ msgstr "Close modal"

#: src/components/modals/ServerInfoModal.tsx:22
msgid "Instance Name"
msgstr "Instansnavn"

#: src/components/modals/ServerInfoModal.tsx:28
msgid "Server Version"
msgstr "Serverversjon"

#: src/components/modals/ServerInfoModal.tsx:38
#~ msgid "Bebug Mode"
#~ msgstr "Bebug Mode"

#: src/components/modals/ServerInfoModal.tsx:40
msgid "Database"
msgstr "Database"

#: src/components/modals/ServerInfoModal.tsx:49
#: src/components/nav/Alerts.tsx:41
msgid "Debug Mode"
msgstr "Feilsøkingsmodus"

#: src/components/modals/ServerInfoModal.tsx:54
msgid "Server is running in debug mode"
msgstr "Serveren kjører i debug-modus"

#: src/components/modals/ServerInfoModal.tsx:62
msgid "Docker Mode"
msgstr "Docker-modus"

#: src/components/modals/ServerInfoModal.tsx:65
msgid "Server is deployed using docker"
msgstr "Serveren er distribuert ved hjelp av docker"

#: src/components/modals/ServerInfoModal.tsx:71
msgid "Plugin Support"
msgstr "Støtte for utvidelser"

#: src/components/modals/ServerInfoModal.tsx:76
msgid "Plugin support enabled"
msgstr "Utvidelsesstøtte aktivert"

#: src/components/modals/ServerInfoModal.tsx:78
msgid "Plugin support disabled"
msgstr "Utvidelsesstøtte deaktivert"

#: src/components/modals/ServerInfoModal.tsx:85
msgid "Server status"
msgstr "Serverstatus"

#: src/components/modals/ServerInfoModal.tsx:91
msgid "Healthy"
msgstr "Frisk"

#: src/components/modals/ServerInfoModal.tsx:93
msgid "Issues detected"
msgstr "Problemer oppdaget"

#: src/components/modals/ServerInfoModal.tsx:102
#: src/components/nav/Alerts.tsx:50
msgid "Background Worker"
msgstr "Bakgrunnsarbeider"

#: src/components/modals/ServerInfoModal.tsx:107
msgid "The background worker process is not running"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:107
#~ msgid "The Background worker process is not running."
#~ msgstr "The Background worker process is not running."

#: src/components/modals/ServerInfoModal.tsx:115
#: src/pages/Index/Settings/AdminCenter/Index.tsx:119
msgid "Email Settings"
msgstr "E-Post-Innstillinger"

#: src/components/modals/ServerInfoModal.tsx:118
#~ msgid "Email settings not configured"
#~ msgstr "Email settings not configured"

#: src/components/modals/ServerInfoModal.tsx:120
#: src/components/nav/Alerts.tsx:61
msgid "Email settings not configured."
msgstr ""

#: src/components/nav/Alerts.tsx:43
msgid "The server is running in debug mode."
msgstr ""

#: src/components/nav/Alerts.tsx:52
msgid "The background worker process is not running."
msgstr ""

#: src/components/nav/Alerts.tsx:59
msgid "Email settings"
msgstr ""

#: src/components/nav/Alerts.tsx:68
msgid "Server Restart"
msgstr ""

#: src/components/nav/Alerts.tsx:70
msgid "The server requires a restart to apply changes."
msgstr ""

#: src/components/nav/Alerts.tsx:80
msgid "Database Migrations"
msgstr ""

#: src/components/nav/Alerts.tsx:82
msgid "There are pending database migrations."
msgstr ""

#: src/components/nav/Alerts.tsx:98
msgid "Alerts"
msgstr ""

#: src/components/nav/Alerts.tsx:141
msgid "Learn more about {code}"
msgstr ""

#: src/components/nav/Header.tsx:187
#: src/components/nav/NavigationDrawer.tsx:141
#: src/components/nav/NotificationDrawer.tsx:181
#: src/pages/Index/Settings/SystemSettings.tsx:121
#: src/pages/Index/Settings/UserSettings.tsx:106
#: src/pages/Notifications.tsx:45
#: src/pages/Notifications.tsx:130
msgid "Notifications"
msgstr "Varlser"

#: src/components/nav/Layout.tsx:83
msgid "Nothing found..."
msgstr ""

#: src/components/nav/MainMenu.tsx:40
#: src/pages/Index/Profile/Profile.tsx:15
#~ msgid "Profile"
#~ msgstr "Profile"

#: src/components/nav/MainMenu.tsx:52
#: src/components/nav/NavigationDrawer.tsx:193
#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:21
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:39
msgid "Settings"
msgstr "Innstillinger"

#: src/components/nav/MainMenu.tsx:59
#: src/pages/Index/Settings/UserSettings.tsx:145
msgid "Account Settings"
msgstr "Kontoinnstillinger"

#: src/components/nav/MainMenu.tsx:59
#: src/defaults/menuItems.tsx:15
#~ msgid "Account settings"
#~ msgstr "Account settings"

#: src/components/nav/MainMenu.tsx:67
#: src/components/nav/NavigationDrawer.tsx:153
#: src/components/nav/SettingsHeader.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:347
#: src/pages/Index/Settings/SystemSettings.tsx:352
msgid "System Settings"
msgstr "Systeminnstillinger"

#: src/components/nav/MainMenu.tsx:68
#~ msgid "Current language {locale}"
#~ msgstr "Current language {locale}"

#: src/components/nav/MainMenu.tsx:71
#~ msgid "Switch to pseudo language"
#~ msgstr "Switch to pseudo language"

#: src/components/nav/MainMenu.tsx:76
#: src/components/nav/NavigationDrawer.tsx:160
#: src/components/nav/SettingsHeader.tsx:42
#: src/defaults/actions.tsx:83
#: src/pages/Index/Settings/AdminCenter/Index.tsx:282
#: src/pages/Index/Settings/AdminCenter/Index.tsx:287
msgid "Admin Center"
msgstr "Adminsenter"

#: src/components/nav/MainMenu.tsx:96
msgid "Logout"
msgstr "Logg ut"

#: src/components/nav/NavHoverMenu.tsx:84
#~ msgid "View all"
#~ msgstr "View all"

#: src/components/nav/NavHoverMenu.tsx:100
#: src/components/nav/NavHoverMenu.tsx:110
#~ msgid "Get started"
#~ msgstr "Get started"

#: src/components/nav/NavHoverMenu.tsx:103
#~ msgid "Overview over high-level objects, functions and possible usecases."
#~ msgstr "Overview over high-level objects, functions and possible usecases."

#: src/components/nav/NavigationDrawer.tsx:60
#~ msgid "Pages"
#~ msgstr "Pages"

#: src/components/nav/NavigationDrawer.tsx:84
#: src/components/render/Part.tsx:33
#: src/defaults/links.tsx:42
#: src/forms/StockForms.tsx:735
#: src/pages/Index/Settings/SystemSettings.tsx:224
#: src/pages/part/PartDetail.tsx:804
#: src/pages/stock/LocationDetail.tsx:390
#: src/pages/stock/StockDetail.tsx:626
#: src/tables/stock/StockItemTable.tsx:86
msgid "Stock"
msgstr "Lagerbeholdning"

#: src/components/nav/NavigationDrawer.tsx:91
#: src/defaults/links.tsx:48
#: src/pages/build/BuildDetail.tsx:741
#: src/pages/build/BuildIndex.tsx:102
msgid "Manufacturing"
msgstr ""

#: src/components/nav/NavigationDrawer.tsx:98
#: src/defaults/links.tsx:54
#: src/pages/company/ManufacturerDetail.tsx:9
#: src/pages/company/ManufacturerPartDetail.tsx:260
#: src/pages/company/SupplierDetail.tsx:9
#: src/pages/company/SupplierPartDetail.tsx:356
#: src/pages/purchasing/PurchaseOrderDetail.tsx:534
#: src/pages/purchasing/PurchasingIndex.tsx:122
msgid "Purchasing"
msgstr "Innkjøp"

#: src/components/nav/NavigationDrawer.tsx:105
#: src/defaults/links.tsx:60
#: src/pages/company/CustomerDetail.tsx:9
#: src/pages/sales/ReturnOrderDetail.tsx:521
#: src/pages/sales/SalesIndex.tsx:139
#: src/pages/sales/SalesOrderDetail.tsx:591
#: src/pages/sales/SalesOrderShipmentDetail.tsx:360
msgid "Sales"
msgstr "Salg"

#: src/components/nav/NavigationDrawer.tsx:147
#: src/components/nav/SettingsHeader.tsx:40
#: src/pages/Index/Settings/UserSettings.tsx:141
msgid "User Settings"
msgstr ""

#: src/components/nav/NavigationDrawer.tsx:188
msgid "Navigation"
msgstr "Navigasjon"

#: src/components/nav/NavigationDrawer.tsx:223
msgid "About"
msgstr "Om"

#: src/components/nav/NavigationTree.tsx:211
msgid "Error loading navigation tree."
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:183
#: src/pages/Notifications.tsx:74
msgid "Mark all as read"
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:193
msgid "View all notifications"
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:216
msgid "You have no unread notifications."
msgstr "Du har ingen uleste varsler."

#: src/components/nav/NotificationDrawer.tsx:238
msgid "Error loading notifications."
msgstr ""

#: src/components/nav/SearchDrawer.tsx:106
msgid "No Overview Available"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:107
msgid "No overview available for this model type"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:125
msgid "View all results"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:140
msgid "results"
msgstr "resultater"

#: src/components/nav/SearchDrawer.tsx:144
msgid "Remove search group"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:288
#: src/pages/company/ManufacturerPartDetail.tsx:176
#: src/pages/part/PartDetail.tsx:861
#: src/pages/part/PartSupplierDetail.tsx:15
#: src/pages/purchasing/PurchasingIndex.tsx:81
msgid "Suppliers"
msgstr "Leverandører"

#: src/components/nav/SearchDrawer.tsx:298
#: src/pages/part/PartSupplierDetail.tsx:23
#: src/pages/purchasing/PurchasingIndex.tsx:98
msgid "Manufacturers"
msgstr "Produsenter"

#: src/components/nav/SearchDrawer.tsx:308
#: src/pages/sales/SalesIndex.tsx:124
msgid "Customers"
msgstr "Kunder"

#: src/components/nav/SearchDrawer.tsx:462
#~ msgid "No results"
#~ msgstr "No results"

#: src/components/nav/SearchDrawer.tsx:477
msgid "Enter search text"
msgstr "Skriv inn søketekst"

#: src/components/nav/SearchDrawer.tsx:488
msgid "Refresh search results"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:499
#: src/components/nav/SearchDrawer.tsx:506
msgid "Search Options"
msgstr "Alternativer for søk"

#: src/components/nav/SearchDrawer.tsx:509
msgid "Whole word search"
msgstr "Helordsøk"

#: src/components/nav/SearchDrawer.tsx:518
msgid "Regex search"
msgstr "Regex-søk"

#: src/components/nav/SearchDrawer.tsx:527
msgid "Notes search"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:575
msgid "An error occurred during search query"
msgstr "Det oppstod en feil under søk"

#: src/components/nav/SearchDrawer.tsx:586
#: src/tables/part/PartTestTemplateTable.tsx:82
msgid "No Results"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:589
msgid "No results available for search query"
msgstr "Ingen resultater tilgjengelig for søk"

#: src/components/panels/AttachmentPanel.tsx:18
msgid "Attachments"
msgstr "Vedlegg"

#: src/components/panels/NotesPanel.tsx:23
#: src/tables/build/BuildOrderTestTable.tsx:196
#: src/tables/stock/StockTrackingTable.tsx:212
msgid "Notes"
msgstr "Notater"

#: src/components/panels/PanelGroup.tsx:158
msgid "Plugin Provided"
msgstr ""

#: src/components/panels/PanelGroup.tsx:275
msgid "Collapse panels"
msgstr ""

#: src/components/panels/PanelGroup.tsx:275
msgid "Expand panels"
msgstr ""

#: src/components/plugins/LocateItemButton.tsx:68
#: src/components/plugins/LocateItemButton.tsx:88
msgid "Locate Item"
msgstr ""

#: src/components/plugins/LocateItemButton.tsx:70
msgid "Item location requested"
msgstr ""

#: src/components/plugins/PluginDrawer.tsx:47
msgid "Plugin Inactive"
msgstr ""

#: src/components/plugins/PluginDrawer.tsx:50
msgid "Plugin is not active"
msgstr ""

#: src/components/plugins/PluginDrawer.tsx:59
msgid "Plugin Information"
msgstr ""

#: src/components/plugins/PluginDrawer.tsx:73
#: src/forms/selectionListFields.tsx:104
#: src/pages/build/BuildDetail.tsx:244
#: src/pages/company/CompanyDetail.tsx:93
#: src/pages/company/ManufacturerPartDetail.tsx:91
#: src/pages/company/ManufacturerPartDetail.tsx:118
#: src/pages/company/SupplierPartDetail.tsx:144
#: src/pages/part/CategoryDetail.tsx:106
#: src/pages/part/PartDetail.tsx:461
#: src/pages/purchasing/PurchaseOrderDetail.tsx:144
#: src/pages/sales/ReturnOrderDetail.tsx:109
#: src/pages/sales/SalesOrderDetail.tsx:118
#: src/pages/stock/LocationDetail.tsx:104
#: src/tables/ColumnRenderers.tsx:269
#: src/tables/build/BuildAllocatedStockTable.tsx:91
#: src/tables/machine/MachineTypeTable.tsx:128
#: src/tables/machine/MachineTypeTable.tsx:239
#: src/tables/plugin/PluginListTable.tsx:110
msgid "Description"
msgstr "Beskrivelse"

#: src/components/plugins/PluginDrawer.tsx:78
msgid "Author"
msgstr "Forfatter"

#: src/components/plugins/PluginDrawer.tsx:83
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:41
#: src/pages/part/pricing/SaleHistoryPanel.tsx:38
#: src/tables/ColumnRenderers.tsx:411
#: src/tables/build/BuildOrderTestTable.tsx:204
msgid "Date"
msgstr "Dato"

#: src/components/plugins/PluginDrawer.tsx:93
#: src/forms/selectionListFields.tsx:105
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:68
#: src/pages/core/UserDetail.tsx:81
#: src/pages/core/UserDetail.tsx:209
#: src/pages/part/PartDetail.tsx:615
#: src/tables/bom/UsedInTable.tsx:90
#: src/tables/company/CompanyTable.tsx:57
#: src/tables/company/CompanyTable.tsx:91
#: src/tables/machine/MachineListTable.tsx:336
#: src/tables/machine/MachineListTable.tsx:606
#: src/tables/part/ParametricPartTable.tsx:350
#: src/tables/part/PartTable.tsx:184
#: src/tables/part/PartVariantTable.tsx:15
#: src/tables/plugin/PluginListTable.tsx:96
#: src/tables/plugin/PluginListTable.tsx:412
#: src/tables/purchasing/SupplierPartTable.tsx:85
#: src/tables/purchasing/SupplierPartTable.tsx:179
#: src/tables/settings/ApiTokenTable.tsx:63
#: src/tables/settings/UserTable.tsx:410
#: src/tables/stock/StockItemTable.tsx:323
msgid "Active"
msgstr "Aktiv"

#: src/components/plugins/PluginDrawer.tsx:105
msgid "Package Name"
msgstr ""

#: src/components/plugins/PluginDrawer.tsx:111
msgid "Installation Path"
msgstr ""

#: src/components/plugins/PluginDrawer.tsx:116
#: src/tables/machine/MachineTypeTable.tsx:151
#: src/tables/machine/MachineTypeTable.tsx:275
#: src/tables/plugin/PluginListTable.tsx:101
#: src/tables/plugin/PluginListTable.tsx:417
msgid "Builtin"
msgstr "Innebygd"

#: src/components/plugins/PluginDrawer.tsx:121
msgid "Package"
msgstr ""

#: src/components/plugins/PluginDrawer.tsx:133
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:55
#: src/pages/Index/Settings/SystemSettings.tsx:330
#: src/pages/Index/Settings/UserSettings.tsx:128
msgid "Plugin Settings"
msgstr "Innstillinger for Utvidelser"

#: src/components/plugins/PluginPanel.tsx:87
#~ msgid "Error occurred while rendering plugin content"
#~ msgstr "Error occurred while rendering plugin content"

#: src/components/plugins/PluginPanel.tsx:91
#~ msgid "Plugin did not provide panel rendering function"
#~ msgstr "Plugin did not provide panel rendering function"

#: src/components/plugins/PluginPanel.tsx:103
#~ msgid "No content provided for this plugin"
#~ msgstr "No content provided for this plugin"

#: src/components/plugins/PluginPanel.tsx:116
#: src/components/plugins/PluginSettingsPanel.tsx:76
#~ msgid "Error Loading Plugin"
#~ msgstr "Error Loading Plugin"

#: src/components/plugins/PluginSettingsPanel.tsx:51
#~ msgid "Error occurred while rendering plugin settings"
#~ msgstr "Error occurred while rendering plugin settings"

#: src/components/plugins/PluginSettingsPanel.tsx:55
#~ msgid "Plugin did not provide settings rendering function"
#~ msgstr "Plugin did not provide settings rendering function"

#: src/components/plugins/PluginUIFeature.tsx:102
msgid "Error occurred while rendering the template editor."
msgstr ""

#: src/components/plugins/PluginUIFeature.tsx:119
msgid "Error Loading Plugin Editor"
msgstr ""

#: src/components/plugins/PluginUIFeature.tsx:155
msgid "Error occurred while rendering the template preview."
msgstr ""

#: src/components/plugins/PluginUIFeature.tsx:166
msgid "Error Loading Plugin Preview"
msgstr ""

#: src/components/plugins/RemoteComponent.tsx:111
msgid "Invalid source or function name"
msgstr ""

#: src/components/plugins/RemoteComponent.tsx:143
msgid "Error Loading Content"
msgstr ""

#: src/components/plugins/RemoteComponent.tsx:147
msgid "Error occurred while loading plugin content"
msgstr ""

#: src/components/render/Instance.tsx:238
#~ msgid "Unknown model: {model}"
#~ msgstr "Unknown model: {model}"

#: src/components/render/Instance.tsx:246
msgid "Unknown model: {model_name}"
msgstr ""

#: src/components/render/ModelType.tsx:234
#~ msgid "Purchase Order Line Item"
#~ msgstr "Purchase Order Line Item"

#: src/components/render/ModelType.tsx:264
#~ msgid "Unknown Model"
#~ msgstr "Unknown Model"

#: src/components/render/ModelType.tsx:307
#~ msgid "Purchase Order Line Items"
#~ msgstr "Purchase Order Line Items"

#: src/components/render/ModelType.tsx:337
#~ msgid "Unknown Models"
#~ msgstr "Unknown Models"

#: src/components/render/Order.tsx:122
#: src/tables/sales/SalesOrderAllocationTable.tsx:170
msgid "Shipment"
msgstr "Forsendelse"

#: src/components/render/Part.tsx:28
#: src/components/render/Plugin.tsx:17
#: src/components/render/User.tsx:37
#: src/pages/company/CompanyDetail.tsx:325
#: src/pages/company/SupplierPartDetail.tsx:369
#: src/pages/core/UserDetail.tsx:211
#: src/pages/part/PartDetail.tsx:1012
msgid "Inactive"
msgstr ""

#: src/components/render/Part.tsx:31
#: src/tables/bom/BomTable.tsx:289
#: src/tables/part/PartTable.tsx:139
msgid "No stock"
msgstr "Ingen lagerbeholdning"

#: src/components/render/Part.tsx:74
#: src/pages/part/PartDetail.tsx:488
#: src/tables/ColumnRenderers.tsx:224
#: src/tables/ColumnRenderers.tsx:233
#: src/tables/notifications/NotificationTable.tsx:32
#: src/tables/part/PartCategoryTemplateTable.tsx:71
msgid "Category"
msgstr "Kategori"

#: src/components/render/Stock.tsx:36
#: src/components/render/Stock.tsx:107
#: src/components/render/Stock.tsx:125
#: src/forms/BuildForms.tsx:759
#: src/forms/PurchaseOrderForms.tsx:592
#: src/forms/StockForms.tsx:733
#: src/forms/StockForms.tsx:779
#: src/forms/StockForms.tsx:825
#: src/forms/StockForms.tsx:864
#: src/forms/StockForms.tsx:900
#: src/forms/StockForms.tsx:938
#: src/forms/StockForms.tsx:980
#: src/forms/StockForms.tsx:1028
#: src/forms/StockForms.tsx:1072
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:88
#: src/pages/core/UserDetail.tsx:158
#: src/pages/stock/StockDetail.tsx:295
#: src/tables/ColumnRenderers.tsx:176
#: src/tables/ColumnRenderers.tsx:185
#: src/tables/Filter.tsx:392
#: src/tables/stock/StockTrackingTable.tsx:98
msgid "Location"
msgstr ""

#: src/components/render/Stock.tsx:92
#: src/pages/stock/StockDetail.tsx:195
#: src/pages/stock/StockDetail.tsx:901
#: src/tables/build/BuildAllocatedStockTable.tsx:121
#: src/tables/build/BuildOutputTable.tsx:111
#: src/tables/sales/SalesOrderAllocationTable.tsx:139
msgid "Serial Number"
msgstr "Serienummer"

#: src/components/render/Stock.tsx:97
#: src/components/wizards/OrderPartsWizard.tsx:222
#: src/forms/BuildForms.tsx:243
#: src/forms/BuildForms.tsx:605
#: src/forms/BuildForms.tsx:761
#: src/forms/PurchaseOrderForms.tsx:794
#: src/forms/ReturnOrderForms.tsx:240
#: src/forms/SalesOrderForms.tsx:270
#: src/forms/StockForms.tsx:781
#: src/pages/part/PartStockHistoryDetail.tsx:56
#: src/pages/part/PartStockHistoryDetail.tsx:210
#: src/pages/part/PartStockHistoryDetail.tsx:234
#: src/pages/part/pricing/BomPricingPanel.tsx:146
#: src/pages/part/pricing/PriceBreakPanel.tsx:89
#: src/pages/part/pricing/PriceBreakPanel.tsx:172
#: src/pages/stock/StockDetail.tsx:255
#: src/pages/stock/StockDetail.tsx:907
#: src/tables/build/BuildLineTable.tsx:84
#: src/tables/build/BuildOrderTestTable.tsx:251
#: src/tables/part/PartPurchaseOrdersTable.tsx:94
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:168
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:199
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:69
#: src/tables/sales/ReturnOrderLineItemTable.tsx:121
#: src/tables/stock/StockTrackingTable.tsx:72
msgid "Quantity"
msgstr "Antall"

#: src/components/render/Stock.tsx:110
#: src/forms/BuildForms.tsx:312
#: src/forms/BuildForms.tsx:386
#: src/forms/BuildForms.tsx:450
#: src/forms/StockForms.tsx:734
#: src/forms/StockForms.tsx:780
#: src/forms/StockForms.tsx:826
#: src/forms/StockForms.tsx:865
#: src/forms/StockForms.tsx:901
#: src/forms/StockForms.tsx:939
#: src/forms/StockForms.tsx:981
#: src/forms/StockForms.tsx:1029
#: src/forms/StockForms.tsx:1073
#: src/tables/build/BuildLineTable.tsx:94
msgid "Batch"
msgstr ""

#: src/components/settings/ConfigValueList.tsx:33
#~ msgid "<0>{0}</0> is set via {1} and was last set {2}"
#~ msgstr "<0>{0}</0> is set via {1} and was last set {2}"

#: src/components/settings/ConfigValueList.tsx:36
msgid "Setting"
msgstr ""

#: src/components/settings/ConfigValueList.tsx:39
msgid "Source"
msgstr ""

#: src/components/settings/SettingItem.tsx:47
#: src/components/settings/SettingItem.tsx:100
#~ msgid "{0} updated successfully"
#~ msgstr "{0} updated successfully"

#: src/components/settings/SettingList.tsx:78
msgid "Edit Setting"
msgstr "Rediger innstilling"

#: src/components/settings/SettingList.tsx:91
msgid "Setting {key} updated successfully"
msgstr ""

#: src/components/settings/SettingList.tsx:120
msgid "Setting updated"
msgstr "Innstilling oppdatert"

#. placeholder {0}: setting.key
#: src/components/settings/SettingList.tsx:121
msgid "Setting {0} updated successfully"
msgstr ""

#: src/components/settings/SettingList.tsx:130
msgid "Error editing setting"
msgstr "Feil ved endring av innstilling"

#: src/components/settings/SettingList.tsx:146
msgid "Error loading settings"
msgstr ""

#: src/components/settings/SettingList.tsx:187
msgid "No settings specified"
msgstr ""

#: src/components/tables/FilterGroup.tsx:29
#~ msgid "Add table filter"
#~ msgstr "Add table filter"

#: src/components/tables/FilterGroup.tsx:44
#~ msgid "Clear all filters"
#~ msgstr "Clear all filters"

#: src/components/tables/FilterGroup.tsx:51
#~ msgid "Add filter"
#~ msgstr "Add filter"

#: src/components/tables/FilterSelectModal.tsx:143
#~ msgid "Add Table Filter"
#~ msgstr "Add Table Filter"

#: src/components/tables/FilterSelectModal.tsx:145
#~ msgid "Select from the available filters"
#~ msgstr "Select from the available filters"

#: src/components/tables/bom/BomTable.tsx:200
#~ msgid "Validate"
#~ msgstr "Validate"

#: src/components/tables/bom/BomTable.tsx:250
#~ msgid "Has Available Stock"
#~ msgstr "Has Available Stock"

#: src/components/tables/bom/UsedInTable.tsx:40
#~ msgid "Required Part"
#~ msgstr "Required Part"

#: src/components/tables/build/BuildOrderTable.tsx:52
#~ msgid "Progress"
#~ msgstr "Progress"

#: src/components/tables/build/BuildOrderTable.tsx:65
#~ msgid "Priority"
#~ msgstr "Priority"

#: src/components/tables/company/AddressTable.tsx:68
#~ msgid "Postal Code"
#~ msgstr "Postal Code"

#: src/components/tables/company/AddressTable.tsx:74
#~ msgid "City"
#~ msgstr "City"

#: src/components/tables/company/AddressTable.tsx:80
#~ msgid "State / Province"
#~ msgstr "State / Province"

#: src/components/tables/company/AddressTable.tsx:86
#~ msgid "Country"
#~ msgstr "Country"

#: src/components/tables/company/AddressTable.tsx:92
#~ msgid "Courier Notes"
#~ msgstr "Courier Notes"

#: src/components/tables/company/AddressTable.tsx:98
#~ msgid "Internal Notes"
#~ msgstr "Internal Notes"

#: src/components/tables/company/AddressTable.tsx:130
#~ msgid "Address updated"
#~ msgstr "Address updated"

#: src/components/tables/company/AddressTable.tsx:142
#~ msgid "Address deleted"
#~ msgstr "Address deleted"

#: src/components/tables/company/CompanyTable.tsx:32
#~ msgid "Company Name"
#~ msgstr "Company Name"

#: src/components/tables/company/ContactTable.tsx:41
#~ msgid "Phone"
#~ msgstr "Phone"

#: src/components/tables/company/ContactTable.tsx:78
#~ msgid "Contact updated"
#~ msgstr "Contact updated"

#: src/components/tables/company/ContactTable.tsx:90
#~ msgid "Contact deleted"
#~ msgstr "Contact deleted"

#: src/components/tables/company/ContactTable.tsx:92
#~ msgid "Are you sure you want to delete this contact?"
#~ msgstr "Are you sure you want to delete this contact?"

#: src/components/tables/company/ContactTable.tsx:108
#~ msgid "Create Contact"
#~ msgstr "Create Contact"

#: src/components/tables/company/ContactTable.tsx:110
#~ msgid "Contact created"
#~ msgstr "Contact created"

#: src/components/tables/general/AttachmentTable.tsx:47
#~ msgid "Comment"
#~ msgstr "Comment"

#: src/components/tables/part/PartCategoryTable.tsx:122
#~ msgid "Part category updated"
#~ msgstr "Part category updated"

#: src/components/tables/part/PartParameterTable.tsx:41
#~ msgid "Parameter"
#~ msgstr "Parameter"

#: src/components/tables/part/PartParameterTable.tsx:114
#~ msgid "Part parameter updated"
#~ msgstr "Part parameter updated"

#: src/components/tables/part/PartParameterTable.tsx:130
#~ msgid "Part parameter deleted"
#~ msgstr "Part parameter deleted"

#: src/components/tables/part/PartParameterTable.tsx:132
#~ msgid "Are you sure you want to remove this parameter?"
#~ msgstr "Are you sure you want to remove this parameter?"

#: src/components/tables/part/PartParameterTable.tsx:159
#~ msgid "Part parameter added"
#~ msgstr "Part parameter added"

#: src/components/tables/part/PartParameterTemplateTable.tsx:67
#~ msgid "Choices"
#~ msgstr "Choices"

#: src/components/tables/part/PartParameterTemplateTable.tsx:83
#~ msgid "Remove parameter template"
#~ msgstr "Remove parameter template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:84
#~ msgid "Parameter template updated"
#~ msgstr "Parameter template updated"

#: src/components/tables/part/PartParameterTemplateTable.tsx:96
#~ msgid "Parameter template deleted"
#~ msgstr "Parameter template deleted"

#: src/components/tables/part/PartParameterTemplateTable.tsx:98
#~ msgid "Are you sure you want to remove this parameter template?"
#~ msgstr "Are you sure you want to remove this parameter template?"

#: src/components/tables/part/PartParameterTemplateTable.tsx:110
#~ msgid "Create Parameter Template"
#~ msgstr "Create Parameter Template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:112
#~ msgid "Parameter template created"
#~ msgstr "Parameter template created"

#: src/components/tables/part/PartTable.tsx:211
#~ msgid "Detail"
#~ msgstr "Detail"

#: src/components/tables/part/PartTestTemplateTable.tsx:30
#~ msgid "Test Name"
#~ msgstr "Test Name"

#: src/components/tables/part/PartTestTemplateTable.tsx:86
#~ msgid "Template updated"
#~ msgstr "Template updated"

#: src/components/tables/part/PartTestTemplateTable.tsx:98
#~ msgid "Test Template deleted"
#~ msgstr "Test Template deleted"

#: src/components/tables/part/PartTestTemplateTable.tsx:115
#~ msgid "Create Test Template"
#~ msgstr "Create Test Template"

#: src/components/tables/part/PartTestTemplateTable.tsx:117
#~ msgid "Template created"
#~ msgstr "Template created"

#: src/components/tables/part/RelatedPartTable.tsx:79
#~ msgid "Related Part"
#~ msgstr "Related Part"

#: src/components/tables/part/RelatedPartTable.tsx:82
#~ msgid "Related part added"
#~ msgstr "Related part added"

#: src/components/tables/part/RelatedPartTable.tsx:114
#~ msgid "Related part deleted"
#~ msgstr "Related part deleted"

#: src/components/tables/part/RelatedPartTable.tsx:115
#~ msgid "Are you sure you want to remove this relationship?"
#~ msgstr "Are you sure you want to remove this relationship?"

#: src/components/tables/plugin/PluginListTable.tsx:191
#~ msgid "Installation path"
#~ msgstr "Installation path"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:55
#~ msgid "Receive"
#~ msgstr "Receive"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:81
#~ msgid "Line item updated"
#~ msgstr "Line item updated"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:232
#~ msgid "Line item added"
#~ msgstr "Line item added"

#: src/components/tables/settings/CustomUnitsTable.tsx:37
#~ msgid "Definition"
#~ msgstr "Definition"

#: src/components/tables/settings/CustomUnitsTable.tsx:43
#~ msgid "Symbol"
#~ msgstr "Symbol"

#: src/components/tables/settings/CustomUnitsTable.tsx:59
#~ msgid "Edit custom unit"
#~ msgstr "Edit custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:66
#~ msgid "Custom unit updated"
#~ msgstr "Custom unit updated"

#: src/components/tables/settings/CustomUnitsTable.tsx:76
#~ msgid "Delete custom unit"
#~ msgstr "Delete custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:77
#~ msgid "Custom unit deleted"
#~ msgstr "Custom unit deleted"

#: src/components/tables/settings/CustomUnitsTable.tsx:79
#~ msgid "Are you sure you want to remove this custom unit?"
#~ msgstr "Are you sure you want to remove this custom unit?"

#: src/components/tables/settings/CustomUnitsTable.tsx:97
#~ msgid "Custom unit created"
#~ msgstr "Custom unit created"

#: src/components/tables/settings/GroupTable.tsx:45
#~ msgid "Group updated"
#~ msgstr "Group updated"

#: src/components/tables/settings/GroupTable.tsx:131
#~ msgid "Added group"
#~ msgstr "Added group"

#: src/components/tables/settings/ProjectCodeTable.tsx:49
#~ msgid "Edit project code"
#~ msgstr "Edit project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:56
#~ msgid "Project code updated"
#~ msgstr "Project code updated"

#: src/components/tables/settings/ProjectCodeTable.tsx:66
#~ msgid "Delete project code"
#~ msgstr "Delete project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:67
#~ msgid "Project code deleted"
#~ msgstr "Project code deleted"

#: src/components/tables/settings/ProjectCodeTable.tsx:69
#~ msgid "Are you sure you want to remove this project code?"
#~ msgstr "Are you sure you want to remove this project code?"

#: src/components/tables/settings/ProjectCodeTable.tsx:88
#~ msgid "Added project code"
#~ msgstr "Added project code"

#: src/components/tables/settings/UserDrawer.tsx:92
#~ msgid "User permission changed successfully"
#~ msgstr "User permission changed successfully"

#: src/components/tables/settings/UserDrawer.tsx:93
#~ msgid "Some changes might only take effect after the user refreshes their login."
#~ msgstr "Some changes might only take effect after the user refreshes their login."

#: src/components/tables/settings/UserDrawer.tsx:118
#~ msgid "Changed user active status successfully"
#~ msgstr "Changed user active status successfully"

#: src/components/tables/settings/UserDrawer.tsx:119
#~ msgid "Set to {active}"
#~ msgstr "Set to {active}"

#: src/components/tables/settings/UserDrawer.tsx:142
#~ msgid "User details for {0}"
#~ msgstr "User details for {0}"

#: src/components/tables/settings/UserDrawer.tsx:176
#~ msgid "Rights"
#~ msgstr "Rights"

#: src/components/tables/settings/UserTable.tsx:117
#~ msgid "user deleted"
#~ msgstr "user deleted"

#: src/components/tables/stock/StockItemTable.tsx:247
#~ msgid "Test Filter"
#~ msgstr "Test Filter"

#: src/components/tables/stock/StockItemTable.tsx:248
#~ msgid "This is a test filter"
#~ msgstr "This is a test filter"

#: src/components/tables/stock/StockLocationTable.tsx:145
#~ msgid "Stock location updated"
#~ msgstr "Stock location updated"

#: src/components/widgets/FeedbackWidget.tsx:19
#~ msgid "Something is new: Platform UI"
#~ msgstr "Something is new: Platform UI"

#: src/components/widgets/FeedbackWidget.tsx:21
#~ msgid "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."
#~ msgstr "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."

#: src/components/widgets/FeedbackWidget.tsx:32
#~ msgid "Provide Feedback"
#~ msgstr "Provide Feedback"

#: src/components/widgets/GetStartedWidget.tsx:11
#~ msgid "Getting started"
#~ msgstr "Getting started"

#: src/components/widgets/MarkdownEditor.tsx:108
#~ msgid "Failed to upload image"
#~ msgstr "Failed to upload image"

#: src/components/widgets/MarkdownEditor.tsx:146
#~ msgid "Notes saved"
#~ msgstr "Notes saved"

#: src/components/widgets/WidgetLayout.tsx:166
#~ msgid "Layout"
#~ msgstr "Layout"

#: src/components/widgets/WidgetLayout.tsx:172
#~ msgid "Reset Layout"
#~ msgstr "Reset Layout"

#: src/components/widgets/WidgetLayout.tsx:185
#~ msgid "Stop Edit"
#~ msgstr "Stop Edit"

#: src/components/widgets/WidgetLayout.tsx:191
#~ msgid "Appearance"
#~ msgstr "Appearance"

#: src/components/widgets/WidgetLayout.tsx:203
#~ msgid "Show Boxes"
#~ msgstr "Show Boxes"

#: src/components/wizards/OrderPartsWizard.tsx:61
msgid "New Purchase Order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:63
msgid "Purchase order created"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:75
msgid "New Supplier Part"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:77
#: src/tables/purchasing/SupplierPartTable.tsx:161
msgid "Supplier part created"
msgstr "Leverandørdel opprettet"

#: src/components/wizards/OrderPartsWizard.tsx:103
msgid "Add to Purchase Order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:115
msgid "Part added to purchase order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:156
msgid "Select supplier part"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:171
msgid "New supplier part"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:195
msgid "Select purchase order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:209
msgid "New purchase order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:257
msgid "Add to selected purchase order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:269
#: src/components/wizards/OrderPartsWizard.tsx:382
msgid "No parts selected"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:270
msgid "No purchaseable parts selected"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:306
msgid "Parts Added"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:307
msgid "All selected parts added to a purchase order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:383
msgid "You must select at least one part to order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:394
msgid "Supplier part is required"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:398
msgid "Quantity is required"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:411
msgid "Invalid part selection"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:413
msgid "Please correct the errors in the selected parts"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:424
#: src/tables/build/BuildLineTable.tsx:794
#: src/tables/part/PartTable.tsx:407
#: src/tables/sales/SalesOrderLineItemTable.tsx:347
msgid "Order Parts"
msgstr ""

#: src/contexts/LanguageContext.tsx:22
msgid "Arabic"
msgstr ""

#: src/contexts/LanguageContext.tsx:23
msgid "Bulgarian"
msgstr "Bulgarsk"

#: src/contexts/LanguageContext.tsx:24
msgid "Czech"
msgstr "Tsjekkisk"

#: src/contexts/LanguageContext.tsx:25
msgid "Danish"
msgstr "Dansk"

#: src/contexts/LanguageContext.tsx:26
msgid "German"
msgstr "Tysk"

#: src/contexts/LanguageContext.tsx:27
msgid "Greek"
msgstr "Gresk"

#: src/contexts/LanguageContext.tsx:28
msgid "English"
msgstr "Engelsk"

#: src/contexts/LanguageContext.tsx:29
msgid "Spanish"
msgstr "Spansk"

#: src/contexts/LanguageContext.tsx:30
msgid "Spanish (Mexican)"
msgstr "Spansk (Meksikansk)"

#: src/contexts/LanguageContext.tsx:31
msgid "Estonian"
msgstr ""

#: src/contexts/LanguageContext.tsx:32
msgid "Farsi / Persian"
msgstr "Farsi / Persisk"

#: src/contexts/LanguageContext.tsx:33
msgid "Finnish"
msgstr "Finsk"

#: src/contexts/LanguageContext.tsx:34
msgid "French"
msgstr "Fransk"

#: src/contexts/LanguageContext.tsx:35
msgid "Hebrew"
msgstr "Hebraisk"

#: src/contexts/LanguageContext.tsx:36
msgid "Hindi"
msgstr "Hindi"

#: src/contexts/LanguageContext.tsx:37
msgid "Hungarian"
msgstr "Ungarsk"

#: src/contexts/LanguageContext.tsx:38
msgid "Italian"
msgstr "Italiensk"

#: src/contexts/LanguageContext.tsx:39
msgid "Japanese"
msgstr "Japansk"

#: src/contexts/LanguageContext.tsx:40
msgid "Korean"
msgstr "Koreansk"

#: src/contexts/LanguageContext.tsx:41
msgid "Lithuanian"
msgstr ""

#: src/contexts/LanguageContext.tsx:42
msgid "Latvian"
msgstr ""

#: src/contexts/LanguageContext.tsx:43
msgid "Dutch"
msgstr "Nederlandsk"

#: src/contexts/LanguageContext.tsx:44
msgid "Norwegian"
msgstr "Norsk"

#: src/contexts/LanguageContext.tsx:45
msgid "Polish"
msgstr "Polsk"

#: src/contexts/LanguageContext.tsx:46
msgid "Portuguese"
msgstr "Portugisisk"

#: src/contexts/LanguageContext.tsx:47
msgid "Portuguese (Brazilian)"
msgstr "Portugisisk (Brasil)"

#: src/contexts/LanguageContext.tsx:48
msgid "Romanian"
msgstr ""

#: src/contexts/LanguageContext.tsx:49
msgid "Russian"
msgstr "Russisk"

#: src/contexts/LanguageContext.tsx:50
msgid "Slovak"
msgstr ""

#: src/contexts/LanguageContext.tsx:51
msgid "Slovenian"
msgstr "Slovensk"

#: src/contexts/LanguageContext.tsx:52
msgid "Serbian"
msgstr ""

#: src/contexts/LanguageContext.tsx:53
msgid "Swedish"
msgstr "Svensk"

#: src/contexts/LanguageContext.tsx:54
msgid "Thai"
msgstr "Thailandsk"

#: src/contexts/LanguageContext.tsx:55
msgid "Turkish"
msgstr "Tyrkisk"

#: src/contexts/LanguageContext.tsx:56
msgid "Ukrainian"
msgstr ""

#: src/contexts/LanguageContext.tsx:57
msgid "Vietnamese"
msgstr "Vietnamesisk"

#: src/contexts/LanguageContext.tsx:58
msgid "Chinese (Simplified)"
msgstr "Kinesisk (forenklet)"

#: src/contexts/LanguageContext.tsx:59
msgid "Chinese (Traditional)"
msgstr "Kinesisk (tradisjonell)"

#: src/defaults/actions.tsx:18
#: src/defaults/links.tsx:27
#: src/defaults/menuItems.tsx:9
#~ msgid "Home"
#~ msgstr "Home"

#: src/defaults/actions.tsx:29
msgid "Go to the InvenTree dashboard"
msgstr ""

#: src/defaults/actions.tsx:36
msgid "Visit the documentation to learn more about InvenTree"
msgstr ""

#: src/defaults/actions.tsx:41
#: src/defaults/links.tsx:118
#~ msgid "About this Inventree instance"
#~ msgstr "About this Inventree instance"

#: src/defaults/actions.tsx:44
#: src/defaults/links.tsx:140
#: src/defaults/links.tsx:186
msgid "About InvenTree"
msgstr "Om InvenTree"

#: src/defaults/actions.tsx:45
msgid "About the InvenTree org"
msgstr "Om InvenTree-organisasjonen"

#: src/defaults/actions.tsx:51
msgid "Server Information"
msgstr ""

#: src/defaults/actions.tsx:52
#: src/defaults/links.tsx:169
msgid "About this InvenTree instance"
msgstr ""

#: src/defaults/actions.tsx:58
#: src/defaults/links.tsx:153
#: src/defaults/links.tsx:175
msgid "License Information"
msgstr ""

#: src/defaults/actions.tsx:59
msgid "Licenses for dependencies of the service"
msgstr ""

#: src/defaults/actions.tsx:65
msgid "Open Navigation"
msgstr "Åpne Navigasjon"

#: src/defaults/actions.tsx:66
msgid "Open the main navigation menu"
msgstr ""

#: src/defaults/actions.tsx:73
msgid "Scan a barcode or QR code"
msgstr ""

#: src/defaults/actions.tsx:84
msgid "Go to the Admin Center"
msgstr ""

#: src/defaults/dashboardItems.tsx:29
#~ msgid "Latest Parts"
#~ msgstr "Latest Parts"

#: src/defaults/dashboardItems.tsx:36
#~ msgid "BOM Waiting Validation"
#~ msgstr "BOM Waiting Validation"

#: src/defaults/dashboardItems.tsx:43
#~ msgid "Recently Updated"
#~ msgstr "Recently Updated"

#: src/defaults/dashboardItems.tsx:57
#~ msgid "Depleted Stock"
#~ msgstr "Depleted Stock"

#: src/defaults/dashboardItems.tsx:71
#~ msgid "Expired Stock"
#~ msgstr "Expired Stock"

#: src/defaults/dashboardItems.tsx:78
#~ msgid "Stale Stock"
#~ msgstr "Stale Stock"

#: src/defaults/dashboardItems.tsx:85
#~ msgid "Build Orders In Progress"
#~ msgstr "Build Orders In Progress"

#: src/defaults/dashboardItems.tsx:99
#~ msgid "Outstanding Purchase Orders"
#~ msgstr "Outstanding Purchase Orders"

#: src/defaults/dashboardItems.tsx:113
#~ msgid "Outstanding Sales Orders"
#~ msgstr "Outstanding Sales Orders"

#: src/defaults/dashboardItems.tsx:127
#~ msgid "Current News"
#~ msgstr "Current News"

#: src/defaults/defaultHostList.tsx:8
#~ msgid "InvenTree Demo"
#~ msgstr "InvenTree Demo"

#: src/defaults/defaultHostList.tsx:16
#~ msgid "Local Server"
#~ msgstr "Local Server"

#: src/defaults/links.tsx:17
#~ msgid "GitHub"
#~ msgstr "GitHub"

#: src/defaults/links.tsx:22
#~ msgid "Demo"
#~ msgstr "Demo"

#: src/defaults/links.tsx:41
#: src/defaults/menuItems.tsx:71
#: src/pages/Index/Playground.tsx:217
#~ msgid "Playground"
#~ msgstr "Playground"

#: src/defaults/links.tsx:76
#~ msgid "Instance"
#~ msgstr "Instance"

#: src/defaults/links.tsx:83
#~ msgid "InvenTree"
#~ msgstr "InvenTree"

#: src/defaults/links.tsx:93
msgid "API"
msgstr "API"

#: src/defaults/links.tsx:96
msgid "InvenTree API documentation"
msgstr "InvenTree-API-dokumentasjon"

#: src/defaults/links.tsx:100
msgid "Developer Manual"
msgstr "Utviklermanual"

#: src/defaults/links.tsx:103
msgid "InvenTree developer manual"
msgstr "InvenTree utviklermanual"

#: src/defaults/links.tsx:107
msgid "FAQ"
msgstr "FAQ"

#: src/defaults/links.tsx:110
msgid "Frequently asked questions"
msgstr "Ofte stilte spørsmål"

#: src/defaults/links.tsx:114
msgid "GitHub Repository"
msgstr ""

#: src/defaults/links.tsx:117
msgid "InvenTree source code on GitHub"
msgstr ""

#: src/defaults/links.tsx:117
#~ msgid "Licenses for packages used by InvenTree"
#~ msgstr "Licenses for packages used by InvenTree"

#: src/defaults/links.tsx:127
#: src/defaults/links.tsx:168
msgid "System Information"
msgstr "Systeminformasjon"

#: src/defaults/links.tsx:134
#~ msgid "Licenses"
#~ msgstr "Licenses"

#: src/defaults/links.tsx:176
msgid "Licenses for dependencies of the InvenTree software"
msgstr ""

#: src/defaults/links.tsx:187
msgid "About the InvenTree Project"
msgstr ""

#: src/defaults/menuItems.tsx:7
#~ msgid "Open sourcea"
#~ msgstr "Open sourcea"

#: src/defaults/menuItems.tsx:9
#~ msgid "Open source"
#~ msgstr "Open source"

#: src/defaults/menuItems.tsx:10
#~ msgid "Start page of your instance."
#~ msgstr "Start page of your instance."

#: src/defaults/menuItems.tsx:10
#~ msgid "This Pokémon’s cry is very loud and distracting"
#~ msgstr "This Pokémon’s cry is very loud and distracting"

#: src/defaults/menuItems.tsx:12
#~ msgid "This Pokémon’s cry is very loud and distracting and more and more and more"
#~ msgstr "This Pokémon’s cry is very loud and distracting and more and more and more"

#: src/defaults/menuItems.tsx:15
#~ msgid "Profile page"
#~ msgstr "Profile page"

#: src/defaults/menuItems.tsx:17
#~ msgid "User attributes and design settings."
#~ msgstr "User attributes and design settings."

#: src/defaults/menuItems.tsx:21
#~ msgid "Free for everyone"
#~ msgstr "Free for everyone"

#: src/defaults/menuItems.tsx:22
#~ msgid "The fluid of Smeargle’s tail secretions changes"
#~ msgstr "The fluid of Smeargle’s tail secretions changes"

#: src/defaults/menuItems.tsx:23
#~ msgid "View for interactive scanning and multiple actions."
#~ msgstr "View for interactive scanning and multiple actions."

#: src/defaults/menuItems.tsx:24
#~ msgid "The fluid of Smeargle’s tail secretions changes in the intensity"
#~ msgstr "The fluid of Smeargle’s tail secretions changes in the intensity"

#: src/defaults/menuItems.tsx:32
#~ msgid "abc"
#~ msgstr "abc"

#: src/defaults/menuItems.tsx:37
#~ msgid "Random image"
#~ msgstr "Random image"

#: src/defaults/menuItems.tsx:40
#~ msgid "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"
#~ msgstr "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"

#: src/defaults/menuItems.tsx:105
#~ msgid "Yanma is capable of seeing 360 degrees without"
#~ msgstr "Yanma is capable of seeing 360 degrees without"

#: src/defaults/menuItems.tsx:111
#~ msgid "The shell’s rounded shape and the grooves on its."
#~ msgstr "The shell’s rounded shape and the grooves on its."

#: src/defaults/menuItems.tsx:116
#~ msgid "Analytics"
#~ msgstr "Analytics"

#: src/defaults/menuItems.tsx:118
#~ msgid "This Pokémon uses its flying ability to quickly chase"
#~ msgstr "This Pokémon uses its flying ability to quickly chase"

#: src/defaults/menuItems.tsx:125
#~ msgid "Combusken battles with the intensely hot flames it spews"
#~ msgstr "Combusken battles with the intensely hot flames it spews"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add File"
#~ msgstr "Add File"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add Link"
#~ msgstr "Add Link"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "File added"
#~ msgstr "File added"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "Link added"
#~ msgstr "Link added"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit File"
#~ msgstr "Edit File"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit Link"
#~ msgstr "Edit Link"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "File updated"
#~ msgstr "File updated"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "Link updated"
#~ msgstr "Link updated"

#: src/forms/AttachmentForms.tsx:125
#~ msgid "Attachment deleted"
#~ msgstr "Attachment deleted"

#: src/forms/AttachmentForms.tsx:128
#~ msgid "Are you sure you want to delete this attachment?"
#~ msgstr "Are you sure you want to delete this attachment?"

#: src/forms/BomForms.tsx:109
msgid "Substitute Part"
msgstr ""

#: src/forms/BomForms.tsx:126
msgid "Edit BOM Substitutes"
msgstr ""

#: src/forms/BomForms.tsx:133
msgid "Add Substitute"
msgstr ""

#: src/forms/BomForms.tsx:134
msgid "Substitute added"
msgstr ""

#: src/forms/BuildForms.tsx:112
#: src/forms/BuildForms.tsx:217
#: src/forms/StockForms.tsx:197
msgid "Next batch code"
msgstr ""

#: src/forms/BuildForms.tsx:212
#: src/forms/StockForms.tsx:183
#: src/forms/StockForms.tsx:188
#: src/forms/StockForms.tsx:359
#: src/pages/stock/StockDetail.tsx:231
msgid "Next serial number"
msgstr ""

#: src/forms/BuildForms.tsx:248
#~ msgid "Remove output"
#~ msgstr "Remove output"

#: src/forms/BuildForms.tsx:311
#: src/forms/BuildForms.tsx:656
#: src/tables/build/BuildAllocatedStockTable.tsx:150
#: src/tables/build/BuildOrderTestTable.tsx:230
#: src/tables/build/BuildOrderTestTable.tsx:254
#: src/tables/build/BuildOutputTable.tsx:592
msgid "Build Output"
msgstr ""

#: src/forms/BuildForms.tsx:313
#: src/forms/BuildForms.tsx:387
#: src/forms/BuildForms.tsx:451
#: src/forms/PurchaseOrderForms.tsx:714
#: src/forms/ReturnOrderForms.tsx:194
#: src/forms/ReturnOrderForms.tsx:241
#: src/forms/StockForms.tsx:656
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:87
#: src/pages/build/BuildDetail.tsx:205
#: src/pages/core/UserDetail.tsx:151
#: src/pages/purchasing/PurchaseOrderDetail.tsx:150
#: src/pages/sales/ReturnOrderDetail.tsx:115
#: src/pages/sales/SalesOrderDetail.tsx:124
#: src/pages/stock/StockDetail.tsx:162
#: src/tables/Filter.tsx:266
#: src/tables/build/BuildOutputTable.tsx:408
#: src/tables/machine/MachineListTable.tsx:339
#: src/tables/part/PartPurchaseOrdersTable.tsx:38
#: src/tables/sales/ReturnOrderLineItemTable.tsx:135
#: src/tables/sales/ReturnOrderLineItemTable.tsx:172
#: src/tables/settings/CustomStateTable.tsx:79
#: src/tables/settings/EmailTable.tsx:73
#: src/tables/settings/ImportSessionTable.tsx:117
#: src/tables/stock/StockItemTable.tsx:328
#: src/tables/stock/StockTrackingTable.tsx:65
msgid "Status"
msgstr "Status"

#: src/forms/BuildForms.tsx:335
msgid "Complete Build Outputs"
msgstr ""

#: src/forms/BuildForms.tsx:338
msgid "Build outputs have been completed"
msgstr ""

#: src/forms/BuildForms.tsx:405
#: src/forms/BuildForms.tsx:407
msgid "Scrap Build Outputs"
msgstr ""

#: src/forms/BuildForms.tsx:408
#~ msgid "Selected build outputs will be deleted"
#~ msgstr "Selected build outputs will be deleted"

#: src/forms/BuildForms.tsx:410
msgid "Selected build outputs will be completed, but marked as scrapped"
msgstr ""

#: src/forms/BuildForms.tsx:412
msgid "Allocated stock items will be consumed"
msgstr ""

#: src/forms/BuildForms.tsx:418
msgid "Build outputs have been scrapped"
msgstr ""

#: src/forms/BuildForms.tsx:461
#: src/forms/BuildForms.tsx:463
msgid "Cancel Build Outputs"
msgstr ""

#: src/forms/BuildForms.tsx:465
msgid "Selected build outputs will be removed"
msgstr ""

#: src/forms/BuildForms.tsx:467
msgid "Allocated stock items will be returned to stock"
msgstr ""

#: src/forms/BuildForms.tsx:470
#~ msgid "Remove line"
#~ msgstr "Remove line"

#: src/forms/BuildForms.tsx:474
msgid "Build outputs have been cancelled"
msgstr ""

#: src/forms/BuildForms.tsx:603
#: src/forms/BuildForms.tsx:760
#: src/forms/BuildForms.tsx:861
#: src/forms/SalesOrderForms.tsx:268
#: src/tables/build/BuildAllocatedStockTable.tsx:139
#: src/tables/build/BuildLineTable.tsx:180
#: src/tables/sales/SalesOrderLineItemTable.tsx:319
#: src/tables/stock/StockItemTable.tsx:339
msgid "Allocated"
msgstr "Tildelt"

#: src/forms/BuildForms.tsx:638
#: src/forms/SalesOrderForms.tsx:257
#: src/pages/build/BuildDetail.tsx:320
msgid "Source Location"
msgstr ""

#: src/forms/BuildForms.tsx:639
#: src/forms/SalesOrderForms.tsx:258
msgid "Select the source location for the stock allocation"
msgstr ""

#: src/forms/BuildForms.tsx:671
#: src/forms/SalesOrderForms.tsx:298
#: src/tables/build/BuildLineTable.tsx:555
#: src/tables/build/BuildLineTable.tsx:710
#: src/tables/build/BuildLineTable.tsx:809
#: src/tables/sales/SalesOrderLineItemTable.tsx:357
#: src/tables/sales/SalesOrderLineItemTable.tsx:388
msgid "Allocate Stock"
msgstr "Tildel lagerbeholdning"

#: src/forms/BuildForms.tsx:674
#: src/forms/SalesOrderForms.tsx:303
msgid "Stock items allocated"
msgstr ""

#: src/forms/BuildForms.tsx:780
#: src/forms/BuildForms.tsx:881
#: src/tables/build/BuildAllocatedStockTable.tsx:233
#: src/tables/build/BuildAllocatedStockTable.tsx:265
#: src/tables/build/BuildLineTable.tsx:720
#: src/tables/build/BuildLineTable.tsx:843
msgid "Consume Stock"
msgstr ""

#: src/forms/BuildForms.tsx:781
#: src/forms/BuildForms.tsx:882
msgid "Stock items consumed"
msgstr ""

#: src/forms/BuildForms.tsx:817
#: src/tables/build/BuildLineTable.tsx:495
#: src/tables/part/PartBuildAllocationsTable.tsx:101
msgid "Fully consumed"
msgstr ""

#: src/forms/BuildForms.tsx:862
#: src/tables/build/BuildLineTable.tsx:185
#: src/tables/stock/StockItemTable.tsx:372
msgid "Consumed"
msgstr ""

#: src/forms/CompanyForms.tsx:150
#~ msgid "Company updated"
#~ msgstr "Company updated"

#: src/forms/PartForms.tsx:70
#: src/forms/PartForms.tsx:157
#: src/pages/part/CategoryDetail.tsx:122
#: src/pages/part/PartDetail.tsx:668
#: src/tables/part/PartCategoryTable.tsx:94
#: src/tables/part/PartTable.tsx:312
msgid "Subscribed"
msgstr ""

#: src/forms/PartForms.tsx:71
msgid "Subscribe to notifications for this part"
msgstr ""

#: src/forms/PartForms.tsx:106
#~ msgid "Create Part"
#~ msgstr "Create Part"

#: src/forms/PartForms.tsx:108
#~ msgid "Part created"
#~ msgstr "Part created"

#: src/forms/PartForms.tsx:129
#~ msgid "Part updated"
#~ msgstr "Part updated"

#: src/forms/PartForms.tsx:143
msgid "Parent part category"
msgstr "Overordnet del-kategori"

#: src/forms/PartForms.tsx:158
msgid "Subscribe to notifications for this category"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:385
msgid "Assign Batch Code and Serial Numbers"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:387
msgid "Assign Batch Code"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:407
msgid "Choose Location"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:415
msgid "Item Destination selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:421
#~ msgid "Assign Batch Code{0}"
#~ msgstr "Assign Batch Code{0}"

#: src/forms/PurchaseOrderForms.tsx:425
msgid "Part category default location selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:435
msgid "Received stock location selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:443
msgid "Default location selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:444
#: src/forms/StockForms.tsx:428
#~ msgid "Remove item from list"
#~ msgstr "Remove item from list"

#: src/forms/PurchaseOrderForms.tsx:504
msgid "Set Location"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:521
msgid "Set Expiry Date"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:529
#: src/forms/StockForms.tsx:637
msgid "Adjust Packaging"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:537
#: src/forms/StockForms.tsx:628
#: src/hooks/UseStockAdjustActions.tsx:148
msgid "Change Status"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:543
msgid "Add Note"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:566
#~ msgid "Serial numbers"
#~ msgstr "Serial numbers"

#: src/forms/PurchaseOrderForms.tsx:582
#~ msgid "Store at line item destination"
#~ msgstr "Store at line item destination"

#: src/forms/PurchaseOrderForms.tsx:607
msgid "Store at default location"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:622
msgid "Store at line item destination "
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:634
msgid "Store with already received stock"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:658
#: src/pages/build/BuildDetail.tsx:334
#: src/pages/stock/StockDetail.tsx:277
#: src/pages/stock/StockDetail.tsx:923
#: src/tables/Filter.tsx:83
#: src/tables/build/BuildAllocatedStockTable.tsx:128
#: src/tables/build/BuildOrderTestTable.tsx:242
#: src/tables/build/BuildOutputTable.tsx:116
#: src/tables/sales/SalesOrderAllocationTable.tsx:146
msgid "Batch Code"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:658
#~ msgid "Receive line items"
#~ msgstr "Receive line items"

#: src/forms/PurchaseOrderForms.tsx:659
msgid "Enter batch code for received items"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:672
#: src/forms/StockForms.tsx:176
msgid "Serial Numbers"
msgstr "Serienumre"

#: src/forms/PurchaseOrderForms.tsx:673
msgid "Enter serial numbers for received items"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:687
#: src/pages/stock/StockDetail.tsx:379
#: src/tables/stock/StockItemTable.tsx:295
msgid "Expiry Date"
msgstr "Utløpsdato"

#: src/forms/PurchaseOrderForms.tsx:688
msgid "Enter an expiry date for received items"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:700
#: src/forms/StockForms.tsx:672
#: src/pages/company/SupplierPartDetail.tsx:172
#: src/pages/company/SupplierPartDetail.tsx:236
#: src/pages/stock/StockDetail.tsx:416
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:220
msgid "Packaging"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:724
#: src/pages/company/SupplierPartDetail.tsx:119
#: src/tables/ColumnRenderers.tsx:323
msgid "Note"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:792
#: src/pages/company/SupplierPartDetail.tsx:137
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:49
msgid "SKU"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:793
#: src/tables/part/PartPurchaseOrdersTable.tsx:127
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:206
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:272
#: src/tables/sales/ReturnOrderLineItemTable.tsx:167
msgid "Received"
msgstr "Mottatt"

#: src/forms/PurchaseOrderForms.tsx:810
msgid "Receive Line Items"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:816
msgid "Items received"
msgstr ""

#: src/forms/ReturnOrderForms.tsx:254
msgid "Receive Items"
msgstr ""

#: src/forms/ReturnOrderForms.tsx:261
msgid "Item received into stock"
msgstr ""

#: src/forms/StockForms.tsx:110
#~ msgid "Create Stock Item"
#~ msgstr "Create Stock Item"

#: src/forms/StockForms.tsx:155
msgid "Add given quantity as packs instead of individual items"
msgstr "Legg til gitt mengde som pakker i stedet for enkeltprodukter"

#: src/forms/StockForms.tsx:158
#~ msgid "Stock item updated"
#~ msgstr "Stock item updated"

#: src/forms/StockForms.tsx:169
msgid "Enter initial quantity for this stock item"
msgstr "Angi innledende antall for denne lagervaren"

#: src/forms/StockForms.tsx:178
msgid "Enter serial numbers for new stock (or leave blank)"
msgstr "Angi serienumre for ny lagerbeholdning (eller la stå tom)"

#: src/forms/StockForms.tsx:200
msgid "Stock Status"
msgstr ""

#: src/forms/StockForms.tsx:260
#: src/pages/stock/StockDetail.tsx:670
#: src/tables/stock/StockItemTable.tsx:525
#: src/tables/stock/StockItemTable.tsx:572
msgid "Add Stock Item"
msgstr ""

#: src/forms/StockForms.tsx:304
msgid "Select the part to install"
msgstr ""

#: src/forms/StockForms.tsx:431
msgid "Confirm Stock Transfer"
msgstr ""

#: src/forms/StockForms.tsx:558
msgid "Loading..."
msgstr ""

#: src/forms/StockForms.tsx:616
msgid "Move to default location"
msgstr ""

#: src/forms/StockForms.tsx:736
msgid "Move"
msgstr ""

#: src/forms/StockForms.tsx:782
msgid "Return"
msgstr ""

#: src/forms/StockForms.tsx:827
#: src/forms/StockForms.tsx:866
#: src/forms/StockForms.tsx:902
#: src/forms/StockForms.tsx:940
#: src/forms/StockForms.tsx:982
#: src/forms/StockForms.tsx:1030
#: src/forms/StockForms.tsx:1074
#: src/pages/company/SupplierPartDetail.tsx:190
#: src/pages/company/SupplierPartDetail.tsx:374
#: src/pages/part/PartDetail.tsx:535
#: src/pages/part/PartDetail.tsx:970
#: src/tables/purchasing/SupplierPartTable.tsx:194
#: src/tables/stock/StockItemTable.tsx:359
msgid "In Stock"
msgstr "På lager"

#: src/forms/StockForms.tsx:903
#: src/pages/Index/Scan.tsx:182
msgid "Count"
msgstr "Tell"

#: src/forms/StockForms.tsx:1187
#: src/hooks/UseStockAdjustActions.tsx:108
msgid "Add Stock"
msgstr ""

#: src/forms/StockForms.tsx:1188
msgid "Stock added"
msgstr ""

#: src/forms/StockForms.tsx:1191
msgid "Increase the quantity of the selected stock items by a given amount."
msgstr ""

#: src/forms/StockForms.tsx:1202
#: src/hooks/UseStockAdjustActions.tsx:118
msgid "Remove Stock"
msgstr ""

#: src/forms/StockForms.tsx:1203
msgid "Stock removed"
msgstr ""

#: src/forms/StockForms.tsx:1206
msgid "Decrease the quantity of the selected stock items by a given amount."
msgstr ""

#: src/forms/StockForms.tsx:1217
#: src/hooks/UseStockAdjustActions.tsx:128
msgid "Transfer Stock"
msgstr "Overfør lager"

#: src/forms/StockForms.tsx:1218
msgid "Stock transferred"
msgstr ""

#: src/forms/StockForms.tsx:1221
msgid "Transfer selected items to the specified location."
msgstr ""

#: src/forms/StockForms.tsx:1232
#: src/hooks/UseStockAdjustActions.tsx:168
msgid "Return Stock"
msgstr ""

#: src/forms/StockForms.tsx:1233
msgid "Stock returned"
msgstr ""

#: src/forms/StockForms.tsx:1236
msgid "Return selected items into stock, to the specified location."
msgstr ""

#: src/forms/StockForms.tsx:1247
#: src/hooks/UseStockAdjustActions.tsx:98
msgid "Count Stock"
msgstr "Tell beholdning"

#: src/forms/StockForms.tsx:1248
msgid "Stock counted"
msgstr ""

#: src/forms/StockForms.tsx:1251
msgid "Count the selected stock items, and adjust the quantity accordingly."
msgstr ""

#: src/forms/StockForms.tsx:1262
msgid "Change Stock Status"
msgstr ""

#: src/forms/StockForms.tsx:1263
msgid "Stock status changed"
msgstr ""

#: src/forms/StockForms.tsx:1266
msgid "Change the status of the selected stock items."
msgstr ""

#: src/forms/StockForms.tsx:1277
#: src/hooks/UseStockAdjustActions.tsx:138
msgid "Merge Stock"
msgstr ""

#: src/forms/StockForms.tsx:1278
msgid "Stock merged"
msgstr ""

#: src/forms/StockForms.tsx:1280
msgid "Merge Stock Items"
msgstr ""

#: src/forms/StockForms.tsx:1282
msgid "Merge operation cannot be reversed"
msgstr ""

#: src/forms/StockForms.tsx:1283
msgid "Tracking information may be lost when merging items"
msgstr ""

#: src/forms/StockForms.tsx:1284
msgid "Supplier information may be lost when merging items"
msgstr ""

#: src/forms/StockForms.tsx:1302
msgid "Assign Stock to Customer"
msgstr ""

#: src/forms/StockForms.tsx:1303
msgid "Stock assigned to customer"
msgstr ""

#: src/forms/StockForms.tsx:1313
msgid "Delete Stock Items"
msgstr ""

#: src/forms/StockForms.tsx:1314
msgid "Stock deleted"
msgstr ""

#: src/forms/StockForms.tsx:1317
msgid "This operation will permanently delete the selected stock items."
msgstr ""

#: src/forms/StockForms.tsx:1326
msgid "Parent stock location"
msgstr ""

#: src/forms/StockForms.tsx:1453
msgid "Find Serial Number"
msgstr ""

#: src/forms/StockForms.tsx:1464
msgid "No matching items"
msgstr ""

#: src/forms/StockForms.tsx:1470
msgid "Multiple matching items"
msgstr ""

#: src/forms/StockForms.tsx:1479
msgid "Invalid response from server"
msgstr ""

#: src/forms/selectionListFields.tsx:97
msgid "Entries"
msgstr ""

#: src/forms/selectionListFields.tsx:98
msgid "List of entries to choose from"
msgstr ""

#: src/forms/selectionListFields.tsx:102
#: src/pages/part/PartStockHistoryDetail.tsx:59
#: src/tables/FilterSelectDrawer.tsx:114
#: src/tables/FilterSelectDrawer.tsx:137
#: src/tables/FilterSelectDrawer.tsx:149
#: src/tables/build/BuildOrderTestTable.tsx:188
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:201
msgid "Value"
msgstr "Verdi"

#: src/forms/selectionListFields.tsx:103
msgid "Label"
msgstr ""

#: src/functions/api.tsx:33
msgid "Bad request"
msgstr "Ugyldig forespørsel"

#: src/functions/api.tsx:36
msgid "Unauthorized"
msgstr "Uautorisert"

#: src/functions/api.tsx:39
msgid "Forbidden"
msgstr "Forbudt"

#: src/functions/api.tsx:42
msgid "Not found"
msgstr "Ikke funnet"

#: src/functions/api.tsx:45
msgid "Method not allowed"
msgstr ""

#: src/functions/api.tsx:48
msgid "Internal server error"
msgstr ""

#: src/functions/auth.tsx:34
#~ msgid "Error fetching token from server."
#~ msgstr "Error fetching token from server."

#: src/functions/auth.tsx:36
#~ msgid "Logout successfull"
#~ msgstr "Logout successfull"

#: src/functions/auth.tsx:60
#~ msgid "See you soon."
#~ msgstr "See you soon."

#: src/functions/auth.tsx:70
#~ msgid "Logout successful"
#~ msgstr "Logout successful"

#: src/functions/auth.tsx:71
#~ msgid "You have been logged out"
#~ msgstr "You have been logged out"

#: src/functions/auth.tsx:113
#: src/functions/auth.tsx:313
msgid "Already logged in"
msgstr ""

#: src/functions/auth.tsx:114
#: src/functions/auth.tsx:314
msgid "There is a conflicting session on the server for this browser. Please logout of that first."
msgstr ""

#: src/functions/auth.tsx:142
#~ msgid "Found an existing login - using it to log you in."
#~ msgstr "Found an existing login - using it to log you in."

#: src/functions/auth.tsx:143
#~ msgid "Found an existing login - welcome back!"
#~ msgstr "Found an existing login - welcome back!"

#: src/functions/auth.tsx:150
msgid "MFA Login successful"
msgstr ""

#: src/functions/auth.tsx:151
msgid "MFA details were automatically provided in the browser"
msgstr ""

#: src/functions/auth.tsx:179
msgid "Logged Out"
msgstr ""

#: src/functions/auth.tsx:180
msgid "Successfully logged out"
msgstr ""

#: src/functions/auth.tsx:218
msgid "Language changed"
msgstr ""

#: src/functions/auth.tsx:219
msgid "Your active language has been changed to the one set in your profile"
msgstr ""

#: src/functions/auth.tsx:239
msgid "Theme changed"
msgstr ""

#: src/functions/auth.tsx:240
msgid "Your active theme has been changed to the one set in your profile"
msgstr ""

#: src/functions/auth.tsx:274
msgid "Check your inbox for a reset link. This only works if you have an account. Check in spam too."
msgstr "Sjekk innboksen for en nullstillingslenke. Dette fungerer bare hvis du har en konto. Sjekk også i spam."

#: src/functions/auth.tsx:281
#: src/functions/auth.tsx:538
msgid "Reset failed"
msgstr "Tilbakestilling feilet"

#: src/functions/auth.tsx:370
msgid "Logged In"
msgstr ""

#: src/functions/auth.tsx:371
msgid "Successfully logged in"
msgstr ""

#: src/functions/auth.tsx:498
msgid "Failed to set up MFA"
msgstr ""

#: src/functions/auth.tsx:528
msgid "Password set"
msgstr "Passord angitt"

#: src/functions/auth.tsx:529
#: src/functions/auth.tsx:638
msgid "The password was set successfully. You can now login with your new password"
msgstr "Passordet er blitt satt. Du kan nå logge inn med ditt nye passord"

#: src/functions/auth.tsx:603
msgid "Password could not be changed"
msgstr ""

#: src/functions/auth.tsx:621
msgid "The two password fields didn’t match"
msgstr ""

#: src/functions/auth.tsx:637
msgid "Password Changed"
msgstr "Passord endret"

#: src/functions/forms.tsx:50
#~ msgid "Form method not provided"
#~ msgstr "Form method not provided"

#: src/functions/forms.tsx:59
#~ msgid "Response did not contain action data"
#~ msgstr "Response did not contain action data"

#: src/functions/forms.tsx:182
#~ msgid "Invalid Form"
#~ msgstr "Invalid Form"

#: src/functions/forms.tsx:183
#~ msgid "method parameter not supplied"
#~ msgstr "method parameter not supplied"

#: src/functions/notifications.tsx:13
msgid "Not implemented"
msgstr "Ikke implementert"

#: src/functions/notifications.tsx:14
msgid "This feature is not yet implemented"
msgstr "Denne funksjonen er ikke implementert ennå"

#: src/functions/notifications.tsx:24
#~ msgid "Permission denied"
#~ msgstr "Permission denied"

#: src/functions/notifications.tsx:26
msgid "You do not have permission to perform this action"
msgstr "Du har ikke rettigheter til å utføre denne handlingen"

#: src/functions/notifications.tsx:37
msgid "Invalid Return Code"
msgstr "Ugyldig returkode"

#: src/functions/notifications.tsx:38
msgid "Server returned status {returnCode}"
msgstr "Serveren returnerte status {returnCode}"

#: src/functions/notifications.tsx:48
msgid "Timeout"
msgstr ""

#: src/functions/notifications.tsx:49
msgid "The request timed out"
msgstr ""

#: src/hooks/UseDataExport.tsx:34
msgid "Exporting Data"
msgstr ""

#: src/hooks/UseDataExport.tsx:109
msgid "Export Data"
msgstr ""

#: src/hooks/UseDataExport.tsx:112
msgid "Export"
msgstr "Eksport"

#: src/hooks/UseDataOutput.tsx:57
#: src/hooks/UseDataOutput.tsx:111
msgid "Process failed"
msgstr ""

#: src/hooks/UseDataOutput.tsx:75
msgid "Process completed successfully"
msgstr ""

#: src/hooks/UseForm.tsx:92
msgid "Item Created"
msgstr ""

#: src/hooks/UseForm.tsx:112
msgid "Item Updated"
msgstr ""

#: src/hooks/UseForm.tsx:133
msgid "Items Updated"
msgstr ""

#: src/hooks/UseForm.tsx:135
msgid "Update multiple items"
msgstr ""

#: src/hooks/UseForm.tsx:165
msgid "Item Deleted"
msgstr ""

#: src/hooks/UseForm.tsx:169
msgid "Are you sure you want to delete this item?"
msgstr ""

#: src/hooks/UsePlaceholder.tsx:59
#~ msgid "Latest serial number"
#~ msgstr "Latest serial number"

#: src/hooks/UseStockAdjustActions.tsx:100
msgid "Count selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:110
msgid "Add to selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:120
msgid "Remove from selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:130
msgid "Transfer selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:140
msgid "Merge selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:150
msgid "Change status of selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:158
msgid "Assign Stock"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:160
msgid "Assign selected stock items to a customer"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:170
msgid "Return selected items into stock"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:178
msgid "Delete Stock"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:180
msgid "Delete selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:205
#: src/pages/part/PartDetail.tsx:1144
msgid "Stock Actions"
msgstr "Lagerhandlinger"

#: src/pages/Auth/ChangePassword.tsx:32
#: src/pages/Auth/Reset.tsx:14
msgid "Reset Password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:46
msgid "Current Password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:47
msgid "Enter your current password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:53
msgid "New Password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:54
msgid "Enter your new password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:60
msgid "Confirm New Password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:61
msgid "Confirm your new password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:80
msgid "Confirm"
msgstr ""

#: src/pages/Auth/Layout.tsx:59
msgid "Log off"
msgstr ""

#: src/pages/Auth/LoggedIn.tsx:19
msgid "Checking if you are already logged in"
msgstr "Sjekker om du allerede er innlogget"

#: src/pages/Auth/Login.tsx:32
msgid "No selection"
msgstr "Ingen utvalg"

#: src/pages/Auth/Login.tsx:91
#~ msgid "Welcome, log in below"
#~ msgstr "Welcome, log in below"

#: src/pages/Auth/Login.tsx:93
#~ msgid "Register below"
#~ msgstr "Register below"

#: src/pages/Auth/Login.tsx:100
msgid "Login"
msgstr "Innlogging"

#: src/pages/Auth/Login.tsx:106
msgid "Logging you in"
msgstr ""

#: src/pages/Auth/Login.tsx:113
msgid "Don't have an account?"
msgstr ""

#: src/pages/Auth/Logout.tsx:22
#~ msgid "Logging out"
#~ msgstr "Logging out"

#: src/pages/Auth/MFA.tsx:16
#~ msgid "Multi-Factor Login"
#~ msgstr "Multi-Factor Login"

#: src/pages/Auth/MFA.tsx:17
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:79
msgid "Multi-Factor Authentication"
msgstr ""

#: src/pages/Auth/MFA.tsx:20
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:693
msgid "TOTP Code"
msgstr ""

#: src/pages/Auth/MFA.tsx:22
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:695
msgid "Enter your TOTP or recovery code"
msgstr ""

#: src/pages/Auth/MFA.tsx:27
msgid "Remember this device"
msgstr ""

#: src/pages/Auth/MFA.tsx:29
msgid "If enabled, you will not be asked for MFA on this device for 30 days."
msgstr ""

#: src/pages/Auth/MFA.tsx:38
msgid "Log in"
msgstr ""

#: src/pages/Auth/MFASetup.tsx:23
msgid "MFA Setup Required"
msgstr ""

#: src/pages/Auth/MFASetup.tsx:34
msgid "Add TOTP"
msgstr ""

#: src/pages/Auth/Register.tsx:23
msgid "Go back to login"
msgstr ""

#: src/pages/Auth/Reset.tsx:41
#: src/pages/Auth/Set-Password.tsx:112
#~ msgid "Send mail"
#~ msgstr "Send mail"

#: src/pages/Auth/ResetPassword.tsx:22
#: src/pages/Auth/VerifyEmail.tsx:19
msgid "Key invalid"
msgstr ""

#: src/pages/Auth/ResetPassword.tsx:23
msgid "You need to provide a valid key to set a new password. Check your inbox for a reset link."
msgstr ""

#: src/pages/Auth/ResetPassword.tsx:30
#~ msgid "Token invalid"
#~ msgstr "Token invalid"

#: src/pages/Auth/ResetPassword.tsx:31
msgid "Set new password"
msgstr "Angi nytt passord"

#: src/pages/Auth/ResetPassword.tsx:31
#~ msgid "You need to provide a valid token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a valid token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/ResetPassword.tsx:35
msgid "The desired new password"
msgstr ""

#: src/pages/Auth/ResetPassword.tsx:44
msgid "Send Password"
msgstr ""

#: src/pages/Auth/Set-Password.tsx:49
#~ msgid "No token provided"
#~ msgstr "No token provided"

#: src/pages/Auth/Set-Password.tsx:50
#~ msgid "You need to provide a token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/VerifyEmail.tsx:20
msgid "You need to provide a valid key."
msgstr ""

#: src/pages/Auth/VerifyEmail.tsx:28
msgid "Verify Email"
msgstr ""

#: src/pages/Auth/VerifyEmail.tsx:30
msgid "Verify"
msgstr ""

#. placeholder {0}: error.statusText
#: src/pages/ErrorPage.tsx:16
msgid "Error: {0}"
msgstr "Feil: {0}"

#: src/pages/ErrorPage.tsx:23
msgid "An unexpected error has occurred"
msgstr ""

#: src/pages/ErrorPage.tsx:28
#~ msgid "Sorry, an unexpected error has occurred."
#~ msgstr "Sorry, an unexpected error has occurred."

#: src/pages/Index/Dashboard.tsx:22
#~ msgid "Autoupdate"
#~ msgstr "Autoupdate"

#: src/pages/Index/Dashboard.tsx:26
#~ msgid "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."
#~ msgstr "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."

#: src/pages/Index/Home.tsx:58
#~ msgid "Welcome to your Dashboard{0}"
#~ msgstr "Welcome to your Dashboard{0}"

#: src/pages/Index/Playground.tsx:222
#~ msgid "This page is a showcase for the possibilities of Platform UI."
#~ msgstr "This page is a showcase for the possibilities of Platform UI."

#: src/pages/Index/Profile/Profile.tsx:30
#: src/pages/Index/Profile/Profile.tsx:141
#~ msgid "Notification Settings"
#~ msgstr "Notification Settings"

#: src/pages/Index/Profile/Profile.tsx:33
#~ msgid "Global Settings"
#~ msgstr "Global Settings"

#: src/pages/Index/Profile/Profile.tsx:47
#~ msgid "Settings for the current user"
#~ msgstr "Settings for the current user"

#: src/pages/Index/Profile/Profile.tsx:51
#~ msgid "Home Page Settings"
#~ msgstr "Home Page Settings"

#: src/pages/Index/Profile/Profile.tsx:76
#~ msgid "Search Settings"
#~ msgstr "Search Settings"

#: src/pages/Index/Profile/Profile.tsx:115
#: src/pages/Index/Profile/Profile.tsx:211
#~ msgid "Label Settings"
#~ msgstr "Label Settings"

#: src/pages/Index/Profile/Profile.tsx:120
#: src/pages/Index/Profile/Profile.tsx:219
#~ msgid "Report Settings"
#~ msgstr "Report Settings"

#: src/pages/Index/Profile/Profile.tsx:142
#~ msgid "Settings for the notifications"
#~ msgstr "Settings for the notifications"

#: src/pages/Index/Profile/Profile.tsx:148
#~ msgid "Global Server Settings"
#~ msgstr "Global Server Settings"

#: src/pages/Index/Profile/Profile.tsx:149
#~ msgid "Global Settings for this instance"
#~ msgstr "Global Settings for this instance"

#: src/pages/Index/Profile/Profile.tsx:153
#~ msgid "Server Settings"
#~ msgstr "Server Settings"

#: src/pages/Index/Profile/Profile.tsx:187
#~ msgid "Login Settings"
#~ msgstr "Login Settings"

#: src/pages/Index/Profile/Profile.tsx:202
#~ msgid "Barcode Settings"
#~ msgstr "Barcode Settings"

#: src/pages/Index/Profile/Profile.tsx:230
#~ msgid "Part Settings"
#~ msgstr "Part Settings"

#: src/pages/Index/Profile/Profile.tsx:255
#~ msgid "Pricing Settings"
#~ msgstr "Pricing Settings"

#: src/pages/Index/Profile/Profile.tsx:270
#~ msgid "Stock Settings"
#~ msgstr "Stock Settings"

#: src/pages/Index/Profile/Profile.tsx:284
#~ msgid "Build Order Settings"
#~ msgstr "Build Order Settings"

#: src/pages/Index/Profile/Profile.tsx:289
#~ msgid "Purchase Order Settings"
#~ msgstr "Purchase Order Settings"

#: src/pages/Index/Profile/Profile.tsx:300
#~ msgid "Sales Order Settings"
#~ msgstr "Sales Order Settings"

#: src/pages/Index/Profile/Profile.tsx:330
#~ msgid "Plugin Settings for this instance"
#~ msgstr "Plugin Settings for this instance"

#: src/pages/Index/Profile/SettingsPanel.tsx:27
#~ msgid "Data is current beeing loaded"
#~ msgstr "Data is current beeing loaded"

#: src/pages/Index/Profile/SettingsPanel.tsx:69
#: src/pages/Index/Profile/SettingsPanel.tsx:76
#~ msgid "Failed to load"
#~ msgstr "Failed to load"

#: src/pages/Index/Profile/SettingsPanel.tsx:100
#~ msgid "Show internal names"
#~ msgstr "Show internal names"

#: src/pages/Index/Profile/SettingsPanel.tsx:148
#~ msgid "Input {0} is not known"
#~ msgstr "Input {0} is not known"

#: src/pages/Index/Profile/SettingsPanel.tsx:161
#~ msgid "Saved changes {0}"
#~ msgstr "Saved changes {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:162
#~ msgid "Changed to {0}"
#~ msgstr "Changed to {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:176
#~ msgid "Error while saving {0}"
#~ msgstr "Error while saving {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:177
#~ msgid "Error was {err}"
#~ msgstr "Error was {err}"

#: src/pages/Index/Profile/SettingsPanel.tsx:257
#~ msgid "Plugin: {0}"
#~ msgstr "Plugin: {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:262
#~ msgid "Method: {0}"
#~ msgstr "Method: {0}"

#: src/pages/Index/Profile/UserPanel.tsx:85
#~ msgid "Userinfo"
#~ msgstr "Userinfo"

#: src/pages/Index/Profile/UserPanel.tsx:122
#~ msgid "Username: {0}"
#~ msgstr "Username: {0}"

#: src/pages/Index/Profile/UserTheme.tsx:83
#~ msgid "Design <0/>"
#~ msgstr "Design <0/>"

#: src/pages/Index/Scan.tsx:65
msgid "Item already scanned"
msgstr ""

#: src/pages/Index/Scan.tsx:82
msgid "API Error"
msgstr ""

#: src/pages/Index/Scan.tsx:83
msgid "Failed to fetch instance data"
msgstr ""

#: src/pages/Index/Scan.tsx:130
msgid "Scan Error"
msgstr ""

#: src/pages/Index/Scan.tsx:162
msgid "Selected elements are not known"
msgstr "Valgte elementer er ikke kjent"

#: src/pages/Index/Scan.tsx:169
msgid "Multiple object types selected"
msgstr "Flere objekttyper er valgt"

#: src/pages/Index/Scan.tsx:175
#~ msgid "Actions ..."
#~ msgstr "Actions ..."

#: src/pages/Index/Scan.tsx:177
msgid "Actions ... "
msgstr ""

#: src/pages/Index/Scan.tsx:194
#: src/pages/Index/Scan.tsx:198
msgid "Barcode Scanning"
msgstr ""

#: src/pages/Index/Scan.tsx:207
msgid "Barcode Input"
msgstr ""

#: src/pages/Index/Scan.tsx:214
msgid "Action"
msgstr "Handling"

#: src/pages/Index/Scan.tsx:217
msgid "No Items Selected"
msgstr ""

#: src/pages/Index/Scan.tsx:217
#~ msgid "Manual input"
#~ msgstr "Manual input"

#: src/pages/Index/Scan.tsx:218
msgid "Scan and select items to perform actions"
msgstr ""

#: src/pages/Index/Scan.tsx:218
#~ msgid "Image Barcode"
#~ msgstr "Image Barcode"

#. placeholder {0}: selection.length
#: src/pages/Index/Scan.tsx:223
msgid "{0} items selected"
msgstr "{0} elementer valgt"

#: src/pages/Index/Scan.tsx:235
msgid "Scanned Items"
msgstr ""

#: src/pages/Index/Scan.tsx:276
#~ msgid "Actions for {0}"
#~ msgstr "Actions for {0}"

#: src/pages/Index/Scan.tsx:298
#~ msgid "Scan Page"
#~ msgstr "Scan Page"

#: src/pages/Index/Scan.tsx:301
#~ msgid "This page can be used for continuously scanning items and taking actions on them."
#~ msgstr "This page can be used for continuously scanning items and taking actions on them."

#: src/pages/Index/Scan.tsx:308
#~ msgid "Toggle Fullscreen"
#~ msgstr "Toggle Fullscreen"

#: src/pages/Index/Scan.tsx:321
#~ msgid "Select the input method you want to use to scan items."
#~ msgstr "Select the input method you want to use to scan items."

#: src/pages/Index/Scan.tsx:323
#~ msgid "Input"
#~ msgstr "Input"

#: src/pages/Index/Scan.tsx:330
#~ msgid "Select input method"
#~ msgstr "Select input method"

#: src/pages/Index/Scan.tsx:331
#~ msgid "Nothing found"
#~ msgstr "Nothing found"

#: src/pages/Index/Scan.tsx:339
#~ msgid "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."
#~ msgstr "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."

#: src/pages/Index/Scan.tsx:353
#~ msgid "General Actions"
#~ msgstr "General Actions"

#: src/pages/Index/Scan.tsx:367
#~ msgid "Lookup part"
#~ msgstr "Lookup part"

#: src/pages/Index/Scan.tsx:375
#~ msgid "Open Link"
#~ msgstr "Open Link"

#: src/pages/Index/Scan.tsx:391
#~ msgid "History is locally kept in this browser."
#~ msgstr "History is locally kept in this browser."

#: src/pages/Index/Scan.tsx:392
#~ msgid "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."
#~ msgstr "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."

#: src/pages/Index/Scan.tsx:400
#~ msgid "Delete History"
#~ msgstr "Delete History"

#: src/pages/Index/Scan.tsx:465
#~ msgid "No history"
#~ msgstr "No history"

#: src/pages/Index/Scan.tsx:492
#~ msgid "Scanned at"
#~ msgstr "Scanned at"

#: src/pages/Index/Scan.tsx:549
#~ msgid "Enter item serial or data"
#~ msgstr "Enter item serial or data"

#: src/pages/Index/Scan.tsx:561
#~ msgid "Add dummy item"
#~ msgstr "Add dummy item"

#: src/pages/Index/Scan.tsx:652
#~ msgid "Error while getting camera"
#~ msgstr "Error while getting camera"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Scanning"
#~ msgstr "Scanning"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Not scanning"
#~ msgstr "Not scanning"

#: src/pages/Index/Scan.tsx:777
#~ msgid "Select Camera"
#~ msgstr "Select Camera"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:30
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:52
#~ msgid "Edit User Information"
#~ msgstr "Edit User Information"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:33
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:113
msgid "Edit Account Information"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:34
#~ msgid "User details updated"
#~ msgstr "User details updated"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:37
msgid "Account details updated"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:46
#~ msgid "User Actions"
#~ msgstr "User Actions"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:50
#~ msgid "First name"
#~ msgstr "First name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:136
msgid "Edit Profile Information"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#~ msgid "Last name"
#~ msgstr "Last name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:56
#~ msgid "Set User Password"
#~ msgstr "Set User Password"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:58
#~ msgid "First name: {0}"
#~ msgstr "First name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:59
msgid "Profile details updated"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:61
#~ msgid "Last name: {0}"
#~ msgstr "Last name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:65
#: src/pages/core/UserDetail.tsx:55
msgid "First Name"
msgstr "Fornavn"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:66
#: src/pages/core/UserDetail.tsx:63
msgid "Last Name"
msgstr "Etternavn"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:67
#~ msgid "First name:"
#~ msgstr "First name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:71
#~ msgid "Last name:"
#~ msgstr "Last name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:72
msgid "Staff Access"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:85
#: src/pages/core/UserDetail.tsx:119
#: src/tables/settings/CustomStateTable.tsx:101
msgid "Display Name"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:86
#: src/pages/core/UserDetail.tsx:127
msgid "Position"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:90
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:447
msgid "Type"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:91
#: src/pages/core/UserDetail.tsx:143
msgid "Organisation"
msgstr "Organisasjon"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:92
msgid "Primary Group"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:104
msgid "Account Details"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:107
msgid "Account Actions"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:111
msgid "Edit Account"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:117
#: src/tables/settings/UserTable.tsx:322
msgid "Change Password"
msgstr "Endre passord"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:119
msgid "Change User Password"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:131
msgid "Profile Details"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:134
msgid "Edit Profile"
msgstr "Endre Profil"

#. placeholder {0}: item.label
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:153
msgid "{0}"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:24
msgid "Secret"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:30
msgid "One-Time Password"
msgstr "Engangspassord"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:31
msgid "Enter the TOTP code to ensure it registered correctly"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:53
msgid "Email Addresses"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:55
#~ msgid "Single Sign On Accounts"
#~ msgstr "Single Sign On Accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:61
msgid "Single Sign On"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
msgid "Not enabled"
msgstr "Ikke aktivert"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
#~ msgid "Multifactor"
#~ msgstr "Multifactor"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:71
#~ msgid "Single Sign On is not enabled for this server"
#~ msgstr "Single Sign On is not enabled for this server"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:72
msgid "Single Sign On is not enabled for this server "
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:83
#~ msgid "Multifactor authentication is not configured for your account"
#~ msgstr "Multifactor authentication is not configured for your account"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:87
msgid "Access Tokens"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:125
msgid "Error while updating email"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:139
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:297
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:435
msgid "Not Configured"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:142
msgid "Currently no email addresses are registered."
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:150
msgid "The following email addresses are associated with your account:"
msgstr "Følgende e-postadresser er tilknyttet din konto:"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:163
msgid "Primary"
msgstr "Primær"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:168
msgid "Verified"
msgstr "Bekreftet"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:172
msgid "Unverified"
msgstr "Ubekreftet"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:190
msgid "Make Primary"
msgstr "Gjør til primær"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:196
msgid "Re-send Verification"
msgstr "Re-send bekreftelse"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:210
msgid "Add Email Address"
msgstr "Legg til e-postadresse"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:212
msgid "E-Mail"
msgstr "E-post"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:213
msgid "E-Mail address"
msgstr "E-postadresse"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:225
msgid "Error while adding email"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:236
msgid "Add Email"
msgstr "Legg til e-post"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:270
#~ msgid "Provider has not been configured"
#~ msgstr "Provider has not been configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:280
#~ msgid "Not configured"
#~ msgstr "Not configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:283
#~ msgid "There are no social network accounts connected to this account."
#~ msgstr "There are no social network accounts connected to this account."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:293
#~ msgid "You can sign in to your account using any of the following third party accounts"
#~ msgstr "You can sign in to your account using any of the following third party accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:300
msgid "There are no providers connected to this account."
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:309
msgid "You can sign in to your account using any of the following providers"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:322
msgid "Remove Provider Link"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:439
msgid "No multi-factor tokens configured for this account"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:450
msgid "Last used at"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:453
msgid "Created at"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:474
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:579
msgid "Recovery Codes"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:478
msgid "Unused Codes"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:483
msgid "Used Codes"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:538
msgid "Error while registering recovery codes"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:572
msgid "TOTP"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:573
msgid "Time-based One-Time Password"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:580
msgid "One-Time pre-generated recovery codes"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:594
msgid "Add Token"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:609
msgid "Register TOTP Token"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:634
msgid "Error registering TOTP token"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:710
msgid "Enter your password"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:741
#~ msgid "Token is used - no actions"
#~ msgstr "Token is used - no actions"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:761
#~ msgid "No tokens configured"
#~ msgstr "No tokens configured"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:61
msgid "Display Settings"
msgstr "Visningsinnstillinger"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:65
#~ msgid "bars"
#~ msgstr "bars"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:66
#~ msgid "oval"
#~ msgstr "oval"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
msgid "Language"
msgstr "Språk"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
#~ msgid "dots"
#~ msgstr "dots"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:78
msgid "Use pseudo language"
msgstr "Bruk pseudo-språk"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:81
#~ msgid "Theme"
#~ msgstr "Theme"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:85
msgid "Color Mode"
msgstr "Fargemodus"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:87
#~ msgid "Primary color"
#~ msgstr "Primary color"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:96
msgid "Highlight color"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:110
msgid "Example"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:116
msgid "White color"
msgstr "Hvit farge"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:139
msgid "Black color"
msgstr "Svart farge"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:162
msgid "Border Radius"
msgstr "Kantradius"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:178
msgid "Loader"
msgstr "Laster"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:185
msgid "Bars"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:186
msgid "Oval"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:187
msgid "Dots"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:93
msgid "Reauthentication"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:109
msgid "OK"
msgstr ""

#: src/pages/Index/Settings/AdminCenter.tsx:91
#~ msgid "Advanced Amininistrative Options for InvenTree"
#~ msgstr "Advanced Amininistrative Options for InvenTree"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:28
#: src/tables/ColumnRenderers.tsx:476
msgid "Currency"
msgstr "Valuta"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:33
msgid "Rate"
msgstr "Kurs"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:46
msgid "Exchange rates updated"
msgstr "Valutakurser oppdatert"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:53
msgid "Exchange rate update error"
msgstr "Feil udner oppdatering av valutakurs"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:63
msgid "Refresh currency exchange rates"
msgstr "Oppdater valutakursene"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:99
msgid "Last fetched"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:100
msgid "Base currency"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:13
msgid "Email Messages"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:107
#~ msgid "User Management"
#~ msgstr "User Management"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:112
msgid "Users / Access"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:126
msgid "Data Import"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:127
#~ msgid "Templates"
#~ msgstr "Templates"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:132
msgid "Data Export"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:138
msgid "Barcode Scans"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:144
msgid "Background Tasks"
msgstr "Bakgrunnsoppgaver"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:150
msgid "Error Reports"
msgstr "Feilrapporter"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:156
msgid "Currencies"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:170
#~ msgid "Location types"
#~ msgstr "Location types"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:173
msgid "Custom States"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:179
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:57
msgid "Custom Units"
msgstr "Egendefinerte enheter"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:185
#: src/pages/part/CategoryDetail.tsx:302
msgid "Part Parameters"
msgstr "Delparametere"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:192
msgid "Category Parameters"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:211
msgid "Location Types"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:221
#~ msgid "Quick Actions"
#~ msgstr "Quick Actions"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:225
#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:48
#: src/tables/machine/MachineTypeTable.tsx:307
msgid "Machines"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:226
#~ msgid "Add a new user"
#~ msgstr "Add a new user"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:236
msgid "Operations"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:248
msgid "Data Management"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:259
#: src/pages/Index/Settings/SystemSettings.tsx:175
#: src/pages/Index/Settings/UserSettings.tsx:118
msgid "Reporting"
msgstr "Rapportering"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:264
msgid "PLM"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:274
msgid "Extend / Integrate"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:288
msgid "Advanced Options"
msgstr "Avanserte Innstillinger"

#: src/pages/Index/Settings/AdminCenter/LabelTemplatePanel.tsx:40
#~ msgid "Generated Labels"
#~ msgstr "Generated Labels"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:43
#~ msgid "Machine types"
#~ msgstr "Machine types"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:53
#~ msgid "Machine Error Stack"
#~ msgstr "Machine Error Stack"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:56
msgid "Machine Types"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:62
#~ msgid "There are no machine registry errors."
#~ msgstr "There are no machine registry errors."

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:64
msgid "Machine Errors"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:77
msgid "Registry Registry Errors"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:80
msgid "There are machine registry errors"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:86
msgid "Machine Registry Errors"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:89
msgid "There are no machine registry errors"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#: src/tables/settings/UserTable.tsx:195
msgid "Info"
msgstr "Info"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#~ msgid "Plugin Error Stack"
#~ msgstr "Plugin Error Stack"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:37
msgid "External plugins are not enabled for this InvenTree installation."
msgstr "Eksterne utvidelser er ikke aktivert for denne InvenTree-installasjonen."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:45
#~ msgid "Warning"
#~ msgstr "Warning"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:47
#~ msgid "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."
#~ msgstr "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:76
msgid "Plugin Errors"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:16
msgid "Page Size"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:19
msgid "Landscape"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:25
msgid "Merge"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:31
msgid "Attach to Model"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:55
#~ msgid "Generated Reports"
#~ msgstr "Generated Reports"

#: src/pages/Index/Settings/AdminCenter/StocktakePanel.tsx:25
#~ msgid "Stocktake Reports"
#~ msgstr "Stocktake Reports"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:30
msgid "Background worker not running"
msgstr "Bakgrunnsarbeider kjører ikke"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:31
msgid "The background task manager service is not running. Contact your system administrator."
msgstr ""

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:35
#~ msgid "Background Worker Not Running"
#~ msgstr "Background Worker Not Running"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:38
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:47
msgid "Pending Tasks"
msgstr "Ventende oppgaver"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:39
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:55
msgid "Scheduled Tasks"
msgstr "Planlagte oppgaver"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:40
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:63
msgid "Failed Tasks"
msgstr "Mislykkede oppgaver"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:67
#~ msgid "Stock item"
#~ msgstr "Stock item"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:76
#~ msgid "Build line"
#~ msgstr "Build line"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:88
#~ msgid "Reports"
#~ msgstr "Reports"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:99
#~ msgid "Purchase order"
#~ msgstr "Purchase order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:108
#~ msgid "Sales order"
#~ msgstr "Sales order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:117
#~ msgid "Return order"
#~ msgstr "Return order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:145
#~ msgid "Tests"
#~ msgstr "Tests"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:154
#~ msgid "Stock location"
#~ msgstr "Stock location"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:21
msgid "Alias"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:22
msgid "Dimensionless"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:65
msgid "All units"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:31
msgid "Tokens"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:32
#~ msgid "Select settings relevant for user lifecycle. More available in"
#~ msgstr "Select settings relevant for user lifecycle. More available in"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:37
#~ msgid "System settings"
#~ msgstr "System settings"

#: src/pages/Index/Settings/PluginSettingsGroup.tsx:99
msgid "The settings below are specific to each available plugin"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:77
msgid "Authentication"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:103
msgid "Barcodes"
msgstr "Strekkoder"

#: src/pages/Index/Settings/SystemSettings.tsx:118
#~ msgid "Physical Units"
#~ msgstr "Physical Units"

#: src/pages/Index/Settings/SystemSettings.tsx:119
#~ msgid "This panel is a placeholder."
#~ msgstr "This panel is a placeholder."

#: src/pages/Index/Settings/SystemSettings.tsx:127
#: src/pages/Index/Settings/UserSettings.tsx:112
msgid "The settings below are specific to each available notification method"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:133
msgid "Pricing"
msgstr "Prising"

#: src/pages/Index/Settings/SystemSettings.tsx:135
#~ msgid "Exchange Rates"
#~ msgstr "Exchange Rates"

#: src/pages/Index/Settings/SystemSettings.tsx:169
msgid "Labels"
msgstr "Etiketter"

#: src/pages/Index/Settings/SystemSettings.tsx:317
#~ msgid "Switch to User Setting"
#~ msgstr "Switch to User Setting"

#: src/pages/Index/Settings/UserSettings.tsx:41
msgid "Account"
msgstr "Konto"

#: src/pages/Index/Settings/UserSettings.tsx:47
msgid "Security"
msgstr "Sikkerhet"

#: src/pages/Index/Settings/UserSettings.tsx:53
msgid "Display Options"
msgstr "Visningsvalg"

#: src/pages/Index/Settings/UserSettings.tsx:159
#~ msgid "Switch to System Setting"
#~ msgstr "Switch to System Setting"

#: src/pages/Logged-In.tsx:24
#~ msgid "Found an exsisting login - using it to log you in."
#~ msgstr "Found an exsisting login - using it to log you in."

#: src/pages/NotFound.tsx:20
#~ msgid "Sorry, this page is not known or was moved."
#~ msgstr "Sorry, this page is not known or was moved."

#: src/pages/NotFound.tsx:27
#~ msgid "Go to the start page"
#~ msgstr "Go to the start page"

#: src/pages/Notifications.tsx:44
#~ msgid "Delete Notifications"
#~ msgstr "Delete Notifications"

#: src/pages/Notifications.tsx:83
msgid "History"
msgstr "Logg"

#: src/pages/Notifications.tsx:91
msgid "Mark as unread"
msgstr "Marker som ulest"

#: src/pages/Notifications.tsx:146
#~ msgid "Delete notifications"
#~ msgstr "Delete notifications"

#: src/pages/build/BuildDetail.tsx:68
msgid "No Required Items"
msgstr ""

#: src/pages/build/BuildDetail.tsx:70
msgid "This build order does not have any required items."
msgstr ""

#: src/pages/build/BuildDetail.tsx:71
msgid "The assembled part may not have a Bill of Materials (BOM) defined, or the BOM is empty."
msgstr ""

#: src/pages/build/BuildDetail.tsx:80
#~ msgid "Build Status"
#~ msgstr "Build Status"

#: src/pages/build/BuildDetail.tsx:185
#: src/pages/part/PartDetail.tsx:269
#: src/pages/stock/StockDetail.tsx:150
#~ msgid "View part barcode"
#~ msgstr "View part barcode"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/company/ManufacturerPartDetail.tsx:83
#: src/pages/company/SupplierPartDetail.tsx:95
#: src/pages/part/PartDetail.tsx:454
#: src/pages/stock/StockDetail.tsx:153
#: src/tables/bom/BomTable.tsx:134
#: src/tables/bom/UsedInTable.tsx:40
#: src/tables/build/BuildAllocatedStockTable.tsx:108
#: src/tables/build/BuildLineTable.tsx:328
#: src/tables/build/BuildOrderTable.tsx:78
#: src/tables/part/PartSalesAllocationsTable.tsx:61
#: src/tables/part/RelatedPartTable.tsx:73
#: src/tables/sales/SalesOrderAllocationTable.tsx:132
#: src/tables/sales/SalesOrderLineItemTable.tsx:96
#: src/tables/stock/StockItemTable.tsx:70
msgid "IPN"
msgstr "IPN"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/part/PartDetail.tsx:274
#~ msgid "Link custom barcode to part"
#~ msgstr "Link custom barcode to part"

#: src/pages/build/BuildDetail.tsx:196
#: src/pages/part/PartDetail.tsx:280
#~ msgid "Unlink custom barcode from part"
#~ msgstr "Unlink custom barcode from part"

#: src/pages/build/BuildDetail.tsx:198
#: src/pages/part/PartDetail.tsx:481
#: src/tables/bom/UsedInTable.tsx:44
#: src/tables/build/BuildOrderTable.tsx:82
#: src/tables/stock/StockItemTable.tsx:75
msgid "Revision"
msgstr ""

#: src/pages/build/BuildDetail.tsx:202
#~ msgid "Build Order updated"
#~ msgstr "Build Order updated"

#: src/pages/build/BuildDetail.tsx:211
#: src/pages/purchasing/PurchaseOrderDetail.tsx:156
#: src/pages/sales/ReturnOrderDetail.tsx:121
#: src/pages/sales/SalesOrderDetail.tsx:130
#: src/pages/stock/StockDetail.tsx:168
msgid "Custom Status"
msgstr ""

#: src/pages/build/BuildDetail.tsx:220
#: src/pages/build/BuildDetail.tsx:716
#: src/pages/build/BuildIndex.tsx:28
#: src/pages/stock/LocationDetail.tsx:142
#: src/tables/build/BuildOrderTable.tsx:122
#: src/tables/build/BuildOrderTable.tsx:184
#: src/tables/stock/StockLocationTable.tsx:48
msgid "External"
msgstr ""

#: src/pages/build/BuildDetail.tsx:221
#~ msgid "Edit build order"
#~ msgstr "Edit build order"

#: src/pages/build/BuildDetail.tsx:226
#~ msgid "Duplicate build order"
#~ msgstr "Duplicate build order"

#: src/pages/build/BuildDetail.tsx:231
#~ msgid "Delete build order"
#~ msgstr "Delete build order"

#: src/pages/build/BuildDetail.tsx:238
#: src/pages/purchasing/PurchaseOrderDetail.tsx:123
#: src/pages/sales/ReturnOrderDetail.tsx:88
#: src/pages/sales/SalesOrderDetail.tsx:97
#: src/tables/ColumnRenderers.tsx:312
#: src/tables/build/BuildAllocatedStockTable.tsx:115
#: src/tables/build/BuildLineTable.tsx:337
msgid "Reference"
msgstr ""

#: src/pages/build/BuildDetail.tsx:252
msgid "Parent Build"
msgstr ""

#: src/pages/build/BuildDetail.tsx:263
msgid "Build Quantity"
msgstr ""

#: src/pages/build/BuildDetail.tsx:269
#: src/pages/part/PartDetail.tsx:598
#: src/tables/bom/BomTable.tsx:347
#: src/tables/bom/BomTable.tsx:382
msgid "Can Build"
msgstr "Kan Produsere"

#: src/pages/build/BuildDetail.tsx:278
#: src/pages/build/BuildDetail.tsx:468
msgid "Completed Outputs"
msgstr "Fullførte artikler"

#: src/pages/build/BuildDetail.tsx:295
#: src/tables/Filter.tsx:373
msgid "Issued By"
msgstr ""

#: src/pages/build/BuildDetail.tsx:303
#: src/pages/part/PartDetail.tsx:691
#: src/pages/purchasing/PurchaseOrderDetail.tsx:243
#: src/pages/sales/ReturnOrderDetail.tsx:207
#: src/pages/sales/SalesOrderDetail.tsx:219
#: src/tables/Filter.tsx:311
msgid "Responsible"
msgstr "Ansvarlig"

#: src/pages/build/BuildDetail.tsx:321
msgid "Any location"
msgstr ""

#: src/pages/build/BuildDetail.tsx:328
msgid "Destination Location"
msgstr ""

#: src/pages/build/BuildDetail.tsx:344
#: src/tables/settings/ApiTokenTable.tsx:98
#: src/tables/settings/PendingTasksTable.tsx:41
msgid "Created"
msgstr "Opprettet"

#: src/pages/build/BuildDetail.tsx:347
#: src/pages/part/PartDetail.tsx:727
#~ msgid "Test Statistics"
#~ msgstr "Test Statistics"

#: src/pages/build/BuildDetail.tsx:352
#: src/pages/purchasing/PurchaseOrderDetail.tsx:268
#: src/pages/sales/ReturnOrderDetail.tsx:233
#: src/pages/sales/SalesOrderDetail.tsx:244
#: src/tables/ColumnRenderers.tsx:424
msgid "Start Date"
msgstr ""

#: src/pages/build/BuildDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:276
#: src/pages/sales/ReturnOrderDetail.tsx:241
#: src/pages/sales/SalesOrderDetail.tsx:252
#: src/tables/ColumnRenderers.tsx:432
#: src/tables/part/PartPurchaseOrdersTable.tsx:101
#: src/tables/sales/ReturnOrderLineItemTable.tsx:150
#: src/tables/sales/SalesOrderLineItemTable.tsx:130
msgid "Target Date"
msgstr "Måldato"

#: src/pages/build/BuildDetail.tsx:368
#: src/tables/build/BuildOrderTable.tsx:92
#: src/tables/sales/SalesOrderLineItemTable.tsx:324
msgid "Completed"
msgstr ""

#: src/pages/build/BuildDetail.tsx:368
#~ msgid "Reporting Actions"
#~ msgstr "Reporting Actions"

#: src/pages/build/BuildDetail.tsx:374
#~ msgid "Print build report"
#~ msgstr "Print build report"

#: src/pages/build/BuildDetail.tsx:404
msgid "Build Details"
msgstr "Produksjonsdetaljer"

#: src/pages/build/BuildDetail.tsx:410
msgid "Required Parts"
msgstr ""

#: src/pages/build/BuildDetail.tsx:422
#: src/pages/sales/SalesOrderDetail.tsx:380
#: src/pages/sales/SalesOrderShipmentDetail.tsx:210
#: src/tables/part/PartSalesAllocationsTable.tsx:73
msgid "Allocated Stock"
msgstr ""

#: src/pages/build/BuildDetail.tsx:438
msgid "Consumed Stock"
msgstr "Brukt lagerbeholdning"

#: src/pages/build/BuildDetail.tsx:455
msgid "Incomplete Outputs"
msgstr "Ufullstendige artikler"

#: src/pages/build/BuildDetail.tsx:483
msgid "External Orders"
msgstr ""

#: src/pages/build/BuildDetail.tsx:497
msgid "Child Build Orders"
msgstr "Underordnede Produksjonsordrer"

#: src/pages/build/BuildDetail.tsx:507
#: src/tables/build/BuildOutputTable.tsx:664
#: src/tables/stock/StockItemTestResultTable.tsx:167
msgid "Test Results"
msgstr ""

#: src/pages/build/BuildDetail.tsx:544
msgid "Edit Build Order"
msgstr "Rediger produksjonsordre"

#: src/pages/build/BuildDetail.tsx:566
#: src/tables/build/BuildOrderTable.tsx:208
#: src/tables/build/BuildOrderTable.tsx:224
msgid "Add Build Order"
msgstr "Legg til produksjonsordre"

#: src/pages/build/BuildDetail.tsx:576
msgid "Cancel Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:578
#: src/pages/purchasing/PurchaseOrderDetail.tsx:398
#: src/pages/sales/ReturnOrderDetail.tsx:393
#: src/pages/sales/SalesOrderDetail.tsx:427
msgid "Order cancelled"
msgstr ""

#: src/pages/build/BuildDetail.tsx:579
#: src/pages/purchasing/PurchaseOrderDetail.tsx:397
#: src/pages/sales/ReturnOrderDetail.tsx:392
#: src/pages/sales/SalesOrderDetail.tsx:426
msgid "Cancel this order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:588
msgid "Hold Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:590
#: src/pages/purchasing/PurchaseOrderDetail.tsx:405
#: src/pages/sales/ReturnOrderDetail.tsx:400
#: src/pages/sales/SalesOrderDetail.tsx:434
msgid "Place this order on hold"
msgstr ""

#: src/pages/build/BuildDetail.tsx:591
#: src/pages/purchasing/PurchaseOrderDetail.tsx:406
#: src/pages/sales/ReturnOrderDetail.tsx:401
#: src/pages/sales/SalesOrderDetail.tsx:435
msgid "Order placed on hold"
msgstr ""

#: src/pages/build/BuildDetail.tsx:596
msgid "Issue Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:598
#: src/pages/purchasing/PurchaseOrderDetail.tsx:389
#: src/pages/sales/ReturnOrderDetail.tsx:384
#: src/pages/sales/SalesOrderDetail.tsx:418
msgid "Issue this order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:599
#: src/pages/purchasing/PurchaseOrderDetail.tsx:390
#: src/pages/sales/ReturnOrderDetail.tsx:385
#: src/pages/sales/SalesOrderDetail.tsx:419
msgid "Order issued"
msgstr ""

#: src/pages/build/BuildDetail.tsx:618
msgid "Complete Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:624
#: src/pages/purchasing/PurchaseOrderDetail.tsx:418
#: src/pages/sales/ReturnOrderDetail.tsx:408
#: src/pages/sales/SalesOrderDetail.tsx:453
msgid "Mark this order as complete"
msgstr ""

#: src/pages/build/BuildDetail.tsx:627
#: src/pages/purchasing/PurchaseOrderDetail.tsx:412
#: src/pages/sales/ReturnOrderDetail.tsx:409
#: src/pages/sales/SalesOrderDetail.tsx:454
msgid "Order completed"
msgstr ""

#: src/pages/build/BuildDetail.tsx:654
#: src/pages/purchasing/PurchaseOrderDetail.tsx:441
#: src/pages/sales/ReturnOrderDetail.tsx:438
#: src/pages/sales/SalesOrderDetail.tsx:489
msgid "Issue Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:661
#: src/pages/purchasing/PurchaseOrderDetail.tsx:448
#: src/pages/sales/ReturnOrderDetail.tsx:445
#: src/pages/sales/SalesOrderDetail.tsx:503
msgid "Complete Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:679
msgid "Build Order Actions"
msgstr "Produksjonsordre-handlinger"

#: src/pages/build/BuildDetail.tsx:684
#: src/pages/purchasing/PurchaseOrderDetail.tsx:470
#: src/pages/sales/ReturnOrderDetail.tsx:467
#: src/pages/sales/SalesOrderDetail.tsx:526
msgid "Edit order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:688
#: src/pages/purchasing/PurchaseOrderDetail.tsx:478
#: src/pages/sales/ReturnOrderDetail.tsx:473
#: src/pages/sales/SalesOrderDetail.tsx:531
msgid "Duplicate order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:692
#: src/pages/purchasing/PurchaseOrderDetail.tsx:481
#: src/pages/sales/ReturnOrderDetail.tsx:478
#: src/pages/sales/SalesOrderDetail.tsx:534
msgid "Hold order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:697
#: src/pages/purchasing/PurchaseOrderDetail.tsx:486
#: src/pages/sales/ReturnOrderDetail.tsx:483
#: src/pages/sales/SalesOrderDetail.tsx:539
msgid "Cancel order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:735
#: src/pages/stock/StockDetail.tsx:341
#: src/tables/build/BuildAllocatedStockTable.tsx:85
#: src/tables/part/PartBuildAllocationsTable.tsx:45
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:150
#: src/tables/stock/StockTrackingTable.tsx:109
msgid "Build Order"
msgstr ""

#: src/pages/build/BuildIndex.tsx:23
#~ msgid "Build order created"
#~ msgstr "Build order created"

#: src/pages/build/BuildIndex.tsx:29
#: src/tables/build/BuildOrderTable.tsx:185
msgid "Show external build orders"
msgstr ""

#: src/pages/build/BuildIndex.tsx:39
#~ msgid "New Build Order"
#~ msgstr "New Build Order"

#: src/pages/build/BuildIndex.tsx:83
#: src/pages/purchasing/PurchasingIndex.tsx:69
#: src/pages/sales/SalesIndex.tsx:90
#: src/pages/sales/SalesIndex.tsx:111
msgid "Table View"
msgstr ""

#: src/pages/build/BuildIndex.tsx:86
#: src/pages/purchasing/PurchasingIndex.tsx:72
#: src/pages/sales/SalesIndex.tsx:93
#: src/pages/sales/SalesIndex.tsx:114
msgid "Calendar View"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:99
msgid "Website"
msgstr "Nettside"

#: src/pages/company/CompanyDetail.tsx:107
msgid "Phone Number"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:114
msgid "Email Address"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:121
msgid "Tax ID"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:131
msgid "Default Currency"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:136
#: src/pages/company/SupplierDetail.tsx:8
#: src/pages/company/SupplierPartDetail.tsx:129
#: src/pages/company/SupplierPartDetail.tsx:235
#: src/pages/company/SupplierPartDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:138
#: src/tables/Filter.tsx:352
#: src/tables/company/CompanyTable.tsx:96
#: src/tables/part/PartPurchaseOrdersTable.tsx:43
#: src/tables/purchasing/PurchaseOrderTable.tsx:109
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:40
msgid "Supplier"
msgstr "Leverandør"

#: src/pages/company/CompanyDetail.tsx:142
#: src/pages/company/ManufacturerDetail.tsx:8
#: src/pages/company/ManufacturerPartDetail.tsx:102
#: src/pages/company/ManufacturerPartDetail.tsx:264
#: src/pages/company/SupplierPartDetail.tsx:151
#: src/tables/Filter.tsx:339
#: src/tables/company/CompanyTable.tsx:101
#: src/tables/purchasing/SupplierPartTable.tsx:70
msgid "Manufacturer"
msgstr "Produsent"

#: src/pages/company/CompanyDetail.tsx:148
#: src/pages/company/CustomerDetail.tsx:8
#: src/pages/part/pricing/SaleHistoryPanel.tsx:31
#: src/pages/sales/ReturnOrderDetail.tsx:103
#: src/pages/sales/SalesOrderDetail.tsx:112
#: src/pages/sales/SalesOrderShipmentDetail.tsx:102
#: src/pages/stock/StockDetail.tsx:367
#: src/tables/company/CompanyTable.tsx:106
#: src/tables/sales/ReturnOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:153
msgid "Customer"
msgstr "Kunde"

#: src/pages/company/CompanyDetail.tsx:175
#~ msgid "Edit company"
#~ msgstr "Edit company"

#: src/pages/company/CompanyDetail.tsx:181
msgid "Company Details"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:187
msgid "Supplied Parts"
msgstr "Leverte Deler"

#: src/pages/company/CompanyDetail.tsx:189
#~ msgid "Delete company"
#~ msgstr "Delete company"

#: src/pages/company/CompanyDetail.tsx:196
msgid "Manufactured Parts"
msgstr "Produserte deler"

#: src/pages/company/CompanyDetail.tsx:243
msgid "Assigned Stock"
msgstr "Tildelt lagerbeholdning"

#: src/pages/company/CompanyDetail.tsx:284
#: src/tables/company/CompanyTable.tsx:82
msgid "Edit Company"
msgstr "Rediger Bedrift"

#: src/pages/company/CompanyDetail.tsx:292
msgid "Delete Company"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:307
msgid "Company Actions"
msgstr "Bedriftshandlinger"

#: src/pages/company/ManufacturerPartDetail.tsx:76
#: src/pages/company/SupplierPartDetail.tsx:88
msgid "Internal Part"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:110
#: src/pages/company/SupplierPartDetail.tsx:160
msgid "Manufacturer Part Number"
msgstr "Produsentens delenummer"

#: src/pages/company/ManufacturerPartDetail.tsx:127
#: src/pages/company/SupplierPartDetail.tsx:112
msgid "External Link"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:146
#: src/pages/company/SupplierPartDetail.tsx:232
#: src/pages/part/PartDetail.tsx:787
msgid "Part Details"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:149
msgid "Manufacturer Details"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:158
msgid "Manufacturer Part Details"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:164
#: src/pages/part/PartDetail.tsx:793
msgid "Parameters"
msgstr "Parametere"

#: src/pages/company/ManufacturerPartDetail.tsx:204
#: src/tables/purchasing/ManufacturerPartTable.tsx:84
msgid "Edit Manufacturer Part"
msgstr "Rediger produsentdel"

#: src/pages/company/ManufacturerPartDetail.tsx:211
#: src/tables/purchasing/ManufacturerPartTable.tsx:72
#: src/tables/purchasing/ManufacturerPartTable.tsx:104
msgid "Add Manufacturer Part"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:223
#: src/tables/purchasing/ManufacturerPartTable.tsx:92
msgid "Delete Manufacturer Part"
msgstr "Slett produsentdel"

#: src/pages/company/ManufacturerPartDetail.tsx:238
msgid "Manufacturer Part Actions"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:281
msgid "ManufacturerPart"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:103
#: src/tables/part/RelatedPartTable.tsx:82
msgid "Part Description"
msgstr "Delbeskrivelse"

#: src/pages/company/SupplierPartDetail.tsx:179
#: src/tables/part/PartPurchaseOrdersTable.tsx:73
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:184
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:226
#: src/tables/purchasing/SupplierPartTable.tsx:120
msgid "Pack Quantity"
msgstr "Pakkeantall"

#: src/pages/company/SupplierPartDetail.tsx:197
#: src/pages/company/SupplierPartDetail.tsx:390
#: src/pages/part/PartDetail.tsx:1000
#: src/tables/bom/BomTable.tsx:414
#: src/tables/part/PartTable.tsx:95
msgid "On Order"
msgstr "I bestilling"

#: src/pages/company/SupplierPartDetail.tsx:204
msgid "Supplier Availability"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:212
msgid "Availability Updated"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:237
msgid "Availability"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:246
msgid "Supplier Part Details"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:252
#: src/pages/purchasing/PurchaseOrderDetail.tsx:361
msgid "Received Stock"
msgstr "Mottatt lagerbeholdning"

#: src/pages/company/SupplierPartDetail.tsx:279
#: src/pages/part/PartPricingPanel.tsx:113
#: src/pages/part/pricing/PricingOverviewPanel.tsx:232
msgid "Supplier Pricing"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:304
msgid "Supplier Part Actions"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:328
#: src/tables/purchasing/SupplierPartTable.tsx:207
msgid "Edit Supplier Part"
msgstr "Rediger Leverandørdel"

#: src/pages/company/SupplierPartDetail.tsx:336
#: src/tables/purchasing/SupplierPartTable.tsx:215
msgid "Delete Supplier Part"
msgstr "Slett Leverandørdel"

#: src/pages/company/SupplierPartDetail.tsx:344
#: src/tables/purchasing/SupplierPartTable.tsx:154
msgid "Add Supplier Part"
msgstr "Legg til leverandørdel"

#: src/pages/company/SupplierPartDetail.tsx:384
#: src/pages/part/PartDetail.tsx:988
msgid "No Stock"
msgstr ""

#: src/pages/core/CoreIndex.tsx:46
#: src/pages/core/GroupDetail.tsx:81
#: src/pages/core/UserDetail.tsx:224
msgid "System Overview"
msgstr ""

#: src/pages/core/GroupDetail.tsx:45
msgid "Group Name"
msgstr ""

#: src/pages/core/GroupDetail.tsx:52
#: src/pages/core/GroupDetail.tsx:67
#: src/tables/settings/GroupTable.tsx:85
msgid "Group Details"
msgstr ""

#: src/pages/core/GroupDetail.tsx:55
#: src/tables/settings/GroupTable.tsx:112
msgid "Group Roles"
msgstr ""

#: src/pages/core/UserDetail.tsx:175
msgid "User Information"
msgstr ""

#: src/pages/core/UserDetail.tsx:176
msgid "User Permissions"
msgstr ""

#: src/pages/core/UserDetail.tsx:178
msgid "User Profile"
msgstr ""

#: src/pages/core/UserDetail.tsx:188
#: src/tables/settings/UserTable.tsx:164
msgid "User Details"
msgstr ""

#: src/pages/core/UserDetail.tsx:206
msgid "Basic user"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:98
#: src/pages/stock/LocationDetail.tsx:96
#: src/tables/settings/ErrorTable.tsx:63
#: src/tables/settings/ErrorTable.tsx:108
msgid "Path"
msgstr "Sti"

#: src/pages/part/CategoryDetail.tsx:114
msgid "Parent Category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:137
#: src/pages/part/CategoryDetail.tsx:267
msgid "Subcategories"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:144
#: src/pages/stock/LocationDetail.tsx:136
#: src/tables/part/PartCategoryTable.tsx:89
#: src/tables/stock/StockLocationTable.tsx:43
msgid "Structural"
msgstr "Strukturell"

#: src/pages/part/CategoryDetail.tsx:150
msgid "Parent default location"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:157
msgid "Default location"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:168
msgid "Top level part category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:178
#: src/pages/part/CategoryDetail.tsx:244
#: src/tables/part/PartCategoryTable.tsx:122
msgid "Edit Part Category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:187
msgid "Move items to parent category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:191
#: src/pages/stock/LocationDetail.tsx:228
msgid "Delete items"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:199
#: src/pages/part/CategoryDetail.tsx:249
msgid "Delete Part Category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:202
msgid "Parts Action"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:203
msgid "Action for parts in this category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:208
msgid "Child Categories Action"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:209
msgid "Action for child categories in this category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:240
#: src/tables/part/PartCategoryTable.tsx:143
msgid "Category Actions"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:261
msgid "Category Details"
msgstr ""

#: src/pages/part/PartAllocationPanel.tsx:21
#: src/pages/stock/StockDetail.tsx:539
#: src/tables/part/PartTable.tsx:108
msgid "Build Order Allocations"
msgstr "Produksjonsordre-tildelinger"

#: src/pages/part/PartAllocationPanel.tsx:31
#: src/pages/stock/StockDetail.tsx:554
#: src/tables/part/PartTable.tsx:116
msgid "Sales Order Allocations"
msgstr "Salgsordretildelinger"

#: src/pages/part/PartDetail.tsx:181
#: src/pages/part/PartDetail.tsx:184
#: src/pages/part/PartDetail.tsx:228
msgid "Validate BOM"
msgstr ""

#: src/pages/part/PartDetail.tsx:185
msgid "Do you want to validate the bill of materials for this assembly?"
msgstr ""

#: src/pages/part/PartDetail.tsx:188
msgid "BOM validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:206
msgid "BOM Validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:207
msgid "The Bill of Materials for this part has been validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:211
#: src/pages/part/PartDetail.tsx:216
msgid "BOM Not Validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:212
msgid "The Bill of Materials for this part has previously been checked, but requires revalidation"
msgstr ""

#: src/pages/part/PartDetail.tsx:217
msgid "The Bill of Materials for this part has not yet been validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:248
msgid "Validated On"
msgstr ""

#: src/pages/part/PartDetail.tsx:253
msgid "Validated By"
msgstr ""

#: src/pages/part/PartDetail.tsx:286
#~ msgid "Variant Stock"
#~ msgstr "Variant Stock"

#: src/pages/part/PartDetail.tsx:310
#~ msgid "Edit part"
#~ msgstr "Edit part"

#: src/pages/part/PartDetail.tsx:322
#~ msgid "Duplicate part"
#~ msgstr "Duplicate part"

#: src/pages/part/PartDetail.tsx:327
#~ msgid "Delete part"
#~ msgstr "Delete part"

#: src/pages/part/PartDetail.tsx:467
msgid "Variant of"
msgstr ""

#: src/pages/part/PartDetail.tsx:474
msgid "Revision of"
msgstr ""

#: src/pages/part/PartDetail.tsx:494
#: src/tables/ColumnRenderers.tsx:200
#: src/tables/ColumnRenderers.tsx:209
msgid "Default Location"
msgstr ""

#: src/pages/part/PartDetail.tsx:501
msgid "Category Default Location"
msgstr ""

#: src/pages/part/PartDetail.tsx:508
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:48
msgid "Units"
msgstr "Enheter"

#: src/pages/part/PartDetail.tsx:510
#~ msgid "Stocktake By"
#~ msgstr "Stocktake By"

#: src/pages/part/PartDetail.tsx:515
#: src/tables/settings/PendingTasksTable.tsx:51
msgid "Keywords"
msgstr "Nøkkelord"

#: src/pages/part/PartDetail.tsx:542
#: src/tables/bom/BomTable.tsx:409
#: src/tables/build/BuildLineTable.tsx:297
#: src/tables/part/PartTable.tsx:306
#: src/tables/sales/SalesOrderLineItemTable.tsx:134
msgid "Available Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:548
#: src/tables/bom/BomTable.tsx:323
#: src/tables/build/BuildLineTable.tsx:259
#: src/tables/sales/SalesOrderLineItemTable.tsx:172
msgid "On order"
msgstr "I bestilling"

#: src/pages/part/PartDetail.tsx:555
msgid "Required for Orders"
msgstr ""

#: src/pages/part/PartDetail.tsx:566
msgid "Allocated to Build Orders"
msgstr ""

#: src/pages/part/PartDetail.tsx:578
msgid "Allocated to Sales Orders"
msgstr ""

#: src/pages/part/PartDetail.tsx:587
#: src/pages/part/PartDetail.tsx:1006
#: src/pages/stock/StockDetail.tsx:896
#: src/tables/build/BuildOrderTestTable.tsx:273
#: src/tables/stock/StockItemTable.tsx:364
msgid "In Production"
msgstr "Under produksjon"

#: src/pages/part/PartDetail.tsx:605
msgid "Minimum Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:613
#~ msgid "Scheduling"
#~ msgstr "Scheduling"

#: src/pages/part/PartDetail.tsx:620
#: src/tables/part/ParametricPartTable.tsx:355
#: src/tables/part/PartTable.tsx:190
msgid "Locked"
msgstr ""

#: src/pages/part/PartDetail.tsx:626
msgid "Template Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:631
#: src/tables/bom/BomTable.tsx:404
msgid "Assembled Part"
msgstr "Sammenstilt del"

#: src/pages/part/PartDetail.tsx:636
msgid "Component Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:641
#: src/tables/bom/BomTable.tsx:394
msgid "Testable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:647
#: src/tables/bom/BomTable.tsx:399
msgid "Trackable Part"
msgstr "Sporbar del"

#: src/pages/part/PartDetail.tsx:652
msgid "Purchaseable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:658
msgid "Saleable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:663
msgid "Virtual Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:678
#: src/pages/purchasing/PurchaseOrderDetail.tsx:253
#: src/pages/sales/ReturnOrderDetail.tsx:217
#: src/pages/sales/SalesOrderDetail.tsx:229
#: src/tables/ColumnRenderers.tsx:440
msgid "Creation Date"
msgstr "Opprettelsesdato"

#: src/pages/part/PartDetail.tsx:683
#: src/tables/ColumnRenderers.tsx:386
#: src/tables/Filter.tsx:365
msgid "Created By"
msgstr ""

#: src/pages/part/PartDetail.tsx:698
msgid "Default Supplier"
msgstr ""

#: src/pages/part/PartDetail.tsx:704
msgid "Default Expiry"
msgstr ""

#: src/pages/part/PartDetail.tsx:709
msgid "days"
msgstr ""

#: src/pages/part/PartDetail.tsx:719
#: src/pages/part/pricing/BomPricingPanel.tsx:113
#: src/pages/part/pricing/VariantPricingPanel.tsx:95
#: src/tables/part/PartTable.tsx:166
msgid "Price Range"
msgstr "Prisområde"

#: src/pages/part/PartDetail.tsx:729
msgid "Latest Serial Number"
msgstr ""

#: src/pages/part/PartDetail.tsx:757
msgid "Select Part Revision"
msgstr ""

#: src/pages/part/PartDetail.tsx:822
msgid "Variants"
msgstr "Varianter"

#: src/pages/part/PartDetail.tsx:829
#: src/pages/stock/StockDetail.tsx:526
msgid "Allocations"
msgstr "Tildelinger"

#: src/pages/part/PartDetail.tsx:836
msgid "Bill of Materials"
msgstr "Stykkliste (BOM)"

#: src/pages/part/PartDetail.tsx:848
msgid "Used In"
msgstr "Brukt i"

#: src/pages/part/PartDetail.tsx:855
msgid "Part Pricing"
msgstr ""

#: src/pages/part/PartDetail.tsx:923
msgid "Test Templates"
msgstr "Testmaler"

#: src/pages/part/PartDetail.tsx:934
msgid "Related Parts"
msgstr "Relaterte Deler"

#: src/pages/part/PartDetail.tsx:956
#~ msgid "Count part stock"
#~ msgstr "Count part stock"

#: src/pages/part/PartDetail.tsx:967
#~ msgid "Transfer part stock"
#~ msgstr "Transfer part stock"

#: src/pages/part/PartDetail.tsx:994
#: src/tables/part/PartTestTemplateTable.tsx:112
#: src/tables/stock/StockItemTestResultTable.tsx:401
msgid "Required"
msgstr ""

#: src/pages/part/PartDetail.tsx:1025
#: src/tables/part/PartTable.tsx:355
msgid "Edit Part"
msgstr "Rediger del"

#: src/pages/part/PartDetail.tsx:1065
#: src/tables/part/PartTable.tsx:343
#: src/tables/part/PartTable.tsx:420
msgid "Add Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:1079
msgid "Delete Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:1088
msgid "Deleting this part cannot be reversed"
msgstr ""

#: src/pages/part/PartDetail.tsx:1149
#: src/pages/stock/StockDetail.tsx:854
msgid "Order"
msgstr ""

#: src/pages/part/PartDetail.tsx:1150
#: src/pages/stock/StockDetail.tsx:855
#: src/tables/build/BuildLineTable.tsx:740
msgid "Order Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:1162
msgid "Search by serial number"
msgstr ""

#: src/pages/part/PartDetail.tsx:1170
#: src/tables/part/PartTable.tsx:392
msgid "Part Actions"
msgstr "Delhandlinger"

#: src/pages/part/PartIndex.tsx:29
#~ msgid "Categories"
#~ msgstr "Categories"

#: src/pages/part/PartPricingPanel.tsx:72
msgid "No pricing data found for this part."
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:87
#: src/pages/part/pricing/PricingOverviewPanel.tsx:325
msgid "Pricing Overview"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:93
msgid "Purchase History"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:107
#: src/pages/part/pricing/PricingOverviewPanel.tsx:204
msgid "Internal Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:122
#: src/pages/part/pricing/PricingOverviewPanel.tsx:214
msgid "BOM Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:129
#: src/pages/part/pricing/PricingOverviewPanel.tsx:242
msgid "Variant Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:141
#: src/pages/part/pricing/PricingOverviewPanel.tsx:251
msgid "Sale Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:147
#: src/pages/part/pricing/PricingOverviewPanel.tsx:260
msgid "Sale History"
msgstr ""

#: src/pages/part/PartSchedulingDetail.tsx:51
#: src/pages/part/PartSchedulingDetail.tsx:291
#~ msgid "Scheduled"
#~ msgstr "Scheduled"

#: src/pages/part/PartSchedulingDetail.tsx:95
#~ msgid "Quantity is speculative"
#~ msgstr "Quantity is speculative"

#: src/pages/part/PartSchedulingDetail.tsx:104
#~ msgid "No date available for provided quantity"
#~ msgstr "No date available for provided quantity"

#: src/pages/part/PartSchedulingDetail.tsx:108
#~ msgid "Date is in the past"
#~ msgstr "Date is in the past"

#: src/pages/part/PartSchedulingDetail.tsx:115
#~ msgid "Scheduled Quantity"
#~ msgstr "Scheduled Quantity"

#: src/pages/part/PartSchedulingDetail.tsx:242
#~ msgid "No information available"
#~ msgstr "No information available"

#: src/pages/part/PartSchedulingDetail.tsx:243
#~ msgid "There is no scheduling information available for the selected part"
#~ msgstr "There is no scheduling information available for the selected part"

#: src/pages/part/PartSchedulingDetail.tsx:278
#~ msgid "Expected Quantity"
#~ msgstr "Expected Quantity"

#: src/pages/part/PartStockHistoryDetail.tsx:80
msgid "Edit Stocktake Entry"
msgstr ""

#: src/pages/part/PartStockHistoryDetail.tsx:88
msgid "Delete Stocktake Entry"
msgstr ""

#: src/pages/part/PartStockHistoryDetail.tsx:107
#: src/pages/part/PartStockHistoryDetail.tsx:211
#: src/pages/stock/StockDetail.tsx:399
#: src/tables/stock/StockItemTable.tsx:272
msgid "Stock Value"
msgstr ""

#: src/pages/part/PartStockHistoryDetail.tsx:240
#: src/pages/part/pricing/PricingOverviewPanel.tsx:327
msgid "Minimum Value"
msgstr ""

#: src/pages/part/PartStockHistoryDetail.tsx:246
#: src/pages/part/pricing/PricingOverviewPanel.tsx:328
msgid "Maximum Value"
msgstr ""

#: src/pages/part/PartStocktakeDetail.tsx:99
#: src/tables/settings/StocktakeReportTable.tsx:70
#~ msgid "Generate Stocktake Report"
#~ msgstr "Generate Stocktake Report"

#: src/pages/part/PartStocktakeDetail.tsx:104
#: src/tables/settings/StocktakeReportTable.tsx:72
#~ msgid "Stocktake report scheduled"
#~ msgstr "Stocktake report scheduled"

#: src/pages/part/PartStocktakeDetail.tsx:145
#: src/tables/settings/StocktakeReportTable.tsx:78
#~ msgid "New Stocktake Report"
#~ msgstr "New Stocktake Report"

#: src/pages/part/pricing/BomPricingPanel.tsx:87
#: src/pages/part/pricing/BomPricingPanel.tsx:175
#: src/tables/ColumnRenderers.tsx:490
#: src/tables/bom/BomTable.tsx:270
#: src/tables/general/ExtraLineItemTable.tsx:69
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:255
#: src/tables/purchasing/PurchaseOrderTable.tsx:138
#: src/tables/sales/ReturnOrderTable.tsx:139
#: src/tables/sales/SalesOrderLineItemTable.tsx:120
#: src/tables/sales/SalesOrderTable.tsx:175
msgid "Total Price"
msgstr "Total pris"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#: src/pages/part/pricing/BomPricingPanel.tsx:141
#: src/tables/bom/UsedInTable.tsx:54
#: src/tables/part/PartTable.tsx:214
msgid "Component"
msgstr "Komponent"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#~ msgid "Minimum Total Price"
#~ msgstr "Minimum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:115
#: src/pages/part/pricing/VariantPricingPanel.tsx:35
#: src/pages/part/pricing/VariantPricingPanel.tsx:97
msgid "Minimum Price"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:116
#: src/pages/part/pricing/VariantPricingPanel.tsx:43
#: src/pages/part/pricing/VariantPricingPanel.tsx:98
msgid "Maximum Price"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:117
#~ msgid "Maximum Total Price"
#~ msgstr "Maximum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:166
#: src/pages/part/pricing/PriceBreakPanel.tsx:173
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:71
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:126
#: src/pages/part/pricing/SupplierPricingPanel.tsx:66
#: src/pages/stock/StockDetail.tsx:387
#: src/tables/bom/BomTable.tsx:260
#: src/tables/general/ExtraLineItemTable.tsx:61
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:250
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:84
#: src/tables/stock/StockItemTable.tsx:260
msgid "Unit Price"
msgstr "Enhetspris"

#: src/pages/part/pricing/BomPricingPanel.tsx:256
msgid "Pie Chart"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:257
msgid "Bar Chart"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:58
#: src/pages/part/pricing/PriceBreakPanel.tsx:111
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:134
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:162
msgid "Add Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:71
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:146
msgid "Edit Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:81
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:154
msgid "Delete Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:95
msgid "Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:171
msgid "Price"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:72
msgid "Refreshing pricing data"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:92
msgid "Pricing data updated"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:99
msgid "Failed to update pricing data"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:127
msgid "Edit Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:139
msgid "Pricing Category"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:158
msgid "Minimum"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:170
msgid "Maximum"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:188
msgid "Override Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:196
msgid "Overall Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:222
msgid "Purchase Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:281
#: src/pages/stock/StockDetail.tsx:179
#: src/tables/part/PartParameterTable.tsx:121
#: src/tables/stock/StockItemTable.tsx:301
msgid "Last Updated"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:285
msgid "Pricing Not Set"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:286
msgid "Pricing data has not been calculated for this part"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:290
msgid "Pricing Actions"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:293
msgid "Refresh"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:294
msgid "Refresh pricing data"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:309
msgid "Edit pricing data"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:24
msgid "No data available"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:65
msgid "No Data"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:66
msgid "No pricing data available"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:77
msgid "Loading pricing data"
msgstr ""

#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:48
msgid "Purchase Price"
msgstr ""

#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#~ msgid "Sale Order"
#~ msgstr "Sale Order"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:44
#: src/pages/part/pricing/SaleHistoryPanel.tsx:87
msgid "Sale Price"
msgstr ""

#: src/pages/part/pricing/SupplierPricingPanel.tsx:69
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:75
msgid "Supplier Price"
msgstr ""

#: src/pages/part/pricing/VariantPricingPanel.tsx:29
#: src/pages/part/pricing/VariantPricingPanel.tsx:94
msgid "Variant Part"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:89
msgid "Edit Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:107
#: src/tables/purchasing/PurchaseOrderTable.tsx:154
#: src/tables/purchasing/PurchaseOrderTable.tsx:167
msgid "Add Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:129
msgid "Supplier Reference"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:159
#: src/pages/sales/ReturnOrderDetail.tsx:126
#: src/pages/sales/SalesOrderDetail.tsx:130
#~ msgid "Order Currency,"
#~ msgstr "Order Currency,"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:169
#: src/pages/sales/ReturnOrderDetail.tsx:140
#: src/pages/sales/SalesOrderDetail.tsx:143
msgid "Completed Line Items"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:178
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:261
msgid "Destination"
msgstr "Destinasjon"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:184
#: src/pages/sales/ReturnOrderDetail.tsx:147
#: src/pages/sales/SalesOrderDetail.tsx:160
msgid "Order Currency"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:190
#: src/pages/sales/ReturnOrderDetail.tsx:154
#: src/pages/sales/SalesOrderDetail.tsx:166
msgid "Total Cost"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:207
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:191
#~ msgid "Created On"
#~ msgstr "Created On"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:219
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:195
msgid "Contact Email"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:227
#: src/pages/sales/ReturnOrderDetail.tsx:191
#: src/pages/sales/SalesOrderDetail.tsx:203
msgid "Contact Phone"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:260
#: src/pages/sales/ReturnOrderDetail.tsx:225
#: src/pages/sales/SalesOrderDetail.tsx:236
msgid "Issue Date"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:249
#: src/pages/sales/SalesOrderDetail.tsx:259
#: src/tables/ColumnRenderers.tsx:448
#: src/tables/build/BuildOrderTable.tsx:136
#: src/tables/part/PartPurchaseOrdersTable.tsx:106
msgid "Completion Date"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:315
#: src/pages/sales/ReturnOrderDetail.tsx:279
#: src/pages/sales/SalesOrderDetail.tsx:325
msgid "Order Details"
msgstr "Ordredetaljer"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:321
#: src/pages/purchasing/PurchaseOrderDetail.tsx:330
#: src/pages/sales/ReturnOrderDetail.tsx:133
#: src/pages/sales/ReturnOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:294
#: src/pages/sales/SalesOrderDetail.tsx:331
#: src/pages/sales/SalesOrderDetail.tsx:340
msgid "Line Items"
msgstr "Ordrelinjer"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:344
#: src/pages/sales/ReturnOrderDetail.tsx:308
#: src/pages/sales/SalesOrderDetail.tsx:357
msgid "Extra Line Items"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:387
msgid "Issue Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:395
msgid "Cancel Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:403
msgid "Hold Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:411
msgid "Complete Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:466
#: src/pages/sales/ReturnOrderDetail.tsx:463
#: src/pages/sales/SalesOrderDetail.tsx:521
msgid "Order Actions"
msgstr "Ordrehandlinger"

#: src/pages/sales/ReturnOrderDetail.tsx:94
#: src/pages/sales/SalesOrderDetail.tsx:103
#: src/pages/sales/SalesOrderShipmentDetail.tsx:111
#: src/tables/sales/SalesOrderTable.tsx:141
msgid "Customer Reference"
msgstr "Kundereferanse"

#: src/pages/sales/ReturnOrderDetail.tsx:349
#~ msgid "Order canceled"
#~ msgstr "Order canceled"

#: src/pages/sales/ReturnOrderDetail.tsx:355
msgid "Edit Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:373
#: src/tables/sales/ReturnOrderTable.tsx:154
#: src/tables/sales/ReturnOrderTable.tsx:167
msgid "Add Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:382
msgid "Issue Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:390
msgid "Cancel Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:398
msgid "Hold Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:406
msgid "Complete Return Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:152
msgid "Completed Shipments"
msgstr "Fullførte forsendelser"

#: src/pages/sales/SalesOrderDetail.tsx:256
#~ msgid "Pending Shipments"
#~ msgstr "Pending Shipments"

#: src/pages/sales/SalesOrderDetail.tsx:292
msgid "Edit Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:314
#: src/tables/sales/SalesOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:121
msgid "Add Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:374
#: src/tables/sales/SalesOrderTable.tsx:147
msgid "Shipments"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:416
msgid "Issue Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:424
msgid "Cancel Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:432
msgid "Hold Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:440
msgid "Ship Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:442
msgid "Ship this order?"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:443
msgid "Order shipped"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:451
msgid "Complete Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:496
msgid "Ship Order"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:119
#: src/tables/sales/SalesOrderShipmentTable.tsx:94
msgid "Shipment Reference"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:126
msgid "Allocated Items"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:135
msgid "Tracking Number"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:143
msgid "Invoice Number"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:151
#: src/tables/ColumnRenderers.tsx:456
#: src/tables/sales/SalesOrderAllocationTable.tsx:179
#: src/tables/sales/SalesOrderShipmentTable.tsx:113
msgid "Shipment Date"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:159
#: src/tables/sales/SalesOrderShipmentTable.tsx:117
msgid "Delivery Date"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:204
msgid "Shipment Details"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:211
#~ msgid "Assigned Items"
#~ msgstr "Assigned Items"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:242
#: src/pages/sales/SalesOrderShipmentDetail.tsx:334
#: src/tables/sales/SalesOrderShipmentTable.tsx:73
msgid "Edit Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:249
#: src/pages/sales/SalesOrderShipmentDetail.tsx:339
#: src/tables/sales/SalesOrderShipmentTable.tsx:65
msgid "Cancel Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:262
#: src/tables/sales/SalesOrderShipmentTable.tsx:81
#: src/tables/sales/SalesOrderShipmentTable.tsx:144
msgid "Complete Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:279
#: src/tables/part/PartPurchaseOrdersTable.tsx:122
msgid "Pending"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:285
#: src/tables/sales/SalesOrderShipmentTable.tsx:106
#: src/tables/sales/SalesOrderShipmentTable.tsx:190
msgid "Shipped"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:291
#: src/tables/sales/SalesOrderShipmentTable.tsx:195
#: src/tables/settings/EmailTable.tsx:84
msgid "Delivered"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:306
msgid "Send Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:329
msgid "Shipment Actions"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:112
msgid "Parent Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:130
msgid "Sublocations"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:148
#: src/tables/stock/StockLocationTable.tsx:57
msgid "Location Type"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:159
msgid "Top level stock location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:170
msgid "Location Details"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:196
msgid "Default Parts"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:215
#: src/pages/stock/LocationDetail.tsx:374
#: src/tables/stock/StockLocationTable.tsx:121
msgid "Edit Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:224
msgid "Move items to parent location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:236
#: src/pages/stock/LocationDetail.tsx:379
msgid "Delete Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:239
msgid "Items Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:240
msgid "Action for stock items in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:245
msgid "Child Locations Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:246
msgid "Action for child locations in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:280
msgid "Scan Stock Item"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:298
#: src/pages/stock/StockDetail.tsx:783
msgid "Scanned stock item into location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:304
#: src/pages/stock/StockDetail.tsx:789
msgid "Error scanning stock item"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:311
msgid "Scan Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:323
msgid "Scanned stock location into location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:329
msgid "Error scanning stock location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:370
#: src/tables/stock/StockLocationTable.tsx:142
msgid "Location Actions"
msgstr ""

#: src/pages/stock/StockDetail.tsx:147
msgid "Base Part"
msgstr "Basisdel"

#: src/pages/stock/StockDetail.tsx:155
#~ msgid "Link custom barcode to stock item"
#~ msgstr "Link custom barcode to stock item"

#: src/pages/stock/StockDetail.tsx:156
#~ msgid "Completed Tests"
#~ msgstr "Completed Tests"

#: src/pages/stock/StockDetail.tsx:161
#~ msgid "Unlink custom barcode from stock item"
#~ msgstr "Unlink custom barcode from stock item"

#: src/pages/stock/StockDetail.tsx:185
msgid "Last Stocktake"
msgstr ""

#: src/pages/stock/StockDetail.tsx:203
msgid "Previous serial number"
msgstr ""

#: src/pages/stock/StockDetail.tsx:205
#~ msgid "Edit stock item"
#~ msgstr "Edit stock item"

#: src/pages/stock/StockDetail.tsx:217
#~ msgid "Delete stock item"
#~ msgstr "Delete stock item"

#: src/pages/stock/StockDetail.tsx:225
msgid "Find serial number"
msgstr ""

#: src/pages/stock/StockDetail.tsx:269
msgid "Allocated to Orders"
msgstr ""

#: src/pages/stock/StockDetail.tsx:302
msgid "Installed In"
msgstr ""

#: src/pages/stock/StockDetail.tsx:322
msgid "Parent Item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:326
msgid "Parent stock item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:332
msgid "Consumed By"
msgstr ""

#: src/pages/stock/StockDetail.tsx:433
#~ msgid "Duplicate stock item"
#~ msgstr "Duplicate stock item"

#: src/pages/stock/StockDetail.tsx:510
msgid "Stock Details"
msgstr ""

#: src/pages/stock/StockDetail.tsx:516
msgid "Stock Tracking"
msgstr "Sporing av lager"

#: src/pages/stock/StockDetail.tsx:571
msgid "Test Data"
msgstr "Testdata"

#: src/pages/stock/StockDetail.tsx:585
msgid "Installed Items"
msgstr "Installerte artikler"

#: src/pages/stock/StockDetail.tsx:592
msgid "Child Items"
msgstr "Underordnede artikler"

#: src/pages/stock/StockDetail.tsx:645
msgid "Edit Stock Item"
msgstr "Rediger lagervare"

#: src/pages/stock/StockDetail.tsx:671
#: src/tables/stock/StockItemTable.tsx:452
#~ msgid "Add stock"
#~ msgstr "Add stock"

#: src/pages/stock/StockDetail.tsx:680
#: src/tables/stock/StockItemTable.tsx:461
#~ msgid "Remove stock"
#~ msgstr "Remove stock"

#: src/pages/stock/StockDetail.tsx:687
msgid "Items Created"
msgstr ""

#: src/pages/stock/StockDetail.tsx:688
msgid "Created {n} stock items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:698
#: src/tables/stock/StockItemTable.tsx:481
#~ msgid "Transfer stock"
#~ msgstr "Transfer stock"

#: src/pages/stock/StockDetail.tsx:705
msgid "Delete Stock Item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:741
msgid "Serialize Stock Item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:757
#: src/tables/stock/StockItemTable.tsx:539
msgid "Stock item serialized"
msgstr ""

#: src/pages/stock/StockDetail.tsx:762
#~ msgid "Return Stock Item"
#~ msgstr "Return Stock Item"

#: src/pages/stock/StockDetail.tsx:765
msgid "Scan Into Location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:765
#~ msgid "Return this item into stock. This will remove the customer assignment."
#~ msgstr "Return this item into stock. This will remove the customer assignment."

#: src/pages/stock/StockDetail.tsx:777
#~ msgid "Item returned to stock"
#~ msgstr "Item returned to stock"

#: src/pages/stock/StockDetail.tsx:823
msgid "Scan into location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:825
msgid "Scan this item into a location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:837
msgid "Stock Operations"
msgstr "Lagerhandlinger"

#: src/pages/stock/StockDetail.tsx:842
#: src/tables/build/BuildOutputTable.tsx:532
msgid "Serialize"
msgstr ""

#: src/pages/stock/StockDetail.tsx:843
msgid "Serialize stock"
msgstr ""

#: src/pages/stock/StockDetail.tsx:868
msgid "Stock Item Actions"
msgstr ""

#: src/pages/stock/StockDetail.tsx:868
#~ msgid "Count stock"
#~ msgstr "Count stock"

#: src/pages/stock/StockDetail.tsx:890
#~ msgid "Return from customer"
#~ msgstr "Return from customer"

#: src/pages/stock/StockDetail.tsx:900
#~ msgid "Transfer"
#~ msgstr "Transfer"

#: src/pages/stock/StockDetail.tsx:937
#: src/tables/stock/StockItemTable.tsx:409
msgid "Stale"
msgstr ""

#: src/pages/stock/StockDetail.tsx:943
#: src/tables/stock/StockItemTable.tsx:403
msgid "Expired"
msgstr ""

#: src/pages/stock/StockDetail.tsx:949
msgid "Unavailable"
msgstr ""

#: src/pages/stock/StockDetail.tsx:950
#~ msgid "Assign to Customer"
#~ msgstr "Assign to Customer"

#: src/pages/stock/StockDetail.tsx:951
#~ msgid "Assign to a customer"
#~ msgstr "Assign to a customer"

#: src/states/IconState.tsx:47
#: src/states/IconState.tsx:77
msgid "Error loading icon package from server"
msgstr ""

#: src/tables/ColumnRenderers.tsx:41
#~ msgid "Part is locked"
#~ msgstr "Part is locked"

#: src/tables/ColumnRenderers.tsx:59
msgid "Part is not active"
msgstr ""

#: src/tables/ColumnRenderers.tsx:64
#: src/tables/bom/BomTable.tsx:619
#: src/tables/part/PartParameterTable.tsx:235
#: src/tables/part/PartTestTemplateTable.tsx:258
msgid "Part is Locked"
msgstr ""

#: src/tables/ColumnRenderers.tsx:69
msgid "You are subscribed to notifications for this part"
msgstr ""

#: src/tables/ColumnRenderers.tsx:93
#~ msgid "No location set"
#~ msgstr "No location set"

#: src/tables/ColumnSelect.tsx:16
#: src/tables/ColumnSelect.tsx:23
msgid "Select Columns"
msgstr "Velg Kolonner"

#: src/tables/DownloadAction.tsx:13
#~ msgid "Excel"
#~ msgstr "Excel"

#: src/tables/DownloadAction.tsx:21
#~ msgid "CSV"
#~ msgstr "CSV"

#: src/tables/DownloadAction.tsx:21
#~ msgid "Download selected data"
#~ msgstr "Download selected data"

#: src/tables/DownloadAction.tsx:22
#~ msgid "TSV"
#~ msgstr "TSV"

#: src/tables/DownloadAction.tsx:23
#~ msgid "Excel (.xlsx)"
#~ msgstr "Excel (.xlsx)"

#: src/tables/DownloadAction.tsx:24
#~ msgid "Excel (.xls)"
#~ msgstr "Excel (.xls)"

#: src/tables/DownloadAction.tsx:36
#~ msgid "Download Data"
#~ msgstr "Download Data"

#: src/tables/Filter.tsx:75
msgid "Has Batch Code"
msgstr "Har batchkode"

#: src/tables/Filter.tsx:76
msgid "Show items which have a batch code"
msgstr "Vis elementer som har en batchkode"

#: src/tables/Filter.tsx:84
msgid "Filter items by batch code"
msgstr ""

#: src/tables/Filter.tsx:92
msgid "Is Serialized"
msgstr "Er serialisert"

#: src/tables/Filter.tsx:93
msgid "Show items which have a serial number"
msgstr "Vis elementer som har et serienummer"

#: src/tables/Filter.tsx:100
msgid "Serial"
msgstr ""

#: src/tables/Filter.tsx:101
msgid "Filter items by serial number"
msgstr ""

#: src/tables/Filter.tsx:106
#~ msgid "Show overdue orders"
#~ msgstr "Show overdue orders"

#: src/tables/Filter.tsx:109
msgid "Serial Below"
msgstr ""

#: src/tables/Filter.tsx:110
msgid "Show items with serial numbers less than or equal to a given value"
msgstr ""

#: src/tables/Filter.tsx:118
msgid "Serial Above"
msgstr ""

#: src/tables/Filter.tsx:119
msgid "Show items with serial numbers greater than or equal to a given value"
msgstr ""

#: src/tables/Filter.tsx:128
msgid "Assigned to me"
msgstr "Tilordnet meg"

#: src/tables/Filter.tsx:129
msgid "Show orders assigned to me"
msgstr "Vis ordre tildelt meg"

#: src/tables/Filter.tsx:136
#: src/tables/sales/SalesOrderAllocationTable.tsx:85
msgid "Outstanding"
msgstr "Utestående"

#: src/tables/Filter.tsx:137
msgid "Show outstanding items"
msgstr ""

#: src/tables/Filter.tsx:145
msgid "Show overdue items"
msgstr ""

#: src/tables/Filter.tsx:152
msgid "Minimum Date"
msgstr ""

#: src/tables/Filter.tsx:153
msgid "Show items after this date"
msgstr ""

#: src/tables/Filter.tsx:161
msgid "Maximum Date"
msgstr ""

#: src/tables/Filter.tsx:162
msgid "Show items before this date"
msgstr ""

#: src/tables/Filter.tsx:170
msgid "Created Before"
msgstr ""

#: src/tables/Filter.tsx:171
msgid "Show items created before this date"
msgstr ""

#: src/tables/Filter.tsx:179
msgid "Created After"
msgstr ""

#: src/tables/Filter.tsx:180
msgid "Show items created after this date"
msgstr ""

#: src/tables/Filter.tsx:188
msgid "Start Date Before"
msgstr ""

#: src/tables/Filter.tsx:189
msgid "Show items with a start date before this date"
msgstr ""

#: src/tables/Filter.tsx:197
msgid "Start Date After"
msgstr ""

#: src/tables/Filter.tsx:198
msgid "Show items with a start date after this date"
msgstr ""

#: src/tables/Filter.tsx:206
msgid "Target Date Before"
msgstr ""

#: src/tables/Filter.tsx:207
msgid "Show items with a target date before this date"
msgstr ""

#: src/tables/Filter.tsx:215
msgid "Target Date After"
msgstr ""

#: src/tables/Filter.tsx:216
msgid "Show items with a target date after this date"
msgstr ""

#: src/tables/Filter.tsx:224
msgid "Completed Before"
msgstr ""

#: src/tables/Filter.tsx:225
msgid "Show items completed before this date"
msgstr ""

#: src/tables/Filter.tsx:233
msgid "Completed After"
msgstr ""

#: src/tables/Filter.tsx:234
msgid "Show items completed after this date"
msgstr ""

#: src/tables/Filter.tsx:246
msgid "Has Project Code"
msgstr ""

#: src/tables/Filter.tsx:247
msgid "Show orders with an assigned project code"
msgstr ""

#: src/tables/Filter.tsx:256
msgid "Include Variants"
msgstr "Inkluder varianter"

#: src/tables/Filter.tsx:257
msgid "Include results for part variants"
msgstr ""

#: src/tables/Filter.tsx:267
#: src/tables/part/PartPurchaseOrdersTable.tsx:133
msgid "Filter by order status"
msgstr "Filtrer etter ordrestatus"

#: src/tables/Filter.tsx:279
msgid "Filter by project code"
msgstr ""

#: src/tables/Filter.tsx:312
msgid "Filter by responsible owner"
msgstr ""

#: src/tables/Filter.tsx:328
#: src/tables/settings/ApiTokenTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:191
msgid "Filter by user"
msgstr ""

#: src/tables/Filter.tsx:340
msgid "Filter by manufacturer"
msgstr ""

#: src/tables/Filter.tsx:353
msgid "Filter by supplier"
msgstr ""

#: src/tables/Filter.tsx:366
msgid "Filter by user who created the order"
msgstr ""

#: src/tables/Filter.tsx:374
msgid "Filter by user who issued the order"
msgstr ""

#: src/tables/Filter.tsx:382
msgid "Filter by part category"
msgstr ""

#: src/tables/Filter.tsx:393
msgid "Filter by stock location"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:59
msgid "Remove filter"
msgstr "Fjern filter"

#: src/tables/FilterSelectDrawer.tsx:102
#: src/tables/FilterSelectDrawer.tsx:104
#: src/tables/FilterSelectDrawer.tsx:151
msgid "Select filter value"
msgstr "Velg filterverdi"

#: src/tables/FilterSelectDrawer.tsx:116
msgid "Enter filter value"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:138
msgid "Select date value"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:260
msgid "Select filter"
msgstr "Velg filter"

#: src/tables/FilterSelectDrawer.tsx:261
msgid "Filter"
msgstr "Filter"

#: src/tables/FilterSelectDrawer.tsx:313
#: src/tables/InvenTreeTableHeader.tsx:257
msgid "Table Filters"
msgstr "Tabellfiltre"

#: src/tables/FilterSelectDrawer.tsx:346
msgid "Add Filter"
msgstr "Legg til filter"

#: src/tables/FilterSelectDrawer.tsx:355
msgid "Clear Filters"
msgstr "Fjern filtre"

#: src/tables/InvenTreeTable.tsx:44
#: src/tables/InvenTreeTable.tsx:479
msgid "No records found"
msgstr "Ingen poster funnet"

#: src/tables/InvenTreeTable.tsx:151
msgid "Error loading table options"
msgstr ""

#: src/tables/InvenTreeTable.tsx:250
#~ msgid "Failed to load table options"
#~ msgstr "Failed to load table options"

#: src/tables/InvenTreeTable.tsx:510
#~ msgid "Are you sure you want to delete the selected records?"
#~ msgstr "Are you sure you want to delete the selected records?"

#: src/tables/InvenTreeTable.tsx:520
msgid "Server returned incorrect data type"
msgstr "Serveren returnerte feil datatype"

#: src/tables/InvenTreeTable.tsx:535
#~ msgid "Deleted records"
#~ msgstr "Deleted records"

#: src/tables/InvenTreeTable.tsx:536
#~ msgid "Records were deleted successfully"
#~ msgstr "Records were deleted successfully"

#: src/tables/InvenTreeTable.tsx:545
#~ msgid "Failed to delete records"
#~ msgstr "Failed to delete records"

#: src/tables/InvenTreeTable.tsx:552
#~ msgid "This action cannot be undone!"
#~ msgstr "This action cannot be undone!"

#: src/tables/InvenTreeTable.tsx:553
msgid "Error loading table data"
msgstr ""

#: src/tables/InvenTreeTable.tsx:594
#: src/tables/InvenTreeTable.tsx:595
#~ msgid "Print actions"
#~ msgstr "Print actions"

#: src/tables/InvenTreeTable.tsx:655
#: src/tables/InvenTreeTable.tsx:656
#~ msgid "Barcode actions"
#~ msgstr "Barcode actions"

#: src/tables/InvenTreeTable.tsx:680
msgid "View details"
msgstr ""

#: src/tables/InvenTreeTable.tsx:712
#~ msgid "Table filters"
#~ msgstr "Table filters"

#: src/tables/InvenTreeTable.tsx:725
#~ msgid "Clear custom query filters"
#~ msgstr "Clear custom query filters"

#: src/tables/InvenTreeTableHeader.tsx:104
msgid "Delete Selected Items"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:108
msgid "Are you sure you want to delete the selected items?"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:110
#: src/tables/plugin/PluginListTable.tsx:316
msgid "This action cannot be undone"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:121
msgid "Items deleted"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:126
msgid "Failed to delete items"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:177
msgid "Custom table filters are active"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:203
#: src/tables/general/BarcodeScanTable.tsx:93
msgid "Delete selected records"
msgstr "Slett valgte oppføringer"

#: src/tables/InvenTreeTableHeader.tsx:223
msgid "Refresh data"
msgstr "Oppdater data"

#: src/tables/InvenTreeTableHeader.tsx:269
msgid "Active Filters"
msgstr ""

#: src/tables/TableHoverCard.tsx:35
#~ msgid "item-{idx}"
#~ msgstr "item-{idx}"

#: src/tables/UploadAction.tsx:7
#~ msgid "Upload Data"
#~ msgstr "Upload Data"

#: src/tables/bom/BomTable.tsx:102
msgid "This BOM item is defined for a different parent"
msgstr "Denne BOM-artikkelen er definert for en annen overordnet del"

#: src/tables/bom/BomTable.tsx:118
msgid "Part Information"
msgstr "Delinformasjon"

#: src/tables/bom/BomTable.tsx:121
msgid "This BOM item has not been validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:228
msgid "Substitutes"
msgstr ""

#: src/tables/bom/BomTable.tsx:297
#: src/tables/build/BuildLineTable.tsx:268
#: src/tables/part/PartTable.tsx:132
msgid "External stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:301
#~ msgid "Create BOM Item"
#~ msgstr "Create BOM Item"

#: src/tables/bom/BomTable.tsx:305
#: src/tables/build/BuildLineTable.tsx:231
msgid "Includes substitute stock"
msgstr "Inkluderer erstatningsbeholdning"

#: src/tables/bom/BomTable.tsx:310
#~ msgid "Show asssmbled items"
#~ msgstr "Show asssmbled items"

#: src/tables/bom/BomTable.tsx:314
#: src/tables/build/BuildLineTable.tsx:241
#: src/tables/sales/SalesOrderLineItemTable.tsx:158
msgid "Includes variant stock"
msgstr "Inkluderer variantbeholdning"

#: src/tables/bom/BomTable.tsx:331
#: src/tables/part/PartTable.tsx:101
msgid "Building"
msgstr "Produseres"

#: src/tables/bom/BomTable.tsx:331
#~ msgid "Edit Bom Item"
#~ msgstr "Edit Bom Item"

#: src/tables/bom/BomTable.tsx:333
#~ msgid "Bom item updated"
#~ msgstr "Bom item updated"

#: src/tables/bom/BomTable.tsx:340
#: src/tables/part/PartTable.tsx:158
#: src/tables/sales/SalesOrderLineItemTable.tsx:181
#: src/tables/stock/StockItemTable.tsx:223
msgid "Stock Information"
msgstr "Lagerinformasjon"

#: src/tables/bom/BomTable.tsx:348
#~ msgid "Delete Bom Item"
#~ msgstr "Delete Bom Item"

#: src/tables/bom/BomTable.tsx:349
#~ msgid "Bom item deleted"
#~ msgstr "Bom item deleted"

#: src/tables/bom/BomTable.tsx:351
#~ msgid "Are you sure you want to remove this BOM item?"
#~ msgstr "Are you sure you want to remove this BOM item?"

#: src/tables/bom/BomTable.tsx:354
#~ msgid "Validate BOM line"
#~ msgstr "Validate BOM line"

#: src/tables/bom/BomTable.tsx:374
#: src/tables/build/BuildLineTable.tsx:478
#: src/tables/build/BuildLineTable.tsx:518
msgid "Consumable item"
msgstr "Forbruksvare"

#: src/tables/bom/BomTable.tsx:377
msgid "No available stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:395
#: src/tables/build/BuildLineTable.tsx:211
msgid "Show testable items"
msgstr ""

#: src/tables/bom/BomTable.tsx:400
msgid "Show trackable items"
msgstr "Vis sporbare deler"

#: src/tables/bom/BomTable.tsx:405
#: src/tables/build/BuildLineTable.tsx:206
msgid "Show assembled items"
msgstr ""

#: src/tables/bom/BomTable.tsx:410
#: src/tables/build/BuildLineTable.tsx:191
msgid "Show items with available stock"
msgstr "Vis elementer med tilgjengelig lagerbeholdning"

#: src/tables/bom/BomTable.tsx:415
msgid "Show items on order"
msgstr "Vis elementer i bestilling"

#: src/tables/bom/BomTable.tsx:419
msgid "Validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:420
msgid "Show validated items"
msgstr "Vis godkjente elementer"

#: src/tables/bom/BomTable.tsx:424
#: src/tables/bom/UsedInTable.tsx:80
msgid "Inherited"
msgstr ""

#: src/tables/bom/BomTable.tsx:425
#: src/tables/bom/UsedInTable.tsx:81
msgid "Show inherited items"
msgstr "Vis arvede elementer"

#: src/tables/bom/BomTable.tsx:429
msgid "Allow Variants"
msgstr ""

#: src/tables/bom/BomTable.tsx:430
msgid "Show items which allow variant substitution"
msgstr ""

#: src/tables/bom/BomTable.tsx:434
#: src/tables/bom/UsedInTable.tsx:85
#: src/tables/build/BuildLineTable.tsx:200
msgid "Optional"
msgstr "Valgfritt"

#: src/tables/bom/BomTable.tsx:435
#: src/tables/bom/UsedInTable.tsx:86
msgid "Show optional items"
msgstr "Vis valgfrie elementer"

#: src/tables/bom/BomTable.tsx:439
#: src/tables/build/BuildLineTable.tsx:195
msgid "Consumable"
msgstr "Forbruksvare"

#: src/tables/bom/BomTable.tsx:440
msgid "Show consumable items"
msgstr "Vis forbruksartikler"

#: src/tables/bom/BomTable.tsx:444
#: src/tables/part/PartTable.tsx:300
msgid "Has Pricing"
msgstr "Har prising"

#: src/tables/bom/BomTable.tsx:445
msgid "Show items with pricing"
msgstr "Vis varer med priser"

#: src/tables/bom/BomTable.tsx:467
#: src/tables/bom/BomTable.tsx:596
msgid "Import BOM Data"
msgstr ""

#: src/tables/bom/BomTable.tsx:477
#: src/tables/bom/BomTable.tsx:603
msgid "Add BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:482
msgid "BOM item created"
msgstr ""

#: src/tables/bom/BomTable.tsx:489
msgid "Edit BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:491
msgid "BOM item updated"
msgstr ""

#: src/tables/bom/BomTable.tsx:498
msgid "Delete BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:499
msgid "BOM item deleted"
msgstr ""

#: src/tables/bom/BomTable.tsx:519
msgid "BOM item validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:528
msgid "Failed to validate BOM item"
msgstr ""

#: src/tables/bom/BomTable.tsx:540
msgid "View BOM"
msgstr "Vis BOM"

#: src/tables/bom/BomTable.tsx:551
msgid "Validate BOM Line"
msgstr ""

#: src/tables/bom/BomTable.tsx:570
msgid "Edit Substitutes"
msgstr "Rediger erstatninger"

#: src/tables/bom/BomTable.tsx:624
msgid "Bill of materials cannot be edited, as the part is locked"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:34
#: src/tables/build/BuildLineTable.tsx:205
#: src/tables/part/ParametricPartTable.tsx:360
#: src/tables/part/PartBuildAllocationsTable.tsx:60
#: src/tables/part/PartTable.tsx:196
#: src/tables/stock/StockItemTable.tsx:334
msgid "Assembly"
msgstr "Sammenstilling"

#: src/tables/bom/UsedInTable.tsx:91
msgid "Show active assemblies"
msgstr "Vis aktive sammenstillinger"

#: src/tables/bom/UsedInTable.tsx:95
#: src/tables/part/PartTable.tsx:226
#: src/tables/part/PartVariantTable.tsx:30
msgid "Trackable"
msgstr "Sporbar"

#: src/tables/bom/UsedInTable.tsx:96
msgid "Show trackable assemblies"
msgstr "Vis sporbare sammenstillinger"

#: src/tables/build/BuildAllocatedStockTable.tsx:67
msgid "Allocated to Output"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:68
msgid "Show items allocated to a build output"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:73
#: src/tables/build/BuildOrderTable.tsx:197
#: src/tables/part/PartPurchaseOrdersTable.tsx:140
#: src/tables/sales/ReturnOrderTable.tsx:100
#: src/tables/sales/SalesOrderAllocationTable.tsx:101
#: src/tables/sales/SalesOrderTable.tsx:101
#~ msgid "Include orders for part variants"
#~ msgstr "Include orders for part variants"

#: src/tables/build/BuildAllocatedStockTable.tsx:97
#: src/tables/part/PartBuildAllocationsTable.tsx:84
#: src/tables/part/PartPurchaseOrdersTable.tsx:132
#: src/tables/part/PartSalesAllocationsTable.tsx:69
#: src/tables/sales/SalesOrderAllocationTable.tsx:119
msgid "Order Status"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:164
#~ msgid "Edit Build Item"
#~ msgstr "Edit Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:167
#: src/tables/build/BuildLineTable.tsx:631
msgid "Edit Stock Allocation"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:174
#~ msgid "Delete Build Item"
#~ msgstr "Delete Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:180
#: src/tables/build/BuildLineTable.tsx:644
msgid "Delete Stock Allocation"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:232
msgid "Consume"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:59
#~ msgid "Show lines with available stock"
#~ msgstr "Show lines with available stock"

#: src/tables/build/BuildLineTable.tsx:106
msgid "View Stock Item"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:181
msgid "Show fully allocated lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:186
msgid "Show fully consumed lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:189
#~ msgid "Show allocated lines"
#~ msgstr "Show allocated lines"

#: src/tables/build/BuildLineTable.tsx:196
msgid "Show consumable lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:201
msgid "Show optional lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:210
#: src/tables/part/PartTable.tsx:220
msgid "Testable"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:215
#: src/tables/stock/StockItemTable.tsx:393
msgid "Tracked"
msgstr "Spores"

#: src/tables/build/BuildLineTable.tsx:216
msgid "Show tracked lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:250
#: src/tables/sales/SalesOrderLineItemTable.tsx:164
msgid "In production"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:278
msgid "Insufficient stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:294
#: src/tables/sales/SalesOrderLineItemTable.tsx:152
#: src/tables/stock/StockItemTable.tsx:192
msgid "No stock available"
msgstr "Ingen lagerbeholdning tilgjengelig"

#: src/tables/build/BuildLineTable.tsx:360
msgid "Gets Inherited"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:373
msgid "Unit Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:389
msgid "Required Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:400
msgid "Setup Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:409
msgid "Attrition"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:417
msgid "Rounding Multiple"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:426
msgid "BOM Information"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:496
#: src/tables/part/PartBuildAllocationsTable.tsx:102
msgid "Fully allocated"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:544
#: src/tables/sales/SalesOrderLineItemTable.tsx:290
msgid "Create Build Order"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:573
msgid "Auto allocation in progress"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:576
#: src/tables/build/BuildLineTable.tsx:781
msgid "Auto Allocate Stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:577
msgid "Automatically allocate stock to this build according to the selected options"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:597
#: src/tables/build/BuildLineTable.tsx:611
#: src/tables/build/BuildLineTable.tsx:730
#: src/tables/build/BuildLineTable.tsx:831
#: src/tables/build/BuildOutputTable.tsx:359
#: src/tables/build/BuildOutputTable.tsx:364
msgid "Deallocate Stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:613
msgid "Deallocate all untracked stock for this build order"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:615
msgid "Deallocate stock from the selected line item"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:619
msgid "Stock has been deallocated"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:750
msgid "Build Stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:763
#: src/tables/sales/SalesOrderLineItemTable.tsx:377
msgid "View Part"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:116
#~ msgid "Cascade"
#~ msgstr "Cascade"

#: src/tables/build/BuildOrderTable.tsx:117
#~ msgid "Display recursive child orders"
#~ msgstr "Display recursive child orders"

#: src/tables/build/BuildOrderTable.tsx:121
#~ msgid "Show active orders"
#~ msgstr "Show active orders"

#: src/tables/build/BuildOrderTable.tsx:122
#~ msgid "Show overdue status"
#~ msgstr "Show overdue status"

#: src/tables/build/BuildOrderTable.tsx:127
#~ msgid "Show outstanding orders"
#~ msgstr "Show outstanding orders"

#: src/tables/build/BuildOrderTable.tsx:139
#: src/tables/purchasing/PurchaseOrderTable.tsx:71
#: src/tables/sales/ReturnOrderTable.tsx:62
#: src/tables/sales/SalesOrderTable.tsx:69
#~ msgid "Filter by whether the purchase order has a project code"
#~ msgstr "Filter by whether the purchase order has a project code"

#: src/tables/build/BuildOrderTable.tsx:167
#: src/tables/purchasing/PurchaseOrderTable.tsx:83
#: src/tables/sales/ReturnOrderTable.tsx:79
#: src/tables/sales/SalesOrderTable.tsx:80
msgid "Has Target Date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:168
#: src/tables/purchasing/PurchaseOrderTable.tsx:84
#: src/tables/sales/ReturnOrderTable.tsx:80
#: src/tables/sales/SalesOrderTable.tsx:81
msgid "Show orders with a target date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:173
#: src/tables/purchasing/PurchaseOrderTable.tsx:89
#: src/tables/sales/ReturnOrderTable.tsx:85
#: src/tables/sales/SalesOrderTable.tsx:86
msgid "Has Start Date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:174
#: src/tables/purchasing/PurchaseOrderTable.tsx:90
#: src/tables/sales/ReturnOrderTable.tsx:86
#: src/tables/sales/SalesOrderTable.tsx:87
msgid "Show orders with a start date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:179
#~ msgid "Filter by user who issued this order"
#~ msgstr "Filter by user who issued this order"

#: src/tables/build/BuildOrderTestTable.tsx:85
#: src/tables/build/BuildOrderTestTable.tsx:163
#: src/tables/build/BuildOrderTestTable.tsx:283
#: src/tables/build/BuildOrderTestTable.tsx:297
#: src/tables/stock/StockItemTestResultTable.tsx:293
#: src/tables/stock/StockItemTestResultTable.tsx:365
#: src/tables/stock/StockItemTestResultTable.tsx:426
msgid "Add Test Result"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:92
#: src/tables/stock/StockItemTestResultTable.tsx:295
msgid "Test result added"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:124
msgid "Add Test Results"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:134
msgid "Test results added"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:162
#: src/tables/stock/StockItemTestResultTable.tsx:191
msgid "No Result"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:274
msgid "Show build outputs currently in production"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:104
msgid "Build Output Stock Allocation"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:161
#~ msgid "Delete build output"
#~ msgstr "Delete build output"

#: src/tables/build/BuildOutputTable.tsx:294
#: src/tables/build/BuildOutputTable.tsx:479
msgid "Add Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:297
msgid "Build output created"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:304
#~ msgid "Edit build output"
#~ msgstr "Edit build output"

#: src/tables/build/BuildOutputTable.tsx:350
#: src/tables/build/BuildOutputTable.tsx:553
msgid "Edit Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:366
msgid "This action will deallocate all stock from the selected build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:391
msgid "Serialize Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:409
#: src/tables/stock/StockItemTable.tsx:329
msgid "Filter by stock status"
msgstr "Filtrer etter lagerstatus"

#: src/tables/build/BuildOutputTable.tsx:446
msgid "Complete selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:457
msgid "Scrap selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:468
msgid "Cancel selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:498
msgid "View Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:504
msgid "Allocate"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:505
msgid "Allocate stock to build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:518
msgid "Deallocate"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:519
msgid "Deallocate stock from build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:533
msgid "Serialize build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:544
msgid "Complete build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:560
msgid "Scrap"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:561
msgid "Scrap build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:571
msgid "Cancel build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:620
msgid "Allocated Lines"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:635
msgid "Required Tests"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:710
msgid "External Build"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:712
msgid "This build order is fulfilled by an external purchase order"
msgstr ""

#: src/tables/company/AddressTable.tsx:122
#: src/tables/company/AddressTable.tsx:187
msgid "Add Address"
msgstr "Legg til adresse"

#: src/tables/company/AddressTable.tsx:127
msgid "Address created"
msgstr "Adresse opprettet"

#: src/tables/company/AddressTable.tsx:136
msgid "Edit Address"
msgstr "Rediger adresse"

#: src/tables/company/AddressTable.tsx:144
msgid "Delete Address"
msgstr "Slett adresse"

#: src/tables/company/AddressTable.tsx:145
msgid "Are you sure you want to delete this address?"
msgstr "Er du sikker på at du vil slette denne adressen?"

#: src/tables/company/CompanyTable.tsx:70
#: src/tables/company/CompanyTable.tsx:120
msgid "Add Company"
msgstr ""

#: src/tables/company/CompanyTable.tsx:71
#~ msgid "New Company"
#~ msgstr "New Company"

#: src/tables/company/CompanyTable.tsx:92
msgid "Show active companies"
msgstr ""

#: src/tables/company/CompanyTable.tsx:97
msgid "Show companies which are suppliers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:102
msgid "Show companies which are manufacturers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:107
msgid "Show companies which are customers"
msgstr ""

#: src/tables/company/ContactTable.tsx:99
msgid "Edit Contact"
msgstr "Rediger kontakt"

#: src/tables/company/ContactTable.tsx:106
msgid "Add Contact"
msgstr ""

#: src/tables/company/ContactTable.tsx:117
msgid "Delete Contact"
msgstr "Slett kontakt"

#: src/tables/company/ContactTable.tsx:158
msgid "Add contact"
msgstr "Legg til kontakt"

#: src/tables/general/AttachmentTable.tsx:108
msgid "Uploading file {filename}"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:139
#~ msgid "File uploaded"
#~ msgstr "File uploaded"

#: src/tables/general/AttachmentTable.tsx:140
#~ msgid "File {0} uploaded successfully"
#~ msgstr "File {0} uploaded successfully"

#: src/tables/general/AttachmentTable.tsx:160
#: src/tables/general/AttachmentTable.tsx:174
msgid "Uploading File"
msgstr "Laster opp fil"

#: src/tables/general/AttachmentTable.tsx:185
msgid "File Uploaded"
msgstr "Fil lastet opp"

#: src/tables/general/AttachmentTable.tsx:186
msgid "File {name} uploaded successfully"
msgstr "Filen {name} ble lastet opp"

#: src/tables/general/AttachmentTable.tsx:202
msgid "File could not be uploaded"
msgstr "Kunne ikke laste opp filen"

#: src/tables/general/AttachmentTable.tsx:253
msgid "Upload Attachment"
msgstr "Last opp vedlegg"

#: src/tables/general/AttachmentTable.tsx:254
#~ msgid "Upload attachment"
#~ msgstr "Upload attachment"

#: src/tables/general/AttachmentTable.tsx:263
msgid "Edit Attachment"
msgstr "Redigere vedlegg"

#: src/tables/general/AttachmentTable.tsx:277
msgid "Delete Attachment"
msgstr "Slett vedlegg"

#: src/tables/general/AttachmentTable.tsx:287
msgid "Is Link"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:288
msgid "Show link attachments"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:292
msgid "Is File"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:293
msgid "Show file attachments"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:302
msgid "Add attachment"
msgstr "Legg til vedlegg"

#: src/tables/general/AttachmentTable.tsx:313
msgid "Add external link"
msgstr "Ny ekstern lenke"

#: src/tables/general/AttachmentTable.tsx:361
msgid "No attachments found"
msgstr "Ingen vedlegg funnet"

#: src/tables/general/AttachmentTable.tsx:400
msgid "Drag attachment file here to upload"
msgstr ""

#: src/tables/general/BarcodeScanTable.tsx:35
msgid "Item"
msgstr "Artikkel"

#: src/tables/general/BarcodeScanTable.tsx:50
msgid "Model"
msgstr ""

#: src/tables/general/BarcodeScanTable.tsx:60
#: src/tables/settings/BarcodeScanHistoryTable.tsx:75
#: src/tables/settings/EmailTable.tsx:105
#: src/tables/settings/ErrorTable.tsx:59
msgid "Timestamp"
msgstr ""

#: src/tables/general/BarcodeScanTable.tsx:75
msgid "View Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:91
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:288
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:397
#: src/tables/sales/ReturnOrderLineItemTable.tsx:80
#: src/tables/sales/ReturnOrderLineItemTable.tsx:183
#: src/tables/sales/SalesOrderLineItemTable.tsx:231
#: src/tables/sales/SalesOrderLineItemTable.tsx:334
msgid "Add Line Item"
msgstr "Legg til ordrelinje"

#: src/tables/general/ExtraLineItemTable.tsx:104
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:309
#: src/tables/sales/ReturnOrderLineItemTable.tsx:93
#: src/tables/sales/SalesOrderLineItemTable.tsx:250
msgid "Edit Line Item"
msgstr "Rediger ordrelinje"

#: src/tables/general/ExtraLineItemTable.tsx:113
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:318
#: src/tables/sales/ReturnOrderLineItemTable.tsx:102
#: src/tables/sales/SalesOrderLineItemTable.tsx:259
msgid "Delete Line Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:151
msgid "Add Extra Line Item"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:206
msgid "Machine restarted"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:216
#: src/tables/machine/MachineListTable.tsx:264
msgid "Edit machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:230
#: src/tables/machine/MachineListTable.tsx:268
msgid "Delete machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:231
msgid "Machine successfully deleted."
msgstr ""

#. placeholder {0}: machine?.name ?? 'unknown'
#: src/tables/machine/MachineListTable.tsx:235
msgid "Are you sure you want to remove the machine \"{0}\"?"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:252
msgid "Machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:257
#: src/tables/machine/MachineListTable.tsx:444
msgid "Restart required"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:261
msgid "Machine Actions"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:273
msgid "Restart"
msgstr "Start på nytt"

#: src/tables/machine/MachineListTable.tsx:275
msgid "Restart machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:277
msgid "manual restart required"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:291
#~ msgid "Machine information"
#~ msgstr "Machine information"

#: src/tables/machine/MachineListTable.tsx:295
msgid "Machine Information"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:305
#: src/tables/machine/MachineListTable.tsx:611
msgid "Machine Type"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:318
msgid "Machine Driver"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:333
msgid "Initialized"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:362
#: src/tables/machine/MachineTypeTable.tsx:289
msgid "No errors reported"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:381
msgid "Machine Settings"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:397
msgid "Driver Settings"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:494
#~ msgid "Create machine"
#~ msgstr "Create machine"

#: src/tables/machine/MachineListTable.tsx:517
msgid "Add Machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:559
msgid "Add machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:561
#~ msgid "Machine detail"
#~ msgstr "Machine detail"

#: src/tables/machine/MachineListTable.tsx:573
msgid "Machine Detail"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:620
msgid "Driver"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:77
msgid "Builtin driver"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:95
msgid "Not Found"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:98
msgid "Machine type not found."
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:99
#~ msgid "Machine type information"
#~ msgstr "Machine type information"

#: src/tables/machine/MachineTypeTable.tsx:108
msgid "Machine Type Information"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:123
#: src/tables/machine/MachineTypeTable.tsx:237
msgid "Slug"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:134
#: src/tables/machine/MachineTypeTable.tsx:258
msgid "Provider plugin"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:146
#: src/tables/machine/MachineTypeTable.tsx:270
msgid "Provider file"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:148
#~ msgid "Available drivers"
#~ msgstr "Available drivers"

#: src/tables/machine/MachineTypeTable.tsx:161
msgid "Available Drivers"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:216
msgid "Machine driver not found."
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:224
msgid "Machine driver information"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:244
msgid "Machine type"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:338
#~ msgid "Machine type detail"
#~ msgstr "Machine type detail"

#: src/tables/machine/MachineTypeTable.tsx:344
msgid "Builtin type"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:348
#~ msgid "Machine driver detail"
#~ msgstr "Machine driver detail"

#: src/tables/machine/MachineTypeTable.tsx:353
msgid "Machine Type Detail"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:363
msgid "Machine Driver Detail"
msgstr ""

#: src/tables/notifications/NotificationTable.tsx:26
msgid "Age"
msgstr "Alder"

#: src/tables/notifications/NotificationTable.tsx:37
msgid "Notification"
msgstr "Varsel"

#: src/tables/notifications/NotificationTable.tsx:41
#: src/tables/plugin/PluginErrorTable.tsx:37
#: src/tables/settings/ErrorTable.tsx:50
msgid "Message"
msgstr "Melding"

#: src/tables/part/ParametricPartTable.tsx:78
msgid "Click to edit"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:82
#~ msgid "Edit parameter"
#~ msgstr "Edit parameter"

#: src/tables/part/ParametricPartTable.tsx:240
msgid "Add Part Parameter"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:254
#: src/tables/part/PartParameterTable.tsx:172
#: src/tables/part/PartParameterTable.tsx:195
msgid "Edit Part Parameter"
msgstr "Rediger del-parameter"

#: src/tables/part/ParametricPartTable.tsx:351
msgid "Show active parts"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:356
msgid "Show locked parts"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:361
msgid "Show assembly parts"
msgstr ""

#: src/tables/part/ParametricPartTableFilters.tsx:67
msgid "True"
msgstr ""

#: src/tables/part/ParametricPartTableFilters.tsx:68
msgid "False"
msgstr ""

#: src/tables/part/ParametricPartTableFilters.tsx:73
#: src/tables/part/ParametricPartTableFilters.tsx:97
msgid "Select a choice"
msgstr ""

#: src/tables/part/ParametricPartTableFilters.tsx:116
msgid "Enter a value"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:64
msgid "Assembly IPN"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:73
msgid "Part IPN"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:91
msgid "Required Stock"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:124
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:354
msgid "View Build Order"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:51
msgid "You are subscribed to notifications for this category"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:84
#: src/tables/part/PartTable.tsx:208
msgid "Include Subcategories"
msgstr "Inkluder underkategorier"

#: src/tables/part/PartCategoryTable.tsx:85
msgid "Include subcategories in results"
msgstr "Inkluder underkategorier i resultatene"

#: src/tables/part/PartCategoryTable.tsx:90
msgid "Show structural categories"
msgstr "Vis strukturelle kategorier"

#: src/tables/part/PartCategoryTable.tsx:95
msgid "Show categories to which the user is subscribed"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:104
msgid "New Part Category"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:130
msgid "Set Parent Category"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:148
#: src/tables/stock/StockLocationTable.tsx:147
msgid "Set Parent"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:150
msgid "Set parent category for the selected items"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:161
msgid "Add Part Category"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:42
#: src/tables/part/PartCategoryTemplateTable.tsx:136
msgid "Add Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:50
msgid "Edit Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:58
msgid "Delete Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:80
msgid "Parameter Template"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:93
#~ msgid "[{0}]"
#~ msgstr "[{0}]"

#: src/tables/part/PartParameterTable.tsx:108
msgid "Internal Units"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:127
#: src/tables/part/PartParameterTable.tsx:146
msgid "Updated By"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:147
msgid "Filter by user who last updated the parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:156
msgid "New Part Parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:181
#: src/tables/part/PartParameterTable.tsx:203
msgid "Delete Part Parameter"
msgstr "Slett del-parameter"

#: src/tables/part/PartParameterTable.tsx:221
msgid "Add parameter"
msgstr "Legg til parameter"

#: src/tables/part/PartParameterTable.tsx:240
msgid "Part parameters cannot be edited, as the part is locked"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:36
msgid "Checkbox"
msgstr "Sjekkboks"

#: src/tables/part/PartParameterTemplateTable.tsx:37
msgid "Show checkbox templates"
msgstr "Vis sjekkboks-maler"

#: src/tables/part/PartParameterTemplateTable.tsx:41
msgid "Has choices"
msgstr "Har valg"

#: src/tables/part/PartParameterTemplateTable.tsx:42
msgid "Show templates with choices"
msgstr "Vis maler med valg"

#: src/tables/part/PartParameterTemplateTable.tsx:46
#: src/tables/part/PartTable.tsx:232
msgid "Has Units"
msgstr "Har enheter"

#: src/tables/part/PartParameterTemplateTable.tsx:47
msgid "Show templates with units"
msgstr "Vis maler med enheter"

#: src/tables/part/PartParameterTemplateTable.tsx:91
#: src/tables/part/PartParameterTemplateTable.tsx:166
msgid "Add Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:105
msgid "Duplicate Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:117
msgid "Edit Parameter Template"
msgstr "Rediger parametermal"

#: src/tables/part/PartParameterTemplateTable.tsx:128
msgid "Delete Parameter Template"
msgstr "Slett parametermal"

#: src/tables/part/PartParameterTemplateTable.tsx:141
#~ msgid "Add parameter template"
#~ msgstr "Add parameter template"

#: src/tables/part/PartPurchaseOrdersTable.tsx:79
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:190
msgid "Total Quantity"
msgstr "Totalt Antall"

#: src/tables/part/PartPurchaseOrdersTable.tsx:123
msgid "Show pending orders"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:128
msgid "Show received items"
msgstr ""

#: src/tables/part/PartSalesAllocationsTable.tsx:90
msgid "View Sales Order"
msgstr ""

#: src/tables/part/PartTable.tsx:86
msgid "Minimum stock"
msgstr "Minimumsbeholdning"

#: src/tables/part/PartTable.tsx:185
msgid "Filter by part active status"
msgstr "Filtrer etter del aktiv-status"

#: src/tables/part/PartTable.tsx:191
msgid "Filter by part locked status"
msgstr ""

#: src/tables/part/PartTable.tsx:197
msgid "Filter by assembly attribute"
msgstr "Filtrer etter sammenstillingsattributt"

#: src/tables/part/PartTable.tsx:202
msgid "BOM Valid"
msgstr ""

#: src/tables/part/PartTable.tsx:203
msgid "Filter by parts with a valid BOM"
msgstr ""

#: src/tables/part/PartTable.tsx:209
msgid "Include parts in subcategories"
msgstr "Inkluder deler i underkategorier"

#: src/tables/part/PartTable.tsx:215
msgid "Filter by component attribute"
msgstr "Filtrer etter komponentattributt"

#: src/tables/part/PartTable.tsx:221
msgid "Filter by testable attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:227
msgid "Filter by trackable attribute"
msgstr "Filtrer etter sporbar attributt"

#: src/tables/part/PartTable.tsx:233
msgid "Filter by parts which have units"
msgstr "Filtrer etter deler som har enheter"

#: src/tables/part/PartTable.tsx:238
msgid "Has IPN"
msgstr "Har IPN"

#: src/tables/part/PartTable.tsx:239
msgid "Filter by parts which have an internal part number"
msgstr "Filtrer etter deler som har internt delnummer"

#: src/tables/part/PartTable.tsx:244
msgid "Has Stock"
msgstr "Har beholdning"

#: src/tables/part/PartTable.tsx:245
msgid "Filter by parts which have stock"
msgstr "Filtrer etter deler som har lagerbeholdning"

#: src/tables/part/PartTable.tsx:251
msgid "Filter by parts which have low stock"
msgstr "Filtrer etter deler som har lav lagerbeholdning"

#: src/tables/part/PartTable.tsx:256
msgid "Purchaseable"
msgstr "Kjøpbar"

#: src/tables/part/PartTable.tsx:257
msgid "Filter by parts which are purchaseable"
msgstr "Filtrer etter deler som kan kjøpes"

#: src/tables/part/PartTable.tsx:262
msgid "Salable"
msgstr "Salgbar"

#: src/tables/part/PartTable.tsx:263
msgid "Filter by parts which are salable"
msgstr "Filtrer etter deler som kan selges"

#: src/tables/part/PartTable.tsx:268
#: src/tables/part/PartTable.tsx:272
#: src/tables/part/PartVariantTable.tsx:25
msgid "Virtual"
msgstr "Virtuell"

#: src/tables/part/PartTable.tsx:269
msgid "Filter by parts which are virtual"
msgstr "Filtrer etter deler som er virtuelle"

#: src/tables/part/PartTable.tsx:273
msgid "Not Virtual"
msgstr "Ikke virtuell"

#: src/tables/part/PartTable.tsx:278
msgid "Is Template"
msgstr ""

#: src/tables/part/PartTable.tsx:279
msgid "Filter by parts which are templates"
msgstr ""

#: src/tables/part/PartTable.tsx:284
msgid "Is Variant"
msgstr ""

#: src/tables/part/PartTable.tsx:285
msgid "Filter by parts which are variants"
msgstr ""

#: src/tables/part/PartTable.tsx:290
msgid "Is Revision"
msgstr ""

#: src/tables/part/PartTable.tsx:291
msgid "Filter by parts which are revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:295
msgid "Has Revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:296
msgid "Filter by parts which have revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:301
msgid "Filter by parts which have pricing information"
msgstr ""

#: src/tables/part/PartTable.tsx:307
msgid "Filter by parts which have available stock"
msgstr ""

#: src/tables/part/PartTable.tsx:313
msgid "Filter by parts to which the user is subscribed"
msgstr ""

#: src/tables/part/PartTable.tsx:322
#~ msgid "Has Stocktake"
#~ msgstr "Has Stocktake"

#: src/tables/part/PartTable.tsx:323
#~ msgid "Filter by parts which have stocktake information"
#~ msgstr "Filter by parts which have stocktake information"

#: src/tables/part/PartTable.tsx:363
#: src/tables/part/PartTable.tsx:397
msgid "Set Category"
msgstr ""

#: src/tables/part/PartTable.tsx:399
msgid "Set category for selected parts"
msgstr ""

#: src/tables/part/PartTable.tsx:409
msgid "Order selected parts"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:56
msgid "Test is defined for a parent template part"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:70
msgid "Template Details"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:80
msgid "Results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:113
msgid "Show required tests"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:118
msgid "Show enabled tests"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:122
msgid "Requires Value"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:123
msgid "Show tests that require a value"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:127
msgid "Requires Attachment"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:128
msgid "Show tests that require an attachment"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:132
msgid "Include Inherited"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:133
msgid "Show tests from inherited templates"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:137
msgid "Has Results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:138
msgid "Show tests which have recorded results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:160
#: src/tables/part/PartTestTemplateTable.tsx:243
msgid "Add Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:176
msgid "Edit Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:187
msgid "Delete Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:189
msgid "This action cannot be reversed"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:191
msgid "Any tests results associated with this template will be deleted"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:209
msgid "View Parent Part"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:263
msgid "Part templates cannot be edited, as the part is locked"
msgstr ""

#: src/tables/part/PartThumbTable.tsx:224
msgid "Select"
msgstr "Velg"

#: src/tables/part/PartVariantTable.tsx:16
msgid "Show active variants"
msgstr "Vis aktive varianter"

#: src/tables/part/PartVariantTable.tsx:20
msgid "Template"
msgstr "Mal"

#: src/tables/part/PartVariantTable.tsx:21
msgid "Show template variants"
msgstr "Vis malvarianter"

#: src/tables/part/PartVariantTable.tsx:26
msgid "Show virtual variants"
msgstr "Vis virtuelle varianter"

#: src/tables/part/PartVariantTable.tsx:31
msgid "Show trackable variants"
msgstr "Vis sporbare varianter"

#: src/tables/part/RelatedPartTable.tsx:104
#: src/tables/part/RelatedPartTable.tsx:137
msgid "Add Related Part"
msgstr "Legg til relatert del"

#: src/tables/part/RelatedPartTable.tsx:109
#~ msgid "Add related part"
#~ msgstr "Add related part"

#: src/tables/part/RelatedPartTable.tsx:119
msgid "Delete Related Part"
msgstr "Slett relatert del"

#: src/tables/part/RelatedPartTable.tsx:126
msgid "Edit Related Part"
msgstr ""

#: src/tables/part/SelectionListTable.tsx:64
#: src/tables/part/SelectionListTable.tsx:115
msgid "Add Selection List"
msgstr ""

#: src/tables/part/SelectionListTable.tsx:76
msgid "Edit Selection List"
msgstr ""

#: src/tables/part/SelectionListTable.tsx:84
msgid "Delete Selection List"
msgstr ""

#: src/tables/plugin/PluginErrorTable.tsx:29
msgid "Stage"
msgstr "Stadium"

#: src/tables/plugin/PluginListTable.tsx:43
msgid "Plugin is active"
msgstr "Utvidelsen er aktiv"

#: src/tables/plugin/PluginListTable.tsx:49
msgid "Plugin is inactive"
msgstr "Utvidelsen er inaktiv"

#: src/tables/plugin/PluginListTable.tsx:56
msgid "Plugin is not installed"
msgstr "Utvidelsen er ikke installert"

#: src/tables/plugin/PluginListTable.tsx:78
#: src/tables/settings/ExportSessionTable.tsx:33
msgid "Plugin"
msgstr "Utvidelse"

#: src/tables/plugin/PluginListTable.tsx:95
#~ msgid "Plugin with key {pluginKey} not found"
#~ msgstr "Plugin with key {pluginKey} not found"

#: src/tables/plugin/PluginListTable.tsx:97
#~ msgid "An error occurred while fetching plugin details"
#~ msgstr "An error occurred while fetching plugin details"

#: src/tables/plugin/PluginListTable.tsx:106
#: src/tables/plugin/PluginListTable.tsx:422
msgid "Mandatory"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:113
#~ msgid "Plugin with id {id} not found"
#~ msgstr "Plugin with id {id} not found"

#: src/tables/plugin/PluginListTable.tsx:120
msgid "Description not available"
msgstr "Beskrivelse ikke tilgjengelig"

#: src/tables/plugin/PluginListTable.tsx:122
#~ msgid "Plugin information"
#~ msgstr "Plugin information"

#: src/tables/plugin/PluginListTable.tsx:134
#~ msgid "Plugin Actions"
#~ msgstr "Plugin Actions"

#: src/tables/plugin/PluginListTable.tsx:138
#: src/tables/plugin/PluginListTable.tsx:141
#~ msgid "Edit plugin"
#~ msgstr "Edit plugin"

#: src/tables/plugin/PluginListTable.tsx:152
#: src/tables/plugin/PluginListTable.tsx:153
#~ msgid "Reload"
#~ msgstr "Reload"

#: src/tables/plugin/PluginListTable.tsx:153
msgid "Confirm plugin activation"
msgstr "Bekreft aktivering av utvidelse"

#: src/tables/plugin/PluginListTable.tsx:154
msgid "Confirm plugin deactivation"
msgstr "Bekreft deaktivering av utvidelse"

#: src/tables/plugin/PluginListTable.tsx:159
msgid "The selected plugin will be activated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:160
msgid "The selected plugin will be deactivated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:163
#~ msgid "Package information"
#~ msgstr "Package information"

#: src/tables/plugin/PluginListTable.tsx:178
msgid "Deactivate"
msgstr "Deaktiver"

#: src/tables/plugin/PluginListTable.tsx:192
msgid "Activate"
msgstr "Aktivér"

#: src/tables/plugin/PluginListTable.tsx:193
msgid "Activate selected plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:197
#~ msgid "Plugin settings"
#~ msgstr "Plugin settings"

#: src/tables/plugin/PluginListTable.tsx:205
msgid "Update selected plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:224
#: src/tables/stock/InstalledItemsTable.tsx:106
msgid "Uninstall"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:225
msgid "Uninstall selected plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:244
msgid "Delete selected plugin configuration"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:260
msgid "Activate Plugin"
msgstr "Aktivér utvidelse"

#: src/tables/plugin/PluginListTable.tsx:267
msgid "The plugin was activated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:268
msgid "The plugin was deactivated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:280
#~ msgid "Install plugin"
#~ msgstr "Install plugin"

#: src/tables/plugin/PluginListTable.tsx:281
#: src/tables/plugin/PluginListTable.tsx:368
msgid "Install Plugin"
msgstr "Installer Utvidelse"

#: src/tables/plugin/PluginListTable.tsx:294
msgid "Install"
msgstr "Installer"

#: src/tables/plugin/PluginListTable.tsx:295
msgid "Plugin installed successfully"
msgstr "Utvidelse installert"

#: src/tables/plugin/PluginListTable.tsx:300
msgid "Uninstall Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:308
#~ msgid "This action cannot be undone."
#~ msgstr "This action cannot be undone."

#: src/tables/plugin/PluginListTable.tsx:312
msgid "Confirm plugin uninstall"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:315
msgid "The selected plugin will be uninstalled."
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:320
msgid "Plugin uninstalled successfully"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:328
msgid "Delete Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:329
msgid "Deleting this plugin configuration will remove all associated settings and data. Are you sure you want to delete this plugin?"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:338
#~ msgid "Deactivate Plugin"
#~ msgstr "Deactivate Plugin"

#: src/tables/plugin/PluginListTable.tsx:342
msgid "Plugins reloaded"
msgstr "Utvidelser lastet inn på nytt"

#: src/tables/plugin/PluginListTable.tsx:343
msgid "Plugins were reloaded successfully"
msgstr "Utvidelser ble lastet inn på nytt"

#: src/tables/plugin/PluginListTable.tsx:354
#~ msgid "The following plugin will be activated"
#~ msgstr "The following plugin will be activated"

#: src/tables/plugin/PluginListTable.tsx:355
#~ msgid "The following plugin will be deactivated"
#~ msgstr "The following plugin will be deactivated"

#: src/tables/plugin/PluginListTable.tsx:361
msgid "Reload Plugins"
msgstr "Last utvidelser på nytt"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Activating plugin"
#~ msgstr "Activating plugin"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Deactivating plugin"
#~ msgstr "Deactivating plugin"

#: src/tables/plugin/PluginListTable.tsx:385
msgid "Plugin Detail"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:392
#~ msgid "Plugin updated"
#~ msgstr "Plugin updated"

#: src/tables/plugin/PluginListTable.tsx:403
#~ msgid "Error updating plugin"
#~ msgstr "Error updating plugin"

#: src/tables/plugin/PluginListTable.tsx:427
msgid "Sample"
msgstr "Eksempel"

#: src/tables/plugin/PluginListTable.tsx:432
#: src/tables/stock/StockItemTable.tsx:377
msgid "Installed"
msgstr "Installert"

#: src/tables/plugin/PluginListTable.tsx:615
#~ msgid "Plugin detail"
#~ msgstr "Plugin detail"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:60
#~ msgid "Parameter updated"
#~ msgstr "Parameter updated"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:63
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:112
msgid "Add Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:73
#~ msgid "Parameter deleted"
#~ msgstr "Parameter deleted"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
msgid "Edit Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
#~ msgid "Are you sure you want to delete this parameter?"
#~ msgstr "Are you sure you want to delete this parameter?"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:82
msgid "Delete Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartTable.tsx:56
#: src/tables/purchasing/SupplierPartTable.tsx:80
msgid "MPN"
msgstr "MPN"

#: src/tables/purchasing/ManufacturerPartTable.tsx:63
#~ msgid "Create Manufacturer Part"
#~ msgstr "Create Manufacturer Part"

#: src/tables/purchasing/ManufacturerPartTable.tsx:100
#~ msgid "Manufacturer part updated"
#~ msgstr "Manufacturer part updated"

#: src/tables/purchasing/ManufacturerPartTable.tsx:112
#~ msgid "Manufacturer part deleted"
#~ msgstr "Manufacturer part deleted"

#: src/tables/purchasing/ManufacturerPartTable.tsx:114
#~ msgid "Are you sure you want to remove this manufacturer part?"
#~ msgstr "Are you sure you want to remove this manufacturer part?"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:109
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:391
msgid "Import Line Items"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:230
msgid "Supplier Code"
msgstr "Leverandørkode"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:237
msgid "Supplier Link"
msgstr "Leverandørlenke"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:244
msgid "Manufacturer Code"
msgstr "Produsentens kode"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:273
msgid "Show line items which have been received"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
msgid "Receive line item"
msgstr "Motta ordrelinje"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
#: src/tables/sales/ReturnOrderLineItemTable.tsx:160
#: src/tables/sales/SalesOrderLineItemTable.tsx:258
#~ msgid "Add line item"
#~ msgstr "Add line item"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:408
msgid "Receive items"
msgstr "Motta artikler"

#: src/tables/purchasing/SupplierPartTable.tsx:111
msgid "Base units"
msgstr "Basisenhet"

#: src/tables/purchasing/SupplierPartTable.tsx:168
msgid "Add supplier part"
msgstr "Legg til leverandørdel"

#: src/tables/purchasing/SupplierPartTable.tsx:180
msgid "Show active supplier parts"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:184
msgid "Active Part"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:185
msgid "Show active internal parts"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:189
msgid "Active Supplier"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:190
msgid "Show active suppliers"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:193
#~ msgid "Supplier part updated"
#~ msgstr "Supplier part updated"

#: src/tables/purchasing/SupplierPartTable.tsx:195
msgid "Show supplier parts with stock"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:205
#~ msgid "Supplier part deleted"
#~ msgstr "Supplier part deleted"

#: src/tables/purchasing/SupplierPartTable.tsx:207
#~ msgid "Are you sure you want to remove this supplier part?"
#~ msgstr "Are you sure you want to remove this supplier part?"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:154
msgid "Received Date"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:168
msgid "Show items which have been received"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:173
msgid "Filter by line item status"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:191
msgid "Receive selected items"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:223
msgid "Receive Item"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:86
msgid "Show outstanding allocations"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:90
msgid "Assigned to Shipment"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:91
msgid "Show allocations assigned to a shipment"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:153
msgid "Available Quantity"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:160
msgid "Allocated Quantity"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:174
#: src/tables/sales/SalesOrderAllocationTable.tsx:188
msgid "No shipment"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:186
msgid "Not shipped"
msgstr "Ikke sendt"

#: src/tables/sales/SalesOrderAllocationTable.tsx:208
#: src/tables/sales/SalesOrderAllocationTable.tsx:230
msgid "Edit Allocation"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:215
#: src/tables/sales/SalesOrderAllocationTable.tsx:238
msgid "Delete Allocation"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:293
msgid "Assign to Shipment"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:309
msgid "Assign to shipment"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:272
msgid "Allocate Serial Numbers"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:280
#~ msgid "Allocate stock"
#~ msgstr "Allocate stock"

#: src/tables/sales/SalesOrderLineItemTable.tsx:291
#~ msgid "Allocate Serials"
#~ msgstr "Allocate Serials"

#: src/tables/sales/SalesOrderLineItemTable.tsx:320
msgid "Show lines which are fully allocated"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:325
msgid "Show lines which are completed"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:402
msgid "Allocate serials"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:419
msgid "Build stock"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:436
msgid "Order stock"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:51
#~ msgid "Delete Shipment"
#~ msgstr "Delete Shipment"

#: src/tables/sales/SalesOrderShipmentTable.tsx:55
msgid "Create Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:102
msgid "Items"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:137
msgid "View Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:154
msgid "Edit shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:162
msgid "Cancel shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:177
msgid "Add shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:191
msgid "Show shipments which have been shipped"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:196
msgid "Show shipments which have been delivered"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:31
#: src/tables/settings/ApiTokenTable.tsx:45
msgid "Generate Token"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:33
msgid "Token generated"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:68
#: src/tables/settings/ApiTokenTable.tsx:123
msgid "Revoked"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:72
#: src/tables/settings/ApiTokenTable.tsx:185
msgid "Token"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:79
msgid "In Use"
msgstr "I bruk"

#: src/tables/settings/ApiTokenTable.tsx:88
msgid "Last Seen"
msgstr "Sist sett"

#: src/tables/settings/ApiTokenTable.tsx:93
msgid "Expiry"
msgstr "Utløp"

#: src/tables/settings/ApiTokenTable.tsx:124
msgid "Show revoked tokens"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:143
msgid "Revoke"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:167
msgid "Error revoking token"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:189
msgid "Tokens are only shown once - make sure to note it down."
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:60
msgid "Barcode Information"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:85
msgid "Endpoint"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:89
#: src/tables/settings/BarcodeScanHistoryTable.tsx:208
#: src/tables/stock/StockItemTestResultTable.tsx:185
msgid "Result"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:97
msgid "Context"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:118
msgid "Response"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:209
msgid "Filter by result"
msgstr "Filtrer etter resultat"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:223
msgid "Delete Barcode Scan Record"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:249
msgid "Barcode Scan Details"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:259
msgid "Logging Disabled"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:261
msgid "Barcode logging is not enabled"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:63
msgid "Status Group"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:84
msgid "Logical State"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:96
msgid "Identifier"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:115
#~ msgid "Add state"
#~ msgstr "Add state"

#: src/tables/settings/CustomStateTable.tsx:133
#: src/tables/settings/CustomStateTable.tsx:140
#: src/tables/settings/CustomStateTable.tsx:202
msgid "Add State"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:153
msgid "Edit State"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:161
msgid "Delete State"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:54
msgid "Add Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:64
msgid "Edit Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:72
msgid "Delete Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:103
msgid "Add custom unit"
msgstr "Legg til egendefinert enhet"

#: src/tables/settings/EmailTable.tsx:21
#: src/tables/settings/EmailTable.tsx:36
msgid "Send Test Email"
msgstr ""

#: src/tables/settings/EmailTable.tsx:23
msgid "Email sent successfully"
msgstr ""

#: src/tables/settings/EmailTable.tsx:49
msgid "Delete Email"
msgstr ""

#: src/tables/settings/EmailTable.tsx:50
msgid "Email deleted successfully"
msgstr ""

#: src/tables/settings/EmailTable.tsx:58
msgid "Subject"
msgstr ""

#: src/tables/settings/EmailTable.tsx:63
msgid "To"
msgstr ""

#: src/tables/settings/EmailTable.tsx:68
msgid "Sender"
msgstr ""

#: src/tables/settings/EmailTable.tsx:78
msgid "Announced"
msgstr ""

#: src/tables/settings/EmailTable.tsx:80
msgid "Sent"
msgstr ""

#: src/tables/settings/EmailTable.tsx:82
msgid "Failed"
msgstr ""

#: src/tables/settings/EmailTable.tsx:86
msgid "Read"
msgstr ""

#: src/tables/settings/EmailTable.tsx:88
msgid "Confirmed"
msgstr ""

#: src/tables/settings/EmailTable.tsx:96
msgid "Direction"
msgstr ""

#: src/tables/settings/EmailTable.tsx:99
msgid "Incoming"
msgstr ""

#: src/tables/settings/EmailTable.tsx:99
msgid "Outgoing"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:51
#~ msgid "Delete error report"
#~ msgstr "Delete error report"

#: src/tables/settings/ErrorTable.tsx:67
msgid "Traceback"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:103
msgid "When"
msgstr "Når"

#: src/tables/settings/ErrorTable.tsx:113
msgid "Error Information"
msgstr "Feilinformasjon"

#: src/tables/settings/ErrorTable.tsx:123
msgid "Delete Error Report"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:125
msgid "Are you sure you want to delete this error report?"
msgstr "Er du sikker på at du vil slette denne feilrapporten?"

#: src/tables/settings/ErrorTable.tsx:127
msgid "Error report deleted"
msgstr "Feilrapport slettet"

#: src/tables/settings/ErrorTable.tsx:146
#: src/tables/settings/FailedTasksTable.tsx:65
msgid "Error Details"
msgstr "Feildetaljer"

#: src/tables/settings/ExportSessionTable.tsx:28
msgid "Output Type"
msgstr ""

#: src/tables/settings/ExportSessionTable.tsx:38
msgid "Exported On"
msgstr "Eksportert den"

#: src/tables/settings/ExportSessionTable.tsx:59
msgid "Delete Output"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:32
#: src/tables/settings/PendingTasksTable.tsx:28
#: src/tables/settings/ScheduledTasksTable.tsx:19
msgid "Task"
msgstr "Oppgave"

#: src/tables/settings/FailedTasksTable.tsx:38
#: src/tables/settings/PendingTasksTable.tsx:33
msgid "Task ID"
msgstr "Oppgave-ID"

#: src/tables/settings/FailedTasksTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:230
msgid "Started"
msgstr "Startet"

#: src/tables/settings/FailedTasksTable.tsx:54
msgid "Attempts"
msgstr "Forsøk"

#: src/tables/settings/FailedTasksTable.tsx:92
msgid "No Information"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:93
msgid "No error details are available for this task"
msgstr ""

#: src/tables/settings/GroupTable.tsx:71
msgid "Group with id {id} not found"
msgstr "Gruppe med id {id} er ikke funnet"

#: src/tables/settings/GroupTable.tsx:73
msgid "An error occurred while fetching group details"
msgstr "Det oppstod en feil under henting av gruppedetaljer"

#: src/tables/settings/GroupTable.tsx:96
#: src/tables/settings/GroupTable.tsx:197
msgid "Name of the user group"
msgstr ""

#: src/tables/settings/GroupTable.tsx:117
#~ msgid "Permission set"
#~ msgstr "Permission set"

#: src/tables/settings/GroupTable.tsx:170
#: src/tables/settings/UserTable.tsx:315
msgid "Open Profile"
msgstr ""

#: src/tables/settings/GroupTable.tsx:185
msgid "Delete group"
msgstr "Slett gruppe"

#: src/tables/settings/GroupTable.tsx:186
msgid "Group deleted"
msgstr "Gruppe slettet"

#: src/tables/settings/GroupTable.tsx:188
msgid "Are you sure you want to delete this group?"
msgstr "Er du sikker på at du vil slette denne gruppen?"

#: src/tables/settings/GroupTable.tsx:193
msgid "Add Group"
msgstr ""

#: src/tables/settings/GroupTable.tsx:210
msgid "Add group"
msgstr "Legg til gruppe"

#: src/tables/settings/GroupTable.tsx:213
#~ msgid "Edit group"
#~ msgstr "Edit group"

#: src/tables/settings/GroupTable.tsx:231
msgid "Edit Group"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:38
msgid "Delete Import Session"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:129
msgid "Create Import Session"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:72
msgid "Uploaded"
msgstr "Lastet opp"

#: src/tables/settings/ImportSessionTable.tsx:83
msgid "Imported Rows"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:111
#: src/tables/settings/TemplateTable.tsx:368
msgid "Model Type"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:112
#: src/tables/settings/TemplateTable.tsx:369
msgid "Filter by target model type"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:118
msgid "Filter by import session status"
msgstr ""

#: src/tables/settings/PendingTasksTable.tsx:47
msgid "Arguments"
msgstr "Argumenter"

#: src/tables/settings/PendingTasksTable.tsx:61
msgid "Remove all pending tasks"
msgstr ""

#: src/tables/settings/PendingTasksTable.tsx:69
msgid "All pending tasks deleted"
msgstr ""

#: src/tables/settings/PendingTasksTable.tsx:76
msgid "Error while deleting all pending tasks"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:46
msgid "Add Project Code"
msgstr "Legg til prosjektkode"

#: src/tables/settings/ProjectCodeTable.tsx:58
msgid "Edit Project Code"
msgstr "Endre prosjektkode"

#: src/tables/settings/ProjectCodeTable.tsx:66
msgid "Delete Project Code"
msgstr "Slett prosjektkode"

#: src/tables/settings/ProjectCodeTable.tsx:97
msgid "Add project code"
msgstr "Legg til prosjektkode"

#: src/tables/settings/ScheduledTasksTable.tsx:28
msgid "Last Run"
msgstr "Sist kjørt"

#: src/tables/settings/ScheduledTasksTable.tsx:50
msgid "Next Run"
msgstr "Neste kjøring"

#: src/tables/settings/StocktakeReportTable.tsx:28
#~ msgid "Report"
#~ msgstr "Report"

#: src/tables/settings/StocktakeReportTable.tsx:36
#~ msgid "Part Count"
#~ msgstr "Part Count"

#: src/tables/settings/StocktakeReportTable.tsx:59
#~ msgid "Delete Report"
#~ msgstr "Delete Report"

#: src/tables/settings/TemplateTable.tsx:120
#~ msgid "{templateTypeTranslation} with id {id} not found"
#~ msgstr "{templateTypeTranslation} with id {id} not found"

#: src/tables/settings/TemplateTable.tsx:124
#~ msgid "An error occurred while fetching {templateTypeTranslation} details"
#~ msgstr "An error occurred while fetching {templateTypeTranslation} details"

#: src/tables/settings/TemplateTable.tsx:146
#~ msgid "actions"
#~ msgstr "actions"

#: src/tables/settings/TemplateTable.tsx:165
msgid "Template not found"
msgstr "Mal ikke funnet"

#: src/tables/settings/TemplateTable.tsx:167
msgid "An error occurred while fetching template details"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Add new"
#~ msgstr "Add new"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Create new"
#~ msgstr "Create new"

#: src/tables/settings/TemplateTable.tsx:261
msgid "Modify"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:262
msgid "Modify template file"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:313
#: src/tables/settings/TemplateTable.tsx:381
msgid "Edit Template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:321
msgid "Delete template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:327
msgid "Add Template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:340
msgid "Add template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:363
msgid "Filter by enabled status"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:420
#~ msgid "Report Output"
#~ msgstr "Report Output"

#: src/tables/settings/UserTable.tsx:123
msgid "Groups updated"
msgstr ""

#: src/tables/settings/UserTable.tsx:124
msgid "User groups updated successfully"
msgstr ""

#: src/tables/settings/UserTable.tsx:131
msgid "Error updating user groups"
msgstr ""

#: src/tables/settings/UserTable.tsx:150
msgid "User with id {id} not found"
msgstr "Bruker med Id {id} ble ikke funnet"

#: src/tables/settings/UserTable.tsx:152
msgid "An error occurred while fetching user details"
msgstr "Det oppstod en feil under henting av brukerdetaljer"

#: src/tables/settings/UserTable.tsx:154
#~ msgid "No groups"
#~ msgstr "No groups"

#: src/tables/settings/UserTable.tsx:178
msgid "Is Active"
msgstr "Er aktiv"

#: src/tables/settings/UserTable.tsx:179
msgid "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
msgstr "Markerer om denne brukeren skal behandles som aktiv. Fjern avmerkingen istedet for å slette kontoer."

#: src/tables/settings/UserTable.tsx:183
msgid "Is Staff"
msgstr "Er ansatte"

#: src/tables/settings/UserTable.tsx:184
msgid "Designates whether the user can log into the django admin site."
msgstr "Markerer om brukeren kan logge inn til Django-administrasjonssiden."

#: src/tables/settings/UserTable.tsx:188
msgid "Is Superuser"
msgstr "Er Superbruker"

#: src/tables/settings/UserTable.tsx:189
msgid "Designates that this user has all permissions without explicitly assigning them."
msgstr "Markerer at denne brukeren har alle tillatelser uten å eksplisitt tilordne dem."

#: src/tables/settings/UserTable.tsx:199
msgid "You cannot edit the rights for the currently logged-in user."
msgstr "Du kan ikke redigere rettighetene for brukeren som er logget inn."

#: src/tables/settings/UserTable.tsx:218
msgid "User Groups"
msgstr ""

#: src/tables/settings/UserTable.tsx:305
#~ msgid "Edit user"
#~ msgstr "Edit user"

#: src/tables/settings/UserTable.tsx:332
msgid "Lock user"
msgstr ""

#: src/tables/settings/UserTable.tsx:342
msgid "Unlock user"
msgstr ""

#: src/tables/settings/UserTable.tsx:358
msgid "Delete user"
msgstr "Slett bruker"

#: src/tables/settings/UserTable.tsx:359
msgid "User deleted"
msgstr "Bruker slettet"

#: src/tables/settings/UserTable.tsx:361
msgid "Are you sure you want to delete this user?"
msgstr "Er du sikker på at du vil slette denne brukeren?"

#: src/tables/settings/UserTable.tsx:367
msgid "Add User"
msgstr ""

#: src/tables/settings/UserTable.tsx:375
msgid "Added user"
msgstr "Bruker lagt til"

#: src/tables/settings/UserTable.tsx:382
msgid "Set Password"
msgstr ""

#: src/tables/settings/UserTable.tsx:387
msgid "Password updated"
msgstr ""

#: src/tables/settings/UserTable.tsx:398
msgid "Add user"
msgstr "Legg til bruker"

#: src/tables/settings/UserTable.tsx:411
msgid "Show active users"
msgstr ""

#: src/tables/settings/UserTable.tsx:416
msgid "Show staff users"
msgstr ""

#: src/tables/settings/UserTable.tsx:421
msgid "Show superusers"
msgstr ""

#: src/tables/settings/UserTable.tsx:440
msgid "Edit User"
msgstr ""

#: src/tables/settings/UserTable.tsx:476
msgid "User updated"
msgstr ""

#: src/tables/settings/UserTable.tsx:477
msgid "User updated successfully"
msgstr ""

#: src/tables/settings/UserTable.tsx:483
msgid "Error updating user"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:37
#: src/tables/stock/InstalledItemsTable.tsx:89
msgid "Install Item"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:39
msgid "Item installed"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:50
msgid "Uninstall Item"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:52
msgid "Item uninstalled"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:107
msgid "Uninstall stock item"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:44
#: src/tables/stock/LocationTypesTable.tsx:111
msgid "Add Location Type"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:52
msgid "Edit Location Type"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:60
msgid "Delete Location Type"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:68
msgid "Icon"
msgstr "Ikon"

#: src/tables/stock/StockItemTable.tsx:107
msgid "This stock item is in production"
msgstr "Denne lagervaren er i produksjon"

#: src/tables/stock/StockItemTable.tsx:114
msgid "This stock item has been assigned to a sales order"
msgstr "Denne lagervaren har blitt tildelt en salgsordre"

#: src/tables/stock/StockItemTable.tsx:121
msgid "This stock item has been assigned to a customer"
msgstr "Denne lagervaren har blitt tilordnet en kunde"

#: src/tables/stock/StockItemTable.tsx:128
msgid "This stock item is installed in another stock item"
msgstr "Denne lagervaren er montert i en annen lagervare"

#: src/tables/stock/StockItemTable.tsx:135
msgid "This stock item has been consumed by a build order"
msgstr "Denne lagervaren har blitt konsumert av en produksjonsordre"

#: src/tables/stock/StockItemTable.tsx:142
msgid "This stock item is unavailable"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:151
msgid "This stock item has expired"
msgstr "Denne lagervaren har utløpt"

#: src/tables/stock/StockItemTable.tsx:155
msgid "This stock item is stale"
msgstr "Denne lagervaren er gammel"

#: src/tables/stock/StockItemTable.tsx:167
msgid "This stock item is fully allocated"
msgstr "Denne lagervaren er i sin helhet tilordnet"

#: src/tables/stock/StockItemTable.tsx:174
msgid "This stock item is partially allocated"
msgstr "Denne lagervaren er delvis tilordnet"

#: src/tables/stock/StockItemTable.tsx:202
msgid "This stock item has been depleted"
msgstr "Denne lagervaren er oppbrukt"

#: src/tables/stock/StockItemTable.tsx:301
#~ msgid "Show stock for assmebled parts"
#~ msgstr "Show stock for assmebled parts"

#: src/tables/stock/StockItemTable.tsx:306
msgid "Stocktake Date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:324
msgid "Show stock for active parts"
msgstr "Vis lagerbeholdning for aktive deler"

#: src/tables/stock/StockItemTable.tsx:335
msgid "Show stock for assembled parts"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:340
msgid "Show items which have been allocated"
msgstr "Vis elementer som har blitt tildelt"

#: src/tables/stock/StockItemTable.tsx:345
msgid "Show items which are available"
msgstr "Vis elementer som er tilgjengelige"

#: src/tables/stock/StockItemTable.tsx:349
#: src/tables/stock/StockLocationTable.tsx:38
msgid "Include Sublocations"
msgstr "Inkluder underplasseringer"

#: src/tables/stock/StockItemTable.tsx:350
msgid "Include stock in sublocations"
msgstr "Inkluder lager i underplasseringer"

#: src/tables/stock/StockItemTable.tsx:354
msgid "Depleted"
msgstr "Oppbrukt"

#: src/tables/stock/StockItemTable.tsx:355
msgid "Show depleted stock items"
msgstr "Vis oppbrukte lagervarer"

#: src/tables/stock/StockItemTable.tsx:360
msgid "Show items which are in stock"
msgstr "Vis elementer som er på lager"

#: src/tables/stock/StockItemTable.tsx:362
#~ msgid "Include stock items for variant parts"
#~ msgstr "Include stock items for variant parts"

#: src/tables/stock/StockItemTable.tsx:365
msgid "Show items which are in production"
msgstr "Vis elementer som er under produksjon"

#: src/tables/stock/StockItemTable.tsx:373
msgid "Show items which have been consumed by a build order"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:378
msgid "Show stock items which are installed in other items"
msgstr "Vis lagervarer som er installert i andre elementer"

#: src/tables/stock/StockItemTable.tsx:382
msgid "Sent to Customer"
msgstr "Sendt til kunde"

#: src/tables/stock/StockItemTable.tsx:383
msgid "Show items which have been sent to a customer"
msgstr "Vis elementer som er sendt til en kunde"

#: src/tables/stock/StockItemTable.tsx:394
msgid "Show tracked items"
msgstr "Vis sporede deler"

#: src/tables/stock/StockItemTable.tsx:397
#~ msgid "Serial Number LTE"
#~ msgstr "Serial Number LTE"

#: src/tables/stock/StockItemTable.tsx:398
msgid "Has Purchase Price"
msgstr "Har innkjøpspris"

#: src/tables/stock/StockItemTable.tsx:399
msgid "Show items which have a purchase price"
msgstr "Vis elementer som har innkjøpspris"

#: src/tables/stock/StockItemTable.tsx:403
#~ msgid "Serial Number GTE"
#~ msgstr "Serial Number GTE"

#: src/tables/stock/StockItemTable.tsx:404
msgid "Show items which have expired"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:410
msgid "Show items which are stale"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:415
msgid "Expired Before"
msgstr "Utløpt før"

#: src/tables/stock/StockItemTable.tsx:416
msgid "Show items which expired before this date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:422
msgid "Expired After"
msgstr "Utløpt etter"

#: src/tables/stock/StockItemTable.tsx:423
msgid "Show items which expired after this date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:429
msgid "Updated Before"
msgstr "Oppdatert før"

#: src/tables/stock/StockItemTable.tsx:430
msgid "Show items updated before this date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:435
msgid "Updated After"
msgstr "Oppdatert etter"

#: src/tables/stock/StockItemTable.tsx:436
msgid "Show items updated after this date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:441
msgid "Stocktake Before"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:442
msgid "Show items counted before this date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:447
msgid "Stocktake After"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:448
msgid "Show items counted after this date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:453
msgid "External Location"
msgstr "Ekstern plassering"

#: src/tables/stock/StockItemTable.tsx:454
msgid "Show items in an external location"
msgstr "Vis elementer ved en ekstern plassering"

#: src/tables/stock/StockItemTable.tsx:528
#~ msgid "Delete stock items"
#~ msgstr "Delete stock items"

#: src/tables/stock/StockItemTable.tsx:559
msgid "Order items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:595
#~ msgid "Add a new stock item"
#~ msgstr "Add a new stock item"

#: src/tables/stock/StockItemTable.tsx:604
#~ msgid "Remove some quantity from a stock item"
#~ msgstr "Remove some quantity from a stock item"

#: src/tables/stock/StockItemTable.tsx:615
#~ msgid "Move Stock items to new locations"
#~ msgstr "Move Stock items to new locations"

#: src/tables/stock/StockItemTable.tsx:622
#~ msgid "Change stock status"
#~ msgstr "Change stock status"

#: src/tables/stock/StockItemTable.tsx:624
#~ msgid "Change the status of stock items"
#~ msgstr "Change the status of stock items"

#: src/tables/stock/StockItemTable.tsx:631
#~ msgid "Merge stock"
#~ msgstr "Merge stock"

#: src/tables/stock/StockItemTable.tsx:633
#~ msgid "Merge stock items"
#~ msgstr "Merge stock items"

#: src/tables/stock/StockItemTable.tsx:642
#~ msgid "Order new stock"
#~ msgstr "Order new stock"

#: src/tables/stock/StockItemTable.tsx:653
#~ msgid "Assign to customer"
#~ msgstr "Assign to customer"

#: src/tables/stock/StockItemTable.tsx:655
#~ msgid "Assign items to a customer"
#~ msgstr "Assign items to a customer"

#: src/tables/stock/StockItemTable.tsx:662
#~ msgid "Delete stock"
#~ msgstr "Delete stock"

#: src/tables/stock/StockItemTestResultTable.tsx:140
msgid "Test"
msgstr "Test"

#: src/tables/stock/StockItemTestResultTable.tsx:174
msgid "Test result for installed stock item"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:205
msgid "Attachment"
msgstr "Vedlegg"

#: src/tables/stock/StockItemTestResultTable.tsx:224
msgid "Test station"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:246
msgid "Finished"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:304
#: src/tables/stock/StockItemTestResultTable.tsx:375
msgid "Edit Test Result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:306
msgid "Test result updated"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:312
#: src/tables/stock/StockItemTestResultTable.tsx:384
msgid "Delete Test Result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:314
msgid "Test result deleted"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:328
msgid "Test Passed"
msgstr "Test bestått"

#: src/tables/stock/StockItemTestResultTable.tsx:329
msgid "Test result has been recorded"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:336
msgid "Failed to record test result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:353
msgid "Pass Test"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:402
msgid "Show results for required tests"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:406
msgid "Include Installed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:407
msgid "Show results for installed stock items"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:411
msgid "Passed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:412
msgid "Show only passed tests"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:417
msgid "Show results for enabled tests"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:38
#~ msgid "structural"
#~ msgstr "structural"

#: src/tables/stock/StockLocationTable.tsx:39
msgid "Include sublocations in results"
msgstr "Inkluder underkategorier i resultatene"

#: src/tables/stock/StockLocationTable.tsx:43
#~ msgid "external"
#~ msgstr "external"

#: src/tables/stock/StockLocationTable.tsx:44
msgid "Show structural locations"
msgstr "Vis strukturelle plasseringer"

#: src/tables/stock/StockLocationTable.tsx:49
msgid "Show external locations"
msgstr "Vis eksterne plasseringer"

#: src/tables/stock/StockLocationTable.tsx:53
msgid "Has location type"
msgstr "Har plasseringstype"

#: src/tables/stock/StockLocationTable.tsx:58
msgid "Filter by location type"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:105
#: src/tables/stock/StockLocationTable.tsx:160
msgid "Add Stock Location"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:129
msgid "Set Parent Location"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:149
msgid "Set parent location for the selected items"
msgstr ""

#: src/tables/stock/StockTrackingTable.tsx:77
msgid "Added"
msgstr "Lagt til"

#: src/tables/stock/StockTrackingTable.tsx:82
msgid "Removed"
msgstr "Fjernet"

#: src/tables/stock/StockTrackingTable.tsx:206
msgid "Details"
msgstr "Detaljer"

#: src/tables/stock/StockTrackingTable.tsx:221
msgid "No user information"
msgstr "Ingen brukerinformasjon"

#: src/tables/stock/TestStatisticsTable.tsx:34
#: src/tables/stock/TestStatisticsTable.tsx:64
#~ msgid "Total"
#~ msgstr "Total"

#: src/views/MobileAppView.tsx:25
msgid "Mobile viewport detected"
msgstr "Mobilvisning oppdaget"

#: src/views/MobileAppView.tsx:25
#~ msgid "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
#~ msgstr "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."

#: src/views/MobileAppView.tsx:28
msgid "InvenTree UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
msgstr ""

#: src/views/MobileAppView.tsx:34
msgid "Read the docs"
msgstr "Les dokumentasjonen"

#: src/views/MobileAppView.tsx:38
msgid "Ignore and continue to Desktop view"
msgstr ""

