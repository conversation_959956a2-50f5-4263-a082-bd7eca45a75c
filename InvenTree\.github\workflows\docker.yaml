# Build, test and push InvenTree docker image
# This workflow runs under any of the following conditions:
#
# - Push to the master branch
# - Publish release
#
# The following actions are performed:
#
# - Check that the version number matches the current branch or tag
# - Build the InvenTree docker image
# - Run suite of unit tests against the build image
# - Push the compiled, tested image to dockerhub

name: Docker

on:
  release:
    types: [published]

  push:
    branches:
      - "master"
  pull_request:
    branches:
      - "master"

permissions:
  contents: read

jobs:
  paths-filter:
    permissions:
      contents: read # for dorny/paths-filter to fetch a list of changed files
      pull-requests: read # for dorny/paths-filter to read pull requests
    name: Filter
    runs-on: ubuntu-latest

    outputs:
      docker: ${{ steps.filter.outputs.docker }}

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # pin@v5.0.0
        with:
          persist-credentials: false
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # pin@v3.0.2
        id: filter
        with:
          filters: |
            docker:
              - .github/workflows/docker.yaml
              - contrib/container/**
              - src/backend/InvenTree/InvenTree/settings.py
              - src/backend/requirements.txt
              - tasks.py

  # Build the docker image
  build:
    needs: paths-filter
    if: needs.paths-filter.outputs.docker == 'true' || github.event_name == 'release' || github.event_name == 'push' || contains(github.event.pull_request.labels.*.name, 'full-run')
    permissions:
      contents: read
      packages: write
      id-token: write
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      python_version: "3.11"
    runs-on: ubuntu-latest # in the future we can try to use alternative runners here

    steps:
      - name: Check out repo
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # pin@v5.0.0
        with:
          persist-credentials: false
      - name: Set Up Python ${{ env.python_version }}
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # pin@v5.6.0
        with:
          python-version: ${{ env.python_version }}
      - name: Version Check
        run: |
          pip install --require-hashes -r contrib/dev_reqs/requirements.txt
          python3 .github/scripts/version_check.py
          echo "git_commit_hash=$(git rev-parse --short HEAD)" >> $GITHUB_ENV
          echo "git_commit_date=$(git show -s --format=%ci)" >> $GITHUB_ENV
      - name: Test Docker Image
        id: test-docker
        run: |
          docker build . --target production --tag inventree-test -f contrib/container/Dockerfile
          docker run --rm inventree-test invoke version
          docker run --rm inventree-test invoke --version
          docker run --rm inventree-test invoke --list
          docker run --rm inventree-test gunicorn --version
          docker run --rm inventree-test pg_dump --version
          docker run --rm inventree-test test -f /home/<USER>/init.sh
          docker run --rm inventree-test test -f /home/<USER>/tasks.py
          docker run --rm inventree-test test -f /home/<USER>/gunicorn.conf.py
          docker run --rm inventree-test test -f /home/<USER>/src/backend/requirements.txt
          docker run --rm inventree-test test -f /home/<USER>/src/backend/InvenTree/manage.py
      - name: Build Docker Image
        # Build the development docker image (using docker-compose.yml)
        run: docker compose --project-directory . -f contrib/container/dev-docker-compose.yml build --no-cache
      - name: Update Docker Image
        run: |
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run inventree-dev-server invoke install
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run inventree-dev-server invoke version
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run inventree-dev-server invoke update
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run inventree-dev-server invoke backup
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run inventree-dev-server invoke restore
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run inventree-dev-server invoke dev.setup-dev
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml up -d
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run inventree-dev-server invoke wait
      - name: Check Data Directory
        # The following file structure should have been created by the docker image
        run: |
          test -d data
          test -d data/env
          test -d data/pgdb
          test -d data/media
          test -d data/static
          test -d data/plugins
          test -f data/config.yaml
          test -f data/plugins.txt
          test -f data/secret_key.txt
          test -f data/oidc.pem
      - name: Run Unit Tests
        run: |
          echo "GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}" >> contrib/container/docker.dev.env
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run --rm inventree-dev-server invoke dev.test --check --disable-pty --translations
      - name: Run Migration Tests
        run: |
          docker compose --project-directory . -f contrib/container/dev-docker-compose.yml run --rm inventree-dev-server invoke dev.test --check --migrations --translations
      - name: Clean up test folder
        run: |
          rm -rf InvenTree/_testfolder
      - name: Set up QEMU
        if: github.event_name != 'pull_request'
        uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392 # pin@v3.6.0
      - name: Set up Docker Buildx
        if: github.event_name != 'pull_request'
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # pin@v3.11.1
      - name: Set up cosign
        if: github.event_name != 'pull_request'
        uses: sigstore/cosign-installer@d58896d6a1865668819e1d91763c7751a165e159 # pin@v3.9.2
      - name: Check if Dockerhub login is required
        id: docker_login
        run: |
          if [ -z "${{ secrets.DOCKER_USERNAME }}" ]; then
            echo "skip_dockerhub_login=true" >> $GITHUB_OUTPUT
          else
            echo "skip_dockerhub_login=false" >> $GITHUB_OUTPUT
          fi
      - name: Login to Dockerhub
        if: github.event_name != 'pull_request' && steps.docker_login.outputs.skip_dockerhub_login != 'true'
        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 # pin@v3.5.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Log into registry ghcr.io
        if: github.event_name != 'pull_request'
        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 # pin@v3.5.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract Docker metadata
        if: github.event_name != 'pull_request'
        id: meta
        uses: docker/metadata-action@c1e51972afc2121e065aed6d45c65596fe445f3f # pin@v5.8.0
        with:
          images: |
            inventree/inventree
            ghcr.io/${{ github.repository }}
      - uses: depot/setup-action@b0b1ea4f69e92ebf5dea3f8713a1b0c37b2126a5 # pin@v1
      - name: Push Docker Images
        id: push-docker
        if: github.event_name != 'pull_request'
        uses: depot/build-push-action@9785b135c3c76c33db102e45be96a25ab55cd507 # pin@v1
        with:
          project: jczzbjkk68
          context: .
          file: ./contrib/container/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          sbom: true
          provenance: false
          target: production
          tags: ${{ env.docker_tags }}
          build-args: |
            commit_hash=${{ env.git_commit_hash }}
            commit_date=${{ env.git_commit_date }}
