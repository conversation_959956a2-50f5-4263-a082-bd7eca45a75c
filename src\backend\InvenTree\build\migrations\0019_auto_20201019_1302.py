# Generated by Django 3.0.7 on 2020-10-19 13:02

import build.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('build', '0018_build_reference'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='build',
            options={'verbose_name': 'Build Order', 'verbose_name_plural': 'Build Orders'},
        ),
        migrations.AlterField(
            model_name='build',
            name='reference',
            field=models.CharField(help_text='Build Order Reference', max_length=64, unique=True, validators=[build.validators.validate_build_order_reference], verbose_name='Reference'),
        ),
    ]
