# Generated by Django 3.2.16 on 2022-12-21 11:26

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('part', '0090_auto_20221115_0816'),
    ]

    operations = [
        migrations.CreateModel(
            name='PartStocktake',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=5, help_text='Total available stock at time of stocktake', max_digits=19, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Quantity')),
                ('date', models.DateField(auto_now_add=True, help_text='Date stocktake was performed', verbose_name='Date')),
                ('note', models.Char<PERSON>ield(blank=True, help_text='Additional notes', max_length=250, verbose_name='Notes')),
                ('part', models.ForeignKey(help_text='Part for stocktake', on_delete=django.db.models.deletion.CASCADE, related_name='stocktakes', to='part.part', verbose_name='Part')),
                ('user', models.ForeignKey(blank=True, help_text='User who performed this stocktake', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='part_stocktakes', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
        ),
    ]
