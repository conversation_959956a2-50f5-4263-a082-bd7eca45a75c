---
title: Part Notifications
---

## Part Notifications Plugin

The **Part Notifications Plugin** provides a notification system for part changes. It allows users to receive notifications when certain events occur on parts.

### Activation

This plugin is an *optional* plugin, and must be enabled in the InvenTree settings.

### Plugin Settings

This plugin has the following configurable settings:

{{ image("part_notification_settings.png", base="plugin/builtin", title="Part Notification Settings") }}

## Usage

To enable the part notifications plugin, the *Enable Event Integration* setting must be enabled in the InvenTree settings. This will allow the plugin to send notifications when certain events occur on parts.

{{ image("enable_events.png", base="plugin/builtin", title="Enable Events") }}
