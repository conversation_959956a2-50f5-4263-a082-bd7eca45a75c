---
title: Release 0.1.7
---

## Release 0.1.7

[Release 0.1.7](https://github.com/inventree/InvenTree/releases/tag/0.1.7) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Label Printing

Label printing functionality has been simplified and brought into line with the PDF reporting functionality.

[#1342](https://github.com/inventree/InvenTree/pull/1342) represents a significant refactor of the label printing code.

!!! info "More Information"
    Refer to the [label printing documentation](../report/labels.md) for further details.

### Display Sub Builds

[#1344](https://github.com/inventree/InvenTree/pull/1344) adds display of sub-builds under the build detail

### Recently Updated Stock

[#1350](https://github.com/inventree/InvenTree/pull/1350) adds *Recently Updated Stock* view to the index page

### Menubar Improvements

[#1352](https://github.com/inventree/InvenTree/pull/1354) provides a *significant* visual improvement for displaying menubars on various pages. The previous *tab style* navigation has been removed, and replaced with a vertical menu which can be toggled between icon-and-text or icon-only.

### API Permissions

[#1363](https://github.com/inventree/InvenTree/pull/1363) enforces user role permissions onto the REST API endpoints. Authenticated users can now only perform REST actions which align with their allocated role(s). Refer to the [API documentation](../api/index.md#authorization) for further information.

### Query Pagination

[#1373](https://github.com/inventree/InvenTree/pull/1373) *finally* introduces server-side pagination of large querysets, resulting in a significantly improved user experience. For larger data sets, retrieval time has been reduced from multiple seconds to ~100ms.

### Search Results

[#1385](https://github.com/inventree/InvenTree/pull/1385) adds some additional search results to the *Search* page. Users can now search:

- Build Orders
- Purchase Orders
- Sales Orders

## Major Bug Fixes

| PR | Description |
| --- | --- |
| [#1341](https://github.com/inventree/InvenTree/pull/1341) | Fixes display issue with Part variant table |
| [#1351](https://github.com/inventree/InvenTree/pull/1351) | Fixes bug which prevented filtering of reports and labels to work correctly |
| [#1352](https://github.com/inventree/InvenTree/pull/1352) | Fixes bug updating part "trackable" status |
| [#1353](https://github.com/inventree/InvenTree/pull/1353) | Fixes bug which caused error when "special" regex characters were used for part search |
| [#1371](https://github.com/inventree/InvenTree/pull/1371) | Fixes a *very* long-running bug whereby API requests for invalid endpoints were redirected to the web-view index page |
| [#1379](https://github.com/inventree/InvenTree/pull/1379) | Fixes an obtuse bug introduced by the new server-side pagination feature |
| [#1381](https://github.com/inventree/InvenTree/pull/1381) | Fixes permissions error caused by incorrect table name lookup |
