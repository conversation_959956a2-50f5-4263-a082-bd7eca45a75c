# Generated by Django 2.2.4 on 2019-09-02 23:02

import django.core.validators
from django.db import migrations, models


class CreateModelOrSkip(migrations.CreateModel):
    """Custom migration operation to create a model if it does not already exist.

    - If the model already exists, the migration is skipped
    - This class has been added to deal with some errors being thrown in CI tests
    - The 'common_currency' table doesn't exist anymore anyway!
    - In the future, these migrations will be squashed
    """

    def database_forwards(self, app_label, schema_editor, from_state, to_state) -> None:
        """Forwards migration *attempts* to create the model, but will fail gracefully if it already exists"""

        try:
            super().database_forwards(app_label, schema_editor, from_state, to_state)
        except Exception:  # pragma: no cover
            pass

    def state_forwards(self, app_label, state) -> None:
        try:
            super().state_forwards(app_label, state)
        except Exception:  # pragma: no cover
            pass


class Migration(migrations.Migration):

    initial = True
    atomic = False

    dependencies = [
    ]

    operations = [
        CreateModelOrSkip(
            name='Currency',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(help_text='Currency Symbol e.g. $', max_length=10)),
                ('suffix', models.CharField(help_text='Currency Suffix e.g. AUD', max_length=10, unique=True)),
                ('description', models.CharField(help_text='Currency Description', max_length=100)),
                ('value', models.DecimalField(decimal_places=5, help_text='Currency Value', max_digits=10, validators=[django.core.validators.MinValueValidator(1e-05), django.core.validators.MaxValueValidator(100000)])),
                ('base', models.BooleanField(default=False, help_text='Use this currency as the base currency')),
            ],
        ),
    ]
