---
title: Release 0.2.2
---

## Release 0.2.2

[Release 0.2.2](https://github.com/inventree/InvenTree/releases/tag/0.2.2) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Custom Site Title

[#1481](https://github.com/inventree/InvenTree/pull/1481) adds the ability to customize the site title (which was previously hard-coded to "InvenTree")

### Django Update

[#1490](https://github.com/inventree/InvenTree/pull/1490) updates to [Django v3.2](https://docs.djangoproject.com/en/3.2/releases/3.2/) which is a LTS release with significant improvements.

### Location Display

[#1493](https://github.com/inventree/InvenTree/pull/1493) greatly improves the display of sublocations within a given location. Additionally, multiple sublocations can be selected (via checkboxes) allowing for printing of multiple StockLocation labels at once.

### Category Display

[#1493](https://github.com/inventree/InvenTree/pull/1493) greatly improves the display of subcategories within a given category.

### Build Order Improvements

[#1492](https://github.com/inventree/InvenTree/pull/1492) drastically improves the build order process, streamlining the stock allocation process and providing a more intuitive user experience. Refer to the [build order documentation](../manufacturing/build.md) for further information.

### Javascript Translation

[#1494](https://github.com/inventree/InvenTree/pull/1494) provides significant improvements for delivery of JavaScript files. Previously, JavaScript files which required i18n translation were compiled at run-time by the server. Now, JavaScript files are pre-compiled as part of the static file creation step. This significantly speeds up delivery of JavaScript files to the client.

### Serial Number Entry Improvements

[#1521](https://github.com/inventree/InvenTree/pull/1521) adds some more "shortcuts" for quickly assigning or selecting serial numbers.

### Language Selection

[#1529](https://github.com/inventree/InvenTree/pull/1529) adds the ability for the user to select desired translation language from the InvenTree settings menu

### Purchase Order Pricing

[#1452](https://github.com/inventree/InvenTree/pull/1542) adds purchase price information to stock items received against a purchase order

### Stock Item History

[#1572](https://github.com/inventree/InvenTree/pull/1572) greatly improves the stock item history tracking, allowing translated history strings and tracking more information

### Purchase Order Import

[#1561](https://github.com/inventree/InvenTree/pull/1561) introduces an import wizard for purchase orders

### Currency Conversion Support

[#1598](https://github.com/inventree/InvenTree/pull/1598), [#1605](https://github.com/inventree/InvenTree/pull/1605), [#1608](https://github.com/inventree/InvenTree/pull/1608) and [#1611](https://github.com/inventree/InvenTree/pull/1611) add support for automatically updating currency conversion rates.

[#1600](https://github.com/inventree/InvenTree/pull/1600) adds a settings page for managing currency conversion, and provides support for manual currency exchange rate input

For more information refer to the [pricing documentation](../part/pricing.md).

## Major Bug Fixes

| PR | Description |
| --- | --- |
| [#1489](https://github.com/inventree/InvenTree/pull/1489) | Fixes bug which prevented customer assigned stock items from being returned to stock |
| [#1525](https://github.com/inventree/InvenTree/pull/1525) | Fixes bug which referenced `MPN` field instead of `SKU` field
| [#1523](https://github.com/inventree/InvenTree/pull/1523) | Fixes bug in part API filtering |
| [#1535](https://github.com/inventree/InvenTree/pull/1535) | Fixes issues with part price break calculation |
| [#1544](https://github.com/inventree/InvenTree/pull/1544) | Fix invoke bug in tasks.py |
| [#1558](https://github.com/inventree/InvenTree/pull/1558) | Fixes calendar rendering bug |
| [#1567](https://github.com/inventree/InvenTree/pull/1567) | Fixes logout screen redirect loop |
