from django.db import migrations, models


def reverse_empty_email(apps, schema_editor):  # pragma: no cover
    Company = apps.get_model('company', 'Company')
    for company in Company.objects.all():
        if company.email == None:
            company.email = ''
            company.save()


def make_empty_email_field_null(apps, schema_editor):
    Company = apps.get_model('company', 'Company')
    for company in Company.objects.all():
        if company.email == '':
            company.email = None
            company.save()


class Migration(migrations.Migration):

    atomic = False

    dependencies = [
        ('company', '0023_auto_20200808_0715'),
    ]

    operations = [
        # Allow email field to be NULL
        migrations.AlterField(
            model_name='company',
            name='email',
            field=models.EmailField(blank=True, help_text='Contact email address', max_length=254, null=True, unique=False, verbose_name='Email'),
        ),
        # Convert empty email string to NULL
        migrations.RunPython(make_empty_email_field_null, reverse_code=reverse_empty_email),
        # Remove unique constraint on name field
        migrations.AlterField(
            model_name='company',
            name='name',
            field=models.CharField(help_text='Company name', max_length=100, verbose_name='Company name'),
        ),
        # Add unique constraint on name/email pair
        migrations.AddConstraint(
            model_name='company',
            constraint=models.UniqueConstraint(fields=('name', 'email'), name='unique_name_email_pair'),
        ),
    ]
