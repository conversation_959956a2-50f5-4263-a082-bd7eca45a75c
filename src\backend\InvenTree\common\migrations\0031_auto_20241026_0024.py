# Generated by Django 4.2.16 on 2024-10-26 00:24

from django.conf import settings
from django.db import migrations

import logging

logger = logging.getLogger('inventree')


def update_news_feed_urls(apps, schema_editor):
    """Update and validate the news feed URLs."""

    from common.models import NewsFeedEntry

    n = 0

    for entry in NewsFeedEntry.objects.all():
        if entry.link and entry.link.startswith('/'):
            entry.link = settings.INVENTREE_BASE_URL + entry.link
            entry.save()
            n += 1

    if n > 0:
        logger.info("Updated link for %s NewsFeedEntry objects", n)


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0030_barcodescanresult'),
    ]

    operations = [
        migrations.RunPython(
            update_news_feed_urls,
            reverse_code=migrations.RunPython.noop
        )
    ]
