---
title: Part Views
---

## Part Category View

From the *home screen*, select *Parts* to open the top-level part category view.

### Details Tab

The *Details* tab shows information about the selected part category. In particular, it shows the name and description of the category, a link to the parent category (if available) and a list of subcategories.

{{ image("app/part_category_detail.png", "Part Category") }}

#### Parent Category

If the current category has a parent category (i.e. it is not a top-level category) then a link is provided to the parent category. Tap the *parent category* tile to navigate to the category detail page for the parent category.

#### Subcategories

If the current category has any subcategories, these are listed here. Select any of the subcategories to navigate to it.

### Parts Tab

The *Parts* tab displays all the parts available in this category. Tap a displayed part to navigate to the part detail view.

{{ image("app/category_parts_tab.png", "Category Parts") }}

The list of available parts can be filtered using the input box at the top of the screen:

{{ image("app/category_parts_filter.png", "Category Parts Filter") }}

### Context Actions

The following *Context Actions* are available for the selected category:

{{ image("app/category_actions_tab.png", "Category Actions") }}

#### New Category

Create a new subcategory under the current category:

{{ image("app/new_category.jpg", "New Category") }}

#### New Part

Create a new part within the current category:

{{ image("app/new_part.jpg", "New Part") }}

### Edit Category

Select the *Edit* button in the top right corner of the screen to edit the details for the selected part category:

{{ image("app/part_category_edit.jpg", "Edit Category") }}

!!! info "Permission Required"
    If the user does not have permission to edit part details, this button will be hidden

In the part category display screen, there are three tabs of information available:

## Part Detail View

The *Part Detail* view displays information about a single part:

{{ image("app/part_details.png", "Part Detail") }}

### Details Tab

The *details* tab shows information about the selected part. Some of the displayed tiles provide further information when selected:

#### Category

Tap on the displayed part category to navigate to a detail view for that category.

#### Stock

The *stock* tile shows the total quantity of stock available for the part. Tap on this tile to navigate to the *Stock Tab* view for this part.

#### Notes

Tap on the *notes* tile to view (and edit) the notes for this part:

{{ image("app/part_notes.jpg", "Part Notes") }}

#### Attachments

Tap on the *attachments* tile to view the file attachments for this part:

{{ image("app/part_attachments.jpg", "Part Attachments") }}

New attachments can be uploaded by tapping on the icons in the top right of the screen.

Select a particular attachment file to downloaded it to the local device.

### Stock Tab

The *Stock* tab displays all the stock items available for this part. Tap on a particular stock item to navigate to a detail view for that item.

{{ image("app/part_stock.png", "Part Stock") }}

The list of available stock items can be filtered using the input box at the top of the screen.

### Actions Tab

The *Actions* tab displays the available actions for the selected part:

#### New Stock Item

Create a new stock item for this part:

{{ image("app/new_stock_item.jpg", "New Stock Item") }}

### Edit Part

To edit the part details, select the *Edit* button in the top right corner of the screen:

{{ image("app/part_edit.jpg", "Edit Part") }}

!!! info "Permission Required"
    If the user does not have permission to edit part details, this button will be hidden

### Part Image View

Tap the image of the part (displayed at the top left of the screen) to launch the part image view:

{{ image("app/part_image.jpg", "Part Image") }}

A full-screen view of the image is displayed. The user can also upload a new image for the part, either selecting an image from the device, or taking a new picture with the device's camera.
