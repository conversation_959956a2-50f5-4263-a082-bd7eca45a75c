msgid ""
msgstr ""
"POT-Creation-Date: 2023-06-09 22:10+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ja\n"
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-20 11:54\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: /src/frontend/src/locales/en/messages.po\n"
"X-Crowdin-File-ID: 252\n"

#: lib/components/RowActions.tsx:36
#: src/components/items/ActionDropdown.tsx:287
#: src/pages/Index/Scan.tsx:64
msgid "Duplicate"
msgstr "複製"

#: lib/components/RowActions.tsx:46
#: src/components/items/ActionDropdown.tsx:243
msgid "Edit"
msgstr "編集"

#: lib/components/RowActions.tsx:56
#: src/components/forms/ApiForm.tsx:719
#: src/components/items/ActionDropdown.tsx:255
#: src/components/items/RoleTable.tsx:155
#: src/hooks/UseForm.tsx:160
#: src/pages/Notifications.tsx:109
#: src/tables/plugin/PluginListTable.tsx:243
msgid "Delete"
msgstr "削除"

#: lib/components/RowActions.tsx:66
#: src/components/details/DetailsImage.tsx:83
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:187
#: src/components/items/ActionDropdown.tsx:275
#: src/components/items/ActionDropdown.tsx:276
#: src/contexts/ThemeContext.tsx:45
#: src/hooks/UseForm.tsx:33
#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:106
#: src/tables/FilterSelectDrawer.tsx:336
#: src/tables/build/BuildOutputTable.tsx:570
msgid "Cancel"
msgstr "キャンセル"

#: lib/components/RowActions.tsx:136
#: src/components/nav/NavigationDrawer.tsx:198
#: src/forms/PurchaseOrderForms.tsx:795
#: src/forms/StockForms.tsx:737
#: src/forms/StockForms.tsx:783
#: src/forms/StockForms.tsx:829
#: src/forms/StockForms.tsx:868
#: src/forms/StockForms.tsx:904
#: src/forms/StockForms.tsx:983
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:456
msgid "Actions"
msgstr "アクション"

#: lib/components/SearchInput.tsx:34
#: src/components/forms/fields/RelatedModelField.tsx:387
#: src/components/nav/Header.tsx:168
#: src/pages/Index/Settings/UserSettings.tsx:74
#: src/pages/part/PartDetail.tsx:1161
msgid "Search"
msgstr "検索"

#: lib/components/YesNoButton.tsx:20
msgid "Pass"
msgstr "パス"

#: lib/components/YesNoButton.tsx:21
msgid "Fail"
msgstr "失敗"

#: lib/components/YesNoButton.tsx:43
#: src/tables/Filter.tsx:35
msgid "Yes"
msgstr "はい"

#: lib/components/YesNoButton.tsx:44
#: src/tables/Filter.tsx:36
msgid "No"
msgstr "いいえ"

#: lib/enums/ModelInformation.tsx:28
#: src/components/wizards/OrderPartsWizard.tsx:132
#: src/forms/BuildForms.tsx:310
#: src/forms/BuildForms.tsx:384
#: src/forms/BuildForms.tsx:448
#: src/forms/BuildForms.tsx:602
#: src/forms/BuildForms.tsx:757
#: src/forms/BuildForms.tsx:860
#: src/forms/PurchaseOrderForms.tsx:791
#: src/forms/ReturnOrderForms.tsx:239
#: src/forms/SalesOrderForms.tsx:267
#: src/forms/StockForms.tsx:303
#: src/forms/StockForms.tsx:732
#: src/forms/StockForms.tsx:778
#: src/forms/StockForms.tsx:824
#: src/forms/StockForms.tsx:863
#: src/forms/StockForms.tsx:899
#: src/forms/StockForms.tsx:937
#: src/forms/StockForms.tsx:979
#: src/forms/StockForms.tsx:1027
#: src/forms/StockForms.tsx:1071
#: src/pages/build/BuildDetail.tsx:183
#: src/pages/part/PartDetail.tsx:1213
#: src/tables/ColumnRenderers.tsx:82
#: src/tables/part/RelatedPartTable.tsx:53
#: src/tables/stock/StockTrackingTable.tsx:87
msgid "Part"
msgstr "パーツ"

#: lib/enums/ModelInformation.tsx:29
#: lib/enums/Roles.tsx:35
#: src/components/nav/NavigationDrawer.tsx:77
#: src/defaults/links.tsx:36
#: src/pages/Index/Settings/SystemSettings.tsx:190
#: src/pages/part/CategoryDetail.tsx:130
#: src/pages/part/CategoryDetail.tsx:273
#: src/pages/part/CategoryDetail.tsx:312
#: src/pages/part/PartDetail.tsx:951
msgid "Parts"
msgstr "パーツ"

#: lib/enums/ModelInformation.tsx:37
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:13
msgid "Part Parameter Template"
msgstr "部品パラメータテンプレート"

#: lib/enums/ModelInformation.tsx:38
msgid "Part Parameter Templates"
msgstr "部品パラメータテンプレート"

#: lib/enums/ModelInformation.tsx:45
msgid "Part Test Template"
msgstr "部品試験テンプレート"

#: lib/enums/ModelInformation.tsx:46
msgid "Part Test Templates"
msgstr "部品試験テンプレート"

#: lib/enums/ModelInformation.tsx:52
#: src/components/wizards/OrderPartsWizard.tsx:143
#: src/pages/company/SupplierPartDetail.tsx:409
#: src/pages/stock/StockDetail.tsx:286
#: src/tables/build/BuildAllocatedStockTable.tsx:155
#: src/tables/part/PartPurchaseOrdersTable.tsx:50
#: src/tables/purchasing/SupplierPartTable.tsx:64
#: src/tables/stock/StockItemTable.tsx:248
msgid "Supplier Part"
msgstr "サプライヤー"

#: lib/enums/ModelInformation.tsx:53
#: src/pages/purchasing/PurchasingIndex.tsx:92
msgid "Supplier Parts"
msgstr "サプライヤー・パーツ"

#: lib/enums/ModelInformation.tsx:61
#: src/tables/part/PartPurchaseOrdersTable.tsx:56
#: src/tables/stock/StockItemTable.tsx:254
msgid "Manufacturer Part"
msgstr "メーカー・パーツ"

#: lib/enums/ModelInformation.tsx:62
#: src/pages/purchasing/PurchasingIndex.tsx:109
msgid "Manufacturer Parts"
msgstr "メーカー・パーツ"

#: lib/enums/ModelInformation.tsx:70
#: src/pages/part/CategoryDetail.tsx:343
#: src/tables/Filter.tsx:381
msgid "Part Category"
msgstr "パーツカテゴリ"

#: lib/enums/ModelInformation.tsx:71
#: lib/enums/Roles.tsx:37
#: src/pages/part/CategoryDetail.tsx:334
#: src/pages/part/PartDetail.tsx:1202
msgid "Part Categories"
msgstr "パーツカテゴリ"

#: lib/enums/ModelInformation.tsx:79
#: src/forms/BuildForms.tsx:385
#: src/forms/BuildForms.tsx:449
#: src/forms/BuildForms.tsx:604
#: src/forms/BuildForms.tsx:758
#: src/forms/SalesOrderForms.tsx:269
#: src/pages/stock/StockDetail.tsx:976
#: src/tables/stock/StockTrackingTable.tsx:48
#: src/tables/stock/StockTrackingTable.tsx:55
msgid "Stock Item"
msgstr "在庫商品"

#: lib/enums/ModelInformation.tsx:80
#: lib/enums/Roles.tsx:45
#: src/pages/company/CompanyDetail.tsx:212
#: src/pages/part/CategoryDetail.tsx:287
#: src/pages/part/PartStockHistoryDetail.tsx:101
#: src/pages/stock/LocationDetail.tsx:123
#: src/pages/stock/LocationDetail.tsx:182
msgid "Stock Items"
msgstr "在庫商品"

#: lib/enums/ModelInformation.tsx:88
#: lib/enums/Roles.tsx:47
#: src/pages/stock/LocationDetail.tsx:420
msgid "Stock Location"
msgstr "在庫場所"

#: lib/enums/ModelInformation.tsx:89
#: src/pages/stock/LocationDetail.tsx:176
#: src/pages/stock/LocationDetail.tsx:412
#: src/pages/stock/StockDetail.tsx:967
msgid "Stock Locations"
msgstr "在庫場所"

#: lib/enums/ModelInformation.tsx:97
msgid "Stock Location Type"
msgstr "在庫場所 タイプ"

#: lib/enums/ModelInformation.tsx:98
msgid "Stock Location Types"
msgstr "ストックロケーションの種類"

#: lib/enums/ModelInformation.tsx:103
#: src/pages/Index/Settings/SystemSettings.tsx:248
#: src/pages/part/PartDetail.tsx:910
msgid "Stock History"
msgstr "株式履歴"

#: lib/enums/ModelInformation.tsx:104
msgid "Stock Histories"
msgstr "株式履歴"

#: lib/enums/ModelInformation.tsx:109
msgid "Build"
msgstr "組立"

#: lib/enums/ModelInformation.tsx:110
msgid "Builds"
msgstr "ビルド"

#: lib/enums/ModelInformation.tsx:118
msgid "Build Line"
msgstr "組立ライン"

#: lib/enums/ModelInformation.tsx:119
msgid "Build Lines"
msgstr "ビルドライン"

#: lib/enums/ModelInformation.tsx:126
msgid "Build Item"
msgstr "ビルドアイテム"

#: lib/enums/ModelInformation.tsx:127
msgid "Build Items"
msgstr "ビルドアイテム"

#: lib/enums/ModelInformation.tsx:132
#: src/pages/company/CompanyDetail.tsx:342
#: src/tables/company/CompanyTable.tsx:47
#: src/tables/company/ContactTable.tsx:67
msgid "Company"
msgstr "会社名"

#: lib/enums/ModelInformation.tsx:133
msgid "Companies"
msgstr "会社"

#: lib/enums/ModelInformation.tsx:140
#: src/pages/build/BuildDetail.tsx:310
#: src/pages/purchasing/PurchaseOrderDetail.tsx:235
#: src/pages/sales/ReturnOrderDetail.tsx:199
#: src/pages/sales/SalesOrderDetail.tsx:211
#: src/tables/ColumnRenderers.tsx:353
#: src/tables/Filter.tsx:278
#: src/tables/TableHoverCard.tsx:101
msgid "Project Code"
msgstr "プロジェクトコード"

#: lib/enums/ModelInformation.tsx:141
#: src/pages/Index/Settings/AdminCenter/Index.tsx:162
msgid "Project Codes"
msgstr "プロジェクトコード"

#: lib/enums/ModelInformation.tsx:147
#: src/components/wizards/OrderPartsWizard.tsx:183
#: src/pages/build/BuildDetail.tsx:227
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:33
#: src/pages/purchasing/PurchaseOrderDetail.tsx:531
#: src/pages/stock/StockDetail.tsx:349
#: src/tables/part/PartPurchaseOrdersTable.tsx:32
#: src/tables/stock/StockItemTable.tsx:240
#: src/tables/stock/StockTrackingTable.tsx:120
msgid "Purchase Order"
msgstr "注文"

#: lib/enums/ModelInformation.tsx:148
#: lib/enums/Roles.tsx:39
#: src/pages/Index/Settings/SystemSettings.tsx:283
#: src/pages/company/CompanyDetail.tsx:205
#: src/pages/company/SupplierPartDetail.tsx:266
#: src/pages/part/PartDetail.tsx:874
#: src/pages/purchasing/PurchasingIndex.tsx:60
msgid "Purchase Orders"
msgstr "購入注文"

#: lib/enums/ModelInformation.tsx:156
msgid "Purchase Order Line"
msgstr "発注ライン"

#: lib/enums/ModelInformation.tsx:157
msgid "Purchase Order Lines"
msgstr "発注ライン"

#: lib/enums/ModelInformation.tsx:162
#: src/pages/build/BuildDetail.tsx:283
#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#: src/pages/sales/SalesOrderDetail.tsx:586
#: src/pages/sales/SalesOrderShipmentDetail.tsx:94
#: src/pages/sales/SalesOrderShipmentDetail.tsx:358
#: src/pages/stock/StockDetail.tsx:358
#: src/tables/part/PartSalesAllocationsTable.tsx:41
#: src/tables/sales/SalesOrderAllocationTable.tsx:107
#: src/tables/stock/StockTrackingTable.tsx:131
msgid "Sales Order"
msgstr "セールスオーダー"

#: lib/enums/ModelInformation.tsx:163
#: lib/enums/Roles.tsx:43
#: src/pages/Index/Settings/SystemSettings.tsx:299
#: src/pages/company/CompanyDetail.tsx:225
#: src/pages/part/PartDetail.tsx:886
#: src/pages/sales/SalesIndex.tsx:82
msgid "Sales Orders"
msgstr "セールスオーダー"

#: lib/enums/ModelInformation.tsx:171
#: src/pages/sales/SalesOrderShipmentDetail.tsx:357
msgid "Sales Order Shipment"
msgstr "販売注文の出荷"

#: lib/enums/ModelInformation.tsx:172
msgid "Sales Order Shipments"
msgstr "販売注文の出荷"

#: lib/enums/ModelInformation.tsx:178
#: src/pages/sales/ReturnOrderDetail.tsx:516
#: src/tables/stock/StockTrackingTable.tsx:142
msgid "Return Order"
msgstr "リターンオーダー"

#: lib/enums/ModelInformation.tsx:179
#: lib/enums/Roles.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:315
#: src/pages/company/CompanyDetail.tsx:232
#: src/pages/part/PartDetail.tsx:893
#: src/pages/sales/SalesIndex.tsx:103
msgid "Return Orders"
msgstr "返品注文"

#: lib/enums/ModelInformation.tsx:187
msgid "Return Order Line Item"
msgstr "返品注文項目"

#: lib/enums/ModelInformation.tsx:188
msgid "Return Order Line Items"
msgstr "返品注文項目"

#: lib/enums/ModelInformation.tsx:193
#: src/tables/company/AddressTable.tsx:52
msgid "Address"
msgstr "住所"

#: lib/enums/ModelInformation.tsx:194
#: src/pages/company/CompanyDetail.tsx:266
msgid "Addresses"
msgstr "マイアカウント"

#: lib/enums/ModelInformation.tsx:200
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:89
#: src/pages/core/UserDetail.tsx:135
#: src/pages/purchasing/PurchaseOrderDetail.tsx:211
#: src/pages/sales/ReturnOrderDetail.tsx:175
#: src/pages/sales/SalesOrderDetail.tsx:187
msgid "Contact"
msgstr "お問い合わせ"

#: lib/enums/ModelInformation.tsx:201
#: src/pages/company/CompanyDetail.tsx:260
#: src/pages/core/CoreIndex.tsx:33
msgid "Contacts"
msgstr "連絡先"

#: lib/enums/ModelInformation.tsx:207
msgid "Owner"
msgstr "所有者"

#: lib/enums/ModelInformation.tsx:208
msgid "Owners"
msgstr "所有者"

#: lib/enums/ModelInformation.tsx:214
#: src/pages/Auth/ChangePassword.tsx:36
#: src/pages/core/UserDetail.tsx:220
#: src/tables/Filter.tsx:327
#: src/tables/settings/ApiTokenTable.tsx:105
#: src/tables/settings/ApiTokenTable.tsx:132
#: src/tables/settings/BarcodeScanHistoryTable.tsx:79
#: src/tables/settings/ExportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:77
#: src/tables/stock/StockItemTestResultTable.tsx:216
#: src/tables/stock/StockTrackingTable.tsx:190
#: src/tables/stock/StockTrackingTable.tsx:218
msgid "User"
msgstr "ユーザー"

#: lib/enums/ModelInformation.tsx:215
#: src/components/nav/NavigationDrawer.tsx:112
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:15
#: src/pages/core/CoreIndex.tsx:21
#: src/pages/core/UserDetail.tsx:226
msgid "Users"
msgstr "ユーザー"

#: lib/enums/ModelInformation.tsx:221
#: src/pages/core/GroupDetail.tsx:78
msgid "Group"
msgstr "グループ"

#: lib/enums/ModelInformation.tsx:222
#: src/components/nav/NavigationDrawer.tsx:118
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:23
#: src/pages/core/CoreIndex.tsx:27
#: src/pages/core/GroupDetail.tsx:82
#: src/pages/core/UserDetail.tsx:99
#: src/tables/settings/UserTable.tsx:276
msgid "Groups"
msgstr "グループ"

#: lib/enums/ModelInformation.tsx:229
msgid "Import Session"
msgstr "インポートセッション"

#: lib/enums/ModelInformation.tsx:230
msgid "Import Sessions"
msgstr "インポートセッション"

#: lib/enums/ModelInformation.tsx:237
msgid "Label Template"
msgstr "ラベルテンプレート"

#: lib/enums/ModelInformation.tsx:238
#: src/pages/Index/Settings/AdminCenter/Index.tsx:199
msgid "Label Templates"
msgstr "ラベルテンプレート"

#: lib/enums/ModelInformation.tsx:245
msgid "Report Template"
msgstr "レポートテンプレート"

#: lib/enums/ModelInformation.tsx:246
#: src/pages/Index/Settings/AdminCenter/Index.tsx:205
msgid "Report Templates"
msgstr "レポートテンプレート"

#: lib/enums/ModelInformation.tsx:253
#: src/components/plugins/PluginDrawer.tsx:145
msgid "Plugin Configuration"
msgstr "プラグインの設定"

#: lib/enums/ModelInformation.tsx:254
msgid "Plugin Configurations"
msgstr "プラグインの設定"

#: lib/enums/ModelInformation.tsx:261
msgid "Content Type"
msgstr "コンテンツタイプ"

#: lib/enums/ModelInformation.tsx:262
msgid "Content Types"
msgstr "コンテンツタイプ"

#: lib/enums/ModelInformation.tsx:267
msgid "Selection List"
msgstr "セレクションリスト"

#: lib/enums/ModelInformation.tsx:268
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:21
msgid "Selection Lists"
msgstr "セレクション・リスト"

#: lib/enums/ModelInformation.tsx:274
#: src/components/barcodes/BarcodeInput.tsx:114
#: src/components/dashboard/DashboardLayout.tsx:224
#: src/components/editors/NotesEditor.tsx:74
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:157
#: src/components/forms/fields/ApiFormField.tsx:263
#: src/components/forms/fields/TableField.tsx:45
#: src/components/importer/ImportDataSelector.tsx:192
#: src/components/importer/ImporterColumnSelector.tsx:217
#: src/components/importer/ImporterDrawer.tsx:88
#: src/components/modals/LicenseModal.tsx:85
#: src/components/nav/NavigationTree.tsx:210
#: src/components/nav/NotificationDrawer.tsx:235
#: src/components/nav/SearchDrawer.tsx:572
#: src/components/settings/SettingList.tsx:145
#: src/forms/BomForms.tsx:69
#: src/functions/auth.tsx:612
#: src/pages/ErrorPage.tsx:11
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:124
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:643
#: src/pages/part/PartPricingPanel.tsx:71
#: src/states/IconState.tsx:46
#: src/states/IconState.tsx:76
#: src/tables/InvenTreeTableHeader.tsx:125
#: src/tables/bom/BomTable.tsx:527
#: src/tables/stock/StockItemTestResultTable.tsx:335
msgid "Error"
msgstr "エラー"

#: lib/enums/ModelInformation.tsx:275
#: src/tables/machine/MachineListTable.tsx:354
#: src/tables/machine/MachineTypeTable.tsx:281
msgid "Errors"
msgstr "エラー"

#: lib/enums/Roles.tsx:31
msgid "Admin"
msgstr "管理者"

#: lib/enums/Roles.tsx:33
#: src/pages/Index/Settings/SystemSettings.tsx:264
#: src/pages/build/BuildIndex.tsx:75
#: src/pages/part/PartDetail.tsx:903
#: src/pages/sales/SalesOrderDetail.tsx:394
msgid "Build Orders"
msgstr "組立注文"

#: lib/enums/Roles.tsx:50
#: src/pages/Index/Settings/AdminCenter/Index.tsx:202
#~ msgid "Stocktake"
#~ msgstr "Stocktake"

#: src/components/Boundary.tsx:12
msgid "Error rendering component"
msgstr "エラー：コンポーネント描画"

#: src/components/Boundary.tsx:14
msgid "An error occurred while rendering this component. Refer to the console for more information."
msgstr "このコンポーネントの描画中にエラーが発生しました。詳細はコンソールを参照してください。"

#: src/components/DashboardItemProxy.tsx:34
#~ msgid "Title"
#~ msgstr "Title"

#: src/components/barcodes/BarcodeCameraInput.tsx:103
msgid "Error while scanning"
msgstr "スキャン中のエラー"

#: src/components/barcodes/BarcodeCameraInput.tsx:117
msgid "Error while stopping"
msgstr "停止時のエラー"

#: src/components/barcodes/BarcodeCameraInput.tsx:159
msgid "Start scanning by selecting a camera and pressing the play button."
msgstr "カメラを選択し、再生ボタンを押してスキャンを開始します。"

#: src/components/barcodes/BarcodeCameraInput.tsx:180
msgid "Stop scanning"
msgstr "スキャン停止"

#: src/components/barcodes/BarcodeCameraInput.tsx:190
msgid "Start scanning"
msgstr "スキャン開始"

#: src/components/barcodes/BarcodeInput.tsx:34
#: src/tables/general/BarcodeScanTable.tsx:55
#: src/tables/settings/BarcodeScanHistoryTable.tsx:64
msgid "Barcode"
msgstr "バーコード"

#: src/components/barcodes/BarcodeInput.tsx:35
#: src/components/barcodes/BarcodeKeyboardInput.tsx:18
#: src/defaults/actions.tsx:72
msgid "Scan"
msgstr "スキャン"

#: src/components/barcodes/BarcodeInput.tsx:53
msgid "Camera Input"
msgstr "カメラ入力"

#: src/components/barcodes/BarcodeInput.tsx:63
msgid "Scanner Input"
msgstr "スキャナー入力"

#: src/components/barcodes/BarcodeInput.tsx:105
msgid "Barcode Data"
msgstr "バーコード情報"

#: src/components/barcodes/BarcodeInput.tsx:109
msgid "No barcode data"
msgstr "バーコードデータなし"

#: src/components/barcodes/BarcodeInput.tsx:110
msgid "Scan or enter barcode data"
msgstr "バーコードデータをスキャンまたは入力"

#: src/components/barcodes/BarcodeKeyboardInput.tsx:64
msgid "Enter barcode data"
msgstr "バーコードデータの入力"

#: src/components/barcodes/BarcodeScanDialog.tsx:49
#: src/components/buttons/ScanButton.tsx:15
#: src/components/nav/NavigationDrawer.tsx:129
#: src/forms/PurchaseOrderForms.tsx:454
#: src/forms/PurchaseOrderForms.tsx:560
msgid "Scan Barcode"
msgstr "バーコードをスキャン"

#: src/components/barcodes/BarcodeScanDialog.tsx:105
msgid "No matching item found"
msgstr "一致するアイテムは見つかりませんでした"

#: src/components/barcodes/BarcodeScanDialog.tsx:134
msgid "Barcode does not match the expected model type"
msgstr ""

#: src/components/barcodes/BarcodeScanDialog.tsx:145
#: src/components/editors/NotesEditor.tsx:84
#: src/components/editors/NotesEditor.tsx:118
#: src/components/forms/ApiForm.tsx:451
#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:45
#: src/tables/bom/BomTable.tsx:518
#: src/tables/settings/PendingTasksTable.tsx:68
msgid "Success"
msgstr "成功"

#: src/components/barcodes/BarcodeScanDialog.tsx:151
msgid "Failed to handle barcode"
msgstr ""

#: src/components/barcodes/BarcodeScanDialog.tsx:167
#: src/pages/Index/Scan.tsx:129
msgid "Failed to scan barcode"
msgstr "バーコードのスキャンに失敗しました"

#: src/components/barcodes/QRCode.tsx:94
msgid "Low (7%)"
msgstr "Low (7%)"

#: src/components/barcodes/QRCode.tsx:95
msgid "Medium (15%)"
msgstr "Medium (15%)"

#: src/components/barcodes/QRCode.tsx:96
msgid "Quartile (25%)"
msgstr "Quartile (25%)"

#: src/components/barcodes/QRCode.tsx:97
msgid "High (30%)"
msgstr "High (30%)"

#: src/components/barcodes/QRCode.tsx:107
msgid "Custom barcode"
msgstr "カスタムバーコード"

#: src/components/barcodes/QRCode.tsx:108
msgid "A custom barcode is registered for this item. The shown code is not that custom barcode."
msgstr "この商品にはカスタムバーコードが登録されています。表示されているコードはそのカスタムバーコードではありません。"

#: src/components/barcodes/QRCode.tsx:127
msgid "Barcode Data:"
msgstr "バーコードデータ："

#: src/components/barcodes/QRCode.tsx:138
msgid "Select Error Correction Level"
msgstr "エラー訂正レベルの選択"

#: src/components/barcodes/QRCode.tsx:170
msgid "Failed to link barcode"
msgstr "バーコードのリンクに失敗"

#: src/components/barcodes/QRCode.tsx:179
#: src/pages/part/PartDetail.tsx:522
#: src/pages/purchasing/PurchaseOrderDetail.tsx:204
#: src/pages/sales/ReturnOrderDetail.tsx:168
#: src/pages/sales/SalesOrderDetail.tsx:180
#: src/pages/sales/SalesOrderShipmentDetail.tsx:168
msgid "Link"
msgstr "リンク"

#: src/components/barcodes/QRCode.tsx:200
msgid "This will remove the link to the associated barcode"
msgstr "これにより、関連するバーコードへのリンクが削除されます。"

#: src/components/barcodes/QRCode.tsx:205
#: src/components/items/ActionDropdown.tsx:190
#: src/forms/PurchaseOrderForms.tsx:551
msgid "Unlink Barcode"
msgstr "リンク解除バーコード"

#: src/components/buttons/AdminButton.tsx:86
msgid "Open in admin interface"
msgstr "管理画面で開く"

#: src/components/buttons/CopyButton.tsx:18
#~ msgid "Copy to clipboard"
#~ msgstr "Copy to clipboard"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copied"
msgstr "コピーしました"

#: src/components/buttons/CopyButton.tsx:31
msgid "Copy"
msgstr "コピー"

#: src/components/buttons/PrintingActions.tsx:51
msgid "Printing Labels"
msgstr "ラベル印刷中"

#: src/components/buttons/PrintingActions.tsx:56
msgid "Printing Reports"
msgstr "レポート印刷中"

#: src/components/buttons/PrintingActions.tsx:77
#~ msgid "Printing"
#~ msgstr "Printing"

#: src/components/buttons/PrintingActions.tsx:78
#~ msgid "Printing completed successfully"
#~ msgstr "Printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:114
#~ msgid "Label printing completed successfully"
#~ msgstr "Label printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:116
msgid "Print Label"
msgstr "ラベルの印刷"

#: src/components/buttons/PrintingActions.tsx:121
#~ msgid "The label could not be generated"
#~ msgstr "The label could not be generated"

#: src/components/buttons/PrintingActions.tsx:122
#: src/components/buttons/PrintingActions.tsx:148
msgid "Print"
msgstr "印刷"

#: src/components/buttons/PrintingActions.tsx:131
msgid "Print Report"
msgstr "印刷レポート"

#: src/components/buttons/PrintingActions.tsx:152
#~ msgid "Generate"
#~ msgstr "Generate"

#: src/components/buttons/PrintingActions.tsx:153
#~ msgid "Report printing completed successfully"
#~ msgstr "Report printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:159
#~ msgid "The report could not be generated"
#~ msgstr "The report could not be generated"

#: src/components/buttons/PrintingActions.tsx:169
msgid "Printing Actions"
msgstr "印刷アクション"

#: src/components/buttons/PrintingActions.tsx:174
msgid "Print Labels"
msgstr "ラベル印刷"

#: src/components/buttons/PrintingActions.tsx:180
msgid "Print Reports"
msgstr "印刷レポート"

#: src/components/buttons/RemoveRowButton.tsx:8
msgid "Remove this row"
msgstr "この行を削除"

#: src/components/buttons/SSOButton.tsx:40
msgid "You will be redirected to the provider for further actions."
msgstr "プロバイダーにリダイレクトされます。"

#: src/components/buttons/SSOButton.tsx:44
#~ msgid "This provider is not full set up."
#~ msgstr "This provider is not full set up."

#: src/components/buttons/SSOButton.tsx:54
#~ msgid "Sign in redirect failed."
#~ msgstr "Sign in redirect failed."

#: src/components/buttons/ScanButton.tsx:15
#~ msgid "Scan QR code"
#~ msgstr "Scan QR code"

#: src/components/buttons/ScanButton.tsx:19
msgid "Open Barcode Scanner"
msgstr "バーコードスキャナを開く"

#: src/components/buttons/ScanButton.tsx:20
#~ msgid "Open QR code scanner"
#~ msgstr "Open QR code scanner"

#: src/components/buttons/SpotlightButton.tsx:12
msgid "Open spotlight"
msgstr "スポットライトを開く"

#: src/components/buttons/StarredToggleButton.tsx:36
msgid "Subscription Updated"
msgstr ""

#: src/components/buttons/StarredToggleButton.tsx:57
#~ msgid "Unsubscribe from part"
#~ msgstr "Unsubscribe from part"

#: src/components/buttons/StarredToggleButton.tsx:66
msgid "Unsubscribe from notifications"
msgstr "通知の配信停止"

#: src/components/buttons/StarredToggleButton.tsx:67
msgid "Subscribe to notifications"
msgstr "通知を受け取る"

#: src/components/calendar/Calendar.tsx:99
#: src/components/calendar/Calendar.tsx:162
msgid "Calendar Filters"
msgstr "カレンダーフィルター"

#: src/components/calendar/Calendar.tsx:114
msgid "Previous month"
msgstr "先月"

#: src/components/calendar/Calendar.tsx:123
msgid "Select month"
msgstr "月を選択"

#: src/components/calendar/Calendar.tsx:144
msgid "Next month"
msgstr "来月"

#: src/components/calendar/Calendar.tsx:175
#: src/tables/InvenTreeTableHeader.tsx:291
msgid "Download data"
msgstr "ダウンロードデータ"

#: src/components/calendar/OrderCalendar.tsx:132
msgid "Order Updated"
msgstr "オーダー更新完了"

#: src/components/calendar/OrderCalendar.tsx:142
msgid "Error updating order"
msgstr "オーダー更新エラー"

#: src/components/calendar/OrderCalendar.tsx:178
#: src/tables/Filter.tsx:144
msgid "Overdue"
msgstr "締め切り超過"

#: src/components/dashboard/DashboardLayout.tsx:225
msgid "Failed to load dashboard widgets."
msgstr ""

#: src/components/dashboard/DashboardLayout.tsx:235
msgid "No Widgets Selected"
msgstr "ウィジェット未選択"

#: src/components/dashboard/DashboardLayout.tsx:238
msgid "Use the menu to add widgets to the dashboard"
msgstr "メニューを使用して、ダッシュボードにウィジェットを追加します"

#: src/components/dashboard/DashboardMenu.tsx:62
#: src/components/dashboard/DashboardMenu.tsx:138
msgid "Accept Layout"
msgstr "レイアウトを受け入れる"

#: src/components/dashboard/DashboardMenu.tsx:94
#: src/components/nav/NavigationDrawer.tsx:71
#: src/defaults/actions.tsx:28
#: src/defaults/links.tsx:31
#: src/pages/Index/Home.tsx:8
msgid "Dashboard"
msgstr "ダッシュボード"

#: src/components/dashboard/DashboardMenu.tsx:102
msgid "Edit Layout"
msgstr "レイアウトを編集"

#: src/components/dashboard/DashboardMenu.tsx:111
msgid "Add Widget"
msgstr "ウィジェット追加"

#: src/components/dashboard/DashboardMenu.tsx:120
msgid "Remove Widgets"
msgstr "ウィジェットの削除"

#: src/components/dashboard/DashboardMenu.tsx:129
msgid "Clear Widgets"
msgstr ""

#: src/components/dashboard/DashboardWidget.tsx:81
msgid "Remove this widget from the dashboard"
msgstr "ダッシュボードからこのウィジェットを削除"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:77
msgid "Filter dashboard widgets"
msgstr "ダッシュボード・ウィジェットのフィルタリング"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:98
msgid "Add this widget to the dashboard"
msgstr "このウィジェットをダッシュボードに追加します"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:123
msgid "No Widgets Available"
msgstr "ウィジェットなし"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:124
msgid "There are no more widgets available for the dashboard"
msgstr "ダッシュボードで使用できるウィジェットはなくなりました"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:24
msgid "Subscribed Parts"
msgstr "加入部品"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:25
msgid "Show the number of parts which you have subscribed to"
msgstr "登録済みパーツ数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:31
msgid "Subscribed Categories"
msgstr "通知カテゴリー "

#: src/components/dashboard/DashboardWidgetLibrary.tsx:32
msgid "Show the number of part categories which you have subscribed to"
msgstr "登録済み部品カテゴリー数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:38
msgid "Invalid BOMs"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:39
msgid "Assemblies requiring bill of materials validation"
msgstr ""

#: src/components/dashboard/DashboardWidgetLibrary.tsx:50
#: src/tables/part/PartTable.tsx:250
msgid "Low Stock"
msgstr "在庫少"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:52
msgid "Show the number of parts which are low on stock"
msgstr "低在庫部品数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:57
msgid "Required for Build Orders"
msgstr "ご注文に必要なもの"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:59
msgid "Show parts which are required for active build orders"
msgstr "有効な製造指示の必要部品を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:64
msgid "Expired Stock Items"
msgstr "期限切れ在庫品"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:66
msgid "Show the number of stock items which have expired"
msgstr "期限切れ在庫品数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:72
msgid "Stale Stock Items"
msgstr "古くなった在庫品"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:74
msgid "Show the number of stock items which are stale"
msgstr "古くなった在庫品数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:80
msgid "Active Build Orders"
msgstr "有効な製造指示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:82
msgid "Show the number of build orders which are currently active"
msgstr "現在有効な製造指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:87
msgid "Overdue Build Orders"
msgstr "期限切れ受注"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:89
msgid "Show the number of build orders which are overdue"
msgstr "期限切れ製造指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:94
msgid "Assigned Build Orders"
msgstr "割り当てられた製造指示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:96
msgid "Show the number of build orders which are assigned to you"
msgstr "割り当てられた製造指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:101
msgid "Active Sales Orders"
msgstr "有効な受注"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:103
msgid "Show the number of sales orders which are currently active"
msgstr "現在有効な受注数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:108
msgid "Overdue Sales Orders"
msgstr "販売期限切れ注文"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:110
msgid "Show the number of sales orders which are overdue"
msgstr "期限切れ受注数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:115
msgid "Assigned Sales Orders"
msgstr "割り当てられた受注"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:117
msgid "Show the number of sales orders which are assigned to you"
msgstr "割り当てられた受注数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:122
msgid "Active Purchase Orders"
msgstr "有効な購入指示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:124
msgid "Show the number of purchase orders which are currently active"
msgstr "現在有効な購入指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:129
msgid "Overdue Purchase Orders"
msgstr "期限切れ発注書"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:131
msgid "Show the number of purchase orders which are overdue"
msgstr "期限切れ購入指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:136
msgid "Assigned Purchase Orders"
msgstr "割り当てられた購入指示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:138
msgid "Show the number of purchase orders which are assigned to you"
msgstr "割り当てられている購入指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:143
msgid "Active Return Orders"
msgstr "有効な返品指示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:145
msgid "Show the number of return orders which are currently active"
msgstr "現在有効な返品指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:150
msgid "Overdue Return Orders"
msgstr "期限切れ返品指示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:152
msgid "Show the number of return orders which are overdue"
msgstr "期限切れ返品指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:157
msgid "Assigned Return Orders"
msgstr "割り当てられた返品指示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:159
msgid "Show the number of return orders which are assigned to you"
msgstr "割り当てられた返品指示の数を表示"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:179
#: src/components/dashboard/widgets/GetStartedWidget.tsx:15
#: src/defaults/links.tsx:86
msgid "Getting Started"
msgstr "はじめましょう"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:180
#: src/defaults/links.tsx:89
msgid "Getting started with InvenTree"
msgstr "InvenTreeの利用開始"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:187
#: src/components/dashboard/widgets/NewsWidget.tsx:123
msgid "News Updates"
msgstr "最新ニュース"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:188
msgid "The latest news from InvenTree"
msgstr "InvenTreeの最新ニュース"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:18
#: src/components/nav/MainMenu.tsx:87
msgid "Change Color Mode"
msgstr "カラーモードの変更"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:23
msgid "Change the color mode of the user interface"
msgstr "ユーザーインターフェースのカラーモードの変更"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:18
msgid "Change Language"
msgstr "言語を変更"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:23
msgid "Change the language of the user interface"
msgstr "ユーザーインターフェースの言語変更"

#: src/components/dashboard/widgets/NewsWidget.tsx:60
#: src/components/nav/NotificationDrawer.tsx:94
#: src/pages/Notifications.tsx:53
msgid "Mark as read"
msgstr "既読にする"

#: src/components/dashboard/widgets/NewsWidget.tsx:115
msgid "Requires Superuser"
msgstr "管理者権限が必要"

#: src/components/dashboard/widgets/NewsWidget.tsx:116
msgid "This widget requires superuser permissions"
msgstr "このウィジェットには管理者権限が必要です"

#: src/components/dashboard/widgets/NewsWidget.tsx:133
msgid "No News"
msgstr "ニュースなし"

#: src/components/dashboard/widgets/NewsWidget.tsx:134
msgid "There are no unread news items"
msgstr "未読のニュースはありません"

#: src/components/details/Details.tsx:117
#~ msgid "Email:"
#~ msgstr "Email:"

#: src/components/details/Details.tsx:123
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:76
#: src/pages/core/UserDetail.tsx:93
#: src/pages/core/UserDetail.tsx:203
#: src/tables/settings/UserTable.tsx:420
msgid "Superuser"
msgstr "スーパーユーザー"

#: src/components/details/Details.tsx:124
#: src/pages/core/UserDetail.tsx:87
#: src/pages/core/UserDetail.tsx:200
#: src/tables/settings/UserTable.tsx:415
msgid "Staff"
msgstr "スタッフ"

#: src/components/details/Details.tsx:125
msgid "Email: "
msgstr "メールアドレス:"

#: src/components/details/Details.tsx:407
msgid "No name defined"
msgstr "名称未定"

#: src/components/details/DetailsImage.tsx:77
msgid "Remove Image"
msgstr "画像を削除"

#: src/components/details/DetailsImage.tsx:80
msgid "Remove the associated image from this item?"
msgstr "このアイテムから関連画像を削除しますか？"

#: src/components/details/DetailsImage.tsx:83
#: src/forms/StockForms.tsx:828
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:203
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:408
msgid "Remove"
msgstr "削除"

#: src/components/details/DetailsImage.tsx:109
msgid "Drag and drop to upload"
msgstr "ドラッグ＆ドロップでアップロード"

#: src/components/details/DetailsImage.tsx:112
msgid "Click to select file(s)"
msgstr "クリックしてファイルを選択"

#: src/components/details/DetailsImage.tsx:172
msgid "Image uploaded"
msgstr "画像アップロード完了"

#: src/components/details/DetailsImage.tsx:173
msgid "Image has been uploaded successfully"
msgstr "画像は正常にアップロードされました"

#: src/components/details/DetailsImage.tsx:180
#: src/tables/general/AttachmentTable.tsx:201
msgid "Upload Error"
msgstr "アップロードエラー"

#: src/components/details/DetailsImage.tsx:250
msgid "Clear"
msgstr "クリア"

#: src/components/details/DetailsImage.tsx:256
#: src/components/forms/ApiForm.tsx:661
#: src/contexts/ThemeContext.tsx:44
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:654
msgid "Submit"
msgstr "送信"

#: src/components/details/DetailsImage.tsx:300
msgid "Select from existing images"
msgstr "既存の画像から選択"

#: src/components/details/DetailsImage.tsx:308
msgid "Select Image"
msgstr "画像を選択"

#: src/components/details/DetailsImage.tsx:324
msgid "Download remote image"
msgstr "リモート画像ダウンロード"

#: src/components/details/DetailsImage.tsx:339
msgid "Upload new image"
msgstr "新しい画像をアップロード"

#: src/components/details/DetailsImage.tsx:346
msgid "Upload Image"
msgstr "画像をアップロード"

#: src/components/details/DetailsImage.tsx:359
msgid "Delete image"
msgstr "画像を削除"

#: src/components/details/DetailsImage.tsx:393
msgid "Download Image"
msgstr "イメージのダウンロード"

#: src/components/details/DetailsImage.tsx:398
msgid "Image downloaded successfully"
msgstr "画像のダウンロードに成功しました"

#: src/components/details/PartIcons.tsx:43
#~ msgid "Part is a template part (variants can be made from this part)"
#~ msgstr "Part is a template part (variants can be made from this part)"

#: src/components/details/PartIcons.tsx:49
#~ msgid "Part can be assembled from other parts"
#~ msgstr "Part can be assembled from other parts"

#: src/components/details/PartIcons.tsx:55
#~ msgid "Part can be used in assemblies"
#~ msgstr "Part can be used in assemblies"

#: src/components/details/PartIcons.tsx:61
#~ msgid "Part stock is tracked by serial number"
#~ msgstr "Part stock is tracked by serial number"

#: src/components/details/PartIcons.tsx:67
#~ msgid "Part can be purchased from external suppliers"
#~ msgstr "Part can be purchased from external suppliers"

#: src/components/details/PartIcons.tsx:73
#~ msgid "Part can be sold to customers"
#~ msgstr "Part can be sold to customers"

#: src/components/details/PartIcons.tsx:78
#~ msgid "Part is virtual (not a physical part)"
#~ msgstr "Part is virtual (not a physical part)"

#: src/components/editors/NotesEditor.tsx:75
msgid "Image upload failed"
msgstr "画像のアップロードに失敗しました"

#: src/components/editors/NotesEditor.tsx:85
msgid "Image uploaded successfully"
msgstr "画像のアップロードに成功しました"

#: src/components/editors/NotesEditor.tsx:119
msgid "Notes saved successfully"
msgstr "メモが正常に保存されました"

#: src/components/editors/NotesEditor.tsx:130
msgid "Failed to save notes"
msgstr "メモの保存に失敗しました"

#: src/components/editors/NotesEditor.tsx:133
msgid "Error Saving Notes"
msgstr "メモの保存エラー"

#: src/components/editors/NotesEditor.tsx:151
#~ msgid "Disable Editing"
#~ msgstr "Disable Editing"

#: src/components/editors/NotesEditor.tsx:153
msgid "Save Notes"
msgstr "メモを保存"

#: src/components/editors/NotesEditor.tsx:172
msgid "Close Editor"
msgstr "エディターを閉じる"

#: src/components/editors/NotesEditor.tsx:179
msgid "Enable Editing"
msgstr "編集を有効にする"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Preview Notes"
#~ msgstr "Preview Notes"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Edit Notes"
#~ msgstr "Edit Notes"

#: src/components/editors/TemplateEditor/CodeEditor/index.tsx:9
msgid "Code"
msgstr "コード"

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:44
#~ msgid "Failed to parse error response from server."
#~ msgstr "Failed to parse error response from server."

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:50
msgid "Error rendering preview"
msgstr ""

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:120
msgid "Preview not available, click \"Reload Preview\"."
msgstr "プレビューが表示されない場合は、「プレビューの再読み込み」をクリックしてください。"

#: src/components/editors/TemplateEditor/PdfPreview/index.tsx:9
msgid "PDF Preview"
msgstr "PDF プレビュー"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:109
msgid "Error loading template"
msgstr "テンプレートの読み込みエラー"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:121
msgid "Error saving template"
msgstr "テンプレートの保存エラー"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:151
#~ msgid "Save & Reload preview?"
#~ msgstr "Save & Reload preview?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:158
msgid "Could not load the template from the server."
msgstr "サーバーからテンプレートをロードできませんでした。"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:175
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:306
msgid "Save & Reload Preview"
msgstr "保存と再読み込みプレビュー"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:180
msgid "Are you sure you want to Save & Reload the preview?"
msgstr "プレビューを保存して再読み込みしますか？"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:182
msgid "To render the preview the current template needs to be replaced on the server with your modifications which may break the label if it is under active use. Do you want to proceed?"
msgstr "プレビューを表示するには、現在のテンプレートをサーバー上であなたの修正に置き換える必要があります。続行しますか？"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:186
msgid "Save & Reload"
msgstr "保存と再読み込み"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:218
msgid "Preview updated"
msgstr "プレビューの更新完了"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:219
msgid "The preview has been updated successfully."
msgstr "プレビューは正常に更新されました。"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:263
#~ msgid "Save & Reload preview"
#~ msgstr "Save & Reload preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:298
msgid "Reload preview"
msgstr "プレビューを再読み込み"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:299
msgid "Use the currently stored template from the server"
msgstr "サーバーから現在保存されているテンプレートを使用"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:307
msgid "Save the current template and reload the preview"
msgstr "現在のテンプレートを保存し、プレビューを再読み込みします"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:322
#~ msgid "to preview"
#~ msgstr "to preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:366
msgid "Select instance to preview"
msgstr "プレビューするインスタンスを選択"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:410
msgid "Error rendering template"
msgstr "テンプレート描画エラー"

#: src/components/errors/ClientError.tsx:23
msgid "Client Error"
msgstr "クライアントエラー"

#: src/components/errors/ClientError.tsx:24
msgid "Client error occurred"
msgstr "クライアントエラーが発生しました"

#: src/components/errors/GenericErrorPage.tsx:50
msgid "Status Code"
msgstr "ステータスコード"

#: src/components/errors/GenericErrorPage.tsx:63
msgid "Return to the index page"
msgstr "インデックスページに戻る"

#: src/components/errors/NotAuthenticated.tsx:8
msgid "Not Authenticated"
msgstr "認証されませんでした"

#: src/components/errors/NotAuthenticated.tsx:9
msgid "You are not logged in."
msgstr "ログインしていません。"

#: src/components/errors/NotFound.tsx:8
msgid "Page Not Found"
msgstr "お探しのページが見つかりません。"

#: src/components/errors/NotFound.tsx:9
msgid "This page does not exist"
msgstr "このページは存在しません"

#: src/components/errors/PermissionDenied.tsx:8
#: src/functions/notifications.tsx:25
msgid "Permission Denied"
msgstr "権限がありません"

#: src/components/errors/PermissionDenied.tsx:9
msgid "You do not have permission to view this page."
msgstr "このページを表示する権限がありません。"

#: src/components/errors/ServerError.tsx:8
msgid "Server Error"
msgstr "サーバーエラー"

#: src/components/errors/ServerError.tsx:9
msgid "A server error occurred"
msgstr "サーバーエラーが発生しました"

#: src/components/forms/ApiForm.tsx:103
#: src/components/forms/ApiForm.tsx:580
msgid "Form Error"
msgstr "フォームエラー"

#: src/components/forms/ApiForm.tsx:487
#~ msgid "Form Errors Exist"
#~ msgstr "Form Errors Exist"

#: src/components/forms/ApiForm.tsx:588
msgid "Errors exist for one or more form fields"
msgstr "1つ以上のフォームフィールドにエラーがあります"

#: src/components/forms/ApiForm.tsx:699
#: src/hooks/UseForm.tsx:129
#: src/tables/plugin/PluginListTable.tsx:204
msgid "Update"
msgstr "更新"

#: src/components/forms/AuthenticationForm.tsx:48
#: src/components/forms/AuthenticationForm.tsx:74
#: src/functions/auth.tsx:83
#~ msgid "Check your your input and try again."
#~ msgstr "Check your your input and try again."

#: src/components/forms/AuthenticationForm.tsx:52
#~ msgid "Welcome back!"
#~ msgstr "Welcome back!"

#: src/components/forms/AuthenticationForm.tsx:53
#~ msgid "Login successfull"
#~ msgstr "Login successfull"

#: src/components/forms/AuthenticationForm.tsx:65
#: src/functions/auth.tsx:74
#~ msgid "Mail delivery successfull"
#~ msgstr "Mail delivery successfull"

#: src/components/forms/AuthenticationForm.tsx:73
msgid "Login successful"
msgstr "ログインが成功しました"

#: src/components/forms/AuthenticationForm.tsx:74
msgid "Logged in successfully"
msgstr "ログイン成功"

#: src/components/forms/AuthenticationForm.tsx:81
#: src/components/forms/AuthenticationForm.tsx:89
msgid "Login failed"
msgstr "ログインに失敗しました"

#: src/components/forms/AuthenticationForm.tsx:82
#: src/components/forms/AuthenticationForm.tsx:90
#: src/components/forms/AuthenticationForm.tsx:106
#: src/functions/auth.tsx:282
msgid "Check your input and try again."
msgstr "入力内容を確認し、もう一度やり直してください。"

#: src/components/forms/AuthenticationForm.tsx:100
#: src/functions/auth.tsx:273
msgid "Mail delivery successful"
msgstr "メール送信成功"

#: src/components/forms/AuthenticationForm.tsx:101
msgid "Check your inbox for the login link. If you have an account, you will receive a login link. Check in spam too."
msgstr "ログイン用リンクを受信箱でご確認ください。アカウントをお持ちの場合は、ログイン用リンクが届きます。迷惑メールボックスもチェックしてください。"

#: src/components/forms/AuthenticationForm.tsx:105
msgid "Mail delivery failed"
msgstr "メール送信失敗"

#: src/components/forms/AuthenticationForm.tsx:125
msgid "Or continue with other methods"
msgstr "あるいは他の方法で続けますか"

#: src/components/forms/AuthenticationForm.tsx:136
#: src/components/forms/AuthenticationForm.tsx:296
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:64
#: src/pages/core/UserDetail.tsx:48
msgid "Username"
msgstr "ユーザー名"

#: src/components/forms/AuthenticationForm.tsx:136
#~ msgid "I will use username and password"
#~ msgstr "I will use username and password"

#: src/components/forms/AuthenticationForm.tsx:138
#: src/components/forms/AuthenticationForm.tsx:298
msgid "Your username"
msgstr "ユーザー名"

#: src/components/forms/AuthenticationForm.tsx:143
#: src/components/forms/AuthenticationForm.tsx:311
#: src/pages/Auth/ResetPassword.tsx:34
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:708
msgid "Password"
msgstr "パスワード"

#: src/components/forms/AuthenticationForm.tsx:145
#: src/components/forms/AuthenticationForm.tsx:313
msgid "Your password"
msgstr "パスワード"

#: src/components/forms/AuthenticationForm.tsx:164
msgid "Reset password"
msgstr "パスワードを再設定"

#: src/components/forms/AuthenticationForm.tsx:173
#: src/components/forms/AuthenticationForm.tsx:303
#: src/pages/Auth/Reset.tsx:17
#: src/pages/core/UserDetail.tsx:71
msgid "Email"
msgstr "メールアドレス"

#: src/components/forms/AuthenticationForm.tsx:174
#: src/pages/Auth/Reset.tsx:18
msgid "We will send you a link to login - if you are registered"
msgstr "登録されている方には、ログイン用のリンクをお送りします"

#: src/components/forms/AuthenticationForm.tsx:190
msgid "Send me an email"
msgstr "メールを送る"

#: src/components/forms/AuthenticationForm.tsx:192
msgid "Use username and password"
msgstr "ユーザー名とパスワードを使用"

#: src/components/forms/AuthenticationForm.tsx:201
msgid "Log In"
msgstr "ログイン"

#: src/components/forms/AuthenticationForm.tsx:203
#: src/pages/Auth/Reset.tsx:26
msgid "Send Email"
msgstr "Eメールを送信"

#: src/components/forms/AuthenticationForm.tsx:239
msgid "Passwords do not match"
msgstr "パスワードが一致しません"

#: src/components/forms/AuthenticationForm.tsx:256
msgid "Registration successful"
msgstr "登録が完了しました"

#: src/components/forms/AuthenticationForm.tsx:257
msgid "Please confirm your email address to complete the registration"
msgstr "ご登録のメールアドレスをご確認ください"

#: src/components/forms/AuthenticationForm.tsx:280
msgid "Input error"
msgstr "入力エラー"

#: src/components/forms/AuthenticationForm.tsx:281
msgid "Check your input and try again. "
msgstr "入力内容を確認し、もう一度やり直してください。"

#: src/components/forms/AuthenticationForm.tsx:305
msgid "This will be used for a confirmation"
msgstr "これは確認に使用されます"

#: src/components/forms/AuthenticationForm.tsx:318
msgid "Password repeat"
msgstr "パスワードを再入力"

#: src/components/forms/AuthenticationForm.tsx:320
msgid "Repeat password"
msgstr "パスワードを再入力"

#: src/components/forms/AuthenticationForm.tsx:332
#: src/pages/Auth/Login.tsx:121
#: src/pages/Auth/Register.tsx:13
msgid "Register"
msgstr "登録"

#: src/components/forms/AuthenticationForm.tsx:338
msgid "Or use SSO"
msgstr "またはSSOを使用します。"

#: src/components/forms/AuthenticationForm.tsx:348
msgid "Registration not active"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:349
msgid "This might be related to missing mail settings or could be a deliberate decision."
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:36
#: src/components/forms/HostOptionsForm.tsx:67
msgid "Host"
msgstr "ホスト"

#: src/components/forms/HostOptionsForm.tsx:42
#: src/components/forms/HostOptionsForm.tsx:70
#: src/components/forms/InstanceOptions.tsx:124
#: src/components/plugins/PluginDrawer.tsx:68
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:19
#: src/pages/part/CategoryDetail.tsx:86
#: src/pages/part/PartDetail.tsx:447
#: src/pages/stock/LocationDetail.tsx:84
#: src/tables/machine/MachineTypeTable.tsx:72
#: src/tables/machine/MachineTypeTable.tsx:118
#: src/tables/machine/MachineTypeTable.tsx:236
#: src/tables/machine/MachineTypeTable.tsx:339
#: src/tables/plugin/PluginErrorTable.tsx:33
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:36
#: src/tables/settings/ApiTokenTable.tsx:58
#: src/tables/settings/GroupTable.tsx:95
#: src/tables/settings/GroupTable.tsx:148
#: src/tables/settings/GroupTable.tsx:196
#: src/tables/settings/PendingTasksTable.tsx:37
#: src/tables/stock/LocationTypesTable.tsx:74
msgid "Name"
msgstr "名前"

#: src/components/forms/HostOptionsForm.tsx:75
msgid "No one here..."
msgstr "ここには誰も..."

#: src/components/forms/HostOptionsForm.tsx:86
msgid "Add Host"
msgstr "ホストを追加"

#: src/components/forms/HostOptionsForm.tsx:90
#: src/components/items/RoleTable.tsx:224
#: src/components/items/TransferList.tsx:215
#: src/components/items/TransferList.tsx:223
msgid "Save"
msgstr "保存"

#: src/components/forms/InstanceOptions.tsx:43
#~ msgid "Select destination instance"
#~ msgstr "Select destination instance"

#: src/components/forms/InstanceOptions.tsx:58
msgid "Select Server"
msgstr "サーバーの選択"

#: src/components/forms/InstanceOptions.tsx:68
#: src/components/forms/InstanceOptions.tsx:92
msgid "Edit host options"
msgstr "ホストオプションの編集"

#: src/components/forms/InstanceOptions.tsx:71
#~ msgid "Edit possible host options"
#~ msgstr "Edit possible host options"

#: src/components/forms/InstanceOptions.tsx:76
msgid "Save host selection"
msgstr "ホスト選択の保存"

#: src/components/forms/InstanceOptions.tsx:98
#~ msgid "Version: {0}"
#~ msgstr "Version: {0}"

#: src/components/forms/InstanceOptions.tsx:100
#~ msgid "API:{0}"
#~ msgstr "API:{0}"

#: src/components/forms/InstanceOptions.tsx:102
#~ msgid "Name: {0}"
#~ msgstr "Name: {0}"

#: src/components/forms/InstanceOptions.tsx:104
#~ msgid "State: <0>worker</0> ({0}), <1>plugins</1>{1}"
#~ msgstr "State: <0>worker</0> ({0}), <1>plugins</1>{1}"

#: src/components/forms/InstanceOptions.tsx:118
#: src/pages/Index/Settings/SystemSettings.tsx:45
msgid "Server"
msgstr "サーバー"

#: src/components/forms/InstanceOptions.tsx:130
#: src/components/plugins/PluginDrawer.tsx:88
#: src/tables/plugin/PluginListTable.tsx:127
msgid "Version"
msgstr "バージョン"

#: src/components/forms/InstanceOptions.tsx:136
#: src/components/modals/AboutInvenTreeModal.tsx:122
#: src/components/modals/ServerInfoModal.tsx:34
msgid "API Version"
msgstr "API バージョン"

#: src/components/forms/InstanceOptions.tsx:142
#: src/components/nav/NavigationDrawer.tsx:205
#: src/pages/Index/Settings/AdminCenter/Index.tsx:218
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:46
msgid "Plugins"
msgstr "プラグイン"

#: src/components/forms/InstanceOptions.tsx:143
#: src/tables/part/PartTestTemplateTable.tsx:117
#: src/tables/settings/TemplateTable.tsx:251
#: src/tables/settings/TemplateTable.tsx:362
#: src/tables/stock/StockItemTestResultTable.tsx:416
msgid "Enabled"
msgstr "有効"

#: src/components/forms/InstanceOptions.tsx:143
msgid "Disabled"
msgstr "無効"

#: src/components/forms/InstanceOptions.tsx:149
msgid "Worker"
msgstr "担当者"

#: src/components/forms/InstanceOptions.tsx:150
#: src/tables/settings/FailedTasksTable.tsx:48
msgid "Stopped"
msgstr "中断"

#: src/components/forms/InstanceOptions.tsx:150
msgid "Running"
msgstr "実行中"

#: src/components/forms/fields/IconField.tsx:83
msgid "No icon selected"
msgstr "アイコン選択なし"

#: src/components/forms/fields/IconField.tsx:161
msgid "Uncategorized"
msgstr "未分類"

#: src/components/forms/fields/IconField.tsx:211
#: src/components/nav/Layout.tsx:80
#: src/tables/part/PartThumbTable.tsx:201
msgid "Search..."
msgstr "検索…"

#: src/components/forms/fields/IconField.tsx:225
msgid "Select category"
msgstr "カテゴリを選択"

#: src/components/forms/fields/IconField.tsx:234
msgid "Select pack"
msgstr "パック選択"

#. placeholder {0}: filteredIcons.length
#: src/components/forms/fields/IconField.tsx:239
msgid "{0} icons"
msgstr "{0} アイコン"

#: src/components/forms/fields/RelatedModelField.tsx:388
#: src/components/modals/AboutInvenTreeModal.tsx:94
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:332
msgid "Loading"
msgstr "読み込み中"

#: src/components/forms/fields/RelatedModelField.tsx:390
msgid "No results found"
msgstr "一致するものが見つかりませんでした"

#: src/components/forms/fields/TableField.tsx:46
msgid "modelRenderer entry required for tables"
msgstr "テーブルに必要な modelRenderer エントリ"

#: src/components/forms/fields/TableField.tsx:187
msgid "No entries available"
msgstr "選択した機能に使用できるエントリはありません"

#: src/components/forms/fields/TableField.tsx:198
msgid "Add new row"
msgstr "新しい行を追加"

#: src/components/images/DetailsImage.tsx:252
#~ msgid "Select image"
#~ msgstr "Select image"

#: src/components/images/Thumbnail.tsx:12
msgid "Thumbnail"
msgstr "サムネイル"

#: src/components/importer/ImportDataSelector.tsx:175
msgid "Importing Rows"
msgstr "行のインポート"

#: src/components/importer/ImportDataSelector.tsx:176
msgid "Please wait while the data is imported"
msgstr "データがインポートされるまでお待ちください"

#: src/components/importer/ImportDataSelector.tsx:193
msgid "An error occurred while importing data"
msgstr "データのインポート中にエラーが発生しました"

#: src/components/importer/ImportDataSelector.tsx:214
msgid "Edit Data"
msgstr "データの編集"

#: src/components/importer/ImportDataSelector.tsx:246
msgid "Delete Row"
msgstr "行の削除"

#: src/components/importer/ImportDataSelector.tsx:276
msgid "Row"
msgstr "行"

#: src/components/importer/ImportDataSelector.tsx:294
msgid "Row contains errors"
msgstr "行にエラーが含まれています"

#: src/components/importer/ImportDataSelector.tsx:335
msgid "Accept"
msgstr "承諾"

#: src/components/importer/ImportDataSelector.tsx:368
msgid "Valid"
msgstr "有効"

#: src/components/importer/ImportDataSelector.tsx:369
msgid "Filter by row validation status"
msgstr "行の検証ステータスによるフィルタリング"

#: src/components/importer/ImportDataSelector.tsx:374
#: src/components/wizards/WizardDrawer.tsx:101
#: src/tables/build/BuildOutputTable.tsx:543
msgid "Complete"
msgstr "完了"

#: src/components/importer/ImportDataSelector.tsx:375
msgid "Filter by row completion status"
msgstr "行の完了ステータスによるフィルタリング"

#: src/components/importer/ImportDataSelector.tsx:393
msgid "Import selected rows"
msgstr "選択行のインポート"

#: src/components/importer/ImportDataSelector.tsx:408
msgid "Processing Data"
msgstr "加工データ"

#: src/components/importer/ImporterColumnSelector.tsx:55
#: src/components/importer/ImporterColumnSelector.tsx:186
#: src/components/items/ErrorItem.tsx:12
#: src/functions/api.tsx:60
#: src/functions/auth.tsx:333
msgid "An error occurred"
msgstr "エラーが発生しました"

#: src/components/importer/ImporterColumnSelector.tsx:68
msgid "Select column, or leave blank to ignore this field."
msgstr "列を選択するか、このフィールドを無視する場合は空白のままにします。"

#: src/components/importer/ImporterColumnSelector.tsx:91
#~ msgid "Select a column from the data file"
#~ msgstr "Select a column from the data file"

#: src/components/importer/ImporterColumnSelector.tsx:104
#~ msgid "Map data columns to database fields"
#~ msgstr "Map data columns to database fields"

#: src/components/importer/ImporterColumnSelector.tsx:119
#~ msgid "Imported Column Name"
#~ msgstr "Imported Column Name"

#: src/components/importer/ImporterColumnSelector.tsx:192
msgid "Ignore this field"
msgstr "このフィールドを無視する"

#: src/components/importer/ImporterColumnSelector.tsx:206
msgid "Mapping data columns to database fields"
msgstr "データ・カラムとデータベース・フィールドのマッピング"

#: src/components/importer/ImporterColumnSelector.tsx:211
msgid "Accept Column Mapping"
msgstr "カラムマッピングの受け入れ"

#: src/components/importer/ImporterColumnSelector.tsx:224
msgid "Database Field"
msgstr "データベース・フィールド"

#: src/components/importer/ImporterColumnSelector.tsx:225
msgid "Field Description"
msgstr "フィールドの説明"

#: src/components/importer/ImporterColumnSelector.tsx:226
msgid "Imported Column"
msgstr "インポートされた列"

#: src/components/importer/ImporterColumnSelector.tsx:227
msgid "Default Value"
msgstr "初期値"

#: src/components/importer/ImporterDrawer.tsx:43
msgid "Upload File"
msgstr "ファイルをアップロードする"

#: src/components/importer/ImporterDrawer.tsx:44
msgid "Map Columns"
msgstr "マップ列"

#: src/components/importer/ImporterDrawer.tsx:45
msgid "Import Data"
msgstr "データをインポート"

#: src/components/importer/ImporterDrawer.tsx:46
msgid "Process Data"
msgstr "加工データ"

#: src/components/importer/ImporterDrawer.tsx:47
msgid "Complete Import"
msgstr "インポート完了"

#: src/components/importer/ImporterDrawer.tsx:89
msgid "Failed to fetch import session data"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:97
#~ msgid "Cancel import session"
#~ msgstr "Cancel import session"

#: src/components/importer/ImporterDrawer.tsx:104
msgid "Import Complete"
msgstr "インポート完了"

#: src/components/importer/ImporterDrawer.tsx:107
msgid "Data has been imported successfully"
msgstr "データは正常にインポートされました"

#: src/components/importer/ImporterDrawer.tsx:109
#: src/components/modals/AboutInvenTreeModal.tsx:197
#: src/components/modals/ServerInfoModal.tsx:134
#: src/forms/BomForms.tsx:132
msgid "Close"
msgstr "閉じる"

#: src/components/importer/ImporterDrawer.tsx:119
#~ msgid "Import session has unknown status"
#~ msgstr "Import session has unknown status"

#: src/components/importer/ImporterDrawer.tsx:128
msgid "Importing Data"
msgstr "データのインポート"

#: src/components/importer/ImporterImportProgress.tsx:36
#~ msgid "Importing Records"
#~ msgstr "Importing Records"

#: src/components/importer/ImporterImportProgress.tsx:39
#~ msgid "Imported rows"
#~ msgstr "Imported rows"

#: src/components/importer/ImporterStatus.tsx:19
msgid "Unknown Status"
msgstr "ステータス不明"

#: src/components/items/ActionDropdown.tsx:133
msgid "Options"
msgstr "オプション"

#: src/components/items/ActionDropdown.tsx:162
#~ msgid "Link custom barcode"
#~ msgstr "Link custom barcode"

#: src/components/items/ActionDropdown.tsx:169
#: src/tables/InvenTreeTableHeader.tsx:193
#: src/tables/InvenTreeTableHeader.tsx:194
msgid "Barcode Actions"
msgstr "バーコードアクション"

#: src/components/items/ActionDropdown.tsx:174
msgid "View Barcode"
msgstr "バーコードを表示"

#: src/components/items/ActionDropdown.tsx:176
msgid "View barcode"
msgstr "バーコードを表示"

#: src/components/items/ActionDropdown.tsx:182
msgid "Link Barcode"
msgstr "リンクバーコード"

#: src/components/items/ActionDropdown.tsx:184
msgid "Link a custom barcode to this item"
msgstr "このアイテムにカスタムバーコードをリンク"

#: src/components/items/ActionDropdown.tsx:192
msgid "Unlink custom barcode"
msgstr "カスタムバーコードのリンク解除"

#: src/components/items/ActionDropdown.tsx:244
msgid "Edit item"
msgstr "アイテムを編集"

#: src/components/items/ActionDropdown.tsx:256
msgid "Delete item"
msgstr "アイテムを削除"

#: src/components/items/ActionDropdown.tsx:264
#: src/components/items/ActionDropdown.tsx:265
msgid "Hold"
msgstr "保留する"

#: src/components/items/ActionDropdown.tsx:288
msgid "Duplicate item"
msgstr "アイテムを複製"

#: src/components/items/BarcodeInput.tsx:24
#~ msgid "Scan barcode data here using barcode scanner"
#~ msgstr "Scan barcode data here using barcode scanner"

#: src/components/items/ColorToggle.tsx:17
msgid "Toggle color scheme"
msgstr "配色の切り替え"

#: src/components/items/DocTooltip.tsx:92
#: src/components/items/GettingStartedCarousel.tsx:20
msgid "Read More"
msgstr "続きを読む"

#: src/components/items/ErrorItem.tsx:8
#: src/functions/api.tsx:51
#: src/tables/settings/PendingTasksTable.tsx:80
msgid "Unknown error"
msgstr "不明なエラー"

#: src/components/items/ErrorItem.tsx:13
#~ msgid "An error occurred:"
#~ msgstr "An error occurred:"

#: src/components/items/GettingStartedCarousel.tsx:27
#~ msgid "Read more"
#~ msgstr "Read more"

#: src/components/items/InfoItem.tsx:27
msgid "None"
msgstr "なし"

#: src/components/items/InvenTreeLogo.tsx:23
msgid "InvenTree Logo"
msgstr "InvenTree ロゴ"

#: src/components/items/LanguageToggle.tsx:21
msgid "Select language"
msgstr "言語を選択"

#: src/components/items/OnlyStaff.tsx:10
#: src/components/modals/AboutInvenTreeModal.tsx:50
msgid "This information is only available for staff users"
msgstr "この情報はスタッフユーザーのみ利用可能です。"

#: src/components/items/Placeholder.tsx:14
#~ msgid "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."
#~ msgstr "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."

#: src/components/items/Placeholder.tsx:17
#~ msgid "PLH"
#~ msgstr "PLH"

#: src/components/items/RoleTable.tsx:81
msgid "Updating"
msgstr "更新中"

#: src/components/items/RoleTable.tsx:82
msgid "Updating group roles"
msgstr "グループロールの更新中"

#: src/components/items/RoleTable.tsx:118
#: src/components/settings/ConfigValueList.tsx:42
#: src/pages/part/pricing/BomPricingPanel.tsx:191
#: src/pages/part/pricing/VariantPricingPanel.tsx:51
#: src/tables/purchasing/SupplierPartTable.tsx:137
msgid "Updated"
msgstr "更新しました"

#: src/components/items/RoleTable.tsx:119
msgid "Group roles updated"
msgstr "グループロールが更新されました"

#: src/components/items/RoleTable.tsx:135
msgid "Role"
msgstr "ロール"

#: src/components/items/RoleTable.tsx:140
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:413
msgid "View"
msgstr "表示"

#: src/components/items/RoleTable.tsx:145
msgid "Change"
msgstr "変更"

#: src/components/items/RoleTable.tsx:150
#: src/forms/StockForms.tsx:867
#: src/tables/stock/StockItemTestResultTable.tsx:364
msgid "Add"
msgstr "追加"

#: src/components/items/RoleTable.tsx:203
msgid "Reset group roles"
msgstr "グループロールをリセット"

#: src/components/items/RoleTable.tsx:212
msgid "Reset"
msgstr "リセット"

#: src/components/items/RoleTable.tsx:215
msgid "Save group roles"
msgstr "グループロールの保存"

#: src/components/items/TransferList.tsx:65
msgid "No items"
msgstr "項目なし"

#: src/components/items/TransferList.tsx:161
#: src/components/render/Stock.tsx:95
#: src/pages/part/PartDetail.tsx:980
#: src/pages/stock/StockDetail.tsx:262
#: src/pages/stock/StockDetail.tsx:913
#: src/tables/build/BuildAllocatedStockTable.tsx:135
#: src/tables/build/BuildLineTable.tsx:190
#: src/tables/part/PartTable.tsx:124
#: src/tables/stock/StockItemTable.tsx:183
#: src/tables/stock/StockItemTable.tsx:344
msgid "Available"
msgstr "利用可能"

#: src/components/items/TransferList.tsx:162
msgid "Selected"
msgstr "選択済み"

#: src/components/modals/AboutInvenTreeModal.tsx:103
#~ msgid "Your InvenTree version status is"
#~ msgstr "Your InvenTree version status is"

#: src/components/modals/AboutInvenTreeModal.tsx:116
msgid "InvenTree Version"
msgstr "InvenTreeバージョン"

#: src/components/modals/AboutInvenTreeModal.tsx:128
msgid "Python Version"
msgstr "Python Version"

#: src/components/modals/AboutInvenTreeModal.tsx:133
msgid "Django Version"
msgstr "ジャンゴバージョン"

#: src/components/modals/AboutInvenTreeModal.tsx:142
msgid "Commit Hash"
msgstr "コミットハッシュ"

#: src/components/modals/AboutInvenTreeModal.tsx:147
msgid "Commit Date"
msgstr "コミット日"

#: src/components/modals/AboutInvenTreeModal.tsx:152
msgid "Commit Branch"
msgstr "コミット・ブランチ"

#: src/components/modals/AboutInvenTreeModal.tsx:163
msgid "Version Information"
msgstr "バージョン情報"

#: src/components/modals/AboutInvenTreeModal.tsx:165
#~ msgid "Credits"
#~ msgstr "Credits"

#: src/components/modals/AboutInvenTreeModal.tsx:168
#~ msgid "InvenTree Documentation"
#~ msgstr "InvenTree Documentation"

#: src/components/modals/AboutInvenTreeModal.tsx:169
#~ msgid "View Code on GitHub"
#~ msgstr "View Code on GitHub"

#: src/components/modals/AboutInvenTreeModal.tsx:172
msgid "Links"
msgstr "リンク"

#: src/components/modals/AboutInvenTreeModal.tsx:178
#: src/components/nav/NavigationDrawer.tsx:217
#: src/defaults/actions.tsx:35
msgid "Documentation"
msgstr "ドキュメント"

#: src/components/modals/AboutInvenTreeModal.tsx:179
msgid "Source Code"
msgstr "ソースコード"

#: src/components/modals/AboutInvenTreeModal.tsx:180
msgid "Mobile App"
msgstr "モバイルアプリ"

#: src/components/modals/AboutInvenTreeModal.tsx:181
msgid "Submit Bug Report"
msgstr "バグレポートの提出"

#: src/components/modals/AboutInvenTreeModal.tsx:189
#: src/components/modals/ServerInfoModal.tsx:147
#~ msgid "Dismiss"
#~ msgstr "Dismiss"

#: src/components/modals/AboutInvenTreeModal.tsx:190
msgid "Copy version information"
msgstr "バージョン情報のコピー"

#: src/components/modals/AboutInvenTreeModal.tsx:207
msgid "Development Version"
msgstr "開発バージョン"

#: src/components/modals/AboutInvenTreeModal.tsx:209
msgid "Up to Date"
msgstr "最新情報"

#: src/components/modals/AboutInvenTreeModal.tsx:211
msgid "Update Available"
msgstr "利用可能なアップデート"

#: src/components/modals/LicenseModal.tsx:41
msgid "No license text available"
msgstr "ライセンステキストはありません"

#: src/components/modals/LicenseModal.tsx:48
msgid "No Information provided - this is likely a server issue"
msgstr "サーバーの問題だと思われます"

#: src/components/modals/LicenseModal.tsx:81
msgid "Loading license information"
msgstr "ライセンス情報の読み込み"

#: src/components/modals/LicenseModal.tsx:87
msgid "Failed to fetch license information"
msgstr "ライセンス情報の取得に失敗しました"

#: src/components/modals/LicenseModal.tsx:99
msgid "{key} Packages"
msgstr "{key} パッケージ"

#: src/components/modals/QrCodeModal.tsx:24
#~ msgid "Unknown response"
#~ msgstr "Unknown response"

#: src/components/modals/QrCodeModal.tsx:39
#~ msgid "No scans yet!"
#~ msgstr "No scans yet!"

#: src/components/modals/QrCodeModal.tsx:57
#~ msgid "Close modal"
#~ msgstr "Close modal"

#: src/components/modals/ServerInfoModal.tsx:22
msgid "Instance Name"
msgstr "インスタンス名"

#: src/components/modals/ServerInfoModal.tsx:28
msgid "Server Version"
msgstr "Serve Version"

#: src/components/modals/ServerInfoModal.tsx:38
#~ msgid "Bebug Mode"
#~ msgstr "Bebug Mode"

#: src/components/modals/ServerInfoModal.tsx:40
msgid "Database"
msgstr "データベース"

#: src/components/modals/ServerInfoModal.tsx:49
#: src/components/nav/Alerts.tsx:41
msgid "Debug Mode"
msgstr "デバッグモード"

#: src/components/modals/ServerInfoModal.tsx:54
msgid "Server is running in debug mode"
msgstr "サーバーはデバッグモードで動作しています。"

#: src/components/modals/ServerInfoModal.tsx:62
msgid "Docker Mode"
msgstr "ドッカーモード"

#: src/components/modals/ServerInfoModal.tsx:65
msgid "Server is deployed using docker"
msgstr "サーバーは docker を使ってデプロイします。"

#: src/components/modals/ServerInfoModal.tsx:71
msgid "Plugin Support"
msgstr "プラグインのサポート"

#: src/components/modals/ServerInfoModal.tsx:76
msgid "Plugin support enabled"
msgstr "プラグイン対応"

#: src/components/modals/ServerInfoModal.tsx:78
msgid "Plugin support disabled"
msgstr "プラグインサポート無効"

#: src/components/modals/ServerInfoModal.tsx:85
msgid "Server status"
msgstr "サーバーの状態"

#: src/components/modals/ServerInfoModal.tsx:91
msgid "Healthy"
msgstr "健康"

#: src/components/modals/ServerInfoModal.tsx:93
msgid "Issues detected"
msgstr "検出された問題"

#: src/components/modals/ServerInfoModal.tsx:102
#: src/components/nav/Alerts.tsx:50
msgid "Background Worker"
msgstr "バックグラウンドワーカー"

#: src/components/modals/ServerInfoModal.tsx:107
msgid "The background worker process is not running"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:107
#~ msgid "The Background worker process is not running."
#~ msgstr "The Background worker process is not running."

#: src/components/modals/ServerInfoModal.tsx:115
#: src/pages/Index/Settings/AdminCenter/Index.tsx:119
msgid "Email Settings"
msgstr "メール設定"

#: src/components/modals/ServerInfoModal.tsx:118
#~ msgid "Email settings not configured"
#~ msgstr "Email settings not configured"

#: src/components/modals/ServerInfoModal.tsx:120
#: src/components/nav/Alerts.tsx:61
msgid "Email settings not configured."
msgstr "Eメール設定は行なわれていません"

#: src/components/nav/Alerts.tsx:43
msgid "The server is running in debug mode."
msgstr "サーバーはデバッグモードで動作しています"

#: src/components/nav/Alerts.tsx:52
msgid "The background worker process is not running."
msgstr "バックグラウンドワーカープロセスが実行されていません。"

#: src/components/nav/Alerts.tsx:59
msgid "Email settings"
msgstr "Eメール設定"

#: src/components/nav/Alerts.tsx:68
msgid "Server Restart"
msgstr "サーバー再起動"

#: src/components/nav/Alerts.tsx:70
msgid "The server requires a restart to apply changes."
msgstr "変更を適用するにはサーバーの再起動が必要です。"

#: src/components/nav/Alerts.tsx:80
msgid "Database Migrations"
msgstr "データベースの移行"

#: src/components/nav/Alerts.tsx:82
msgid "There are pending database migrations."
msgstr "保留中のデータベース移行があります"

#: src/components/nav/Alerts.tsx:98
msgid "Alerts"
msgstr "アラート"

#: src/components/nav/Alerts.tsx:141
msgid "Learn more about {code}"
msgstr "{code} についてもっと知る"

#: src/components/nav/Header.tsx:187
#: src/components/nav/NavigationDrawer.tsx:141
#: src/components/nav/NotificationDrawer.tsx:181
#: src/pages/Index/Settings/SystemSettings.tsx:121
#: src/pages/Index/Settings/UserSettings.tsx:106
#: src/pages/Notifications.tsx:45
#: src/pages/Notifications.tsx:130
msgid "Notifications"
msgstr "通知"

#: src/components/nav/Layout.tsx:83
msgid "Nothing found..."
msgstr "見つかりませんでした…"

#: src/components/nav/MainMenu.tsx:40
#: src/pages/Index/Profile/Profile.tsx:15
#~ msgid "Profile"
#~ msgstr "Profile"

#: src/components/nav/MainMenu.tsx:52
#: src/components/nav/NavigationDrawer.tsx:193
#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:21
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:39
msgid "Settings"
msgstr "設定"

#: src/components/nav/MainMenu.tsx:59
#: src/pages/Index/Settings/UserSettings.tsx:145
msgid "Account Settings"
msgstr "アカウント設定"

#: src/components/nav/MainMenu.tsx:59
#: src/defaults/menuItems.tsx:15
#~ msgid "Account settings"
#~ msgstr "Account settings"

#: src/components/nav/MainMenu.tsx:67
#: src/components/nav/NavigationDrawer.tsx:153
#: src/components/nav/SettingsHeader.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:347
#: src/pages/Index/Settings/SystemSettings.tsx:352
msgid "System Settings"
msgstr "システム設定"

#: src/components/nav/MainMenu.tsx:68
#~ msgid "Current language {locale}"
#~ msgstr "Current language {locale}"

#: src/components/nav/MainMenu.tsx:71
#~ msgid "Switch to pseudo language"
#~ msgstr "Switch to pseudo language"

#: src/components/nav/MainMenu.tsx:76
#: src/components/nav/NavigationDrawer.tsx:160
#: src/components/nav/SettingsHeader.tsx:42
#: src/defaults/actions.tsx:83
#: src/pages/Index/Settings/AdminCenter/Index.tsx:282
#: src/pages/Index/Settings/AdminCenter/Index.tsx:287
msgid "Admin Center"
msgstr "管理センター"

#: src/components/nav/MainMenu.tsx:96
msgid "Logout"
msgstr "ログアウト"

#: src/components/nav/NavHoverMenu.tsx:84
#~ msgid "View all"
#~ msgstr "View all"

#: src/components/nav/NavHoverMenu.tsx:100
#: src/components/nav/NavHoverMenu.tsx:110
#~ msgid "Get started"
#~ msgstr "Get started"

#: src/components/nav/NavHoverMenu.tsx:103
#~ msgid "Overview over high-level objects, functions and possible usecases."
#~ msgstr "Overview over high-level objects, functions and possible usecases."

#: src/components/nav/NavigationDrawer.tsx:60
#~ msgid "Pages"
#~ msgstr "Pages"

#: src/components/nav/NavigationDrawer.tsx:84
#: src/components/render/Part.tsx:33
#: src/defaults/links.tsx:42
#: src/forms/StockForms.tsx:735
#: src/pages/Index/Settings/SystemSettings.tsx:224
#: src/pages/part/PartDetail.tsx:804
#: src/pages/stock/LocationDetail.tsx:390
#: src/pages/stock/StockDetail.tsx:626
#: src/tables/stock/StockItemTable.tsx:86
msgid "Stock"
msgstr "在庫"

#: src/components/nav/NavigationDrawer.tsx:91
#: src/defaults/links.tsx:48
#: src/pages/build/BuildDetail.tsx:741
#: src/pages/build/BuildIndex.tsx:102
msgid "Manufacturing"
msgstr "製造"

#: src/components/nav/NavigationDrawer.tsx:98
#: src/defaults/links.tsx:54
#: src/pages/company/ManufacturerDetail.tsx:9
#: src/pages/company/ManufacturerPartDetail.tsx:260
#: src/pages/company/SupplierDetail.tsx:9
#: src/pages/company/SupplierPartDetail.tsx:356
#: src/pages/purchasing/PurchaseOrderDetail.tsx:534
#: src/pages/purchasing/PurchasingIndex.tsx:122
msgid "Purchasing"
msgstr "購買"

#: src/components/nav/NavigationDrawer.tsx:105
#: src/defaults/links.tsx:60
#: src/pages/company/CustomerDetail.tsx:9
#: src/pages/sales/ReturnOrderDetail.tsx:521
#: src/pages/sales/SalesIndex.tsx:139
#: src/pages/sales/SalesOrderDetail.tsx:591
#: src/pages/sales/SalesOrderShipmentDetail.tsx:360
msgid "Sales"
msgstr "販売"

#: src/components/nav/NavigationDrawer.tsx:147
#: src/components/nav/SettingsHeader.tsx:40
#: src/pages/Index/Settings/UserSettings.tsx:141
msgid "User Settings"
msgstr "ユーザー設定"

#: src/components/nav/NavigationDrawer.tsx:188
msgid "Navigation"
msgstr "ナビゲーション"

#: src/components/nav/NavigationDrawer.tsx:223
msgid "About"
msgstr "概要"

#: src/components/nav/NavigationTree.tsx:211
msgid "Error loading navigation tree."
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:183
#: src/pages/Notifications.tsx:74
msgid "Mark all as read"
msgstr "すべて既読にする"

#: src/components/nav/NotificationDrawer.tsx:193
msgid "View all notifications"
msgstr "すべての通知を表示"

#: src/components/nav/NotificationDrawer.tsx:216
msgid "You have no unread notifications."
msgstr "未読の通知はありません。"

#: src/components/nav/NotificationDrawer.tsx:238
msgid "Error loading notifications."
msgstr ""

#: src/components/nav/SearchDrawer.tsx:106
msgid "No Overview Available"
msgstr "概要不明"

#: src/components/nav/SearchDrawer.tsx:107
msgid "No overview available for this model type"
msgstr "このモデルタイプの概要はありません"

#: src/components/nav/SearchDrawer.tsx:125
msgid "View all results"
msgstr "全ての検索結果を表示"

#: src/components/nav/SearchDrawer.tsx:140
msgid "results"
msgstr "結果"

#: src/components/nav/SearchDrawer.tsx:144
msgid "Remove search group"
msgstr "検索グループの削除"

#: src/components/nav/SearchDrawer.tsx:288
#: src/pages/company/ManufacturerPartDetail.tsx:176
#: src/pages/part/PartDetail.tsx:861
#: src/pages/part/PartSupplierDetail.tsx:15
#: src/pages/purchasing/PurchasingIndex.tsx:81
msgid "Suppliers"
msgstr "仕入先"

#: src/components/nav/SearchDrawer.tsx:298
#: src/pages/part/PartSupplierDetail.tsx:23
#: src/pages/purchasing/PurchasingIndex.tsx:98
msgid "Manufacturers"
msgstr "メーカー"

#: src/components/nav/SearchDrawer.tsx:308
#: src/pages/sales/SalesIndex.tsx:124
msgid "Customers"
msgstr "顧客"

#: src/components/nav/SearchDrawer.tsx:462
#~ msgid "No results"
#~ msgstr "No results"

#: src/components/nav/SearchDrawer.tsx:477
msgid "Enter search text"
msgstr "検索文字列の入力"

#: src/components/nav/SearchDrawer.tsx:488
msgid "Refresh search results"
msgstr "検索結果の更新"

#: src/components/nav/SearchDrawer.tsx:499
#: src/components/nav/SearchDrawer.tsx:506
msgid "Search Options"
msgstr "検索設定"

#: src/components/nav/SearchDrawer.tsx:509
msgid "Whole word search"
msgstr "全単語検索"

#: src/components/nav/SearchDrawer.tsx:518
msgid "Regex search"
msgstr "正規表現検索"

#: src/components/nav/SearchDrawer.tsx:527
msgid "Notes search"
msgstr "備考検索"

#: src/components/nav/SearchDrawer.tsx:575
msgid "An error occurred during search query"
msgstr "検索中にエラーが発生しました"

#: src/components/nav/SearchDrawer.tsx:586
#: src/tables/part/PartTestTemplateTable.tsx:82
msgid "No Results"
msgstr "該当なし"

#: src/components/nav/SearchDrawer.tsx:589
msgid "No results available for search query"
msgstr "検索結果がありません"

#: src/components/panels/AttachmentPanel.tsx:18
msgid "Attachments"
msgstr "添付ファイル"

#: src/components/panels/NotesPanel.tsx:23
#: src/tables/build/BuildOrderTestTable.tsx:196
#: src/tables/stock/StockTrackingTable.tsx:212
msgid "Notes"
msgstr "メモ"

#: src/components/panels/PanelGroup.tsx:158
msgid "Plugin Provided"
msgstr ""

#: src/components/panels/PanelGroup.tsx:275
msgid "Collapse panels"
msgstr ""

#: src/components/panels/PanelGroup.tsx:275
msgid "Expand panels"
msgstr ""

#: src/components/plugins/LocateItemButton.tsx:68
#: src/components/plugins/LocateItemButton.tsx:88
msgid "Locate Item"
msgstr "アイテムを探す"

#: src/components/plugins/LocateItemButton.tsx:70
msgid "Item location requested"
msgstr "ご希望のアイテム位置"

#: src/components/plugins/PluginDrawer.tsx:47
msgid "Plugin Inactive"
msgstr "プラグインは無効"

#: src/components/plugins/PluginDrawer.tsx:50
msgid "Plugin is not active"
msgstr "プラグインがアクティブではありません"

#: src/components/plugins/PluginDrawer.tsx:59
msgid "Plugin Information"
msgstr "プラグイン情報"

#: src/components/plugins/PluginDrawer.tsx:73
#: src/forms/selectionListFields.tsx:104
#: src/pages/build/BuildDetail.tsx:244
#: src/pages/company/CompanyDetail.tsx:93
#: src/pages/company/ManufacturerPartDetail.tsx:91
#: src/pages/company/ManufacturerPartDetail.tsx:118
#: src/pages/company/SupplierPartDetail.tsx:144
#: src/pages/part/CategoryDetail.tsx:106
#: src/pages/part/PartDetail.tsx:461
#: src/pages/purchasing/PurchaseOrderDetail.tsx:144
#: src/pages/sales/ReturnOrderDetail.tsx:109
#: src/pages/sales/SalesOrderDetail.tsx:118
#: src/pages/stock/LocationDetail.tsx:104
#: src/tables/ColumnRenderers.tsx:269
#: src/tables/build/BuildAllocatedStockTable.tsx:91
#: src/tables/machine/MachineTypeTable.tsx:128
#: src/tables/machine/MachineTypeTable.tsx:239
#: src/tables/plugin/PluginListTable.tsx:110
msgid "Description"
msgstr "説明"

#: src/components/plugins/PluginDrawer.tsx:78
msgid "Author"
msgstr "投稿者"

#: src/components/plugins/PluginDrawer.tsx:83
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:41
#: src/pages/part/pricing/SaleHistoryPanel.tsx:38
#: src/tables/ColumnRenderers.tsx:411
#: src/tables/build/BuildOrderTestTable.tsx:204
msgid "Date"
msgstr "日付"

#: src/components/plugins/PluginDrawer.tsx:93
#: src/forms/selectionListFields.tsx:105
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:68
#: src/pages/core/UserDetail.tsx:81
#: src/pages/core/UserDetail.tsx:209
#: src/pages/part/PartDetail.tsx:615
#: src/tables/bom/UsedInTable.tsx:90
#: src/tables/company/CompanyTable.tsx:57
#: src/tables/company/CompanyTable.tsx:91
#: src/tables/machine/MachineListTable.tsx:336
#: src/tables/machine/MachineListTable.tsx:606
#: src/tables/part/ParametricPartTable.tsx:350
#: src/tables/part/PartTable.tsx:184
#: src/tables/part/PartVariantTable.tsx:15
#: src/tables/plugin/PluginListTable.tsx:96
#: src/tables/plugin/PluginListTable.tsx:412
#: src/tables/purchasing/SupplierPartTable.tsx:85
#: src/tables/purchasing/SupplierPartTable.tsx:179
#: src/tables/settings/ApiTokenTable.tsx:63
#: src/tables/settings/UserTable.tsx:410
#: src/tables/stock/StockItemTable.tsx:323
msgid "Active"
msgstr "有効"

#: src/components/plugins/PluginDrawer.tsx:105
msgid "Package Name"
msgstr "ご利用プラン"

#: src/components/plugins/PluginDrawer.tsx:111
msgid "Installation Path"
msgstr "設置経路"

#: src/components/plugins/PluginDrawer.tsx:116
#: src/tables/machine/MachineTypeTable.tsx:151
#: src/tables/machine/MachineTypeTable.tsx:275
#: src/tables/plugin/PluginListTable.tsx:101
#: src/tables/plugin/PluginListTable.tsx:417
msgid "Builtin"
msgstr "組み込み"

#: src/components/plugins/PluginDrawer.tsx:121
msgid "Package"
msgstr "パッケージ"

#: src/components/plugins/PluginDrawer.tsx:133
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:55
#: src/pages/Index/Settings/SystemSettings.tsx:330
#: src/pages/Index/Settings/UserSettings.tsx:128
msgid "Plugin Settings"
msgstr "プラグイン設定"

#: src/components/plugins/PluginPanel.tsx:87
#~ msgid "Error occurred while rendering plugin content"
#~ msgstr "Error occurred while rendering plugin content"

#: src/components/plugins/PluginPanel.tsx:91
#~ msgid "Plugin did not provide panel rendering function"
#~ msgstr "Plugin did not provide panel rendering function"

#: src/components/plugins/PluginPanel.tsx:103
#~ msgid "No content provided for this plugin"
#~ msgstr "No content provided for this plugin"

#: src/components/plugins/PluginPanel.tsx:116
#: src/components/plugins/PluginSettingsPanel.tsx:76
#~ msgid "Error Loading Plugin"
#~ msgstr "Error Loading Plugin"

#: src/components/plugins/PluginSettingsPanel.tsx:51
#~ msgid "Error occurred while rendering plugin settings"
#~ msgstr "Error occurred while rendering plugin settings"

#: src/components/plugins/PluginSettingsPanel.tsx:55
#~ msgid "Plugin did not provide settings rendering function"
#~ msgstr "Plugin did not provide settings rendering function"

#: src/components/plugins/PluginUIFeature.tsx:102
msgid "Error occurred while rendering the template editor."
msgstr "テンプレート・エディタのレンダリング中にエラーが発生しました。"

#: src/components/plugins/PluginUIFeature.tsx:119
msgid "Error Loading Plugin Editor"
msgstr "プラグインエディタの読み込みエラー"

#: src/components/plugins/PluginUIFeature.tsx:155
msgid "Error occurred while rendering the template preview."
msgstr "テンプレート・プレビューのレンダリング中にエラーが発生しました。"

#: src/components/plugins/PluginUIFeature.tsx:166
msgid "Error Loading Plugin Preview"
msgstr "プラグインプレビューの読み込みエラー"

#: src/components/plugins/RemoteComponent.tsx:111
msgid "Invalid source or function name"
msgstr "無効なソース名または関数名"

#: src/components/plugins/RemoteComponent.tsx:143
msgid "Error Loading Content"
msgstr "コンテンツの読み込みエラー"

#: src/components/plugins/RemoteComponent.tsx:147
msgid "Error occurred while loading plugin content"
msgstr "プラグインコンテンツの読み込み中にエラーが発生しました"

#: src/components/render/Instance.tsx:238
#~ msgid "Unknown model: {model}"
#~ msgstr "Unknown model: {model}"

#: src/components/render/Instance.tsx:246
msgid "Unknown model: {model_name}"
msgstr "不明なモデル{model_name}"

#: src/components/render/ModelType.tsx:234
#~ msgid "Purchase Order Line Item"
#~ msgstr "Purchase Order Line Item"

#: src/components/render/ModelType.tsx:264
#~ msgid "Unknown Model"
#~ msgstr "Unknown Model"

#: src/components/render/ModelType.tsx:307
#~ msgid "Purchase Order Line Items"
#~ msgstr "Purchase Order Line Items"

#: src/components/render/ModelType.tsx:337
#~ msgid "Unknown Models"
#~ msgstr "Unknown Models"

#: src/components/render/Order.tsx:122
#: src/tables/sales/SalesOrderAllocationTable.tsx:170
msgid "Shipment"
msgstr "発送"

#: src/components/render/Part.tsx:28
#: src/components/render/Plugin.tsx:17
#: src/components/render/User.tsx:37
#: src/pages/company/CompanyDetail.tsx:325
#: src/pages/company/SupplierPartDetail.tsx:369
#: src/pages/core/UserDetail.tsx:211
#: src/pages/part/PartDetail.tsx:1012
msgid "Inactive"
msgstr "非アクティブ"

#: src/components/render/Part.tsx:31
#: src/tables/bom/BomTable.tsx:289
#: src/tables/part/PartTable.tsx:139
msgid "No stock"
msgstr "在庫なし"

#: src/components/render/Part.tsx:74
#: src/pages/part/PartDetail.tsx:488
#: src/tables/ColumnRenderers.tsx:224
#: src/tables/ColumnRenderers.tsx:233
#: src/tables/notifications/NotificationTable.tsx:32
#: src/tables/part/PartCategoryTemplateTable.tsx:71
msgid "Category"
msgstr "カテゴリ"

#: src/components/render/Stock.tsx:36
#: src/components/render/Stock.tsx:107
#: src/components/render/Stock.tsx:125
#: src/forms/BuildForms.tsx:759
#: src/forms/PurchaseOrderForms.tsx:592
#: src/forms/StockForms.tsx:733
#: src/forms/StockForms.tsx:779
#: src/forms/StockForms.tsx:825
#: src/forms/StockForms.tsx:864
#: src/forms/StockForms.tsx:900
#: src/forms/StockForms.tsx:938
#: src/forms/StockForms.tsx:980
#: src/forms/StockForms.tsx:1028
#: src/forms/StockForms.tsx:1072
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:88
#: src/pages/core/UserDetail.tsx:158
#: src/pages/stock/StockDetail.tsx:295
#: src/tables/ColumnRenderers.tsx:176
#: src/tables/ColumnRenderers.tsx:185
#: src/tables/Filter.tsx:392
#: src/tables/stock/StockTrackingTable.tsx:98
msgid "Location"
msgstr "場所"

#: src/components/render/Stock.tsx:92
#: src/pages/stock/StockDetail.tsx:195
#: src/pages/stock/StockDetail.tsx:901
#: src/tables/build/BuildAllocatedStockTable.tsx:121
#: src/tables/build/BuildOutputTable.tsx:111
#: src/tables/sales/SalesOrderAllocationTable.tsx:139
msgid "Serial Number"
msgstr "シリアル番号"

#: src/components/render/Stock.tsx:97
#: src/components/wizards/OrderPartsWizard.tsx:222
#: src/forms/BuildForms.tsx:243
#: src/forms/BuildForms.tsx:605
#: src/forms/BuildForms.tsx:761
#: src/forms/PurchaseOrderForms.tsx:794
#: src/forms/ReturnOrderForms.tsx:240
#: src/forms/SalesOrderForms.tsx:270
#: src/forms/StockForms.tsx:781
#: src/pages/part/PartStockHistoryDetail.tsx:56
#: src/pages/part/PartStockHistoryDetail.tsx:210
#: src/pages/part/PartStockHistoryDetail.tsx:234
#: src/pages/part/pricing/BomPricingPanel.tsx:146
#: src/pages/part/pricing/PriceBreakPanel.tsx:89
#: src/pages/part/pricing/PriceBreakPanel.tsx:172
#: src/pages/stock/StockDetail.tsx:255
#: src/pages/stock/StockDetail.tsx:907
#: src/tables/build/BuildLineTable.tsx:84
#: src/tables/build/BuildOrderTestTable.tsx:251
#: src/tables/part/PartPurchaseOrdersTable.tsx:94
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:168
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:199
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:69
#: src/tables/sales/ReturnOrderLineItemTable.tsx:121
#: src/tables/stock/StockTrackingTable.tsx:72
msgid "Quantity"
msgstr "数量"

#: src/components/render/Stock.tsx:110
#: src/forms/BuildForms.tsx:312
#: src/forms/BuildForms.tsx:386
#: src/forms/BuildForms.tsx:450
#: src/forms/StockForms.tsx:734
#: src/forms/StockForms.tsx:780
#: src/forms/StockForms.tsx:826
#: src/forms/StockForms.tsx:865
#: src/forms/StockForms.tsx:901
#: src/forms/StockForms.tsx:939
#: src/forms/StockForms.tsx:981
#: src/forms/StockForms.tsx:1029
#: src/forms/StockForms.tsx:1073
#: src/tables/build/BuildLineTable.tsx:94
msgid "Batch"
msgstr "スクール機能"

#: src/components/settings/ConfigValueList.tsx:33
#~ msgid "<0>{0}</0> is set via {1} and was last set {2}"
#~ msgstr "<0>{0}</0> is set via {1} and was last set {2}"

#: src/components/settings/ConfigValueList.tsx:36
msgid "Setting"
msgstr ""

#: src/components/settings/ConfigValueList.tsx:39
msgid "Source"
msgstr ""

#: src/components/settings/SettingItem.tsx:47
#: src/components/settings/SettingItem.tsx:100
#~ msgid "{0} updated successfully"
#~ msgstr "{0} updated successfully"

#: src/components/settings/SettingList.tsx:78
msgid "Edit Setting"
msgstr "設定を編集"

#: src/components/settings/SettingList.tsx:91
msgid "Setting {key} updated successfully"
msgstr "設定 {key} が正常に更新されました"

#: src/components/settings/SettingList.tsx:120
msgid "Setting updated"
msgstr "設定を更新しました。"

#. placeholder {0}: setting.key
#: src/components/settings/SettingList.tsx:121
msgid "Setting {0} updated successfully"
msgstr "設定 {0} が正常に更新されました"

#: src/components/settings/SettingList.tsx:130
msgid "Error editing setting"
msgstr "エラー編集設定"

#: src/components/settings/SettingList.tsx:146
msgid "Error loading settings"
msgstr ""

#: src/components/settings/SettingList.tsx:187
msgid "No settings specified"
msgstr "設定なし"

#: src/components/tables/FilterGroup.tsx:29
#~ msgid "Add table filter"
#~ msgstr "Add table filter"

#: src/components/tables/FilterGroup.tsx:44
#~ msgid "Clear all filters"
#~ msgstr "Clear all filters"

#: src/components/tables/FilterGroup.tsx:51
#~ msgid "Add filter"
#~ msgstr "Add filter"

#: src/components/tables/FilterSelectModal.tsx:143
#~ msgid "Add Table Filter"
#~ msgstr "Add Table Filter"

#: src/components/tables/FilterSelectModal.tsx:145
#~ msgid "Select from the available filters"
#~ msgstr "Select from the available filters"

#: src/components/tables/bom/BomTable.tsx:200
#~ msgid "Validate"
#~ msgstr "Validate"

#: src/components/tables/bom/BomTable.tsx:250
#~ msgid "Has Available Stock"
#~ msgstr "Has Available Stock"

#: src/components/tables/bom/UsedInTable.tsx:40
#~ msgid "Required Part"
#~ msgstr "Required Part"

#: src/components/tables/build/BuildOrderTable.tsx:52
#~ msgid "Progress"
#~ msgstr "Progress"

#: src/components/tables/build/BuildOrderTable.tsx:65
#~ msgid "Priority"
#~ msgstr "Priority"

#: src/components/tables/company/AddressTable.tsx:68
#~ msgid "Postal Code"
#~ msgstr "Postal Code"

#: src/components/tables/company/AddressTable.tsx:74
#~ msgid "City"
#~ msgstr "City"

#: src/components/tables/company/AddressTable.tsx:80
#~ msgid "State / Province"
#~ msgstr "State / Province"

#: src/components/tables/company/AddressTable.tsx:86
#~ msgid "Country"
#~ msgstr "Country"

#: src/components/tables/company/AddressTable.tsx:92
#~ msgid "Courier Notes"
#~ msgstr "Courier Notes"

#: src/components/tables/company/AddressTable.tsx:98
#~ msgid "Internal Notes"
#~ msgstr "Internal Notes"

#: src/components/tables/company/AddressTable.tsx:130
#~ msgid "Address updated"
#~ msgstr "Address updated"

#: src/components/tables/company/AddressTable.tsx:142
#~ msgid "Address deleted"
#~ msgstr "Address deleted"

#: src/components/tables/company/CompanyTable.tsx:32
#~ msgid "Company Name"
#~ msgstr "Company Name"

#: src/components/tables/company/ContactTable.tsx:41
#~ msgid "Phone"
#~ msgstr "Phone"

#: src/components/tables/company/ContactTable.tsx:78
#~ msgid "Contact updated"
#~ msgstr "Contact updated"

#: src/components/tables/company/ContactTable.tsx:90
#~ msgid "Contact deleted"
#~ msgstr "Contact deleted"

#: src/components/tables/company/ContactTable.tsx:92
#~ msgid "Are you sure you want to delete this contact?"
#~ msgstr "Are you sure you want to delete this contact?"

#: src/components/tables/company/ContactTable.tsx:108
#~ msgid "Create Contact"
#~ msgstr "Create Contact"

#: src/components/tables/company/ContactTable.tsx:110
#~ msgid "Contact created"
#~ msgstr "Contact created"

#: src/components/tables/general/AttachmentTable.tsx:47
#~ msgid "Comment"
#~ msgstr "Comment"

#: src/components/tables/part/PartCategoryTable.tsx:122
#~ msgid "Part category updated"
#~ msgstr "Part category updated"

#: src/components/tables/part/PartParameterTable.tsx:41
#~ msgid "Parameter"
#~ msgstr "Parameter"

#: src/components/tables/part/PartParameterTable.tsx:114
#~ msgid "Part parameter updated"
#~ msgstr "Part parameter updated"

#: src/components/tables/part/PartParameterTable.tsx:130
#~ msgid "Part parameter deleted"
#~ msgstr "Part parameter deleted"

#: src/components/tables/part/PartParameterTable.tsx:132
#~ msgid "Are you sure you want to remove this parameter?"
#~ msgstr "Are you sure you want to remove this parameter?"

#: src/components/tables/part/PartParameterTable.tsx:159
#~ msgid "Part parameter added"
#~ msgstr "Part parameter added"

#: src/components/tables/part/PartParameterTemplateTable.tsx:67
#~ msgid "Choices"
#~ msgstr "Choices"

#: src/components/tables/part/PartParameterTemplateTable.tsx:83
#~ msgid "Remove parameter template"
#~ msgstr "Remove parameter template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:84
#~ msgid "Parameter template updated"
#~ msgstr "Parameter template updated"

#: src/components/tables/part/PartParameterTemplateTable.tsx:96
#~ msgid "Parameter template deleted"
#~ msgstr "Parameter template deleted"

#: src/components/tables/part/PartParameterTemplateTable.tsx:98
#~ msgid "Are you sure you want to remove this parameter template?"
#~ msgstr "Are you sure you want to remove this parameter template?"

#: src/components/tables/part/PartParameterTemplateTable.tsx:110
#~ msgid "Create Parameter Template"
#~ msgstr "Create Parameter Template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:112
#~ msgid "Parameter template created"
#~ msgstr "Parameter template created"

#: src/components/tables/part/PartTable.tsx:211
#~ msgid "Detail"
#~ msgstr "Detail"

#: src/components/tables/part/PartTestTemplateTable.tsx:30
#~ msgid "Test Name"
#~ msgstr "Test Name"

#: src/components/tables/part/PartTestTemplateTable.tsx:86
#~ msgid "Template updated"
#~ msgstr "Template updated"

#: src/components/tables/part/PartTestTemplateTable.tsx:98
#~ msgid "Test Template deleted"
#~ msgstr "Test Template deleted"

#: src/components/tables/part/PartTestTemplateTable.tsx:115
#~ msgid "Create Test Template"
#~ msgstr "Create Test Template"

#: src/components/tables/part/PartTestTemplateTable.tsx:117
#~ msgid "Template created"
#~ msgstr "Template created"

#: src/components/tables/part/RelatedPartTable.tsx:79
#~ msgid "Related Part"
#~ msgstr "Related Part"

#: src/components/tables/part/RelatedPartTable.tsx:82
#~ msgid "Related part added"
#~ msgstr "Related part added"

#: src/components/tables/part/RelatedPartTable.tsx:114
#~ msgid "Related part deleted"
#~ msgstr "Related part deleted"

#: src/components/tables/part/RelatedPartTable.tsx:115
#~ msgid "Are you sure you want to remove this relationship?"
#~ msgstr "Are you sure you want to remove this relationship?"

#: src/components/tables/plugin/PluginListTable.tsx:191
#~ msgid "Installation path"
#~ msgstr "Installation path"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:55
#~ msgid "Receive"
#~ msgstr "Receive"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:81
#~ msgid "Line item updated"
#~ msgstr "Line item updated"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:232
#~ msgid "Line item added"
#~ msgstr "Line item added"

#: src/components/tables/settings/CustomUnitsTable.tsx:37
#~ msgid "Definition"
#~ msgstr "Definition"

#: src/components/tables/settings/CustomUnitsTable.tsx:43
#~ msgid "Symbol"
#~ msgstr "Symbol"

#: src/components/tables/settings/CustomUnitsTable.tsx:59
#~ msgid "Edit custom unit"
#~ msgstr "Edit custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:66
#~ msgid "Custom unit updated"
#~ msgstr "Custom unit updated"

#: src/components/tables/settings/CustomUnitsTable.tsx:76
#~ msgid "Delete custom unit"
#~ msgstr "Delete custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:77
#~ msgid "Custom unit deleted"
#~ msgstr "Custom unit deleted"

#: src/components/tables/settings/CustomUnitsTable.tsx:79
#~ msgid "Are you sure you want to remove this custom unit?"
#~ msgstr "Are you sure you want to remove this custom unit?"

#: src/components/tables/settings/CustomUnitsTable.tsx:97
#~ msgid "Custom unit created"
#~ msgstr "Custom unit created"

#: src/components/tables/settings/GroupTable.tsx:45
#~ msgid "Group updated"
#~ msgstr "Group updated"

#: src/components/tables/settings/GroupTable.tsx:131
#~ msgid "Added group"
#~ msgstr "Added group"

#: src/components/tables/settings/ProjectCodeTable.tsx:49
#~ msgid "Edit project code"
#~ msgstr "Edit project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:56
#~ msgid "Project code updated"
#~ msgstr "Project code updated"

#: src/components/tables/settings/ProjectCodeTable.tsx:66
#~ msgid "Delete project code"
#~ msgstr "Delete project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:67
#~ msgid "Project code deleted"
#~ msgstr "Project code deleted"

#: src/components/tables/settings/ProjectCodeTable.tsx:69
#~ msgid "Are you sure you want to remove this project code?"
#~ msgstr "Are you sure you want to remove this project code?"

#: src/components/tables/settings/ProjectCodeTable.tsx:88
#~ msgid "Added project code"
#~ msgstr "Added project code"

#: src/components/tables/settings/UserDrawer.tsx:92
#~ msgid "User permission changed successfully"
#~ msgstr "User permission changed successfully"

#: src/components/tables/settings/UserDrawer.tsx:93
#~ msgid "Some changes might only take effect after the user refreshes their login."
#~ msgstr "Some changes might only take effect after the user refreshes their login."

#: src/components/tables/settings/UserDrawer.tsx:118
#~ msgid "Changed user active status successfully"
#~ msgstr "Changed user active status successfully"

#: src/components/tables/settings/UserDrawer.tsx:119
#~ msgid "Set to {active}"
#~ msgstr "Set to {active}"

#: src/components/tables/settings/UserDrawer.tsx:142
#~ msgid "User details for {0}"
#~ msgstr "User details for {0}"

#: src/components/tables/settings/UserDrawer.tsx:176
#~ msgid "Rights"
#~ msgstr "Rights"

#: src/components/tables/settings/UserTable.tsx:117
#~ msgid "user deleted"
#~ msgstr "user deleted"

#: src/components/tables/stock/StockItemTable.tsx:247
#~ msgid "Test Filter"
#~ msgstr "Test Filter"

#: src/components/tables/stock/StockItemTable.tsx:248
#~ msgid "This is a test filter"
#~ msgstr "This is a test filter"

#: src/components/tables/stock/StockLocationTable.tsx:145
#~ msgid "Stock location updated"
#~ msgstr "Stock location updated"

#: src/components/widgets/FeedbackWidget.tsx:19
#~ msgid "Something is new: Platform UI"
#~ msgstr "Something is new: Platform UI"

#: src/components/widgets/FeedbackWidget.tsx:21
#~ msgid "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."
#~ msgstr "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."

#: src/components/widgets/FeedbackWidget.tsx:32
#~ msgid "Provide Feedback"
#~ msgstr "Provide Feedback"

#: src/components/widgets/GetStartedWidget.tsx:11
#~ msgid "Getting started"
#~ msgstr "Getting started"

#: src/components/widgets/MarkdownEditor.tsx:108
#~ msgid "Failed to upload image"
#~ msgstr "Failed to upload image"

#: src/components/widgets/MarkdownEditor.tsx:146
#~ msgid "Notes saved"
#~ msgstr "Notes saved"

#: src/components/widgets/WidgetLayout.tsx:166
#~ msgid "Layout"
#~ msgstr "Layout"

#: src/components/widgets/WidgetLayout.tsx:172
#~ msgid "Reset Layout"
#~ msgstr "Reset Layout"

#: src/components/widgets/WidgetLayout.tsx:185
#~ msgid "Stop Edit"
#~ msgstr "Stop Edit"

#: src/components/widgets/WidgetLayout.tsx:191
#~ msgid "Appearance"
#~ msgstr "Appearance"

#: src/components/widgets/WidgetLayout.tsx:203
#~ msgid "Show Boxes"
#~ msgstr "Show Boxes"

#: src/components/wizards/OrderPartsWizard.tsx:61
msgid "New Purchase Order"
msgstr "新規発注"

#: src/components/wizards/OrderPartsWizard.tsx:63
msgid "Purchase order created"
msgstr "発注書作成完了"

#: src/components/wizards/OrderPartsWizard.tsx:75
msgid "New Supplier Part"
msgstr "新しいサプライヤー・パーツ"

#: src/components/wizards/OrderPartsWizard.tsx:77
#: src/tables/purchasing/SupplierPartTable.tsx:161
msgid "Supplier part created"
msgstr "サプライヤー部品作成"

#: src/components/wizards/OrderPartsWizard.tsx:103
msgid "Add to Purchase Order"
msgstr "注文書に追加する"

#: src/components/wizards/OrderPartsWizard.tsx:115
msgid "Part added to purchase order"
msgstr "注文書に追加された部品"

#: src/components/wizards/OrderPartsWizard.tsx:156
msgid "Select supplier part"
msgstr "サプライヤー部品の選択"

#: src/components/wizards/OrderPartsWizard.tsx:171
msgid "New supplier part"
msgstr "新サプライヤー部品"

#: src/components/wizards/OrderPartsWizard.tsx:195
msgid "Select purchase order"
msgstr "注文書の選択"

#: src/components/wizards/OrderPartsWizard.tsx:209
msgid "New purchase order"
msgstr "新規発注"

#: src/components/wizards/OrderPartsWizard.tsx:257
msgid "Add to selected purchase order"
msgstr "選択した注文書に追加する"

#: src/components/wizards/OrderPartsWizard.tsx:269
#: src/components/wizards/OrderPartsWizard.tsx:382
msgid "No parts selected"
msgstr "部品選択なし"

#: src/components/wizards/OrderPartsWizard.tsx:270
msgid "No purchaseable parts selected"
msgstr "購入可能な部品が選択されていない"

#: src/components/wizards/OrderPartsWizard.tsx:306
msgid "Parts Added"
msgstr "部品が追加された"

#: src/components/wizards/OrderPartsWizard.tsx:307
msgid "All selected parts added to a purchase order"
msgstr "選択されたすべての部品が発注書に追加される"

#: src/components/wizards/OrderPartsWizard.tsx:383
msgid "You must select at least one part to order"
msgstr "少なくとも1つの部品を選択して注文する必要があります。"

#: src/components/wizards/OrderPartsWizard.tsx:394
msgid "Supplier part is required"
msgstr "サプライヤーの部品が必要"

#: src/components/wizards/OrderPartsWizard.tsx:398
msgid "Quantity is required"
msgstr "数量が必要です"

#: src/components/wizards/OrderPartsWizard.tsx:411
msgid "Invalid part selection"
msgstr "無効な部品選択"

#: src/components/wizards/OrderPartsWizard.tsx:413
msgid "Please correct the errors in the selected parts"
msgstr "選択した部品の誤りを訂正してください。"

#: src/components/wizards/OrderPartsWizard.tsx:424
#: src/tables/build/BuildLineTable.tsx:794
#: src/tables/part/PartTable.tsx:407
#: src/tables/sales/SalesOrderLineItemTable.tsx:347
msgid "Order Parts"
msgstr "パーツの注文"

#: src/contexts/LanguageContext.tsx:22
msgid "Arabic"
msgstr "アラビア語"

#: src/contexts/LanguageContext.tsx:23
msgid "Bulgarian"
msgstr "ブルガリア語"

#: src/contexts/LanguageContext.tsx:24
msgid "Czech"
msgstr "チェコ語"

#: src/contexts/LanguageContext.tsx:25
msgid "Danish"
msgstr "デンマーク語"

#: src/contexts/LanguageContext.tsx:26
msgid "German"
msgstr "ドイツ語"

#: src/contexts/LanguageContext.tsx:27
msgid "Greek"
msgstr "ギリシャ語"

#: src/contexts/LanguageContext.tsx:28
msgid "English"
msgstr "英語"

#: src/contexts/LanguageContext.tsx:29
msgid "Spanish"
msgstr "スペイン語"

#: src/contexts/LanguageContext.tsx:30
msgid "Spanish (Mexican)"
msgstr "スペイン語(メキシコ)"

#: src/contexts/LanguageContext.tsx:31
msgid "Estonian"
msgstr "エストニア語"

#: src/contexts/LanguageContext.tsx:32
msgid "Farsi / Persian"
msgstr "ペルシャ語"

#: src/contexts/LanguageContext.tsx:33
msgid "Finnish"
msgstr "フィンランド語"

#: src/contexts/LanguageContext.tsx:34
msgid "French"
msgstr "フランス語"

#: src/contexts/LanguageContext.tsx:35
msgid "Hebrew"
msgstr "ヘブライ語"

#: src/contexts/LanguageContext.tsx:36
msgid "Hindi"
msgstr "ヒンディー語"

#: src/contexts/LanguageContext.tsx:37
msgid "Hungarian"
msgstr "ハンガリー語"

#: src/contexts/LanguageContext.tsx:38
msgid "Italian"
msgstr "イタリア語"

#: src/contexts/LanguageContext.tsx:39
msgid "Japanese"
msgstr "日本語"

#: src/contexts/LanguageContext.tsx:40
msgid "Korean"
msgstr "韓国語"

#: src/contexts/LanguageContext.tsx:41
msgid "Lithuanian"
msgstr "リトアニア語"

#: src/contexts/LanguageContext.tsx:42
msgid "Latvian"
msgstr "ラトビア語"

#: src/contexts/LanguageContext.tsx:43
msgid "Dutch"
msgstr "オランダ語"

#: src/contexts/LanguageContext.tsx:44
msgid "Norwegian"
msgstr "ノルウェー語"

#: src/contexts/LanguageContext.tsx:45
msgid "Polish"
msgstr "ポーランド語"

#: src/contexts/LanguageContext.tsx:46
msgid "Portuguese"
msgstr "ポルトガル語"

#: src/contexts/LanguageContext.tsx:47
msgid "Portuguese (Brazilian)"
msgstr "ポルトガル語 (ブラジル)"

#: src/contexts/LanguageContext.tsx:48
msgid "Romanian"
msgstr "ルーマニア語"

#: src/contexts/LanguageContext.tsx:49
msgid "Russian"
msgstr "ロシア語"

#: src/contexts/LanguageContext.tsx:50
msgid "Slovak"
msgstr "スロバキア語"

#: src/contexts/LanguageContext.tsx:51
msgid "Slovenian"
msgstr "スロベニア語"

#: src/contexts/LanguageContext.tsx:52
msgid "Serbian"
msgstr "セルビア語"

#: src/contexts/LanguageContext.tsx:53
msgid "Swedish"
msgstr "スウェーデン語"

#: src/contexts/LanguageContext.tsx:54
msgid "Thai"
msgstr "タイ語"

#: src/contexts/LanguageContext.tsx:55
msgid "Turkish"
msgstr "トルコ語"

#: src/contexts/LanguageContext.tsx:56
msgid "Ukrainian"
msgstr "ウクライナ語"

#: src/contexts/LanguageContext.tsx:57
msgid "Vietnamese"
msgstr "ベトナム語"

#: src/contexts/LanguageContext.tsx:58
msgid "Chinese (Simplified)"
msgstr "中国語 (簡体字)"

#: src/contexts/LanguageContext.tsx:59
msgid "Chinese (Traditional)"
msgstr "中国語 (繁体字)"

#: src/defaults/actions.tsx:18
#: src/defaults/links.tsx:27
#: src/defaults/menuItems.tsx:9
#~ msgid "Home"
#~ msgstr "Home"

#: src/defaults/actions.tsx:29
msgid "Go to the InvenTree dashboard"
msgstr "InvenTreeのダッシュボードに移動します。"

#: src/defaults/actions.tsx:36
msgid "Visit the documentation to learn more about InvenTree"
msgstr "InvenTreeの詳細については、ドキュメントをご覧ください。"

#: src/defaults/actions.tsx:41
#: src/defaults/links.tsx:118
#~ msgid "About this Inventree instance"
#~ msgstr "About this Inventree instance"

#: src/defaults/actions.tsx:44
#: src/defaults/links.tsx:140
#: src/defaults/links.tsx:186
msgid "About InvenTree"
msgstr "InvenTree について"

#: src/defaults/actions.tsx:45
msgid "About the InvenTree org"
msgstr "InvenTree orgについて"

#: src/defaults/actions.tsx:51
msgid "Server Information"
msgstr "サーバー情報"

#: src/defaults/actions.tsx:52
#: src/defaults/links.tsx:169
msgid "About this InvenTree instance"
msgstr "このInvenTreeインスタンスについて"

#: src/defaults/actions.tsx:58
#: src/defaults/links.tsx:153
#: src/defaults/links.tsx:175
msgid "License Information"
msgstr "有効にすると、プラグインを削除しているときに、ライセンス情報を含むデータベース (ロールデータを除く) からこのプラグインに関連するすべてのデータを削除します。この場合、ライセンスは自動的に非アクティブ化されません"

#: src/defaults/actions.tsx:59
msgid "Licenses for dependencies of the service"
msgstr "サービスの依存関係のライセンス"

#: src/defaults/actions.tsx:65
msgid "Open Navigation"
msgstr "ナビゲーションを開く"

#: src/defaults/actions.tsx:66
msgid "Open the main navigation menu"
msgstr "メインナビゲーションメニューを開く"

#: src/defaults/actions.tsx:73
msgid "Scan a barcode or QR code"
msgstr "バーコードまたはQRコードをスキャンする"

#: src/defaults/actions.tsx:84
msgid "Go to the Admin Center"
msgstr "管理センターへ"

#: src/defaults/dashboardItems.tsx:29
#~ msgid "Latest Parts"
#~ msgstr "Latest Parts"

#: src/defaults/dashboardItems.tsx:36
#~ msgid "BOM Waiting Validation"
#~ msgstr "BOM Waiting Validation"

#: src/defaults/dashboardItems.tsx:43
#~ msgid "Recently Updated"
#~ msgstr "Recently Updated"

#: src/defaults/dashboardItems.tsx:57
#~ msgid "Depleted Stock"
#~ msgstr "Depleted Stock"

#: src/defaults/dashboardItems.tsx:71
#~ msgid "Expired Stock"
#~ msgstr "Expired Stock"

#: src/defaults/dashboardItems.tsx:78
#~ msgid "Stale Stock"
#~ msgstr "Stale Stock"

#: src/defaults/dashboardItems.tsx:85
#~ msgid "Build Orders In Progress"
#~ msgstr "Build Orders In Progress"

#: src/defaults/dashboardItems.tsx:99
#~ msgid "Outstanding Purchase Orders"
#~ msgstr "Outstanding Purchase Orders"

#: src/defaults/dashboardItems.tsx:113
#~ msgid "Outstanding Sales Orders"
#~ msgstr "Outstanding Sales Orders"

#: src/defaults/dashboardItems.tsx:127
#~ msgid "Current News"
#~ msgstr "Current News"

#: src/defaults/defaultHostList.tsx:8
#~ msgid "InvenTree Demo"
#~ msgstr "InvenTree Demo"

#: src/defaults/defaultHostList.tsx:16
#~ msgid "Local Server"
#~ msgstr "Local Server"

#: src/defaults/links.tsx:17
#~ msgid "GitHub"
#~ msgstr "GitHub"

#: src/defaults/links.tsx:22
#~ msgid "Demo"
#~ msgstr "Demo"

#: src/defaults/links.tsx:41
#: src/defaults/menuItems.tsx:71
#: src/pages/Index/Playground.tsx:217
#~ msgid "Playground"
#~ msgstr "Playground"

#: src/defaults/links.tsx:76
#~ msgid "Instance"
#~ msgstr "Instance"

#: src/defaults/links.tsx:83
#~ msgid "InvenTree"
#~ msgstr "InvenTree"

#: src/defaults/links.tsx:93
msgid "API"
msgstr "API"

#: src/defaults/links.tsx:96
msgid "InvenTree API documentation"
msgstr "InvenTree API ドキュメント"

#: src/defaults/links.tsx:100
msgid "Developer Manual"
msgstr "開発者マニュアル"

#: src/defaults/links.tsx:103
msgid "InvenTree developer manual"
msgstr "InvenTree開発者マニュアル"

#: src/defaults/links.tsx:107
msgid "FAQ"
msgstr "よくある質問"

#: src/defaults/links.tsx:110
msgid "Frequently asked questions"
msgstr "FAQ"

#: src/defaults/links.tsx:114
msgid "GitHub Repository"
msgstr "GitHubリポジトリ"

#: src/defaults/links.tsx:117
msgid "InvenTree source code on GitHub"
msgstr "InvenTreeのソースコードはGitHubにあります。"

#: src/defaults/links.tsx:117
#~ msgid "Licenses for packages used by InvenTree"
#~ msgstr "Licenses for packages used by InvenTree"

#: src/defaults/links.tsx:127
#: src/defaults/links.tsx:168
msgid "System Information"
msgstr "システム情報"

#: src/defaults/links.tsx:134
#~ msgid "Licenses"
#~ msgstr "Licenses"

#: src/defaults/links.tsx:176
msgid "Licenses for dependencies of the InvenTree software"
msgstr "InvenTreeソフトウェアの依存関係のライセンス"

#: src/defaults/links.tsx:187
msgid "About the InvenTree Project"
msgstr "InvenTreeプロジェクトについて"

#: src/defaults/menuItems.tsx:7
#~ msgid "Open sourcea"
#~ msgstr "Open sourcea"

#: src/defaults/menuItems.tsx:9
#~ msgid "Open source"
#~ msgstr "Open source"

#: src/defaults/menuItems.tsx:10
#~ msgid "Start page of your instance."
#~ msgstr "Start page of your instance."

#: src/defaults/menuItems.tsx:10
#~ msgid "This Pokémon’s cry is very loud and distracting"
#~ msgstr "This Pokémon’s cry is very loud and distracting"

#: src/defaults/menuItems.tsx:12
#~ msgid "This Pokémon’s cry is very loud and distracting and more and more and more"
#~ msgstr "This Pokémon’s cry is very loud and distracting and more and more and more"

#: src/defaults/menuItems.tsx:15
#~ msgid "Profile page"
#~ msgstr "Profile page"

#: src/defaults/menuItems.tsx:17
#~ msgid "User attributes and design settings."
#~ msgstr "User attributes and design settings."

#: src/defaults/menuItems.tsx:21
#~ msgid "Free for everyone"
#~ msgstr "Free for everyone"

#: src/defaults/menuItems.tsx:22
#~ msgid "The fluid of Smeargle’s tail secretions changes"
#~ msgstr "The fluid of Smeargle’s tail secretions changes"

#: src/defaults/menuItems.tsx:23
#~ msgid "View for interactive scanning and multiple actions."
#~ msgstr "View for interactive scanning and multiple actions."

#: src/defaults/menuItems.tsx:24
#~ msgid "The fluid of Smeargle’s tail secretions changes in the intensity"
#~ msgstr "The fluid of Smeargle’s tail secretions changes in the intensity"

#: src/defaults/menuItems.tsx:32
#~ msgid "abc"
#~ msgstr "abc"

#: src/defaults/menuItems.tsx:37
#~ msgid "Random image"
#~ msgstr "Random image"

#: src/defaults/menuItems.tsx:40
#~ msgid "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"
#~ msgstr "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"

#: src/defaults/menuItems.tsx:105
#~ msgid "Yanma is capable of seeing 360 degrees without"
#~ msgstr "Yanma is capable of seeing 360 degrees without"

#: src/defaults/menuItems.tsx:111
#~ msgid "The shell’s rounded shape and the grooves on its."
#~ msgstr "The shell’s rounded shape and the grooves on its."

#: src/defaults/menuItems.tsx:116
#~ msgid "Analytics"
#~ msgstr "Analytics"

#: src/defaults/menuItems.tsx:118
#~ msgid "This Pokémon uses its flying ability to quickly chase"
#~ msgstr "This Pokémon uses its flying ability to quickly chase"

#: src/defaults/menuItems.tsx:125
#~ msgid "Combusken battles with the intensely hot flames it spews"
#~ msgstr "Combusken battles with the intensely hot flames it spews"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add File"
#~ msgstr "Add File"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add Link"
#~ msgstr "Add Link"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "File added"
#~ msgstr "File added"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "Link added"
#~ msgstr "Link added"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit File"
#~ msgstr "Edit File"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit Link"
#~ msgstr "Edit Link"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "File updated"
#~ msgstr "File updated"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "Link updated"
#~ msgstr "Link updated"

#: src/forms/AttachmentForms.tsx:125
#~ msgid "Attachment deleted"
#~ msgstr "Attachment deleted"

#: src/forms/AttachmentForms.tsx:128
#~ msgid "Are you sure you want to delete this attachment?"
#~ msgstr "Are you sure you want to delete this attachment?"

#: src/forms/BomForms.tsx:109
msgid "Substitute Part"
msgstr ""

#: src/forms/BomForms.tsx:126
msgid "Edit BOM Substitutes"
msgstr ""

#: src/forms/BomForms.tsx:133
msgid "Add Substitute"
msgstr ""

#: src/forms/BomForms.tsx:134
msgid "Substitute added"
msgstr ""

#: src/forms/BuildForms.tsx:112
#: src/forms/BuildForms.tsx:217
#: src/forms/StockForms.tsx:197
msgid "Next batch code"
msgstr "次のバッチコード"

#: src/forms/BuildForms.tsx:212
#: src/forms/StockForms.tsx:183
#: src/forms/StockForms.tsx:188
#: src/forms/StockForms.tsx:359
#: src/pages/stock/StockDetail.tsx:231
msgid "Next serial number"
msgstr "次のシリアル番号"

#: src/forms/BuildForms.tsx:248
#~ msgid "Remove output"
#~ msgstr "Remove output"

#: src/forms/BuildForms.tsx:311
#: src/forms/BuildForms.tsx:656
#: src/tables/build/BuildAllocatedStockTable.tsx:150
#: src/tables/build/BuildOrderTestTable.tsx:230
#: src/tables/build/BuildOrderTestTable.tsx:254
#: src/tables/build/BuildOutputTable.tsx:592
msgid "Build Output"
msgstr "ビルド出力"

#: src/forms/BuildForms.tsx:313
#: src/forms/BuildForms.tsx:387
#: src/forms/BuildForms.tsx:451
#: src/forms/PurchaseOrderForms.tsx:714
#: src/forms/ReturnOrderForms.tsx:194
#: src/forms/ReturnOrderForms.tsx:241
#: src/forms/StockForms.tsx:656
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:87
#: src/pages/build/BuildDetail.tsx:205
#: src/pages/core/UserDetail.tsx:151
#: src/pages/purchasing/PurchaseOrderDetail.tsx:150
#: src/pages/sales/ReturnOrderDetail.tsx:115
#: src/pages/sales/SalesOrderDetail.tsx:124
#: src/pages/stock/StockDetail.tsx:162
#: src/tables/Filter.tsx:266
#: src/tables/build/BuildOutputTable.tsx:408
#: src/tables/machine/MachineListTable.tsx:339
#: src/tables/part/PartPurchaseOrdersTable.tsx:38
#: src/tables/sales/ReturnOrderLineItemTable.tsx:135
#: src/tables/sales/ReturnOrderLineItemTable.tsx:172
#: src/tables/settings/CustomStateTable.tsx:79
#: src/tables/settings/EmailTable.tsx:73
#: src/tables/settings/ImportSessionTable.tsx:117
#: src/tables/stock/StockItemTable.tsx:328
#: src/tables/stock/StockTrackingTable.tsx:65
msgid "Status"
msgstr "ステータス"

#: src/forms/BuildForms.tsx:335
msgid "Complete Build Outputs"
msgstr "完全なビルド出力"

#: src/forms/BuildForms.tsx:338
msgid "Build outputs have been completed"
msgstr "ビルドアウトプット完了"

#: src/forms/BuildForms.tsx:405
#: src/forms/BuildForms.tsx:407
msgid "Scrap Build Outputs"
msgstr "スクラップビルドの出力"

#: src/forms/BuildForms.tsx:408
#~ msgid "Selected build outputs will be deleted"
#~ msgstr "Selected build outputs will be deleted"

#: src/forms/BuildForms.tsx:410
msgid "Selected build outputs will be completed, but marked as scrapped"
msgstr ""

#: src/forms/BuildForms.tsx:412
msgid "Allocated stock items will be consumed"
msgstr ""

#: src/forms/BuildForms.tsx:418
msgid "Build outputs have been scrapped"
msgstr "ビルド出力は廃止"

#: src/forms/BuildForms.tsx:461
#: src/forms/BuildForms.tsx:463
msgid "Cancel Build Outputs"
msgstr "ビルド出力のキャンセル"

#: src/forms/BuildForms.tsx:465
msgid "Selected build outputs will be removed"
msgstr ""

#: src/forms/BuildForms.tsx:467
msgid "Allocated stock items will be returned to stock"
msgstr ""

#: src/forms/BuildForms.tsx:470
#~ msgid "Remove line"
#~ msgstr "Remove line"

#: src/forms/BuildForms.tsx:474
msgid "Build outputs have been cancelled"
msgstr "ビルドアウトプットはキャンセルされました"

#: src/forms/BuildForms.tsx:603
#: src/forms/BuildForms.tsx:760
#: src/forms/BuildForms.tsx:861
#: src/forms/SalesOrderForms.tsx:268
#: src/tables/build/BuildAllocatedStockTable.tsx:139
#: src/tables/build/BuildLineTable.tsx:180
#: src/tables/sales/SalesOrderLineItemTable.tsx:319
#: src/tables/stock/StockItemTable.tsx:339
msgid "Allocated"
msgstr "割り当てられた"

#: src/forms/BuildForms.tsx:638
#: src/forms/SalesOrderForms.tsx:257
#: src/pages/build/BuildDetail.tsx:320
msgid "Source Location"
msgstr "ソース・ロケーション"

#: src/forms/BuildForms.tsx:639
#: src/forms/SalesOrderForms.tsx:258
msgid "Select the source location for the stock allocation"
msgstr "在庫配分のソースの場所を選択します。"

#: src/forms/BuildForms.tsx:671
#: src/forms/SalesOrderForms.tsx:298
#: src/tables/build/BuildLineTable.tsx:555
#: src/tables/build/BuildLineTable.tsx:710
#: src/tables/build/BuildLineTable.tsx:809
#: src/tables/sales/SalesOrderLineItemTable.tsx:357
#: src/tables/sales/SalesOrderLineItemTable.tsx:388
msgid "Allocate Stock"
msgstr "株式の割当"

#: src/forms/BuildForms.tsx:674
#: src/forms/SalesOrderForms.tsx:303
msgid "Stock items allocated"
msgstr "割り当てられた在庫品目"

#: src/forms/BuildForms.tsx:780
#: src/forms/BuildForms.tsx:881
#: src/tables/build/BuildAllocatedStockTable.tsx:233
#: src/tables/build/BuildAllocatedStockTable.tsx:265
#: src/tables/build/BuildLineTable.tsx:720
#: src/tables/build/BuildLineTable.tsx:843
msgid "Consume Stock"
msgstr ""

#: src/forms/BuildForms.tsx:781
#: src/forms/BuildForms.tsx:882
msgid "Stock items consumed"
msgstr ""

#: src/forms/BuildForms.tsx:817
#: src/tables/build/BuildLineTable.tsx:495
#: src/tables/part/PartBuildAllocationsTable.tsx:101
msgid "Fully consumed"
msgstr ""

#: src/forms/BuildForms.tsx:862
#: src/tables/build/BuildLineTable.tsx:185
#: src/tables/stock/StockItemTable.tsx:372
msgid "Consumed"
msgstr "消費済み"

#: src/forms/CompanyForms.tsx:150
#~ msgid "Company updated"
#~ msgstr "Company updated"

#: src/forms/PartForms.tsx:70
#: src/forms/PartForms.tsx:157
#: src/pages/part/CategoryDetail.tsx:122
#: src/pages/part/PartDetail.tsx:668
#: src/tables/part/PartCategoryTable.tsx:94
#: src/tables/part/PartTable.tsx:312
msgid "Subscribed"
msgstr "登録済み"

#: src/forms/PartForms.tsx:71
msgid "Subscribe to notifications for this part"
msgstr "このパーツの通知を受け取る"

#: src/forms/PartForms.tsx:106
#~ msgid "Create Part"
#~ msgstr "Create Part"

#: src/forms/PartForms.tsx:108
#~ msgid "Part created"
#~ msgstr "Part created"

#: src/forms/PartForms.tsx:129
#~ msgid "Part updated"
#~ msgstr "Part updated"

#: src/forms/PartForms.tsx:143
msgid "Parent part category"
msgstr "親部品カテゴリー"

#: src/forms/PartForms.tsx:158
msgid "Subscribe to notifications for this category"
msgstr "このカテゴリの通知を受け取る"

#: src/forms/PurchaseOrderForms.tsx:385
msgid "Assign Batch Code and Serial Numbers"
msgstr "バッチコードとシリアル番号の割り当て"

#: src/forms/PurchaseOrderForms.tsx:387
msgid "Assign Batch Code"
msgstr "バッチコードの割り当て"

#: src/forms/PurchaseOrderForms.tsx:407
msgid "Choose Location"
msgstr "地域を選択"

#: src/forms/PurchaseOrderForms.tsx:415
msgid "Item Destination selected"
msgstr "選択された項目"

#: src/forms/PurchaseOrderForms.tsx:421
#~ msgid "Assign Batch Code{0}"
#~ msgstr "Assign Batch Code{0}"

#: src/forms/PurchaseOrderForms.tsx:425
msgid "Part category default location selected"
msgstr "選択されたパートカテゴリーのデフォルトの場所"

#: src/forms/PurchaseOrderForms.tsx:435
msgid "Received stock location selected"
msgstr "選択された受入在庫場所"

#: src/forms/PurchaseOrderForms.tsx:443
msgid "Default location selected"
msgstr "デフォルトの場所を選択"

#: src/forms/PurchaseOrderForms.tsx:444
#: src/forms/StockForms.tsx:428
#~ msgid "Remove item from list"
#~ msgstr "Remove item from list"

#: src/forms/PurchaseOrderForms.tsx:504
msgid "Set Location"
msgstr "セット場所"

#: src/forms/PurchaseOrderForms.tsx:521
msgid "Set Expiry Date"
msgstr "有効期限の設定"

#: src/forms/PurchaseOrderForms.tsx:529
#: src/forms/StockForms.tsx:637
msgid "Adjust Packaging"
msgstr "パッケージの調整"

#: src/forms/PurchaseOrderForms.tsx:537
#: src/forms/StockForms.tsx:628
#: src/hooks/UseStockAdjustActions.tsx:148
msgid "Change Status"
msgstr "ステータスを変更"

#: src/forms/PurchaseOrderForms.tsx:543
msgid "Add Note"
msgstr "コメントを挿入"

#: src/forms/PurchaseOrderForms.tsx:566
#~ msgid "Serial numbers"
#~ msgstr "Serial numbers"

#: src/forms/PurchaseOrderForms.tsx:582
#~ msgid "Store at line item destination"
#~ msgstr "Store at line item destination"

#: src/forms/PurchaseOrderForms.tsx:607
msgid "Store at default location"
msgstr "デフォルトの場所に保存"

#: src/forms/PurchaseOrderForms.tsx:622
msgid "Store at line item destination "
msgstr "行先での保存"

#: src/forms/PurchaseOrderForms.tsx:634
msgid "Store with already received stock"
msgstr "入荷済みの在庫がある店舗"

#: src/forms/PurchaseOrderForms.tsx:658
#: src/pages/build/BuildDetail.tsx:334
#: src/pages/stock/StockDetail.tsx:277
#: src/pages/stock/StockDetail.tsx:923
#: src/tables/Filter.tsx:83
#: src/tables/build/BuildAllocatedStockTable.tsx:128
#: src/tables/build/BuildOrderTestTable.tsx:242
#: src/tables/build/BuildOutputTable.tsx:116
#: src/tables/sales/SalesOrderAllocationTable.tsx:146
msgid "Batch Code"
msgstr "バッチコード"

#: src/forms/PurchaseOrderForms.tsx:658
#~ msgid "Receive line items"
#~ msgstr "Receive line items"

#: src/forms/PurchaseOrderForms.tsx:659
msgid "Enter batch code for received items"
msgstr "受領品のバッチコードを入力"

#: src/forms/PurchaseOrderForms.tsx:672
#: src/forms/StockForms.tsx:176
msgid "Serial Numbers"
msgstr "シリアル番号"

#: src/forms/PurchaseOrderForms.tsx:673
msgid "Enter serial numbers for received items"
msgstr "受け取った商品のシリアル番号を入力"

#: src/forms/PurchaseOrderForms.tsx:687
#: src/pages/stock/StockDetail.tsx:379
#: src/tables/stock/StockItemTable.tsx:295
msgid "Expiry Date"
msgstr "有効期限"

#: src/forms/PurchaseOrderForms.tsx:688
msgid "Enter an expiry date for received items"
msgstr "受け取った商品の有効期限を入力してください。"

#: src/forms/PurchaseOrderForms.tsx:700
#: src/forms/StockForms.tsx:672
#: src/pages/company/SupplierPartDetail.tsx:172
#: src/pages/company/SupplierPartDetail.tsx:236
#: src/pages/stock/StockDetail.tsx:416
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:220
msgid "Packaging"
msgstr "パッケージング"

#: src/forms/PurchaseOrderForms.tsx:724
#: src/pages/company/SupplierPartDetail.tsx:119
#: src/tables/ColumnRenderers.tsx:323
msgid "Note"
msgstr "備考"

#: src/forms/PurchaseOrderForms.tsx:792
#: src/pages/company/SupplierPartDetail.tsx:137
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:49
msgid "SKU"
msgstr "SKU"

#: src/forms/PurchaseOrderForms.tsx:793
#: src/tables/part/PartPurchaseOrdersTable.tsx:127
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:206
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:272
#: src/tables/sales/ReturnOrderLineItemTable.tsx:167
msgid "Received"
msgstr "受信"

#: src/forms/PurchaseOrderForms.tsx:810
msgid "Receive Line Items"
msgstr "ラインアイテムの受信"

#: src/forms/PurchaseOrderForms.tsx:816
msgid "Items received"
msgstr "受領品目"

#: src/forms/ReturnOrderForms.tsx:254
msgid "Receive Items"
msgstr "商品を受け取る"

#: src/forms/ReturnOrderForms.tsx:261
msgid "Item received into stock"
msgstr "入荷した商品"

#: src/forms/StockForms.tsx:110
#~ msgid "Create Stock Item"
#~ msgstr "Create Stock Item"

#: src/forms/StockForms.tsx:155
msgid "Add given quantity as packs instead of individual items"
msgstr "指定された数量を単品ではなくパックとして追加します。"

#: src/forms/StockForms.tsx:158
#~ msgid "Stock item updated"
#~ msgstr "Stock item updated"

#: src/forms/StockForms.tsx:169
msgid "Enter initial quantity for this stock item"
msgstr "この商品の初期数量を入力"

#: src/forms/StockForms.tsx:178
msgid "Enter serial numbers for new stock (or leave blank)"
msgstr "新しい在庫のシリアル番号を入力（または空白のまま）"

#: src/forms/StockForms.tsx:200
msgid "Stock Status"
msgstr "在庫状況"

#: src/forms/StockForms.tsx:260
#: src/pages/stock/StockDetail.tsx:670
#: src/tables/stock/StockItemTable.tsx:525
#: src/tables/stock/StockItemTable.tsx:572
msgid "Add Stock Item"
msgstr "在庫商品の追加"

#: src/forms/StockForms.tsx:304
msgid "Select the part to install"
msgstr "取り付ける部品の選択"

#: src/forms/StockForms.tsx:431
msgid "Confirm Stock Transfer"
msgstr "株式譲渡の確認"

#: src/forms/StockForms.tsx:558
msgid "Loading..."
msgstr "読み込み中…"

#: src/forms/StockForms.tsx:616
msgid "Move to default location"
msgstr "デフォルトの場所に移動"

#: src/forms/StockForms.tsx:736
msgid "Move"
msgstr "移動"

#: src/forms/StockForms.tsx:782
msgid "Return"
msgstr "戻る"

#: src/forms/StockForms.tsx:827
#: src/forms/StockForms.tsx:866
#: src/forms/StockForms.tsx:902
#: src/forms/StockForms.tsx:940
#: src/forms/StockForms.tsx:982
#: src/forms/StockForms.tsx:1030
#: src/forms/StockForms.tsx:1074
#: src/pages/company/SupplierPartDetail.tsx:190
#: src/pages/company/SupplierPartDetail.tsx:374
#: src/pages/part/PartDetail.tsx:535
#: src/pages/part/PartDetail.tsx:970
#: src/tables/purchasing/SupplierPartTable.tsx:194
#: src/tables/stock/StockItemTable.tsx:359
msgid "In Stock"
msgstr "在庫あり"

#: src/forms/StockForms.tsx:903
#: src/pages/Index/Scan.tsx:182
msgid "Count"
msgstr "カウント"

#: src/forms/StockForms.tsx:1187
#: src/hooks/UseStockAdjustActions.tsx:108
msgid "Add Stock"
msgstr "在庫追加"

#: src/forms/StockForms.tsx:1188
msgid "Stock added"
msgstr "在庫追加"

#: src/forms/StockForms.tsx:1191
msgid "Increase the quantity of the selected stock items by a given amount."
msgstr ""

#: src/forms/StockForms.tsx:1202
#: src/hooks/UseStockAdjustActions.tsx:118
msgid "Remove Stock"
msgstr "在庫の削除"

#: src/forms/StockForms.tsx:1203
msgid "Stock removed"
msgstr "在庫一掃"

#: src/forms/StockForms.tsx:1206
msgid "Decrease the quantity of the selected stock items by a given amount."
msgstr ""

#: src/forms/StockForms.tsx:1217
#: src/hooks/UseStockAdjustActions.tsx:128
msgid "Transfer Stock"
msgstr "株式譲渡"

#: src/forms/StockForms.tsx:1218
msgid "Stock transferred"
msgstr "株式譲渡"

#: src/forms/StockForms.tsx:1221
msgid "Transfer selected items to the specified location."
msgstr ""

#: src/forms/StockForms.tsx:1232
#: src/hooks/UseStockAdjustActions.tsx:168
msgid "Return Stock"
msgstr ""

#: src/forms/StockForms.tsx:1233
msgid "Stock returned"
msgstr ""

#: src/forms/StockForms.tsx:1236
msgid "Return selected items into stock, to the specified location."
msgstr ""

#: src/forms/StockForms.tsx:1247
#: src/hooks/UseStockAdjustActions.tsx:98
msgid "Count Stock"
msgstr "在庫数"

#: src/forms/StockForms.tsx:1248
msgid "Stock counted"
msgstr "在庫数"

#: src/forms/StockForms.tsx:1251
msgid "Count the selected stock items, and adjust the quantity accordingly."
msgstr ""

#: src/forms/StockForms.tsx:1262
msgid "Change Stock Status"
msgstr "在庫状況の変更"

#: src/forms/StockForms.tsx:1263
msgid "Stock status changed"
msgstr "在庫状況の変更"

#: src/forms/StockForms.tsx:1266
msgid "Change the status of the selected stock items."
msgstr ""

#: src/forms/StockForms.tsx:1277
#: src/hooks/UseStockAdjustActions.tsx:138
msgid "Merge Stock"
msgstr "株式の併合"

#: src/forms/StockForms.tsx:1278
msgid "Stock merged"
msgstr "株式併合"

#: src/forms/StockForms.tsx:1280
msgid "Merge Stock Items"
msgstr ""

#: src/forms/StockForms.tsx:1282
msgid "Merge operation cannot be reversed"
msgstr ""

#: src/forms/StockForms.tsx:1283
msgid "Tracking information may be lost when merging items"
msgstr ""

#: src/forms/StockForms.tsx:1284
msgid "Supplier information may be lost when merging items"
msgstr ""

#: src/forms/StockForms.tsx:1302
msgid "Assign Stock to Customer"
msgstr "顧客への在庫割り当て"

#: src/forms/StockForms.tsx:1303
msgid "Stock assigned to customer"
msgstr "顧客に割り当てられた在庫"

#: src/forms/StockForms.tsx:1313
msgid "Delete Stock Items"
msgstr "在庫アイテムの削除"

#: src/forms/StockForms.tsx:1314
msgid "Stock deleted"
msgstr "ストック削除"

#: src/forms/StockForms.tsx:1317
msgid "This operation will permanently delete the selected stock items."
msgstr ""

#: src/forms/StockForms.tsx:1326
msgid "Parent stock location"
msgstr "親株式所在地"

#: src/forms/StockForms.tsx:1453
msgid "Find Serial Number"
msgstr ""

#: src/forms/StockForms.tsx:1464
msgid "No matching items"
msgstr ""

#: src/forms/StockForms.tsx:1470
msgid "Multiple matching items"
msgstr ""

#: src/forms/StockForms.tsx:1479
msgid "Invalid response from server"
msgstr ""

#: src/forms/selectionListFields.tsx:97
msgid "Entries"
msgstr "エントリー"

#: src/forms/selectionListFields.tsx:98
msgid "List of entries to choose from"
msgstr "エントリーリスト"

#: src/forms/selectionListFields.tsx:102
#: src/pages/part/PartStockHistoryDetail.tsx:59
#: src/tables/FilterSelectDrawer.tsx:114
#: src/tables/FilterSelectDrawer.tsx:137
#: src/tables/FilterSelectDrawer.tsx:149
#: src/tables/build/BuildOrderTestTable.tsx:188
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:201
msgid "Value"
msgstr "値"

#: src/forms/selectionListFields.tsx:103
msgid "Label"
msgstr "ラベル"

#: src/functions/api.tsx:33
msgid "Bad request"
msgstr "間違ったリクエスト"

#: src/functions/api.tsx:36
msgid "Unauthorized"
msgstr "無許可"

#: src/functions/api.tsx:39
msgid "Forbidden"
msgstr "禁じられた"

#: src/functions/api.tsx:42
msgid "Not found"
msgstr "みつかりません"

#: src/functions/api.tsx:45
msgid "Method not allowed"
msgstr "不可の方法"

#: src/functions/api.tsx:48
msgid "Internal server error"
msgstr "内部サーバーエラー"

#: src/functions/auth.tsx:34
#~ msgid "Error fetching token from server."
#~ msgstr "Error fetching token from server."

#: src/functions/auth.tsx:36
#~ msgid "Logout successfull"
#~ msgstr "Logout successfull"

#: src/functions/auth.tsx:60
#~ msgid "See you soon."
#~ msgstr "See you soon."

#: src/functions/auth.tsx:70
#~ msgid "Logout successful"
#~ msgstr "Logout successful"

#: src/functions/auth.tsx:71
#~ msgid "You have been logged out"
#~ msgstr "You have been logged out"

#: src/functions/auth.tsx:113
#: src/functions/auth.tsx:313
msgid "Already logged in"
msgstr "ログイン済み"

#: src/functions/auth.tsx:114
#: src/functions/auth.tsx:314
msgid "There is a conflicting session on the server for this browser. Please logout of that first."
msgstr "このブラウザのセッションがサーバー上で競合しています。まずそちらからログアウトしてください。"

#: src/functions/auth.tsx:142
#~ msgid "Found an existing login - using it to log you in."
#~ msgstr "Found an existing login - using it to log you in."

#: src/functions/auth.tsx:143
#~ msgid "Found an existing login - welcome back!"
#~ msgstr "Found an existing login - welcome back!"

#: src/functions/auth.tsx:150
msgid "MFA Login successful"
msgstr ""

#: src/functions/auth.tsx:151
msgid "MFA details were automatically provided in the browser"
msgstr ""

#: src/functions/auth.tsx:179
msgid "Logged Out"
msgstr "ログアウト"

#: src/functions/auth.tsx:180
msgid "Successfully logged out"
msgstr "ログアウトに成功しました"

#: src/functions/auth.tsx:218
msgid "Language changed"
msgstr "言語変更"

#: src/functions/auth.tsx:219
msgid "Your active language has been changed to the one set in your profile"
msgstr "アクティブ言語がプロフィールで設定した言語に変更されました。"

#: src/functions/auth.tsx:239
msgid "Theme changed"
msgstr "テーマ変更"

#: src/functions/auth.tsx:240
msgid "Your active theme has been changed to the one set in your profile"
msgstr "アクティブなテーマがプロフィールで設定したものに変更されました。"

#: src/functions/auth.tsx:274
msgid "Check your inbox for a reset link. This only works if you have an account. Check in spam too."
msgstr "リセットのリンクを受信トレイでご確認ください。これはアカウントを持っている場合にのみ機能します。迷惑メールもチェックしてください。"

#: src/functions/auth.tsx:281
#: src/functions/auth.tsx:538
msgid "Reset failed"
msgstr "リセット失敗"

#: src/functions/auth.tsx:370
msgid "Logged In"
msgstr "ログイン中"

#: src/functions/auth.tsx:371
msgid "Successfully logged in"
msgstr "ログインに成功しました"

#: src/functions/auth.tsx:498
msgid "Failed to set up MFA"
msgstr "MFAの設定に失敗しました"

#: src/functions/auth.tsx:528
msgid "Password set"
msgstr "パスワード設定"

#: src/functions/auth.tsx:529
#: src/functions/auth.tsx:638
msgid "The password was set successfully. You can now login with your new password"
msgstr "パスワードは正常に設定されました。新しいパスワードでログインできます。"

#: src/functions/auth.tsx:603
msgid "Password could not be changed"
msgstr "パスワードを変更できませんでした"

#: src/functions/auth.tsx:621
msgid "The two password fields didn’t match"
msgstr "2つのパスワードフィールドが一致しませんでした"

#: src/functions/auth.tsx:637
msgid "Password Changed"
msgstr "パスワードが変更されました"

#: src/functions/forms.tsx:50
#~ msgid "Form method not provided"
#~ msgstr "Form method not provided"

#: src/functions/forms.tsx:59
#~ msgid "Response did not contain action data"
#~ msgstr "Response did not contain action data"

#: src/functions/forms.tsx:182
#~ msgid "Invalid Form"
#~ msgstr "Invalid Form"

#: src/functions/forms.tsx:183
#~ msgid "method parameter not supplied"
#~ msgstr "method parameter not supplied"

#: src/functions/notifications.tsx:13
msgid "Not implemented"
msgstr "未実施"

#: src/functions/notifications.tsx:14
msgid "This feature is not yet implemented"
msgstr "この機能はまだ実装されていません"

#: src/functions/notifications.tsx:24
#~ msgid "Permission denied"
#~ msgstr "Permission denied"

#: src/functions/notifications.tsx:26
msgid "You do not have permission to perform this action"
msgstr "この操作を実行する権限がありません"

#: src/functions/notifications.tsx:37
msgid "Invalid Return Code"
msgstr "無効なリターンコード"

#: src/functions/notifications.tsx:38
msgid "Server returned status {returnCode}"
msgstr "サーバーが返したステータス [0］"

#: src/functions/notifications.tsx:48
msgid "Timeout"
msgstr "タイムアウト"

#: src/functions/notifications.tsx:49
msgid "The request timed out"
msgstr "リクエストがタイムアウトしました"

#: src/hooks/UseDataExport.tsx:34
msgid "Exporting Data"
msgstr "データエクスポート中"

#: src/hooks/UseDataExport.tsx:109
msgid "Export Data"
msgstr "エクスポートデータ"

#: src/hooks/UseDataExport.tsx:112
msgid "Export"
msgstr "エクスポート"

#: src/hooks/UseDataOutput.tsx:57
#: src/hooks/UseDataOutput.tsx:111
msgid "Process failed"
msgstr "プロセス失敗"

#: src/hooks/UseDataOutput.tsx:75
msgid "Process completed successfully"
msgstr "プロセスは正常に完了しました。"

#: src/hooks/UseForm.tsx:92
msgid "Item Created"
msgstr "作成項目"

#: src/hooks/UseForm.tsx:112
msgid "Item Updated"
msgstr "アイテム更新"

#: src/hooks/UseForm.tsx:133
msgid "Items Updated"
msgstr "アイテム更新完了"

#: src/hooks/UseForm.tsx:135
msgid "Update multiple items"
msgstr "複数アイテムの更新"

#: src/hooks/UseForm.tsx:165
msgid "Item Deleted"
msgstr "アイテムが削除されました"

#: src/hooks/UseForm.tsx:169
msgid "Are you sure you want to delete this item?"
msgstr "このアイテムを削除してもよろしいですか？"

#: src/hooks/UsePlaceholder.tsx:59
#~ msgid "Latest serial number"
#~ msgstr "Latest serial number"

#: src/hooks/UseStockAdjustActions.tsx:100
msgid "Count selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:110
msgid "Add to selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:120
msgid "Remove from selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:130
msgid "Transfer selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:140
msgid "Merge selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:150
msgid "Change status of selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:158
msgid "Assign Stock"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:160
msgid "Assign selected stock items to a customer"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:170
msgid "Return selected items into stock"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:178
msgid "Delete Stock"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:180
msgid "Delete selected stock items"
msgstr ""

#: src/hooks/UseStockAdjustActions.tsx:205
#: src/pages/part/PartDetail.tsx:1144
msgid "Stock Actions"
msgstr "ストックアクション"

#: src/pages/Auth/ChangePassword.tsx:32
#: src/pages/Auth/Reset.tsx:14
msgid "Reset Password"
msgstr "パスワードをリセットする"

#: src/pages/Auth/ChangePassword.tsx:46
msgid "Current Password"
msgstr "現在のパスワード"

#: src/pages/Auth/ChangePassword.tsx:47
msgid "Enter your current password"
msgstr "現在のパスワードを入力してください。"

#: src/pages/Auth/ChangePassword.tsx:53
msgid "New Password"
msgstr "新しいパスワード"

#: src/pages/Auth/ChangePassword.tsx:54
msgid "Enter your new password"
msgstr "新しいパスワードを入力してください"

#: src/pages/Auth/ChangePassword.tsx:60
msgid "Confirm New Password"
msgstr "確認のため新しいパスワードを再入力してください"

#: src/pages/Auth/ChangePassword.tsx:61
msgid "Confirm your new password"
msgstr "確認のため新しいパスワードを再入力してください"

#: src/pages/Auth/ChangePassword.tsx:80
msgid "Confirm"
msgstr "確認"

#: src/pages/Auth/Layout.tsx:59
msgid "Log off"
msgstr "ログオフ"

#: src/pages/Auth/LoggedIn.tsx:19
msgid "Checking if you are already logged in"
msgstr "ログイン済みかどうかの確認"

#: src/pages/Auth/Login.tsx:32
msgid "No selection"
msgstr "選択なし"

#: src/pages/Auth/Login.tsx:91
#~ msgid "Welcome, log in below"
#~ msgstr "Welcome, log in below"

#: src/pages/Auth/Login.tsx:93
#~ msgid "Register below"
#~ msgstr "Register below"

#: src/pages/Auth/Login.tsx:100
msgid "Login"
msgstr "ログイン"

#: src/pages/Auth/Login.tsx:106
msgid "Logging you in"
msgstr "ログイン中"

#: src/pages/Auth/Login.tsx:113
msgid "Don't have an account?"
msgstr "アカウントをお持ちですか?"

#: src/pages/Auth/Logout.tsx:22
#~ msgid "Logging out"
#~ msgstr "Logging out"

#: src/pages/Auth/MFA.tsx:16
#~ msgid "Multi-Factor Login"
#~ msgstr "Multi-Factor Login"

#: src/pages/Auth/MFA.tsx:17
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:79
msgid "Multi-Factor Authentication"
msgstr "多要素認証"

#: src/pages/Auth/MFA.tsx:20
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:693
msgid "TOTP Code"
msgstr "TOTPコード"

#: src/pages/Auth/MFA.tsx:22
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:695
msgid "Enter your TOTP or recovery code"
msgstr "TOTPまたはリカバリーコードを入力してください。"

#: src/pages/Auth/MFA.tsx:27
msgid "Remember this device"
msgstr ""

#: src/pages/Auth/MFA.tsx:29
msgid "If enabled, you will not be asked for MFA on this device for 30 days."
msgstr ""

#: src/pages/Auth/MFA.tsx:38
msgid "Log in"
msgstr "ログイン"

#: src/pages/Auth/MFASetup.tsx:23
msgid "MFA Setup Required"
msgstr "MFA設定必須"

#: src/pages/Auth/MFASetup.tsx:34
msgid "Add TOTP"
msgstr "TOTPを追加"

#: src/pages/Auth/Register.tsx:23
msgid "Go back to login"
msgstr "ログインに戻る"

#: src/pages/Auth/Reset.tsx:41
#: src/pages/Auth/Set-Password.tsx:112
#~ msgid "Send mail"
#~ msgstr "Send mail"

#: src/pages/Auth/ResetPassword.tsx:22
#: src/pages/Auth/VerifyEmail.tsx:19
msgid "Key invalid"
msgstr "キー無効"

#: src/pages/Auth/ResetPassword.tsx:23
msgid "You need to provide a valid key to set a new password. Check your inbox for a reset link."
msgstr "新しいパスワードを設定するには、有効なキーが必要です。リセットリンクは受信箱をご確認ください。"

#: src/pages/Auth/ResetPassword.tsx:30
#~ msgid "Token invalid"
#~ msgstr "Token invalid"

#: src/pages/Auth/ResetPassword.tsx:31
msgid "Set new password"
msgstr "新しいパスワードを設定"

#: src/pages/Auth/ResetPassword.tsx:31
#~ msgid "You need to provide a valid token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a valid token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/ResetPassword.tsx:35
msgid "The desired new password"
msgstr "新しいパスワード"

#: src/pages/Auth/ResetPassword.tsx:44
msgid "Send Password"
msgstr "パスワード送信"

#: src/pages/Auth/Set-Password.tsx:49
#~ msgid "No token provided"
#~ msgstr "No token provided"

#: src/pages/Auth/Set-Password.tsx:50
#~ msgid "You need to provide a token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/VerifyEmail.tsx:20
msgid "You need to provide a valid key."
msgstr "有効なキーを入力する必要があります。"

#: src/pages/Auth/VerifyEmail.tsx:28
msgid "Verify Email"
msgstr "メールアドレスを確認"

#: src/pages/Auth/VerifyEmail.tsx:30
msgid "Verify"
msgstr "確認"

#. placeholder {0}: error.statusText
#: src/pages/ErrorPage.tsx:16
msgid "Error: {0}"
msgstr "エラー：{0}"

#: src/pages/ErrorPage.tsx:23
msgid "An unexpected error has occurred"
msgstr "予期しないエラーが発生しました"

#: src/pages/ErrorPage.tsx:28
#~ msgid "Sorry, an unexpected error has occurred."
#~ msgstr "Sorry, an unexpected error has occurred."

#: src/pages/Index/Dashboard.tsx:22
#~ msgid "Autoupdate"
#~ msgstr "Autoupdate"

#: src/pages/Index/Dashboard.tsx:26
#~ msgid "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."
#~ msgstr "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."

#: src/pages/Index/Home.tsx:58
#~ msgid "Welcome to your Dashboard{0}"
#~ msgstr "Welcome to your Dashboard{0}"

#: src/pages/Index/Playground.tsx:222
#~ msgid "This page is a showcase for the possibilities of Platform UI."
#~ msgstr "This page is a showcase for the possibilities of Platform UI."

#: src/pages/Index/Profile/Profile.tsx:30
#: src/pages/Index/Profile/Profile.tsx:141
#~ msgid "Notification Settings"
#~ msgstr "Notification Settings"

#: src/pages/Index/Profile/Profile.tsx:33
#~ msgid "Global Settings"
#~ msgstr "Global Settings"

#: src/pages/Index/Profile/Profile.tsx:47
#~ msgid "Settings for the current user"
#~ msgstr "Settings for the current user"

#: src/pages/Index/Profile/Profile.tsx:51
#~ msgid "Home Page Settings"
#~ msgstr "Home Page Settings"

#: src/pages/Index/Profile/Profile.tsx:76
#~ msgid "Search Settings"
#~ msgstr "Search Settings"

#: src/pages/Index/Profile/Profile.tsx:115
#: src/pages/Index/Profile/Profile.tsx:211
#~ msgid "Label Settings"
#~ msgstr "Label Settings"

#: src/pages/Index/Profile/Profile.tsx:120
#: src/pages/Index/Profile/Profile.tsx:219
#~ msgid "Report Settings"
#~ msgstr "Report Settings"

#: src/pages/Index/Profile/Profile.tsx:142
#~ msgid "Settings for the notifications"
#~ msgstr "Settings for the notifications"

#: src/pages/Index/Profile/Profile.tsx:148
#~ msgid "Global Server Settings"
#~ msgstr "Global Server Settings"

#: src/pages/Index/Profile/Profile.tsx:149
#~ msgid "Global Settings for this instance"
#~ msgstr "Global Settings for this instance"

#: src/pages/Index/Profile/Profile.tsx:153
#~ msgid "Server Settings"
#~ msgstr "Server Settings"

#: src/pages/Index/Profile/Profile.tsx:187
#~ msgid "Login Settings"
#~ msgstr "Login Settings"

#: src/pages/Index/Profile/Profile.tsx:202
#~ msgid "Barcode Settings"
#~ msgstr "Barcode Settings"

#: src/pages/Index/Profile/Profile.tsx:230
#~ msgid "Part Settings"
#~ msgstr "Part Settings"

#: src/pages/Index/Profile/Profile.tsx:255
#~ msgid "Pricing Settings"
#~ msgstr "Pricing Settings"

#: src/pages/Index/Profile/Profile.tsx:270
#~ msgid "Stock Settings"
#~ msgstr "Stock Settings"

#: src/pages/Index/Profile/Profile.tsx:284
#~ msgid "Build Order Settings"
#~ msgstr "Build Order Settings"

#: src/pages/Index/Profile/Profile.tsx:289
#~ msgid "Purchase Order Settings"
#~ msgstr "Purchase Order Settings"

#: src/pages/Index/Profile/Profile.tsx:300
#~ msgid "Sales Order Settings"
#~ msgstr "Sales Order Settings"

#: src/pages/Index/Profile/Profile.tsx:330
#~ msgid "Plugin Settings for this instance"
#~ msgstr "Plugin Settings for this instance"

#: src/pages/Index/Profile/SettingsPanel.tsx:27
#~ msgid "Data is current beeing loaded"
#~ msgstr "Data is current beeing loaded"

#: src/pages/Index/Profile/SettingsPanel.tsx:69
#: src/pages/Index/Profile/SettingsPanel.tsx:76
#~ msgid "Failed to load"
#~ msgstr "Failed to load"

#: src/pages/Index/Profile/SettingsPanel.tsx:100
#~ msgid "Show internal names"
#~ msgstr "Show internal names"

#: src/pages/Index/Profile/SettingsPanel.tsx:148
#~ msgid "Input {0} is not known"
#~ msgstr "Input {0} is not known"

#: src/pages/Index/Profile/SettingsPanel.tsx:161
#~ msgid "Saved changes {0}"
#~ msgstr "Saved changes {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:162
#~ msgid "Changed to {0}"
#~ msgstr "Changed to {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:176
#~ msgid "Error while saving {0}"
#~ msgstr "Error while saving {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:177
#~ msgid "Error was {err}"
#~ msgstr "Error was {err}"

#: src/pages/Index/Profile/SettingsPanel.tsx:257
#~ msgid "Plugin: {0}"
#~ msgstr "Plugin: {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:262
#~ msgid "Method: {0}"
#~ msgstr "Method: {0}"

#: src/pages/Index/Profile/UserPanel.tsx:85
#~ msgid "Userinfo"
#~ msgstr "Userinfo"

#: src/pages/Index/Profile/UserPanel.tsx:122
#~ msgid "Username: {0}"
#~ msgstr "Username: {0}"

#: src/pages/Index/Profile/UserTheme.tsx:83
#~ msgid "Design <0/>"
#~ msgstr "Design <0/>"

#: src/pages/Index/Scan.tsx:65
msgid "Item already scanned"
msgstr "スキャン済みアイテム"

#: src/pages/Index/Scan.tsx:82
msgid "API Error"
msgstr "APIエラー"

#: src/pages/Index/Scan.tsx:83
msgid "Failed to fetch instance data"
msgstr "インスタンスデータの取得に失敗"

#: src/pages/Index/Scan.tsx:130
msgid "Scan Error"
msgstr "スキャンエラー"

#: src/pages/Index/Scan.tsx:162
msgid "Selected elements are not known"
msgstr "選択された要素は不明"

#: src/pages/Index/Scan.tsx:169
msgid "Multiple object types selected"
msgstr "複数のオブジェクトタイプを選択"

#: src/pages/Index/Scan.tsx:175
#~ msgid "Actions ..."
#~ msgstr "Actions ..."

#: src/pages/Index/Scan.tsx:177
msgid "Actions ... "
msgstr "アクション..."

#: src/pages/Index/Scan.tsx:194
#: src/pages/Index/Scan.tsx:198
msgid "Barcode Scanning"
msgstr "バーコードスキャン"

#: src/pages/Index/Scan.tsx:207
msgid "Barcode Input"
msgstr "バーコード入力"

#: src/pages/Index/Scan.tsx:214
msgid "Action"
msgstr "アクション"

#: src/pages/Index/Scan.tsx:217
msgid "No Items Selected"
msgstr "選択されたアイテムなし"

#: src/pages/Index/Scan.tsx:217
#~ msgid "Manual input"
#~ msgstr "Manual input"

#: src/pages/Index/Scan.tsx:218
msgid "Scan and select items to perform actions"
msgstr "アイテムをスキャンして選択し、アクションを実行する"

#: src/pages/Index/Scan.tsx:218
#~ msgid "Image Barcode"
#~ msgstr "Image Barcode"

#. placeholder {0}: selection.length
#: src/pages/Index/Scan.tsx:223
msgid "{0} items selected"
msgstr "{0} 選択された項目"

#: src/pages/Index/Scan.tsx:235
msgid "Scanned Items"
msgstr "スキャンされたアイテム"

#: src/pages/Index/Scan.tsx:276
#~ msgid "Actions for {0}"
#~ msgstr "Actions for {0}"

#: src/pages/Index/Scan.tsx:298
#~ msgid "Scan Page"
#~ msgstr "Scan Page"

#: src/pages/Index/Scan.tsx:301
#~ msgid "This page can be used for continuously scanning items and taking actions on them."
#~ msgstr "This page can be used for continuously scanning items and taking actions on them."

#: src/pages/Index/Scan.tsx:308
#~ msgid "Toggle Fullscreen"
#~ msgstr "Toggle Fullscreen"

#: src/pages/Index/Scan.tsx:321
#~ msgid "Select the input method you want to use to scan items."
#~ msgstr "Select the input method you want to use to scan items."

#: src/pages/Index/Scan.tsx:323
#~ msgid "Input"
#~ msgstr "Input"

#: src/pages/Index/Scan.tsx:330
#~ msgid "Select input method"
#~ msgstr "Select input method"

#: src/pages/Index/Scan.tsx:331
#~ msgid "Nothing found"
#~ msgstr "Nothing found"

#: src/pages/Index/Scan.tsx:339
#~ msgid "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."
#~ msgstr "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."

#: src/pages/Index/Scan.tsx:353
#~ msgid "General Actions"
#~ msgstr "General Actions"

#: src/pages/Index/Scan.tsx:367
#~ msgid "Lookup part"
#~ msgstr "Lookup part"

#: src/pages/Index/Scan.tsx:375
#~ msgid "Open Link"
#~ msgstr "Open Link"

#: src/pages/Index/Scan.tsx:391
#~ msgid "History is locally kept in this browser."
#~ msgstr "History is locally kept in this browser."

#: src/pages/Index/Scan.tsx:392
#~ msgid "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."
#~ msgstr "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."

#: src/pages/Index/Scan.tsx:400
#~ msgid "Delete History"
#~ msgstr "Delete History"

#: src/pages/Index/Scan.tsx:465
#~ msgid "No history"
#~ msgstr "No history"

#: src/pages/Index/Scan.tsx:492
#~ msgid "Scanned at"
#~ msgstr "Scanned at"

#: src/pages/Index/Scan.tsx:549
#~ msgid "Enter item serial or data"
#~ msgstr "Enter item serial or data"

#: src/pages/Index/Scan.tsx:561
#~ msgid "Add dummy item"
#~ msgstr "Add dummy item"

#: src/pages/Index/Scan.tsx:652
#~ msgid "Error while getting camera"
#~ msgstr "Error while getting camera"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Scanning"
#~ msgstr "Scanning"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Not scanning"
#~ msgstr "Not scanning"

#: src/pages/Index/Scan.tsx:777
#~ msgid "Select Camera"
#~ msgstr "Select Camera"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:30
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:52
#~ msgid "Edit User Information"
#~ msgstr "Edit User Information"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:33
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:113
msgid "Edit Account Information"
msgstr "アカウント情報の編集"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:34
#~ msgid "User details updated"
#~ msgstr "User details updated"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:37
msgid "Account details updated"
msgstr "アカウント詳細更新完了"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:46
#~ msgid "User Actions"
#~ msgstr "User Actions"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:50
#~ msgid "First name"
#~ msgstr "First name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:136
msgid "Edit Profile Information"
msgstr "プロフィール情報の編集"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#~ msgid "Last name"
#~ msgstr "Last name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:56
#~ msgid "Set User Password"
#~ msgstr "Set User Password"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:58
#~ msgid "First name: {0}"
#~ msgstr "First name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:59
msgid "Profile details updated"
msgstr "プロフィール詳細更新完了"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:61
#~ msgid "Last name: {0}"
#~ msgstr "Last name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:65
#: src/pages/core/UserDetail.tsx:55
msgid "First Name"
msgstr "名"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:66
#: src/pages/core/UserDetail.tsx:63
msgid "Last Name"
msgstr "姓"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:67
#~ msgid "First name:"
#~ msgstr "First name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:71
#~ msgid "Last name:"
#~ msgstr "Last name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:72
msgid "Staff Access"
msgstr "スタッフアクセス"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:85
#: src/pages/core/UserDetail.tsx:119
#: src/tables/settings/CustomStateTable.tsx:101
msgid "Display Name"
msgstr "表示名"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:86
#: src/pages/core/UserDetail.tsx:127
msgid "Position"
msgstr "位置"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:90
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:447
msgid "Type"
msgstr "タイプ"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:91
#: src/pages/core/UserDetail.tsx:143
msgid "Organisation"
msgstr "組織"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:92
msgid "Primary Group"
msgstr "プライマリーグループ"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:104
msgid "Account Details"
msgstr "アカウント詳細"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:107
msgid "Account Actions"
msgstr "アカウントアクション"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:111
msgid "Edit Account"
msgstr "アカウント編集"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:117
#: src/tables/settings/UserTable.tsx:322
msgid "Change Password"
msgstr "パスワードの変更"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:119
msgid "Change User Password"
msgstr "ユーザーパスワードの変更"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:131
msgid "Profile Details"
msgstr "プロファイルの詳細"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:134
msgid "Edit Profile"
msgstr "プロフィールを編集"

#. placeholder {0}: item.label
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:153
msgid "{0}"
msgstr "{0}"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:24
msgid "Secret"
msgstr "シークレット"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:30
msgid "One-Time Password"
msgstr "ワンタイムパスワード"

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:31
msgid "Enter the TOTP code to ensure it registered correctly"
msgstr "TOTPコードを入力し、正しく登録されていることを確認します。"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:53
msgid "Email Addresses"
msgstr "E メールアドレス"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:55
#~ msgid "Single Sign On Accounts"
#~ msgstr "Single Sign On Accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:61
msgid "Single Sign On"
msgstr "シングルサインオン"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
msgid "Not enabled"
msgstr "有効になっていません"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
#~ msgid "Multifactor"
#~ msgstr "Multifactor"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:71
#~ msgid "Single Sign On is not enabled for this server"
#~ msgstr "Single Sign On is not enabled for this server"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:72
msgid "Single Sign On is not enabled for this server "
msgstr "このサーバーではシングルサインオンが有効になっていません"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:83
#~ msgid "Multifactor authentication is not configured for your account"
#~ msgstr "Multifactor authentication is not configured for your account"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:87
msgid "Access Tokens"
msgstr "アクセス・トークン"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:125
msgid "Error while updating email"
msgstr "メール更新時のエラー"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:139
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:297
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:435
msgid "Not Configured"
msgstr "未構成"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:142
msgid "Currently no email addresses are registered."
msgstr "現在、メールアドレスは登録されていません。"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:150
msgid "The following email addresses are associated with your account:"
msgstr "以下のメールアドレスがアカウントに関連付けられています："

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:163
msgid "Primary"
msgstr "プライマリー"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:168
msgid "Verified"
msgstr "承認済み"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:172
msgid "Unverified"
msgstr "未承認"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:190
msgid "Make Primary"
msgstr "メインに指定"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:196
msgid "Re-send Verification"
msgstr "検証の再送信"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:210
msgid "Add Email Address"
msgstr "メールアドレスの追加"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:212
msgid "E-Mail"
msgstr "メールアドレス"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:213
msgid "E-Mail address"
msgstr "電子メールアドレス"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:225
msgid "Error while adding email"
msgstr "メール追加時のエラー"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:236
msgid "Add Email"
msgstr "メールアドレスを追加"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:270
#~ msgid "Provider has not been configured"
#~ msgstr "Provider has not been configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:280
#~ msgid "Not configured"
#~ msgstr "Not configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:283
#~ msgid "There are no social network accounts connected to this account."
#~ msgstr "There are no social network accounts connected to this account."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:293
#~ msgid "You can sign in to your account using any of the following third party accounts"
#~ msgstr "You can sign in to your account using any of the following third party accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:300
msgid "There are no providers connected to this account."
msgstr "このアカウントに接続されているプロバイダーはありません。"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:309
msgid "You can sign in to your account using any of the following providers"
msgstr "以下のプロバイダーのいずれかを使用してアカウントにサインインできます。"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:322
msgid "Remove Provider Link"
msgstr "プロバイダーリンクの削除"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:439
msgid "No multi-factor tokens configured for this account"
msgstr "このアカウントには多要素トークンが設定されていません。"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:450
msgid "Last used at"
msgstr "最終使用"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:453
msgid "Created at"
msgstr "作成"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:474
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:579
msgid "Recovery Codes"
msgstr "回復コード"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:478
msgid "Unused Codes"
msgstr "未使用コード"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:483
msgid "Used Codes"
msgstr "使用中コード"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:538
msgid "Error while registering recovery codes"
msgstr "リカバリーコード登録時のエラー"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:572
msgid "TOTP"
msgstr "TOTP"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:573
msgid "Time-based One-Time Password"
msgstr "時間ベースのワンタイムパスワード(TOTP)"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:580
msgid "One-Time pre-generated recovery codes"
msgstr "事前に生成された1回限りのリカバリーコード"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:594
msgid "Add Token"
msgstr "トークンの追加"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:609
msgid "Register TOTP Token"
msgstr "TOTPトークンの登録"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:634
msgid "Error registering TOTP token"
msgstr "TOTPトークンの登録エラー"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:710
msgid "Enter your password"
msgstr "パスワードを入力してください"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:741
#~ msgid "Token is used - no actions"
#~ msgstr "Token is used - no actions"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:761
#~ msgid "No tokens configured"
#~ msgstr "No tokens configured"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:61
msgid "Display Settings"
msgstr "ディスプレイの設定"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:65
#~ msgid "bars"
#~ msgstr "bars"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:66
#~ msgid "oval"
#~ msgstr "oval"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
msgid "Language"
msgstr "言語"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
#~ msgid "dots"
#~ msgstr "dots"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:78
msgid "Use pseudo language"
msgstr "擬似言語の使用"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:81
#~ msgid "Theme"
#~ msgstr "Theme"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:85
msgid "Color Mode"
msgstr "カラーモード"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:87
#~ msgid "Primary color"
#~ msgstr "Primary color"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:96
msgid "Highlight color"
msgstr "ハイライトカラー"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:110
msgid "Example"
msgstr "例"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:116
msgid "White color"
msgstr "ホワイト"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:139
msgid "Black color"
msgstr "ブラック"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:162
msgid "Border Radius"
msgstr "枠線の丸み"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:178
msgid "Loader"
msgstr "ローダー"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:185
msgid "Bars"
msgstr "バー"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:186
msgid "Oval"
msgstr "楕円形"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:187
msgid "Dots"
msgstr "ドット"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:93
msgid "Reauthentication"
msgstr "再認証"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:109
msgid "OK"
msgstr "OK"

#: src/pages/Index/Settings/AdminCenter.tsx:91
#~ msgid "Advanced Amininistrative Options for InvenTree"
#~ msgstr "Advanced Amininistrative Options for InvenTree"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:28
#: src/tables/ColumnRenderers.tsx:476
msgid "Currency"
msgstr "通貨"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:33
msgid "Rate"
msgstr "レート"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:46
msgid "Exchange rates updated"
msgstr "為替レート更新"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:53
msgid "Exchange rate update error"
msgstr "為替レート更新エラー"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:63
msgid "Refresh currency exchange rates"
msgstr "リフレッシュ為替レート"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:99
msgid "Last fetched"
msgstr "最終フェッチ"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:100
msgid "Base currency"
msgstr "基準通貨"

#: src/pages/Index/Settings/AdminCenter/EmailManagementPanel.tsx:13
msgid "Email Messages"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:107
#~ msgid "User Management"
#~ msgstr "User Management"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:112
msgid "Users / Access"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:126
msgid "Data Import"
msgstr "データインポート"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:127
#~ msgid "Templates"
#~ msgstr "Templates"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:132
msgid "Data Export"
msgstr "データエクスポート"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:138
msgid "Barcode Scans"
msgstr "バーコードスキャン"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:144
msgid "Background Tasks"
msgstr "バックグラウンドタスク"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:150
msgid "Error Reports"
msgstr "エラーレポート"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:156
msgid "Currencies"
msgstr "通貨"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:170
#~ msgid "Location types"
#~ msgstr "Location types"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:173
msgid "Custom States"
msgstr "カスタムステート"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:179
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:57
msgid "Custom Units"
msgstr "カスタム単位"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:185
#: src/pages/part/CategoryDetail.tsx:302
msgid "Part Parameters"
msgstr "パーツパラメータ"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:192
msgid "Category Parameters"
msgstr "カテゴリー・パラメーター"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:211
msgid "Location Types"
msgstr "ロケーションタイプ"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:221
#~ msgid "Quick Actions"
#~ msgstr "Quick Actions"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:225
#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:48
#: src/tables/machine/MachineTypeTable.tsx:307
msgid "Machines"
msgstr "機械"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:226
#~ msgid "Add a new user"
#~ msgstr "Add a new user"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:236
msgid "Operations"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:248
msgid "Data Management"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:259
#: src/pages/Index/Settings/SystemSettings.tsx:175
#: src/pages/Index/Settings/UserSettings.tsx:118
msgid "Reporting"
msgstr "レポート"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:264
msgid "PLM"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:274
msgid "Extend / Integrate"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:288
msgid "Advanced Options"
msgstr "高度なオプション"

#: src/pages/Index/Settings/AdminCenter/LabelTemplatePanel.tsx:40
#~ msgid "Generated Labels"
#~ msgstr "Generated Labels"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:43
#~ msgid "Machine types"
#~ msgstr "Machine types"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:53
#~ msgid "Machine Error Stack"
#~ msgstr "Machine Error Stack"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:56
msgid "Machine Types"
msgstr "マシンの種類"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:62
#~ msgid "There are no machine registry errors."
#~ msgstr "There are no machine registry errors."

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:64
msgid "Machine Errors"
msgstr "マシンエラー"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:77
msgid "Registry Registry Errors"
msgstr "レジストリ エラー"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:80
msgid "There are machine registry errors"
msgstr "マシンのレジストリエラー"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:86
msgid "Machine Registry Errors"
msgstr "マシンレジストリエラー"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:89
msgid "There are no machine registry errors"
msgstr "マシンのレジストリエラーはありません。"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#: src/tables/settings/UserTable.tsx:195
msgid "Info"
msgstr "情報"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#~ msgid "Plugin Error Stack"
#~ msgstr "Plugin Error Stack"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:37
msgid "External plugins are not enabled for this InvenTree installation."
msgstr "このInvenTreeインストールでは、外部プラグインは有効になっていません。"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:45
#~ msgid "Warning"
#~ msgstr "Warning"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:47
#~ msgid "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."
#~ msgstr "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:76
msgid "Plugin Errors"
msgstr "プラグインのエラー"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:16
msgid "Page Size"
msgstr "ページサイズ"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:19
msgid "Landscape"
msgstr "ランドスケープ"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:25
msgid "Merge"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:31
msgid "Attach to Model"
msgstr "モデルに装着"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:55
#~ msgid "Generated Reports"
#~ msgstr "Generated Reports"

#: src/pages/Index/Settings/AdminCenter/StocktakePanel.tsx:25
#~ msgid "Stocktake Reports"
#~ msgstr "Stocktake Reports"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:30
msgid "Background worker not running"
msgstr "バックグラウンドワーカーが実行されていません"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:31
msgid "The background task manager service is not running. Contact your system administrator."
msgstr "バックグラウンドタスクマネージャーサービスが実行されていません。システム管理者に連絡してください。"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:35
#~ msgid "Background Worker Not Running"
#~ msgstr "Background Worker Not Running"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:38
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:47
msgid "Pending Tasks"
msgstr "保留タスク"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:39
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:55
msgid "Scheduled Tasks"
msgstr "スケジュールされたタスク"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:40
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:63
msgid "Failed Tasks"
msgstr "失敗したタスク"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:67
#~ msgid "Stock item"
#~ msgstr "Stock item"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:76
#~ msgid "Build line"
#~ msgstr "Build line"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:88
#~ msgid "Reports"
#~ msgstr "Reports"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:99
#~ msgid "Purchase order"
#~ msgstr "Purchase order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:108
#~ msgid "Sales order"
#~ msgstr "Sales order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:117
#~ msgid "Return order"
#~ msgstr "Return order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:145
#~ msgid "Tests"
#~ msgstr "Tests"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:154
#~ msgid "Stock location"
#~ msgstr "Stock location"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:21
msgid "Alias"
msgstr "エイリアス"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:22
msgid "Dimensionless"
msgstr "無次元"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:65
msgid "All units"
msgstr "全てのユニット"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:31
msgid "Tokens"
msgstr "トークン"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:32
#~ msgid "Select settings relevant for user lifecycle. More available in"
#~ msgstr "Select settings relevant for user lifecycle. More available in"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:37
#~ msgid "System settings"
#~ msgstr "System settings"

#: src/pages/Index/Settings/PluginSettingsGroup.tsx:99
msgid "The settings below are specific to each available plugin"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:77
msgid "Authentication"
msgstr "認証"

#: src/pages/Index/Settings/SystemSettings.tsx:103
msgid "Barcodes"
msgstr "バーコード"

#: src/pages/Index/Settings/SystemSettings.tsx:118
#~ msgid "Physical Units"
#~ msgstr "Physical Units"

#: src/pages/Index/Settings/SystemSettings.tsx:119
#~ msgid "This panel is a placeholder."
#~ msgstr "This panel is a placeholder."

#: src/pages/Index/Settings/SystemSettings.tsx:127
#: src/pages/Index/Settings/UserSettings.tsx:112
msgid "The settings below are specific to each available notification method"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:133
msgid "Pricing"
msgstr "価格"

#: src/pages/Index/Settings/SystemSettings.tsx:135
#~ msgid "Exchange Rates"
#~ msgstr "Exchange Rates"

#: src/pages/Index/Settings/SystemSettings.tsx:169
msgid "Labels"
msgstr "ラベル"

#: src/pages/Index/Settings/SystemSettings.tsx:317
#~ msgid "Switch to User Setting"
#~ msgstr "Switch to User Setting"

#: src/pages/Index/Settings/UserSettings.tsx:41
msgid "Account"
msgstr "アカウント"

#: src/pages/Index/Settings/UserSettings.tsx:47
msgid "Security"
msgstr "セキュリティ"

#: src/pages/Index/Settings/UserSettings.tsx:53
msgid "Display Options"
msgstr "表示オプション"

#: src/pages/Index/Settings/UserSettings.tsx:159
#~ msgid "Switch to System Setting"
#~ msgstr "Switch to System Setting"

#: src/pages/Logged-In.tsx:24
#~ msgid "Found an exsisting login - using it to log you in."
#~ msgstr "Found an exsisting login - using it to log you in."

#: src/pages/NotFound.tsx:20
#~ msgid "Sorry, this page is not known or was moved."
#~ msgstr "Sorry, this page is not known or was moved."

#: src/pages/NotFound.tsx:27
#~ msgid "Go to the start page"
#~ msgstr "Go to the start page"

#: src/pages/Notifications.tsx:44
#~ msgid "Delete Notifications"
#~ msgstr "Delete Notifications"

#: src/pages/Notifications.tsx:83
msgid "History"
msgstr "履歴"

#: src/pages/Notifications.tsx:91
msgid "Mark as unread"
msgstr "未読にする"

#: src/pages/Notifications.tsx:146
#~ msgid "Delete notifications"
#~ msgstr "Delete notifications"

#: src/pages/build/BuildDetail.tsx:68
msgid "No Required Items"
msgstr ""

#: src/pages/build/BuildDetail.tsx:70
msgid "This build order does not have any required items."
msgstr ""

#: src/pages/build/BuildDetail.tsx:71
msgid "The assembled part may not have a Bill of Materials (BOM) defined, or the BOM is empty."
msgstr ""

#: src/pages/build/BuildDetail.tsx:80
#~ msgid "Build Status"
#~ msgstr "Build Status"

#: src/pages/build/BuildDetail.tsx:185
#: src/pages/part/PartDetail.tsx:269
#: src/pages/stock/StockDetail.tsx:150
#~ msgid "View part barcode"
#~ msgstr "View part barcode"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/company/ManufacturerPartDetail.tsx:83
#: src/pages/company/SupplierPartDetail.tsx:95
#: src/pages/part/PartDetail.tsx:454
#: src/pages/stock/StockDetail.tsx:153
#: src/tables/bom/BomTable.tsx:134
#: src/tables/bom/UsedInTable.tsx:40
#: src/tables/build/BuildAllocatedStockTable.tsx:108
#: src/tables/build/BuildLineTable.tsx:328
#: src/tables/build/BuildOrderTable.tsx:78
#: src/tables/part/PartSalesAllocationsTable.tsx:61
#: src/tables/part/RelatedPartTable.tsx:73
#: src/tables/sales/SalesOrderAllocationTable.tsx:132
#: src/tables/sales/SalesOrderLineItemTable.tsx:96
#: src/tables/stock/StockItemTable.tsx:70
msgid "IPN"
msgstr "即時支払通知"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/part/PartDetail.tsx:274
#~ msgid "Link custom barcode to part"
#~ msgstr "Link custom barcode to part"

#: src/pages/build/BuildDetail.tsx:196
#: src/pages/part/PartDetail.tsx:280
#~ msgid "Unlink custom barcode from part"
#~ msgstr "Unlink custom barcode from part"

#: src/pages/build/BuildDetail.tsx:198
#: src/pages/part/PartDetail.tsx:481
#: src/tables/bom/UsedInTable.tsx:44
#: src/tables/build/BuildOrderTable.tsx:82
#: src/tables/stock/StockItemTable.tsx:75
msgid "Revision"
msgstr "リビジョン"

#: src/pages/build/BuildDetail.tsx:202
#~ msgid "Build Order updated"
#~ msgstr "Build Order updated"

#: src/pages/build/BuildDetail.tsx:211
#: src/pages/purchasing/PurchaseOrderDetail.tsx:156
#: src/pages/sales/ReturnOrderDetail.tsx:121
#: src/pages/sales/SalesOrderDetail.tsx:130
#: src/pages/stock/StockDetail.tsx:168
msgid "Custom Status"
msgstr "カスタムステータス"

#: src/pages/build/BuildDetail.tsx:220
#: src/pages/build/BuildDetail.tsx:716
#: src/pages/build/BuildIndex.tsx:28
#: src/pages/stock/LocationDetail.tsx:142
#: src/tables/build/BuildOrderTable.tsx:122
#: src/tables/build/BuildOrderTable.tsx:184
#: src/tables/stock/StockLocationTable.tsx:48
msgid "External"
msgstr "外部"

#: src/pages/build/BuildDetail.tsx:221
#~ msgid "Edit build order"
#~ msgstr "Edit build order"

#: src/pages/build/BuildDetail.tsx:226
#~ msgid "Duplicate build order"
#~ msgstr "Duplicate build order"

#: src/pages/build/BuildDetail.tsx:231
#~ msgid "Delete build order"
#~ msgstr "Delete build order"

#: src/pages/build/BuildDetail.tsx:238
#: src/pages/purchasing/PurchaseOrderDetail.tsx:123
#: src/pages/sales/ReturnOrderDetail.tsx:88
#: src/pages/sales/SalesOrderDetail.tsx:97
#: src/tables/ColumnRenderers.tsx:312
#: src/tables/build/BuildAllocatedStockTable.tsx:115
#: src/tables/build/BuildLineTable.tsx:337
msgid "Reference"
msgstr "参照"

#: src/pages/build/BuildDetail.tsx:252
msgid "Parent Build"
msgstr "親ビルド"

#: src/pages/build/BuildDetail.tsx:263
msgid "Build Quantity"
msgstr "数量"

#: src/pages/build/BuildDetail.tsx:269
#: src/pages/part/PartDetail.tsx:598
#: src/tables/bom/BomTable.tsx:347
#: src/tables/bom/BomTable.tsx:382
msgid "Can Build"
msgstr "ビルド"

#: src/pages/build/BuildDetail.tsx:278
#: src/pages/build/BuildDetail.tsx:468
msgid "Completed Outputs"
msgstr "完成したアウトプット"

#: src/pages/build/BuildDetail.tsx:295
#: src/tables/Filter.tsx:373
msgid "Issued By"
msgstr "発行者"

#: src/pages/build/BuildDetail.tsx:303
#: src/pages/part/PartDetail.tsx:691
#: src/pages/purchasing/PurchaseOrderDetail.tsx:243
#: src/pages/sales/ReturnOrderDetail.tsx:207
#: src/pages/sales/SalesOrderDetail.tsx:219
#: src/tables/Filter.tsx:311
msgid "Responsible"
msgstr "責任"

#: src/pages/build/BuildDetail.tsx:321
msgid "Any location"
msgstr "場所"

#: src/pages/build/BuildDetail.tsx:328
msgid "Destination Location"
msgstr "目的地"

#: src/pages/build/BuildDetail.tsx:344
#: src/tables/settings/ApiTokenTable.tsx:98
#: src/tables/settings/PendingTasksTable.tsx:41
msgid "Created"
msgstr "作成日"

#: src/pages/build/BuildDetail.tsx:347
#: src/pages/part/PartDetail.tsx:727
#~ msgid "Test Statistics"
#~ msgstr "Test Statistics"

#: src/pages/build/BuildDetail.tsx:352
#: src/pages/purchasing/PurchaseOrderDetail.tsx:268
#: src/pages/sales/ReturnOrderDetail.tsx:233
#: src/pages/sales/SalesOrderDetail.tsx:244
#: src/tables/ColumnRenderers.tsx:424
msgid "Start Date"
msgstr "開始日"

#: src/pages/build/BuildDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:276
#: src/pages/sales/ReturnOrderDetail.tsx:241
#: src/pages/sales/SalesOrderDetail.tsx:252
#: src/tables/ColumnRenderers.tsx:432
#: src/tables/part/PartPurchaseOrdersTable.tsx:101
#: src/tables/sales/ReturnOrderLineItemTable.tsx:150
#: src/tables/sales/SalesOrderLineItemTable.tsx:130
msgid "Target Date"
msgstr "終了日に達したら"

#: src/pages/build/BuildDetail.tsx:368
#: src/tables/build/BuildOrderTable.tsx:92
#: src/tables/sales/SalesOrderLineItemTable.tsx:324
msgid "Completed"
msgstr "完了"

#: src/pages/build/BuildDetail.tsx:368
#~ msgid "Reporting Actions"
#~ msgstr "Reporting Actions"

#: src/pages/build/BuildDetail.tsx:374
#~ msgid "Print build report"
#~ msgstr "Print build report"

#: src/pages/build/BuildDetail.tsx:404
msgid "Build Details"
msgstr "詳細"

#: src/pages/build/BuildDetail.tsx:410
msgid "Required Parts"
msgstr ""

#: src/pages/build/BuildDetail.tsx:422
#: src/pages/sales/SalesOrderDetail.tsx:380
#: src/pages/sales/SalesOrderShipmentDetail.tsx:210
#: src/tables/part/PartSalesAllocationsTable.tsx:73
msgid "Allocated Stock"
msgstr "割当株式"

#: src/pages/build/BuildDetail.tsx:438
msgid "Consumed Stock"
msgstr "消費在庫"

#: src/pages/build/BuildDetail.tsx:455
msgid "Incomplete Outputs"
msgstr "不完全なアウトプット"

#: src/pages/build/BuildDetail.tsx:483
msgid "External Orders"
msgstr ""

#: src/pages/build/BuildDetail.tsx:497
msgid "Child Build Orders"
msgstr "チャイルド・ビルド・オーダー"

#: src/pages/build/BuildDetail.tsx:507
#: src/tables/build/BuildOutputTable.tsx:664
#: src/tables/stock/StockItemTestResultTable.tsx:167
msgid "Test Results"
msgstr "テストの結果"

#: src/pages/build/BuildDetail.tsx:544
msgid "Edit Build Order"
msgstr "ビルド順序の編集"

#: src/pages/build/BuildDetail.tsx:566
#: src/tables/build/BuildOrderTable.tsx:208
#: src/tables/build/BuildOrderTable.tsx:224
msgid "Add Build Order"
msgstr "ビルドオーダーの追加"

#: src/pages/build/BuildDetail.tsx:576
msgid "Cancel Build Order"
msgstr "ビルドオーダーのキャンセル"

#: src/pages/build/BuildDetail.tsx:578
#: src/pages/purchasing/PurchaseOrderDetail.tsx:398
#: src/pages/sales/ReturnOrderDetail.tsx:393
#: src/pages/sales/SalesOrderDetail.tsx:427
msgid "Order cancelled"
msgstr "注文のキャンセル"

#: src/pages/build/BuildDetail.tsx:579
#: src/pages/purchasing/PurchaseOrderDetail.tsx:397
#: src/pages/sales/ReturnOrderDetail.tsx:392
#: src/pages/sales/SalesOrderDetail.tsx:426
msgid "Cancel this order"
msgstr "この注文をキャンセル"

#: src/pages/build/BuildDetail.tsx:588
msgid "Hold Build Order"
msgstr "ホールドビルドオーダー"

#: src/pages/build/BuildDetail.tsx:590
#: src/pages/purchasing/PurchaseOrderDetail.tsx:405
#: src/pages/sales/ReturnOrderDetail.tsx:400
#: src/pages/sales/SalesOrderDetail.tsx:434
msgid "Place this order on hold"
msgstr "この注文を保留にします"

#: src/pages/build/BuildDetail.tsx:591
#: src/pages/purchasing/PurchaseOrderDetail.tsx:406
#: src/pages/sales/ReturnOrderDetail.tsx:401
#: src/pages/sales/SalesOrderDetail.tsx:435
msgid "Order placed on hold"
msgstr "注文の保留"

#: src/pages/build/BuildDetail.tsx:596
msgid "Issue Build Order"
msgstr "ビルドオーダーの発行"

#: src/pages/build/BuildDetail.tsx:598
#: src/pages/purchasing/PurchaseOrderDetail.tsx:389
#: src/pages/sales/ReturnOrderDetail.tsx:384
#: src/pages/sales/SalesOrderDetail.tsx:418
msgid "Issue this order"
msgstr "本命令の発令"

#: src/pages/build/BuildDetail.tsx:599
#: src/pages/purchasing/PurchaseOrderDetail.tsx:390
#: src/pages/sales/ReturnOrderDetail.tsx:385
#: src/pages/sales/SalesOrderDetail.tsx:419
msgid "Order issued"
msgstr "オーダー発行"

#: src/pages/build/BuildDetail.tsx:618
msgid "Complete Build Order"
msgstr "コンプリート・ビルド・オーダー"

#: src/pages/build/BuildDetail.tsx:624
#: src/pages/purchasing/PurchaseOrderDetail.tsx:418
#: src/pages/sales/ReturnOrderDetail.tsx:408
#: src/pages/sales/SalesOrderDetail.tsx:453
msgid "Mark this order as complete"
msgstr "この注文を完了としてマークしてください。"

#: src/pages/build/BuildDetail.tsx:627
#: src/pages/purchasing/PurchaseOrderDetail.tsx:412
#: src/pages/sales/ReturnOrderDetail.tsx:409
#: src/pages/sales/SalesOrderDetail.tsx:454
msgid "Order completed"
msgstr "注文完了"

#: src/pages/build/BuildDetail.tsx:654
#: src/pages/purchasing/PurchaseOrderDetail.tsx:441
#: src/pages/sales/ReturnOrderDetail.tsx:438
#: src/pages/sales/SalesOrderDetail.tsx:489
msgid "Issue Order"
msgstr "発行順序"

#: src/pages/build/BuildDetail.tsx:661
#: src/pages/purchasing/PurchaseOrderDetail.tsx:448
#: src/pages/sales/ReturnOrderDetail.tsx:445
#: src/pages/sales/SalesOrderDetail.tsx:503
msgid "Complete Order"
msgstr "完全な注文"

#: src/pages/build/BuildDetail.tsx:679
msgid "Build Order Actions"
msgstr "ビルドオーダーアクション"

#: src/pages/build/BuildDetail.tsx:684
#: src/pages/purchasing/PurchaseOrderDetail.tsx:470
#: src/pages/sales/ReturnOrderDetail.tsx:467
#: src/pages/sales/SalesOrderDetail.tsx:526
msgid "Edit order"
msgstr "掲載依頼を編集"

#: src/pages/build/BuildDetail.tsx:688
#: src/pages/purchasing/PurchaseOrderDetail.tsx:478
#: src/pages/sales/ReturnOrderDetail.tsx:473
#: src/pages/sales/SalesOrderDetail.tsx:531
msgid "Duplicate order"
msgstr "重複した注文"

#: src/pages/build/BuildDetail.tsx:692
#: src/pages/purchasing/PurchaseOrderDetail.tsx:481
#: src/pages/sales/ReturnOrderDetail.tsx:478
#: src/pages/sales/SalesOrderDetail.tsx:534
msgid "Hold order"
msgstr "ホールドオーダー"

#: src/pages/build/BuildDetail.tsx:697
#: src/pages/purchasing/PurchaseOrderDetail.tsx:486
#: src/pages/sales/ReturnOrderDetail.tsx:483
#: src/pages/sales/SalesOrderDetail.tsx:539
msgid "Cancel order"
msgstr "お見積をキャンセル"

#: src/pages/build/BuildDetail.tsx:735
#: src/pages/stock/StockDetail.tsx:341
#: src/tables/build/BuildAllocatedStockTable.tsx:85
#: src/tables/part/PartBuildAllocationsTable.tsx:45
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:150
#: src/tables/stock/StockTrackingTable.tsx:109
msgid "Build Order"
msgstr "組立注文"

#: src/pages/build/BuildIndex.tsx:23
#~ msgid "Build order created"
#~ msgstr "Build order created"

#: src/pages/build/BuildIndex.tsx:29
#: src/tables/build/BuildOrderTable.tsx:185
msgid "Show external build orders"
msgstr ""

#: src/pages/build/BuildIndex.tsx:39
#~ msgid "New Build Order"
#~ msgstr "New Build Order"

#: src/pages/build/BuildIndex.tsx:83
#: src/pages/purchasing/PurchasingIndex.tsx:69
#: src/pages/sales/SalesIndex.tsx:90
#: src/pages/sales/SalesIndex.tsx:111
msgid "Table View"
msgstr "テーブルビュー"

#: src/pages/build/BuildIndex.tsx:86
#: src/pages/purchasing/PurchasingIndex.tsx:72
#: src/pages/sales/SalesIndex.tsx:93
#: src/pages/sales/SalesIndex.tsx:114
msgid "Calendar View"
msgstr "カレンダービュー"

#: src/pages/company/CompanyDetail.tsx:99
msgid "Website"
msgstr "ウェブサイト"

#: src/pages/company/CompanyDetail.tsx:107
msgid "Phone Number"
msgstr "電話番号"

#: src/pages/company/CompanyDetail.tsx:114
msgid "Email Address"
msgstr "メールアドレス"

#: src/pages/company/CompanyDetail.tsx:121
msgid "Tax ID"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:131
msgid "Default Currency"
msgstr "デフォルトの通貨"

#: src/pages/company/CompanyDetail.tsx:136
#: src/pages/company/SupplierDetail.tsx:8
#: src/pages/company/SupplierPartDetail.tsx:129
#: src/pages/company/SupplierPartDetail.tsx:235
#: src/pages/company/SupplierPartDetail.tsx:360
#: src/pages/purchasing/PurchaseOrderDetail.tsx:138
#: src/tables/Filter.tsx:352
#: src/tables/company/CompanyTable.tsx:96
#: src/tables/part/PartPurchaseOrdersTable.tsx:43
#: src/tables/purchasing/PurchaseOrderTable.tsx:109
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:40
msgid "Supplier"
msgstr "仕入先"

#: src/pages/company/CompanyDetail.tsx:142
#: src/pages/company/ManufacturerDetail.tsx:8
#: src/pages/company/ManufacturerPartDetail.tsx:102
#: src/pages/company/ManufacturerPartDetail.tsx:264
#: src/pages/company/SupplierPartDetail.tsx:151
#: src/tables/Filter.tsx:339
#: src/tables/company/CompanyTable.tsx:101
#: src/tables/purchasing/SupplierPartTable.tsx:70
msgid "Manufacturer"
msgstr "製造元"

#: src/pages/company/CompanyDetail.tsx:148
#: src/pages/company/CustomerDetail.tsx:8
#: src/pages/part/pricing/SaleHistoryPanel.tsx:31
#: src/pages/sales/ReturnOrderDetail.tsx:103
#: src/pages/sales/SalesOrderDetail.tsx:112
#: src/pages/sales/SalesOrderShipmentDetail.tsx:102
#: src/pages/stock/StockDetail.tsx:367
#: src/tables/company/CompanyTable.tsx:106
#: src/tables/sales/ReturnOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:153
msgid "Customer"
msgstr "顧客"

#: src/pages/company/CompanyDetail.tsx:175
#~ msgid "Edit company"
#~ msgstr "Edit company"

#: src/pages/company/CompanyDetail.tsx:181
msgid "Company Details"
msgstr "会社詳細"

#: src/pages/company/CompanyDetail.tsx:187
msgid "Supplied Parts"
msgstr "供給部品"

#: src/pages/company/CompanyDetail.tsx:189
#~ msgid "Delete company"
#~ msgstr "Delete company"

#: src/pages/company/CompanyDetail.tsx:196
msgid "Manufactured Parts"
msgstr "製造部品"

#: src/pages/company/CompanyDetail.tsx:243
msgid "Assigned Stock"
msgstr "割り当て在庫"

#: src/pages/company/CompanyDetail.tsx:284
#: src/tables/company/CompanyTable.tsx:82
msgid "Edit Company"
msgstr "会社情報を編集"

#: src/pages/company/CompanyDetail.tsx:292
msgid "Delete Company"
msgstr "削除会社"

#: src/pages/company/CompanyDetail.tsx:307
msgid "Company Actions"
msgstr "会社の動き"

#: src/pages/company/ManufacturerPartDetail.tsx:76
#: src/pages/company/SupplierPartDetail.tsx:88
msgid "Internal Part"
msgstr "内部パーツ"

#: src/pages/company/ManufacturerPartDetail.tsx:110
#: src/pages/company/SupplierPartDetail.tsx:160
msgid "Manufacturer Part Number"
msgstr "メーカー品番"

#: src/pages/company/ManufacturerPartDetail.tsx:127
#: src/pages/company/SupplierPartDetail.tsx:112
msgid "External Link"
msgstr "外部リンク"

#: src/pages/company/ManufacturerPartDetail.tsx:146
#: src/pages/company/SupplierPartDetail.tsx:232
#: src/pages/part/PartDetail.tsx:787
msgid "Part Details"
msgstr "部品詳細"

#: src/pages/company/ManufacturerPartDetail.tsx:149
msgid "Manufacturer Details"
msgstr "メーカー詳細"

#: src/pages/company/ManufacturerPartDetail.tsx:158
msgid "Manufacturer Part Details"
msgstr "メーカーパーツ詳細"

#: src/pages/company/ManufacturerPartDetail.tsx:164
#: src/pages/part/PartDetail.tsx:793
msgid "Parameters"
msgstr "パラメータ"

#: src/pages/company/ManufacturerPartDetail.tsx:204
#: src/tables/purchasing/ManufacturerPartTable.tsx:84
msgid "Edit Manufacturer Part"
msgstr "メーカー・パーツの編集"

#: src/pages/company/ManufacturerPartDetail.tsx:211
#: src/tables/purchasing/ManufacturerPartTable.tsx:72
#: src/tables/purchasing/ManufacturerPartTable.tsx:104
msgid "Add Manufacturer Part"
msgstr "メーカー部品の追加"

#: src/pages/company/ManufacturerPartDetail.tsx:223
#: src/tables/purchasing/ManufacturerPartTable.tsx:92
msgid "Delete Manufacturer Part"
msgstr "メーカー部品の削除"

#: src/pages/company/ManufacturerPartDetail.tsx:238
msgid "Manufacturer Part Actions"
msgstr "メーカー品番"

#: src/pages/company/ManufacturerPartDetail.tsx:281
msgid "ManufacturerPart"
msgstr "メーカー部品"

#: src/pages/company/SupplierPartDetail.tsx:103
#: src/tables/part/RelatedPartTable.tsx:82
msgid "Part Description"
msgstr "パーツ説明"

#: src/pages/company/SupplierPartDetail.tsx:179
#: src/tables/part/PartPurchaseOrdersTable.tsx:73
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:184
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:226
#: src/tables/purchasing/SupplierPartTable.tsx:120
msgid "Pack Quantity"
msgstr "パック数量"

#: src/pages/company/SupplierPartDetail.tsx:197
#: src/pages/company/SupplierPartDetail.tsx:390
#: src/pages/part/PartDetail.tsx:1000
#: src/tables/bom/BomTable.tsx:414
#: src/tables/part/PartTable.tsx:95
msgid "On Order"
msgstr "注文中"

#: src/pages/company/SupplierPartDetail.tsx:204
msgid "Supplier Availability"
msgstr "サプライヤーの空き状況"

#: src/pages/company/SupplierPartDetail.tsx:212
msgid "Availability Updated"
msgstr "空席状況更新"

#: src/pages/company/SupplierPartDetail.tsx:237
msgid "Availability"
msgstr "有効性"

#: src/pages/company/SupplierPartDetail.tsx:246
msgid "Supplier Part Details"
msgstr "サプライヤー部品詳細"

#: src/pages/company/SupplierPartDetail.tsx:252
#: src/pages/purchasing/PurchaseOrderDetail.tsx:361
msgid "Received Stock"
msgstr "受入在庫"

#: src/pages/company/SupplierPartDetail.tsx:279
#: src/pages/part/PartPricingPanel.tsx:113
#: src/pages/part/pricing/PricingOverviewPanel.tsx:232
msgid "Supplier Pricing"
msgstr "サプライヤー価格"

#: src/pages/company/SupplierPartDetail.tsx:304
msgid "Supplier Part Actions"
msgstr "サプライヤー"

#: src/pages/company/SupplierPartDetail.tsx:328
#: src/tables/purchasing/SupplierPartTable.tsx:207
msgid "Edit Supplier Part"
msgstr "サプライヤーパーツの編集"

#: src/pages/company/SupplierPartDetail.tsx:336
#: src/tables/purchasing/SupplierPartTable.tsx:215
msgid "Delete Supplier Part"
msgstr "サプライヤーの削除"

#: src/pages/company/SupplierPartDetail.tsx:344
#: src/tables/purchasing/SupplierPartTable.tsx:154
msgid "Add Supplier Part"
msgstr "サプライヤー部品の追加"

#: src/pages/company/SupplierPartDetail.tsx:384
#: src/pages/part/PartDetail.tsx:988
msgid "No Stock"
msgstr "在庫切れ"

#: src/pages/core/CoreIndex.tsx:46
#: src/pages/core/GroupDetail.tsx:81
#: src/pages/core/UserDetail.tsx:224
msgid "System Overview"
msgstr "システム概要"

#: src/pages/core/GroupDetail.tsx:45
msgid "Group Name"
msgstr "グループ名"

#: src/pages/core/GroupDetail.tsx:52
#: src/pages/core/GroupDetail.tsx:67
#: src/tables/settings/GroupTable.tsx:85
msgid "Group Details"
msgstr "グループの詳細"

#: src/pages/core/GroupDetail.tsx:55
#: src/tables/settings/GroupTable.tsx:112
msgid "Group Roles"
msgstr "グループロール"

#: src/pages/core/UserDetail.tsx:175
msgid "User Information"
msgstr "ユーザー情報"

#: src/pages/core/UserDetail.tsx:176
msgid "User Permissions"
msgstr "ユーザー権限"

#: src/pages/core/UserDetail.tsx:178
msgid "User Profile"
msgstr "ユーザープロフィール"

#: src/pages/core/UserDetail.tsx:188
#: src/tables/settings/UserTable.tsx:164
msgid "User Details"
msgstr "ユーザー詳細"

#: src/pages/core/UserDetail.tsx:206
msgid "Basic user"
msgstr "基本ユーザー"

#: src/pages/part/CategoryDetail.tsx:98
#: src/pages/stock/LocationDetail.tsx:96
#: src/tables/settings/ErrorTable.tsx:63
#: src/tables/settings/ErrorTable.tsx:108
msgid "Path"
msgstr "パス"

#: src/pages/part/CategoryDetail.tsx:114
msgid "Parent Category"
msgstr "親カテゴリ"

#: src/pages/part/CategoryDetail.tsx:137
#: src/pages/part/CategoryDetail.tsx:267
msgid "Subcategories"
msgstr "サブカテゴリ"

#: src/pages/part/CategoryDetail.tsx:144
#: src/pages/stock/LocationDetail.tsx:136
#: src/tables/part/PartCategoryTable.tsx:89
#: src/tables/stock/StockLocationTable.tsx:43
msgid "Structural"
msgstr "構造に関するパターン"

#: src/pages/part/CategoryDetail.tsx:150
msgid "Parent default location"
msgstr "親のデフォルトの場所"

#: src/pages/part/CategoryDetail.tsx:157
msgid "Default location"
msgstr "デフォルトの場所"

#: src/pages/part/CategoryDetail.tsx:168
msgid "Top level part category"
msgstr "トップレベルのパーツカテゴリ"

#: src/pages/part/CategoryDetail.tsx:178
#: src/pages/part/CategoryDetail.tsx:244
#: src/tables/part/PartCategoryTable.tsx:122
msgid "Edit Part Category"
msgstr "部品カテゴリーの編集"

#: src/pages/part/CategoryDetail.tsx:187
msgid "Move items to parent category"
msgstr "項目を親カテゴリに移動"

#: src/pages/part/CategoryDetail.tsx:191
#: src/pages/stock/LocationDetail.tsx:228
msgid "Delete items"
msgstr "アイテムの削除"

#: src/pages/part/CategoryDetail.tsx:199
#: src/pages/part/CategoryDetail.tsx:249
msgid "Delete Part Category"
msgstr "部品カテゴリの削除"

#: src/pages/part/CategoryDetail.tsx:202
msgid "Parts Action"
msgstr "パーツアクション"

#: src/pages/part/CategoryDetail.tsx:203
msgid "Action for parts in this category"
msgstr "このカテゴリの部品のアクション"

#: src/pages/part/CategoryDetail.tsx:208
msgid "Child Categories Action"
msgstr "チャイルド・カテゴリー・アクション"

#: src/pages/part/CategoryDetail.tsx:209
msgid "Action for child categories in this category"
msgstr "このカテゴリーに含まれる子どもの行動"

#: src/pages/part/CategoryDetail.tsx:240
#: src/tables/part/PartCategoryTable.tsx:143
msgid "Category Actions"
msgstr "カテゴリー・アクション"

#: src/pages/part/CategoryDetail.tsx:261
msgid "Category Details"
msgstr "カテゴリー詳細"

#: src/pages/part/PartAllocationPanel.tsx:21
#: src/pages/stock/StockDetail.tsx:539
#: src/tables/part/PartTable.tsx:108
msgid "Build Order Allocations"
msgstr "ビルド・オーダー・アロケーション"

#: src/pages/part/PartAllocationPanel.tsx:31
#: src/pages/stock/StockDetail.tsx:554
#: src/tables/part/PartTable.tsx:116
msgid "Sales Order Allocations"
msgstr "販売注文の割り当て"

#: src/pages/part/PartDetail.tsx:181
#: src/pages/part/PartDetail.tsx:184
#: src/pages/part/PartDetail.tsx:228
msgid "Validate BOM"
msgstr "BOMの検証"

#: src/pages/part/PartDetail.tsx:185
msgid "Do you want to validate the bill of materials for this assembly?"
msgstr "このアセンブリの部品表を検証しますか？"

#: src/pages/part/PartDetail.tsx:188
msgid "BOM validated"
msgstr "BOM検証済み"

#: src/pages/part/PartDetail.tsx:206
msgid "BOM Validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:207
msgid "The Bill of Materials for this part has been validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:211
#: src/pages/part/PartDetail.tsx:216
msgid "BOM Not Validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:212
msgid "The Bill of Materials for this part has previously been checked, but requires revalidation"
msgstr ""

#: src/pages/part/PartDetail.tsx:217
msgid "The Bill of Materials for this part has not yet been validated"
msgstr ""

#: src/pages/part/PartDetail.tsx:248
msgid "Validated On"
msgstr ""

#: src/pages/part/PartDetail.tsx:253
msgid "Validated By"
msgstr ""

#: src/pages/part/PartDetail.tsx:286
#~ msgid "Variant Stock"
#~ msgstr "Variant Stock"

#: src/pages/part/PartDetail.tsx:310
#~ msgid "Edit part"
#~ msgstr "Edit part"

#: src/pages/part/PartDetail.tsx:322
#~ msgid "Duplicate part"
#~ msgstr "Duplicate part"

#: src/pages/part/PartDetail.tsx:327
#~ msgid "Delete part"
#~ msgstr "Delete part"

#: src/pages/part/PartDetail.tsx:467
msgid "Variant of"
msgstr "変種"

#: src/pages/part/PartDetail.tsx:474
msgid "Revision of"
msgstr "改訂版"

#: src/pages/part/PartDetail.tsx:494
#: src/tables/ColumnRenderers.tsx:200
#: src/tables/ColumnRenderers.tsx:209
msgid "Default Location"
msgstr "デフォルトの場所"

#: src/pages/part/PartDetail.tsx:501
msgid "Category Default Location"
msgstr "カテゴリー デフォルトの場所"

#: src/pages/part/PartDetail.tsx:508
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:48
msgid "Units"
msgstr "単位"

#: src/pages/part/PartDetail.tsx:510
#~ msgid "Stocktake By"
#~ msgstr "Stocktake By"

#: src/pages/part/PartDetail.tsx:515
#: src/tables/settings/PendingTasksTable.tsx:51
msgid "Keywords"
msgstr "キーワード"

#: src/pages/part/PartDetail.tsx:542
#: src/tables/bom/BomTable.tsx:409
#: src/tables/build/BuildLineTable.tsx:297
#: src/tables/part/PartTable.tsx:306
#: src/tables/sales/SalesOrderLineItemTable.tsx:134
msgid "Available Stock"
msgstr "在庫状況"

#: src/pages/part/PartDetail.tsx:548
#: src/tables/bom/BomTable.tsx:323
#: src/tables/build/BuildLineTable.tsx:259
#: src/tables/sales/SalesOrderLineItemTable.tsx:172
msgid "On order"
msgstr "注文中"

#: src/pages/part/PartDetail.tsx:555
msgid "Required for Orders"
msgstr "ご注文に必要なもの"

#: src/pages/part/PartDetail.tsx:566
msgid "Allocated to Build Orders"
msgstr "建設受注に割り当て"

#: src/pages/part/PartDetail.tsx:578
msgid "Allocated to Sales Orders"
msgstr "販売注文に割り当て"

#: src/pages/part/PartDetail.tsx:587
#: src/pages/part/PartDetail.tsx:1006
#: src/pages/stock/StockDetail.tsx:896
#: src/tables/build/BuildOrderTestTable.tsx:273
#: src/tables/stock/StockItemTable.tsx:364
msgid "In Production"
msgstr "生産中"

#: src/pages/part/PartDetail.tsx:605
msgid "Minimum Stock"
msgstr "最小在庫"

#: src/pages/part/PartDetail.tsx:613
#~ msgid "Scheduling"
#~ msgstr "Scheduling"

#: src/pages/part/PartDetail.tsx:620
#: src/tables/part/ParametricPartTable.tsx:355
#: src/tables/part/PartTable.tsx:190
msgid "Locked"
msgstr "ロック中"

#: src/pages/part/PartDetail.tsx:626
msgid "Template Part"
msgstr "テンプレート部品"

#: src/pages/part/PartDetail.tsx:631
#: src/tables/bom/BomTable.tsx:404
msgid "Assembled Part"
msgstr "組立部品"

#: src/pages/part/PartDetail.tsx:636
msgid "Component Part"
msgstr "構成部品"

#: src/pages/part/PartDetail.tsx:641
#: src/tables/bom/BomTable.tsx:394
msgid "Testable Part"
msgstr "テスト可能な部分"

#: src/pages/part/PartDetail.tsx:647
#: src/tables/bom/BomTable.tsx:399
msgid "Trackable Part"
msgstr "追跡可能部品"

#: src/pages/part/PartDetail.tsx:652
msgid "Purchaseable Part"
msgstr "購入可能部品"

#: src/pages/part/PartDetail.tsx:658
msgid "Saleable Part"
msgstr "売却可能部分"

#: src/pages/part/PartDetail.tsx:663
msgid "Virtual Part"
msgstr "バーチャルパート"

#: src/pages/part/PartDetail.tsx:678
#: src/pages/purchasing/PurchaseOrderDetail.tsx:253
#: src/pages/sales/ReturnOrderDetail.tsx:217
#: src/pages/sales/SalesOrderDetail.tsx:229
#: src/tables/ColumnRenderers.tsx:440
msgid "Creation Date"
msgstr "作成日時"

#: src/pages/part/PartDetail.tsx:683
#: src/tables/ColumnRenderers.tsx:386
#: src/tables/Filter.tsx:365
msgid "Created By"
msgstr "作成者"

#: src/pages/part/PartDetail.tsx:698
msgid "Default Supplier"
msgstr "デフォルト・サプライヤー"

#: src/pages/part/PartDetail.tsx:704
msgid "Default Expiry"
msgstr ""

#: src/pages/part/PartDetail.tsx:709
msgid "days"
msgstr ""

#: src/pages/part/PartDetail.tsx:719
#: src/pages/part/pricing/BomPricingPanel.tsx:113
#: src/pages/part/pricing/VariantPricingPanel.tsx:95
#: src/tables/part/PartTable.tsx:166
msgid "Price Range"
msgstr "料金帯"

#: src/pages/part/PartDetail.tsx:729
msgid "Latest Serial Number"
msgstr "最新のシリアル番号"

#: src/pages/part/PartDetail.tsx:757
msgid "Select Part Revision"
msgstr "部品リビジョンの選択"

#: src/pages/part/PartDetail.tsx:822
msgid "Variants"
msgstr "バリアント"

#: src/pages/part/PartDetail.tsx:829
#: src/pages/stock/StockDetail.tsx:526
msgid "Allocations"
msgstr "割り当て"

#: src/pages/part/PartDetail.tsx:836
msgid "Bill of Materials"
msgstr "部品表"

#: src/pages/part/PartDetail.tsx:848
msgid "Used In"
msgstr "中古"

#: src/pages/part/PartDetail.tsx:855
msgid "Part Pricing"
msgstr "部品価格"

#: src/pages/part/PartDetail.tsx:923
msgid "Test Templates"
msgstr "テストテンプレート"

#: src/pages/part/PartDetail.tsx:934
msgid "Related Parts"
msgstr "関連部品"

#: src/pages/part/PartDetail.tsx:956
#~ msgid "Count part stock"
#~ msgstr "Count part stock"

#: src/pages/part/PartDetail.tsx:967
#~ msgid "Transfer part stock"
#~ msgstr "Transfer part stock"

#: src/pages/part/PartDetail.tsx:994
#: src/tables/part/PartTestTemplateTable.tsx:112
#: src/tables/stock/StockItemTestResultTable.tsx:401
msgid "Required"
msgstr "必須"

#: src/pages/part/PartDetail.tsx:1025
#: src/tables/part/PartTable.tsx:355
msgid "Edit Part"
msgstr "パーツを編集"

#: src/pages/part/PartDetail.tsx:1065
#: src/tables/part/PartTable.tsx:343
#: src/tables/part/PartTable.tsx:420
msgid "Add Part"
msgstr "部品追加"

#: src/pages/part/PartDetail.tsx:1079
msgid "Delete Part"
msgstr "削除部分"

#: src/pages/part/PartDetail.tsx:1088
msgid "Deleting this part cannot be reversed"
msgstr "この部分の削除は元に戻せません"

#: src/pages/part/PartDetail.tsx:1149
#: src/pages/stock/StockDetail.tsx:854
msgid "Order"
msgstr "注文"

#: src/pages/part/PartDetail.tsx:1150
#: src/pages/stock/StockDetail.tsx:855
#: src/tables/build/BuildLineTable.tsx:740
msgid "Order Stock"
msgstr "注文在庫"

#: src/pages/part/PartDetail.tsx:1162
msgid "Search by serial number"
msgstr ""

#: src/pages/part/PartDetail.tsx:1170
#: src/tables/part/PartTable.tsx:392
msgid "Part Actions"
msgstr "パートアクション"

#: src/pages/part/PartIndex.tsx:29
#~ msgid "Categories"
#~ msgstr "Categories"

#: src/pages/part/PartPricingPanel.tsx:72
msgid "No pricing data found for this part."
msgstr "この部品の価格データは見つかりませんでした。"

#: src/pages/part/PartPricingPanel.tsx:87
#: src/pages/part/pricing/PricingOverviewPanel.tsx:325
msgid "Pricing Overview"
msgstr "価格概要"

#: src/pages/part/PartPricingPanel.tsx:93
msgid "Purchase History"
msgstr "購入履歴"

#: src/pages/part/PartPricingPanel.tsx:107
#: src/pages/part/pricing/PricingOverviewPanel.tsx:204
msgid "Internal Pricing"
msgstr "内部価格設定"

#: src/pages/part/PartPricingPanel.tsx:122
#: src/pages/part/pricing/PricingOverviewPanel.tsx:214
msgid "BOM Pricing"
msgstr "BOM価格"

#: src/pages/part/PartPricingPanel.tsx:129
#: src/pages/part/pricing/PricingOverviewPanel.tsx:242
msgid "Variant Pricing"
msgstr "バリアント価格"

#: src/pages/part/PartPricingPanel.tsx:141
#: src/pages/part/pricing/PricingOverviewPanel.tsx:251
msgid "Sale Pricing"
msgstr "セール価格"

#: src/pages/part/PartPricingPanel.tsx:147
#: src/pages/part/pricing/PricingOverviewPanel.tsx:260
msgid "Sale History"
msgstr "販売履歴"

#: src/pages/part/PartSchedulingDetail.tsx:51
#: src/pages/part/PartSchedulingDetail.tsx:291
#~ msgid "Scheduled"
#~ msgstr "Scheduled"

#: src/pages/part/PartSchedulingDetail.tsx:95
#~ msgid "Quantity is speculative"
#~ msgstr "Quantity is speculative"

#: src/pages/part/PartSchedulingDetail.tsx:104
#~ msgid "No date available for provided quantity"
#~ msgstr "No date available for provided quantity"

#: src/pages/part/PartSchedulingDetail.tsx:108
#~ msgid "Date is in the past"
#~ msgstr "Date is in the past"

#: src/pages/part/PartSchedulingDetail.tsx:115
#~ msgid "Scheduled Quantity"
#~ msgstr "Scheduled Quantity"

#: src/pages/part/PartSchedulingDetail.tsx:242
#~ msgid "No information available"
#~ msgstr "No information available"

#: src/pages/part/PartSchedulingDetail.tsx:243
#~ msgid "There is no scheduling information available for the selected part"
#~ msgstr "There is no scheduling information available for the selected part"

#: src/pages/part/PartSchedulingDetail.tsx:278
#~ msgid "Expected Quantity"
#~ msgstr "Expected Quantity"

#: src/pages/part/PartStockHistoryDetail.tsx:80
msgid "Edit Stocktake Entry"
msgstr "ストックテークエントリーの編集"

#: src/pages/part/PartStockHistoryDetail.tsx:88
msgid "Delete Stocktake Entry"
msgstr "ストックテークエントリーの削除"

#: src/pages/part/PartStockHistoryDetail.tsx:107
#: src/pages/part/PartStockHistoryDetail.tsx:211
#: src/pages/stock/StockDetail.tsx:399
#: src/tables/stock/StockItemTable.tsx:272
msgid "Stock Value"
msgstr "株式価値"

#: src/pages/part/PartStockHistoryDetail.tsx:240
#: src/pages/part/pricing/PricingOverviewPanel.tsx:327
msgid "Minimum Value"
msgstr "最小値"

#: src/pages/part/PartStockHistoryDetail.tsx:246
#: src/pages/part/pricing/PricingOverviewPanel.tsx:328
msgid "Maximum Value"
msgstr "最大値は"

#: src/pages/part/PartStocktakeDetail.tsx:99
#: src/tables/settings/StocktakeReportTable.tsx:70
#~ msgid "Generate Stocktake Report"
#~ msgstr "Generate Stocktake Report"

#: src/pages/part/PartStocktakeDetail.tsx:104
#: src/tables/settings/StocktakeReportTable.tsx:72
#~ msgid "Stocktake report scheduled"
#~ msgstr "Stocktake report scheduled"

#: src/pages/part/PartStocktakeDetail.tsx:145
#: src/tables/settings/StocktakeReportTable.tsx:78
#~ msgid "New Stocktake Report"
#~ msgstr "New Stocktake Report"

#: src/pages/part/pricing/BomPricingPanel.tsx:87
#: src/pages/part/pricing/BomPricingPanel.tsx:175
#: src/tables/ColumnRenderers.tsx:490
#: src/tables/bom/BomTable.tsx:270
#: src/tables/general/ExtraLineItemTable.tsx:69
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:255
#: src/tables/purchasing/PurchaseOrderTable.tsx:138
#: src/tables/sales/ReturnOrderTable.tsx:139
#: src/tables/sales/SalesOrderLineItemTable.tsx:120
#: src/tables/sales/SalesOrderTable.tsx:175
msgid "Total Price"
msgstr "合計金額"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#: src/pages/part/pricing/BomPricingPanel.tsx:141
#: src/tables/bom/UsedInTable.tsx:54
#: src/tables/part/PartTable.tsx:214
msgid "Component"
msgstr "コンポーネント"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#~ msgid "Minimum Total Price"
#~ msgstr "Minimum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:115
#: src/pages/part/pricing/VariantPricingPanel.tsx:35
#: src/pages/part/pricing/VariantPricingPanel.tsx:97
msgid "Minimum Price"
msgstr "最小価格"

#: src/pages/part/pricing/BomPricingPanel.tsx:116
#: src/pages/part/pricing/VariantPricingPanel.tsx:43
#: src/pages/part/pricing/VariantPricingPanel.tsx:98
msgid "Maximum Price"
msgstr "最大価格"

#: src/pages/part/pricing/BomPricingPanel.tsx:117
#~ msgid "Maximum Total Price"
#~ msgstr "Maximum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:166
#: src/pages/part/pricing/PriceBreakPanel.tsx:173
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:71
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:126
#: src/pages/part/pricing/SupplierPricingPanel.tsx:66
#: src/pages/stock/StockDetail.tsx:387
#: src/tables/bom/BomTable.tsx:260
#: src/tables/general/ExtraLineItemTable.tsx:61
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:250
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:84
#: src/tables/stock/StockItemTable.tsx:260
msgid "Unit Price"
msgstr "単価"

#: src/pages/part/pricing/BomPricingPanel.tsx:256
msgid "Pie Chart"
msgstr "円グラフ"

#: src/pages/part/pricing/BomPricingPanel.tsx:257
msgid "Bar Chart"
msgstr "棒グラフ"

#: src/pages/part/pricing/PriceBreakPanel.tsx:58
#: src/pages/part/pricing/PriceBreakPanel.tsx:111
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:134
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:162
msgid "Add Price Break"
msgstr "価格改定の追加"

#: src/pages/part/pricing/PriceBreakPanel.tsx:71
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:146
msgid "Edit Price Break"
msgstr "価格改定の編集"

#: src/pages/part/pricing/PriceBreakPanel.tsx:81
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:154
msgid "Delete Price Break"
msgstr "プライスブレークの削除"

#: src/pages/part/pricing/PriceBreakPanel.tsx:95
msgid "Price Break"
msgstr "価格ブレーク"

#: src/pages/part/pricing/PriceBreakPanel.tsx:171
msgid "Price"
msgstr "価格"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:72
msgid "Refreshing pricing data"
msgstr "価格データの更新"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:92
msgid "Pricing data updated"
msgstr "価格データ更新"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:99
msgid "Failed to update pricing data"
msgstr "価格データの更新に失敗しました"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:127
msgid "Edit Pricing"
msgstr "価格設定を編集"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:139
msgid "Pricing Category"
msgstr "価格カテゴリー"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:158
msgid "Minimum"
msgstr "最小"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:170
msgid "Maximum"
msgstr "最大"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:188
msgid "Override Pricing"
msgstr "オーバーライド価格"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:196
msgid "Overall Pricing"
msgstr "全体価格"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:222
msgid "Purchase Pricing"
msgstr "購入価格"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:281
#: src/pages/stock/StockDetail.tsx:179
#: src/tables/part/PartParameterTable.tsx:121
#: src/tables/stock/StockItemTable.tsx:301
msgid "Last Updated"
msgstr "最終更新"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:285
msgid "Pricing Not Set"
msgstr "価格未定"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:286
msgid "Pricing data has not been calculated for this part"
msgstr "この部品の価格データは計算されていません。"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:290
msgid "Pricing Actions"
msgstr "価格設定"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:293
msgid "Refresh"
msgstr "更新"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:294
msgid "Refresh pricing data"
msgstr "価格データの更新"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:309
msgid "Edit pricing data"
msgstr "価格データの編集"

#: src/pages/part/pricing/PricingPanel.tsx:24
msgid "No data available"
msgstr "データがありません"

#: src/pages/part/pricing/PricingPanel.tsx:65
msgid "No Data"
msgstr "データなし"

#: src/pages/part/pricing/PricingPanel.tsx:66
msgid "No pricing data available"
msgstr "価格データはありません"

#: src/pages/part/pricing/PricingPanel.tsx:77
msgid "Loading pricing data"
msgstr "価格データの読み込み"

#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:48
msgid "Purchase Price"
msgstr "購入金額"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#~ msgid "Sale Order"
#~ msgstr "Sale Order"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:44
#: src/pages/part/pricing/SaleHistoryPanel.tsx:87
msgid "Sale Price"
msgstr "セール価格"

#: src/pages/part/pricing/SupplierPricingPanel.tsx:69
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:75
msgid "Supplier Price"
msgstr "サプライヤー価格"

#: src/pages/part/pricing/VariantPricingPanel.tsx:29
#: src/pages/part/pricing/VariantPricingPanel.tsx:94
msgid "Variant Part"
msgstr "バリアントパーツ"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:89
msgid "Edit Purchase Order"
msgstr "発注書の編集"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:107
#: src/tables/purchasing/PurchaseOrderTable.tsx:154
#: src/tables/purchasing/PurchaseOrderTable.tsx:167
msgid "Add Purchase Order"
msgstr "発注書の追加"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:129
msgid "Supplier Reference"
msgstr "サプライヤー・リファレンス"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:159
#: src/pages/sales/ReturnOrderDetail.tsx:126
#: src/pages/sales/SalesOrderDetail.tsx:130
#~ msgid "Order Currency,"
#~ msgstr "Order Currency,"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:169
#: src/pages/sales/ReturnOrderDetail.tsx:140
#: src/pages/sales/SalesOrderDetail.tsx:143
msgid "Completed Line Items"
msgstr "完了した項目"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:178
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:261
msgid "Destination"
msgstr "目的地"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:184
#: src/pages/sales/ReturnOrderDetail.tsx:147
#: src/pages/sales/SalesOrderDetail.tsx:160
msgid "Order Currency"
msgstr "注文通貨"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:190
#: src/pages/sales/ReturnOrderDetail.tsx:154
#: src/pages/sales/SalesOrderDetail.tsx:166
msgid "Total Cost"
msgstr "合計コスト"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:207
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:191
#~ msgid "Created On"
#~ msgstr "Created On"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:219
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:195
msgid "Contact Email"
msgstr "連絡先メールアドレス"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:227
#: src/pages/sales/ReturnOrderDetail.tsx:191
#: src/pages/sales/SalesOrderDetail.tsx:203
msgid "Contact Phone"
msgstr "連絡先電話番号"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:260
#: src/pages/sales/ReturnOrderDetail.tsx:225
#: src/pages/sales/SalesOrderDetail.tsx:236
msgid "Issue Date"
msgstr "発行日"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:249
#: src/pages/sales/SalesOrderDetail.tsx:259
#: src/tables/ColumnRenderers.tsx:448
#: src/tables/build/BuildOrderTable.tsx:136
#: src/tables/part/PartPurchaseOrdersTable.tsx:106
msgid "Completion Date"
msgstr "完了日"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:315
#: src/pages/sales/ReturnOrderDetail.tsx:279
#: src/pages/sales/SalesOrderDetail.tsx:325
msgid "Order Details"
msgstr "ご注文の詳細"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:321
#: src/pages/purchasing/PurchaseOrderDetail.tsx:330
#: src/pages/sales/ReturnOrderDetail.tsx:133
#: src/pages/sales/ReturnOrderDetail.tsx:285
#: src/pages/sales/ReturnOrderDetail.tsx:294
#: src/pages/sales/SalesOrderDetail.tsx:331
#: src/pages/sales/SalesOrderDetail.tsx:340
msgid "Line Items"
msgstr "ラインアイテム"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:344
#: src/pages/sales/ReturnOrderDetail.tsx:308
#: src/pages/sales/SalesOrderDetail.tsx:357
msgid "Extra Line Items"
msgstr "追加項目"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:387
msgid "Issue Purchase Order"
msgstr "発注書の発行"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:395
msgid "Cancel Purchase Order"
msgstr "発注書のキャンセル"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:403
msgid "Hold Purchase Order"
msgstr "発注書の保留"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:411
msgid "Complete Purchase Order"
msgstr "完全な発注書"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:466
#: src/pages/sales/ReturnOrderDetail.tsx:463
#: src/pages/sales/SalesOrderDetail.tsx:521
msgid "Order Actions"
msgstr "注文の操作"

#: src/pages/sales/ReturnOrderDetail.tsx:94
#: src/pages/sales/SalesOrderDetail.tsx:103
#: src/pages/sales/SalesOrderShipmentDetail.tsx:111
#: src/tables/sales/SalesOrderTable.tsx:141
msgid "Customer Reference"
msgstr "得意先参照"

#: src/pages/sales/ReturnOrderDetail.tsx:349
#~ msgid "Order canceled"
#~ msgstr "Order canceled"

#: src/pages/sales/ReturnOrderDetail.tsx:355
msgid "Edit Return Order"
msgstr "返品注文の編集"

#: src/pages/sales/ReturnOrderDetail.tsx:373
#: src/tables/sales/ReturnOrderTable.tsx:154
#: src/tables/sales/ReturnOrderTable.tsx:167
msgid "Add Return Order"
msgstr "返品注文の追加"

#: src/pages/sales/ReturnOrderDetail.tsx:382
msgid "Issue Return Order"
msgstr "返品注文の発行"

#: src/pages/sales/ReturnOrderDetail.tsx:390
msgid "Cancel Return Order"
msgstr "返品注文のキャンセル"

#: src/pages/sales/ReturnOrderDetail.tsx:398
msgid "Hold Return Order"
msgstr "返品注文の保留"

#: src/pages/sales/ReturnOrderDetail.tsx:406
msgid "Complete Return Order"
msgstr "リターンオーダー"

#: src/pages/sales/SalesOrderDetail.tsx:152
msgid "Completed Shipments"
msgstr "完了した出荷"

#: src/pages/sales/SalesOrderDetail.tsx:256
#~ msgid "Pending Shipments"
#~ msgstr "Pending Shipments"

#: src/pages/sales/SalesOrderDetail.tsx:292
msgid "Edit Sales Order"
msgstr "販売注文の編集"

#: src/pages/sales/SalesOrderDetail.tsx:314
#: src/tables/sales/SalesOrderTable.tsx:108
#: src/tables/sales/SalesOrderTable.tsx:121
msgid "Add Sales Order"
msgstr "販売注文の追加"

#: src/pages/sales/SalesOrderDetail.tsx:374
#: src/tables/sales/SalesOrderTable.tsx:147
msgid "Shipments"
msgstr "発送"

#: src/pages/sales/SalesOrderDetail.tsx:416
msgid "Issue Sales Order"
msgstr "販売注文書の発行"

#: src/pages/sales/SalesOrderDetail.tsx:424
msgid "Cancel Sales Order"
msgstr "販売注文のキャンセル"

#: src/pages/sales/SalesOrderDetail.tsx:432
msgid "Hold Sales Order"
msgstr "販売注文の保留"

#: src/pages/sales/SalesOrderDetail.tsx:440
msgid "Ship Sales Order"
msgstr "販売注文の発送"

#: src/pages/sales/SalesOrderDetail.tsx:442
msgid "Ship this order?"
msgstr "この注文を発送しますか？"

#: src/pages/sales/SalesOrderDetail.tsx:443
msgid "Order shipped"
msgstr "発送済み注文"

#: src/pages/sales/SalesOrderDetail.tsx:451
msgid "Complete Sales Order"
msgstr "完全な販売注文"

#: src/pages/sales/SalesOrderDetail.tsx:496
msgid "Ship Order"
msgstr "船舶発注"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:119
#: src/tables/sales/SalesOrderShipmentTable.tsx:94
msgid "Shipment Reference"
msgstr "出荷基準"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:126
msgid "Allocated Items"
msgstr "割当項目"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:135
msgid "Tracking Number"
msgstr "追跡番号"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:143
msgid "Invoice Number"
msgstr "請求書番号"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:151
#: src/tables/ColumnRenderers.tsx:456
#: src/tables/sales/SalesOrderAllocationTable.tsx:179
#: src/tables/sales/SalesOrderShipmentTable.tsx:113
msgid "Shipment Date"
msgstr "出荷日"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:159
#: src/tables/sales/SalesOrderShipmentTable.tsx:117
msgid "Delivery Date"
msgstr "配達日"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:204
msgid "Shipment Details"
msgstr "出荷の詳細"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:211
#~ msgid "Assigned Items"
#~ msgstr "Assigned Items"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:242
#: src/pages/sales/SalesOrderShipmentDetail.tsx:334
#: src/tables/sales/SalesOrderShipmentTable.tsx:73
msgid "Edit Shipment"
msgstr "出荷の編集"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:249
#: src/pages/sales/SalesOrderShipmentDetail.tsx:339
#: src/tables/sales/SalesOrderShipmentTable.tsx:65
msgid "Cancel Shipment"
msgstr "出荷のキャンセル"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:262
#: src/tables/sales/SalesOrderShipmentTable.tsx:81
#: src/tables/sales/SalesOrderShipmentTable.tsx:144
msgid "Complete Shipment"
msgstr "完全出荷"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:279
#: src/tables/part/PartPurchaseOrdersTable.tsx:122
msgid "Pending"
msgstr "処理待ち"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:285
#: src/tables/sales/SalesOrderShipmentTable.tsx:106
#: src/tables/sales/SalesOrderShipmentTable.tsx:190
msgid "Shipped"
msgstr "発送済み"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:291
#: src/tables/sales/SalesOrderShipmentTable.tsx:195
#: src/tables/settings/EmailTable.tsx:84
msgid "Delivered"
msgstr "配送済み"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:306
msgid "Send Shipment"
msgstr "発送"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:329
msgid "Shipment Actions"
msgstr "出荷アクション"

#: src/pages/stock/LocationDetail.tsx:112
msgid "Parent Location"
msgstr "親の位置"

#: src/pages/stock/LocationDetail.tsx:130
msgid "Sublocations"
msgstr "サブロケーション"

#: src/pages/stock/LocationDetail.tsx:148
#: src/tables/stock/StockLocationTable.tsx:57
msgid "Location Type"
msgstr "ロケーションタイプ"

#: src/pages/stock/LocationDetail.tsx:159
msgid "Top level stock location"
msgstr "トップレベルの在庫ロケーション"

#: src/pages/stock/LocationDetail.tsx:170
msgid "Location Details"
msgstr "場所の詳細"

#: src/pages/stock/LocationDetail.tsx:196
msgid "Default Parts"
msgstr "デフォルトパーツ"

#: src/pages/stock/LocationDetail.tsx:215
#: src/pages/stock/LocationDetail.tsx:374
#: src/tables/stock/StockLocationTable.tsx:121
msgid "Edit Stock Location"
msgstr "在庫場所の編集"

#: src/pages/stock/LocationDetail.tsx:224
msgid "Move items to parent location"
msgstr "アイテムを親の場所に移動"

#: src/pages/stock/LocationDetail.tsx:236
#: src/pages/stock/LocationDetail.tsx:379
msgid "Delete Stock Location"
msgstr "在庫場所の削除"

#: src/pages/stock/LocationDetail.tsx:239
msgid "Items Action"
msgstr "アクション"

#: src/pages/stock/LocationDetail.tsx:240
msgid "Action for stock items in this location"
msgstr "この場所にある在庫品に対する措置"

#: src/pages/stock/LocationDetail.tsx:245
msgid "Child Locations Action"
msgstr "チャイルド・ロケーション・アクション"

#: src/pages/stock/LocationDetail.tsx:246
msgid "Action for child locations in this location"
msgstr "この場所の子供のための行動"

#: src/pages/stock/LocationDetail.tsx:280
msgid "Scan Stock Item"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:298
#: src/pages/stock/StockDetail.tsx:783
msgid "Scanned stock item into location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:304
#: src/pages/stock/StockDetail.tsx:789
msgid "Error scanning stock item"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:311
msgid "Scan Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:323
msgid "Scanned stock location into location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:329
msgid "Error scanning stock location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:370
#: src/tables/stock/StockLocationTable.tsx:142
msgid "Location Actions"
msgstr "ロケーションアクション"

#: src/pages/stock/StockDetail.tsx:147
msgid "Base Part"
msgstr "ベース部"

#: src/pages/stock/StockDetail.tsx:155
#~ msgid "Link custom barcode to stock item"
#~ msgstr "Link custom barcode to stock item"

#: src/pages/stock/StockDetail.tsx:156
#~ msgid "Completed Tests"
#~ msgstr "Completed Tests"

#: src/pages/stock/StockDetail.tsx:161
#~ msgid "Unlink custom barcode from stock item"
#~ msgstr "Unlink custom barcode from stock item"

#: src/pages/stock/StockDetail.tsx:185
msgid "Last Stocktake"
msgstr "最後のストックテイク"

#: src/pages/stock/StockDetail.tsx:203
msgid "Previous serial number"
msgstr ""

#: src/pages/stock/StockDetail.tsx:205
#~ msgid "Edit stock item"
#~ msgstr "Edit stock item"

#: src/pages/stock/StockDetail.tsx:217
#~ msgid "Delete stock item"
#~ msgstr "Delete stock item"

#: src/pages/stock/StockDetail.tsx:225
msgid "Find serial number"
msgstr ""

#: src/pages/stock/StockDetail.tsx:269
msgid "Allocated to Orders"
msgstr "注文に割り当て"

#: src/pages/stock/StockDetail.tsx:302
msgid "Installed In"
msgstr "設置場所"

#: src/pages/stock/StockDetail.tsx:322
msgid "Parent Item"
msgstr "親アイテム"

#: src/pages/stock/StockDetail.tsx:326
msgid "Parent stock item"
msgstr "親株式"

#: src/pages/stock/StockDetail.tsx:332
msgid "Consumed By"
msgstr "消費者"

#: src/pages/stock/StockDetail.tsx:433
#~ msgid "Duplicate stock item"
#~ msgstr "Duplicate stock item"

#: src/pages/stock/StockDetail.tsx:510
msgid "Stock Details"
msgstr "在庫詳細"

#: src/pages/stock/StockDetail.tsx:516
msgid "Stock Tracking"
msgstr "在庫管理"

#: src/pages/stock/StockDetail.tsx:571
msgid "Test Data"
msgstr "テスト データ"

#: src/pages/stock/StockDetail.tsx:585
msgid "Installed Items"
msgstr "設置項目"

#: src/pages/stock/StockDetail.tsx:592
msgid "Child Items"
msgstr "子供用品"

#: src/pages/stock/StockDetail.tsx:645
msgid "Edit Stock Item"
msgstr "在庫商品を編集"

#: src/pages/stock/StockDetail.tsx:671
#: src/tables/stock/StockItemTable.tsx:452
#~ msgid "Add stock"
#~ msgstr "Add stock"

#: src/pages/stock/StockDetail.tsx:680
#: src/tables/stock/StockItemTable.tsx:461
#~ msgid "Remove stock"
#~ msgstr "Remove stock"

#: src/pages/stock/StockDetail.tsx:687
msgid "Items Created"
msgstr ""

#: src/pages/stock/StockDetail.tsx:688
msgid "Created {n} stock items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:698
#: src/tables/stock/StockItemTable.tsx:481
#~ msgid "Transfer stock"
#~ msgstr "Transfer stock"

#: src/pages/stock/StockDetail.tsx:705
msgid "Delete Stock Item"
msgstr "ストックアイテムの削除"

#: src/pages/stock/StockDetail.tsx:741
msgid "Serialize Stock Item"
msgstr "ストックアイテムのシリアル化"

#: src/pages/stock/StockDetail.tsx:757
#: src/tables/stock/StockItemTable.tsx:539
msgid "Stock item serialized"
msgstr "シリアル化された在庫品"

#: src/pages/stock/StockDetail.tsx:762
#~ msgid "Return Stock Item"
#~ msgstr "Return Stock Item"

#: src/pages/stock/StockDetail.tsx:765
msgid "Scan Into Location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:765
#~ msgid "Return this item into stock. This will remove the customer assignment."
#~ msgstr "Return this item into stock. This will remove the customer assignment."

#: src/pages/stock/StockDetail.tsx:777
#~ msgid "Item returned to stock"
#~ msgstr "Item returned to stock"

#: src/pages/stock/StockDetail.tsx:823
msgid "Scan into location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:825
msgid "Scan this item into a location"
msgstr ""

#: src/pages/stock/StockDetail.tsx:837
msgid "Stock Operations"
msgstr "株式運用"

#: src/pages/stock/StockDetail.tsx:842
#: src/tables/build/BuildOutputTable.tsx:532
msgid "Serialize"
msgstr "シリアライズ"

#: src/pages/stock/StockDetail.tsx:843
msgid "Serialize stock"
msgstr "在庫のシリアル化"

#: src/pages/stock/StockDetail.tsx:868
msgid "Stock Item Actions"
msgstr "在庫品アクション"

#: src/pages/stock/StockDetail.tsx:868
#~ msgid "Count stock"
#~ msgstr "Count stock"

#: src/pages/stock/StockDetail.tsx:890
#~ msgid "Return from customer"
#~ msgstr "Return from customer"

#: src/pages/stock/StockDetail.tsx:900
#~ msgid "Transfer"
#~ msgstr "Transfer"

#: src/pages/stock/StockDetail.tsx:937
#: src/tables/stock/StockItemTable.tsx:409
msgid "Stale"
msgstr "期限失効"

#: src/pages/stock/StockDetail.tsx:943
#: src/tables/stock/StockItemTable.tsx:403
msgid "Expired"
msgstr "期限切れ"

#: src/pages/stock/StockDetail.tsx:949
msgid "Unavailable"
msgstr "利用不可"

#: src/pages/stock/StockDetail.tsx:950
#~ msgid "Assign to Customer"
#~ msgstr "Assign to Customer"

#: src/pages/stock/StockDetail.tsx:951
#~ msgid "Assign to a customer"
#~ msgstr "Assign to a customer"

#: src/states/IconState.tsx:47
#: src/states/IconState.tsx:77
msgid "Error loading icon package from server"
msgstr "サーバーからのアイコンパッケージの読み込みエラー"

#: src/tables/ColumnRenderers.tsx:41
#~ msgid "Part is locked"
#~ msgstr "Part is locked"

#: src/tables/ColumnRenderers.tsx:59
msgid "Part is not active"
msgstr "パートはアクティブではありません"

#: src/tables/ColumnRenderers.tsx:64
#: src/tables/bom/BomTable.tsx:619
#: src/tables/part/PartParameterTable.tsx:235
#: src/tables/part/PartTestTemplateTable.tsx:258
msgid "Part is Locked"
msgstr "部品がロックされています"

#: src/tables/ColumnRenderers.tsx:69
msgid "You are subscribed to notifications for this part"
msgstr "このパートに関する通知を購読しています。"

#: src/tables/ColumnRenderers.tsx:93
#~ msgid "No location set"
#~ msgstr "No location set"

#: src/tables/ColumnSelect.tsx:16
#: src/tables/ColumnSelect.tsx:23
msgid "Select Columns"
msgstr "列の選択"

#: src/tables/DownloadAction.tsx:13
#~ msgid "Excel"
#~ msgstr "Excel"

#: src/tables/DownloadAction.tsx:21
#~ msgid "CSV"
#~ msgstr "CSV"

#: src/tables/DownloadAction.tsx:21
#~ msgid "Download selected data"
#~ msgstr "Download selected data"

#: src/tables/DownloadAction.tsx:22
#~ msgid "TSV"
#~ msgstr "TSV"

#: src/tables/DownloadAction.tsx:23
#~ msgid "Excel (.xlsx)"
#~ msgstr "Excel (.xlsx)"

#: src/tables/DownloadAction.tsx:24
#~ msgid "Excel (.xls)"
#~ msgstr "Excel (.xls)"

#: src/tables/DownloadAction.tsx:36
#~ msgid "Download Data"
#~ msgstr "Download Data"

#: src/tables/Filter.tsx:75
msgid "Has Batch Code"
msgstr "バッチコード"

#: src/tables/Filter.tsx:76
msgid "Show items which have a batch code"
msgstr "バッチコードを持つアイテムを表示"

#: src/tables/Filter.tsx:84
msgid "Filter items by batch code"
msgstr "バッチコードによるアイテムのフィルタリング"

#: src/tables/Filter.tsx:92
msgid "Is Serialized"
msgstr "連載中"

#: src/tables/Filter.tsx:93
msgid "Show items which have a serial number"
msgstr "シリアル番号のある商品を表示"

#: src/tables/Filter.tsx:100
msgid "Serial"
msgstr ""

#: src/tables/Filter.tsx:101
msgid "Filter items by serial number"
msgstr "シリアル番号で商品を絞り込む"

#: src/tables/Filter.tsx:106
#~ msgid "Show overdue orders"
#~ msgstr "Show overdue orders"

#: src/tables/Filter.tsx:109
msgid "Serial Below"
msgstr ""

#: src/tables/Filter.tsx:110
msgid "Show items with serial numbers less than or equal to a given value"
msgstr "指定された値以下のシリアル番号のアイテムを表示します。"

#: src/tables/Filter.tsx:118
msgid "Serial Above"
msgstr ""

#: src/tables/Filter.tsx:119
msgid "Show items with serial numbers greater than or equal to a given value"
msgstr "指定された値以上のシリアル番号を持つアイテムを表示します。"

#: src/tables/Filter.tsx:128
msgid "Assigned to me"
msgstr "担当"

#: src/tables/Filter.tsx:129
msgid "Show orders assigned to me"
msgstr "私に割り当てられた命令を表示"

#: src/tables/Filter.tsx:136
#: src/tables/sales/SalesOrderAllocationTable.tsx:85
msgid "Outstanding"
msgstr "並外れた"

#: src/tables/Filter.tsx:137
msgid "Show outstanding items"
msgstr "未処理項目の表示"

#: src/tables/Filter.tsx:145
msgid "Show overdue items"
msgstr "期限切れアイテムの表示"

#: src/tables/Filter.tsx:152
msgid "Minimum Date"
msgstr "最小の日付"

#: src/tables/Filter.tsx:153
msgid "Show items after this date"
msgstr "この日付以降のアイテムを表示"

#: src/tables/Filter.tsx:161
msgid "Maximum Date"
msgstr "最大日付"

#: src/tables/Filter.tsx:162
msgid "Show items before this date"
msgstr "この日以前の商品を表示"

#: src/tables/Filter.tsx:170
msgid "Created Before"
msgstr "作成前"

#: src/tables/Filter.tsx:171
msgid "Show items created before this date"
msgstr "この日付より前に作成されたアイテムを表示"

#: src/tables/Filter.tsx:179
msgid "Created After"
msgstr "の後に作成されました。"

#: src/tables/Filter.tsx:180
msgid "Show items created after this date"
msgstr "この日付以降に作成されたアイテムを表示"

#: src/tables/Filter.tsx:188
msgid "Start Date Before"
msgstr "開始日 前"

#: src/tables/Filter.tsx:189
msgid "Show items with a start date before this date"
msgstr "この日付より前の開始日のアイテムを表示"

#: src/tables/Filter.tsx:197
msgid "Start Date After"
msgstr "開始日 後"

#: src/tables/Filter.tsx:198
msgid "Show items with a start date after this date"
msgstr "この日付以降の開始日のアイテムを表示"

#: src/tables/Filter.tsx:206
msgid "Target Date Before"
msgstr "目標期日"

#: src/tables/Filter.tsx:207
msgid "Show items with a target date before this date"
msgstr "この日付より前の日付のアイテムを表示"

#: src/tables/Filter.tsx:215
msgid "Target Date After"
msgstr "以降の目標日"

#: src/tables/Filter.tsx:216
msgid "Show items with a target date after this date"
msgstr "この日以降に目標日が設定されたアイテムを表示"

#: src/tables/Filter.tsx:224
msgid "Completed Before"
msgstr "完成前"

#: src/tables/Filter.tsx:225
msgid "Show items completed before this date"
msgstr "この日までに完了した項目を表示"

#: src/tables/Filter.tsx:233
msgid "Completed After"
msgstr "終了後"

#: src/tables/Filter.tsx:234
msgid "Show items completed after this date"
msgstr "この日以降に完了した項目を表示"

#: src/tables/Filter.tsx:246
msgid "Has Project Code"
msgstr "プロジェクトコード"

#: src/tables/Filter.tsx:247
msgid "Show orders with an assigned project code"
msgstr "プロジェクトコードが割り当てられた注文の表示"

#: src/tables/Filter.tsx:256
msgid "Include Variants"
msgstr "バリアントを含む"

#: src/tables/Filter.tsx:257
msgid "Include results for part variants"
msgstr ""

#: src/tables/Filter.tsx:267
#: src/tables/part/PartPurchaseOrdersTable.tsx:133
msgid "Filter by order status"
msgstr "注文状況による絞り込み"

#: src/tables/Filter.tsx:279
msgid "Filter by project code"
msgstr "プロジェクトコードによるフィルタリング"

#: src/tables/Filter.tsx:312
msgid "Filter by responsible owner"
msgstr "責任所有者による絞り込み"

#: src/tables/Filter.tsx:328
#: src/tables/settings/ApiTokenTable.tsx:133
#: src/tables/stock/StockTrackingTable.tsx:191
msgid "Filter by user"
msgstr "レポートのフィルタリング"

#: src/tables/Filter.tsx:340
msgid "Filter by manufacturer"
msgstr ""

#: src/tables/Filter.tsx:353
msgid "Filter by supplier"
msgstr ""

#: src/tables/Filter.tsx:366
msgid "Filter by user who created the order"
msgstr "注文を作成したユーザーによるフィルタリング"

#: src/tables/Filter.tsx:374
msgid "Filter by user who issued the order"
msgstr "注文を発行したユーザーによるフィルタリング"

#: src/tables/Filter.tsx:382
msgid "Filter by part category"
msgstr "部品カテゴリーによる絞り込み"

#: src/tables/Filter.tsx:393
msgid "Filter by stock location"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:59
msgid "Remove filter"
msgstr "フィルタを削除"

#: src/tables/FilterSelectDrawer.tsx:102
#: src/tables/FilterSelectDrawer.tsx:104
#: src/tables/FilterSelectDrawer.tsx:151
msgid "Select filter value"
msgstr "フィルタの値を選択"

#: src/tables/FilterSelectDrawer.tsx:116
msgid "Enter filter value"
msgstr "フィルター値の入力"

#: src/tables/FilterSelectDrawer.tsx:138
msgid "Select date value"
msgstr "日付の値を選択"

#: src/tables/FilterSelectDrawer.tsx:260
msgid "Select filter"
msgstr "フィルタを選択"

#: src/tables/FilterSelectDrawer.tsx:261
msgid "Filter"
msgstr "フィルタ"

#: src/tables/FilterSelectDrawer.tsx:313
#: src/tables/InvenTreeTableHeader.tsx:257
msgid "Table Filters"
msgstr "テーブルフィルター"

#: src/tables/FilterSelectDrawer.tsx:346
msgid "Add Filter"
msgstr "フィルタを追加"

#: src/tables/FilterSelectDrawer.tsx:355
msgid "Clear Filters"
msgstr "絞り込み条件を解除する"

#: src/tables/InvenTreeTable.tsx:44
#: src/tables/InvenTreeTable.tsx:479
msgid "No records found"
msgstr "記録が見つかりません"

#: src/tables/InvenTreeTable.tsx:151
msgid "Error loading table options"
msgstr ""

#: src/tables/InvenTreeTable.tsx:250
#~ msgid "Failed to load table options"
#~ msgstr "Failed to load table options"

#: src/tables/InvenTreeTable.tsx:510
#~ msgid "Are you sure you want to delete the selected records?"
#~ msgstr "Are you sure you want to delete the selected records?"

#: src/tables/InvenTreeTable.tsx:520
msgid "Server returned incorrect data type"
msgstr "サーバーが不正なデータ型を返しました。"

#: src/tables/InvenTreeTable.tsx:535
#~ msgid "Deleted records"
#~ msgstr "Deleted records"

#: src/tables/InvenTreeTable.tsx:536
#~ msgid "Records were deleted successfully"
#~ msgstr "Records were deleted successfully"

#: src/tables/InvenTreeTable.tsx:545
#~ msgid "Failed to delete records"
#~ msgstr "Failed to delete records"

#: src/tables/InvenTreeTable.tsx:552
#~ msgid "This action cannot be undone!"
#~ msgstr "This action cannot be undone!"

#: src/tables/InvenTreeTable.tsx:553
msgid "Error loading table data"
msgstr ""

#: src/tables/InvenTreeTable.tsx:594
#: src/tables/InvenTreeTable.tsx:595
#~ msgid "Print actions"
#~ msgstr "Print actions"

#: src/tables/InvenTreeTable.tsx:655
#: src/tables/InvenTreeTable.tsx:656
#~ msgid "Barcode actions"
#~ msgstr "Barcode actions"

#: src/tables/InvenTreeTable.tsx:680
msgid "View details"
msgstr "詳細を見る"

#: src/tables/InvenTreeTable.tsx:712
#~ msgid "Table filters"
#~ msgstr "Table filters"

#: src/tables/InvenTreeTable.tsx:725
#~ msgid "Clear custom query filters"
#~ msgstr "Clear custom query filters"

#: src/tables/InvenTreeTableHeader.tsx:104
msgid "Delete Selected Items"
msgstr "選択したアイテムを削除"

#: src/tables/InvenTreeTableHeader.tsx:108
msgid "Are you sure you want to delete the selected items?"
msgstr "選択したアイテムを削除しますか?"

#: src/tables/InvenTreeTableHeader.tsx:110
#: src/tables/plugin/PluginListTable.tsx:316
msgid "This action cannot be undone"
msgstr "この操作は元に戻せません。"

#: src/tables/InvenTreeTableHeader.tsx:121
msgid "Items deleted"
msgstr "削除されたアイテム"

#: src/tables/InvenTreeTableHeader.tsx:126
msgid "Failed to delete items"
msgstr "アイテムの削除に失敗しました"

#: src/tables/InvenTreeTableHeader.tsx:177
msgid "Custom table filters are active"
msgstr "カスタムテーブルフィルターが有効"

#: src/tables/InvenTreeTableHeader.tsx:203
#: src/tables/general/BarcodeScanTable.tsx:93
msgid "Delete selected records"
msgstr "選択したレコードの削除"

#: src/tables/InvenTreeTableHeader.tsx:223
msgid "Refresh data"
msgstr "データを更新する"

#: src/tables/InvenTreeTableHeader.tsx:269
msgid "Active Filters"
msgstr ""

#: src/tables/TableHoverCard.tsx:35
#~ msgid "item-{idx}"
#~ msgstr "item-{idx}"

#: src/tables/UploadAction.tsx:7
#~ msgid "Upload Data"
#~ msgstr "Upload Data"

#: src/tables/bom/BomTable.tsx:102
msgid "This BOM item is defined for a different parent"
msgstr "このBOMアイテムは、別の親に定義されています。"

#: src/tables/bom/BomTable.tsx:118
msgid "Part Information"
msgstr "部品情報"

#: src/tables/bom/BomTable.tsx:121
msgid "This BOM item has not been validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:228
msgid "Substitutes"
msgstr ""

#: src/tables/bom/BomTable.tsx:297
#: src/tables/build/BuildLineTable.tsx:268
#: src/tables/part/PartTable.tsx:132
msgid "External stock"
msgstr "外部在庫"

#: src/tables/bom/BomTable.tsx:301
#~ msgid "Create BOM Item"
#~ msgstr "Create BOM Item"

#: src/tables/bom/BomTable.tsx:305
#: src/tables/build/BuildLineTable.tsx:231
msgid "Includes substitute stock"
msgstr "代用株を含む"

#: src/tables/bom/BomTable.tsx:310
#~ msgid "Show asssmbled items"
#~ msgstr "Show asssmbled items"

#: src/tables/bom/BomTable.tsx:314
#: src/tables/build/BuildLineTable.tsx:241
#: src/tables/sales/SalesOrderLineItemTable.tsx:158
msgid "Includes variant stock"
msgstr "バリアントストック付き"

#: src/tables/bom/BomTable.tsx:331
#: src/tables/part/PartTable.tsx:101
msgid "Building"
msgstr "建物"

#: src/tables/bom/BomTable.tsx:331
#~ msgid "Edit Bom Item"
#~ msgstr "Edit Bom Item"

#: src/tables/bom/BomTable.tsx:333
#~ msgid "Bom item updated"
#~ msgstr "Bom item updated"

#: src/tables/bom/BomTable.tsx:340
#: src/tables/part/PartTable.tsx:158
#: src/tables/sales/SalesOrderLineItemTable.tsx:181
#: src/tables/stock/StockItemTable.tsx:223
msgid "Stock Information"
msgstr "株式情報"

#: src/tables/bom/BomTable.tsx:348
#~ msgid "Delete Bom Item"
#~ msgstr "Delete Bom Item"

#: src/tables/bom/BomTable.tsx:349
#~ msgid "Bom item deleted"
#~ msgstr "Bom item deleted"

#: src/tables/bom/BomTable.tsx:351
#~ msgid "Are you sure you want to remove this BOM item?"
#~ msgstr "Are you sure you want to remove this BOM item?"

#: src/tables/bom/BomTable.tsx:354
#~ msgid "Validate BOM line"
#~ msgstr "Validate BOM line"

#: src/tables/bom/BomTable.tsx:374
#: src/tables/build/BuildLineTable.tsx:478
#: src/tables/build/BuildLineTable.tsx:518
msgid "Consumable item"
msgstr "消耗品"

#: src/tables/bom/BomTable.tsx:377
msgid "No available stock"
msgstr "在庫なし"

#: src/tables/bom/BomTable.tsx:395
#: src/tables/build/BuildLineTable.tsx:211
msgid "Show testable items"
msgstr "テスト可能な項目を表示"

#: src/tables/bom/BomTable.tsx:400
msgid "Show trackable items"
msgstr "追跡可能なアイテムの表示"

#: src/tables/bom/BomTable.tsx:405
#: src/tables/build/BuildLineTable.tsx:206
msgid "Show assembled items"
msgstr "組み立てられた商品を表示"

#: src/tables/bom/BomTable.tsx:410
#: src/tables/build/BuildLineTable.tsx:191
msgid "Show items with available stock"
msgstr "在庫のある商品を表示"

#: src/tables/bom/BomTable.tsx:415
msgid "Show items on order"
msgstr "注文商品の表示"

#: src/tables/bom/BomTable.tsx:419
msgid "Validated"
msgstr "検証済み"

#: src/tables/bom/BomTable.tsx:420
msgid "Show validated items"
msgstr "有効な項目を表示"

#: src/tables/bom/BomTable.tsx:424
#: src/tables/bom/UsedInTable.tsx:80
msgid "Inherited"
msgstr "継承"

#: src/tables/bom/BomTable.tsx:425
#: src/tables/bom/UsedInTable.tsx:81
msgid "Show inherited items"
msgstr "継承された項目を表示"

#: src/tables/bom/BomTable.tsx:429
msgid "Allow Variants"
msgstr "バリアントを許可"

#: src/tables/bom/BomTable.tsx:430
msgid "Show items which allow variant substitution"
msgstr "バリアント置換が可能な項目を表示"

#: src/tables/bom/BomTable.tsx:434
#: src/tables/bom/UsedInTable.tsx:85
#: src/tables/build/BuildLineTable.tsx:200
msgid "Optional"
msgstr "オプション"

#: src/tables/bom/BomTable.tsx:435
#: src/tables/bom/UsedInTable.tsx:86
msgid "Show optional items"
msgstr "オプション項目の表示"

#: src/tables/bom/BomTable.tsx:439
#: src/tables/build/BuildLineTable.tsx:195
msgid "Consumable"
msgstr "消耗品"

#: src/tables/bom/BomTable.tsx:440
msgid "Show consumable items"
msgstr "消耗品の表示"

#: src/tables/bom/BomTable.tsx:444
#: src/tables/part/PartTable.tsx:300
msgid "Has Pricing"
msgstr "価格"

#: src/tables/bom/BomTable.tsx:445
msgid "Show items with pricing"
msgstr "価格表示アイテム"

#: src/tables/bom/BomTable.tsx:467
#: src/tables/bom/BomTable.tsx:596
msgid "Import BOM Data"
msgstr "BOMデータのインポート"

#: src/tables/bom/BomTable.tsx:477
#: src/tables/bom/BomTable.tsx:603
msgid "Add BOM Item"
msgstr "BOMアイテムの追加"

#: src/tables/bom/BomTable.tsx:482
msgid "BOM item created"
msgstr "BOMアイテムの作成"

#: src/tables/bom/BomTable.tsx:489
msgid "Edit BOM Item"
msgstr "BOMアイテムの編集"

#: src/tables/bom/BomTable.tsx:491
msgid "BOM item updated"
msgstr "BOMアイテム更新"

#: src/tables/bom/BomTable.tsx:498
msgid "Delete BOM Item"
msgstr "BOMアイテムの削除"

#: src/tables/bom/BomTable.tsx:499
msgid "BOM item deleted"
msgstr "BOMアイテム削除"

#: src/tables/bom/BomTable.tsx:519
msgid "BOM item validated"
msgstr "BOMアイテムの検証"

#: src/tables/bom/BomTable.tsx:528
msgid "Failed to validate BOM item"
msgstr "BOMアイテムの検証に失敗しました"

#: src/tables/bom/BomTable.tsx:540
msgid "View BOM"
msgstr "BOMを見る"

#: src/tables/bom/BomTable.tsx:551
msgid "Validate BOM Line"
msgstr "BOMラインの検証"

#: src/tables/bom/BomTable.tsx:570
msgid "Edit Substitutes"
msgstr "代理編集"

#: src/tables/bom/BomTable.tsx:624
msgid "Bill of materials cannot be edited, as the part is locked"
msgstr "部品がロックされているため、部品表を編集できません。"

#: src/tables/bom/UsedInTable.tsx:34
#: src/tables/build/BuildLineTable.tsx:205
#: src/tables/part/ParametricPartTable.tsx:360
#: src/tables/part/PartBuildAllocationsTable.tsx:60
#: src/tables/part/PartTable.tsx:196
#: src/tables/stock/StockItemTable.tsx:334
msgid "Assembly"
msgstr "アセンブリ"

#: src/tables/bom/UsedInTable.tsx:91
msgid "Show active assemblies"
msgstr "アクティブなアセンブリの表示"

#: src/tables/bom/UsedInTable.tsx:95
#: src/tables/part/PartTable.tsx:226
#: src/tables/part/PartVariantTable.tsx:30
msgid "Trackable"
msgstr "追跡可能"

#: src/tables/bom/UsedInTable.tsx:96
msgid "Show trackable assemblies"
msgstr "追跡可能なアセンブリの表示"

#: src/tables/build/BuildAllocatedStockTable.tsx:67
msgid "Allocated to Output"
msgstr "出力への割り当て"

#: src/tables/build/BuildAllocatedStockTable.tsx:68
msgid "Show items allocated to a build output"
msgstr "ビルド出力に割り当てられた項目を表示"

#: src/tables/build/BuildAllocatedStockTable.tsx:73
#: src/tables/build/BuildOrderTable.tsx:197
#: src/tables/part/PartPurchaseOrdersTable.tsx:140
#: src/tables/sales/ReturnOrderTable.tsx:100
#: src/tables/sales/SalesOrderAllocationTable.tsx:101
#: src/tables/sales/SalesOrderTable.tsx:101
#~ msgid "Include orders for part variants"
#~ msgstr "Include orders for part variants"

#: src/tables/build/BuildAllocatedStockTable.tsx:97
#: src/tables/part/PartBuildAllocationsTable.tsx:84
#: src/tables/part/PartPurchaseOrdersTable.tsx:132
#: src/tables/part/PartSalesAllocationsTable.tsx:69
#: src/tables/sales/SalesOrderAllocationTable.tsx:119
msgid "Order Status"
msgstr "注文ステータス"

#: src/tables/build/BuildAllocatedStockTable.tsx:164
#~ msgid "Edit Build Item"
#~ msgstr "Edit Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:167
#: src/tables/build/BuildLineTable.tsx:631
msgid "Edit Stock Allocation"
msgstr "株式配分の編集"

#: src/tables/build/BuildAllocatedStockTable.tsx:174
#~ msgid "Delete Build Item"
#~ msgstr "Delete Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:180
#: src/tables/build/BuildLineTable.tsx:644
msgid "Delete Stock Allocation"
msgstr "株式割当の削除"

#: src/tables/build/BuildAllocatedStockTable.tsx:232
msgid "Consume"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:59
#~ msgid "Show lines with available stock"
#~ msgstr "Show lines with available stock"

#: src/tables/build/BuildLineTable.tsx:106
msgid "View Stock Item"
msgstr "在庫を見る"

#: src/tables/build/BuildLineTable.tsx:181
msgid "Show fully allocated lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:186
msgid "Show fully consumed lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:189
#~ msgid "Show allocated lines"
#~ msgstr "Show allocated lines"

#: src/tables/build/BuildLineTable.tsx:196
msgid "Show consumable lines"
msgstr "消耗品ラインの表示"

#: src/tables/build/BuildLineTable.tsx:201
msgid "Show optional lines"
msgstr "オプションラインの表示"

#: src/tables/build/BuildLineTable.tsx:210
#: src/tables/part/PartTable.tsx:220
msgid "Testable"
msgstr "テスト可能"

#: src/tables/build/BuildLineTable.tsx:215
#: src/tables/stock/StockItemTable.tsx:393
msgid "Tracked"
msgstr "追跡"

#: src/tables/build/BuildLineTable.tsx:216
msgid "Show tracked lines"
msgstr "トラッキングラインの表示"

#: src/tables/build/BuildLineTable.tsx:250
#: src/tables/sales/SalesOrderLineItemTable.tsx:164
msgid "In production"
msgstr "生産中"

#: src/tables/build/BuildLineTable.tsx:278
msgid "Insufficient stock"
msgstr "在庫不足"

#: src/tables/build/BuildLineTable.tsx:294
#: src/tables/sales/SalesOrderLineItemTable.tsx:152
#: src/tables/stock/StockItemTable.tsx:192
msgid "No stock available"
msgstr "在庫なし"

#: src/tables/build/BuildLineTable.tsx:360
msgid "Gets Inherited"
msgstr "継承"

#: src/tables/build/BuildLineTable.tsx:373
msgid "Unit Quantity"
msgstr "単位 数量"

#: src/tables/build/BuildLineTable.tsx:389
msgid "Required Quantity"
msgstr "必要数量"

#: src/tables/build/BuildLineTable.tsx:400
msgid "Setup Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:409
msgid "Attrition"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:417
msgid "Rounding Multiple"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:426
msgid "BOM Information"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:496
#: src/tables/part/PartBuildAllocationsTable.tsx:102
msgid "Fully allocated"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:544
#: src/tables/sales/SalesOrderLineItemTable.tsx:290
msgid "Create Build Order"
msgstr "ビルドオーダーの作成"

#: src/tables/build/BuildLineTable.tsx:573
msgid "Auto allocation in progress"
msgstr "自動割り当て中"

#: src/tables/build/BuildLineTable.tsx:576
#: src/tables/build/BuildLineTable.tsx:781
msgid "Auto Allocate Stock"
msgstr "在庫の自動割り当て"

#: src/tables/build/BuildLineTable.tsx:577
msgid "Automatically allocate stock to this build according to the selected options"
msgstr "選択されたオプションに従って、このビルドに在庫を自動的に割り当てます。"

#: src/tables/build/BuildLineTable.tsx:597
#: src/tables/build/BuildLineTable.tsx:611
#: src/tables/build/BuildLineTable.tsx:730
#: src/tables/build/BuildLineTable.tsx:831
#: src/tables/build/BuildOutputTable.tsx:359
#: src/tables/build/BuildOutputTable.tsx:364
msgid "Deallocate Stock"
msgstr "在庫処分"

#: src/tables/build/BuildLineTable.tsx:613
msgid "Deallocate all untracked stock for this build order"
msgstr "このビルドオーダーのすべての未引当在庫を割り当て解除します。"

#: src/tables/build/BuildLineTable.tsx:615
msgid "Deallocate stock from the selected line item"
msgstr "選択された品目から在庫を配分解除"

#: src/tables/build/BuildLineTable.tsx:619
msgid "Stock has been deallocated"
msgstr "在庫の配分が終了しました"

#: src/tables/build/BuildLineTable.tsx:750
msgid "Build Stock"
msgstr "ビルドストック"

#: src/tables/build/BuildLineTable.tsx:763
#: src/tables/sales/SalesOrderLineItemTable.tsx:377
msgid "View Part"
msgstr "部品を見る"

#: src/tables/build/BuildOrderTable.tsx:116
#~ msgid "Cascade"
#~ msgstr "Cascade"

#: src/tables/build/BuildOrderTable.tsx:117
#~ msgid "Display recursive child orders"
#~ msgstr "Display recursive child orders"

#: src/tables/build/BuildOrderTable.tsx:121
#~ msgid "Show active orders"
#~ msgstr "Show active orders"

#: src/tables/build/BuildOrderTable.tsx:122
#~ msgid "Show overdue status"
#~ msgstr "Show overdue status"

#: src/tables/build/BuildOrderTable.tsx:127
#~ msgid "Show outstanding orders"
#~ msgstr "Show outstanding orders"

#: src/tables/build/BuildOrderTable.tsx:139
#: src/tables/purchasing/PurchaseOrderTable.tsx:71
#: src/tables/sales/ReturnOrderTable.tsx:62
#: src/tables/sales/SalesOrderTable.tsx:69
#~ msgid "Filter by whether the purchase order has a project code"
#~ msgstr "Filter by whether the purchase order has a project code"

#: src/tables/build/BuildOrderTable.tsx:167
#: src/tables/purchasing/PurchaseOrderTable.tsx:83
#: src/tables/sales/ReturnOrderTable.tsx:79
#: src/tables/sales/SalesOrderTable.tsx:80
msgid "Has Target Date"
msgstr "目標期日あり"

#: src/tables/build/BuildOrderTable.tsx:168
#: src/tables/purchasing/PurchaseOrderTable.tsx:84
#: src/tables/sales/ReturnOrderTable.tsx:80
#: src/tables/sales/SalesOrderTable.tsx:81
msgid "Show orders with a target date"
msgstr "目標期日を指定した注文の表示"

#: src/tables/build/BuildOrderTable.tsx:173
#: src/tables/purchasing/PurchaseOrderTable.tsx:89
#: src/tables/sales/ReturnOrderTable.tsx:85
#: src/tables/sales/SalesOrderTable.tsx:86
msgid "Has Start Date"
msgstr "開始日あり"

#: src/tables/build/BuildOrderTable.tsx:174
#: src/tables/purchasing/PurchaseOrderTable.tsx:90
#: src/tables/sales/ReturnOrderTable.tsx:86
#: src/tables/sales/SalesOrderTable.tsx:87
msgid "Show orders with a start date"
msgstr "開始日を指定した注文の表示"

#: src/tables/build/BuildOrderTable.tsx:179
#~ msgid "Filter by user who issued this order"
#~ msgstr "Filter by user who issued this order"

#: src/tables/build/BuildOrderTestTable.tsx:85
#: src/tables/build/BuildOrderTestTable.tsx:163
#: src/tables/build/BuildOrderTestTable.tsx:283
#: src/tables/build/BuildOrderTestTable.tsx:297
#: src/tables/stock/StockItemTestResultTable.tsx:293
#: src/tables/stock/StockItemTestResultTable.tsx:365
#: src/tables/stock/StockItemTestResultTable.tsx:426
msgid "Add Test Result"
msgstr "テスト結果の追加"

#: src/tables/build/BuildOrderTestTable.tsx:92
#: src/tables/stock/StockItemTestResultTable.tsx:295
msgid "Test result added"
msgstr "テスト結果追加"

#: src/tables/build/BuildOrderTestTable.tsx:124
msgid "Add Test Results"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:134
msgid "Test results added"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:162
#: src/tables/stock/StockItemTestResultTable.tsx:191
msgid "No Result"
msgstr "結果なし"

#: src/tables/build/BuildOrderTestTable.tsx:274
msgid "Show build outputs currently in production"
msgstr "現在生産中のビルド出力を表示"

#: src/tables/build/BuildOutputTable.tsx:104
msgid "Build Output Stock Allocation"
msgstr "生産量ストック配分"

#: src/tables/build/BuildOutputTable.tsx:161
#~ msgid "Delete build output"
#~ msgstr "Delete build output"

#: src/tables/build/BuildOutputTable.tsx:294
#: src/tables/build/BuildOutputTable.tsx:479
msgid "Add Build Output"
msgstr "ビルド出力の追加"

#: src/tables/build/BuildOutputTable.tsx:297
msgid "Build output created"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:304
#~ msgid "Edit build output"
#~ msgstr "Edit build output"

#: src/tables/build/BuildOutputTable.tsx:350
#: src/tables/build/BuildOutputTable.tsx:553
msgid "Edit Build Output"
msgstr "ビルド出力の編集"

#: src/tables/build/BuildOutputTable.tsx:366
msgid "This action will deallocate all stock from the selected build output"
msgstr "このアクションは、選択されたビルド出力からすべてのストックを割り当て解除します。"

#: src/tables/build/BuildOutputTable.tsx:391
msgid "Serialize Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:409
#: src/tables/stock/StockItemTable.tsx:329
msgid "Filter by stock status"
msgstr "在庫状況で絞り込む"

#: src/tables/build/BuildOutputTable.tsx:446
msgid "Complete selected outputs"
msgstr "選択された出力の完了"

#: src/tables/build/BuildOutputTable.tsx:457
msgid "Scrap selected outputs"
msgstr "選択した出力のスクラップ"

#: src/tables/build/BuildOutputTable.tsx:468
msgid "Cancel selected outputs"
msgstr "選択した出力のキャンセル"

#: src/tables/build/BuildOutputTable.tsx:498
msgid "View Build Output"
msgstr "ビルド出力の表示"

#: src/tables/build/BuildOutputTable.tsx:504
msgid "Allocate"
msgstr "割り当て"

#: src/tables/build/BuildOutputTable.tsx:505
msgid "Allocate stock to build output"
msgstr "生産量を増やすための在庫配分"

#: src/tables/build/BuildOutputTable.tsx:518
msgid "Deallocate"
msgstr "デアロケート"

#: src/tables/build/BuildOutputTable.tsx:519
msgid "Deallocate stock from build output"
msgstr "ビルド出力から在庫を割り当て解除"

#: src/tables/build/BuildOutputTable.tsx:533
msgid "Serialize build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:544
msgid "Complete build output"
msgstr "完全なビルド出力"

#: src/tables/build/BuildOutputTable.tsx:560
msgid "Scrap"
msgstr "スクラップ"

#: src/tables/build/BuildOutputTable.tsx:561
msgid "Scrap build output"
msgstr "スクラップビルド出力"

#: src/tables/build/BuildOutputTable.tsx:571
msgid "Cancel build output"
msgstr "ビルド出力のキャンセル"

#: src/tables/build/BuildOutputTable.tsx:620
msgid "Allocated Lines"
msgstr "割り当てライン"

#: src/tables/build/BuildOutputTable.tsx:635
msgid "Required Tests"
msgstr "必須試験"

#: src/tables/build/BuildOutputTable.tsx:710
msgid "External Build"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:712
msgid "This build order is fulfilled by an external purchase order"
msgstr ""

#: src/tables/company/AddressTable.tsx:122
#: src/tables/company/AddressTable.tsx:187
msgid "Add Address"
msgstr "アドレスを追加"

#: src/tables/company/AddressTable.tsx:127
msgid "Address created"
msgstr "アドレス作成"

#: src/tables/company/AddressTable.tsx:136
msgid "Edit Address"
msgstr "住所変更"

#: src/tables/company/AddressTable.tsx:144
msgid "Delete Address"
msgstr "アドレス削除"

#: src/tables/company/AddressTable.tsx:145
msgid "Are you sure you want to delete this address?"
msgstr "本当にこのアドレスを削除しますか？"

#: src/tables/company/CompanyTable.tsx:70
#: src/tables/company/CompanyTable.tsx:120
msgid "Add Company"
msgstr "会社を追加する"

#: src/tables/company/CompanyTable.tsx:71
#~ msgid "New Company"
#~ msgstr "New Company"

#: src/tables/company/CompanyTable.tsx:92
msgid "Show active companies"
msgstr "活動中の企業を表示"

#: src/tables/company/CompanyTable.tsx:97
msgid "Show companies which are suppliers"
msgstr "サプライヤー企業の表示"

#: src/tables/company/CompanyTable.tsx:102
msgid "Show companies which are manufacturers"
msgstr "メーカーを表示"

#: src/tables/company/CompanyTable.tsx:107
msgid "Show companies which are customers"
msgstr "顧客企業の表示"

#: src/tables/company/ContactTable.tsx:99
msgid "Edit Contact"
msgstr "連絡先の編集"

#: src/tables/company/ContactTable.tsx:106
msgid "Add Contact"
msgstr "担当者を追加"

#: src/tables/company/ContactTable.tsx:117
msgid "Delete Contact"
msgstr "連絡先の削除"

#: src/tables/company/ContactTable.tsx:158
msgid "Add contact"
msgstr "担当者を追加"

#: src/tables/general/AttachmentTable.tsx:108
msgid "Uploading file {filename}"
msgstr "アップロード中のファイル {filename}"

#: src/tables/general/AttachmentTable.tsx:139
#~ msgid "File uploaded"
#~ msgstr "File uploaded"

#: src/tables/general/AttachmentTable.tsx:140
#~ msgid "File {0} uploaded successfully"
#~ msgstr "File {0} uploaded successfully"

#: src/tables/general/AttachmentTable.tsx:160
#: src/tables/general/AttachmentTable.tsx:174
msgid "Uploading File"
msgstr "ファイルをアップロード中"

#: src/tables/general/AttachmentTable.tsx:185
msgid "File Uploaded"
msgstr "ファイルアップロード完了"

#: src/tables/general/AttachmentTable.tsx:186
msgid "File {name} uploaded successfully"
msgstr "ファイル{name}が正常にアップロードされました"

#: src/tables/general/AttachmentTable.tsx:202
msgid "File could not be uploaded"
msgstr "ファイルをアップロードできませんでした。"

#: src/tables/general/AttachmentTable.tsx:253
msgid "Upload Attachment"
msgstr "添付ファイルのアップロード"

#: src/tables/general/AttachmentTable.tsx:254
#~ msgid "Upload attachment"
#~ msgstr "Upload attachment"

#: src/tables/general/AttachmentTable.tsx:263
msgid "Edit Attachment"
msgstr "添付ファイルの編集"

#: src/tables/general/AttachmentTable.tsx:277
msgid "Delete Attachment"
msgstr "添付ファイルを削除"

#: src/tables/general/AttachmentTable.tsx:287
msgid "Is Link"
msgstr "リンク"

#: src/tables/general/AttachmentTable.tsx:288
msgid "Show link attachments"
msgstr "リンクの添付ファイルを表示"

#: src/tables/general/AttachmentTable.tsx:292
msgid "Is File"
msgstr "ファイル"

#: src/tables/general/AttachmentTable.tsx:293
msgid "Show file attachments"
msgstr "添付ファイルの表示"

#: src/tables/general/AttachmentTable.tsx:302
msgid "Add attachment"
msgstr "添付ファイルを追加"

#: src/tables/general/AttachmentTable.tsx:313
msgid "Add external link"
msgstr "外部リンクの追加"

#: src/tables/general/AttachmentTable.tsx:361
msgid "No attachments found"
msgstr "%s 孤立した添付ファイルが見つかりました"

#: src/tables/general/AttachmentTable.tsx:400
msgid "Drag attachment file here to upload"
msgstr "添付ファイルをここにドラッグしてアップロードしてください。"

#: src/tables/general/BarcodeScanTable.tsx:35
msgid "Item"
msgstr "アイテム"

#: src/tables/general/BarcodeScanTable.tsx:50
msgid "Model"
msgstr "モデル"

#: src/tables/general/BarcodeScanTable.tsx:60
#: src/tables/settings/BarcodeScanHistoryTable.tsx:75
#: src/tables/settings/EmailTable.tsx:105
#: src/tables/settings/ErrorTable.tsx:59
msgid "Timestamp"
msgstr "タイムスタンプ"

#: src/tables/general/BarcodeScanTable.tsx:75
msgid "View Item"
msgstr "アイテムを見る"

#: src/tables/general/ExtraLineItemTable.tsx:91
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:288
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:397
#: src/tables/sales/ReturnOrderLineItemTable.tsx:80
#: src/tables/sales/ReturnOrderLineItemTable.tsx:183
#: src/tables/sales/SalesOrderLineItemTable.tsx:231
#: src/tables/sales/SalesOrderLineItemTable.tsx:334
msgid "Add Line Item"
msgstr "項目追加"

#: src/tables/general/ExtraLineItemTable.tsx:104
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:309
#: src/tables/sales/ReturnOrderLineItemTable.tsx:93
#: src/tables/sales/SalesOrderLineItemTable.tsx:250
msgid "Edit Line Item"
msgstr "ラインアイテムの編集"

#: src/tables/general/ExtraLineItemTable.tsx:113
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:318
#: src/tables/sales/ReturnOrderLineItemTable.tsx:102
#: src/tables/sales/SalesOrderLineItemTable.tsx:259
msgid "Delete Line Item"
msgstr "行削除"

#: src/tables/general/ExtraLineItemTable.tsx:151
msgid "Add Extra Line Item"
msgstr "追加項目"

#: src/tables/machine/MachineListTable.tsx:206
msgid "Machine restarted"
msgstr "マシン再起動"

#: src/tables/machine/MachineListTable.tsx:216
#: src/tables/machine/MachineListTable.tsx:264
msgid "Edit machine"
msgstr "編集機"

#: src/tables/machine/MachineListTable.tsx:230
#: src/tables/machine/MachineListTable.tsx:268
msgid "Delete machine"
msgstr "マシンの削除"

#: src/tables/machine/MachineListTable.tsx:231
msgid "Machine successfully deleted."
msgstr "マシンは正常に削除されました。"

#. placeholder {0}: machine?.name ?? 'unknown'
#: src/tables/machine/MachineListTable.tsx:235
msgid "Are you sure you want to remove the machine \"{0}\"?"
msgstr "マシン「{0}」を削除しますか？"

#: src/tables/machine/MachineListTable.tsx:252
msgid "Machine"
msgstr "マシン"

#: src/tables/machine/MachineListTable.tsx:257
#: src/tables/machine/MachineListTable.tsx:444
msgid "Restart required"
msgstr "再起動が必要"

#: src/tables/machine/MachineListTable.tsx:261
msgid "Machine Actions"
msgstr "マシンアクション"

#: src/tables/machine/MachineListTable.tsx:273
msgid "Restart"
msgstr "再実行"

#: src/tables/machine/MachineListTable.tsx:275
msgid "Restart machine"
msgstr "マシンの再起動"

#: src/tables/machine/MachineListTable.tsx:277
msgid "manual restart required"
msgstr "手動再起動が必要"

#: src/tables/machine/MachineListTable.tsx:291
#~ msgid "Machine information"
#~ msgstr "Machine information"

#: src/tables/machine/MachineListTable.tsx:295
msgid "Machine Information"
msgstr "機械情報"

#: src/tables/machine/MachineListTable.tsx:305
#: src/tables/machine/MachineListTable.tsx:611
msgid "Machine Type"
msgstr "機種"

#: src/tables/machine/MachineListTable.tsx:318
msgid "Machine Driver"
msgstr "マシンドライバー"

#: src/tables/machine/MachineListTable.tsx:333
msgid "Initialized"
msgstr "初期化"

#: src/tables/machine/MachineListTable.tsx:362
#: src/tables/machine/MachineTypeTable.tsx:289
msgid "No errors reported"
msgstr "エラーなし"

#: src/tables/machine/MachineListTable.tsx:381
msgid "Machine Settings"
msgstr "マシン設定"

#: src/tables/machine/MachineListTable.tsx:397
msgid "Driver Settings"
msgstr "ドライバーの設定"

#: src/tables/machine/MachineListTable.tsx:494
#~ msgid "Create machine"
#~ msgstr "Create machine"

#: src/tables/machine/MachineListTable.tsx:517
msgid "Add Machine"
msgstr "マシン追加"

#: src/tables/machine/MachineListTable.tsx:559
msgid "Add machine"
msgstr "マシン追加"

#: src/tables/machine/MachineListTable.tsx:561
#~ msgid "Machine detail"
#~ msgstr "Machine detail"

#: src/tables/machine/MachineListTable.tsx:573
msgid "Machine Detail"
msgstr "マシン詳細"

#: src/tables/machine/MachineListTable.tsx:620
msgid "Driver"
msgstr "ドライバー"

#: src/tables/machine/MachineTypeTable.tsx:77
msgid "Builtin driver"
msgstr "内蔵ドライバー"

#: src/tables/machine/MachineTypeTable.tsx:95
msgid "Not Found"
msgstr "みつかりません"

#: src/tables/machine/MachineTypeTable.tsx:98
msgid "Machine type not found."
msgstr "マシンタイプが見つかりません。"

#: src/tables/machine/MachineTypeTable.tsx:99
#~ msgid "Machine type information"
#~ msgstr "Machine type information"

#: src/tables/machine/MachineTypeTable.tsx:108
msgid "Machine Type Information"
msgstr "マシンタイプ情報"

#: src/tables/machine/MachineTypeTable.tsx:123
#: src/tables/machine/MachineTypeTable.tsx:237
msgid "Slug"
msgstr "スラッグ"

#: src/tables/machine/MachineTypeTable.tsx:134
#: src/tables/machine/MachineTypeTable.tsx:258
msgid "Provider plugin"
msgstr "プロバイダプラグイン"

#: src/tables/machine/MachineTypeTable.tsx:146
#: src/tables/machine/MachineTypeTable.tsx:270
msgid "Provider file"
msgstr "プロバイダーファイル"

#: src/tables/machine/MachineTypeTable.tsx:148
#~ msgid "Available drivers"
#~ msgstr "Available drivers"

#: src/tables/machine/MachineTypeTable.tsx:161
msgid "Available Drivers"
msgstr "利用可能なドライバー"

#: src/tables/machine/MachineTypeTable.tsx:216
msgid "Machine driver not found."
msgstr "マシンドライバーが見つかりません。"

#: src/tables/machine/MachineTypeTable.tsx:224
msgid "Machine driver information"
msgstr "マシンドライバー情報"

#: src/tables/machine/MachineTypeTable.tsx:244
msgid "Machine type"
msgstr "マシンタイプ"

#: src/tables/machine/MachineTypeTable.tsx:338
#~ msgid "Machine type detail"
#~ msgstr "Machine type detail"

#: src/tables/machine/MachineTypeTable.tsx:344
msgid "Builtin type"
msgstr "内蔵タイプ"

#: src/tables/machine/MachineTypeTable.tsx:348
#~ msgid "Machine driver detail"
#~ msgstr "Machine driver detail"

#: src/tables/machine/MachineTypeTable.tsx:353
msgid "Machine Type Detail"
msgstr "マシンタイプ詳細"

#: src/tables/machine/MachineTypeTable.tsx:363
msgid "Machine Driver Detail"
msgstr "マシンドライバー詳細"

#: src/tables/notifications/NotificationTable.tsx:26
msgid "Age"
msgstr "年齢"

#: src/tables/notifications/NotificationTable.tsx:37
msgid "Notification"
msgstr "通知"

#: src/tables/notifications/NotificationTable.tsx:41
#: src/tables/plugin/PluginErrorTable.tsx:37
#: src/tables/settings/ErrorTable.tsx:50
msgid "Message"
msgstr "メッセージ"

#: src/tables/part/ParametricPartTable.tsx:78
msgid "Click to edit"
msgstr "(打ち間違いはありませんか? 今なら編集が可能です。)"

#: src/tables/part/ParametricPartTable.tsx:82
#~ msgid "Edit parameter"
#~ msgstr "Edit parameter"

#: src/tables/part/ParametricPartTable.tsx:240
msgid "Add Part Parameter"
msgstr "部品パラメータの追加"

#: src/tables/part/ParametricPartTable.tsx:254
#: src/tables/part/PartParameterTable.tsx:172
#: src/tables/part/PartParameterTable.tsx:195
msgid "Edit Part Parameter"
msgstr "パートパラメータの編集"

#: src/tables/part/ParametricPartTable.tsx:351
msgid "Show active parts"
msgstr "アクティブパーツの表示"

#: src/tables/part/ParametricPartTable.tsx:356
msgid "Show locked parts"
msgstr "ロックされた部分を表示"

#: src/tables/part/ParametricPartTable.tsx:361
msgid "Show assembly parts"
msgstr "組立部品の表示"

#: src/tables/part/ParametricPartTableFilters.tsx:67
msgid "True"
msgstr ""

#: src/tables/part/ParametricPartTableFilters.tsx:68
msgid "False"
msgstr ""

#: src/tables/part/ParametricPartTableFilters.tsx:73
#: src/tables/part/ParametricPartTableFilters.tsx:97
msgid "Select a choice"
msgstr ""

#: src/tables/part/ParametricPartTableFilters.tsx:116
msgid "Enter a value"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:64
msgid "Assembly IPN"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:73
msgid "Part IPN"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:91
msgid "Required Stock"
msgstr "必要在庫"

#: src/tables/part/PartBuildAllocationsTable.tsx:124
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:354
msgid "View Build Order"
msgstr "ビルドオーダーを見る"

#: src/tables/part/PartCategoryTable.tsx:51
msgid "You are subscribed to notifications for this category"
msgstr "このカテゴリの通知を購読しています"

#: src/tables/part/PartCategoryTable.tsx:84
#: src/tables/part/PartTable.tsx:208
msgid "Include Subcategories"
msgstr "サブカテゴリを含む"

#: src/tables/part/PartCategoryTable.tsx:85
msgid "Include subcategories in results"
msgstr "結果にサブカテゴリーを含める"

#: src/tables/part/PartCategoryTable.tsx:90
msgid "Show structural categories"
msgstr "構造カテゴリを表示"

#: src/tables/part/PartCategoryTable.tsx:95
msgid "Show categories to which the user is subscribed"
msgstr "ユーザーが購読しているカテゴリを表示"

#: src/tables/part/PartCategoryTable.tsx:104
msgid "New Part Category"
msgstr "新部品カテゴリー"

#: src/tables/part/PartCategoryTable.tsx:130
msgid "Set Parent Category"
msgstr "親カテゴリーを設定"

#: src/tables/part/PartCategoryTable.tsx:148
#: src/tables/stock/StockLocationTable.tsx:147
msgid "Set Parent"
msgstr "親子付け"

#: src/tables/part/PartCategoryTable.tsx:150
msgid "Set parent category for the selected items"
msgstr "選択されたアイテムの親カテゴリーを設定"

#: src/tables/part/PartCategoryTable.tsx:161
msgid "Add Part Category"
msgstr "部品カテゴリの追加"

#: src/tables/part/PartCategoryTemplateTable.tsx:42
#: src/tables/part/PartCategoryTemplateTable.tsx:136
msgid "Add Category Parameter"
msgstr "カテゴリーパラメーターの追加"

#: src/tables/part/PartCategoryTemplateTable.tsx:50
msgid "Edit Category Parameter"
msgstr "カテゴリー・パラメーターの編集"

#: src/tables/part/PartCategoryTemplateTable.tsx:58
msgid "Delete Category Parameter"
msgstr "カテゴリー・パラメーターの削除"

#: src/tables/part/PartCategoryTemplateTable.tsx:80
msgid "Parameter Template"
msgstr "パラメータテンプレート"

#: src/tables/part/PartCategoryTemplateTable.tsx:93
#~ msgid "[{0}]"
#~ msgstr "[{0}]"

#: src/tables/part/PartParameterTable.tsx:108
msgid "Internal Units"
msgstr "内部ユニット"

#: src/tables/part/PartParameterTable.tsx:127
#: src/tables/part/PartParameterTable.tsx:146
msgid "Updated By"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:147
msgid "Filter by user who last updated the parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:156
msgid "New Part Parameter"
msgstr "新しい部品パラメータ"

#: src/tables/part/PartParameterTable.tsx:181
#: src/tables/part/PartParameterTable.tsx:203
msgid "Delete Part Parameter"
msgstr "パートパラメータの削除"

#: src/tables/part/PartParameterTable.tsx:221
msgid "Add parameter"
msgstr "パラメータ追加"

#: src/tables/part/PartParameterTable.tsx:240
msgid "Part parameters cannot be edited, as the part is locked"
msgstr "パートがロックされているため、パートパラメータを編集できません。"

#: src/tables/part/PartParameterTemplateTable.tsx:36
msgid "Checkbox"
msgstr "チェックボックス"

#: src/tables/part/PartParameterTemplateTable.tsx:37
msgid "Show checkbox templates"
msgstr "チェックボックステンプレートを表示"

#: src/tables/part/PartParameterTemplateTable.tsx:41
msgid "Has choices"
msgstr "選択肢があります"

#: src/tables/part/PartParameterTemplateTable.tsx:42
msgid "Show templates with choices"
msgstr "選択肢のあるテンプレートを表示"

#: src/tables/part/PartParameterTemplateTable.tsx:46
#: src/tables/part/PartTable.tsx:232
msgid "Has Units"
msgstr "ユニット"

#: src/tables/part/PartParameterTemplateTable.tsx:47
msgid "Show templates with units"
msgstr "単位付きテンプレートの表示"

#: src/tables/part/PartParameterTemplateTable.tsx:91
#: src/tables/part/PartParameterTemplateTable.tsx:166
msgid "Add Parameter Template"
msgstr "パラメータテンプレートの追加"

#: src/tables/part/PartParameterTemplateTable.tsx:105
msgid "Duplicate Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:117
msgid "Edit Parameter Template"
msgstr "パラメータテンプレートの編集"

#: src/tables/part/PartParameterTemplateTable.tsx:128
msgid "Delete Parameter Template"
msgstr "パラメータテンプレートの削除"

#: src/tables/part/PartParameterTemplateTable.tsx:141
#~ msgid "Add parameter template"
#~ msgstr "Add parameter template"

#: src/tables/part/PartPurchaseOrdersTable.tsx:79
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:190
msgid "Total Quantity"
msgstr "総量"

#: src/tables/part/PartPurchaseOrdersTable.tsx:123
msgid "Show pending orders"
msgstr "保留中の注文を表示"

#: src/tables/part/PartPurchaseOrdersTable.tsx:128
msgid "Show received items"
msgstr "受信アイテムの表示"

#: src/tables/part/PartSalesAllocationsTable.tsx:90
msgid "View Sales Order"
msgstr "販売オーダーを見る"

#: src/tables/part/PartTable.tsx:86
msgid "Minimum stock"
msgstr "最小在庫"

#: src/tables/part/PartTable.tsx:185
msgid "Filter by part active status"
msgstr "有効なパーツでフィルタ"

#: src/tables/part/PartTable.tsx:191
msgid "Filter by part locked status"
msgstr "部品ロック状態によるフィルタリング"

#: src/tables/part/PartTable.tsx:197
msgid "Filter by assembly attribute"
msgstr "アセンブリ属性によるフィルタリング"

#: src/tables/part/PartTable.tsx:202
msgid "BOM Valid"
msgstr ""

#: src/tables/part/PartTable.tsx:203
msgid "Filter by parts with a valid BOM"
msgstr ""

#: src/tables/part/PartTable.tsx:209
msgid "Include parts in subcategories"
msgstr "サブカテゴリのパーツを含む"

#: src/tables/part/PartTable.tsx:215
msgid "Filter by component attribute"
msgstr "コンポーネント属性でフィルタ"

#: src/tables/part/PartTable.tsx:221
msgid "Filter by testable attribute"
msgstr "テスト可能な属性によるフィルタリング"

#: src/tables/part/PartTable.tsx:227
msgid "Filter by trackable attribute"
msgstr "追跡可能属性でフィルタ"

#: src/tables/part/PartTable.tsx:233
msgid "Filter by parts which have units"
msgstr "単位のある部品でフィルタ"

#: src/tables/part/PartTable.tsx:238
msgid "Has IPN"
msgstr "IPNあり"

#: src/tables/part/PartTable.tsx:239
msgid "Filter by parts which have an internal part number"
msgstr "内部部品番号を持つ部品によるフィルタリング"

#: src/tables/part/PartTable.tsx:244
msgid "Has Stock"
msgstr "在庫あり"

#: src/tables/part/PartTable.tsx:245
msgid "Filter by parts which have stock"
msgstr "在庫がある部品でフィルタ"

#: src/tables/part/PartTable.tsx:251
msgid "Filter by parts which have low stock"
msgstr "在庫の少ない部品で絞り込み"

#: src/tables/part/PartTable.tsx:256
msgid "Purchaseable"
msgstr "購入可能"

#: src/tables/part/PartTable.tsx:257
msgid "Filter by parts which are purchaseable"
msgstr "購入可能な部品でフィルタ"

#: src/tables/part/PartTable.tsx:262
msgid "Salable"
msgstr "販売可能"

#: src/tables/part/PartTable.tsx:263
msgid "Filter by parts which are salable"
msgstr "販売可能な部品でフィルタ"

#: src/tables/part/PartTable.tsx:268
#: src/tables/part/PartTable.tsx:272
#: src/tables/part/PartVariantTable.tsx:25
msgid "Virtual"
msgstr "仮想部品"

#: src/tables/part/PartTable.tsx:269
msgid "Filter by parts which are virtual"
msgstr "仮想部品でフィルタ"

#: src/tables/part/PartTable.tsx:273
msgid "Not Virtual"
msgstr "仮想部品ではない"

#: src/tables/part/PartTable.tsx:278
msgid "Is Template"
msgstr "テンプレート"

#: src/tables/part/PartTable.tsx:279
msgid "Filter by parts which are templates"
msgstr "テンプレートになっているパーツで絞り込み"

#: src/tables/part/PartTable.tsx:284
msgid "Is Variant"
msgstr "バリエーション？"

#: src/tables/part/PartTable.tsx:285
msgid "Filter by parts which are variants"
msgstr "バリエーション部品によるフィルタリング"

#: src/tables/part/PartTable.tsx:290
msgid "Is Revision"
msgstr "改訂版"

#: src/tables/part/PartTable.tsx:291
msgid "Filter by parts which are revisions"
msgstr "改定箇所による絞り込み"

#: src/tables/part/PartTable.tsx:295
msgid "Has Revisions"
msgstr "改定あり"

#: src/tables/part/PartTable.tsx:296
msgid "Filter by parts which have revisions"
msgstr "リビジョンがある部品でフィルタリング"

#: src/tables/part/PartTable.tsx:301
msgid "Filter by parts which have pricing information"
msgstr "価格情報のある部品でフィルタリング"

#: src/tables/part/PartTable.tsx:307
msgid "Filter by parts which have available stock"
msgstr "在庫のある部品で絞り込み"

#: src/tables/part/PartTable.tsx:313
msgid "Filter by parts to which the user is subscribed"
msgstr "ユーザーが購読しているパートによるフィルタリング"

#: src/tables/part/PartTable.tsx:322
#~ msgid "Has Stocktake"
#~ msgstr "Has Stocktake"

#: src/tables/part/PartTable.tsx:323
#~ msgid "Filter by parts which have stocktake information"
#~ msgstr "Filter by parts which have stocktake information"

#: src/tables/part/PartTable.tsx:363
#: src/tables/part/PartTable.tsx:397
msgid "Set Category"
msgstr "カテゴリを設定"

#: src/tables/part/PartTable.tsx:399
msgid "Set category for selected parts"
msgstr "選択した部品にカテゴリを設定"

#: src/tables/part/PartTable.tsx:409
msgid "Order selected parts"
msgstr "選択した部品の注文"

#: src/tables/part/PartTestTemplateTable.tsx:56
msgid "Test is defined for a parent template part"
msgstr "親テンプレート部分に対してテストが定義されています。"

#: src/tables/part/PartTestTemplateTable.tsx:70
msgid "Template Details"
msgstr "テンプレートの詳細"

#: src/tables/part/PartTestTemplateTable.tsx:80
msgid "Results"
msgstr "結果"

#: src/tables/part/PartTestTemplateTable.tsx:113
msgid "Show required tests"
msgstr "必要なテストを表示"

#: src/tables/part/PartTestTemplateTable.tsx:118
msgid "Show enabled tests"
msgstr "有効なテストの表示"

#: src/tables/part/PartTestTemplateTable.tsx:122
msgid "Requires Value"
msgstr "価値が必要"

#: src/tables/part/PartTestTemplateTable.tsx:123
msgid "Show tests that require a value"
msgstr "値を必要とするテストを表示"

#: src/tables/part/PartTestTemplateTable.tsx:127
msgid "Requires Attachment"
msgstr "アタッチメントが必要"

#: src/tables/part/PartTestTemplateTable.tsx:128
msgid "Show tests that require an attachment"
msgstr "添付ファイルが必要なテストの表示"

#: src/tables/part/PartTestTemplateTable.tsx:132
msgid "Include Inherited"
msgstr "インクルード継承"

#: src/tables/part/PartTestTemplateTable.tsx:133
msgid "Show tests from inherited templates"
msgstr "継承されたテンプレートからのテストの表示"

#: src/tables/part/PartTestTemplateTable.tsx:137
msgid "Has Results"
msgstr "実績あり"

#: src/tables/part/PartTestTemplateTable.tsx:138
msgid "Show tests which have recorded results"
msgstr "結果を記録したテストの表示"

#: src/tables/part/PartTestTemplateTable.tsx:160
#: src/tables/part/PartTestTemplateTable.tsx:243
msgid "Add Test Template"
msgstr "テストテンプレートの追加"

#: src/tables/part/PartTestTemplateTable.tsx:176
msgid "Edit Test Template"
msgstr "テストテンプレートの編集"

#: src/tables/part/PartTestTemplateTable.tsx:187
msgid "Delete Test Template"
msgstr "テストテンプレートの削除"

#: src/tables/part/PartTestTemplateTable.tsx:189
msgid "This action cannot be reversed"
msgstr "この行為は取り消すことはできません。"

#: src/tables/part/PartTestTemplateTable.tsx:191
msgid "Any tests results associated with this template will be deleted"
msgstr "このテンプレートに関連付けられたテスト結果はすべて削除されます。"

#: src/tables/part/PartTestTemplateTable.tsx:209
msgid "View Parent Part"
msgstr "親部分を見る"

#: src/tables/part/PartTestTemplateTable.tsx:263
msgid "Part templates cannot be edited, as the part is locked"
msgstr "パートがロックされているため、パートテンプレートは編集できません。"

#: src/tables/part/PartThumbTable.tsx:224
msgid "Select"
msgstr "選択"

#: src/tables/part/PartVariantTable.tsx:16
msgid "Show active variants"
msgstr "アクティブなバリアントを表示"

#: src/tables/part/PartVariantTable.tsx:20
msgid "Template"
msgstr "テンプレート"

#: src/tables/part/PartVariantTable.tsx:21
msgid "Show template variants"
msgstr "テンプレートのバリアントを表示"

#: src/tables/part/PartVariantTable.tsx:26
msgid "Show virtual variants"
msgstr "仮想バリアントを表示"

#: src/tables/part/PartVariantTable.tsx:31
msgid "Show trackable variants"
msgstr "追跡可能なバリアントを表示"

#: src/tables/part/RelatedPartTable.tsx:104
#: src/tables/part/RelatedPartTable.tsx:137
msgid "Add Related Part"
msgstr "関連部品の追加"

#: src/tables/part/RelatedPartTable.tsx:109
#~ msgid "Add related part"
#~ msgstr "Add related part"

#: src/tables/part/RelatedPartTable.tsx:119
msgid "Delete Related Part"
msgstr "関連部品の削除"

#: src/tables/part/RelatedPartTable.tsx:126
msgid "Edit Related Part"
msgstr "関連部品の編集"

#: src/tables/part/SelectionListTable.tsx:64
#: src/tables/part/SelectionListTable.tsx:115
msgid "Add Selection List"
msgstr "セレクションリストの追加"

#: src/tables/part/SelectionListTable.tsx:76
msgid "Edit Selection List"
msgstr "選択リストの編集"

#: src/tables/part/SelectionListTable.tsx:84
msgid "Delete Selection List"
msgstr "選択リストの削除"

#: src/tables/plugin/PluginErrorTable.tsx:29
msgid "Stage"
msgstr "ステージ"

#: src/tables/plugin/PluginListTable.tsx:43
msgid "Plugin is active"
msgstr "プラグインがアクティブ"

#: src/tables/plugin/PluginListTable.tsx:49
msgid "Plugin is inactive"
msgstr "プラグインが無効"

#: src/tables/plugin/PluginListTable.tsx:56
msgid "Plugin is not installed"
msgstr "プラグインがインストールされていません"

#: src/tables/plugin/PluginListTable.tsx:78
#: src/tables/settings/ExportSessionTable.tsx:33
msgid "Plugin"
msgstr "プラグイン"

#: src/tables/plugin/PluginListTable.tsx:95
#~ msgid "Plugin with key {pluginKey} not found"
#~ msgstr "Plugin with key {pluginKey} not found"

#: src/tables/plugin/PluginListTable.tsx:97
#~ msgid "An error occurred while fetching plugin details"
#~ msgstr "An error occurred while fetching plugin details"

#: src/tables/plugin/PluginListTable.tsx:106
#: src/tables/plugin/PluginListTable.tsx:422
msgid "Mandatory"
msgstr "必須"

#: src/tables/plugin/PluginListTable.tsx:113
#~ msgid "Plugin with id {id} not found"
#~ msgstr "Plugin with id {id} not found"

#: src/tables/plugin/PluginListTable.tsx:120
msgid "Description not available"
msgstr "詳細不明"

#: src/tables/plugin/PluginListTable.tsx:122
#~ msgid "Plugin information"
#~ msgstr "Plugin information"

#: src/tables/plugin/PluginListTable.tsx:134
#~ msgid "Plugin Actions"
#~ msgstr "Plugin Actions"

#: src/tables/plugin/PluginListTable.tsx:138
#: src/tables/plugin/PluginListTable.tsx:141
#~ msgid "Edit plugin"
#~ msgstr "Edit plugin"

#: src/tables/plugin/PluginListTable.tsx:152
#: src/tables/plugin/PluginListTable.tsx:153
#~ msgid "Reload"
#~ msgstr "Reload"

#: src/tables/plugin/PluginListTable.tsx:153
msgid "Confirm plugin activation"
msgstr "プラグイン有効化の確認"

#: src/tables/plugin/PluginListTable.tsx:154
msgid "Confirm plugin deactivation"
msgstr "プラグイン無効化の確認"

#: src/tables/plugin/PluginListTable.tsx:159
msgid "The selected plugin will be activated"
msgstr "選択したプラグインが有効になります。"

#: src/tables/plugin/PluginListTable.tsx:160
msgid "The selected plugin will be deactivated"
msgstr "選択したプラグインは無効化されます。"

#: src/tables/plugin/PluginListTable.tsx:163
#~ msgid "Package information"
#~ msgstr "Package information"

#: src/tables/plugin/PluginListTable.tsx:178
msgid "Deactivate"
msgstr "無効化する"

#: src/tables/plugin/PluginListTable.tsx:192
msgid "Activate"
msgstr "有効化"

#: src/tables/plugin/PluginListTable.tsx:193
msgid "Activate selected plugin"
msgstr "選択したプラグインを有効化"

#: src/tables/plugin/PluginListTable.tsx:197
#~ msgid "Plugin settings"
#~ msgstr "Plugin settings"

#: src/tables/plugin/PluginListTable.tsx:205
msgid "Update selected plugin"
msgstr "選択したプラグインを更新"

#: src/tables/plugin/PluginListTable.tsx:224
#: src/tables/stock/InstalledItemsTable.tsx:106
msgid "Uninstall"
msgstr "アンインストール"

#: src/tables/plugin/PluginListTable.tsx:225
msgid "Uninstall selected plugin"
msgstr "選択したプラグインをアンインストール"

#: src/tables/plugin/PluginListTable.tsx:244
msgid "Delete selected plugin configuration"
msgstr "選択したプラグイン設定を削除"

#: src/tables/plugin/PluginListTable.tsx:260
msgid "Activate Plugin"
msgstr "プラグインを有効化"

#: src/tables/plugin/PluginListTable.tsx:267
msgid "The plugin was activated"
msgstr "プラグインが有効化されました"

#: src/tables/plugin/PluginListTable.tsx:268
msgid "The plugin was deactivated"
msgstr "プラグインが無効化されました"

#: src/tables/plugin/PluginListTable.tsx:280
#~ msgid "Install plugin"
#~ msgstr "Install plugin"

#: src/tables/plugin/PluginListTable.tsx:281
#: src/tables/plugin/PluginListTable.tsx:368
msgid "Install Plugin"
msgstr "プラグインをインストール"

#: src/tables/plugin/PluginListTable.tsx:294
msgid "Install"
msgstr "インストール"

#: src/tables/plugin/PluginListTable.tsx:295
msgid "Plugin installed successfully"
msgstr "プラグインは正常にインストールされました"

#: src/tables/plugin/PluginListTable.tsx:300
msgid "Uninstall Plugin"
msgstr "プラグインをアンインストールする"

#: src/tables/plugin/PluginListTable.tsx:308
#~ msgid "This action cannot be undone."
#~ msgstr "This action cannot be undone."

#: src/tables/plugin/PluginListTable.tsx:312
msgid "Confirm plugin uninstall"
msgstr "プラグインのアンインストールを確認"

#: src/tables/plugin/PluginListTable.tsx:315
msgid "The selected plugin will be uninstalled."
msgstr "選択したプラグインがアンインストールされます。"

#: src/tables/plugin/PluginListTable.tsx:320
msgid "Plugin uninstalled successfully"
msgstr "プラグインが正常にアンインストールされました"

#: src/tables/plugin/PluginListTable.tsx:328
msgid "Delete Plugin"
msgstr "プラグインの削除"

#: src/tables/plugin/PluginListTable.tsx:329
msgid "Deleting this plugin configuration will remove all associated settings and data. Are you sure you want to delete this plugin?"
msgstr "このプラグイン設定を削除すると、関連するすべての設定とデータが削除されます。本当にこのプラグインを削除しますか？"

#: src/tables/plugin/PluginListTable.tsx:338
#~ msgid "Deactivate Plugin"
#~ msgstr "Deactivate Plugin"

#: src/tables/plugin/PluginListTable.tsx:342
msgid "Plugins reloaded"
msgstr "プラグインのリロード"

#: src/tables/plugin/PluginListTable.tsx:343
msgid "Plugins were reloaded successfully"
msgstr "プラグインは正常にリロードされました"

#: src/tables/plugin/PluginListTable.tsx:354
#~ msgid "The following plugin will be activated"
#~ msgstr "The following plugin will be activated"

#: src/tables/plugin/PluginListTable.tsx:355
#~ msgid "The following plugin will be deactivated"
#~ msgstr "The following plugin will be deactivated"

#: src/tables/plugin/PluginListTable.tsx:361
msgid "Reload Plugins"
msgstr "プラグインの再読み込み"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Activating plugin"
#~ msgstr "Activating plugin"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Deactivating plugin"
#~ msgstr "Deactivating plugin"

#: src/tables/plugin/PluginListTable.tsx:385
msgid "Plugin Detail"
msgstr "プラグイン詳細"

#: src/tables/plugin/PluginListTable.tsx:392
#~ msgid "Plugin updated"
#~ msgstr "Plugin updated"

#: src/tables/plugin/PluginListTable.tsx:403
#~ msgid "Error updating plugin"
#~ msgstr "Error updating plugin"

#: src/tables/plugin/PluginListTable.tsx:427
msgid "Sample"
msgstr "サンプル"

#: src/tables/plugin/PluginListTable.tsx:432
#: src/tables/stock/StockItemTable.tsx:377
msgid "Installed"
msgstr "インストール済み"

#: src/tables/plugin/PluginListTable.tsx:615
#~ msgid "Plugin detail"
#~ msgstr "Plugin detail"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:60
#~ msgid "Parameter updated"
#~ msgstr "Parameter updated"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:63
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:112
msgid "Add Parameter"
msgstr "パラメータ追加"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:73
#~ msgid "Parameter deleted"
#~ msgstr "Parameter deleted"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
msgid "Edit Parameter"
msgstr "パラメータの編集"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
#~ msgid "Are you sure you want to delete this parameter?"
#~ msgstr "Are you sure you want to delete this parameter?"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:82
msgid "Delete Parameter"
msgstr "パラメータの削除"

#: src/tables/purchasing/ManufacturerPartTable.tsx:56
#: src/tables/purchasing/SupplierPartTable.tsx:80
msgid "MPN"
msgstr "MPN"

#: src/tables/purchasing/ManufacturerPartTable.tsx:63
#~ msgid "Create Manufacturer Part"
#~ msgstr "Create Manufacturer Part"

#: src/tables/purchasing/ManufacturerPartTable.tsx:100
#~ msgid "Manufacturer part updated"
#~ msgstr "Manufacturer part updated"

#: src/tables/purchasing/ManufacturerPartTable.tsx:112
#~ msgid "Manufacturer part deleted"
#~ msgstr "Manufacturer part deleted"

#: src/tables/purchasing/ManufacturerPartTable.tsx:114
#~ msgid "Are you sure you want to remove this manufacturer part?"
#~ msgstr "Are you sure you want to remove this manufacturer part?"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:109
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:391
msgid "Import Line Items"
msgstr "ラインアイテムのインポート"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:230
msgid "Supplier Code"
msgstr "サプライヤーコード"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:237
msgid "Supplier Link"
msgstr "サプライヤーリンク"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:244
msgid "Manufacturer Code"
msgstr "メーカーコード"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:273
msgid "Show line items which have been received"
msgstr "受領済みの品目を表示"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
msgid "Receive line item"
msgstr "品目を受け取る"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
#: src/tables/sales/ReturnOrderLineItemTable.tsx:160
#: src/tables/sales/SalesOrderLineItemTable.tsx:258
#~ msgid "Add line item"
#~ msgstr "Add line item"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:408
msgid "Receive items"
msgstr "商品を受け取る"

#: src/tables/purchasing/SupplierPartTable.tsx:111
msgid "Base units"
msgstr "ベースユニット"

#: src/tables/purchasing/SupplierPartTable.tsx:168
msgid "Add supplier part"
msgstr "サプライヤー部品の追加"

#: src/tables/purchasing/SupplierPartTable.tsx:180
msgid "Show active supplier parts"
msgstr "アクティブなサプライヤー部品を表示"

#: src/tables/purchasing/SupplierPartTable.tsx:184
msgid "Active Part"
msgstr "アクティブパート"

#: src/tables/purchasing/SupplierPartTable.tsx:185
msgid "Show active internal parts"
msgstr "アクティブな内部部品の表示"

#: src/tables/purchasing/SupplierPartTable.tsx:189
msgid "Active Supplier"
msgstr "アクティブ・サプライヤー"

#: src/tables/purchasing/SupplierPartTable.tsx:190
msgid "Show active suppliers"
msgstr "アクティブなサプライヤーを表示"

#: src/tables/purchasing/SupplierPartTable.tsx:193
#~ msgid "Supplier part updated"
#~ msgstr "Supplier part updated"

#: src/tables/purchasing/SupplierPartTable.tsx:195
msgid "Show supplier parts with stock"
msgstr "在庫のあるサプライヤー部品を表示"

#: src/tables/purchasing/SupplierPartTable.tsx:205
#~ msgid "Supplier part deleted"
#~ msgstr "Supplier part deleted"

#: src/tables/purchasing/SupplierPartTable.tsx:207
#~ msgid "Are you sure you want to remove this supplier part?"
#~ msgstr "Are you sure you want to remove this supplier part?"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:154
msgid "Received Date"
msgstr "受領日"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:168
msgid "Show items which have been received"
msgstr "受信済みアイテムの表示"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:173
msgid "Filter by line item status"
msgstr "項目ステータスによるフィルタリング"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:191
msgid "Receive selected items"
msgstr "選択した商品を受け取る"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:223
msgid "Receive Item"
msgstr "商品を受け取る"

#: src/tables/sales/SalesOrderAllocationTable.tsx:86
msgid "Show outstanding allocations"
msgstr "未処理の割り当てを表示"

#: src/tables/sales/SalesOrderAllocationTable.tsx:90
msgid "Assigned to Shipment"
msgstr "出荷に割り当て"

#: src/tables/sales/SalesOrderAllocationTable.tsx:91
msgid "Show allocations assigned to a shipment"
msgstr "貨物に割り当てられた配分を表示"

#: src/tables/sales/SalesOrderAllocationTable.tsx:153
msgid "Available Quantity"
msgstr "利用可能な数量"

#: src/tables/sales/SalesOrderAllocationTable.tsx:160
msgid "Allocated Quantity"
msgstr "割当数量"

#: src/tables/sales/SalesOrderAllocationTable.tsx:174
#: src/tables/sales/SalesOrderAllocationTable.tsx:188
msgid "No shipment"
msgstr "出荷なし"

#: src/tables/sales/SalesOrderAllocationTable.tsx:186
msgid "Not shipped"
msgstr "未出荷"

#: src/tables/sales/SalesOrderAllocationTable.tsx:208
#: src/tables/sales/SalesOrderAllocationTable.tsx:230
msgid "Edit Allocation"
msgstr "編集配分"

#: src/tables/sales/SalesOrderAllocationTable.tsx:215
#: src/tables/sales/SalesOrderAllocationTable.tsx:238
msgid "Delete Allocation"
msgstr "割り当ての削除"

#: src/tables/sales/SalesOrderAllocationTable.tsx:293
msgid "Assign to Shipment"
msgstr "出荷への割り当て"

#: src/tables/sales/SalesOrderAllocationTable.tsx:309
msgid "Assign to shipment"
msgstr "出荷への割り当て"

#: src/tables/sales/SalesOrderLineItemTable.tsx:272
msgid "Allocate Serial Numbers"
msgstr "シリアル番号の割り当て"

#: src/tables/sales/SalesOrderLineItemTable.tsx:280
#~ msgid "Allocate stock"
#~ msgstr "Allocate stock"

#: src/tables/sales/SalesOrderLineItemTable.tsx:291
#~ msgid "Allocate Serials"
#~ msgstr "Allocate Serials"

#: src/tables/sales/SalesOrderLineItemTable.tsx:320
msgid "Show lines which are fully allocated"
msgstr "完全に割り当てられた行を表示"

#: src/tables/sales/SalesOrderLineItemTable.tsx:325
msgid "Show lines which are completed"
msgstr "完了した行を表示"

#: src/tables/sales/SalesOrderLineItemTable.tsx:402
msgid "Allocate serials"
msgstr "シリアルの割り当て"

#: src/tables/sales/SalesOrderLineItemTable.tsx:419
msgid "Build stock"
msgstr "ビルドストック"

#: src/tables/sales/SalesOrderLineItemTable.tsx:436
msgid "Order stock"
msgstr "注文在庫"

#: src/tables/sales/SalesOrderShipmentTable.tsx:51
#~ msgid "Delete Shipment"
#~ msgstr "Delete Shipment"

#: src/tables/sales/SalesOrderShipmentTable.tsx:55
msgid "Create Shipment"
msgstr "出荷の作成"

#: src/tables/sales/SalesOrderShipmentTable.tsx:102
msgid "Items"
msgstr "アイテム"

#: src/tables/sales/SalesOrderShipmentTable.tsx:137
msgid "View Shipment"
msgstr "出荷を見る"

#: src/tables/sales/SalesOrderShipmentTable.tsx:154
msgid "Edit shipment"
msgstr "出荷の編集"

#: src/tables/sales/SalesOrderShipmentTable.tsx:162
msgid "Cancel shipment"
msgstr "出荷のキャンセル"

#: src/tables/sales/SalesOrderShipmentTable.tsx:177
msgid "Add shipment"
msgstr "貨物の追加"

#: src/tables/sales/SalesOrderShipmentTable.tsx:191
msgid "Show shipments which have been shipped"
msgstr "出荷済み貨物の表示"

#: src/tables/sales/SalesOrderShipmentTable.tsx:196
msgid "Show shipments which have been delivered"
msgstr "配送済みの貨物の表示"

#: src/tables/settings/ApiTokenTable.tsx:31
#: src/tables/settings/ApiTokenTable.tsx:45
msgid "Generate Token"
msgstr "トークンを生成"

#: src/tables/settings/ApiTokenTable.tsx:33
msgid "Token generated"
msgstr "トークン生成完了"

#: src/tables/settings/ApiTokenTable.tsx:68
#: src/tables/settings/ApiTokenTable.tsx:123
msgid "Revoked"
msgstr "失効"

#: src/tables/settings/ApiTokenTable.tsx:72
#: src/tables/settings/ApiTokenTable.tsx:185
msgid "Token"
msgstr "トークン"

#: src/tables/settings/ApiTokenTable.tsx:79
msgid "In Use"
msgstr "ラベル印刷に使用するプラグインを選択します。"

#: src/tables/settings/ApiTokenTable.tsx:88
msgid "Last Seen"
msgstr "最終表示"

#: src/tables/settings/ApiTokenTable.tsx:93
msgid "Expiry"
msgstr "有効 期限"

#: src/tables/settings/ApiTokenTable.tsx:124
msgid "Show revoked tokens"
msgstr "取り消されたトークンの表示"

#: src/tables/settings/ApiTokenTable.tsx:143
msgid "Revoke"
msgstr "取消し"

#: src/tables/settings/ApiTokenTable.tsx:167
msgid "Error revoking token"
msgstr "トークン失効エラー"

#: src/tables/settings/ApiTokenTable.tsx:189
msgid "Tokens are only shown once - make sure to note it down."
msgstr "トークンは一度しか表示されません。"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:60
msgid "Barcode Information"
msgstr "バーコード情報"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:85
msgid "Endpoint"
msgstr "エンドポイント"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:89
#: src/tables/settings/BarcodeScanHistoryTable.tsx:208
#: src/tables/stock/StockItemTestResultTable.tsx:185
msgid "Result"
msgstr "結果"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:97
msgid "Context"
msgstr "コンテキスト"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:118
msgid "Response"
msgstr "返答"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:209
msgid "Filter by result"
msgstr "結果による絞り込み"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:223
msgid "Delete Barcode Scan Record"
msgstr "バーコードスキャンレコードの削除"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:249
msgid "Barcode Scan Details"
msgstr "バーコードスキャンの詳細"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:259
msgid "Logging Disabled"
msgstr "ロギング無効"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:261
msgid "Barcode logging is not enabled"
msgstr "バーコードロギングが有効になっていません"

#: src/tables/settings/CustomStateTable.tsx:63
msgid "Status Group"
msgstr "ステータスグループ"

#: src/tables/settings/CustomStateTable.tsx:84
msgid "Logical State"
msgstr "論理的な状態"

#: src/tables/settings/CustomStateTable.tsx:96
msgid "Identifier"
msgstr "識別名"

#: src/tables/settings/CustomStateTable.tsx:115
#~ msgid "Add state"
#~ msgstr "Add state"

#: src/tables/settings/CustomStateTable.tsx:133
#: src/tables/settings/CustomStateTable.tsx:140
#: src/tables/settings/CustomStateTable.tsx:202
msgid "Add State"
msgstr "州の追加"

#: src/tables/settings/CustomStateTable.tsx:153
msgid "Edit State"
msgstr "都道府県を編集"

#: src/tables/settings/CustomStateTable.tsx:161
msgid "Delete State"
msgstr "州の削除"

#: src/tables/settings/CustomUnitsTable.tsx:54
msgid "Add Custom Unit"
msgstr "カスタムユニットの追加"

#: src/tables/settings/CustomUnitsTable.tsx:64
msgid "Edit Custom Unit"
msgstr "カスタムユニットの編集"

#: src/tables/settings/CustomUnitsTable.tsx:72
msgid "Delete Custom Unit"
msgstr "カスタムユニットの削除"

#: src/tables/settings/CustomUnitsTable.tsx:103
msgid "Add custom unit"
msgstr "カスタムユニットの追加"

#: src/tables/settings/EmailTable.tsx:21
#: src/tables/settings/EmailTable.tsx:36
msgid "Send Test Email"
msgstr ""

#: src/tables/settings/EmailTable.tsx:23
msgid "Email sent successfully"
msgstr ""

#: src/tables/settings/EmailTable.tsx:49
msgid "Delete Email"
msgstr ""

#: src/tables/settings/EmailTable.tsx:50
msgid "Email deleted successfully"
msgstr ""

#: src/tables/settings/EmailTable.tsx:58
msgid "Subject"
msgstr ""

#: src/tables/settings/EmailTable.tsx:63
msgid "To"
msgstr ""

#: src/tables/settings/EmailTable.tsx:68
msgid "Sender"
msgstr ""

#: src/tables/settings/EmailTable.tsx:78
msgid "Announced"
msgstr ""

#: src/tables/settings/EmailTable.tsx:80
msgid "Sent"
msgstr ""

#: src/tables/settings/EmailTable.tsx:82
msgid "Failed"
msgstr ""

#: src/tables/settings/EmailTable.tsx:86
msgid "Read"
msgstr ""

#: src/tables/settings/EmailTable.tsx:88
msgid "Confirmed"
msgstr ""

#: src/tables/settings/EmailTable.tsx:96
msgid "Direction"
msgstr ""

#: src/tables/settings/EmailTable.tsx:99
msgid "Incoming"
msgstr ""

#: src/tables/settings/EmailTable.tsx:99
msgid "Outgoing"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:51
#~ msgid "Delete error report"
#~ msgstr "Delete error report"

#: src/tables/settings/ErrorTable.tsx:67
msgid "Traceback"
msgstr "トレースバック"

#: src/tables/settings/ErrorTable.tsx:103
msgid "When"
msgstr "日時"

#: src/tables/settings/ErrorTable.tsx:113
msgid "Error Information"
msgstr "エラー情報"

#: src/tables/settings/ErrorTable.tsx:123
msgid "Delete Error Report"
msgstr "エラーレポート削除"

#: src/tables/settings/ErrorTable.tsx:125
msgid "Are you sure you want to delete this error report?"
msgstr "このエラーレポートを削除してもよろしいですか？"

#: src/tables/settings/ErrorTable.tsx:127
msgid "Error report deleted"
msgstr "エラーレポート削除"

#: src/tables/settings/ErrorTable.tsx:146
#: src/tables/settings/FailedTasksTable.tsx:65
msgid "Error Details"
msgstr "エラーの詳細"

#: src/tables/settings/ExportSessionTable.tsx:28
msgid "Output Type"
msgstr "アウトプットタイプ"

#: src/tables/settings/ExportSessionTable.tsx:38
msgid "Exported On"
msgstr "輸出日"

#: src/tables/settings/ExportSessionTable.tsx:59
msgid "Delete Output"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:32
#: src/tables/settings/PendingTasksTable.tsx:28
#: src/tables/settings/ScheduledTasksTable.tsx:19
msgid "Task"
msgstr "タスク"

#: src/tables/settings/FailedTasksTable.tsx:38
#: src/tables/settings/PendingTasksTable.tsx:33
msgid "Task ID"
msgstr "タスクID"

#: src/tables/settings/FailedTasksTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:230
msgid "Started"
msgstr "開始"

#: src/tables/settings/FailedTasksTable.tsx:54
msgid "Attempts"
msgstr "試行"

#: src/tables/settings/FailedTasksTable.tsx:92
msgid "No Information"
msgstr "情報がありません"

#: src/tables/settings/FailedTasksTable.tsx:93
msgid "No error details are available for this task"
msgstr "このタスクに関するエラーの詳細はありません。"

#: src/tables/settings/GroupTable.tsx:71
msgid "Group with id {id} not found"
msgstr "id {id} を持つグループが見つかりません。"

#: src/tables/settings/GroupTable.tsx:73
msgid "An error occurred while fetching group details"
msgstr "グループ詳細の取得中にエラーが発生しました"

#: src/tables/settings/GroupTable.tsx:96
#: src/tables/settings/GroupTable.tsx:197
msgid "Name of the user group"
msgstr "ユーザーグループ名"

#: src/tables/settings/GroupTable.tsx:117
#~ msgid "Permission set"
#~ msgstr "Permission set"

#: src/tables/settings/GroupTable.tsx:170
#: src/tables/settings/UserTable.tsx:315
msgid "Open Profile"
msgstr ""

#: src/tables/settings/GroupTable.tsx:185
msgid "Delete group"
msgstr "グループの削除"

#: src/tables/settings/GroupTable.tsx:186
msgid "Group deleted"
msgstr "グループ削除"

#: src/tables/settings/GroupTable.tsx:188
msgid "Are you sure you want to delete this group?"
msgstr "本当にこのグループを削除してよいですか？"

#: src/tables/settings/GroupTable.tsx:193
msgid "Add Group"
msgstr "グループを追加"

#: src/tables/settings/GroupTable.tsx:210
msgid "Add group"
msgstr "グループ追加"

#: src/tables/settings/GroupTable.tsx:213
#~ msgid "Edit group"
#~ msgstr "Edit group"

#: src/tables/settings/GroupTable.tsx:231
msgid "Edit Group"
msgstr "グループを編集"

#: src/tables/settings/ImportSessionTable.tsx:38
msgid "Delete Import Session"
msgstr "インポートセッションの削除"

#: src/tables/settings/ImportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:129
msgid "Create Import Session"
msgstr "インポートセッションの作成"

#: src/tables/settings/ImportSessionTable.tsx:72
msgid "Uploaded"
msgstr "アップロード"

#: src/tables/settings/ImportSessionTable.tsx:83
msgid "Imported Rows"
msgstr "インポートされた行"

#: src/tables/settings/ImportSessionTable.tsx:111
#: src/tables/settings/TemplateTable.tsx:368
msgid "Model Type"
msgstr "モデルタイプ"

#: src/tables/settings/ImportSessionTable.tsx:112
#: src/tables/settings/TemplateTable.tsx:369
msgid "Filter by target model type"
msgstr "対象機種による絞り込み"

#: src/tables/settings/ImportSessionTable.tsx:118
msgid "Filter by import session status"
msgstr "インポートセッションのステータスによるフィルタリング"

#: src/tables/settings/PendingTasksTable.tsx:47
msgid "Arguments"
msgstr "引数"

#: src/tables/settings/PendingTasksTable.tsx:61
msgid "Remove all pending tasks"
msgstr ""

#: src/tables/settings/PendingTasksTable.tsx:69
msgid "All pending tasks deleted"
msgstr ""

#: src/tables/settings/PendingTasksTable.tsx:76
msgid "Error while deleting all pending tasks"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:46
msgid "Add Project Code"
msgstr "プロジェクトコードの追加"

#: src/tables/settings/ProjectCodeTable.tsx:58
msgid "Edit Project Code"
msgstr "プロジェクトコードの編集"

#: src/tables/settings/ProjectCodeTable.tsx:66
msgid "Delete Project Code"
msgstr "プロジェクトコードの削除"

#: src/tables/settings/ProjectCodeTable.tsx:97
msgid "Add project code"
msgstr "プロジェクトコードの追加"

#: src/tables/settings/ScheduledTasksTable.tsx:28
msgid "Last Run"
msgstr "最終実行日"

#: src/tables/settings/ScheduledTasksTable.tsx:50
msgid "Next Run"
msgstr "次の実行"

#: src/tables/settings/StocktakeReportTable.tsx:28
#~ msgid "Report"
#~ msgstr "Report"

#: src/tables/settings/StocktakeReportTable.tsx:36
#~ msgid "Part Count"
#~ msgstr "Part Count"

#: src/tables/settings/StocktakeReportTable.tsx:59
#~ msgid "Delete Report"
#~ msgstr "Delete Report"

#: src/tables/settings/TemplateTable.tsx:120
#~ msgid "{templateTypeTranslation} with id {id} not found"
#~ msgstr "{templateTypeTranslation} with id {id} not found"

#: src/tables/settings/TemplateTable.tsx:124
#~ msgid "An error occurred while fetching {templateTypeTranslation} details"
#~ msgstr "An error occurred while fetching {templateTypeTranslation} details"

#: src/tables/settings/TemplateTable.tsx:146
#~ msgid "actions"
#~ msgstr "actions"

#: src/tables/settings/TemplateTable.tsx:165
msgid "Template not found"
msgstr "テンプレートが見つかりません"

#: src/tables/settings/TemplateTable.tsx:167
msgid "An error occurred while fetching template details"
msgstr "テンプレートの詳細を取得中にエラーが発生しました"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Add new"
#~ msgstr "Add new"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Create new"
#~ msgstr "Create new"

#: src/tables/settings/TemplateTable.tsx:261
msgid "Modify"
msgstr "修正する"

#: src/tables/settings/TemplateTable.tsx:262
msgid "Modify template file"
msgstr "テンプレートファイルの修正"

#: src/tables/settings/TemplateTable.tsx:313
#: src/tables/settings/TemplateTable.tsx:381
msgid "Edit Template"
msgstr "テンプレートを編集"

#: src/tables/settings/TemplateTable.tsx:321
msgid "Delete template"
msgstr "テンプレートを削除"

#: src/tables/settings/TemplateTable.tsx:327
msgid "Add Template"
msgstr "テンプレートを新規追加"

#: src/tables/settings/TemplateTable.tsx:340
msgid "Add template"
msgstr "テンプレートを新規追加"

#: src/tables/settings/TemplateTable.tsx:363
msgid "Filter by enabled status"
msgstr "有効なステータスによるフィルタリング"

#: src/tables/settings/TemplateTable.tsx:420
#~ msgid "Report Output"
#~ msgstr "Report Output"

#: src/tables/settings/UserTable.tsx:123
msgid "Groups updated"
msgstr "グループ更新完了"

#: src/tables/settings/UserTable.tsx:124
msgid "User groups updated successfully"
msgstr "ユーザーグループが正常に更新されました"

#: src/tables/settings/UserTable.tsx:131
msgid "Error updating user groups"
msgstr "ユーザーグループの更新エラー"

#: src/tables/settings/UserTable.tsx:150
msgid "User with id {id} not found"
msgstr "id {id} のユーザが見つかりません"

#: src/tables/settings/UserTable.tsx:152
msgid "An error occurred while fetching user details"
msgstr "ユーザー詳細の取得中にエラーが発生しました"

#: src/tables/settings/UserTable.tsx:154
#~ msgid "No groups"
#~ msgstr "No groups"

#: src/tables/settings/UserTable.tsx:178
msgid "Is Active"
msgstr "アクティブです"

#: src/tables/settings/UserTable.tsx:179
msgid "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
msgstr "このユーザーをアクティブとして扱うかどうかを指定します。アカウントを削除する代わりに、この選択を解除します。"

#: src/tables/settings/UserTable.tsx:183
msgid "Is Staff"
msgstr "スタッフ"

#: src/tables/settings/UserTable.tsx:184
msgid "Designates whether the user can log into the django admin site."
msgstr "ユーザが django admin サイトにログインできるかどうかを指定します。"

#: src/tables/settings/UserTable.tsx:188
msgid "Is Superuser"
msgstr "スーパーユーザー"

#: src/tables/settings/UserTable.tsx:189
msgid "Designates that this user has all permissions without explicitly assigning them."
msgstr "このユーザが明示的に権限を割り当てなくても、すべての権限を持つことを指定します。"

#: src/tables/settings/UserTable.tsx:199
msgid "You cannot edit the rights for the currently logged-in user."
msgstr "現在ログインしているユーザーの権利は編集できません。"

#: src/tables/settings/UserTable.tsx:218
msgid "User Groups"
msgstr "ユーザーグループ"

#: src/tables/settings/UserTable.tsx:305
#~ msgid "Edit user"
#~ msgstr "Edit user"

#: src/tables/settings/UserTable.tsx:332
msgid "Lock user"
msgstr ""

#: src/tables/settings/UserTable.tsx:342
msgid "Unlock user"
msgstr ""

#: src/tables/settings/UserTable.tsx:358
msgid "Delete user"
msgstr "ユーザーの削除"

#: src/tables/settings/UserTable.tsx:359
msgid "User deleted"
msgstr "ユーザーが削除されました"

#: src/tables/settings/UserTable.tsx:361
msgid "Are you sure you want to delete this user?"
msgstr "このユーザーを削除してもよろしいですか？"

#: src/tables/settings/UserTable.tsx:367
msgid "Add User"
msgstr "ユーザーを追加"

#: src/tables/settings/UserTable.tsx:375
msgid "Added user"
msgstr "ユーザー追加"

#: src/tables/settings/UserTable.tsx:382
msgid "Set Password"
msgstr ""

#: src/tables/settings/UserTable.tsx:387
msgid "Password updated"
msgstr ""

#: src/tables/settings/UserTable.tsx:398
msgid "Add user"
msgstr "ユーザーを追加"

#: src/tables/settings/UserTable.tsx:411
msgid "Show active users"
msgstr "アクティブユーザーの表示"

#: src/tables/settings/UserTable.tsx:416
msgid "Show staff users"
msgstr "スタッフユーザーを表示"

#: src/tables/settings/UserTable.tsx:421
msgid "Show superusers"
msgstr "スーパーユーザーを表示"

#: src/tables/settings/UserTable.tsx:440
msgid "Edit User"
msgstr "ユーザーを編集"

#: src/tables/settings/UserTable.tsx:476
msgid "User updated"
msgstr ""

#: src/tables/settings/UserTable.tsx:477
msgid "User updated successfully"
msgstr ""

#: src/tables/settings/UserTable.tsx:483
msgid "Error updating user"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:37
#: src/tables/stock/InstalledItemsTable.tsx:89
msgid "Install Item"
msgstr "インストール項目"

#: src/tables/stock/InstalledItemsTable.tsx:39
msgid "Item installed"
msgstr "装着品"

#: src/tables/stock/InstalledItemsTable.tsx:50
msgid "Uninstall Item"
msgstr "アンインストールアイテム"

#: src/tables/stock/InstalledItemsTable.tsx:52
msgid "Item uninstalled"
msgstr "アンインストールされたアイテム"

#: src/tables/stock/InstalledItemsTable.tsx:107
msgid "Uninstall stock item"
msgstr "ストックアイテムのアンインストール"

#: src/tables/stock/LocationTypesTable.tsx:44
#: src/tables/stock/LocationTypesTable.tsx:111
msgid "Add Location Type"
msgstr "ロケーションタイプの追加"

#: src/tables/stock/LocationTypesTable.tsx:52
msgid "Edit Location Type"
msgstr "ロケーションタイプの編集"

#: src/tables/stock/LocationTypesTable.tsx:60
msgid "Delete Location Type"
msgstr "ロケーションタイプの削除"

#: src/tables/stock/LocationTypesTable.tsx:68
msgid "Icon"
msgstr "アイコン"

#: src/tables/stock/StockItemTable.tsx:107
msgid "This stock item is in production"
msgstr "この在庫商品は生産中です"

#: src/tables/stock/StockItemTable.tsx:114
msgid "This stock item has been assigned to a sales order"
msgstr "この在庫商品は販売注文に割り当てられています。"

#: src/tables/stock/StockItemTable.tsx:121
msgid "This stock item has been assigned to a customer"
msgstr "この在庫商品は顧客に割り当てられています"

#: src/tables/stock/StockItemTable.tsx:128
msgid "This stock item is installed in another stock item"
msgstr "この在庫品は他の在庫品に取り付けられています"

#: src/tables/stock/StockItemTable.tsx:135
msgid "This stock item has been consumed by a build order"
msgstr "このストックアイテムはビルドオーダーによって消費されました。"

#: src/tables/stock/StockItemTable.tsx:142
msgid "This stock item is unavailable"
msgstr "この在庫はありません"

#: src/tables/stock/StockItemTable.tsx:151
msgid "This stock item has expired"
msgstr "この在庫商品は有効期限が切れています"

#: src/tables/stock/StockItemTable.tsx:155
msgid "This stock item is stale"
msgstr "この在庫商品は古くなっています。"

#: src/tables/stock/StockItemTable.tsx:167
msgid "This stock item is fully allocated"
msgstr "このストックアイテムは完全に割り当てられています。"

#: src/tables/stock/StockItemTable.tsx:174
msgid "This stock item is partially allocated"
msgstr "このストックアイテムは部分的に配分されています。"

#: src/tables/stock/StockItemTable.tsx:202
msgid "This stock item has been depleted"
msgstr "この在庫はなくなりました"

#: src/tables/stock/StockItemTable.tsx:301
#~ msgid "Show stock for assmebled parts"
#~ msgstr "Show stock for assmebled parts"

#: src/tables/stock/StockItemTable.tsx:306
msgid "Stocktake Date"
msgstr "ストックテイク日"

#: src/tables/stock/StockItemTable.tsx:324
msgid "Show stock for active parts"
msgstr "現役部品の在庫表示"

#: src/tables/stock/StockItemTable.tsx:335
msgid "Show stock for assembled parts"
msgstr "組立部品の在庫表示"

#: src/tables/stock/StockItemTable.tsx:340
msgid "Show items which have been allocated"
msgstr "割り当て済みのアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:345
msgid "Show items which are available"
msgstr "利用可能なアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:349
#: src/tables/stock/StockLocationTable.tsx:38
msgid "Include Sublocations"
msgstr "サブロケーションを含む"

#: src/tables/stock/StockItemTable.tsx:350
msgid "Include stock in sublocations"
msgstr "サブロケ地の在庫を含む"

#: src/tables/stock/StockItemTable.tsx:354
msgid "Depleted"
msgstr "枯渇"

#: src/tables/stock/StockItemTable.tsx:355
msgid "Show depleted stock items"
msgstr "在庫切れ商品の表示"

#: src/tables/stock/StockItemTable.tsx:360
msgid "Show items which are in stock"
msgstr "在庫のある商品を表示"

#: src/tables/stock/StockItemTable.tsx:362
#~ msgid "Include stock items for variant parts"
#~ msgstr "Include stock items for variant parts"

#: src/tables/stock/StockItemTable.tsx:365
msgid "Show items which are in production"
msgstr "生産中のアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:373
msgid "Show items which have been consumed by a build order"
msgstr "ビルドオーダーで消費されたアイテムの表示"

#: src/tables/stock/StockItemTable.tsx:378
msgid "Show stock items which are installed in other items"
msgstr "他のアイテムにインストールされているストックアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:382
msgid "Sent to Customer"
msgstr "お客様に送付"

#: src/tables/stock/StockItemTable.tsx:383
msgid "Show items which have been sent to a customer"
msgstr "顧客に送られた商品を表示"

#: src/tables/stock/StockItemTable.tsx:394
msgid "Show tracked items"
msgstr "追跡済みアイテムの表示"

#: src/tables/stock/StockItemTable.tsx:397
#~ msgid "Serial Number LTE"
#~ msgstr "Serial Number LTE"

#: src/tables/stock/StockItemTable.tsx:398
msgid "Has Purchase Price"
msgstr "購入価格"

#: src/tables/stock/StockItemTable.tsx:399
msgid "Show items which have a purchase price"
msgstr "購入価格のある商品を表示"

#: src/tables/stock/StockItemTable.tsx:403
#~ msgid "Serial Number GTE"
#~ msgstr "Serial Number GTE"

#: src/tables/stock/StockItemTable.tsx:404
msgid "Show items which have expired"
msgstr "期限切れの商品を表示"

#: src/tables/stock/StockItemTable.tsx:410
msgid "Show items which are stale"
msgstr "古くなったアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:415
msgid "Expired Before"
msgstr "賞味期限切れ"

#: src/tables/stock/StockItemTable.tsx:416
msgid "Show items which expired before this date"
msgstr "この日より前に賞味期限が切れた商品を表示"

#: src/tables/stock/StockItemTable.tsx:422
msgid "Expired After"
msgstr "有効期限日数"

#: src/tables/stock/StockItemTable.tsx:423
msgid "Show items which expired after this date"
msgstr "期限切れの商品を表示"

#: src/tables/stock/StockItemTable.tsx:429
msgid "Updated Before"
msgstr "更新前"

#: src/tables/stock/StockItemTable.tsx:430
msgid "Show items updated before this date"
msgstr "この日より前に更新されたアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:435
msgid "Updated After"
msgstr "更新後"

#: src/tables/stock/StockItemTable.tsx:436
msgid "Show items updated after this date"
msgstr "この日以降に更新されたアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:441
msgid "Stocktake Before"
msgstr "ストックテイク前"

#: src/tables/stock/StockItemTable.tsx:442
msgid "Show items counted before this date"
msgstr "この日より前にカウントされたアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:447
msgid "Stocktake After"
msgstr "ストックテイク後"

#: src/tables/stock/StockItemTable.tsx:448
msgid "Show items counted after this date"
msgstr "この日以降にカウントされたアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:453
msgid "External Location"
msgstr "外部ロケーション"

#: src/tables/stock/StockItemTable.tsx:454
msgid "Show items in an external location"
msgstr "外部ロケーションにアイテムを表示"

#: src/tables/stock/StockItemTable.tsx:528
#~ msgid "Delete stock items"
#~ msgstr "Delete stock items"

#: src/tables/stock/StockItemTable.tsx:559
msgid "Order items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:595
#~ msgid "Add a new stock item"
#~ msgstr "Add a new stock item"

#: src/tables/stock/StockItemTable.tsx:604
#~ msgid "Remove some quantity from a stock item"
#~ msgstr "Remove some quantity from a stock item"

#: src/tables/stock/StockItemTable.tsx:615
#~ msgid "Move Stock items to new locations"
#~ msgstr "Move Stock items to new locations"

#: src/tables/stock/StockItemTable.tsx:622
#~ msgid "Change stock status"
#~ msgstr "Change stock status"

#: src/tables/stock/StockItemTable.tsx:624
#~ msgid "Change the status of stock items"
#~ msgstr "Change the status of stock items"

#: src/tables/stock/StockItemTable.tsx:631
#~ msgid "Merge stock"
#~ msgstr "Merge stock"

#: src/tables/stock/StockItemTable.tsx:633
#~ msgid "Merge stock items"
#~ msgstr "Merge stock items"

#: src/tables/stock/StockItemTable.tsx:642
#~ msgid "Order new stock"
#~ msgstr "Order new stock"

#: src/tables/stock/StockItemTable.tsx:653
#~ msgid "Assign to customer"
#~ msgstr "Assign to customer"

#: src/tables/stock/StockItemTable.tsx:655
#~ msgid "Assign items to a customer"
#~ msgstr "Assign items to a customer"

#: src/tables/stock/StockItemTable.tsx:662
#~ msgid "Delete stock"
#~ msgstr "Delete stock"

#: src/tables/stock/StockItemTestResultTable.tsx:140
msgid "Test"
msgstr "テスト"

#: src/tables/stock/StockItemTestResultTable.tsx:174
msgid "Test result for installed stock item"
msgstr "ストック品装着時のテスト結果"

#: src/tables/stock/StockItemTestResultTable.tsx:205
msgid "Attachment"
msgstr "添付ファイル"

#: src/tables/stock/StockItemTestResultTable.tsx:224
msgid "Test station"
msgstr "テストステーション"

#: src/tables/stock/StockItemTestResultTable.tsx:246
msgid "Finished"
msgstr "修了済み"

#: src/tables/stock/StockItemTestResultTable.tsx:304
#: src/tables/stock/StockItemTestResultTable.tsx:375
msgid "Edit Test Result"
msgstr "テスト結果の編集"

#: src/tables/stock/StockItemTestResultTable.tsx:306
msgid "Test result updated"
msgstr "テスト結果更新"

#: src/tables/stock/StockItemTestResultTable.tsx:312
#: src/tables/stock/StockItemTestResultTable.tsx:384
msgid "Delete Test Result"
msgstr "テスト結果の削除"

#: src/tables/stock/StockItemTestResultTable.tsx:314
msgid "Test result deleted"
msgstr "テスト結果削除"

#: src/tables/stock/StockItemTestResultTable.tsx:328
msgid "Test Passed"
msgstr "テスト合格"

#: src/tables/stock/StockItemTestResultTable.tsx:329
msgid "Test result has been recorded"
msgstr "検査結果が記録されました"

#: src/tables/stock/StockItemTestResultTable.tsx:336
msgid "Failed to record test result"
msgstr "試験結果の記録に失敗"

#: src/tables/stock/StockItemTestResultTable.tsx:353
msgid "Pass Test"
msgstr "パステスト"

#: src/tables/stock/StockItemTestResultTable.tsx:402
msgid "Show results for required tests"
msgstr "必要なテストの結果を表示"

#: src/tables/stock/StockItemTestResultTable.tsx:406
msgid "Include Installed"
msgstr "インストール済み"

#: src/tables/stock/StockItemTestResultTable.tsx:407
msgid "Show results for installed stock items"
msgstr "インストールされているストックアイテムの結果を表示"

#: src/tables/stock/StockItemTestResultTable.tsx:411
msgid "Passed"
msgstr "合格"

#: src/tables/stock/StockItemTestResultTable.tsx:412
msgid "Show only passed tests"
msgstr "合格したテストのみを表示"

#: src/tables/stock/StockItemTestResultTable.tsx:417
msgid "Show results for enabled tests"
msgstr "有効化されたテストの結果を表示"

#: src/tables/stock/StockLocationTable.tsx:38
#~ msgid "structural"
#~ msgstr "structural"

#: src/tables/stock/StockLocationTable.tsx:39
msgid "Include sublocations in results"
msgstr "結果にサブロケーションを含める"

#: src/tables/stock/StockLocationTable.tsx:43
#~ msgid "external"
#~ msgstr "external"

#: src/tables/stock/StockLocationTable.tsx:44
msgid "Show structural locations"
msgstr "構造上の位置を表示"

#: src/tables/stock/StockLocationTable.tsx:49
msgid "Show external locations"
msgstr "外部ロケーションの表示"

#: src/tables/stock/StockLocationTable.tsx:53
msgid "Has location type"
msgstr "ロケーションタイプ"

#: src/tables/stock/StockLocationTable.tsx:58
msgid "Filter by location type"
msgstr "ロケーションタイプによる絞り込み"

#: src/tables/stock/StockLocationTable.tsx:105
#: src/tables/stock/StockLocationTable.tsx:160
msgid "Add Stock Location"
msgstr "ストックロケーションの追加"

#: src/tables/stock/StockLocationTable.tsx:129
msgid "Set Parent Location"
msgstr "親の位置の設定"

#: src/tables/stock/StockLocationTable.tsx:149
msgid "Set parent location for the selected items"
msgstr "選択されたアイテムの親ロケーションを設定"

#: src/tables/stock/StockTrackingTable.tsx:77
msgid "Added"
msgstr "追加"

#: src/tables/stock/StockTrackingTable.tsx:82
msgid "Removed"
msgstr "削除されました"

#: src/tables/stock/StockTrackingTable.tsx:206
msgid "Details"
msgstr "詳細"

#: src/tables/stock/StockTrackingTable.tsx:221
msgid "No user information"
msgstr "ユーザー情報なし"

#: src/tables/stock/TestStatisticsTable.tsx:34
#: src/tables/stock/TestStatisticsTable.tsx:64
#~ msgid "Total"
#~ msgstr "Total"

#: src/views/MobileAppView.tsx:25
msgid "Mobile viewport detected"
msgstr "モバイルビューポートが検出されました"

#: src/views/MobileAppView.tsx:25
#~ msgid "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
#~ msgstr "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."

#: src/views/MobileAppView.tsx:28
msgid "InvenTree UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
msgstr "InvenTree UIはタブレットとデスクトップに最適化されています。"

#: src/views/MobileAppView.tsx:34
msgid "Read the docs"
msgstr "ドキュメントを読む"

#: src/views/MobileAppView.tsx:38
msgid "Ignore and continue to Desktop view"
msgstr "無視してデスクトップビューへ"

