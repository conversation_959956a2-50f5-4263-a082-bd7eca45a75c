---
title: Resources InvenTree receives
---

The InvenTree project is grateful to receive resources from various vendors free of charge.

Individuals and companies can also support via [GitHub sponsors](https://github.com/sponsors/inventree).

## Current supporters
- [DigitalOcean](https://inventree.org/digitalocean) - Cloud hosting provider, used to host the InvenTree demo instance
- [Crowdin](https://crowdin.com/) - Translation platform, used to manage the [InvenTree translations](../develop/contributing.md#translations) across backend, frontend and app
- [SonarQube Cloud](https://sonarcloud.io/) - Code quality and security analysis, used to track the code quality of the various components
- [Packager.io](https://packager.io/) - Linux package builder/hosting service, used to host the InvenTree debian packages
- [Codecov](https://codecov.io) - Code coverage as a service, used to track the code coverage of the various components
- [Netlify](https://www.netlify.com/) - Static site hosting provider, used to test deploy the frontend and website
- [Depot](https://depot.dev/?utm_source=inventree) - Docker build accelerator, used to build the multi-arch images for the InvenTree docker image

## Past supporters
Non cromprehensive list of past supporters. The project stops consuming resources for various reasons, this does not mean they are not good resources.
- [Coveralls](https://coveralls.io/) - Code coverage as a service
- [Deepsource](https://deepsource.io/) - Code quality and security analysis
