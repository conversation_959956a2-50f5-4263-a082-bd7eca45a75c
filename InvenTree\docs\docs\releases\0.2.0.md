---
title: Release 0.2.0
---

## Release 0.2.0

[Release 0.2.0](https://github.com/inventree/InvenTree/releases/tag/0.2.0) introduces some major new features!

## Background Worker

This release adds a "background worker" - a separately managed process which allows long-running or asynchronous tasks to be handled separately to web server requests.

This feature is critical for the InvenTree development path, allowing (in future releases) for complex tasks to be handled, such as email support, automatic report generation, and integration with third party services.

For more information on the background worker, refer to the [background tasks documentation](../settings/tasks.md).

!!! info "Installation"
    Instructions for managing the background worker process are included in the [installation guide](../start/install.md).

!!! warning "Upgrading"
    If you are upgrading your InvenTree installation from an older version, you will need to ensure that you are also now running the background worker process!

## Docker

The other major feature that `0.2.0` introduces is an officical docker installation guide.

The addition of the *Background Worker* process significantly increases the complexity of an InvenTree installation. Further, a robust *production grade* server requires a lot of work.

To simplify this, an official InvenTree docker image is available on [DockerHub](https://hub.docker.com/r/inventree/inventree).

!!! success "Docker Is the Way"
    Docker is now the recommended way to install InvenTree

Refer to the [docker setup guide](../start/docker.md) for further information!
