# Generated by Django 3.2.18 on 2023-03-14 12:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0054_companyattachment'),
        ('order', '0081_auto_20230314_0725'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='contact',
            field=models.ForeignKey(blank=True, help_text='Point of contact for this order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='company.contact', verbose_name='Contact'),
        ),
        migrations.AddField(
            model_name='returnorder',
            name='contact',
            field=models.ForeignKey(blank=True, help_text='Point of contact for this order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='company.contact', verbose_name='Contact'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='contact',
            field=models.ForeignKey(blank=True, help_text='Point of contact for this order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='company.contact', verbose_name='Contact'),
        ),
    ]
