---
title: Release 0.4.4
---

## Release 0.4.4

### Label and Report Options

[#1920](https://github.com/inventree/InvenTree/pull/1920) adds a user-configurable setting to select if generated PDF labels are displayed inline in the browser, or downloaded as an attachment. The same option can be configured separately for generated PDF reports.

### Secondary Modals

[#1922](https://github.com/inventree/InvenTree/pull/1922) reintroduces the concept of "secondary" modal forms, implemented within the new API forms framework.

### Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#1916](https://github.com/inventree/InvenTree/pull/1916) | Fixes bug on the *Part Settings* page which prevented editing of part parameter templates |
| [#1918](https://github.com/inventree/InvenTree/pull/1918) | Adds check for InvenTree version number before publishing releases to docker hub |
| [#1919](https://github.com/inventree/InvenTree/pull/1919) | Prevents problematic characters from being used for part parameter template names |
| [#1925](https://github.com/inventree/InvenTree/pull/1925) | Fixes bug in part pricing display |
