# Generated by Django 3.0.7 on 2020-11-09 12:46

from django.db import migrations, models
import part.settings


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0052_partrelated'),
    ]

    operations = [
        migrations.AlterField(
            model_name='part',
            name='active',
            field=models.BooleanField(default=True, help_text='Is this part active?', verbose_name='Active'),
        ),
        migrations.AlterField(
            model_name='part',
            name='component',
            field=models.BooleanField(default=part.settings.part_component_default, help_text='Can this part be used to build other parts?', verbose_name='Component'),
        ),
        migrations.AlterField(
            model_name='part',
            name='purchaseable',
            field=models.BooleanField(default=part.settings.part_purchaseable_default, help_text='Can this part be purchased from external suppliers?', verbose_name='Purchaseable'),
        ),
        migrations.AlterField(
            model_name='part',
            name='salable',
            field=models.BooleanField(default=part.settings.part_salable_default, help_text='Can this part be sold to customers?', verbose_name='Salable'),
        ),
        migrations.AlterField(
            model_name='part',
            name='trackable',
            field=models.BooleanField(default=part.settings.part_trackable_default, help_text='Does this part have tracking for unique items?', verbose_name='Trackable'),
        ),
        migrations.AlterField(
            model_name='part',
            name='virtual',
            field=models.BooleanField(default=False, help_text='Is this a virtual part, such as a software product or license?', verbose_name='Virtual'),
        ),
    ]
