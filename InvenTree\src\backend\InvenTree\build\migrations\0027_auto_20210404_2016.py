# Generated by Django 3.0.7 on 2021-04-04 20:16

import InvenTree.models
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0058_stockitem_packaging'),
        ('users', '0005_owner_model'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('build', '0026_auto_20210216_1539'),
    ]

    operations = [
        migrations.AlterField(
            model_name='build',
            name='completed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='builds_completed', to=settings.AUTH_USER_MODEL, verbose_name='completed by'),
        ),
        migrations.AlterField(
            model_name='build',
            name='completion_date',
            field=models.DateField(blank=True, null=True, verbose_name='Completion Date'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='build',
            name='creation_date',
            field=models.DateField(auto_now_add=True, verbose_name='Creation Date'),
        ),
        migrations.AlterField(
            model_name='build',
            name='issued_by',
            field=models.ForeignKey(blank=True, help_text='User who issued this build order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='builds_issued', to=settings.AUTH_USER_MODEL, verbose_name='Issued by'),
        ),
        migrations.AlterField(
            model_name='build',
            name='responsible',
            field=models.ForeignKey(blank=True, help_text='User responsible for this build order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='builds_responsible', to='users.Owner', verbose_name='Responsible'),
        ),
        migrations.AlterField(
            model_name='builditem',
            name='build',
            field=models.ForeignKey(help_text='Build to allocate parts', on_delete=django.db.models.deletion.CASCADE, related_name='allocated_stock', to='build.Build', verbose_name='Build'),
        ),
        migrations.AlterField(
            model_name='builditem',
            name='install_into',
            field=models.ForeignKey(blank=True, help_text='Destination stock item', limit_choices_to={'is_building': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='items_to_install', to='stock.StockItem', verbose_name='Install into'),
        ),
        migrations.AlterField(
            model_name='builditem',
            name='quantity',
            field=models.DecimalField(decimal_places=5, default=1, help_text='Stock quantity to allocate to build', max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Quantity'),
        ),
        migrations.AlterField(
            model_name='builditem',
            name='stock_item',
            field=models.ForeignKey(help_text='Source stock item', limit_choices_to={'belongs_to': None, 'sales_order': None}, on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to='stock.StockItem', verbose_name='Stock Item'),
        ),
        migrations.AlterField(
            model_name='buildorderattachment',
            name='attachment',
            field=models.FileField(help_text='Select file to attach', upload_to='attachments', verbose_name='Attachment'),
        ),
        migrations.AlterField(
            model_name='buildorderattachment',
            name='comment',
            field=models.CharField(blank=True, help_text='File comment', max_length=100, verbose_name='Comment'),
        ),
        migrations.AlterField(
            model_name='buildorderattachment',
            name='upload_date',
            field=models.DateField(auto_now_add=True, null=True, verbose_name='upload date'),
        ),
        migrations.AlterField(
            model_name='buildorderattachment',
            name='user',
            field=models.ForeignKey(blank=True, help_text='User', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
    ]
