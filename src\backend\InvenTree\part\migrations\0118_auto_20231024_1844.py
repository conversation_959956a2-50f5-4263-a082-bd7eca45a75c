# Generated by Django 3.2.22 on 2023-10-24 16:44

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0117_remove_part_responsible'),
    ]

    operations = [
        migrations.AlterField(
            model_name='partinternalpricebreak',
            name='price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='bom_cost_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='bom_cost_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='internal_cost_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='internal_cost_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='overall_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='overall_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='purchase_cost_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='purchase_cost_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='sale_history_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='sale_history_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='sale_price_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='sale_price_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='supplier_price_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='supplier_price_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='variant_cost_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partpricing',
            name='variant_cost_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partsellpricebreak',
            name='price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partstocktake',
            name='cost_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='partstocktake',
            name='cost_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
    ]
