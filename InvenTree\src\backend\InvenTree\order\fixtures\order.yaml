# PurchaseOrder and PurchaseOrderLineItem objects for testing

# Ordering some screws from ACME
- model: order.purchaseorder
  pk: 1
  fields:
    reference: 'PO-0001'
    reference_int: 1
    description: "Ordering some screws"
    supplier: 1
    status: 10  # Pending

# Ordering some screws from Zerg Corp
- model: order.purchaseorder
  pk: 2
  fields:
    reference: 'PO-0002'
    reference_int: 2
    description: "Ordering some more screws"
    supplier: 3
    status: 10  # Pending

- model: order.purchaseorder
  pk: 3
  fields:
    reference: 'PO-0003'
    reference_int: 3
    description: 'Another PO'
    supplier: 3
    status: 20  # Placed

- model: order.purchaseorder
  pk: 4
  fields:
    reference: 'PO-0004'
    reference_int: 4
    description: 'Another PO'
    supplier: 3
    status: 20  # Placed

- model: order.purchaseorder
  pk: 5
  fields:
    reference: 'PO-0005'
    reference_int: 5
    description: 'Another PO'
    supplier: 3
    status: 30  # Complete

- model: order.purchaseorder
  pk: 6
  fields:
    reference: 'PO-0006'
    reference_int: 6
    description: 'Another PO'
    supplier: 3
    status: 40  # Cancelled

  # for pricebreaks
- model: order.purchaseorder
  pk: 7
  fields:
    reference: 'PO-0007'
    reference_int: 7
    description: 'Another PO'
    supplier: 2
    status: 10  # Pending

# Add some line items against PO 0001

# 100 x ACME0001 (M2x4 LPHS)
- model: order.purchaseorderlineitem
  pk: 1
  fields:
    order: 1
    part: 1
    quantity: 100
    destination: 5 # Desk/Drawer_1

# 250 x ACME0002 (M2x4 LPHS)
# Partially received (50)
- model: order.purchaseorderlineitem
  pk: 2
  fields:
    order: 1
    part: 2
    quantity: 250
    received: 50

# 1000 x ACME0003
- model: order.purchaseorderlineitem
  pk: 3
  fields:
    order: 1
    part: 3
    quantity: 1000

# 100 x ZERGLPHS (M2x4 LPHS)
- model: order.purchaseorderlineitem
  pk: 22
  fields:
    order: 2
    part: 3
    quantity: 100

# 1 x R_4K7_0603
- model: order.purchaseorderlineitem
  pk: 23
  fields:
    order: 1
    part: 5
    quantity: 1

# An extra line item
- model: order.purchaseorderextraline
  pk: 1
  fields:
    order: 7
    reference: 'Freight cost'
    quantity: 1
