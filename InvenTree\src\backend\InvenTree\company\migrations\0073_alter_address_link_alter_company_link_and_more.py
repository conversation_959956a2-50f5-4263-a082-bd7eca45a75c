# Generated by Django 4.2.19 on 2025-02-21 13:46

import InvenTree.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("company", "0072_auto_20250221_1236"),
    ]

    operations = [
        migrations.AlterField(
            model_name="address",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to address information (external)",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="company",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external company information",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="company",
            name="website",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Company website URL",
                max_length=2000,
                verbose_name="Website",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="supplierpart",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="URL for external supplier part link",
                max_length=2000,
                null=True,
                verbose_name="Link",
            ),
        ),
    ]
