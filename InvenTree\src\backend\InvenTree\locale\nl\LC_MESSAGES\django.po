msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Dutch\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: nl\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Schakel tweestapsverificatie in voordat je iets anders kunt doen."

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "API eindpunt niet gevonden"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr "Lijst met items of filters moet worden opgegeven voor bulk bewerking"

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr "Items moeten worden opgegeven als een lijst"

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Ongeldige items lijst verstrekt"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr "Filters moeten als woordenboek worden opgegeven"

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Ongeldige filters opgegeven"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr "Alles filteren alleen gebruiken met True"

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr "Geen items die overeenkomen met de opgegeven criteria"

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "Gebruiker heeft geen rechten om dit model te bekijken"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "E-mailadres (opnieuw)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "E-mailadres bevestiging"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Er moet hetzelfde e-mailadres ingevoerd worden."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Het opgegeven primaire e-mailadres is ongeldig."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Het ingevoerde e-maildomein is niet goedgekeurd."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Ongeldige eenheid opgegeven ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Geen waarde opgegeven"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "{original} kon niet worden omgezet naar {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Ongeldige hoeveelheid ingevoerd"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Error details kunnen worden gevonden in het admin scherm"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Voer datum in"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Ongeldige decimale waarde"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Opmerkingen"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Waarde '{name}' verschijnt niet in patroonformaat"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Opgegeven waarde komt niet overeen met vereist patroon: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Kan niet meer dan 1000 items tegelijk serienummers geven."

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Leeg serienummer"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Duplicaat serienummer"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Ongeldige groep: {group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Groepsbereik {group} overschrijdt toegestane hoeveelheid ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Geen serienummers gevonden"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr "Hoeveelheid van unieke serienummers ({n}) moet overeenkomen met de hoeveelheid ({q})"

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Verwijder HTML tags van deze waarde"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "Gegevens bevatten verboden markdown inhoud"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Verbindingsfout"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Server reageerde met ongeldige statuscode"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Uitzondering opgetreden"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Server reageerde met ongeldige Content-Length waarde"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Afbeeldingsformaat is te groot"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Beelddownload overschrijdt de maximale grootte"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Externe server heeft lege reactie teruggegeven"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Opgegeven URL is geen geldig afbeeldingsbestand"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabisch"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgaars"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tsjechisch"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Deens"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Duits"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Grieks"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Engels"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spaans"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spaans (Mexicaans)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estlands"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Perzisch"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Fins"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Frans"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebreeuws"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Hongaars"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiaans"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japans"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Koreaans"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Litouws"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Lets"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Nederlands"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Noors"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Pools"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugees"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugees (Braziliaans)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Roemeens"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russisch"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slowaaks"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Sloveens"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Servisch"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Zweeds"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thais"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turks"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Oekraïnes"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamees"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Chinees (vereenvoudigd)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Chinees (traditioneel)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Log in op de app"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "E-mail"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Fout bij uitvoeren plug-in validatie"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Metadata moeten een python dict object zijn"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Plug-in metadata"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "JSON metadata veld, voor gebruik door externe plugins"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Onjuist opgemaakt patroon"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Onbekende opmaaksleutel gespecificeerd"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Vereiste opmaaksleutel ontbreekt"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "Referentieveld mag niet leeg zijn"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "Referentie moet overeenkomen met verplicht patroon"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Referentienummer is te groot"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Ongeldige keuze"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Naam"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Omschrijving"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Omschrijving (optioneel)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Pad"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Dubbele namen kunnen niet bestaan onder hetzelfde bovenliggende object"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Markdown notitie (optioneel)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Streepjescode gegevens"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Streepjescode van derden"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Hash van Streepjescode"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Unieke hash van barcode gegevens"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Bestaande barcode gevonden"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Taak mislukt"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Achtergrondtaak '{f}' is mislukt na {n} pogingen"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Serverfout"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "Er is een fout gelogd door de server."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Moet een geldig nummer zijn"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Valuta"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Selecteer valuta uit beschikbare opties"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Ongeldige waarde"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Externe afbeelding"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL van extern afbeeldingsbestand"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Afbeeldingen van externe URL downloaden is niet ingeschakeld"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Fout bij het downloaden van afbeelding van externe URL"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Ongeldige fysieke eenheid"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Geen geldige valutacode"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Status van bestelling"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Bovenliggende Productie"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Inclusief varianten"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Onderdeel"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Categorie"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Voorouderlijke bouw"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Toegewezen aan mij"

#: build/api.py:154
msgid "Assigned To"
msgstr "Toegewezen aan"

#: build/api.py:189
msgid "Created before"
msgstr "Gemaakt voor"

#: build/api.py:193
msgid "Created after"
msgstr "Gemaakt na"

#: build/api.py:197
msgid "Has start date"
msgstr "Heeft een startdatum"

#: build/api.py:205
msgid "Start date before"
msgstr "Vervaldatum voor"

#: build/api.py:209
msgid "Start date after"
msgstr "Vervaldatum na"

#: build/api.py:213
msgid "Has target date"
msgstr "Heeft doel datum"

#: build/api.py:221
msgid "Target date before"
msgstr "Doel datum voor"

#: build/api.py:225
msgid "Target date after"
msgstr "Doel datum na"

#: build/api.py:229
msgid "Completed before"
msgstr "Voltooid voor"

#: build/api.py:233
msgid "Completed after"
msgstr "Voltooid na"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr "Min. datum"

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr "Max. datum"

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Boomstructuur uitsluiten"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "Productie moet geannuleerd worden voordat het kan worden verwijderd"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Verbruiksartikelen"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Optioneel"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Samenstelling"

#: build/api.py:450
msgid "Tracked"
msgstr "Gevolgd"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Testbaar"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "Openstaande order"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Toegewezen"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Beschikbaar"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Productieorder"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Locatie"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Productieorders"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "Assemblage stuklijst is niet gevalideerd"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Bouw bestelling kan niet worden aangemaakt voor een inactief onderdeel"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Maken opdracht kan niet worden gemaakt voor een ontgrendeld onderdeel"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr "Bestellingen bouwen kan alleen extern worden vervuld voor aankochte onderdelen"

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Verantwoorde gebruiker of groep moet worden opgegeven"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "Bouworder onderdeel kan niet worden gewijzigd"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr "Doeldatum moet na startdatum zijn"

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Productieorderreferentie"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referentie"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Korte beschrijving van de build (optioneel)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Productieorder waar deze productie aan is toegewezen"

#: build/models.py:273
msgid "Select part to build"
msgstr "Selecteer onderdeel om te produceren"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Verkooporder Referentie"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Verkooporder waar deze productie aan is toegewezen"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Bronlocatie"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Selecteer de locatie waar de voorraad van de productie vandaan moet komen (laat leeg om vanaf elke standaard locatie te nemen)"

#: build/models.py:300
msgid "External Build"
msgstr "Externe bouw"

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr "Deze bouw opdracht is extern uitgevoerd"

#: build/models.py:306
msgid "Destination Location"
msgstr "Bestemmings Locatie"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Selecteer locatie waar de voltooide items zullen worden opgeslagen"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Productiehoeveelheid"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Aantal voorraaditems om te produceren"

#: build/models.py:322
msgid "Completed items"
msgstr "Voltooide voorraadartikelen"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Aantal voorraadartikelen die zijn voltooid"

#: build/models.py:328
msgid "Build Status"
msgstr "Productiestatus"

#: build/models.py:333
msgid "Build status code"
msgstr "Productiestatuscode"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Batchcode"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Batchcode voor deze productieuitvoer"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Aanmaakdatum"

#: build/models.py:356
msgid "Build start date"
msgstr "Bouw start datum"

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr "Geplande startdatum voor deze bestelling"

#: build/models.py:363
msgid "Target completion date"
msgstr "Verwachte opleveringsdatum"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Doeldatum voor productie voltooiing. Productie zal achterstallig zijn na deze datum."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Opleveringsdatum"

#: build/models.py:378
msgid "completed by"
msgstr "voltooid door"

#: build/models.py:387
msgid "Issued by"
msgstr "Uitgegeven door"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Gebruiker die de productieorder heeft gegeven"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Verantwoordelijke"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Gebruiker of groep verantwoordelijk voor deze bouwopdracht"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Externe Link"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Link naar externe URL"

#: build/models.py:410
msgid "Build Priority"
msgstr "Bouw prioriteit"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Prioriteit van deze bouwopdracht"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Project code"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Project code voor deze build order"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Verwijderen van taak om toewijzingen te voltooien mislukt"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Productieorder {build} is voltooid"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "Een productieorder is voltooid"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Serienummers moeten worden opgegeven voor traceerbare onderdelen"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Geen productie uitvoer opgegeven"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "Productie uitvoer is al voltooid"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "Productuitvoer komt niet overeen met de Productieorder"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "Hoeveelheid moet groter zijn dan nul"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "Hoeveelheid kan niet groter zijn dan aantal"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Build output {serial} heeft niet alle vereiste tests doorstaan"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Bouw order regel item"

#: build/models.py:1602
msgid "Build object"
msgstr "Bouw object"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Hoeveelheid"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Vereiste hoeveelheid voor bouwopdracht"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Productieartikel moet een productieuitvoer specificeren, omdat het hoofdonderdeel gemarkeerd is als traceerbaar"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Toegewezen hoeveelheid ({q}) mag de beschikbare voorraad ({a}) niet overschrijden"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "Voorraad item is te veel toegewezen"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "Toewijzing hoeveelheid moet groter zijn dan nul"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "Hoeveelheid moet 1 zijn voor geserialiseerde voorraad"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "Geselecteerde voorraadartikelen komen niet overeen met de BOM-regel"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Voorraadartikel"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Bron voorraadartikel"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Voorraad hoeveelheid toe te wijzen aan productie"

#: build/models.py:1924
msgid "Install into"
msgstr "Installeren in"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Bestemming voorraadartikel"

#: build/serializers.py:115
msgid "Build Level"
msgstr "Bouw level"

#: build/serializers.py:124
msgid "Part Name"
msgstr "Onderdeel naam"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Projectcode label"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Productieuitvoer"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "Productieuitvoer komt niet overeen met de bovenliggende productie"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "Uitvoeronderdeel komt niet overeen met productieorderonderdeel"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Deze productieuitvoer is al voltooid"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Deze productieuitvoer is niet volledig toegewezen"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Voer hoeveelheid in voor productie uitvoer"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Hoeveelheid als geheel getal vereist voor traceerbare onderdelen"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Geheel getal vereist omdat de stuklijst traceerbare onderdelen bevat"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Serienummers"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Voer serienummers in voor productieuitvoeren"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Voorraad locatie voor project uitvoer"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Serienummers automatisch toewijzen"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Vereiste artikelen automatisch toewijzen met overeenkomende serienummers"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "De volgende serienummers bestaan al of zijn ongeldig"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "Een lijst van productieuitvoeren moet worden verstrekt"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Voorraadlocatie voor geannuleerde outputs"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Toewijzingen weggooien"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Verwijder alle voorraadtoewijzingen voor geannuleerde outputs"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Reden voor annulering van bouworder(s)"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Locatie van voltooide productieuitvoeren"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Incomplete Toewijzing Accepteren"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Voltooi de uitvoer als de voorraad niet volledig is toegewezen"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Toegewezen voorraad gebruiken"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Verbruik elke voorraad die al is toegewezen aan deze build"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Verwijder Incomplete Uitvoeren"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Verwijder alle productieuitvoeren die niet zijn voltooid"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Niet toegestaan"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Accepteer zoals geconsumeerd onder deze bouwopdracht"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "De-alloceren voordat deze bouwopdracht voltooid wordt"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Overgealloceerde voorraad"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Hoe wilt u omgaan met extra voorraaditems toegewezen aan de bouworder"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Sommige voorraadartikelen zijn overalloceerd"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Accepteer Niet-toegewezen"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Accepteer dat voorraadartikelen niet volledig zijn toegewezen aan deze productieorder"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "Vereiste voorraad is niet volledig toegewezen"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Accepteer Onvolledig"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Accepteer dat het vereist aantal productieuitvoeren niet is voltooid"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "Vereiste productiehoeveelheid is voltooid"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "Bouw opdracht heeft open sub bouw orders"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "Bouwen moet in de productiestatus staan"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "Productieorder heeft onvolledige uitvoeren"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Productielijn"

#: build/serializers.py:877
msgid "Build output"
msgstr "Productieuitvoer"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "Productieuitvoer moet naar dezelfde productie wijzen"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Bouw lijn-item"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part moet naar hetzelfde onderdeel wijzen als de productieorder"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "Artikel moet op voorraad zijn"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Beschikbare hoeveelheid ({q}) overschreden"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Productieuitvoer moet worden opgegeven voor de toewijzing van gevolgde onderdelen"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Productieuitvoer kan niet worden gespecificeerd voor de toewijzing van niet gevolgde onderdelen"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Allocaties voor artikelen moeten worden opgegeven"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Voorraadlocatie waar onderdelen afkomstig zijn (laat leeg om van elke locatie te nemen)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Locatie uitsluiten"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Voorraadartikelen van deze geselecteerde locatie uitsluiten"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Uitwisselbare voorraad"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Voorraadartikelen op meerdere locaties kunnen uitwisselbaar worden gebruikt"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Vervangende Voorraad"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Toewijzing van vervangende onderdelen toestaan"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Optionele Items"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Alloceer optionele BOM items om bestelling te bouwen"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Starten van automatische toewijzing taak mislukt"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "BOM referentie"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "BOM onderdeel ID"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "BOM onderdeel naam"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "Bouwen"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Leveranciersonderdeel"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Toegewezen hoeveelheid"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Bouw referentie"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Naam categorie onderdeel"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Volgbaar"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Overgenomen"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Varianten toestaan"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "Stuklijstartikel"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "In bestelling"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "In productie"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr "Gepland om te bouwen"

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Externe voorraad"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Beschikbare Voorraad"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Beschikbare vervanging voorraad"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Beschikbare varianten voorraad"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Bezig"

#: build/status_codes.py:12
msgid "Production"
msgstr "Productie"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "In de wacht"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Geannuleerd"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Voltooid"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Voorraad vereist voor productieorder"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Achterstallige Productieorder"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Productieorder {bo} is nu achterstallig"

#: common/api.py:688
msgid "Is Link"
msgstr "Is koppeling"

#: common/api.py:696
msgid "Is File"
msgstr "Is een bestand"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "Gebruiker heeft geen toestemming om deze bijlagen te verwijderen"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "Gebruiker heeft geen toestemming om deze bijlage te verwijderen."

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Ongeldige valuta code"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Dubbele valutacode"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Geen geldige valuta codes opgegeven"

#: common/currency.py:146
msgid "No plugin"
msgstr "Geen plug-in gevonden"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Bijgewerkt"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Tijdstempel van laatste update"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "Unieke projectcode"

#: common/models.py:171
msgid "Project description"
msgstr "Projectbeschrijving"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Gebruiker of groep die verantwoordelijk is voor dit project"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "Instellingen"

#: common/models.py:780
msgid "Settings value"
msgstr "Instellingswaarde"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "Gekozen waarde is geen geldige optie"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Waarde moet een booleaanse waarde zijn"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Waarde moet een geheel getal zijn"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr "Waarde moet een geldig getal zijn"

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr "Waarde is niet geldig voor validatiecontrole"

#: common/models.py:914
msgid "Key string must be unique"
msgstr "Sleutelreeks moet uniek zijn"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Gebruiker"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Prijs pauze hoeveelheid"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Prijs"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Stukprijs op opgegeven hoeveelheid"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Eindpunt"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Eindpunt waarop deze webhook wordt ontvangen"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Naam van deze webhook"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Actief"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Is deze webhook actief"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Sleutel"

#: common/models.py:1437
msgid "Token for access"
msgstr "Token voor toegang"

#: common/models.py:1445
msgid "Secret"
msgstr "Geheim"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Gedeeld geheim voor HMAC"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "Bericht ID"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Unieke identificatie voor dit bericht"

#: common/models.py:1563
msgid "Host"
msgstr "Host"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Host waarvan dit bericht is ontvangen"

#: common/models.py:1572
msgid "Header"
msgstr "Koptekst"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Koptekst van dit bericht"

#: common/models.py:1580
msgid "Body"
msgstr "Berichtinhoud"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Inhoud van dit bericht"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Eindpunt waarop dit bericht is ontvangen"

#: common/models.py:1596
msgid "Worked on"
msgstr "Aan gewerkt"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Is het werk aan dit bericht voltooid?"

#: common/models.py:1723
msgid "Id"
msgstr "Id"

#: common/models.py:1725
msgid "Title"
msgstr "Titel"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Koppeling"

#: common/models.py:1729
msgid "Published"
msgstr "Gepubliceerd"

#: common/models.py:1731
msgid "Author"
msgstr "Auteur"

#: common/models.py:1733
msgid "Summary"
msgstr "Samenvatting"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Gelezen"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Is dit nieuwsitem gelezen?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Afbeelding"

#: common/models.py:1753
msgid "Image file"
msgstr "Afbeelding"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "Doel type voor deze afbeelding"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "Doel modelnummer voor deze afbeelding"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Aangepaste eenheid"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "Eenheid symbool moet uniek zijn"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Naam van de unit moet een geldig id zijn"

#: common/models.py:1843
msgid "Unit name"
msgstr "Naam van eenheid"

#: common/models.py:1850
msgid "Symbol"
msgstr "Symbool"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Optionele eenheid symbool"

#: common/models.py:1857
msgid "Definition"
msgstr "Definitie"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Definitie van eenheid"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Bijlage"

#: common/models.py:1933
msgid "Missing file"
msgstr "Ontbrekend bestand"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Externe link ontbreekt"

#: common/models.py:1971
msgid "Model type"
msgstr "Model type"

#: common/models.py:1972
msgid "Target model type for image"
msgstr "Doel type voor afbeelding"

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Bestand als bijlage selecteren"

#: common/models.py:1996
msgid "Comment"
msgstr "Opmerking"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Opmerking van bijlage"

#: common/models.py:2013
msgid "Upload date"
msgstr "Uploaddatum"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Datum waarop het bestand is geüpload"

#: common/models.py:2018
msgid "File size"
msgstr "Bestandsgrootte"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Bestandsgrootte in bytes"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Ongeldig modeltype opgegeven voor bijlage"

#: common/models.py:2077
msgid "Custom State"
msgstr "Aangepaste staat"

#: common/models.py:2078
msgid "Custom States"
msgstr "Aangepaste statussen"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "Referentie status set"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "Status set die met deze aangepaste status wordt uitgebreid"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Logische sleutel"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Staat logische sleutel die gelijk is aan deze staat in zakelijke logica"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Waarde"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr "De numerieke waarde die wordt opgeslagen in de modellendatabase"

#: common/models.py:2102
msgid "Name of the state"
msgstr "Naam van de toestand"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Label"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "Label dat in de frontend getoond wordt"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Kleur"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "Kleur die in de frontend getoond wordt"

#: common/models.py:2128
msgid "Model"
msgstr "Model"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "Model met deze staat is gekoppeld aan"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "Het model moet worden gekozen"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "Sleutel moet worden geselecteerd"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "Logische sleutel moet worden geselecteerd"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "Sleutel moet anders zijn dan logische sleutel"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr "Geldige referentie status klasse moet worden opgegeven"

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "Sleutel moet verschillen van de logische sleutels van de referentie status"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "Logische sleutel moet in de logische sleutels van de referentiestatus staan"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "Naam moet anders zijn dan de namen van de referentie status"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Keuzelijst"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "Selectielijst"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "Naam van de selectielijst"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "Beschrijving van de selectielijst"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Vergrendeld"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "Is deze selectielijst vergrendeld?"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "Kan deze selectielijst worden gebruikt?"

#: common/models.py:2255
msgid "Source Plugin"
msgstr "Bron plug-in"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr "Plug-in die de selectielijst biedt"

#: common/models.py:2261
msgid "Source String"
msgstr "Bron tekenreeks"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr "Optionele tekenreeks die de bron identificeert die voor deze lijst wordt gebruikt"

#: common/models.py:2271
msgid "Default Entry"
msgstr "Standaard vermelding"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr "Standaard vermelding voor deze selectielijst"

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Gecreëerd"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr "Datum en tijd waarop de selectielijst is aangemaakt"

#: common/models.py:2283
msgid "Last Updated"
msgstr "Laatst bijgewerkt"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr "Datum en tijd waarop de selectielijst voor het laatst is bijgewerkt"

#: common/models.py:2318
msgid "Selection List Entry"
msgstr "Selectielijst item"

#: common/models.py:2319
msgid "Selection List Entries"
msgstr "Selectielijst item"

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr "Selectielijst waaraan dit item hoort"

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr "Naam van de selectielijst"

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr "Label voor het item in de selectielijst"

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr "Beschrijving van het item in de selectielijst"

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr "Is dit item in deze lijst actief?"

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "Barcode Scan"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Gegevens"

#: common/models.py:2377
msgid "Barcode data"
msgstr "Barcode gegevens"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "Gebruiker die de barcode gescand heeft"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Tijdstempel"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "Datum en tijd van de streepjescode scan"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "Adres eindpunt dat de streepjescode verwerkt"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Inhoud"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "Contextgegevens voor de barcode scan"

#: common/models.py:2415
msgid "Response"
msgstr "Reactie"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "Reactiegegevens van de barcode scan"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Resultaat"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "Was de barcode succesvol gescand?"

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr "E-mailbericht"

#: common/models.py:2574
msgid "Email Messages"
msgstr "E-mail berichten"

#: common/models.py:2581
msgid "Announced"
msgstr "Aangekondigd"

#: common/models.py:2583
msgid "Sent"
msgstr "Verzonden"

#: common/models.py:2584
msgid "Failed"
msgstr "Mislukt"

#: common/models.py:2587
msgid "Delivered"
msgstr "Geleverd"

#: common/models.py:2595
msgid "Confirmed"
msgstr "Bevestigd"

#: common/models.py:2601
msgid "Inbound"
msgstr "Inkomend"

#: common/models.py:2602
msgid "Outbound"
msgstr "Uitgaand"

#: common/models.py:2607
msgid "No Reply"
msgstr "Geen antwoord"

#: common/models.py:2608
msgid "Track Delivery"
msgstr "Track levering"

#: common/models.py:2609
msgid "Track Read"
msgstr "Track gelezen"

#: common/models.py:2610
msgid "Track Click"
msgstr "Track Klik"

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr "Globaal ID"

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr "Identificatie voor dit bericht (kan worden geleverd door een extern systeem)"

#: common/models.py:2633
msgid "Thread ID"
msgstr "Discussie ID"

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr "Identificatie voor deze bericht draad (kan worden geleverd door een extern systeem)"

#: common/models.py:2644
msgid "Thread"
msgstr "Gesprek"

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr "Gekoppeld onderwerp voor dit bericht"

#: common/models.py:2661
msgid "Prioriy"
msgstr "Prioriteit"

#: common/models.py:2703
msgid "Email Thread"
msgstr "E-mail gesprekken"

#: common/models.py:2704
msgid "Email Threads"
msgstr "E-mail gesprekken"

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Sleutel"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr "Unieke sleutel voor deze thread (gebruikt om de conversatie te identificeren)"

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr "Unieke identificatie voor dit bericht"

#: common/models.py:2724
msgid "Started Internal"
msgstr "Intern gestart"

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr "Is dit onderwerp intern gestart?"

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr "Datum en tijd waarop de conversatie voor het laatst is bijgewerkt"

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr "Datum en tijd waarop de conversatie voor het laatst is bijgewerkt"

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nieuw: {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Een nieuwe order is aangemaakt en aan u toegewezen"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} is geannuleerd"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Een bestelling die aan u is toegewezen is geannuleerd"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Ontvangen items"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Artikelen zijn ontvangen tegen een inkooporder"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Items zijn ontvangen tegen een retour bestelling"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr "Geeft aan of de instelling overschreven wordt door een omgevingsvariabele"

#: common/serializers.py:147
msgid "Override"
msgstr "Overschrijven"

#: common/serializers.py:486
msgid "Is Running"
msgstr "Is actief"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Openstaande taken"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Geplande taken"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Mislukte taken"

#: common/serializers.py:519
msgid "Task ID"
msgstr "Taak ID"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "Unieke taak ID"

#: common/serializers.py:521
msgid "Lock"
msgstr "Vergrendel"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Tijdstip van vergrendeling"

#: common/serializers.py:523
msgid "Task name"
msgstr "Naam van de taak"

#: common/serializers.py:525
msgid "Function"
msgstr "Functie"

#: common/serializers.py:525
msgid "Function name"
msgstr "Functie naam"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Argumenten"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Taak argumenten"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Trefwoord argumenten"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Taak trefwoord argumenten"

#: common/serializers.py:640
msgid "Filename"
msgstr "Bestandsnaam"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Model type"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Gebruiker heeft geen toestemming om bijlagen voor dit model te maken of te bewerken"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr "Lijst met selecties is vergrendeld"

#: common/setting/system.py:97
msgid "No group"
msgstr "Geen groep"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "Website URL is vergrendeld door configuratie"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Opnieuw opstarten vereist"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "Een instelling is gewijzigd waarvoor een herstart van de server vereist is"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Migraties in behandeling"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Aantal nog openstaande database migraties"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr "Instantie Id"

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr "Unieke identificatie voor deze InvenTree instantie"

#: common/setting/system.py:199
msgid "Announce ID"
msgstr "Aankondiging ID"

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr "Kondig de instantie ID van de server aan in de server status info (ongeautoriseerd)"

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "ID Serverinstantie"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Stringbeschrijving voor de server instantie"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Gebruik de instantie naam"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Gebruik de naam van de instantie in de titelbalk"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Tonen `over` beperken"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Toon de `over` modal alleen aan superusers"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Bedrijfsnaam"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Interne bedrijfsnaam"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "Basis-URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "Basis URL voor serverinstantie"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Standaard Valuta"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Selecteer basisvaluta voor de berekening van prijzen"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Ondersteunde valuta"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Lijst van ondersteunde valuta codes"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Valuta update interval"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Hoe vaak te controleren op updates (nul om uit te schakelen)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "dagen"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Valuta update plug-in"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Munteenheid update plug-in om te gebruiken"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Download van URL"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Download van afbeeldingen en bestanden vanaf een externe URL toestaan"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Download limiet"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Maximale downloadgrootte voor externe afbeelding"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "User-agent gebruikt om te downloaden van URL"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Sta toe om de user-agent te overschrijven die gebruikt wordt om afbeeldingen en bestanden van externe URL te downloaden (laat leeg voor de standaard)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Strikte URL validatie"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Vereis schema specificatie bij het valideren van URLs"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Interval voor update"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Hoe vaak te controleren op updates (nul om uit te schakelen)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Automatische backup"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Automatische back-up van database- en mediabestanden inschakelen"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Automatische backup interval"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Geef het aantal dagen op tussen geautomatiseerde backup"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Interval Taak Verwijderen"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Resultaten van achtergrondtaken worden verwijderd na het opgegeven aantal dagen"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Error Log Verwijderings Interval"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Resultaten van achtergrondtaken worden verwijderd na het opgegeven aantal dagen"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Interval Verwijderen Notificatie"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Meldingen van gebruikers worden verwijderd na het opgegeven aantal dagen"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Streepjescodeondersteuning"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Schakel barcodescanner ondersteuning in in de webinterface"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "Sla de resultaten van de barcode op"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "Sla de barcode scan resultaten op in de database"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "Maximale aantal Barcode Scans"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "Maximum aantal resultaten van de barcode scan op te slaan"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Barcode Invoer Vertraging"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Barcode invoerverwerking vertraging"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Barcode Webcam Ondersteuning"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Barcode via webcam scannen in browser toestaan"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Barcode gegevens"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Geef barcode gegevens weer in browser als tekst"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "Streepjescode Plug-in"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "Plug-in om te gebruiken voor interne barcode data genereren"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Herzieningen onderdeel"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Revisieveld voor onderdeel inschakelen"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "Alleen assemblee revisie"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "Alleen revisies toestaan voor assemblageonderdelen"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Verwijderen uit Assemblage toestaan"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Verwijderen van onderdelen die in een groep worden gebruikt toestaan"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN Regex"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Regulier expressiepatroon voor het overeenkomende Onderdeel IPN"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Duplicaat IPN toestaan"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Toestaan dat meerdere onderdelen dezelfde IPN gebruiken"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Bewerken IPN toestaan"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Sta het wijzigen van de IPN toe tijdens het bewerken van een onderdeel"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Kopieer Onderdeel Stuklijstgegevens"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Kopieer standaard stuklijstgegevens bij het dupliceren van een onderdeel"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Kopieer Onderdeel Parametergegevens"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Parametergegevens standaard kopiëren bij het dupliceren van een onderdeel"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Kopieer Onderdeel Testdata"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Testdata standaard kopiëren bij het dupliceren van een onderdeel"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Kopiëer Categorieparameter Sjablonen"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Kopieer categorieparameter sjablonen bij het aanmaken van een onderdeel"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Sjabloon"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Onderdelen zijn standaard sjablonen"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Onderdelen kunnen standaard vanuit andere componenten worden samengesteld"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Onderdeel"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Onderdelen kunnen standaard worden gebruikt als subcomponenten"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Koopbaar"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Onderdelen kunnen standaard gekocht worden"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Verkoopbaar"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Onderdelen kunnen standaard verkocht worden"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Onderdelen kunnen standaard gevolgd worden"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtueel"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Onderdelen zijn standaard virtueel"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Verwante onderdelen tonen"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Verwante onderdelen voor een onderdeel tonen"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Initiële voorraadgegevens"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Aanmaken van eerste voorraad toestaan bij het toevoegen van een nieuw onderdeel"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Initiële leveranciergegevens"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Aanmaken van eerste leveranciersgegevens toestaan bij het toevoegen van een nieuw onderdeel"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Onderdelennaam Weergaveopmaak"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Opmaak om de onderdeelnaam weer te geven"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Standaardicoon voor onderdeel catagorie"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Standaardicoon voor onderdeel catagorie (leeg betekent geen pictogram)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Forceer Parameter Eenheden"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Als er eenheden worden opgegeven, moeten parameterwaarden overeenkomen met de opgegeven eenheden"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Minimaal aantal prijs decimalen"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Minimaal aantal decimalen om weer te geven bij het weergeven van prijsgegevens"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Maximum prijs decimalen"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Maximum aantal decimalen om weer te geven bij het weergeven van prijsgegevens"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Gebruik leveranciersprijzen"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Prijsvoordelen leveranciers opnemen in de totale prijsberekening"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Aankoopgeschiedenis overschrijven"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Historische order prijzen overschrijven de prijzen van de leverancier"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Gebruik voorraaditem prijzen"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Gebruik prijzen van handmatig ingevoerde voorraadgegevens voor prijsberekeningen"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Voorraad artikelprijs leeftijd"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Voorraaditems ouder dan dit aantal dagen uitsluiten van prijsberekeningen"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Gebruik variantprijzen"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Variantenprijzen opnemen in de totale prijsberekening"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Alleen actieve varianten"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Gebruik alleen actieve variantonderdelen voor het berekenen van variantprijzen"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Prijzen Herbouw interval"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Aantal dagen voordat de prijzen voor onderdelen automatisch worden bijgewerkt"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Interne Prijzen"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Inschakelen van interne prijzen voor onderdelen"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Interne prijs overschrijven"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Indien beschikbaar, interne prijzen overschrijven berekeningen van prijsbereik"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Printen van labels Inschakelen"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Printen van labels via de webinterface inschakelen"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "Label Afbeelding DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "DPI resolutie bij het genereren van afbeelginsbestanden voor label printer plugins"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Activeer Rapportages"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Activeer het genereren van rapporten"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Foutopsporingsmodus"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Rapporten genereren in debug modus (HTML uitvoer)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Log fouten"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Registreer fouten die optreden bij het genereren van rapporten"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Paginagrootte"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Standaard paginagrootte voor PDF rapporten"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Globaal unieke serienummers"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "Serienummers voor voorraaditems moeten globaal uniek zijn"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Verwijder uitgeputte voorraad"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Bepaalt standaard gedrag wanneer een voorraadartikel leeg is"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Batchcode Sjabloon"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Sjabloon voor het genereren van standaard batchcodes voor voorraadartikelen"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Verlopen Voorraad"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Verlopen voorraad functionaliteit inschakelen"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Verkoop Verlopen Voorraad"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Verkoop verlopen voorraad toestaan"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Voorraad Vervaltijd"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Aantal dagen voordat voorraadartikelen als verouderd worden beschouwd voor ze verlopen"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Produceer Verlopen Voorraad"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Sta productie met verlopen voorraad toe"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Voorraad Eigenaar Toezicht"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Eigenaarstoezicht over voorraadlocaties en items inschakelen"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Voorraadlocatie standaard icoon"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Standaard locatie pictogram (leeg betekent geen icoon)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Geïnstalleerde voorraad items weergeven"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Geïnstalleerde voorraadartikelen in voorraadtabellen tonen"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Controleer BOM bij het installeren van items"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Geïnstalleerde voorraad items moeten in de BOM voor het bovenliggende deel bestaan"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Sta 'Niet op voorraad overschrijving' toe"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Toestaan dat voorraadartikelen die niet op voorraad zijn worden overgebracht tussen voorraadlocaties"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Productieorderreferentiepatroon"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Vereist patroon voor het genereren van het Bouworderreferentieveld"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "Vereis verantwoordelijke eigenaar"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "Een verantwoordelijke eigenaar moet worden toegewezen aan elke bestelling"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "Vereist een actief onderdeel"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "Voorkom het maken van orders voor inactieve onderdelen"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "Vergrendeld onderdeel vereisen"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "Voorkom het maken van orders voor ontgrendelde onderdelen"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "Vereist een geldige BOM"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Voorkom het creëren van bouworders tenzij BOM is gevalideerd"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "Onderliggende bestellingen vereist"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "Voorkom voltooiing van de bouw tot alle sub orders gesloten zijn"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr "Externe Bouw Orders"

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr "Inschakelen externe build order functionaliteit"

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Blokkeren tot test geslaagd"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Voorkom dat de bouw van de uitvoer wordt voltooid totdat alle vereiste testen zijn geslaagd"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Retourorders inschakelen"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Retourorder functionaliteit inschakelen in de gebruikersinterface"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Retourorder referentie patroon"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Verplicht patroon voor het genereren van Retourorder referentie veld"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Bewerk voltooide retourorders"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Bewerken van retourorders toestaan nadat deze zijn voltooid"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Verkooporderreferentiepatroon"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Vereist patroon voor het genereren van het Verkooporderreferentieveld"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Standaard Verzending Verkooporder"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Aanmaken standaard verzending bij verkooporders inschakelen"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Bewerk voltooide verkooporders"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Bewerken van verkooporders toestaan nadat deze zijn verzonden of voltooid"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Verstuurde bestellingen markeren als voltooid"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Verkooporders gemarkeerd als verzonden zullen automatisch worden voltooid, zonder de status \"verzonden\""

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Inkooporderreferentiepatroon"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Vereist patroon voor het genereren van het Inkooporderreferentieveld"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Bewerk voltooide verkooporders"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Bewerken van inkooporders toestaan nadat deze zijn verzonden of voltooid"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr "Valuta converteren"

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr "Verander artikelwaarde naar basisvaluta bij het ontvangen van voorraad"

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Inkooporders automatisch voltooien"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Markeer orders automatisch als voltooid wanneer alle regelitems worden ontvangen"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Wachtwoord vergeten functie inschakelen"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Wachtwoord vergeten functie inschakelen op de inlogpagina's"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Registratie inschakelen"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Zelfregistratie voor gebruikers op de inlogpagina's inschakelen"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "SSO inschakelen"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "SSO inschakelen op de inlogpagina's"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "Schakel gebruikersregistratie met SSO in"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Zelfregistratie voor gebruikers middels SSO op de inlogpagina's inschakelen"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "SSO-groep synchroniseren inschakelen"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Inschakelen van het synchroniseren van InvenTree groepen met groepen geboden door de IdP"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "SSO groep sleutel"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "De naam van de groepen claim attribuut van de IdP"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "SSO groep kaart"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Een mapping van SSO-groepen naar lokale InvenTree groepen. Als de lokale groep niet bestaat, zal deze worden aangemaakt."

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "Verwijder groepen buiten SSO"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Of groepen die zijn toegewezen aan de gebruiker moeten worden verwijderd als ze geen backend zijn door de IdP. Het uitschakelen van deze instelling kan beveiligingsproblemen veroorzaken"

#: common/setting/system.py:959
msgid "Email required"
msgstr "E-mailadres verplicht"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Vereis gebruiker om e-mailadres te registreren bij aanmelding"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "SSO-gebruikers automatisch invullen"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Gebruikersdetails van SSO-accountgegevens automatisch invullen"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "E-mail twee keer"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Bij inschrijving gebruikers twee keer om hun e-mail vragen"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Wachtwoord tweemaal"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Laat gebruikers twee keer om hun wachtwoord vragen tijdens het aanmelden"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Toegestane domeinen"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Inschrijven beperken tot bepaalde domeinen (komma-gescheiden, beginnend met @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Groep bij aanmelding"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Groep waaraan nieuwe gebruikers zijn toegewezen op registratie. Als SSO-groepssynchronisatie is ingeschakeld, is deze groep alleen ingesteld als er geen groep vanuit de IdP kan worden toegewezen."

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "MFA afdwingen"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Gebruikers moeten multifactor-beveiliging gebruiken."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Controleer plugins bij het opstarten"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Controleer of alle plug-ins zijn geïnstalleerd bij het opstarten - inschakelen in container-omgevingen"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Controleren op plug-in updates"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Schakel periodieke controles voor updates voor geïnstalleerde plug-ins in"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "Activeer URL-integratie"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Plugins toestaan om URL-routes toe te voegen"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Activeer navigatie integratie"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Plugins toestaan om te integreren in navigatie"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "Activeer app integratie"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Activeer plug-ins om apps toe te voegen"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Activeer planning integratie"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Activeer plugin om periodiek taken uit te voeren"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Activeer evenement integratie"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Activeer plugin om op interne evenementen te reageren"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "Interface integratie activeren"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "Plug-ins inschakelen om te integreren in de gebruikersinterface"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr "E-mail integratie inschakelen"

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr "Schakel plug-ins in om uitgaande / inkomende mails te verwerken"

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr "Activeer project codes"

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr "Schakel projectcodes in voor het bijhouden van projecten"

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Externe locaties uitsluiten"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Automatische Voorraadcontrole Periode"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Gebruikers volledige namen weergeven"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Laat gebruikers volledige namen zien in plaats van gebruikersnamen"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr "Gebruikersprofielen tonen"

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr "Toon gebruikersprofielen op hun profielpagina"

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Inschakelen van teststation data"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Schakel teststation gegevensverzameling in voor testresultaten"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Inline labelweergave"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "PDF-labels in browser weergeven, in plaats van als bestand te downloaden"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Standaard label printer"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Instellen welke label printer standaard moet worden geselecteerd"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Inline rapport weergeven"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "PDF-rapporten in de browser weergeven, in plaats van als bestand te downloaden"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Zoek Onderdelen"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Onderdelen weergeven in zoekscherm"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Zoek leveranciersonderdelen"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Leveranciersonderdelen weergeven in zoekscherm"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Fabrikant onderdelen zoeken"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Fabrikant onderdelen weergeven in zoekscherm"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Inactieve Onderdelen Verbergen"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Inactieve verkooporders weglaten in het zoekvenster"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Zoek categorieën"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Toon onderdeelcategorieën in zoekvenster"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Zoek in Voorraad"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Toon voorraad items in zoekvenster"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Verberg niet beschikbare voorraad items"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Voorraadartikelen die niet beschikbaar zijn niet in het zoekvenster weergeven"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Locaties doorzoeken"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Toon voorraadlocaties in zoekvenster"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Zoek bedrijven"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Toon bedrijven in zoekvenster"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Zoek Bouworders"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Toon bouworders in zoekvenster"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Inkooporders Zoeken"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Toon inkooporders in het zoekvenster"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Inactieve Inkooporders Weglaten"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Inactieve inkooporders weglaten in het zoekvenster"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Verkooporders zoeken"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Toon verkooporders in het zoekvenster"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Inactieve Verkooporders Weglaten"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Inactieve verkooporders weglaten in het zoekvenster"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Verzendingen verkooporder doorzoeken"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Toon verkooporders in het zoekvenster"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Zoek retourorders"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Toon bouworders in zoekvenster"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Inactieve retourbestellingen weglaten"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Inactieve retourorders uitsluiten in zoekvenster"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Zoekvoorbeeld resultaten"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Aantal resultaten om weer te geven in elk gedeelte van het zoekvenster"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex zoeken"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Schakel reguliere expressies in zoekopdrachten in"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Hele woorden zoeken"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Zoekopdrachten geven resultaat voor hele woord overeenkomsten"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Zoek notities"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "Zoekopdrachten geven resultaten voor overeenkomsten met artikel notities"

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Escape-toets sluit formulieren"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Gebruik de Escape-toets om standaard formulieren te sluiten"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Vaste navigatiebalk"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "De navigatiebalk positie is gefixeerd aan de bovenkant van het scherm"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr "Navigatiepictogrammen"

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr "Pictogrammen weergeven in de navigatiebalk"

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Datum formaat"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Voorkeursindeling voor weergave van datums"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr "Toon laatste broodkruimel"

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr "Toon de huidige pagina in het kruimelpad"

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Foutrapportages ontvangen"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Meldingen ontvangen van systeemfouten"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Laatst gebruikte printer"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Sla de laatst gebruikte printer op voor een gebruiker"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Geen bijlage model type opgegeven"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Ongeldig bijlage type"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Minimale plaatsen mogen niet groter zijn dan het maximum"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Maximum aantal plaatsen kan niet minder zijn dan minimaal"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Een leeg domein is niet toegestaan."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Ongeldige domeinnaam: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "De waarde moet hoofdletters zijn"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "Waarde moet een geldige variabele id zijn"

#: company/api.py:141
msgid "Part is Active"
msgstr "Onderdeel is actief"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Fabrikant is actief"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Leveranciersonderdelen is actief"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Intern onderdeel is actief"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Leverancier is actief"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Fabrikant"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Bedrijf"

#: company/api.py:316
msgid "Has Stock"
msgstr "Heeft voorraad"

#: company/models.py:120
msgid "Companies"
msgstr "Bedrijven"

#: company/models.py:148
msgid "Company description"
msgstr "Bedrijf omschrijving"

#: company/models.py:149
msgid "Description of the company"
msgstr "Omschrijving van het bedrijf"

#: company/models.py:155
msgid "Website"
msgstr "Website"

#: company/models.py:156
msgid "Company website URL"
msgstr "URL bedrijfswebsite"

#: company/models.py:162
msgid "Phone number"
msgstr "Telefoonnummer"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Telefoonnummer voor contact"

#: company/models.py:171
msgid "Contact email address"
msgstr "Contact e-mailadres"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Contact"

#: company/models.py:178
msgid "Point of contact"
msgstr "Contactpunt"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Link naar externe bedrijfsinformatie"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Is dit bedrijf actief?"

#: company/models.py:203
msgid "Is customer"
msgstr "Is klant"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Verkoop je artikelen aan dit bedrijf?"

#: company/models.py:209
msgid "Is supplier"
msgstr "Is leverancier"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Koop je artikelen van dit bedrijf?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "Is fabrikant"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Fabriceert dit bedrijf onderdelen?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Standaardvaluta die gebruikt wordt voor dit bedrijf"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Adres"

#: company/models.py:355
msgid "Addresses"
msgstr "Adres"

#: company/models.py:412
msgid "Select company"
msgstr "Selecteer bedrijf"

#: company/models.py:417
msgid "Address title"
msgstr "Adres titel"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Titel die het adres beschrijft"

#: company/models.py:424
msgid "Primary address"
msgstr "Primair adres"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Instellen als primair adres"

#: company/models.py:430
msgid "Line 1"
msgstr "Lijn 1"

#: company/models.py:431
msgid "Address line 1"
msgstr "Adresregel 1"

#: company/models.py:437
msgid "Line 2"
msgstr "Lijn 2"

#: company/models.py:438
msgid "Address line 2"
msgstr "Adresregel 2"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "Post code"

#: company/models.py:451
msgid "City/Region"
msgstr "Plaats/regio"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Postcode plaats/regio"

#: company/models.py:458
msgid "State/Province"
msgstr "Staat/provincie"

#: company/models.py:459
msgid "State or province"
msgstr "Staat of provincie"

#: company/models.py:465
msgid "Country"
msgstr "Land"

#: company/models.py:466
msgid "Address country"
msgstr "Adres land"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Koerier verzend notities"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Opmerkingen voor verzending koerier"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Interne verzend notities"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Verzend notities voor intern gebruik"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Link naar adres gegevens (extern)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Fabrikant onderdeel"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Basis onderdeel"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Onderdeel selecteren"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Fabrikant selecteren"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "Fabrikant artikel nummer"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Fabrikant artikel nummer (MPN)"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "URL voor externe link van het fabrikant onderdeel"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Omschrijving onderdeel fabrikant"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Fabrikant onderdeel parameter"

#: company/models.py:635
msgid "Parameter name"
msgstr "Parameternaam"

#: company/models.py:642
msgid "Parameter value"
msgstr "Parameterwaarde"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Eenheden"

#: company/models.py:650
msgid "Parameter units"
msgstr "Parameter eenheden"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Pakket eenheden moeten compatibel zijn met de basis onderdeel eenheden"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Hoeveelheid moet groter zijn dan nul"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "Gekoppeld fabrikant onderdeel moet verwijzen naar hetzelfde basis onderdeel"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Leverancier"

#: company/models.py:829
msgid "Select supplier"
msgstr "Leverancier selecteren"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Voorraad beheers eenheid voor leveranciers"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Is dit leveranciersdeel actief?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Selecteer fabrikant onderdeel"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "URL voor link externe leveranciers onderdeel"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Omschrijving leveranciersdeel"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Opmerking"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "basisprijs"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimale kosten (bijv. voorraadkosten)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Verpakking"

#: company/models.py:892
msgid "Part packaging"
msgstr "Onderdeel verpakking"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Pakket hoeveelheid"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Totale hoeveelheid geleverd in één pakket. Laat leeg voor enkele afzonderlijke items."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "meerdere"

#: company/models.py:919
msgid "Order multiple"
msgstr "Order meerdere"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Beschikbare hoeveelheid van leverancier"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Beschikbaarheid bijgewerkt"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Datum van de laatste update van de beschikbaarheid gegevens"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Prijsverschil van leverancier"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr "Geeft als resultaat de string representatie voor het primaire adres. Deze eigenschap bestaat voor achterwaartse compatibiliteit."

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Standaardvaluta die gebruikt wordt voor deze leverancier"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Bedrijfsnaam"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "Op voorraad"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr "Fout opgetreden tijdens data export"

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr "Gegevensexport plug-in geeft onjuiste gegevensindeling terug"

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Exporteer formaat"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "Selecteer export bestandsindeling"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Plug-in exporteren"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "Export plug-in selecteren"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Additionele statusinformatie voor dit item"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Aangepaste status sleutel"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Aangepast"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Klasse"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Waardes"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Geplaatst"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Ongeldige statuscode"

#: importer/models.py:73
msgid "Data File"
msgstr "Data bestand"

#: importer/models.py:74
msgid "Data file to import"
msgstr "Te importeren databestand"

#: importer/models.py:83
msgid "Columns"
msgstr "Kolommen"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr "Doel modeltype voor deze importsessie"

#: importer/models.py:96
msgid "Import status"
msgstr "Status van importeren"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Veld standaard"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "Veld overschrijven"

#: importer/models.py:120
msgid "Field Filters"
msgstr "Veld filters"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "Sommige verplichte velden zijn niet toegewezen"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "De kolom is al toegewezen aan een database veld"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "Het veld is al toegewezen aan een data-kolom"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "Kolom toewijzing moet worden gekoppeld aan een geldige importsessie"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "Kolom bestaat niet in het gegevensbestand"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "Veld bestaat niet in het doel model"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "Geselecteerde veld is alleen lezen"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "Importeer sessie"

#: importer/models.py:471
msgid "Field"
msgstr "Veld"

#: importer/models.py:473
msgid "Column"
msgstr "Kolom"

#: importer/models.py:542
msgid "Row Index"
msgstr "Rij index"

#: importer/models.py:545
msgid "Original row data"
msgstr "Oorspronkelijke rij gegevens"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Fouten"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Geldig"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Niet ondersteunde gegevens bestandsindeling"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Kan het databestand niet openen"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Ongeldige afmetingen van gegevensbestand"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "Ongeldig veld standaard"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "Ongeldige veld overschrijvingen"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "Ongeldige veld filters"

#: importer/serializers.py:177
msgid "Rows"
msgstr "Rijen"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Lijst van te accepteren rij IDs"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Geen rijen opgegeven"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "Regel behoort niet tot deze sessie"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "Regel bevat ongeldige gegevens"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "Regel is al voltooid"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Initialiseren"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Toewijzing van kolommen"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Gegevens importeren"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Gegevens verwerken"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Gegevensbestand overschrijdt maximum grootte"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Gegevensbestand bevat geen kopteksten"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Gegevensbestand bevat te veel kolommen"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Gegevensbestand bevat te veel rijen"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Waarde moet een geldig woordenboek teken zijn"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Kopieën"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Aantal afdrukken voor elk label"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Verbonden"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Onbekend"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Afdrukken"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Geen media"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Het papier is vastgelopen"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Verbinding verbroken"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Label printer"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Direct labels afdrukken voor verschillende items."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Printer locatie"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Bereik de printer naar een specifieke locatie"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Naam van de machine"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Machine type"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Type machine"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Stuurprogramma"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Stuurprogramma gebruikt voor de machine"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Machines kunnen worden uitgeschakeld"

#: machine/models.py:95
msgid "Driver available"
msgstr "Stuurprogramma beschikbaar"

#: machine/models.py:100
msgid "No errors"
msgstr "Geen fouten"

#: machine/models.py:105
msgid "Initialized"
msgstr "Geïnitialiseerd"

#: machine/models.py:117
msgid "Machine status"
msgstr "Machine status"

#: machine/models.py:145
msgid "Machine"
msgstr "Machine"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Machine configuratie"

#: machine/models.py:162
msgid "Config type"
msgstr "Configuratie type"

#: order/api.py:121
msgid "Order Reference"
msgstr "Order Referentie"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "Uitmuntend"

#: order/api.py:165
msgid "Has Project Code"
msgstr "Heeft een projectcode"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Aangemaakt Door"

#: order/api.py:183
msgid "Created Before"
msgstr "Gemaakt vóór"

#: order/api.py:187
msgid "Created After"
msgstr "Gemaakt na"

#: order/api.py:191
msgid "Has Start Date"
msgstr "Heeft vervaldatum"

#: order/api.py:199
msgid "Start Date Before"
msgstr "Vervaldatum voor"

#: order/api.py:203
msgid "Start Date After"
msgstr "Vervaldatum na"

#: order/api.py:207
msgid "Has Target Date"
msgstr "Heeft doel datum"

#: order/api.py:215
msgid "Target Date Before"
msgstr "Doel datum voor"

#: order/api.py:219
msgid "Target Date After"
msgstr "Doel datum na"

#: order/api.py:270
msgid "Has Pricing"
msgstr "Heeft prijsstelling"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr "Voltooid voor"

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "Voltooid na"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr "Externe Bouw Opdracht"

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Bestellen"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "Bestelling voltooid"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Intern onderdeel"

#: order/api.py:578
msgid "Order Pending"
msgstr "Bestelling in behandeling"

#: order/api.py:958
msgid "Completed"
msgstr "Voltooid"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "Heeft verzending"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Inkooporder"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Verkooporder"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Retour bestelling"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Totaalprijs"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Totaalprijs van deze bestelling"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Valuta bestelling"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Valuta voor deze order (laat leeg om de standaard van het bedrijf te gebruiken)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr "Deze bestelling is vergrendeld en kan niet worden gewijzigd"

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "Contact komt niet overeen met het geselecteerde bedrijf"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr "Startdatum moet voor einddatum liggen"

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Bestelling beschrijving (optioneel)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Selecteer projectcode voor deze bestelling"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Link naar externe pagina"

#: order/models.py:458
msgid "Start date"
msgstr "Start datum"

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr "Geplande startdatum voor deze bestelling"

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Streefdatum"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Verwachte datum voor levering van de bestelling. De bestelling wordt achterstallig na deze datum."

#: order/models.py:487
msgid "Issue Date"
msgstr "Datum van uitgifte"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Order uitgegeven op datum"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Gebruiker of groep verantwoordelijk voor deze order"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Contactpunt voor deze volgorde"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Bedrijf adres voor deze bestelling"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Orderreferentie"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Status"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Inkooporder status"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Bedrijf waar de artikelen van worden besteld"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Leveranciersreferentie"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Order referentiecode van leverancier"

#: order/models.py:654
msgid "received by"
msgstr "ontvangen door"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Order voltooid op datum"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Bestemming"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr "Bestemming voor ontvangen items"

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Onderdeelleverancier moet overeenkomen met de Inkooporderleverancier"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "Artikelregel komt niet overeen met inkooporder"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "Hoeveelheid moet een positief getal zijn"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Klant"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Bedrijf waaraan de artikelen worden verkocht"

#: order/models.py:1318
msgid "Sales order status"
msgstr "Verkooporder status"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Klantreferentie "

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Klant order referentiecode"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Verzenddatum"

#: order/models.py:1343
msgid "shipped by"
msgstr "verzonden door"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "Bestelling is al afgerond"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "Order is al geannuleerd"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Alleen een open bestelling kan als voltooid worden gemarkeerd"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Bestelling kan niet worden voltooid omdat er onvolledige verzendingen aanwezig zijn"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "Order kan niet worden voltooid omdat er onvolledige artikelen aanwezig zijn"

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Order kan niet worden voltooid omdat er onvolledige artikelen aanwezig zijn"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr "De bestelling is vergrendeld en kan niet worden gewijzigd"

#: order/models.py:1711
msgid "Item quantity"
msgstr "Hoeveelheid artikelen"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Artikelregel referentie"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Artikel notities"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Doeldatum voor dit regelitem (laat leeg om de doeldatum van de bestelling te gebruiken)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Regelomschrijving (optioneel)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Additionele context voor deze regel"

#: order/models.py:1788
msgid "Unit price"
msgstr "Stukprijs"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "Inkooporder regel item"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "Leveranciersonderdeel moet overeenkomen met leverancier"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr "Bouw bestelling moet worden gemarkeerd als extern"

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr "Bestellingen kunnen alleen aan assemblageonderdelen worden gekoppeld"

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr "De bouw van het order deel moet overeenkomen met regel onderdeel"

#: order/models.py:1884
msgid "Supplier part"
msgstr "Leveranciersonderdeel"

#: order/models.py:1891
msgid "Received"
msgstr "Ontvangen"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Aantal ontvangen artikelen"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Inkoopprijs"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Aankoopprijs per stuk"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr "Externe Build Order moet aan deze regel voldoen"

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "Extra regel inkooporder"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "Verkooporder regel item"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtueel onderdeel kan niet worden toegewezen aan een verkooporder"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Alleen verkoopbare onderdelen kunnen aan een verkooporder worden toegewezen"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Verkoopprijs"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Prijs per stuk"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Verzonden"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Verzonden hoeveelheid"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "Verzending van verkooporder"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Datum van verzending"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Leveringsdatum"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Datum van levering van zending"

#: order/models.py:2221
msgid "Checked By"
msgstr "Gecontroleerd door"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Gebruiker die deze zending gecontroleerd heeft"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Zending"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Zendingsnummer"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Volgnummer"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Zending volginformatie"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "Factuurnummer"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Referentienummer voor bijbehorende factuur"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "Verzending is al verzonden"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "Zending heeft geen toegewezen voorraadartikelen"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "Verkooporder extra regel"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "Toewijzing verkooporder"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "Voorraadartikel is niet toegewezen"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Kan het voorraadartikel niet toewijzen aan een regel met een ander onderdeel"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Kan voorraad niet toewijzen aan een regel zonder onderdeel"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Toewijzingshoeveelheid kan niet hoger zijn dan de voorraadhoeveelheid"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "Hoeveelheid moet 1 zijn voor geserialiseerd voorraadartikel"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "Verkooporder komt niet overeen met zending"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Verzending komt niet overeen met verkooporder"

#: order/models.py:2451
msgid "Line"
msgstr "Regel"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Verzendreferentie verkooporder"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Artikel"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Selecteer voorraadartikel om toe te wijzen"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Voer voorraadtoewijzingshoeveelheid in"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Retour order referentie"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Bedrijf van waaruit items worden teruggestuurd"

#: order/models.py:2625
msgid "Return order status"
msgstr "Retour bestelling status"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "Retourneer bestelregel item"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr "Voorraad item moet worden opgegeven"

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr "Retour hoeveelheid overschrijdt voorraad hoeveelheid"

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr "Het retour aantal moet groter zijn dan nul"

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr "Ongeldige hoeveelheid voor geserialiseerde voorraad"

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Selecteer te retourneren product van de klant"

#: order/models.py:2910
msgid "Received Date"
msgstr "Ontvangst datum"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "De datum waarop dit retour item is ontvangen"

#: order/models.py:2923
msgid "Outcome"
msgstr "Resultaat"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "Resultaat van deze regel item"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "Kosten geassocieerd met teruggave of reparatie voor deze regel item"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr "Retourneren extra regel"

#: order/serializers.py:90
msgid "Order ID"
msgstr "Bestelling ID"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "ID van de bestelling om te dupliceren"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "Kopieer regels"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr "Kopieer regelitems uit de oorspronkelijke bestelling"

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr "Extra regels kopiëren"

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr "Extra regelitems van de oorspronkelijke bestelling kopiëren"

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Artikelen"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "Afgeronde regel items"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "Artikel dupliceren"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr "Specificeer opties voor het dupliceren van deze bestelling"

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "Ongeldige  order ID"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Leveranciers Naam"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "Order kan niet worden geannuleerd"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Toestaan order te sluiten met onvolledige regelitems"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "Bestelling heeft onvolledige regelitems"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "Order is niet open"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "Automatisch prijzen"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Koopprijs automatisch berekenen gebaseerd op leveranciers \n"
" onderdelen gegevens"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Valuta Inkoopprijs"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Items samenvoegen"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Items met hetzelfde onderdeel, bestemming en doeldatum samenvoegen in één regelitem"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "SKU"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Intern Onderdeelnummer"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "Interne naam onderdeel"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "Leveranciersonderdeel moet worden gespecificeerd"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "Inkooporder moet worden gespecificeerd"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "De leverancier moet overeenkomen met de inkooporder"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "Inkooporder moet overeenkomen met de leverancier"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Artikel"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Selecteer bestemmingslocatie voor ontvangen artikelen"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Voer batch code in voor inkomende voorraad items"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Vervaldatum"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr "Voer vervaldatum in voor inkomende voorraad items"

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Voer serienummers in voor inkomende voorraadartikelen"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr "Overschrijf verpakkingsinformatie voor binnenkomende voorraad"

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr "Extra opmerking voor inkomende voorraad items"

#: order/serializers.py:826
msgid "Barcode"
msgstr "Streepjescode"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Gescande streepjescode"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Streepjescode is al in gebruik"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Artikelen moeten worden opgegeven"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "Bestemmingslocatie moet worden opgegeven"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "Geleverde streepjescodewaarden moeten uniek zijn"

#: order/serializers.py:1066
msgid "Shipments"
msgstr "Verzendingen"

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Voltooide Verzendingen"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Valuta verkoopprijs"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr "Toegewezen items"

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Geen verzenddetails opgegeven"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "Artikelregel is niet gekoppeld aan deze bestelling"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "Hoeveelheid moet positief zijn"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Voer serienummers in om toe te wijzen"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "Verzending is al verzonden"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "Zending is niet gekoppeld aan deze bestelling"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Geen overeenkomst gevonden voor de volgende serienummers"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "De volgende serienummers zijn niet beschikbaar"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "Retourneer regel item"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "Artikelregel komt niet overeen met inkooporder"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "Regel item is al ontvangen"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "Artikelen kunnen alleen worden ontvangen tegen lopende bestellingen"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr "Hoeveelheid te retourneren"

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "Lijn prijs valuta"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Kwijt"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Retour"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "In Behandeling"

#: order/status_codes.py:105
msgid "Return"
msgstr "Retour"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Herstel"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Vervangen"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Restitutie"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Afwijzen"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Achterstallige inkooporder"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Inkooporder {po} is nu achterstallig"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Achterstallige Verkooporder"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Verkooporder {so} is nu achterstallig"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr "Achterstallige retour orders"

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr "Productieorder {ro} is nu achterstallig"

#: part/api.py:111
msgid "Starred"
msgstr "Favoriet"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "Filter op categorieën met ster"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Diepte"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "Filteren op categorie diepte"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "Hoogste niveau"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "Filteren op topniveau categorieën"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "Stapelen"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "Inclusief subcategorieën in gefilterde resultaten"

#: part/api.py:185
msgid "Parent"
msgstr "Bovenliggend"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Filter op bovenliggende categorie"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "Subcategorieën uitsluiten in de opgegeven categorie"

#: part/api.py:434
msgid "Has Results"
msgstr "Heeft resultaten"

#: part/api.py:660
msgid "Is Variant"
msgstr "Is een variant"

#: part/api.py:668
msgid "Is Revision"
msgstr "Is revisie"

#: part/api.py:678
msgid "Has Revisions"
msgstr "Heeft revisies"

#: part/api.py:859
msgid "BOM Valid"
msgstr "BOM Valid"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "Assemblage deel is testbaar"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "Component onderdeel is testbaar"

#: part/api.py:1576
msgid "Uses"
msgstr "Gebruik"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Onderdeel Categorie"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Onderdeel Categorieën"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Standaard locatie"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Standaard locatie voor onderdelen in deze categorie"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Structureel"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Onderdelen mogen niet rechtstreeks aan een structurele categorie worden toegewezen, maar kunnen worden toegewezen aan subcategorieën."

#: part/models.py:134
msgid "Default keywords"
msgstr "Standaard trefwoorden"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Standaard trefwoorden voor delen in deze categorie"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Pictogram"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Pictogram (optioneel)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "U kunt deze voorraadlocatie niet structureel maken omdat sommige voorraadartikelen er al in liggen!"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Onderdelen"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "Kan dit deel niet verwijderen omdat het vergrendeld is"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Kan dit deel niet verwijderen omdat het nog actief is"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Kan dit deel niet verwijderen omdat het in een groep gebruikt is"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "{self}' kan niet worden gebruikt in BOM voor '{parent}' (recursief)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "{parent}' wordt gebruikt in BOM voor '{self}' (recursief)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN moet overeenkomen met regex patroon {pattern}"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "Onderdeel kan geen herziening van zichzelf zijn"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Kan geen revisie maken van een onderdeel dat al een revisie is"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "Revisie code moet worden opgegeven"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "Herzieningen zijn alleen toegestaan voor assemblageonderdelen"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "Kan geen revisie maken van een sjabloon onderdeel"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "Bovenliggend onderdeel moet naar dezelfde sjabloon verwijzen"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Voorraadartikel met dit serienummer bestaat al"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "Dubbele IPN niet toegestaan in deelinstellingen"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "Dubbele onderdeel revisie bestaat al."

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Onderdeel met deze naam, IPN en Revisie bestaat al."

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Onderdelen kunnen niet worden toegewezen aan categorieën van structurele onderdelen!"

#: part/models.py:1051
msgid "Part name"
msgstr "Onderdeel naam"

#: part/models.py:1056
msgid "Is Template"
msgstr "Is een sjabloon"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Is dit deel van een sjabloon?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Is dit een variant van een ander deel?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Variant van"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Beschrijving (optioneel)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Sleutelwoorden"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Deel sleutelwoorden om de zichtbaarheid van de zoekresultaten te verbeteren"

#: part/models.py:1093
msgid "Part category"
msgstr "Onderdeel Categorie"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Onderdeel revisie of versienummer"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Revisie"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "Is dit deel een herziening van een ander deel?"

#: part/models.py:1119
msgid "Revision Of"
msgstr "Revisie van"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Waar wordt dit item normaal opgeslagen?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Standaard leverancier"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Standaardleverancier"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Standaard verval datum"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Verlooptijd (in dagen) voor voorraadartikelen van dit deel"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Minimum voorraad"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Minimaal toegelaten stock niveau"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Eenheden voor dit onderdeel"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Kan dit onderdeel uit andere delen worden gebouwd?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Kan dit onderdeel gebruikt worden om andere onderdelen te bouwen?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Heeft dit onderdeel een tracking voor unieke items?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "Kunnen de testresultaten van dit onderdeel tegen dit onderdeel worden geregistreerd?"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Kan dit onderdeel worden gekocht van externe leveranciers?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Kan dit onderdeel aan klanten worden verkocht?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Is dit onderdeel actief?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "Vergrendelde onderdelen kunnen niet worden bewerkt"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Is dit een virtueel onderdeel, zoals een softwareproduct of licentie?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "BOM checksum"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Checksum van BOM opgeslagen"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "BOM gecontroleerd door"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "BOM gecontroleerd datum"

#: part/models.py:1312
msgid "Creation User"
msgstr "Aanmaken gebruiker"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Eigenaar verantwoordelijk voor dit deel"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Verkopen van meerdere"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Valuta die gebruikt wordt voor de cache berekeningen"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Minimale BOM kosten"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Minimale kosten van onderdelen"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Maximale BOM kosten"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Maximale kosten van onderdelen"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Minimale aankoop kosten"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Minimale historische aankoop kosten"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Maximale aanschaf kosten"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Maximum historische aankoop kosten"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Minimale interne prijs"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Minimale kosten op basis van interne prijsschommelingen"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Maximale interne prijs"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Maximale kosten gebaseerd op interne prijsvoordelen"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Minimale leverancier prijs"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Minimale prijs van onderdeel van externe leveranciers"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Maximale leverancier prijs"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Maximale prijs van onderdeel van externe leveranciers"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Minimale variant kosten"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Berekende minimale kosten van variant onderdelen"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Maximale variant kosten"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Berekende maximale kosten van variant onderdelen"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Minimale kostprijs"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Overschrijf minimale kosten"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Maximale kosten"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Overschrijf maximale kosten"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Berekende minimale kosten"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Berekende totale maximale kosten"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Minimale verkoop prijs"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Minimale verkoopprijs gebaseerd op prijsschommelingen"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Maximale verkoop prijs"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Maximale verkoopprijs gebaseerd op prijsschommelingen"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Minimale verkoop prijs"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Minimale historische verkoop prijs"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Maximale verkoop prijs"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Maximale historische verkoop prijs"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Onderdeel voor voorraadcontrole"

#: part/models.py:3444
msgid "Item Count"
msgstr "Getelde items"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Aantal individuele voorraadvermeldingen op het moment van voorraadcontrole"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Totale voorraad op het moment van voorraadcontrole"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Datum"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Datum waarop voorraad werd uitgevoerd"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Minimale voorraadprijs"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Geschatte minimum kosten van de voorraad op de hand"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Maximale voorraadkosten"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Geschatte maximale kosten van de hand van voorraad"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "Periodieke verkoopprijs voor onderdelen"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "Sjabloon test onderdeel"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Ongeldige sjabloonnaam - moet minstens één alfanumeriek teken bevatten"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "Keuzes moeten uniek zijn"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "Test sjablonen kunnen alleen worden gemaakt voor testbare onderdelen"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "Test template met dezelfde sleutel bestaat al voor een deel"

#: part/models.py:3684
msgid "Test Name"
msgstr "Test naam"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Geef een naam op voor de test"

#: part/models.py:3691
msgid "Test Key"
msgstr "Test sleutel"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "Vereenvoudigde sleutel voor de test"

#: part/models.py:3699
msgid "Test Description"
msgstr "Test beschrijving"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Voer beschrijving in voor deze test"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Ingeschakeld"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "Is deze test ingeschakeld?"

#: part/models.py:3709
msgid "Required"
msgstr "Vereist"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Is deze test nodig om te doorlopen?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Waarde vereist"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Heeft deze test een waarde nodig bij het toevoegen van een testresultaat?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Vereist bijlage"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Vereist deze test een bestandsbijlage bij het toevoegen van een testresultaat?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "Keuzes"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "Geldige keuzes voor deze parameter (komma gescheiden)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "Sjabloon deel parameter"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "Checkbox parameters kunnen geen eenheden bevatten"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "Checkbox parameters kunnen geen eenheden bevatten"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "De template van de parameter moet uniek zijn"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Parameternaam"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Fysieke eenheden voor deze parameter"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Parameter omschrijving"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Selectievakje"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Is deze parameter een selectievak?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Geldige keuzes voor deze parameter (komma gescheiden)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr "Lijst met selecties voor deze parameter"

#: part/models.py:3931
msgid "Part Parameter"
msgstr "Onderdeel parameters"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "Parameter kan niet worden gewijzigd - onderdeel is vergrendeld"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Ongeldige keuze voor parameter waarde"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Hoofd onderdeel"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Parameter sjabloon"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Parameterwaarde"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Optioneel notities veld"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "Sjabloon categorie parameters onderdeel"

#: part/models.py:4176
msgid "Default Value"
msgstr "Standaard waarde"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Standaard Parameter Waarde"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "BOM item kan niet worden gewijzigd - assemblage is vergrendeld "

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "BOM item kan niet worden gewijzigd - assemblage is vergrendeld"

#: part/models.py:4363
msgid "Select parent part"
msgstr "Selecteer boven liggend onderdeel"

#: part/models.py:4373
msgid "Sub part"
msgstr "Sub onderdeel"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Selecteer onderdeel dat moet worden gebruikt in BOM"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "BOM hoeveelheid voor dit BOM item"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Dit BOM item is optioneel"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Dit BOM item is verbruikbaar (het wordt niet bijgehouden in build orders)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "Artikelregel referentie"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "BOM item notities"

#: part/models.py:4451
msgid "Checksum"
msgstr "Controle som"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "BOM lijn controle som"

#: part/models.py:4457
msgid "Validated"
msgstr "Goedgekeurd"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Dit BOM item is goedgekeurd"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Wordt overgenomen"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Dit BOM item wordt overgenomen door BOMs voor variant onderdelen"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Voorraaditems voor variant onderdelen kunnen worden gebruikt voor dit BOM artikel"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "Hoeveelheid moet een geheel getal zijn voor trackable onderdelen"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "Onderdeel moet gespecificeerd worden"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "BOM Item vervangingen bewerken"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "Vervanging onderdeel kan niet hetzelfde zijn als het hoofddeel"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Bovenliggend BOM item"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Vervanging onderdeel"

#: part/models.py:4798
msgid "Part 1"
msgstr "Eerste deel"

#: part/models.py:4806
msgid "Part 2"
msgstr "Tweede deel"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "Selecteer gerelateerd onderdeel"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr "Opmerking voor deze relatie"

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Onderdeel relatie kan niet worden gecreëerd tussen een deel en zichzelf"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "Dubbele relatie bestaat al"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "Bovenliggende categorie"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "Bovenliggende onderdeel categorie"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Subcategorieën"

#: part/serializers.py:202
msgid "Results"
msgstr "Resultaten"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "Aantal resultaten opgenomen ten opzichte van deze template"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Inkooporder voor dit voorraadartikel"

#: part/serializers.py:275
msgid "File is not an image"
msgstr "Bestand is geen afbeelding"

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "Aantal onderdelen die deze sjabloon gebruiken"

#: part/serializers.py:480
msgid "Original Part"
msgstr "Oorspronkelijk onderdeel"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Selecteer origineel onderdeel om te dupliceren"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Afbeelding kopiëren"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Afbeelding kopiëren van het oorspronkelijke onderdeel"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Copy BOM"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Kopieer materiaal van het oorspronkelijke deel"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Parameters kopiëren"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Parameter data kopiëren van het originele onderdeel"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Notities kopiëren"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Kopieer notities van het originele deel"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr "Tests kopiëren"

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr "Test sjablonen kopiëren van het originele deel"

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Eerste voorraad hoeveelheid"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Specificeer de initiële voorraad hoeveelheid voor dit onderdeel. Als het aantal nul is, wordt er geen voorraad toegevoegd."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Eerste voorraad locatie"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Specificeer locatie van de eerste voorraad voor dit onderdeel"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Selecteer leverancier (of laat leeg om niets in te vullen)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Selecteer fabrikant (of laat leeg om niets in te vullen)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Fabrikant artikel nummer"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "Geselecteerde onderneming is geen geldige leverancier"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "Geselecteerde bedrijf is geen geldige fabrikant"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "Fabrikant deel dat overeenkomt met deze MPN bestaat al"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "Leveranciersdeel dat overeenkomt met deze SKU bestaat al"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Categorie naam"

#: part/serializers.py:936
msgid "Building"
msgstr "Bouwen"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr "Hoeveelheid van dit deel dat momenteel in productie is"

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr "Er zal een onuitputtelijke hoeveelheid van dit deel worden gebouwd"

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Voorraadartikelen"

#: part/serializers.py:968
msgid "Revisions"
msgstr "Revisies"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Leveranciers"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Totale Voorraad"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "Niet toegewezen voorraad"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "Variant voorraad"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Dupliceer onderdeel"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Kopieer eerste gegevens uit een ander onderdeel"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Eerste voorraad"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Maak onderdeel met eerste voorraad"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Leveranciersgegevens"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Aanvankelijke leveranciersinformatie voor dit deel toevoegen"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Categorie parameters kopiëren"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Parameter sjablonen kopiëren uit geselecteerde onderdeel categorie"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Bestaande afbeelding"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "Bestandsnaam van een bestaande onderdeel afbeelding"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "Afbeeldingsbestand bestaat niet"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Valideer de gehele materiaalbon"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Kan bouwen"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr "Vereist voor bouworders"

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr "Toegewezen aan bouwen van orders"

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr "Vereist voor verkooporders"

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr "Toegewezen aan verkooporders"

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Minimale prijs"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "Overschrijf berekende waarde voor minimale prijs"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "Minimale prijs valuta"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Maximale prijs"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "Overschrijf de berekende waarde voor de maximale prijs"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "Maximale prijs valuta"

#: part/serializers.py:1498
msgid "Update"
msgstr "Bijwerken"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Prijzen voor dit onderdeel bijwerken"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Kan niet converteren van de verstrekte valuta naar {default_currency}"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "Minimumprijs mag niet hoger zijn dan de maximale prijs"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "Maximale prijs mag niet lager zijn dan de minimale prijs"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr "Selecteer de bovenliggende assemblage"

#: part/serializers.py:1716
msgid "Select the component part"
msgstr "Selecteer het onderdeel"

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Selecteer onderdeel om BOM van te kopiëren"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Bestaande gegevens verwijderen"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Verwijder bestaande BOM items voor het kopiëren"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Inclusief overgenomen"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Inclusief stuklijst BOM items die worden overgenomen van getemplated onderdelen"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Ongeldige regels overslaan"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Schakel deze optie in om ongeldige rijen over te slaan"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Verwijder vervangend deel"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Kopieer vervangende onderdelen bij dubbele stuklijst BOM items"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Lage voorraad melding"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "De beschikbare voorraad voor {part.name} is onder het ingestelde minimumniveau gedaald"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr "Ingebouwd"

#: plugin/api.py:92
msgid "Mandatory"
msgstr "verplicht"

#: plugin/api.py:107
msgid "Sample"
msgstr "Voorbeeld"

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Geïnstalleerd"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "De plug-in kan niet worden verwijderd omdat deze momenteel actief is"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Geen actie gespecificeerd"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Geen overeenkomende actie gevonden"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Geen overeenkomst gevonden voor streepjescodegegevens"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Overeenkomst gevonden voor streepjescodegegevens"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Model wordt niet ondersteund"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "Model instantie niet gevonden"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Barcode komt overeen met bestaand item"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Geen overeenkomende actie gevonden"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Geen overeenkomende leveranciers onderdelen gevonden"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Meerdere overeenkomende leveranciers onderdelen gevonden"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "Geen overeenkomende plug-in gevonden voor barcode gegevens"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Overeenkomende leverancier onderdeel"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Regel item is al ontvangen"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "Er komt geen plug-in overeen met de barcode van de leverancier"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Meerdere overeenkomende regelitems gevonden"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Geen overeenkomend voorraaditem gevonden"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Geen verkooporder opgegeven"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Streepjescode komt niet overeen met een bestaand voorraadartikel"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Voorraad item komt niet overeen met regelitem"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Onvoldoende voorraad beschikbaar"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Voorraad item toegewezen aan verkooporder"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Te weinig informatie"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "Gevonden overeenkomend item"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "Leveranciersdeel komt niet overeen met regelitem"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "Regelitem is al voltooid"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Verdere informatie vereist om regelitem te ontvangen"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Inkoopregel ontvangen"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "Kon geen regelitem ontvangen"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Gescande barcode gegevens"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Modelnaam om een streepjescode te genereren voor"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "Primaire sleutel van modelobject om barcode te genereren voor"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Inkooporder om items toe te wijzen"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr "Inkooporder is niet open"

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "Leverancier om items te ontvangen van"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Inkooporder om items tegen te ontvangen"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Inkooporder is niet geplaatst"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Locatie voor het ontvangen van items"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Kan geen structurele locatie selecteren"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr "Inkooporder regel item om items tegen te ontvangen"

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr "Voorraadartikelen automatisch toewijzen aan de inkooporder"

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Verkooporder om items toe te wijzen tegen"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr "Verkooporder is niet open"

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Verkoop bestelling regel item om artikelen tegen toe te wijzen"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Verkoop bestelling om items toe te wijzen aan"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Zending is al geleverd"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Toe te wijzen hoeveelheid"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Label afdrukken mislukt"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "Fout bij het renderen label naar PDF"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "Fout bij het renderen van label naar HTML"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Geen producten aangeboden om af te drukken"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Plug-in naam"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Kenmerk type"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Feature label"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Titel feature"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Kenmerk beschrijving"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Feature pictogram"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Feature opties"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Feature context"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Feature bron (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree barcodes"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Biedt ondersteuning voor barcodes"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "InvenTree bijdragers"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "Interne barcode formaat"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "Selecteer een interne streepjescode formaat"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON barcodes (menselijk leesbaar)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "Korte barcodes (ruimte geoptimaliseerd)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "Korte barcode voorvoegsel"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "Aanpassen van prefix voor korte streepjescodes, kan handig zijn voor omgevingen met meerdere InvenTree instanties"

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr "Automatisch builds aanmaken"

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr "Automatisch bouworders voor assemblages aanmaken"

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr "Automatische uitgifte orders"

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr "Automatisch bestellingen uitgeven op de toegewezen doeldatum"

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr "Automatisch uitgeven Bouw orders"

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr "Bouwopdracht automatisch uitgeven op de toegewezen doeldatum"

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr "Automatische uitgifte inkooporders"

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr "Automatisch inkooporders uitgeven op de toegekende doeldatum"

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr "Automatische uitgifte van verkooporders"

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr "Verkooporders automatisch uitgeven op de toegekende doeldatum"

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr "Automatisch uitgifte retour bestellingen"

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr "Retour orders automatisch uitgeven op de toegekende doeldatum"

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr "Uitgeven achterhaalde orders"

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr "Automatisch orders uitgeven die achterhaald zijn"

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr "Niveau"

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr "Voorraad gegevens"

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr "Inclusief voorraadgegevens"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr "Prijs gegevens"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr "Inclusief prijsgegevens"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr "Leveranciersgegevens"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr "Inclusief leveranciersgegevens"

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr "Fabrikant gegevens"

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr "Inclusief fabrikant gegevens"

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr "Vervang Data"

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr "Voeg vervangende deelgegevens toe"

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr "Parameter gegevens"

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr "Parametergegevens van onderdeel opnemen"

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr "Meerdere niveau BOM export"

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr "Biedt ondersteuning voor het exporteren van multi-level BOMs"

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr "BOM niveau"

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr "Vervanging {n}"

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr "Leverancier {n}"

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr "Leverancier {n} SKU"

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr "Leverancier {n} MPN"

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr "Fabrikant {n}"

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr "Fabrikant {n} MPN"

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr "InvenTree generieke exporteur"

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr "Biedt ondersteuning voor het exporteren van gegevens van InvenTree"

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr "Exporteer onderdeel parameter"

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr "Exporteerder voor gegevens van gedeeltelijke parameter"

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Slack inkomende webhook url"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "URL die wordt gebruikt om berichten te verzenden naar een slack kanaal"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Open link"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree valuta wisselkoers"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Standaard munteenheden omwisselen integratie"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr "Meldingen voor onderdelen"

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr "Gebruikers inlichten over wijzigingen in onderdelen"

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr "Meldingen verzenden"

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr "Stuur meldingen van wijzigingen in onderdelen aan geabonneerde gebruikers"

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr "Melding gewijzigd onderdeel"

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr "Het deel `{part.name}` is geactiveerd met een `{part_action}` event"

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF label printer"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Biedt ondersteuning voor het drukken van PDF labels"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Foutopsporing modus"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Schakel debug modus in - retourneert ruwe HTML in plaats van PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree machine label printer"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "Biedt ondersteuning voor het printen met een machine"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "laatst gebruikt"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "Opties"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Paginaformaat voor het label blad"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Labels overslaan"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Dit aantal labels bij het afdrukken van labels overslaan"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Rand"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "Print een rand rond elk label"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Liggend"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "Print het label blad in liggende modus"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr "Paginamarges"

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr "Marge rondom de pagina in mm"

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "InvenTree label plaat printer"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "Arrays multiple labels op een enkele plaat"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "Label is te groot voor pagina grootte"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "Geen labels gegenereerd"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "Integratie met leverancier - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Biedt ondersteuning voor het scannen van DigiKey barcodes"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "De leverancier die als 'DigiKey' fungeert"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "Integratie met leveranciers - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "Biedt ondersteuning voor het scannen van LCSC-barcodes"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "De leverancier die fungeert als 'LCSC'"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "Integratie met leverancier - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "Biedt ondersteuning voor het scannen van Mouser barcodes"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "De leverancier die als 'Mouser' optreedt"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "Integratie met leverancier - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "Biedt ondersteuning voor het scannen van TME barcodes  "

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "De leverancier die als \"TME” optreedt "

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "Alleen medewerker gebruikers kunnen plug-ins beheren"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "Plug-in installatie is uitgeschakeld"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "Plug-in succesvol geïnstalleerd"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Plug-in geïnstalleerd in {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "De plug-in is niet gevonden in het register"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "De plug-in is geen verpakte plug-in"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "Naam van plug-in pakket niet gevonden"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "Verwijderen van plug-in is uitgeschakeld"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "De plug-in kan niet worden verwijderd omdat deze momenteel actief is"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr "De plug-in is niet geïnstalleerd"

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr "Plug-in installatie niet gevonden"

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "Deïnstalleerde plug-in succesvol"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Plug-in configuratie"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Plug-in configuratie"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Sleutel van plug-in"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Plugin naam van de plug-in"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Pakket naam"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "Naam van het geïnstalleerde pakket, als de plug-in is geïnstalleerd via PIP"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Is de plug-in actief"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Voorbeeld plug-in"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Ingebouwde plug-in"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr "Mandatory Plugin"

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "Pakket plug-in"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "Plug-in"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Geen auteur gevonden"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "De plug-in '{p}' is niet compatibel met de huidige InvenTree versie {v}"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "De plug-in vereist minimaal versie {v}"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Plug-in vereist op de hoogste versie {v}"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "PO inschakelen"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "PO functionaliteit inschakelen in de InvenTree interface"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "API-sleutel"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Sleutel vereist voor toegang tot externe API"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Numeriek"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "Een numerieke instelling"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Keuze instellingen"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "Een instelling met meerdere keuzes"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Voorbeeld valuta uitwisselings plug-in"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree bijdragers"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Onderdeel panelen inschakelen"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Schakel aangepaste panelen in voor deelweergave"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Schakel order panelen in"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Schakel aangepaste panelen in voor inkooporders"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Kapotte panelen inschakelen"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Schakel defecte panelen in voor testen"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Dynamisch paneel inschakelen"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Activeer dynamische panelen om te testen"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Deel paneel"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Kapot dashboard item"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Dit is een gebroken dashboarditem - het zal niet meer renderen!"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Voorbeeld dashboard item"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Dit is een voorbeeld dashboard item. Het maakt een eenvoudige string van HTML inhoud weer."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Context dashboard item"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Beheerder dashboard item"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Dit is een enkel dashboard item voor admins."

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Bron bestand"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Pad naar het bronbestand voor administrator integratie"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Optionele context data voor de administrator integratie"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Bron URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Bron voor het pakket - dit kan een aangepast register of een VCS pad zijn"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Naam voor het Plug-in Pakket - kan ook een versie indicator bevatten"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versie"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Versie specifier voor de plug-in. Laat leeg voor de laatste versie."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Plug-in activeren bevestigen"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Dit zal de plug-in nu installeren in de huidige instantie. De instantie zal in onderhoud gaan."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installatie niet bevestigd"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Ofwel de pakketnaam van de URL moet worden opgegeven"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Volledige herladen"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Herlaad het plug-in register volledig"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Herladen forceren"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Forceer herladen van het plug-in register, zelfs als het al geladen is"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Plug-ins ophalen"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Verzamel plug-ins en voeg ze toe aan het register"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "Activeer plug-in"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "Deze plug-in activeren"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "Configuratie verwijderen"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "Verwijder de plug-in configuratie uit de database"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "Items"

#: report/api.py:114
msgid "Plugin not found"
msgstr "Plug-in niet gevonden"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr "Plug-in ondersteunt geen label printen"

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr "Ongeldige label afmetingen"

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr "Geen geldige items aan de template verstrekt"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Juridisch"

#: report/helpers.py:46
msgid "Letter"
msgstr "Brief"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr "Sjabloonbestand met deze naam bestaat al"

#: report/models.py:217
msgid "Template name"
msgstr "Template naam"

#: report/models.py:223
msgid "Template description"
msgstr "Template beschrijving"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr "Revisie nummer (auto verhogen)"

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr "Bevestig aan het model bij afdrukken"

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Sla rapport output op als bijlage ten opzichte van gekoppelde model instantie bij afdrukken"

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Bestandsnaam Patroon"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr "Patroon voor het genereren van bestandsnamen"

#: report/models.py:287
msgid "Template is enabled"
msgstr "Template is ingeschakeld"

#: report/models.py:294
msgid "Target model type for template"
msgstr "Doel type model voor sjabloon"

#: report/models.py:314
msgid "Filters"
msgstr "Filters"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "Sjabloon zoekfilters (door komma's gescheiden lijst van sleutel=waarde paren)"

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "Sjabloon bestand"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "Standaard paginagrootte voor PDF rapport"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "Rapportage weergeven in liggende stand"

#: report/models.py:393
msgid "Merge"
msgstr "Samenvoegen"

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr "Geef een enkel rapport tegen geselecteerde items"

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr "Rapport gegenereerd door template {self.name}"

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr "Fout bij genereren rapport"

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Breedte [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Label breedte, gespecificeerd in mm"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Hoogte [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Label hoogte, gespecificeerd in mm"

#: report/models.py:780
msgid "Error printing labels"
msgstr "Fout afdrukken van labels"

#: report/models.py:799
msgid "Snippet"
msgstr "Tekstfragment"

#: report/models.py:800
msgid "Report snippet file"
msgstr "Rapporteer snippet bestand"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Snippet bestandsbeschrijving"

#: report/models.py:825
msgid "Asset"
msgstr "Asset"

#: report/models.py:826
msgid "Report asset file"
msgstr "Rapporteer asset bestand"

#: report/models.py:833
msgid "Asset file description"
msgstr "Beschrijving asset bestand"

#: report/serializers.py:96
msgid "Select report template"
msgstr "Selecteer rapport template"

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr "Lijst van primaire productsleutels op te nemen in het verslag"

#: report/serializers.py:137
msgid "Select label template"
msgstr "Selecteer label sjabloon"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr "Plug-in printen"

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr "Selecteer de plug-in voor het afdrukken van labels"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR Code"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR code"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Materiaallijst"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Benodigde materialen"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Afbeelding onderdeel"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Uitgegeven"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Vereist Voor"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Uitgegeven door"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Leverancier is verwijderd"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Stukprijs"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Extra regel items"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Totaal"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Serienummer"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Toewijzingen"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Batch"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Voorraad locatie items"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Rapport voorraadcontrole"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Geïnstalleerde items"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Serienummer"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Test resultaten"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Test"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Geslaagd"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Niet geslaagd"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Geen resultaat (verplicht)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Geen resultaat"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Asset bestand bestaat niet"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Afbeelding bestand niet gevonden"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image tag vereist een onderdeel instantie"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "bedrijf_imagetag vereist een bedrijfsinstantie"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "Filter op locatie diepte"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr "Filter op topniveau locaties"

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "Inclusief sublocaties in gefilterde resultaten"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "Bovenliggende locatie"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "Filter op bovenliggende locatie"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr "Naam van onderdeel (hoofdletter ongevoelig)"

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr "Naam van onderdeel bevat (hoofdletter ongevoelig)"

#: stock/api.py:594
msgid "Part name (regex)"
msgstr "Naam onderdeel (regex)"

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr "Deel IPN (hoofdletter ongevoelig)"

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr "Onderdeel IPN bevat (hoofdletter ongevoelig)"

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr "Deel IPN (regex)"

#: stock/api.py:623
msgid "Minimum stock"
msgstr "Minimale voorraad"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "Maximale voorraad"

#: stock/api.py:630
msgid "Status Code"
msgstr "Status code"

#: stock/api.py:670
msgid "External Location"
msgstr "Externe locatie"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr "Verbruikt door productieorder"

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr "Geïnstalleerd in een ander voorraadartikel"

#: stock/api.py:868
msgid "Part Tree"
msgstr "Boomstructuur onderdeel"

#: stock/api.py:890
msgid "Updated before"
msgstr "Eerder bijgewerkt"

#: stock/api.py:894
msgid "Updated after"
msgstr "Bijgewerkt na"

#: stock/api.py:898
msgid "Stocktake Before"
msgstr "Voorraadcontrole voor"

#: stock/api.py:902
msgid "Stocktake After"
msgstr "Voorraadcontrole na"

#: stock/api.py:907
msgid "Expiry date before"
msgstr "Vervaldatum voor"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "Vervaldatum na"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "Verouderd"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "Hoeveelheid is vereist"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "Geldig onderdeel moet worden opgegeven"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "Het opgegeven leveranciers onderdeel bestaat niet"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Het leveranciersdeel heeft een pakketgrootte gedefinieerd, maar vlag use_pack_size niet ingesteld"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Serienummers kunnen niet worden meegeleverd voor een niet traceerbaar onderdeel"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "Voorraad locatie soort"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "Voorraad locatie soorten"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Standaardpictogram voor alle locaties waarvoor geen pictogram is ingesteld (optioneel)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Voorraadlocatie"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Voorraadlocaties"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Eigenaar"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Selecteer eigenaar"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Voorraaditems kunnen niet direct worden geplaatst op een structurele voorraadlocatie, maar kunnen zich op onderliggende locaties bevinden."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Extern"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Dit is een externe voorraadlocatie"

#: stock/models.py:233
msgid "Location type"
msgstr "Locatie type"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "Voorraad locatie type van deze locatie"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "U kunt deze voorraadlocatie niet structureel maken omdat sommige voorraadartikelen er al in liggen!"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr "Onderdeel moet gespecificeerd worden"

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Voorraaditems kunnen niet worden geplaatst in structurele voorraadlocaties!"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Voorraadartikel kan niet worden aangemaakt voor virtuele onderdelen"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Onderdeel type ('{self.supplier_part.part}') moet {self.part} zijn"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "Hoeveelheid moet 1 zijn voor item met een serienummer"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Serienummer kan niet worden ingesteld als de hoeveelheid groter is dan 1"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "Item kan niet tot zichzelf behoren"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "Item moet een bouw referentie hebben als is_building=True"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "Bouw referentie verwijst niet naar hetzelfde deel object"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Bovenliggend voorraad item"

#: stock/models.py:1028
msgid "Base part"
msgstr "Basis onderdeel"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Selecteer een leveranciersdeel voor dit voorraadartikel"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Waar bevindt zich dit voorraaditem?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "Het verpakken van dit voorraaditem is opgeslagen in"

#: stock/models.py:1064
msgid "Installed In"
msgstr "Geïnstalleerd in"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "Is dit item geïnstalleerd in een ander item?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Serienummer van dit item"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Batch code voor dit voorraaditem"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Voorraad hoeveelheid"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Bron Bouw"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Build voor dit voorraaditem"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "Verbruikt door"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "Bestelling bouwen welke dit voorraadartikel heeft verbruikt"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Inkooporder Bron"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Inkooporder voor dit voorraadartikel"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Bestemming Verkooporder"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Vervaldatum voor voorraadartikel. Voorraad zal worden beschouwd als verlopen na deze datum"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Verwijderen bij leegmaken"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Verwijder dit voorraadproduct wanneer de voorraad is leeg"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Enkele eenheidsprijs van de aankoop op het moment van aankoop"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "Omgezet tot onderdeel"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "Onderdeel is niet ingesteld als traceerbaar"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "Hoeveelheid moet heel getal zijn"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Hoeveelheid mag niet hoger zijn dan de beschikbare voorraad ({self.quantity})"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr "Serienummers moeten als lijst worden opgegeven"

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "Hoeveelheid komt niet overeen met serienummers"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "Testsjabloon bestaat niet"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "Voorraadartikel is toegewezen aan een verkooporder"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "Voorraad item is geïnstalleerd in een ander item"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "Voorraadartikel bevat andere producten"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "Voorraadartikel is aan een klant toegewezen"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "Voorraad item is momenteel in productie"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Geserialiseerde voorraad kan niet worden samengevoegd"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Dupliceer voorraadartikelen"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "Voorraadartikelen moeten hetzelfde onderdeel verwijzen"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "Voorraadartikelen moeten verwijzen naar dezelfde leveranciersdeel"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "De voorraad statuscodes moeten overeenkomen"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Voorraadartikel kan niet worden verplaatst omdat het niet op voorraad is"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr "Voorraad item volgen"

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Item notities"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr "Resultaat voorraad test resultaten"

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Waarde moet voor deze test worden opgegeven"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "Bijlage moet worden geüpload voor deze test"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr "Ongeldige waarde voor deze test"

#: stock/models.py:2951
msgid "Test result"
msgstr "Test resultaat"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Test uitvoer waarde"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Test resultaat bijlage"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Test notities"

#: stock/models.py:2978
msgid "Test station"
msgstr "Test station"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "De identificatie van het teststation waar de test werd uitgevoerd"

#: stock/models.py:2985
msgid "Started"
msgstr "Gestart"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "Het tijdstip van de start test"

#: stock/models.py:2992
msgid "Finished"
msgstr "Afgerond"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "Het tijdstip van de afgeronde test"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "Gegenereerde batch code"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "Selecteer build order"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "Selecteer het voorraaditem om een batchcode te genereren voor"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "Selecteer locatie om batch code voor te genereren"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "Selecteer onderdeel voor het genereren van batchcode voor"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "Selecteer bestelling"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "Voer aantal voor batch code in"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "Gegenereerd serienummer"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "Selecteer onderdeel voor het genereren van het serienummer voor"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "Aantal serienummers om te genereren"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "Test template voor dit resultaat"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "SjabloonID of testnaam moet worden opgegeven"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "De testtijd kan niet eerder zijn dan de starttijd van de test"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Bovenliggend Item"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr "Bovenliggende voorraad item"

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Gebruik pakketgrootte bij het toevoegen: de hoeveelheid gedefinieerd is het aantal pakketten"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Voer serienummers voor nieuwe items in"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "Leverancier artikelnummer"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "Verlopen"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Onderliggende items"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr "Items volgen"

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Inkoopprijs van dit voorraadartikel, per eenheid of pakket"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Aantal voorraaditems om serienummers voor te maken"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Hoeveelheid mag niet hoger zijn dan de beschikbare voorraad ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Locatie van bestemming"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Serienummers kunnen niet worden toegewezen aan dit deel"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "Serienummers bestaan al"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Selecteer voorraaditem om te installeren"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "Te installeren hoeveelheid"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "Voer de te installeren hoeveelheid items in"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "Transactienotitie toevoegen (optioneel)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "Te installeren hoeveelheid moet minimaal 1 zijn"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "Voorraadartikel is niet beschikbaar"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "Het geselecteerde deel zit niet in de materialen lijst"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "De te installeren hoeveelheid mag niet groter zijn dan de beschikbare hoeveelheid"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Bestemmingslocatie voor verwijderd item"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Selecteer onderdeel om voorraaditem om te zetten in"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "Het geselecteerde deel is geen geldige optie voor de omzetting"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Kan voorraadartikel niet converteren met toegewezen leverancier deel"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "Voorraad status code"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "Selecteer voorraadartikelen om status te wijzigen"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "Geen voorraaditems geselecteerd"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Sublocaties"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "Bovenliggende voorraad locatie"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "Onderdeel moet verkoopbaar zijn"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "Artikel is toegewezen aan een verkooporder"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "Artikel is toegewezen aan een productieorder"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Klant om voorraadartikelen toe te wijzen"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "Geselecteerde bedrijf is geen klant"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Voorraad toewijzing notities"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "Een lijst met voorraad artikelen moet worden opgegeven"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Voorraad samenvoegen notities"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Niet overeen komende leveranciers toestaan"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Toestaan dat voorraadartikelen met verschillende leveranciers onderdelen worden samengevoegd"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Sta onjuiste status toe"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Toestaan dat voorraadartikelen met verschillende statuscodes worden samengevoegd"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Er moeten ten minste twee voorraadartikelen worden opgegeven"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "Geen wijziging"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Voorraaditem primaire sleutel waarde"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr "Voorraad artikel is niet op voorraad"

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Voorraad transactie notities"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr "Volgend serienummer"

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr "Vorig serienummer"

#: stock/status_codes.py:11
msgid "OK"
msgstr "Ok"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Aandacht nodig"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Beschadigd"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Verwoest"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Afgewezen"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "In quarantaine geplaatst"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Verouderde volgcode"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Voorraaditem gemaakt"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Bewerken voorraadartikel"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Serienummer toegewezen"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Voorraad geteld"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Voorraad handmatig toegevoegd"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Voorraad handmatig verwijderd"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Locatie veranderd"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Voorraad bijgewerkt"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "Gemonteerd"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Gedemonteerd"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Geïnstalleerd componentartikel"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Verwijderd componentartikel"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Splits van bovenliggend item"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Splits onderliggende item"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Samengevoegde voorraadartikelen"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "Geconverteerd naar variant"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "Product aangemaakt"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Product voltooid"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "Build order uitvoer afgewezen"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Verbruikt door productieorder"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Verzonden onder verkooporder"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Ontvangen onder verkooporder"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Geretourneerd onder retourorder"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Naar klant verzonden"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Geretourneerd door klant"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Toestemming geweigerd"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "U heeft geen rechten om deze pagina te bekijken."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Authenticatie mislukt"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "U bent uitgelogd bij InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Pagina niet gevonden"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "De opgevraagde pagina bestaat niet"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Server fout"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "De %(inventree_title)s server heeft een interne fout veroorzaakt"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Raadpleeg de foutmelding in de admin interface voor verdere details"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Site is in onderhoud"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "De site is momenteel in onderhoud en zal binnenkort weer in bedrijf zijn!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Server herstart vereist"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Een instelling is gewijzigd waarvoor een herstart van de server vereist is"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Neem contact op met uw systeembeheerder voor meer informatie"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Wachtende database migraties"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Er zijn in afwachting van database migraties die aandacht vereisen"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Klik op de volgende link om deze order te bekijken"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Voorraad is vereist voor de volgende productieorder"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Productieorder %(build)s - In productie %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klik op de volgende link om deze productieorder te bekijken"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "De volgende onderdelen hebben een lage vereiste voorraad"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Vereiste Hoeveelheid"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Je ontvangt deze e-mail omdat je bent geabonneerd op notificaties van dit onderdeel "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klik op de volgende link om dit deel te bekijken"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimale hoeveelheid"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr "Je ontvangt deze e-mail omdat je bent geabonneerd op notificaties van dit onderdeel "

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Gebruikers"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Selecteer welke gebruikers zijn toegewezen aan deze groep"

#: users/admin.py:137
msgid "Personal info"
msgstr "Persoonlijke info"

#: users/admin.py:139
msgid "Permissions"
msgstr "Machtigingen"

#: users/admin.py:142
msgid "Important dates"
msgstr "Belangrijke data"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Token is ingetrokken"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token is verlopen"

#: users/models.py:100
msgid "API Token"
msgstr "API Token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API tokens"

#: users/models.py:137
msgid "Token Name"
msgstr "Token naam"

#: users/models.py:138
msgid "Custom token name"
msgstr "Aangepaste token naam"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Token vervaldatum"

#: users/models.py:152
msgid "Last Seen"
msgstr "Laatst gezien"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Laatste keer dat het token werd gebruikt"

#: users/models.py:157
msgid "Revoked"
msgstr "Intrekken"

#: users/models.py:235
msgid "Permission set"
msgstr "Toestemming set"

#: users/models.py:244
msgid "Group"
msgstr "Groep"

#: users/models.py:248
msgid "View"
msgstr "Weergeven"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Machtiging om items te bekijken"

#: users/models.py:252
msgid "Add"
msgstr "Toevoegen"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Machtiging om items toe te voegen"

#: users/models.py:256
msgid "Change"
msgstr "Wijzigen"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Machtigingen om items te bewerken"

#: users/models.py:262
msgid "Delete"
msgstr "Verwijderen"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Toestemming om items te verwijderen"

#: users/models.py:501
msgid "Bot"
msgstr "Bot"

#: users/models.py:502
msgid "Internal"
msgstr "Intern"

#: users/models.py:504
msgid "Guest"
msgstr "Gast"

#: users/models.py:513
msgid "Language"
msgstr "Taal"

#: users/models.py:514
msgid "Preferred language for the user"
msgstr "Voorkeurstaal voor gebruiker"

#: users/models.py:519
msgid "Theme"
msgstr "Thema"

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr "Instellingen voor webinterface als JSON - niet handmatig bewerken!"

#: users/models.py:525
msgid "Widgets"
msgstr "Widget"

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr "Instellingen voor de dashboard widgets als JSON - wijzig niet handmatig!"

#: users/models.py:534
msgid "Display Name"
msgstr "Naam weergeven"

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr "Gekozen weergavenaam voor de gebruiker"

#: users/models.py:541
msgid "Position"
msgstr "Functie"

#: users/models.py:542
msgid "Main job title or position"
msgstr "Titel of positie hoofdtaken"

#: users/models.py:549
msgid "User status message"
msgstr "Gebruiker status bericht"

#: users/models.py:556
msgid "User location information"
msgstr "Informatie over locatie gebruiker"

#: users/models.py:561
msgid "User is actively using the system"
msgstr "Gebruiker gebruikt actief het systeem"

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr "Voorkeursinformatie voor de gebruiker"

#: users/models.py:574
msgid "User Type"
msgstr "Gebruikers type"

#: users/models.py:575
msgid "Which type of user is this?"
msgstr "Welk type gebruiker is dit?"

#: users/models.py:581
msgid "Organisation"
msgstr "Organisatie"

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr "Gebruikers primaire organisatie/affiliatie"

#: users/models.py:590
msgid "Primary Group"
msgstr "Primaire groep"

#: users/models.py:591
msgid "Primary group for the user"
msgstr "Primaire groep van de gebruiker"

#: users/ruleset.py:26
msgid "Admin"
msgstr "Administrator"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Inkooporders"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Verkooporders"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Retour orders"

#: users/serializers.py:196
msgid "Username"
msgstr "Gebruikersnaam"

#: users/serializers.py:199
msgid "First Name"
msgstr "Voornaam :"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Voornaam van de gebruiker"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Achternaam"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Achternaam van de gebruiker"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "E-mailadres van de gebruiker"

#: users/serializers.py:326
msgid "Staff"
msgstr "Medewerkers"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Heeft deze gebruiker medewerker machtigingen"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Administrator "

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "Is deze gebruiker een administrator "

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Is dit gebruikersaccount actief"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr "Enkel een supergebruiker kan dit veld aanpassen"

#: users/serializers.py:376
msgid "Password"
msgstr "Wachtwoord"

#: users/serializers.py:377
msgid "Password for the user"
msgstr "Wachtwoord voor de gebruiker"

#: users/serializers.py:383
msgid "Override warning"
msgstr "Overschrijf waarschuwing"

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr "Overschrijf de waarschuwing over wachtwoord regels"

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr "Alleen administrators kunnen nieuwe gebruikers aanmaken"

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr "U hebt geen toestemming om gebruikers aan te maken"

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Je account is aangemaakt."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Gebruik de wachtwoordreset functie om in te loggen"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Welkom bij InvenTree"

