# Generated by Django 4.2.14 on 2024-08-07 22:40

import django.db.models.deletion
from django.db import migrations, models

from common.models import state_color_mappings


class Migration(migrations.Migration):

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("common", "0028_colortheme_user_obj"),
    ]

    operations = [
        migrations.CreateModel(
            name="InvenTreeCustomUserStateModel",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "key",
                    models.IntegerField(
                        help_text="Value that will be saved in the models database",
                        verbose_name="Key",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the state",
                        max_length=250,
                        verbose_name="Name",
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        help_text="Label that will be displayed in the frontend",
                        max_length=250,
                        verbose_name="Label",
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        choices=state_color_mappings(),
                        default="secondary",
                        help_text="Color that will be displayed in the frontend",
                        max_length=10,
                        verbose_name="Color",
                    ),
                ),
                (
                    "logical_key",
                    models.IntegerField(
                        help_text="State logical key that is equal to this custom state in business logic",
                        verbose_name="Logical Key",
                    ),
                ),
                (
                    "reference_status",
                    models.CharField(
                        help_text="Status set that is extended with this custom state",
                        max_length=250,
                        verbose_name="Reference Status Set",
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        help_text="Model this state is associated with",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="contenttypes.contenttype",
                        verbose_name="Model",
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom State",
                "verbose_name_plural": "Custom States",
                "unique_together": {
                    ("model", "reference_status", "key", "logical_key")
                },
            },
        ),
    ]
