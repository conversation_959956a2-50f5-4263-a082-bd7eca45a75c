# Generated by Django 3.2.16 on 2022-11-10 01:08

import InvenTree.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0074_auto_20220709_0108'),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseorder',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AlterField(
            model_name='salesordershipment',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
    ]
