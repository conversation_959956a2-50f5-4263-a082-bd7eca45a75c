# Generated by Django 3.2.18 on 2023-04-03 14:02

import InvenTree.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0087_alter_salesorder_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorderextraline',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AddField(
            model_name='purchaseorderlineitem',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AddField(
            model_name='returnorderextraline',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AddField(
            model_name='salesorderextraline',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AddField(
            model_name='salesorderlineitem',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
    ]
