msgid ""
msgstr ""
"POT-Creation-Date: 2023-06-09 22:10+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: pseudo-LOCALE\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/AuthenticationForm.tsx:37
#~ msgid "login"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:37
#~ msgid "register"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:56
#~ msgid "Welcome {actionname} to"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:61
#~ msgid "Placeholder"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:64
#~ msgid "Or continue with email"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:74
#~ msgid "Your name"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:90
#~ msgid "Invalid email"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:101
#~ msgid "Password should include at least 6 characters"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:114
#~ msgid "Already have an account? Login"
#~ msgstr ""

#: src/components/AuthenticationForm.tsx:115
#~ msgid "Don't have an account? Register"
#~ msgstr ""

#: src/components/Boundary.tsx:12
msgid "Error rendering component"
msgstr ""

#: src/components/Boundary.tsx:14
msgid "An error occurred while rendering this component. Refer to the console for more information."
msgstr ""

#: src/components/DashboardItemProxy.tsx:34
msgid "Title"
msgstr ""

#: src/components/buttons/AdminButton.tsx:80
msgid "Open in admin interface"
msgstr ""

#: src/components/buttons/CopyButton.tsx:18
#~ msgid "Copy to clipboard"
#~ msgstr ""

#: src/components/buttons/CopyButton.tsx:24
msgid "Copied"
msgstr ""

#: src/components/buttons/CopyButton.tsx:24
msgid "Copy"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:93
msgid "Print Label"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:99
msgid "Print"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:100
msgid "Label printing completed successfully"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:106
#: src/components/buttons/PrintingActions.tsx:144
#: src/components/editors/NotesEditor.tsx:65
#: src/components/editors/NotesEditor.tsx:165
#: src/components/forms/fields/ApiFormField.tsx:319
#: src/components/importer/ImportDataSelector.tsx:187
#: src/components/importer/ImporterColumnSelector.tsx:207
#: src/components/modals/LicenseModal.tsx:75
#: src/components/nav/SearchDrawer.tsx:448
#: src/pages/ErrorPage.tsx:11
#: src/pages/part/PartPricingPanel.tsx:67
#: src/tables/InvenTreeTable.tsx:495
#: src/tables/bom/BomTable.tsx:450
#: src/tables/stock/StockItemTestResultTable.tsx:299
msgid "Error"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:107
msgid "The label could not be generated"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:122
msgid "Print Report"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:138
msgid "Generate"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:139
msgid "Report printing completed successfully"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:145
msgid "The report could not be generated"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:173
msgid "Printing Actions"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:178
msgid "Print Labels"
msgstr ""

#: src/components/buttons/PrintingActions.tsx:184
msgid "Print Reports"
msgstr ""

#: src/components/buttons/ScanButton.tsx:15
msgid "Scan QR code"
msgstr ""

#: src/components/buttons/ScanButton.tsx:20
msgid "Open QR code scanner"
msgstr ""

#: src/components/buttons/SpotlightButton.tsx:14
msgid "Open spotlight"
msgstr ""

#: src/components/buttons/YesNoButton.tsx:16
msgid "Pass"
msgstr ""

#: src/components/buttons/YesNoButton.tsx:17
msgid "Fail"
msgstr ""

#: src/components/buttons/YesNoButton.tsx:33
#: src/tables/Filter.tsx:52
msgid "Yes"
msgstr ""

#: src/components/buttons/YesNoButton.tsx:33
#: src/tables/Filter.tsx:53
msgid "No"
msgstr ""

#: src/components/details/Details.tsx:301
msgid "No name defined"
msgstr ""

#: src/components/details/DetailsImage.tsx:65
msgid "Remove Image"
msgstr ""

#: src/components/details/DetailsImage.tsx:68
msgid "Remove the associated image from this item?"
msgstr ""

#: src/components/details/DetailsImage.tsx:71
#: src/forms/StockForms.tsx:533
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:199
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:306
#: src/pages/stock/StockDetail.tsx:526
msgid "Remove"
msgstr ""

#: src/components/details/DetailsImage.tsx:71
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:158
#: src/components/items/ActionDropdown.tsx:215
#: src/components/items/ActionDropdown.tsx:216
#: src/contexts/ThemeContext.tsx:43
#: src/hooks/UseForm.tsx:40
#: src/tables/FilterSelectDrawer.tsx:205
#: src/tables/build/BuildOutputTable.tsx:286
msgid "Cancel"
msgstr ""

#: src/components/details/DetailsImage.tsx:97
msgid "Drag and drop to upload"
msgstr ""

#: src/components/details/DetailsImage.tsx:100
msgid "Click to select file(s)"
msgstr ""

#: src/components/details/DetailsImage.tsx:226
msgid "Clear"
msgstr ""

#: src/components/details/DetailsImage.tsx:232
#: src/components/forms/ApiForm.tsx:627
#: src/contexts/ThemeContext.tsx:43
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:60
msgid "Submit"
msgstr ""

#: src/components/details/DetailsImage.tsx:272
msgid "Select from existing images"
msgstr ""

#: src/components/details/DetailsImage.tsx:280
msgid "Select Image"
msgstr ""

#: src/components/details/DetailsImage.tsx:292
msgid "Upload new image"
msgstr ""

#: src/components/details/DetailsImage.tsx:299
msgid "Upload Image"
msgstr ""

#: src/components/details/DetailsImage.tsx:312
msgid "Delete image"
msgstr ""

#: src/components/details/PartIcons.tsx:43
#~ msgid "Part is a template part (variants can be made from this part)"
#~ msgstr ""

#: src/components/details/PartIcons.tsx:49
#~ msgid "Part can be assembled from other parts"
#~ msgstr ""

#: src/components/details/PartIcons.tsx:55
#~ msgid "Part can be used in assemblies"
#~ msgstr ""

#: src/components/details/PartIcons.tsx:61
#~ msgid "Part stock is tracked by serial number"
#~ msgstr ""

#: src/components/details/PartIcons.tsx:67
#~ msgid "Part can be purchased from external suppliers"
#~ msgstr ""

#: src/components/details/PartIcons.tsx:73
#~ msgid "Part can be sold to customers"
#~ msgstr ""

#: src/components/details/PartIcons.tsx:78
#~ msgid "Part is virtual (not a physical part)"
#~ msgstr ""

#: src/components/editors/NotesEditor.tsx:66
msgid "Image upload failed"
msgstr ""

#: src/components/editors/NotesEditor.tsx:156
#: src/components/forms/ApiForm.tsx:467
#: src/tables/bom/BomTable.tsx:441
msgid "Success"
msgstr ""

#: src/components/editors/NotesEditor.tsx:157
msgid "Notes saved successfully"
msgstr ""

#: src/components/editors/NotesEditor.tsx:166
msgid "Failed to save notes"
msgstr ""

#: src/components/editors/NotesEditor.tsx:198
msgid "Preview Notes"
msgstr ""

#: src/components/editors/NotesEditor.tsx:198
msgid "Edit Notes"
msgstr ""

#: src/components/editors/NotesEditor.tsx:212
msgid "Save Notes"
msgstr ""

#: src/components/editors/TemplateEditor/CodeEditor/index.tsx:9
msgid "Code"
msgstr ""

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:44
#~ msgid "Failed to parse error response from server."
#~ msgstr ""

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:81
msgid "Preview not available, click \"Reload Preview\"."
msgstr ""

#: src/components/editors/TemplateEditor/PdfPreview/index.tsx:9
msgid "PDF Preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:104
msgid "Error loading template"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:116
msgid "Error saving template"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:146
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:274
msgid "Save & Reload Preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:151
msgid "Are you sure you want to Save & Reload the preview?"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:151
#~ msgid "Save & Reload preview?"
#~ msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:153
msgid "To render the preview the current template needs to be replaced on the server with your modifications which may break the label if it is under active use. Do you want to proceed?"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:157
msgid "Save & Reload"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:189
msgid "Preview updated"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:190
msgid "The preview has been updated successfully."
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:263
#~ msgid "Save & Reload preview"
#~ msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:266
msgid "Reload preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:267
msgid "Use the currently stored template from the server"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:275
msgid "Save the current template and reload the preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:322
#~ msgid "to preview"
#~ msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:333
msgid "Select instance to preview"
msgstr ""

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:377
msgid "Error rendering template"
msgstr ""

#: src/components/errors/ClientError.tsx:23
msgid "Client Error"
msgstr ""

#: src/components/errors/ClientError.tsx:24
msgid "Client error occurred"
msgstr ""

#: src/components/errors/GenericErrorPage.tsx:50
msgid "Status Code"
msgstr ""

#: src/components/errors/GenericErrorPage.tsx:63
msgid "Return to the index page"
msgstr ""

#: src/components/errors/NotAuthenticated.tsx:8
msgid "Not Authenticated"
msgstr ""

#: src/components/errors/NotAuthenticated.tsx:9
msgid "You are not logged in."
msgstr ""

#: src/components/errors/NotFound.tsx:8
msgid "Page Not Found"
msgstr ""

#: src/components/errors/NotFound.tsx:9
msgid "This page does not exist"
msgstr ""

#: src/components/errors/PermissionDenied.tsx:8
msgid "Permission Denied"
msgstr ""

#: src/components/errors/PermissionDenied.tsx:9
msgid "You do not have permission to view this page."
msgstr ""

#: src/components/errors/ServerError.tsx:8
msgid "Server Error"
msgstr ""

#: src/components/errors/ServerError.tsx:9
msgid "A server error occurred"
msgstr ""

#: src/components/forms/ApiForm.tsx:151
#: src/components/forms/ApiForm.tsx:555
msgid "Form Error"
msgstr ""

#: src/components/forms/ApiForm.tsx:487
#~ msgid "Form Errors Exist"
#~ msgstr ""

#: src/components/forms/ApiForm.tsx:563
msgid "Errors exist for one or more form fields"
msgstr ""

#: src/components/forms/ApiForm.tsx:665
#: src/tables/plugin/PluginListTable.tsx:388
msgid "Update"
msgstr ""

#: src/components/forms/ApiForm.tsx:685
#: src/components/items/ActionDropdown.tsx:195
#: src/hooks/UseForm.tsx:122
#: src/pages/Index/Scan.tsx:343
#: src/pages/Notifications.tsx:123
#: src/tables/RowActions.tsx:43
#: src/tables/plugin/PluginListTable.tsx:420
msgid "Delete"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:48
#: src/components/forms/AuthenticationForm.tsx:74
#: src/functions/auth.tsx:83
#~ msgid "Check your your input and try again."
#~ msgstr ""

#: src/components/forms/AuthenticationForm.tsx:51
msgid "Login successful"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:52
msgid "Logged in successfully"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:52
#~ msgid "Welcome back!"
#~ msgstr ""

#: src/components/forms/AuthenticationForm.tsx:53
#~ msgid "Login successfull"
#~ msgstr ""

#: src/components/forms/AuthenticationForm.tsx:58
msgid "Login failed"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:59
#: src/components/forms/AuthenticationForm.tsx:76
#: src/components/forms/AuthenticationForm.tsx:211
#: src/functions/auth.tsx:164
msgid "Check your input and try again."
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:65
#: src/functions/auth.tsx:74
#~ msgid "Mail delivery successfull"
#~ msgstr ""

#: src/components/forms/AuthenticationForm.tsx:70
#: src/functions/auth.tsx:155
msgid "Mail delivery successful"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:71
msgid "Check your inbox for the login link. If you have an account, you will receive a login link. Check in spam too."
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:75
msgid "Mail delivery failed"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:95
msgid "Or continue with other methods"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:106
#: src/components/forms/AuthenticationForm.tsx:227
msgid "Username"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:107
#: src/components/forms/AuthenticationForm.tsx:228
msgid "Your username"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:112
#: src/components/forms/AuthenticationForm.tsx:240
#: src/pages/Auth/Set-Password.tsx:106
msgid "Password"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:113
#: src/components/forms/AuthenticationForm.tsx:241
msgid "Your password"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:125
#: src/pages/Auth/Reset.tsx:26
msgid "Reset password"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:131
#~ msgid "Log in"
#~ msgstr ""

#: src/components/forms/AuthenticationForm.tsx:134
#: src/components/forms/AuthenticationForm.tsx:233
#: src/pages/Auth/Reset.tsx:31
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:51
msgid "Email"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:135
#: src/pages/Auth/Reset.tsx:32
#: src/pages/Auth/Set-Password.tsx:107
msgid "We will send you a link to login - if you are registered"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:136
#~ msgid "I will use username and password"
#~ msgstr ""

#: src/components/forms/AuthenticationForm.tsx:151
msgid "Send me an email"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:153
msgid "Use username and password"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:162
msgid "Log In"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:164
msgid "Send Email"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:193
msgid "Registration successful"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:194
msgid "Please confirm your email address to complete the registration"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:210
msgid "Input error"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:234
msgid "This will be used for a confirmation"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:246
msgid "Password repeat"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:247
msgid "Repeat password"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:259
#: src/components/forms/AuthenticationForm.tsx:304
msgid "Register"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:265
msgid "Or use SSO"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:296
msgid "Don't have an account?"
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:315
msgid "Go back to login"
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:36
#: src/components/forms/HostOptionsForm.tsx:67
msgid "Host"
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:42
#: src/components/forms/HostOptionsForm.tsx:70
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:410
#: src/pages/part/CategoryDetail.tsx:81
#: src/pages/part/PartDetail.tsx:143
#: src/pages/stock/LocationDetail.tsx:89
#: src/tables/machine/MachineTypeTable.tsx:65
#: src/tables/machine/MachineTypeTable.tsx:109
#: src/tables/machine/MachineTypeTable.tsx:216
#: src/tables/machine/MachineTypeTable.tsx:319
#: src/tables/plugin/PluginErrorTable.tsx:33
#: src/tables/plugin/PluginListTable.tsx:126
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:32
#: src/tables/settings/GroupTable.tsx:147
#: src/tables/settings/PendingTasksTable.tsx:28
#: src/tables/stock/LocationTypesTable.tsx:69
msgid "Name"
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:75
msgid "No one here..."
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:86
msgid "Add Host"
msgstr ""

#: src/components/forms/HostOptionsForm.tsx:90
msgid "Save"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:43
msgid "Select destination instance"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:71
msgid "Edit possible host options"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:98
msgid "Version: {0}"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:100
msgid "API:{0}"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:102
msgid "Name: {0}"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:104
msgid "State: <0>worker</0> ({0}), <1>plugins</1>{1}"
msgstr ""

#: src/components/forms/fields/IconField.tsx:81
msgid "No icon selected"
msgstr ""

#: src/components/forms/fields/IconField.tsx:159
msgid "Uncategorized"
msgstr ""

#: src/components/forms/fields/IconField.tsx:209
#: src/components/nav/Layout.tsx:70
#: src/tables/part/PartThumbTable.tsx:192
msgid "Search..."
msgstr ""

#: src/components/forms/fields/IconField.tsx:223
msgid "Select category"
msgstr ""

#: src/components/forms/fields/IconField.tsx:232
msgid "Select pack"
msgstr ""

#: src/components/forms/fields/IconField.tsx:237
msgid "{0} icons"
msgstr ""

#: src/components/forms/fields/RelatedModelField.tsx:318
#: src/pages/Index/Settings/UserSettings.tsx:96
#: src/tables/Search.tsx:23
msgid "Search"
msgstr ""

#: src/components/forms/fields/RelatedModelField.tsx:319
#: src/components/modals/AboutInvenTreeModal.tsx:81
#: src/components/widgets/WidgetLayout.tsx:120
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:316
msgid "Loading"
msgstr ""

#: src/components/forms/fields/RelatedModelField.tsx:321
msgid "No results found"
msgstr ""

#: src/components/forms/fields/TableField.tsx:52
msgid "modelRenderer entry required for tables"
msgstr ""

#: src/components/forms/fields/TableField.tsx:76
msgid "No entries available"
msgstr ""

#: src/components/images/DetailsImage.tsx:252
#~ msgid "Select image"
#~ msgstr ""

#: src/components/images/Thumbnail.tsx:12
msgid "Thumbnail"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:170
msgid "Importing Rows"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:171
msgid "Please wait while the data is imported"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:188
msgid "An error occurred while importing data"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:209
msgid "Edit Data"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:237
msgid "Delete Row"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:267
msgid "Row"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:285
msgid "Row contains errors"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:326
msgid "Accept"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:359
msgid "Valid"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:360
msgid "Filter by row validation status"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:365
#: src/tables/build/BuildOutputTable.tsx:266
msgid "Complete"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:366
msgid "Filter by row completion status"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:383
msgid "Import selected rows"
msgstr ""

#: src/components/importer/ImportDataSelector.tsx:398
msgid "Processing Data"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:50
#: src/components/importer/ImporterColumnSelector.tsx:176
msgid "An error occurred"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:62
msgid "Select column, or leave blank to ignore this field."
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:91
#~ msgid "Select a column from the data file"
#~ msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:104
#~ msgid "Map data columns to database fields"
#~ msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:119
#~ msgid "Imported Column Name"
#~ msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:182
msgid "Ignore this field"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:196
msgid "Mapping data columns to database fields"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:201
msgid "Accept Column Mapping"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:214
msgid "Database Field"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:215
msgid "Field Description"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:216
msgid "Imported Column"
msgstr ""

#: src/components/importer/ImporterColumnSelector.tsx:217
msgid "Default Value"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:44
msgid "Upload File"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:45
msgid "Map Columns"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:46
msgid "Import Data"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:47
msgid "Process Data"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:48
msgid "Complete Import"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:97
#~ msgid "Cancel import session"
#~ msgstr ""

#: src/components/importer/ImporterDrawer.tsx:104
msgid "Import Complete"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:107
msgid "Data has been imported successfully"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:109
#: src/components/importer/ImporterDrawer.tsx:118
msgid "Close"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:115
msgid "Unknown Status"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:116
msgid "Import session has unknown status"
msgstr ""

#: src/components/importer/ImporterDrawer.tsx:135
msgid "Importing Data"
msgstr ""

#: src/components/importer/ImporterImportProgress.tsx:36
msgid "Importing Records"
msgstr ""

#: src/components/importer/ImporterImportProgress.tsx:39
msgid "Imported rows"
msgstr ""

#: src/components/items/ActionDropdown.tsx:121
msgid "Barcode Actions"
msgstr ""

#: src/components/items/ActionDropdown.tsx:140
msgid "View Barcode"
msgstr ""

#: src/components/items/ActionDropdown.tsx:147
msgid "View"
msgstr ""

#: src/components/items/ActionDropdown.tsx:148
msgid "View barcode"
msgstr ""

#: src/components/items/ActionDropdown.tsx:161
msgid "Link Barcode"
msgstr ""

#: src/components/items/ActionDropdown.tsx:162
msgid "Link custom barcode"
msgstr ""

#: src/components/items/ActionDropdown.tsx:173
#: src/forms/PurchaseOrderForms.tsx:426
msgid "Unlink Barcode"
msgstr ""

#: src/components/items/ActionDropdown.tsx:174
msgid "Unlink custom barcode"
msgstr ""

#: src/components/items/ActionDropdown.tsx:183
#: src/tables/RowActions.tsx:33
msgid "Edit"
msgstr ""

#: src/components/items/ActionDropdown.tsx:184
msgid "Edit item"
msgstr ""

#: src/components/items/ActionDropdown.tsx:196
msgid "Delete item"
msgstr ""

#: src/components/items/ActionDropdown.tsx:204
#: src/components/items/ActionDropdown.tsx:205
msgid "Hold"
msgstr ""

#: src/components/items/ActionDropdown.tsx:227
#: src/tables/RowActions.tsx:23
msgid "Duplicate"
msgstr ""

#: src/components/items/ActionDropdown.tsx:228
msgid "Duplicate item"
msgstr ""

#: src/components/items/DocTooltip.tsx:92
msgid "Read More"
msgstr ""

#: src/components/items/ErrorItem.tsx:5
#: src/tables/InvenTreeTable.tsx:487
msgid "Unknown error"
msgstr ""

#: src/components/items/ErrorItem.tsx:6
#~ msgid "An error occured:"
#~ msgstr ""

#: src/components/items/ErrorItem.tsx:10
msgid "An error occurred:"
msgstr ""

#: src/components/items/GettingStartedCarousel.tsx:27
msgid "Read more"
msgstr ""

#: src/components/items/InfoItem.tsx:27
msgid "None"
msgstr ""

#: src/components/items/InvenTreeLogo.tsx:23
msgid "InvenTree Logo"
msgstr ""

#: src/components/items/OnlyStaff.tsx:9
#: src/components/modals/AboutInvenTreeModal.tsx:44
msgid "This information is only available for staff users"
msgstr ""

#: src/components/items/Placeholder.tsx:14
msgid "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."
msgstr ""

#: src/components/items/Placeholder.tsx:17
msgid "PLH"
msgstr ""

#: src/components/items/Placeholder.tsx:31
msgid "This panel is a placeholder."
msgstr ""

#: src/components/items/QRCode.tsx:87
msgid "Low (7%)"
msgstr ""

#: src/components/items/QRCode.tsx:88
msgid "Medium (15%)"
msgstr ""

#: src/components/items/QRCode.tsx:89
msgid "Quartile (25%)"
msgstr ""

#: src/components/items/QRCode.tsx:90
msgid "High (30%)"
msgstr ""

#: src/components/items/QRCode.tsx:107
msgid "Barcode Data:"
msgstr ""

#: src/components/items/QRCode.tsx:118
msgid "Select Error Correction Level"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:99
msgid "Version Information"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:103
msgid "Your InvenTree version status is"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:107
msgid "Development Version"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:111
msgid "Up to Date"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:115
msgid "Update Available"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:125
msgid "InvenTree Version"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:131
msgid "Commit Hash"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:136
msgid "Commit Date"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:141
msgid "Commit Branch"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:146
#: src/components/modals/ServerInfoModal.tsx:133
msgid "API Version"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:149
msgid "Python Version"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:152
msgid "Django Version"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:162
msgid "Links"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:168
msgid "InvenTree Documentation"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:169
msgid "View Code on GitHub"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:170
msgid "Credits"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:171
msgid "Mobile App"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:172
msgid "Submit Bug Report"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:183
msgid "Copy version information"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:192
#: src/components/modals/ServerInfoModal.tsx:147
msgid "Dismiss"
msgstr ""

#: src/components/modals/LicenseModal.tsx:39
msgid "No license text available"
msgstr ""

#: src/components/modals/LicenseModal.tsx:46
msgid "No Information provided - this is likely a server issue"
msgstr ""

#: src/components/modals/LicenseModal.tsx:71
msgid "Loading license information"
msgstr ""

#: src/components/modals/LicenseModal.tsx:77
msgid "Failed to fetch license information"
msgstr ""

#: src/components/modals/LicenseModal.tsx:85
msgid "{key} Packages"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:72
msgid "Unknown response"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:102
#: src/pages/Index/Scan.tsx:636
msgid "Error while getting camera"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:125
#: src/pages/Index/Scan.tsx:659
msgid "Error while scanning"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:139
#: src/pages/Index/Scan.tsx:673
msgid "Error while stopping"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:154
#: src/defaults/menuItems.tsx:21
#: src/pages/Index/Scan.tsx:746
msgid "Scanning"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:154
#: src/pages/Index/Scan.tsx:746
msgid "Not scanning"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:159
#: src/pages/Index/Scan.tsx:752
msgid "Select Camera"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:169
#: src/pages/Index/Scan.tsx:737
msgid "Start scanning"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:176
#: src/pages/Index/Scan.tsx:729
msgid "Stop scanning"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:181
msgid "No scans yet!"
msgstr ""

#: src/components/modals/QrCodeModal.tsx:201
msgid "Close modal"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:26
#: src/pages/Index/Settings/SystemSettings.tsx:38
msgid "Server"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:32
msgid "Instance Name"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:38
msgid "Database"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:38
#~ msgid "Bebug Mode"
#~ msgstr ""

#: src/components/modals/ServerInfoModal.tsx:47
msgid "Debug Mode"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:50
msgid "Server is running in debug mode"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:57
msgid "Docker Mode"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:60
msgid "Server is deployed using docker"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:66
msgid "Plugin Support"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:71
msgid "Plugin support enabled"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:73
msgid "Plugin support disabled"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:80
msgid "Server status"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:86
msgid "Healthy"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:88
msgid "Issues detected"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:97
msgid "Background Worker"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:101
msgid "Background worker not running"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:109
msgid "Email Settings"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:113
msgid "Email settings not configured"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:121
#: src/tables/plugin/PluginListTable.tsx:144
#: src/tables/plugin/PluginListTable.tsx:294
msgid "Version"
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:127
msgid "Server Version"
msgstr ""

#: src/components/nav/Layout.tsx:73
msgid "Nothing found..."
msgstr ""

#: src/components/nav/MainMenu.tsx:40
#: src/pages/Index/Profile/Profile.tsx:15
#~ msgid "Profile"
#~ msgstr ""

#: src/components/nav/MainMenu.tsx:52
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:28
msgid "Settings"
msgstr ""

#: src/components/nav/MainMenu.tsx:59
#: src/defaults/menuItems.tsx:15
msgid "Account settings"
msgstr ""

#: src/components/nav/MainMenu.tsx:67
#: src/defaults/menuItems.tsx:58
#: src/pages/Index/Settings/SystemSettings.tsx:315
msgid "System Settings"
msgstr ""

#: src/components/nav/MainMenu.tsx:68
#~ msgid "Current language {locale}"
#~ msgstr ""

#: src/components/nav/MainMenu.tsx:71
#~ msgid "Switch to pseudo language"
#~ msgstr ""

#: src/components/nav/MainMenu.tsx:77
msgid "Change Color Mode"
msgstr ""

#: src/components/nav/MainMenu.tsx:86
#: src/defaults/actions.tsx:71
#: src/defaults/menuItems.tsx:63
#: src/pages/Index/Settings/AdminCenter/Index.tsx:221
msgid "Admin Center"
msgstr ""

#: src/components/nav/MainMenu.tsx:96
msgid "Logout"
msgstr ""

#: src/components/nav/NavHoverMenu.tsx:65
#: src/defaults/actions.tsx:60
msgid "Open Navigation"
msgstr ""

#: src/components/nav/NavHoverMenu.tsx:84
msgid "View all"
msgstr ""

#: src/components/nav/NavHoverMenu.tsx:100
#: src/components/nav/NavHoverMenu.tsx:110
msgid "Get started"
msgstr ""

#: src/components/nav/NavHoverMenu.tsx:103
msgid "Overview over high-level objects, functions and possible usecases."
msgstr ""

#: src/components/nav/NavigationDrawer.tsx:57
msgid "Navigation"
msgstr ""

#: src/components/nav/NavigationDrawer.tsx:60
msgid "Pages"
msgstr ""

#: src/components/nav/NavigationDrawer.tsx:65
#: src/pages/Index/Settings/AdminCenter/Index.tsx:176
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:41
msgid "Plugins"
msgstr ""

#: src/components/nav/NavigationDrawer.tsx:75
#: src/defaults/actions.tsx:32
msgid "Documentation"
msgstr ""

#: src/components/nav/NavigationDrawer.tsx:78
msgid "About"
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:92
#: src/pages/Index/Settings/SystemSettings.tsx:109
#: src/pages/Index/Settings/UserSettings.tsx:126
#: src/pages/Notifications.tsx:65
#: src/pages/Notifications.tsx:151
msgid "Notifications"
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:94
msgid "Mark all as read"
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:104
msgid "View all notifications"
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:123
msgid "You have no unread notifications."
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:139
#: src/components/nav/NotificationDrawer.tsx:145
#: src/tables/notifications/NotificationsTable.tsx:36
msgid "Notification"
msgstr ""

#: src/components/nav/NotificationDrawer.tsx:168
#: src/pages/Notifications.tsx:73
msgid "Mark as read"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:78
msgid "results"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:370
msgid "Enter search text"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:397
msgid "Search Options"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:400
msgid "Regex search"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:410
msgid "Whole word search"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:451
msgid "An error occurred during search query"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:462
msgid "No results"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:465
msgid "No results available for search query"
msgstr ""

#: src/components/render/Instance.tsx:217
msgid "Unknown model: {model}"
msgstr ""

#: src/components/render/ModelType.tsx:22
#: src/forms/BuildForms.tsx:213
#: src/forms/PurchaseOrderForms.tsx:639
#: src/forms/StockForms.tsx:499
#: src/forms/StockForms.tsx:533
#: src/forms/StockForms.tsx:562
#: src/forms/StockForms.tsx:590
#: src/forms/StockForms.tsx:621
#: src/forms/StockForms.tsx:656
#: src/forms/StockForms.tsx:698
#: src/forms/StockForms.tsx:734
#: src/pages/build/BuildDetail.tsx:95
#: src/pages/part/PartDetail.tsx:1073
#: src/tables/build/BuildAllocatedStockTable.tsx:82
#: src/tables/part/PartTable.tsx:28
#: src/tables/part/RelatedPartTable.tsx:45
#: src/tables/sales/ReturnOrderLineItemTable.tsx:88
#: src/tables/sales/SalesOrderAllocationTable.tsx:68
#: src/tables/stock/StockTrackingTable.tsx:74
msgid "Part"
msgstr ""

#: src/components/render/ModelType.tsx:23
#: src/defaults/links.tsx:29
#: src/defaults/menuItems.tsx:33
#: src/pages/Index/Settings/SystemSettings.tsx:173
#: src/pages/part/CategoryDetail.tsx:119
#: src/pages/part/CategoryDetail.tsx:244
#: src/pages/part/CategoryDetail.tsx:274
#: src/pages/part/PartDetail.tsx:827
msgid "Parts"
msgstr ""

#: src/components/render/ModelType.tsx:31
msgid "Part Parameter Template"
msgstr ""

#: src/components/render/ModelType.tsx:32
msgid "Part Parameter Templates"
msgstr ""

#: src/components/render/ModelType.tsx:38
msgid "Part Test Template"
msgstr ""

#: src/components/render/ModelType.tsx:39
msgid "Part Test Templates"
msgstr ""

#: src/components/render/ModelType.tsx:45
#: src/pages/company/SupplierPartDetail.tsx:203
#: src/pages/company/SupplierPartDetail.tsx:375
#: src/pages/stock/StockDetail.tsx:173
#: src/tables/build/BuildAllocatedStockTable.tsx:131
#: src/tables/part/PartPurchaseOrdersTable.tsx:49
#: src/tables/purchasing/SupplierPartTable.tsx:68
msgid "Supplier Part"
msgstr ""

#: src/components/render/ModelType.tsx:46
msgid "Supplier Parts"
msgstr ""

#: src/components/render/ModelType.tsx:54
#: src/pages/company/ManufacturerPartDetail.tsx:132
#: src/tables/part/PartPurchaseOrdersTable.tsx:55
msgid "Manufacturer Part"
msgstr ""

#: src/components/render/ModelType.tsx:55
msgid "Manufacturer Parts"
msgstr ""

#: src/components/render/ModelType.tsx:63
#: src/pages/part/CategoryDetail.tsx:305
msgid "Part Category"
msgstr ""

#: src/components/render/ModelType.tsx:64
#: src/pages/part/CategoryDetail.tsx:258
#: src/pages/part/CategoryDetail.tsx:296
#: src/pages/part/PartDetail.tsx:1063
msgid "Part Categories"
msgstr ""

#: src/components/render/ModelType.tsx:72
#: src/pages/stock/StockDetail.tsx:624
#: src/tables/sales/ReturnOrderLineItemTable.tsx:94
#: src/tables/stock/StockTrackingTable.tsx:45
msgid "Stock Item"
msgstr ""

#: src/components/render/ModelType.tsx:73
#: src/pages/company/CompanyDetail.tsx:203
#: src/pages/stock/LocationDetail.tsx:128
#: src/pages/stock/LocationDetail.tsx:181
#: src/pages/stock/LocationDetail.tsx:395
msgid "Stock Items"
msgstr ""

#: src/components/render/ModelType.tsx:81
msgid "Stock Location"
msgstr ""

#: src/components/render/ModelType.tsx:82
#: src/pages/stock/LocationDetail.tsx:195
#: src/pages/stock/LocationDetail.tsx:387
#: src/pages/stock/StockDetail.tsx:616
msgid "Stock Locations"
msgstr ""

#: src/components/render/ModelType.tsx:90
msgid "Stock Location Type"
msgstr ""

#: src/components/render/ModelType.tsx:91
msgid "Stock Location Types"
msgstr ""

#: src/components/render/ModelType.tsx:95
msgid "Stock History"
msgstr ""

#: src/components/render/ModelType.tsx:96
msgid "Stock Histories"
msgstr ""

#: src/components/render/ModelType.tsx:100
#: src/defaults/links.tsx:31
#: src/defaults/menuItems.tsx:43
msgid "Build"
msgstr ""

#: src/components/render/ModelType.tsx:101
msgid "Builds"
msgstr ""

#: src/components/render/ModelType.tsx:109
msgid "Build Line"
msgstr ""

#: src/components/render/ModelType.tsx:110
msgid "Build Lines"
msgstr ""

#: src/components/render/ModelType.tsx:117
msgid "Build Item"
msgstr ""

#: src/components/render/ModelType.tsx:118
msgid "Build Items"
msgstr ""

#: src/components/render/ModelType.tsx:122
#: src/pages/company/CompanyDetail.tsx:339
msgid "Company"
msgstr ""

#: src/components/render/ModelType.tsx:123
msgid "Companies"
msgstr ""

#: src/components/render/ModelType.tsx:131
#: src/tables/TableHoverCard.tsx:81
#: src/tables/build/BuildOrderTable.tsx:140
#: src/tables/purchasing/PurchaseOrderTable.tsx:64
#: src/tables/sales/ReturnOrderTable.tsx:55
#: src/tables/sales/SalesOrderTable.tsx:62
msgid "Project Code"
msgstr ""

#: src/components/render/ModelType.tsx:132
#: src/pages/Index/Settings/AdminCenter/Index.tsx:128
msgid "Project Codes"
msgstr ""

#: src/components/render/ModelType.tsx:138
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:32
#: src/pages/purchasing/PurchaseOrderDetail.tsx:480
#: src/tables/part/PartPurchaseOrdersTable.tsx:32
#: src/tables/stock/StockTrackingTable.tsx:107
msgid "Purchase Order"
msgstr ""

#: src/components/render/ModelType.tsx:139
#: src/pages/Index/Settings/SystemSettings.tsx:255
#: src/pages/company/CompanyDetail.tsx:196
#: src/pages/company/SupplierPartDetail.tsx:237
#: src/pages/part/PartDetail.tsx:662
#: src/pages/purchasing/PurchasingIndex.tsx:25
msgid "Purchase Orders"
msgstr ""

#: src/components/render/ModelType.tsx:147
msgid "Purchase Order Line"
msgstr ""

#: src/components/render/ModelType.tsx:148
msgid "Purchase Order Lines"
msgstr ""

#: src/components/render/ModelType.tsx:152
#: src/pages/build/BuildDetail.tsx:153
#: src/pages/sales/SalesOrderDetail.tsx:520
#: src/pages/stock/StockDetail.tsx:221
#: src/tables/sales/SalesOrderAllocationTable.tsx:50
#: src/tables/stock/StockTrackingTable.tsx:118
msgid "Sales Order"
msgstr ""

#: src/components/render/ModelType.tsx:153
#: src/pages/Index/Settings/SystemSettings.tsx:270
#: src/pages/company/CompanyDetail.tsx:216
#: src/pages/part/PartDetail.tsx:669
#: src/pages/sales/SalesIndex.tsx:26
msgid "Sales Orders"
msgstr ""

#: src/components/render/ModelType.tsx:161
msgid "Sales Order Shipment"
msgstr ""

#: src/components/render/ModelType.tsx:162
msgid "Sales Order Shipments"
msgstr ""

#: src/components/render/ModelType.tsx:168
#: src/pages/sales/ReturnOrderDetail.tsx:469
#: src/tables/stock/StockTrackingTable.tsx:129
msgid "Return Order"
msgstr ""

#: src/components/render/ModelType.tsx:169
#: src/pages/Index/Settings/SystemSettings.tsx:286
#: src/pages/company/CompanyDetail.tsx:223
#: src/pages/sales/SalesIndex.tsx:32
msgid "Return Orders"
msgstr ""

#: src/components/render/ModelType.tsx:177
msgid "Return Order Line Item"
msgstr ""

#: src/components/render/ModelType.tsx:178
msgid "Return Order Line Items"
msgstr ""

#: src/components/render/ModelType.tsx:182
#: src/tables/company/AddressTable.tsx:47
msgid "Address"
msgstr ""

#: src/components/render/ModelType.tsx:183
#: src/pages/company/CompanyDetail.tsx:253
msgid "Addresses"
msgstr ""

#: src/components/render/ModelType.tsx:189
#: src/pages/purchasing/PurchaseOrderDetail.tsx:195
#: src/pages/sales/ReturnOrderDetail.tsx:174
#: src/pages/sales/SalesOrderDetail.tsx:183
msgid "Contact"
msgstr ""

#: src/components/render/ModelType.tsx:190
#: src/pages/company/CompanyDetail.tsx:247
msgid "Contacts"
msgstr ""

#: src/components/render/ModelType.tsx:196
msgid "Owner"
msgstr ""

#: src/components/render/ModelType.tsx:197
msgid "Owners"
msgstr ""

#: src/components/render/ModelType.tsx:203
#: src/tables/settings/ImportSessionTable.tsx:121
#: src/tables/stock/StockItemTestResultTable.tsx:198
#: src/tables/stock/StockTrackingTable.tsx:195
msgid "User"
msgstr ""

#: src/components/render/ModelType.tsx:204
#: src/pages/Index/Settings/AdminCenter/Index.tsx:98
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:13
msgid "Users"
msgstr ""

#: src/components/render/ModelType.tsx:210
msgid "Group"
msgstr ""

#: src/components/render/ModelType.tsx:211
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:20
#: src/tables/settings/UserTable.tsx:138
#: src/tables/settings/UserTable.tsx:201
msgid "Groups"
msgstr ""

#: src/components/render/ModelType.tsx:218
msgid "Import Session"
msgstr ""

#: src/components/render/ModelType.tsx:219
msgid "Import Sessions"
msgstr ""

#: src/components/render/ModelType.tsx:225
msgid "Label Template"
msgstr ""

#: src/components/render/ModelType.tsx:226
#: src/pages/Index/Settings/AdminCenter/Index.tsx:158
msgid "Label Templates"
msgstr ""

#: src/components/render/ModelType.tsx:232
msgid "Report Template"
msgstr ""

#: src/components/render/ModelType.tsx:233
#: src/pages/Index/Settings/AdminCenter/Index.tsx:164
msgid "Report Templates"
msgstr ""

#: src/components/render/ModelType.tsx:239
msgid "Plugin Configuration"
msgstr ""

#: src/components/render/ModelType.tsx:240
msgid "Plugin Configurations"
msgstr ""

#: src/components/render/Order.tsx:121
msgid "Shipment"
msgstr ""

#: src/components/render/Part.tsx:25
#: src/components/render/Plugin.tsx:17
#: src/pages/company/CompanyDetail.tsx:325
#: src/pages/company/SupplierPartDetail.tsx:360
#: src/pages/part/PartDetail.tsx:879
msgid "Inactive"
msgstr ""

#: src/components/render/Part.tsx:28
#: src/tables/bom/BomTable.tsx:204
#: src/tables/part/PartTable.tsx:134
msgid "No stock"
msgstr ""

#: src/components/render/Part.tsx:30
#: src/defaults/links.tsx:30
#: src/defaults/menuItems.tsx:38
#: src/pages/Index/Settings/SystemSettings.tsx:206
#: src/pages/part/PartDetail.tsx:543
#: src/pages/stock/LocationDetail.tsx:367
#: src/pages/stock/StockDetail.tsx:411
#: src/tables/stock/StockItemTable.tsx:68
msgid "Stock"
msgstr ""

#: src/components/render/Stock.tsx:60
#: src/pages/stock/StockDetail.tsx:150
#: src/pages/stock/StockDetail.tsx:581
#: src/tables/build/BuildAllocatedStockTable.tsx:102
#: src/tables/sales/SalesOrderAllocationTable.tsx:80
msgid "Serial Number"
msgstr ""

#: src/components/render/Stock.tsx:62
#: src/forms/BuildForms.tsx:218
#: src/forms/PurchaseOrderForms.tsx:639
#: src/pages/part/pricing/BomPricingPanel.tsx:109
#: src/pages/part/pricing/PriceBreakPanel.tsx:89
#: src/pages/part/pricing/PriceBreakPanel.tsx:171
#: src/pages/stock/StockDetail.tsx:145
#: src/pages/stock/StockDetail.tsx:587
#: src/tables/build/BuildOrderTestTable.tsx:197
#: src/tables/part/PartPurchaseOrdersTable.tsx:92
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:143
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:171
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:77
#: src/tables/stock/StockTrackingTable.tsx:59
msgid "Quantity"
msgstr ""

#: src/components/settings/SettingItem.tsx:47
#: src/components/settings/SettingItem.tsx:100
#~ msgid "{0} updated successfully"
#~ msgstr ""

#: src/components/settings/SettingList.tsx:67
msgid "Edit Setting"
msgstr ""

#: src/components/settings/SettingList.tsx:78
#: src/components/settings/SettingList.tsx:108
msgid "Setting {0} updated successfully"
msgstr ""

#: src/components/settings/SettingList.tsx:107
msgid "Setting updated"
msgstr ""

#: src/components/settings/SettingList.tsx:117
msgid "Error editing setting"
msgstr ""

#: src/components/settings/SettingList.tsx:162
msgid "No settings specified"
msgstr ""

#: src/components/tables/FilterGroup.tsx:29
#~ msgid "Add table filter"
#~ msgstr ""

#: src/components/tables/FilterGroup.tsx:44
#~ msgid "Clear all filters"
#~ msgstr ""

#: src/components/tables/FilterGroup.tsx:51
#~ msgid "Add filter"
#~ msgstr ""

#: src/components/tables/FilterSelectModal.tsx:56
#~ msgid "True"
#~ msgstr ""

#: src/components/tables/FilterSelectModal.tsx:57
#~ msgid "False"
#~ msgstr ""

#: src/components/tables/FilterSelectModal.tsx:143
#~ msgid "Add Table Filter"
#~ msgstr ""

#: src/components/tables/FilterSelectModal.tsx:145
#~ msgid "Select from the available filters"
#~ msgstr ""

#: src/components/tables/bom/BomTable.tsx:113
#~ msgid "Substitutes"
#~ msgstr ""

#: src/components/tables/bom/BomTable.tsx:200
#~ msgid "Validate"
#~ msgstr ""

#: src/components/tables/bom/BomTable.tsx:250
#~ msgid "Has Available Stock"
#~ msgstr ""

#: src/components/tables/bom/UsedInTable.tsx:40
#~ msgid "Required Part"
#~ msgstr ""

#: src/components/tables/build/BuildLineTable.tsx:152
#~ msgid "Required Quantity"
#~ msgstr ""

#: src/components/tables/build/BuildOrderTable.tsx:52
#~ msgid "Progress"
#~ msgstr ""

#: src/components/tables/build/BuildOrderTable.tsx:65
#~ msgid "Priority"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:68
#~ msgid "Postal Code"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:74
#~ msgid "City"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:80
#~ msgid "State / Province"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:86
#~ msgid "Country"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:92
#~ msgid "Courier Notes"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:98
#~ msgid "Internal Notes"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:130
#~ msgid "Address updated"
#~ msgstr ""

#: src/components/tables/company/AddressTable.tsx:142
#~ msgid "Address deleted"
#~ msgstr ""

#: src/components/tables/company/CompanyTable.tsx:32
#~ msgid "Company Name"
#~ msgstr ""

#: src/components/tables/company/ContactTable.tsx:41
#~ msgid "Phone"
#~ msgstr ""

#: src/components/tables/company/ContactTable.tsx:53
#~ msgid "Role"
#~ msgstr ""

#: src/components/tables/company/ContactTable.tsx:78
#~ msgid "Contact updated"
#~ msgstr ""

#: src/components/tables/company/ContactTable.tsx:90
#~ msgid "Contact deleted"
#~ msgstr ""

#: src/components/tables/company/ContactTable.tsx:92
#~ msgid "Are you sure you want to delete this contact?"
#~ msgstr ""

#: src/components/tables/company/ContactTable.tsx:108
#~ msgid "Create Contact"
#~ msgstr ""

#: src/components/tables/company/ContactTable.tsx:110
#~ msgid "Contact created"
#~ msgstr ""

#: src/components/tables/general/AttachmentTable.tsx:47
#~ msgid "Comment"
#~ msgstr ""

#: src/components/tables/part/PartCategoryTable.tsx:122
#~ msgid "Part category updated"
#~ msgstr ""

#: src/components/tables/part/PartParameterTable.tsx:41
#~ msgid "Parameter"
#~ msgstr ""

#: src/components/tables/part/PartParameterTable.tsx:114
#~ msgid "Part parameter updated"
#~ msgstr ""

#: src/components/tables/part/PartParameterTable.tsx:130
#~ msgid "Part parameter deleted"
#~ msgstr ""

#: src/components/tables/part/PartParameterTable.tsx:132
#~ msgid "Are you sure you want to remove this parameter?"
#~ msgstr ""

#: src/components/tables/part/PartParameterTable.tsx:159
#~ msgid "Part parameter added"
#~ msgstr ""

#: src/components/tables/part/PartParameterTemplateTable.tsx:67
#~ msgid "Choices"
#~ msgstr ""

#: src/components/tables/part/PartParameterTemplateTable.tsx:83
#~ msgid "Remove parameter template"
#~ msgstr ""

#: src/components/tables/part/PartParameterTemplateTable.tsx:84
#~ msgid "Parameter template updated"
#~ msgstr ""

#: src/components/tables/part/PartParameterTemplateTable.tsx:96
#~ msgid "Parameter template deleted"
#~ msgstr ""

#: src/components/tables/part/PartParameterTemplateTable.tsx:98
#~ msgid "Are you sure you want to remove this parameter template?"
#~ msgstr ""

#: src/components/tables/part/PartParameterTemplateTable.tsx:110
#~ msgid "Create Parameter Template"
#~ msgstr ""

#: src/components/tables/part/PartParameterTemplateTable.tsx:112
#~ msgid "Parameter template created"
#~ msgstr ""

#: src/components/tables/part/PartTable.tsx:211
#~ msgid "Detail"
#~ msgstr ""

#: src/components/tables/part/PartTestTemplateTable.tsx:30
#~ msgid "Test Name"
#~ msgstr ""

#: src/components/tables/part/PartTestTemplateTable.tsx:86
#~ msgid "Template updated"
#~ msgstr ""

#: src/components/tables/part/PartTestTemplateTable.tsx:98
#~ msgid "Test Template deleted"
#~ msgstr ""

#: src/components/tables/part/PartTestTemplateTable.tsx:115
#~ msgid "Create Test Template"
#~ msgstr ""

#: src/components/tables/part/PartTestTemplateTable.tsx:117
#~ msgid "Template created"
#~ msgstr ""

#: src/components/tables/part/RelatedPartTable.tsx:79
#~ msgid "Related Part"
#~ msgstr ""

#: src/components/tables/part/RelatedPartTable.tsx:82
#~ msgid "Related part added"
#~ msgstr ""

#: src/components/tables/part/RelatedPartTable.tsx:114
#~ msgid "Related part deleted"
#~ msgstr ""

#: src/components/tables/part/RelatedPartTable.tsx:115
#~ msgid "Are you sure you want to remove this relationship?"
#~ msgstr ""

#: src/components/tables/plugin/PluginListTable.tsx:191
#~ msgid "Installation path"
#~ msgstr ""

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:55
#~ msgid "Receive"
#~ msgstr ""

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:81
#~ msgid "Line item updated"
#~ msgstr ""

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:232
#~ msgid "Line item added"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:37
#~ msgid "Definition"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:43
#~ msgid "Symbol"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:59
#~ msgid "Edit custom unit"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:66
#~ msgid "Custom unit updated"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:76
#~ msgid "Delete custom unit"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:77
#~ msgid "Custom unit deleted"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:79
#~ msgid "Are you sure you want to remove this custom unit?"
#~ msgstr ""

#: src/components/tables/settings/CustomUnitsTable.tsx:97
#~ msgid "Custom unit created"
#~ msgstr ""

#: src/components/tables/settings/GroupTable.tsx:45
#~ msgid "Group updated"
#~ msgstr ""

#: src/components/tables/settings/GroupTable.tsx:131
#~ msgid "Added group"
#~ msgstr ""

#: src/components/tables/settings/ProjectCodeTable.tsx:49
#~ msgid "Edit project code"
#~ msgstr ""

#: src/components/tables/settings/ProjectCodeTable.tsx:56
#~ msgid "Project code updated"
#~ msgstr ""

#: src/components/tables/settings/ProjectCodeTable.tsx:66
#~ msgid "Delete project code"
#~ msgstr ""

#: src/components/tables/settings/ProjectCodeTable.tsx:67
#~ msgid "Project code deleted"
#~ msgstr ""

#: src/components/tables/settings/ProjectCodeTable.tsx:69
#~ msgid "Are you sure you want to remove this project code?"
#~ msgstr ""

#: src/components/tables/settings/ProjectCodeTable.tsx:88
#~ msgid "Added project code"
#~ msgstr ""

#: src/components/tables/settings/UserDrawer.tsx:92
#~ msgid "User permission changed successfully"
#~ msgstr ""

#: src/components/tables/settings/UserDrawer.tsx:93
#~ msgid "Some changes might only take effect after the user refreshes their login."
#~ msgstr ""

#: src/components/tables/settings/UserDrawer.tsx:118
#~ msgid "Changed user active status successfully"
#~ msgstr ""

#: src/components/tables/settings/UserDrawer.tsx:119
#~ msgid "Set to {active}"
#~ msgstr ""

#: src/components/tables/settings/UserDrawer.tsx:142
#~ msgid "User details for {0}"
#~ msgstr ""

#: src/components/tables/settings/UserDrawer.tsx:176
#~ msgid "Rights"
#~ msgstr ""

#: src/components/tables/settings/UserTable.tsx:106
#~ msgid "User updated"
#~ msgstr ""

#: src/components/tables/settings/UserTable.tsx:117
#~ msgid "user deleted"
#~ msgstr ""

#: src/components/tables/settings/UserTable.tsx:168
#~ msgid "First Name"
#~ msgstr ""

#: src/components/tables/settings/UserTable.tsx:173
#~ msgid "Last Name"
#~ msgstr ""

#: src/components/tables/stock/StockItemTable.tsx:247
#~ msgid "Test Filter"
#~ msgstr ""

#: src/components/tables/stock/StockItemTable.tsx:248
#~ msgid "This is a test filter"
#~ msgstr ""

#: src/components/tables/stock/StockLocationTable.tsx:145
#~ msgid "Stock location updated"
#~ msgstr ""

#: src/components/widgets/DisplayWidget.tsx:11
#: src/pages/Index/Settings/AccountSettings/DisplaySettingsPanel.tsx:17
msgid "Display Settings"
msgstr ""

#: src/components/widgets/DisplayWidget.tsx:15
#: src/pages/Index/Settings/AccountSettings/DisplaySettingsPanel.tsx:23
msgid "Color Mode"
msgstr ""

#: src/components/widgets/DisplayWidget.tsx:21
#: src/pages/Index/Settings/AccountSettings/DisplaySettingsPanel.tsx:33
msgid "Language"
msgstr ""

#: src/components/widgets/FeedbackWidget.tsx:19
msgid "Something is new: Platform UI"
msgstr ""

#: src/components/widgets/FeedbackWidget.tsx:21
msgid "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."
msgstr ""

#: src/components/widgets/FeedbackWidget.tsx:32
msgid "Provide Feedback"
msgstr ""

#: src/components/widgets/GetStartedWidget.tsx:11
msgid "Getting started"
msgstr ""

#: src/components/widgets/MarkdownEditor.tsx:108
#~ msgid "Failed to upload image"
#~ msgstr ""

#: src/components/widgets/MarkdownEditor.tsx:146
#~ msgid "Notes saved"
#~ msgstr ""

#: src/components/widgets/WidgetLayout.tsx:166
msgid "Layout"
msgstr ""

#: src/components/widgets/WidgetLayout.tsx:172
msgid "Reset Layout"
msgstr ""

#: src/components/widgets/WidgetLayout.tsx:185
msgid "Stop Edit"
msgstr ""

#: src/components/widgets/WidgetLayout.tsx:185
msgid "Edit Layout"
msgstr ""

#: src/components/widgets/WidgetLayout.tsx:191
msgid "Appearance"
msgstr ""

#: src/components/widgets/WidgetLayout.tsx:203
msgid "Show Boxes"
msgstr ""

#: src/contexts/LanguageContext.tsx:20
msgid "Arabic"
msgstr ""

#: src/contexts/LanguageContext.tsx:21
msgid "Bulgarian"
msgstr ""

#: src/contexts/LanguageContext.tsx:22
msgid "Czech"
msgstr ""

#: src/contexts/LanguageContext.tsx:23
msgid "Danish"
msgstr ""

#: src/contexts/LanguageContext.tsx:24
msgid "German"
msgstr ""

#: src/contexts/LanguageContext.tsx:25
msgid "Greek"
msgstr ""

#: src/contexts/LanguageContext.tsx:26
msgid "English"
msgstr ""

#: src/contexts/LanguageContext.tsx:27
msgid "Spanish"
msgstr ""

#: src/contexts/LanguageContext.tsx:28
msgid "Spanish (Mexican)"
msgstr ""

#: src/contexts/LanguageContext.tsx:29
msgid "Estonian"
msgstr ""

#: src/contexts/LanguageContext.tsx:30
msgid "Farsi / Persian"
msgstr ""

#: src/contexts/LanguageContext.tsx:31
msgid "Finnish"
msgstr ""

#: src/contexts/LanguageContext.tsx:32
msgid "French"
msgstr ""

#: src/contexts/LanguageContext.tsx:33
msgid "Hebrew"
msgstr ""

#: src/contexts/LanguageContext.tsx:34
msgid "Hindi"
msgstr ""

#: src/contexts/LanguageContext.tsx:35
msgid "Hungarian"
msgstr ""

#: src/contexts/LanguageContext.tsx:36
msgid "Italian"
msgstr ""

#: src/contexts/LanguageContext.tsx:37
msgid "Japanese"
msgstr ""

#: src/contexts/LanguageContext.tsx:38
msgid "Korean"
msgstr ""

#: src/contexts/LanguageContext.tsx:39
msgid "Latvian"
msgstr ""

#: src/contexts/LanguageContext.tsx:40
msgid "Dutch"
msgstr ""

#: src/contexts/LanguageContext.tsx:41
msgid "Norwegian"
msgstr ""

#: src/contexts/LanguageContext.tsx:42
msgid "Polish"
msgstr ""

#: src/contexts/LanguageContext.tsx:43
msgid "Portuguese"
msgstr ""

#: src/contexts/LanguageContext.tsx:44
msgid "Portuguese (Brazilian)"
msgstr ""

#: src/contexts/LanguageContext.tsx:45
msgid "Romanian"
msgstr ""

#: src/contexts/LanguageContext.tsx:46
msgid "Russian"
msgstr ""

#: src/contexts/LanguageContext.tsx:47
msgid "Slovak"
msgstr ""

#: src/contexts/LanguageContext.tsx:48
msgid "Slovenian"
msgstr ""

#: src/contexts/LanguageContext.tsx:49
msgid "Swedish"
msgstr ""

#: src/contexts/LanguageContext.tsx:50
msgid "Thai"
msgstr ""

#: src/contexts/LanguageContext.tsx:51
msgid "Turkish"
msgstr ""

#: src/contexts/LanguageContext.tsx:52
msgid "Ukrainian"
msgstr ""

#: src/contexts/LanguageContext.tsx:53
msgid "Vietnamese"
msgstr ""

#: src/contexts/LanguageContext.tsx:54
msgid "Chinese (Simplified)"
msgstr ""

#: src/contexts/LanguageContext.tsx:55
msgid "Chinese (Traditional)"
msgstr ""

#: src/defaults/actions.tsx:18
#: src/defaults/links.tsx:27
#: src/defaults/menuItems.tsx:9
msgid "Home"
msgstr ""

#: src/defaults/actions.tsx:25
#: src/defaults/links.tsx:28
#: src/defaults/menuItems.tsx:28
#: src/pages/Index/Dashboard.tsx:19
#: src/pages/Index/Settings/UserSettings.tsx:46
msgid "Dashboard"
msgstr ""

#: src/defaults/actions.tsx:26
msgid "Go to the InvenTree dashboard"
msgstr ""

#: src/defaults/actions.tsx:33
msgid "Visit the documentation to learn more about InvenTree"
msgstr ""

#: src/defaults/actions.tsx:39
#: src/defaults/links.tsx:98
#: src/defaults/links.tsx:128
msgid "About InvenTree"
msgstr ""

#: src/defaults/actions.tsx:40
#: src/defaults/links.tsx:129
msgid "About the InvenTree org"
msgstr ""

#: src/defaults/actions.tsx:46
msgid "Server Information"
msgstr ""

#: src/defaults/actions.tsx:47
#: src/defaults/links.tsx:123
msgid "About this Inventree instance"
msgstr ""

#: src/defaults/actions.tsx:53
#: src/defaults/links.tsx:111
msgid "License Information"
msgstr ""

#: src/defaults/actions.tsx:54
#: src/defaults/links.tsx:135
msgid "Licenses for dependencies of the service"
msgstr ""

#: src/defaults/actions.tsx:61
msgid "Open the main navigation menu"
msgstr ""

#: src/defaults/actions.tsx:72
msgid "Go to the Admin Center"
msgstr ""

#: src/defaults/dashboardItems.tsx:15
msgid "Subscribed Parts"
msgstr ""

#: src/defaults/dashboardItems.tsx:22
msgid "Subscribed Categories"
msgstr ""

#: src/defaults/dashboardItems.tsx:29
msgid "Latest Parts"
msgstr ""

#: src/defaults/dashboardItems.tsx:36
msgid "BOM Waiting Validation"
msgstr ""

#: src/defaults/dashboardItems.tsx:43
msgid "Recently Updated"
msgstr ""

#: src/defaults/dashboardItems.tsx:50
#: src/tables/part/PartTable.tsx:238
msgid "Low Stock"
msgstr ""

#: src/defaults/dashboardItems.tsx:57
msgid "Depleted Stock"
msgstr ""

#: src/defaults/dashboardItems.tsx:64
msgid "Required for Build Orders"
msgstr ""

#: src/defaults/dashboardItems.tsx:71
msgid "Expired Stock"
msgstr ""

#: src/defaults/dashboardItems.tsx:78
msgid "Stale Stock"
msgstr ""

#: src/defaults/dashboardItems.tsx:85
msgid "Build Orders In Progress"
msgstr ""

#: src/defaults/dashboardItems.tsx:92
msgid "Overdue Build Orders"
msgstr ""

#: src/defaults/dashboardItems.tsx:99
msgid "Outstanding Purchase Orders"
msgstr ""

#: src/defaults/dashboardItems.tsx:106
msgid "Overdue Purchase Orders"
msgstr ""

#: src/defaults/dashboardItems.tsx:113
msgid "Outstanding Sales Orders"
msgstr ""

#: src/defaults/dashboardItems.tsx:120
msgid "Overdue Sales Orders"
msgstr ""

#: src/defaults/dashboardItems.tsx:127
msgid "Current News"
msgstr ""

#: src/defaults/defaultHostList.tsx:8
#~ msgid "InvenTree Demo"
#~ msgstr ""

#: src/defaults/defaultHostList.tsx:16
#~ msgid "Local Server"
#~ msgstr ""

#: src/defaults/links.tsx:12
#: src/pages/company/CompanyDetail.tsx:96
msgid "Website"
msgstr ""

#: src/defaults/links.tsx:17
msgid "GitHub"
msgstr ""

#: src/defaults/links.tsx:22
msgid "Demo"
msgstr ""

#: src/defaults/links.tsx:33
#: src/defaults/menuItems.tsx:48
#: src/pages/company/ManufacturerDetail.tsx:9
#: src/pages/company/ManufacturerPartDetail.tsx:263
#: src/pages/company/SupplierDetail.tsx:9
#: src/pages/company/SupplierPartDetail.tsx:347
#: src/pages/purchasing/PurchaseOrderDetail.tsx:483
#: src/pages/purchasing/PurchasingIndex.tsx:60
msgid "Purchasing"
msgstr ""

#: src/defaults/links.tsx:37
#: src/defaults/menuItems.tsx:53
#: src/pages/company/CustomerDetail.tsx:9
#: src/pages/sales/ReturnOrderDetail.tsx:474
#: src/pages/sales/SalesIndex.tsx:53
#: src/pages/sales/SalesOrderDetail.tsx:525
msgid "Sales"
msgstr ""

#: src/defaults/links.tsx:41
#: src/defaults/menuItems.tsx:71
#: src/pages/Index/Playground.tsx:217
msgid "Playground"
msgstr ""

#: src/defaults/links.tsx:55
msgid "Getting Started"
msgstr ""

#: src/defaults/links.tsx:56
msgid "Getting started with InvenTree"
msgstr ""

#: src/defaults/links.tsx:62
msgid "API"
msgstr ""

#: src/defaults/links.tsx:63
msgid "InvenTree API documentation"
msgstr ""

#: src/defaults/links.tsx:68
msgid "Developer Manual"
msgstr ""

#: src/defaults/links.tsx:69
msgid "InvenTree developer manual"
msgstr ""

#: src/defaults/links.tsx:74
msgid "FAQ"
msgstr ""

#: src/defaults/links.tsx:75
msgid "Frequently asked questions"
msgstr ""

#: src/defaults/links.tsx:76
#~ msgid "Instance"
#~ msgstr ""

#: src/defaults/links.tsx:83
#~ msgid "InvenTree"
#~ msgstr ""

#: src/defaults/links.tsx:85
#: src/defaults/links.tsx:122
msgid "System Information"
msgstr ""

#: src/defaults/links.tsx:117
#~ msgid "Licenses for packages used by InvenTree"
#~ msgstr ""

#: src/defaults/links.tsx:134
msgid "Licenses"
msgstr ""

#: src/defaults/menuItems.tsx:7
#~ msgid "Open sourcea"
#~ msgstr ""

#: src/defaults/menuItems.tsx:9
#~ msgid "Open source"
#~ msgstr ""

#: src/defaults/menuItems.tsx:10
#~ msgid "Start page of your instance."
#~ msgstr ""

#: src/defaults/menuItems.tsx:10
#~ msgid "This Pokémon’s cry is very loud and distracting"
#~ msgstr ""

#: src/defaults/menuItems.tsx:12
#~ msgid "This Pokémon’s cry is very loud and distracting and more and more and more"
#~ msgstr ""

#: src/defaults/menuItems.tsx:15
#~ msgid "Profile page"
#~ msgstr ""

#: src/defaults/menuItems.tsx:17
msgid "User attributes and design settings."
msgstr ""

#: src/defaults/menuItems.tsx:21
#~ msgid "Free for everyone"
#~ msgstr ""

#: src/defaults/menuItems.tsx:22
#~ msgid "The fluid of Smeargle’s tail secretions changes"
#~ msgstr ""

#: src/defaults/menuItems.tsx:23
msgid "View for interactive scanning and multiple actions."
msgstr ""

#: src/defaults/menuItems.tsx:24
#~ msgid "The fluid of Smeargle’s tail secretions changes in the intensity"
#~ msgstr ""

#: src/defaults/menuItems.tsx:32
#~ msgid "abc"
#~ msgstr ""

#: src/defaults/menuItems.tsx:37
#~ msgid "Random image"
#~ msgstr ""

#: src/defaults/menuItems.tsx:40
#~ msgid "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"
#~ msgstr ""

#: src/defaults/menuItems.tsx:105
#~ msgid "Yanma is capable of seeing 360 degrees without"
#~ msgstr ""

#: src/defaults/menuItems.tsx:111
#~ msgid "The shell’s rounded shape and the grooves on its."
#~ msgstr ""

#: src/defaults/menuItems.tsx:116
#~ msgid "Analytics"
#~ msgstr ""

#: src/defaults/menuItems.tsx:118
#~ msgid "This Pokémon uses its flying ability to quickly chase"
#~ msgstr ""

#: src/defaults/menuItems.tsx:125
#~ msgid "Combusken battles with the intensely hot flames it spews"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add File"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add Link"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:58
#~ msgid "File added"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:58
#~ msgid "Link added"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit File"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit Link"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:100
#~ msgid "File updated"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:100
#~ msgid "Link updated"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:125
#~ msgid "Attachment deleted"
#~ msgstr ""

#: src/forms/AttachmentForms.tsx:128
#~ msgid "Are you sure you want to delete this attachment?"
#~ msgstr ""

#: src/forms/BuildForms.tsx:158
msgid "Next serial number"
msgstr ""

#: src/forms/BuildForms.tsx:162
msgid "Latest serial number"
msgstr ""

#: src/forms/BuildForms.tsx:234
msgid "Remove output"
msgstr ""

#: src/forms/BuildForms.tsx:316
msgid "Complete Build Outputs"
msgstr ""

#: src/forms/BuildForms.tsx:320
msgid "Build outputs have been completed"
msgstr ""

#: src/forms/BuildForms.tsx:389
msgid "Scrap Build Outputs"
msgstr ""

#: src/forms/BuildForms.tsx:393
msgid "Build outputs have been scrapped"
msgstr ""

#: src/forms/BuildForms.tsx:425
#: src/forms/BuildForms.tsx:449
msgid "Cancel Build Outputs"
msgstr ""

#: src/forms/BuildForms.tsx:426
msgid "Selected build outputs will be deleted"
msgstr ""

#: src/forms/BuildForms.tsx:453
msgid "Build outputs have been cancelled"
msgstr ""

#: src/forms/CompanyForms.tsx:150
#~ msgid "Company updated"
#~ msgstr ""

#: src/forms/PartForms.tsx:106
#~ msgid "Create Part"
#~ msgstr ""

#: src/forms/PartForms.tsx:108
#~ msgid "Part created"
#~ msgstr ""

#: src/forms/PartForms.tsx:124
msgid "Parent part category"
msgstr ""

#: src/forms/PartForms.tsx:129
#~ msgid "Part updated"
#~ msgstr ""

#: src/forms/PurchaseOrderForms.tsx:300
msgid "Choose Location"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:308
msgid "Item Destination selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:317
msgid "Part category default location selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:327
msgid "Received stock location selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:332
msgid "Default location selected"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:343
#: src/forms/PurchaseOrderForms.tsx:435
msgid "Scan Barcode"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:388
msgid "Set Location"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:396
msgid "Assign Batch Code{0}"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:405
#: src/forms/StockForms.tsx:420
msgid "Adjust Packaging"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:412
msgid "Change Status"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:418
msgid "Add Note"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:444
#: src/forms/StockForms.tsx:428
msgid "Remove item from list"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:471
#: src/forms/StockForms.tsx:499
#: src/forms/StockForms.tsx:533
#: src/forms/StockForms.tsx:562
#: src/forms/StockForms.tsx:590
#: src/forms/StockForms.tsx:621
#: src/forms/StockForms.tsx:656
#: src/forms/StockForms.tsx:698
#: src/forms/StockForms.tsx:734
#: src/pages/stock/StockDetail.tsx:181
#: src/tables/ColumnRenderers.tsx:49
#: src/tables/stock/StockTrackingTable.tsx:85
msgid "Location"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:486
msgid "Store at default location"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:499
msgid "Store at line item destination"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:509
msgid "Store with already received stock"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:524
#: src/pages/build/BuildDetail.tsx:219
#: src/pages/stock/StockDetail.tsx:162
#: src/pages/stock/StockDetail.tsx:599
#: src/tables/build/BuildAllocatedStockTable.tsx:109
#: src/tables/build/BuildOrderTestTable.tsx:188
#: src/tables/sales/SalesOrderAllocationTable.tsx:87
msgid "Batch Code"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:535
msgid "Serial numbers"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:544
#: src/forms/StockForms.tsx:443
#: src/pages/company/SupplierPartDetail.tsx:156
#: src/pages/company/SupplierPartDetail.tsx:207
#: src/pages/stock/StockDetail.tsx:244
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:192
msgid "Packaging"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:556
#: src/pages/build/BuildDetail.tsx:109
#: src/pages/purchasing/PurchaseOrderDetail.tsx:148
#: src/pages/sales/ReturnOrderDetail.tsx:121
#: src/pages/sales/SalesOrderDetail.tsx:127
#: src/tables/build/BuildOrderTable.tsx:122
#: src/tables/machine/MachineListTable.tsx:335
#: src/tables/part/PartPurchaseOrdersTable.tsx:37
#: src/tables/purchasing/PurchaseOrderTable.tsx:55
#: src/tables/sales/ReturnOrderLineItemTable.tsx:132
#: src/tables/sales/ReturnOrderTable.tsx:46
#: src/tables/sales/SalesOrderTable.tsx:53
#: src/tables/settings/ImportSessionTable.tsx:115
#: src/tables/stock/StockItemTable.tsx:294
#: src/tables/stock/StockTrackingTable.tsx:52
msgid "Status"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:564
#: src/pages/company/SupplierPartDetail.tsx:110
#: src/tables/ColumnRenderers.tsx:132
msgid "Note"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:639
#: src/pages/company/SupplierPartDetail.tsx:128
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:57
msgid "SKU"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:639
#: src/tables/part/PartPurchaseOrdersTable.tsx:120
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:178
#: src/tables/sales/ReturnOrderLineItemTable.tsx:127
msgid "Received"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:639
#: src/forms/StockForms.tsx:499
#: src/forms/StockForms.tsx:533
#: src/forms/StockForms.tsx:562
#: src/forms/StockForms.tsx:590
#: src/forms/StockForms.tsx:621
#: src/forms/StockForms.tsx:656
#: src/forms/StockForms.tsx:698
#: src/forms/StockForms.tsx:734
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:413
#: src/tables/RowActions.tsx:113
msgid "Actions"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:655
msgid "Receive Line Items"
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:658
#~ msgid "Receive line items"
#~ msgstr ""

#: src/forms/StockForms.tsx:104
msgid "Add given quantity as packs instead of individual items"
msgstr ""

#: src/forms/StockForms.tsx:110
#~ msgid "Create Stock Item"
#~ msgstr ""

#: src/forms/StockForms.tsx:117
msgid "Enter initial quantity for this stock item"
msgstr ""

#: src/forms/StockForms.tsx:124
msgid "Serial Numbers"
msgstr ""

#: src/forms/StockForms.tsx:125
msgid "Enter serial numbers for new stock (or leave blank)"
msgstr ""

#: src/forms/StockForms.tsx:158
#~ msgid "Stock item updated"
#~ msgstr ""

#: src/forms/StockForms.tsx:179
#: src/pages/stock/StockDetail.tsx:434
#: src/tables/stock/StockItemTable.tsx:417
#: src/tables/stock/StockItemTable.tsx:535
msgid "Add Stock Item"
msgstr ""

#: src/forms/StockForms.tsx:362
msgid "Loading..."
msgstr ""

#: src/forms/StockForms.tsx:408
msgid "Move to default location"
msgstr ""

#: src/forms/StockForms.tsx:499
#: src/forms/StockForms.tsx:533
#: src/forms/StockForms.tsx:562
#: src/forms/StockForms.tsx:590
#: src/forms/StockForms.tsx:621
#: src/forms/StockForms.tsx:656
#: src/forms/StockForms.tsx:698
#: src/forms/StockForms.tsx:734
#: src/pages/part/PartDetail.tsx:230
#: src/pages/part/PartDetail.tsx:843
#: src/tables/stock/StockItemTable.tsx:325
msgid "In Stock"
msgstr ""

#: src/forms/StockForms.tsx:499
msgid "Move"
msgstr ""

#: src/forms/StockForms.tsx:562
#: src/pages/stock/StockDetail.tsx:518
#: src/tables/stock/StockItemTestResultTable.tsx:328
msgid "Add"
msgstr ""

#: src/forms/StockForms.tsx:590
#: src/pages/Index/Scan.tsx:266
#: src/pages/stock/StockDetail.tsx:508
msgid "Count"
msgstr ""

#: src/forms/StockForms.tsx:835
msgid "Add Stock"
msgstr ""

#: src/forms/StockForms.tsx:844
msgid "Remove Stock"
msgstr ""

#: src/forms/StockForms.tsx:853
#: src/pages/part/PartDetail.tsx:1017
msgid "Transfer Stock"
msgstr ""

#: src/forms/StockForms.tsx:862
#: src/pages/part/PartDetail.tsx:1006
msgid "Count Stock"
msgstr ""

#: src/forms/StockForms.tsx:871
msgid "Change Stock Status"
msgstr ""

#: src/forms/StockForms.tsx:880
msgid "Merge Stock"
msgstr ""

#: src/forms/StockForms.tsx:899
msgid "Delete Stock Items"
msgstr ""

#: src/forms/StockForms.tsx:906
msgid "Parent stock location"
msgstr ""

#: src/functions/auth.tsx:34
#~ msgid "Error fetching token from server."
#~ msgstr ""

#: src/functions/auth.tsx:36
#~ msgid "Logout successfull"
#~ msgstr ""

#: src/functions/auth.tsx:60
#~ msgid "See you soon."
#~ msgstr ""

#: src/functions/auth.tsx:70
#~ msgid "Logout successful"
#~ msgstr ""

#: src/functions/auth.tsx:71
#~ msgid "You have been logged out"
#~ msgstr ""

#: src/functions/auth.tsx:117
msgid "Logged Out"
msgstr ""

#: src/functions/auth.tsx:118
msgid "Successfully logged out"
msgstr ""

#: src/functions/auth.tsx:141
#~ msgid "Already logged in"
#~ msgstr ""

#: src/functions/auth.tsx:142
#~ msgid "Found an existing login - using it to log you in."
#~ msgstr ""

#: src/functions/auth.tsx:143
#~ msgid "Found an existing login - welcome back!"
#~ msgstr ""

#: src/functions/auth.tsx:156
msgid "Check your inbox for a reset link. This only works if you have an account. Check in spam too."
msgstr ""

#: src/functions/auth.tsx:163
#: src/pages/Auth/Set-Password.tsx:39
msgid "Reset failed"
msgstr ""

#: src/functions/auth.tsx:194
msgid "Logged In"
msgstr ""

#: src/functions/auth.tsx:195
msgid "Successfully logged in"
msgstr ""

#: src/functions/forms.tsx:50
#~ msgid "Form method not provided"
#~ msgstr ""

#: src/functions/forms.tsx:59
#~ msgid "Response did not contain action data"
#~ msgstr ""

#: src/functions/forms.tsx:182
#~ msgid "Invalid Form"
#~ msgstr ""

#: src/functions/forms.tsx:183
#~ msgid "method parameter not supplied"
#~ msgstr ""

#: src/functions/notifications.tsx:10
msgid "Not implemented"
msgstr ""

#: src/functions/notifications.tsx:11
msgid "This feature is not yet implemented"
msgstr ""

#: src/functions/notifications.tsx:21
msgid "Permission denied"
msgstr ""

#: src/functions/notifications.tsx:22
msgid "You do not have permission to perform this action"
msgstr ""

#: src/functions/notifications.tsx:33
msgid "Invalid Return Code"
msgstr ""

#: src/functions/notifications.tsx:34
msgid "Server returned status {returnCode}"
msgstr ""

#: src/hooks/UseForm.tsx:88
msgid "Item Created"
msgstr ""

#: src/hooks/UseForm.tsx:105
msgid "Item Updated"
msgstr ""

#: src/hooks/UseForm.tsx:124
msgid "Item Deleted"
msgstr ""

#: src/hooks/UseForm.tsx:128
msgid "Are you sure you want to delete this item?"
msgstr ""

#: src/pages/Auth/Logged-In.tsx:22
msgid "Checking if you are already logged in"
msgstr ""

#: src/pages/Auth/Login.tsx:31
#: src/pages/Index/Scan.tsx:329
msgid "No selection"
msgstr ""

#: src/pages/Auth/Login.tsx:87
msgid "Welcome, log in below"
msgstr ""

#: src/pages/Auth/Login.tsx:89
msgid "Register below"
msgstr ""

#: src/pages/Auth/Login.tsx:121
#~ msgid "Edit host options"
#~ msgstr ""

#: src/pages/Auth/Logout.tsx:22
msgid "Logging out"
msgstr ""

#: src/pages/Auth/Reset.tsx:41
#: src/pages/Auth/Set-Password.tsx:112
msgid "Send mail"
msgstr ""

#: src/pages/Auth/Set-Password.tsx:30
msgid "Token invalid"
msgstr ""

#: src/pages/Auth/Set-Password.tsx:31
msgid "You need to provide a valid token to set a new password. Check your inbox for a reset link."
msgstr ""

#: src/pages/Auth/Set-Password.tsx:49
msgid "No token provided"
msgstr ""

#: src/pages/Auth/Set-Password.tsx:50
msgid "You need to provide a token to set a new password. Check your inbox for a reset link."
msgstr ""

#: src/pages/Auth/Set-Password.tsx:73
msgid "Password set"
msgstr ""

#: src/pages/Auth/Set-Password.tsx:74
msgid "The password was set successfully. You can now login with your new password"
msgstr ""

#: src/pages/Auth/Set-Password.tsx:101
msgid "Set new password"
msgstr ""

#: src/pages/ErrorPage.tsx:16
msgid "Error: {0}"
msgstr ""

#: src/pages/ErrorPage.tsx:23
msgid "An unexpected error has occurred"
msgstr ""

#: src/pages/ErrorPage.tsx:28
#~ msgid "Sorry, an unexpected error has occurred."
#~ msgstr ""

#: src/pages/Index/Dashboard.tsx:22
msgid "Autoupdate"
msgstr ""

#: src/pages/Index/Dashboard.tsx:26
msgid "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."
msgstr ""

#: src/pages/Index/Home.tsx:58
msgid "Welcome to your Dashboard{0}"
msgstr ""

#: src/pages/Index/Playground.tsx:222
msgid "This page is a showcase for the possibilities of Platform UI."
msgstr ""

#: src/pages/Index/Profile/Profile.tsx:30
#: src/pages/Index/Profile/Profile.tsx:141
#~ msgid "Notification Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:33
#~ msgid "Global Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:47
#~ msgid "Settings for the current user"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:51
#~ msgid "Home Page Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:76
#~ msgid "Search Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:115
#: src/pages/Index/Profile/Profile.tsx:211
#~ msgid "Label Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:120
#: src/pages/Index/Profile/Profile.tsx:219
#~ msgid "Report Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:142
#~ msgid "Settings for the notifications"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:148
#~ msgid "Global Server Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:149
#~ msgid "Global Settings for this instance"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:153
#~ msgid "Server Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:187
#~ msgid "Login Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:202
#~ msgid "Barcode Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:230
#~ msgid "Part Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:255
#~ msgid "Pricing Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:270
#~ msgid "Stock Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:284
#~ msgid "Build Order Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:289
#~ msgid "Purchase Order Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:300
#~ msgid "Sales Order Settings"
#~ msgstr ""

#: src/pages/Index/Profile/Profile.tsx:330
#~ msgid "Plugin Settings for this instance"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:27
#~ msgid "Data is current beeing loaded"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:69
#: src/pages/Index/Profile/SettingsPanel.tsx:76
#~ msgid "Failed to load"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:100
#~ msgid "Show internal names"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:148
#~ msgid "Input {0} is not known"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:161
#~ msgid "Saved changes {0}"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:162
#~ msgid "Changed to {0}"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:176
#~ msgid "Error while saving {0}"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:177
#~ msgid "Error was {err}"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:257
#~ msgid "Plugin: {0}"
#~ msgstr ""

#: src/pages/Index/Profile/SettingsPanel.tsx:262
#~ msgid "Method: {0}"
#~ msgstr ""

#: src/pages/Index/Profile/UserPanel.tsx:85
#~ msgid "Userinfo"
#~ msgstr ""

#: src/pages/Index/Profile/UserPanel.tsx:122
#~ msgid "Username: {0}"
#~ msgstr ""

#: src/pages/Index/Profile/UserTheme.tsx:83
#~ msgid "Design <0/>"
#~ msgstr ""

#: src/pages/Index/Scan.tsx:216
msgid "Manual input"
msgstr ""

#: src/pages/Index/Scan.tsx:217
msgid "Image Barcode"
msgstr ""

#: src/pages/Index/Scan.tsx:247
msgid "Selected elements are not known"
msgstr ""

#: src/pages/Index/Scan.tsx:254
msgid "Multiple object types selected"
msgstr ""

#: src/pages/Index/Scan.tsx:261
msgid "Actions for {0}"
msgstr ""

#: src/pages/Index/Scan.tsx:282
msgid "Scan Page"
msgstr ""

#: src/pages/Index/Scan.tsx:285
msgid "This page can be used for continuously scanning items and taking actions on them."
msgstr ""

#: src/pages/Index/Scan.tsx:292
msgid "Toggle Fullscreen"
msgstr ""

#: src/pages/Index/Scan.tsx:305
msgid "Select the input method you want to use to scan items."
msgstr ""

#: src/pages/Index/Scan.tsx:307
msgid "Input"
msgstr ""

#: src/pages/Index/Scan.tsx:314
msgid "Select input method"
msgstr ""

#: src/pages/Index/Scan.tsx:315
msgid "Nothing found"
msgstr ""

#: src/pages/Index/Scan.tsx:323
msgid "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."
msgstr ""

#: src/pages/Index/Scan.tsx:325
msgid "Action"
msgstr ""

#: src/pages/Index/Scan.tsx:334
msgid "{0} items selected"
msgstr ""

#: src/pages/Index/Scan.tsx:337
msgid "General Actions"
msgstr ""

#: src/pages/Index/Scan.tsx:351
msgid "Lookup part"
msgstr ""

#: src/pages/Index/Scan.tsx:359
msgid "Open Link"
msgstr ""

#: src/pages/Index/Scan.tsx:375
msgid "History is locally kept in this browser."
msgstr ""

#: src/pages/Index/Scan.tsx:376
msgid "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."
msgstr ""

#: src/pages/Index/Scan.tsx:378
#: src/pages/Notifications.tsx:100
msgid "History"
msgstr ""

#: src/pages/Index/Scan.tsx:384
msgid "Delete History"
msgstr ""

#: src/pages/Index/Scan.tsx:449
msgid "No history"
msgstr ""

#: src/pages/Index/Scan.tsx:467
msgid "Item"
msgstr ""

#: src/pages/Index/Scan.tsx:470
msgid "Type"
msgstr ""

#: src/pages/Index/Scan.tsx:473
msgid "Source"
msgstr ""

#: src/pages/Index/Scan.tsx:476
msgid "Scanned at"
msgstr ""

#: src/pages/Index/Scan.tsx:528
msgid "Enter item serial or data"
msgstr ""

#: src/pages/Index/Scan.tsx:540
msgid "Add dummy item"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:41
msgid "Account Details"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:50
msgid "First name"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
msgid "Last name"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:58
#~ msgid "First name: {0}"
#~ msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:61
#~ msgid "Last name: {0}"
#~ msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:67
msgid "First name:"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:71
msgid "Last name:"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/DisplaySettingsPanel.tsx:41
msgid "Use pseudo language"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:55
msgid "Single Sign On Accounts"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:62
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:80
msgid "Not enabled"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:65
msgid "Single Sign On is not enabled for this server"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
msgid "Multifactor"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:83
msgid "Multifactor authentication is not configured for your account"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:92
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:407
msgid "Token"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:139
msgid "The following email addresses are associated with your account:"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:151
msgid "Primary"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:156
msgid "Verified"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:160
msgid "Unverified"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:173
msgid "Add Email Address"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:176
msgid "E-Mail"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:177
msgid "E-Mail address"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:189
msgid "Make Primary"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:194
msgid "Re-send Verification"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:205
msgid "Add Email"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:270
msgid "Provider has not been configured"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:280
msgid "Not configured"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:283
msgid "There are no social network accounts connected to this account."
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:293
msgid "You can sign in to your account using any of the following third party accounts"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:368
msgid "Token is used - no actions"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:375
msgid "Revoke"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:389
msgid "No tokens configured"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:398
#: src/pages/part/PartDetail.tsx:297
#: src/tables/bom/UsedInTable.tsx:84
#: src/tables/build/BuildOrderTable.tsx:110
#: src/tables/company/CompanyTable.tsx:61
#: src/tables/company/CompanyTable.tsx:95
#: src/tables/machine/MachineListTable.tsx:332
#: src/tables/machine/MachineListTable.tsx:594
#: src/tables/part/ParametricPartTable.tsx:223
#: src/tables/part/PartTable.tsx:178
#: src/tables/part/PartVariantTable.tsx:15
#: src/tables/plugin/PluginListTable.tsx:149
#: src/tables/plugin/PluginListTable.tsx:271
#: src/tables/plugin/PluginListTable.tsx:563
#: src/tables/purchasing/SupplierPartTable.tsx:98
#: src/tables/purchasing/SupplierPartTable.tsx:187
#: src/tables/settings/UserTable.tsx:284
#: src/tables/stock/StockItemTable.tsx:289
msgid "Active"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:401
msgid "Expiry"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:404
msgid "Last Seen"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:65
msgid "bars"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:66
msgid "oval"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
msgid "dots"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:81
msgid "Theme"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:87
msgid "Primary color"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:100
msgid "White color"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:108
msgid "Black color"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:116
msgid "Border Radius"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:132
msgid "Loader"
msgstr ""

#: src/pages/Index/Settings/AdminCenter.tsx:30
#~ msgid "User Management"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter.tsx:91
#~ msgid "Advanced Amininistrative Options for InvenTree"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:104
msgid "Data Import"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:110
msgid "Background Tasks"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:116
msgid "Error Reports"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:122
msgid "Currencies"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:127
#~ msgid "Templates"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:140
msgid "Custom Units"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:146
#: src/pages/part/CategoryDetail.tsx:264
msgid "Part Parameters"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:152
msgid "Category Parameters"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:170
msgid "Location Types"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:170
#~ msgid "Location types"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:182
#: src/tables/machine/MachineTypeTable.tsx:287
msgid "Machines"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:192
msgid "Quick Actions"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:197
msgid "Add a new user"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:222
msgid "Advanced Options"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:43
msgid "Machine types"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:53
msgid "Machine Error Stack"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:62
msgid "There are no machine registry errors."
msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:28
#: src/tables/settings/UserTable.tsx:119
msgid "Info"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:32
msgid "External plugins are not enabled for this InvenTree installation."
msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#~ msgid "Plugin Error Stack"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:45
#~ msgid "Warning"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:47
#~ msgid "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:50
msgid "Plugin Errors"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:59
msgid "Plugin Settings"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:53
msgid "Background Worker Not Running"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:54
msgid "The background task manager service is not running. Contact your system administrator."
msgstr ""

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:60
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:76
msgid "Pending Tasks"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:64
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:84
msgid "Scheduled Tasks"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:68
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:92
msgid "Failed Tasks"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:39
#~ msgid "Label"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:67
#~ msgid "Stock item"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:76
#~ msgid "Build line"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:88
#~ msgid "Reports"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:89
#: src/pages/build/BuildDetail.tsx:373
#~ msgid "Report"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:99
#~ msgid "Purchase order"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:108
#~ msgid "Sales order"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:117
#~ msgid "Return order"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:145
#~ msgid "Tests"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:154
#~ msgid "Stock location"
#~ msgstr ""

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:32
msgid "Select settings relevant for user lifecycle. More available in"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:37
msgid "System settings"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:67
msgid "Login"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:93
msgid "Barcodes"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:115
msgid "Pricing"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:118
#~ msgid "Physical Units"
#~ msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:135
#~ msgid "Exchange Rates"
#~ msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:150
msgid "Labels"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:156
#: src/pages/Index/Settings/UserSettings.tsx:132
msgid "Reporting"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:231
#: src/pages/part/PartDetail.tsx:682
msgid "Stocktake"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:237
#: src/pages/build/BuildDetail.tsx:554
#: src/pages/build/BuildIndex.tsx:22
#: src/pages/part/PartDetail.tsx:616
#: src/pages/sales/SalesOrderDetail.tsx:333
msgid "Build Orders"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:318
msgid "Switch to User Setting"
msgstr ""

#: src/pages/Index/Settings/UserSettings.tsx:34
msgid "Account"
msgstr ""

#: src/pages/Index/Settings/UserSettings.tsx:40
msgid "Security"
msgstr ""

#: src/pages/Index/Settings/UserSettings.tsx:78
msgid "Display Options"
msgstr ""

#: src/pages/Index/Settings/UserSettings.tsx:150
msgid "Account Settings"
msgstr ""

#: src/pages/Index/Settings/UserSettings.tsx:158
msgid "Switch to System Setting"
msgstr ""

#: src/pages/Index/UserSettings.tsx:103
#~ msgid "User Settings"
#~ msgstr ""

#: src/pages/Logged-In.tsx:24
#~ msgid "Found an exsisting login - using it to log you in."
#~ msgstr ""

#: src/pages/NotFound.tsx:17
#~ msgid "Not Found"
#~ msgstr ""

#: src/pages/NotFound.tsx:20
#~ msgid "Sorry, this page is not known or was moved."
#~ msgstr ""

#: src/pages/NotFound.tsx:27
#~ msgid "Go to the start page"
#~ msgstr ""

#: src/pages/Notifications.tsx:43
msgid "Delete Notifications"
msgstr ""

#: src/pages/Notifications.tsx:108
msgid "Mark as unread"
msgstr ""

#: src/pages/build/BuildDetail.tsx:80
#~ msgid "Build Status"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:102
#: src/pages/part/PartDetail.tsx:150
#: src/tables/bom/BomTable.tsx:118
#: src/tables/bom/UsedInTable.tsx:39
#: src/tables/build/BuildLineTable.tsx:173
#: src/tables/build/BuildOrderTable.tsx:55
#: src/tables/sales/SalesOrderLineItemTable.tsx:62
#: src/tables/stock/StockItemTable.tsx:53
msgid "IPN"
msgstr ""

#: src/pages/build/BuildDetail.tsx:115
#: src/pages/purchasing/PurchaseOrderDetail.tsx:121
#: src/pages/sales/ReturnOrderDetail.tsx:95
#: src/pages/sales/SalesOrderDetail.tsx:101
#: src/tables/ColumnRenderers.tsx:121
#: src/tables/build/BuildAllocatedStockTable.tsx:90
#: src/tables/build/BuildLineTable.tsx:184
msgid "Reference"
msgstr ""

#: src/pages/build/BuildDetail.tsx:121
#: src/pages/company/CompanyDetail.tsx:90
#: src/pages/company/ManufacturerPartDetail.tsx:83
#: src/pages/company/SupplierPartDetail.tsx:96
#: src/pages/part/CategoryDetail.tsx:101
#: src/pages/part/PartDetail.tsx:157
#: src/pages/purchasing/PurchaseOrderDetail.tsx:142
#: src/pages/sales/ReturnOrderDetail.tsx:115
#: src/pages/sales/SalesOrderDetail.tsx:121
#: src/pages/stock/LocationDetail.tsx:109
#: src/tables/ColumnRenderers.tsx:81
#: src/tables/bom/UsedInTable.tsx:44
#: src/tables/build/BuildAllocatedStockTable.tsx:70
#: src/tables/build/BuildLineTable.tsx:178
#: src/tables/machine/MachineTypeTable.tsx:69
#: src/tables/machine/MachineTypeTable.tsx:112
#: src/tables/machine/MachineTypeTable.tsx:219
#: src/tables/machine/MachineTypeTable.tsx:323
#: src/tables/part/RelatedPartTable.tsx:64
#: src/tables/plugin/PluginListTable.tsx:129
#: src/tables/plugin/PluginListTable.tsx:276
#: src/tables/sales/SalesOrderAllocationTable.tsx:56
#: src/tables/sales/SalesOrderLineItemTable.tsx:67
#: src/tables/stock/LocationTypesTable.tsx:74
msgid "Description"
msgstr ""

#: src/pages/build/BuildDetail.tsx:129
msgid "Parent Build"
msgstr ""

#: src/pages/build/BuildDetail.tsx:140
msgid "Build Quantity"
msgstr ""

#: src/pages/build/BuildDetail.tsx:148
#: src/pages/build/BuildDetail.tsx:274
msgid "Completed Outputs"
msgstr ""

#: src/pages/build/BuildDetail.tsx:165
#: src/tables/build/BuildOrderTable.tsx:151
msgid "Issued By"
msgstr ""

#: src/pages/build/BuildDetail.tsx:172
#: src/pages/part/PartDetail.tsx:365
#: src/pages/purchasing/PurchaseOrderDetail.tsx:220
#: src/pages/sales/ReturnOrderDetail.tsx:199
#: src/pages/sales/SalesOrderDetail.tsx:208
#: src/tables/build/BuildOrderTable.tsx:157
#: src/tables/purchasing/PurchaseOrderTable.tsx:75
#: src/tables/sales/ReturnOrderTable.tsx:66
#: src/tables/sales/SalesOrderTable.tsx:73
msgid "Responsible"
msgstr ""

#: src/pages/build/BuildDetail.tsx:179
#: src/tables/settings/PendingTasksTable.tsx:32
msgid "Created"
msgstr ""

#: src/pages/build/BuildDetail.tsx:185
#: src/pages/part/PartDetail.tsx:269
#: src/pages/stock/StockDetail.tsx:150
#~ msgid "View part barcode"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:186
#: src/pages/purchasing/PurchaseOrderDetail.tsx:213
#: src/pages/sales/ReturnOrderDetail.tsx:192
#: src/pages/sales/SalesOrderDetail.tsx:201
#: src/tables/ColumnRenderers.tsx:212
#: src/tables/part/PartPurchaseOrdersTable.tsx:99
#: src/tables/sales/ReturnOrderLineItemTable.tsx:110
#: src/tables/sales/SalesOrderLineItemTable.tsx:99
msgid "Target Date"
msgstr ""

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/part/PartDetail.tsx:274
#~ msgid "Link custom barcode to part"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:193
msgid "Completed"
msgstr ""

#: src/pages/build/BuildDetail.tsx:196
#: src/pages/part/PartDetail.tsx:280
#~ msgid "Unlink custom barcode from part"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:202
#~ msgid "Build Order updated"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:205
msgid "Source Location"
msgstr ""

#: src/pages/build/BuildDetail.tsx:206
msgid "Any location"
msgstr ""

#: src/pages/build/BuildDetail.tsx:213
msgid "Destination Location"
msgstr ""

#: src/pages/build/BuildDetail.tsx:221
#~ msgid "Edit build order"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:226
#~ msgid "Duplicate build order"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:231
#~ msgid "Delete build order"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:251
msgid "Build Details"
msgstr ""

#: src/pages/build/BuildDetail.tsx:257
#: src/pages/purchasing/PurchaseOrderDetail.tsx:258
#: src/pages/purchasing/PurchaseOrderDetail.tsx:267
#: src/pages/sales/ReturnOrderDetail.tsx:130
#: src/pages/sales/ReturnOrderDetail.tsx:237
#: src/pages/sales/ReturnOrderDetail.tsx:246
#: src/pages/sales/SalesOrderDetail.tsx:272
#: src/pages/sales/SalesOrderDetail.tsx:281
msgid "Line Items"
msgstr ""

#: src/pages/build/BuildDetail.tsx:267
msgid "Incomplete Outputs"
msgstr ""

#: src/pages/build/BuildDetail.tsx:289
#: src/pages/sales/SalesOrderDetail.tsx:319
msgid "Allocated Stock"
msgstr ""

#: src/pages/build/BuildDetail.tsx:299
msgid "Consumed Stock"
msgstr ""

#: src/pages/build/BuildDetail.tsx:313
msgid "Child Build Orders"
msgstr ""

#: src/pages/build/BuildDetail.tsx:326
#: src/tables/build/BuildOutputTable.tsx:384
#: src/tables/stock/StockItemTestResultTable.tsx:150
msgid "Test Results"
msgstr ""

#: src/pages/build/BuildDetail.tsx:337
#: src/pages/part/PartDetail.tsx:699
msgid "Test Statistics"
msgstr ""

#: src/pages/build/BuildDetail.tsx:351
#: src/pages/company/CompanyDetail.tsx:259
#: src/pages/company/ManufacturerPartDetail.tsx:179
#: src/pages/part/PartDetail.tsx:721
#: src/pages/purchasing/PurchaseOrderDetail.tsx:309
#: src/pages/sales/ReturnOrderDetail.tsx:274
#: src/pages/sales/SalesOrderDetail.tsx:343
#: src/pages/stock/StockDetail.tsx:385
msgid "Attachments"
msgstr ""

#: src/pages/build/BuildDetail.tsx:359
#: src/pages/company/CompanyDetail.tsx:270
#: src/pages/company/ManufacturerPartDetail.tsx:190
#: src/pages/company/SupplierPartDetail.tsx:257
#: src/pages/part/PartDetail.tsx:729
#: src/pages/purchasing/PurchaseOrderDetail.tsx:320
#: src/pages/sales/ReturnOrderDetail.tsx:285
#: src/pages/sales/SalesOrderDetail.tsx:354
#: src/pages/stock/StockDetail.tsx:396
#: src/tables/build/BuildOrderTestTable.tsx:142
#: src/tables/stock/StockTrackingTable.tsx:189
msgid "Notes"
msgstr ""

#: src/pages/build/BuildDetail.tsx:368
#~ msgid "Reporting Actions"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:374
#~ msgid "Print build report"
#~ msgstr ""

#: src/pages/build/BuildDetail.tsx:377
msgid "Edit Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:384
#: src/tables/build/BuildOrderTable.tsx:172
#: src/tables/build/BuildOrderTable.tsx:187
msgid "Add Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:398
msgid "Cancel Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:400
#: src/pages/purchasing/PurchaseOrderDetail.tsx:348
#: src/pages/sales/SalesOrderDetail.tsx:380
msgid "Order cancelled"
msgstr ""

#: src/pages/build/BuildDetail.tsx:401
#: src/pages/purchasing/PurchaseOrderDetail.tsx:347
#: src/pages/sales/ReturnOrderDetail.tsx:346
#: src/pages/sales/SalesOrderDetail.tsx:379
msgid "Cancel this order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:410
msgid "Hold Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:412
#: src/pages/purchasing/PurchaseOrderDetail.tsx:355
#: src/pages/sales/ReturnOrderDetail.tsx:354
#: src/pages/sales/SalesOrderDetail.tsx:387
msgid "Place this order on hold"
msgstr ""

#: src/pages/build/BuildDetail.tsx:413
#: src/pages/purchasing/PurchaseOrderDetail.tsx:356
#: src/pages/sales/ReturnOrderDetail.tsx:355
#: src/pages/sales/SalesOrderDetail.tsx:388
msgid "Order placed on hold"
msgstr ""

#: src/pages/build/BuildDetail.tsx:418
msgid "Issue Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:420
#: src/pages/purchasing/PurchaseOrderDetail.tsx:339
#: src/pages/sales/ReturnOrderDetail.tsx:338
#: src/pages/sales/SalesOrderDetail.tsx:371
msgid "Issue this order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:421
#: src/pages/purchasing/PurchaseOrderDetail.tsx:340
#: src/pages/sales/ReturnOrderDetail.tsx:339
#: src/pages/sales/SalesOrderDetail.tsx:372
msgid "Order issued"
msgstr ""

#: src/pages/build/BuildDetail.tsx:426
msgid "Complete Build Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:428
#: src/pages/purchasing/PurchaseOrderDetail.tsx:368
#: src/pages/sales/ReturnOrderDetail.tsx:362
#: src/pages/sales/SalesOrderDetail.tsx:395
msgid "Mark this order as complete"
msgstr ""

#: src/pages/build/BuildDetail.tsx:429
#: src/pages/purchasing/PurchaseOrderDetail.tsx:362
#: src/pages/sales/ReturnOrderDetail.tsx:363
#: src/pages/sales/SalesOrderDetail.tsx:396
msgid "Order completed"
msgstr ""

#: src/pages/build/BuildDetail.tsx:460
#: src/pages/purchasing/PurchaseOrderDetail.tsx:391
#: src/pages/sales/ReturnOrderDetail.tsx:392
#: src/pages/sales/SalesOrderDetail.tsx:425
msgid "Issue Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:467
#: src/pages/purchasing/PurchaseOrderDetail.tsx:398
#: src/pages/sales/ReturnOrderDetail.tsx:399
#: src/pages/sales/SalesOrderDetail.tsx:439
msgid "Complete Order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:496
msgid "Build Order Actions"
msgstr ""

#: src/pages/build/BuildDetail.tsx:502
#: src/pages/purchasing/PurchaseOrderDetail.tsx:432
#: src/pages/sales/ReturnOrderDetail.tsx:433
#: src/pages/sales/SalesOrderDetail.tsx:474
msgid "Edit order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:506
#: src/pages/purchasing/PurchaseOrderDetail.tsx:440
#: src/pages/sales/ReturnOrderDetail.tsx:439
#: src/pages/sales/SalesOrderDetail.tsx:479
msgid "Duplicate order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:510
#: src/pages/purchasing/PurchaseOrderDetail.tsx:443
#: src/pages/sales/ReturnOrderDetail.tsx:444
#: src/pages/sales/SalesOrderDetail.tsx:482
msgid "Hold order"
msgstr ""

#: src/pages/build/BuildDetail.tsx:515
#: src/pages/purchasing/PurchaseOrderDetail.tsx:448
#: src/pages/sales/ReturnOrderDetail.tsx:449
#: src/pages/sales/SalesOrderDetail.tsx:487
msgid "Cancel order"
msgstr ""

#: src/pages/build/BuildIndex.tsx:23
#~ msgid "Build order created"
#~ msgstr ""

#: src/pages/build/BuildIndex.tsx:39
#~ msgid "New Build Order"
#~ msgstr ""

#: src/pages/company/CompanyDetail.tsx:104
msgid "Phone Number"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:111
msgid "Email Address"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:121
msgid "Default Currency"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:126
#: src/pages/company/SupplierDetail.tsx:8
#: src/pages/company/SupplierPartDetail.tsx:120
#: src/pages/company/SupplierPartDetail.tsx:206
#: src/pages/company/SupplierPartDetail.tsx:351
#: src/pages/purchasing/PurchaseOrderDetail.tsx:136
#: src/tables/company/CompanyTable.tsx:100
#: src/tables/part/PartPurchaseOrdersTable.tsx:42
#: src/tables/purchasing/PurchaseOrderTable.tsx:88
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:36
msgid "Supplier"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:132
#: src/pages/company/ManufacturerDetail.tsx:8
#: src/pages/company/ManufacturerPartDetail.tsx:101
#: src/pages/company/ManufacturerPartDetail.tsx:267
#: src/pages/company/SupplierPartDetail.tsx:135
#: src/tables/company/CompanyTable.tsx:105
msgid "Manufacturer"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:138
#: src/pages/company/CustomerDetail.tsx:8
#: src/pages/part/pricing/SaleHistoryPanel.tsx:29
#: src/pages/sales/ReturnOrderDetail.tsx:109
#: src/pages/sales/SalesOrderDetail.tsx:115
#: src/pages/stock/StockDetail.tsx:230
#: src/tables/company/CompanyTable.tsx:110
#: src/tables/sales/ReturnOrderTable.tsx:78
#: src/tables/sales/SalesOrderTable.tsx:108
#: src/tables/stock/StockTrackingTable.tsx:140
msgid "Customer"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:172
#: src/tables/stock/StockTrackingTable.tsx:183
msgid "Details"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:175
#~ msgid "Edit company"
#~ msgstr ""

#: src/pages/company/CompanyDetail.tsx:178
msgid "Manufactured Parts"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:187
msgid "Supplied Parts"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:189
#~ msgid "Delete company"
#~ msgstr ""

#: src/pages/company/CompanyDetail.tsx:232
msgid "Assigned Stock"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:290
#: src/tables/company/CompanyTable.tsx:86
msgid "Edit Company"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:298
msgid "Delete Company"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:306
msgid "Company Actions"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:76
#: src/pages/company/SupplierPartDetail.tsx:89
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:128
msgid "Internal Part"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:91
#: src/pages/company/SupplierPartDetail.tsx:103
msgid "External Link"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:109
#: src/pages/company/SupplierPartDetail.tsx:144
#: src/tables/purchasing/ManufacturerPartTable.tsx:56
msgid "Manufacturer Part Number"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:138
msgid "Manufacturer Details"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:147
msgid "Manufacturer Part Details"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:153
#: src/pages/part/PartDetail.tsx:532
msgid "Parameters"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:165
#: src/pages/part/PartDetail.tsx:649
#: src/pages/purchasing/PurchasingIndex.tsx:31
msgid "Suppliers"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:208
#: src/tables/purchasing/ManufacturerPartTable.tsx:84
msgid "Edit Manufacturer Part"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:215
#: src/tables/purchasing/ManufacturerPartTable.tsx:72
#: src/tables/purchasing/ManufacturerPartTable.tsx:103
msgid "Add Manufacturer Part"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:227
#: src/tables/purchasing/ManufacturerPartTable.tsx:92
msgid "Delete Manufacturer Part"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:240
msgid "Manufacturer Part Actions"
msgstr ""

#: src/pages/company/ManufacturerPartDetail.tsx:281
msgid "ManufacturerPart"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:163
#: src/tables/part/PartPurchaseOrdersTable.tsx:71
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:156
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:197
#: src/tables/purchasing/SupplierPartTable.tsx:131
msgid "Pack Quantity"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:174
msgid "Supplier Availability"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:181
msgid "Availability Updated"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:208
msgid "Availability"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:217
msgid "Supplier Part Details"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:223
#: src/pages/purchasing/PurchaseOrderDetail.tsx:296
msgid "Received Stock"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:247
#: src/pages/part/PartPricingPanel.tsx:111
#: src/pages/part/pricing/PricingOverviewPanel.tsx:121
msgid "Supplier Pricing"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:294
msgid "Supplier Part Actions"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:319
#: src/tables/purchasing/SupplierPartTable.tsx:210
msgid "Edit Supplier Part"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:327
#: src/tables/purchasing/SupplierPartTable.tsx:218
msgid "Delete Supplier Part"
msgstr ""

#: src/pages/company/SupplierPartDetail.tsx:335
#: src/tables/purchasing/SupplierPartTable.tsx:163
msgid "Add Supplier Part"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:93
#: src/pages/stock/LocationDetail.tsx:101
#: src/tables/settings/ErrorTable.tsx:36
msgid "Path"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:109
msgid "Parent Category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:126
msgid "Subcategories"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:133
#: src/pages/stock/LocationDetail.tsx:141
#: src/tables/part/PartCategoryTable.tsx:73
#: src/tables/stock/StockLocationTable.tsx:49
msgid "Structural"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:139
msgid "Parent default location"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:146
msgid "Default location"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:157
msgid "Top level part category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:167
#: src/pages/part/CategoryDetail.tsx:221
#: src/tables/part/PartCategoryTable.tsx:102
msgid "Edit Part Category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:180
#: src/pages/stock/LocationDetail.tsx:233
msgid "Delete items"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:188
#: src/pages/part/CategoryDetail.tsx:226
msgid "Delete Part Category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:191
msgid "Parts Action"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:192
msgid "Action for parts in this category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:197
msgid "Child Categories Action"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:198
msgid "Action for child categories in this category"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:216
msgid "Category Actions"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:238
msgid "Category Details"
msgstr ""

#: src/pages/part/PartDetail.tsx:163
msgid "Variant of"
msgstr ""

#: src/pages/part/PartDetail.tsx:170
msgid "Revision of"
msgstr ""

#: src/pages/part/PartDetail.tsx:177
#: src/tables/stock/StockItemTable.tsx:58
msgid "Revision"
msgstr ""

#: src/pages/part/PartDetail.tsx:184
#: src/tables/notifications/NotificationsTable.tsx:31
#: src/tables/part/PartCategoryTemplateTable.tsx:67
msgid "Category"
msgstr ""

#: src/pages/part/PartDetail.tsx:190
msgid "Default Location"
msgstr ""

#: src/pages/part/PartDetail.tsx:197
msgid "Category Default Location"
msgstr ""

#: src/pages/part/PartDetail.tsx:204
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:44
msgid "Units"
msgstr ""

#: src/pages/part/PartDetail.tsx:211
#: src/tables/settings/PendingTasksTable.tsx:42
msgid "Keywords"
msgstr ""

#: src/pages/part/PartDetail.tsx:218
#: src/pages/purchasing/PurchaseOrderDetail.tsx:186
#: src/pages/sales/ReturnOrderDetail.tsx:165
#: src/pages/sales/SalesOrderDetail.tsx:174
msgid "Link"
msgstr ""

#: src/pages/part/PartDetail.tsx:236
#: src/tables/bom/BomTable.tsx:320
#: src/tables/build/BuildLineTable.tsx:155
#: src/tables/part/PartTable.tsx:288
#: src/tables/sales/SalesOrderLineItemTable.tsx:103
msgid "Available Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:243
msgid "Variant Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:251
msgid "Minimum Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:257
#: src/tables/bom/BomTable.tsx:237
#: src/tables/build/BuildLineTable.tsx:117
#: src/tables/sales/SalesOrderLineItemTable.tsx:141
msgid "On order"
msgstr ""

#: src/pages/part/PartDetail.tsx:266
msgid "Allocated to Build Orders"
msgstr ""

#: src/pages/part/PartDetail.tsx:274
msgid "Allocated to Sales Orders"
msgstr ""

#: src/pages/part/PartDetail.tsx:281
#: src/tables/bom/BomTable.tsx:261
#: src/tables/bom/BomTable.tsx:293
msgid "Can Build"
msgstr ""

#: src/pages/part/PartDetail.tsx:288
#: src/tables/bom/BomTable.tsx:245
#: src/tables/part/PartTable.tsx:92
msgid "Building"
msgstr ""

#: src/pages/part/PartDetail.tsx:302
#: src/pages/part/PartDetail.tsx:873
#: src/tables/part/ParametricPartTable.tsx:228
#: src/tables/part/PartTable.tsx:184
msgid "Locked"
msgstr ""

#: src/pages/part/PartDetail.tsx:308
msgid "Template Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:310
#~ msgid "Edit part"
#~ msgstr ""

#: src/pages/part/PartDetail.tsx:313
#: src/tables/bom/BomTable.tsx:315
msgid "Assembled Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:318
msgid "Component Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:322
#~ msgid "Duplicate part"
#~ msgstr ""

#: src/pages/part/PartDetail.tsx:323
#: src/tables/bom/BomTable.tsx:305
msgid "Testable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:327
#~ msgid "Delete part"
#~ msgstr ""

#: src/pages/part/PartDetail.tsx:329
#: src/tables/bom/BomTable.tsx:310
msgid "Trackable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:334
msgid "Purchaseable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:339
msgid "Saleable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:344
msgid "Virtual Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:352
#: src/tables/ColumnRenderers.tsx:220
msgid "Creation Date"
msgstr ""

#: src/pages/part/PartDetail.tsx:357
msgid "Created By"
msgstr ""

#: src/pages/part/PartDetail.tsx:372
msgid "Default Supplier"
msgstr ""

#: src/pages/part/PartDetail.tsx:383
#: src/pages/part/pricing/BomPricingPanel.tsx:79
#: src/pages/part/pricing/VariantPricingPanel.tsx:95
#: src/tables/part/PartTable.tsx:161
msgid "Price Range"
msgstr ""

#: src/pages/part/PartDetail.tsx:423
#: src/pages/stock/StockDetail.tsx:135
msgid "Last Stocktake"
msgstr ""

#: src/pages/part/PartDetail.tsx:462
msgid "Stocktake By"
msgstr ""

#: src/pages/part/PartDetail.tsx:526
msgid "Part Details"
msgstr ""

#: src/pages/part/PartDetail.tsx:557
msgid "Variants"
msgstr ""

#: src/pages/part/PartDetail.tsx:564
#: src/pages/stock/StockDetail.tsx:307
msgid "Allocations"
msgstr ""

#: src/pages/part/PartDetail.tsx:575
#: src/pages/stock/StockDetail.tsx:318
#: src/tables/part/PartTable.tsx:99
msgid "Build Order Allocations"
msgstr ""

#: src/pages/part/PartDetail.tsx:590
#: src/pages/stock/StockDetail.tsx:333
#: src/tables/part/PartTable.tsx:108
msgid "Sales Order Allocations"
msgstr ""

#: src/pages/part/PartDetail.tsx:607
msgid "Bill of Materials"
msgstr ""

#: src/pages/part/PartDetail.tsx:623
msgid "Used In"
msgstr ""

#: src/pages/part/PartDetail.tsx:630
msgid "Part Pricing"
msgstr ""

#: src/pages/part/PartDetail.tsx:636
#: src/pages/purchasing/PurchasingIndex.tsx:42
msgid "Manufacturers"
msgstr ""

#: src/pages/part/PartDetail.tsx:676
msgid "Scheduling"
msgstr ""

#: src/pages/part/PartDetail.tsx:688
msgid "Test Templates"
msgstr ""

#: src/pages/part/PartDetail.tsx:715
msgid "Related Parts"
msgstr ""

#: src/pages/part/PartDetail.tsx:849
#: src/pages/stock/StockDetail.tsx:156
#: src/pages/stock/StockDetail.tsx:593
#: src/tables/build/BuildLineTable.tsx:48
#: src/tables/part/PartTable.tsx:117
#: src/tables/stock/StockItemTable.tsx:177
#: src/tables/stock/StockItemTable.tsx:310
msgid "Available"
msgstr ""

#: src/pages/part/PartDetail.tsx:855
msgid "No Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:861
#: src/tables/bom/BomTable.tsx:325
#: src/tables/part/PartTable.tsx:86
msgid "On Order"
msgstr ""

#: src/pages/part/PartDetail.tsx:867
#: src/pages/stock/StockDetail.tsx:576
#: src/tables/build/BuildOrderTestTable.tsx:219
#: src/tables/stock/StockItemTable.tsx:330
msgid "In Production"
msgstr ""

#: src/pages/part/PartDetail.tsx:892
msgid "Edit Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:927
#: src/tables/part/PartTable.tsx:331
#: src/tables/part/PartTable.tsx:342
msgid "Add Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:941
msgid "Delete Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:950
msgid "Deleting this part cannot be reversed"
msgstr ""

#: src/pages/part/PartDetail.tsx:999
#: src/pages/stock/LocationDetail.tsx:324
#: src/tables/stock/StockItemTable.tsx:444
msgid "Stock Actions"
msgstr ""

#: src/pages/part/PartDetail.tsx:1007
msgid "Count part stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:1018
msgid "Transfer part stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:1027
msgid "Part Actions"
msgstr ""

#: src/pages/part/PartDetail.tsx:1087
msgid "Select Part Revision"
msgstr ""

#: src/pages/part/PartIndex.tsx:29
#~ msgid "Categories"
#~ msgstr ""

#: src/pages/part/PartPricingPanel.tsx:68
msgid "No pricing data found for this part."
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:82
#: src/pages/part/pricing/PricingOverviewPanel.tsx:190
msgid "Pricing Overview"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:88
msgid "Purchase History"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:102
#: src/pages/part/pricing/PricingOverviewPanel.tsx:100
msgid "Internal Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:120
#: src/pages/part/pricing/PricingOverviewPanel.tsx:107
msgid "BOM Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:127
#: src/pages/part/pricing/PricingOverviewPanel.tsx:128
msgid "Variant Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:139
#: src/pages/part/pricing/PricingOverviewPanel.tsx:135
msgid "Sale Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:146
#: src/pages/part/pricing/PricingOverviewPanel.tsx:142
msgid "Sale History"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:58
#: src/pages/part/pricing/BomPricingPanel.tsx:138
#: src/tables/ColumnRenderers.tsx:262
#: src/tables/bom/BomTable.tsx:185
#: src/tables/general/ExtraLineItemTable.tsx:64
#: src/tables/purchasing/PurchaseOrderTable.tsx:112
#: src/tables/sales/ReturnOrderTable.tsx:104
#: src/tables/sales/SalesOrderLineItemTable.tsx:89
#: src/tables/sales/SalesOrderTable.tsx:136
msgid "Total Price"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:78
#: src/pages/part/pricing/BomPricingPanel.tsx:102
#: src/tables/bom/UsedInTable.tsx:49
#: src/tables/part/PartTable.tsx:202
msgid "Component"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:81
#: src/pages/part/pricing/VariantPricingPanel.tsx:37
#: src/pages/part/pricing/VariantPricingPanel.tsx:97
msgid "Minimum Price"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:82
#: src/pages/part/pricing/VariantPricingPanel.tsx:45
#: src/pages/part/pricing/VariantPricingPanel.tsx:98
msgid "Maximum Price"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#~ msgid "Minimum Total Price"
#~ msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:117
#~ msgid "Maximum Total Price"
#~ msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:129
#: src/pages/part/pricing/PriceBreakPanel.tsx:172
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:67
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:128
#: src/pages/part/pricing/SupplierPricingPanel.tsx:62
#: src/tables/bom/BomTable.tsx:176
#: src/tables/general/ExtraLineItemTable.tsx:56
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:223
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:92
msgid "Unit Price"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:154
#: src/pages/part/pricing/VariantPricingPanel.tsx:53
#: src/tables/purchasing/SupplierPartTable.tsx:148
msgid "Updated"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:219
msgid "Pie Chart"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:220
msgid "Bar Chart"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:58
#: src/pages/part/pricing/PriceBreakPanel.tsx:110
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:142
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:168
msgid "Add Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:71
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:153
msgid "Edit Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:81
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:161
msgid "Delete Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:95
msgid "Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:170
msgid "Price"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:51
msgid "Pricing Category"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:70
msgid "Minimum"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:82
msgid "Maximum"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:114
msgid "Purchase Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:149
msgid "Override Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:156
msgid "Overall Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:175
#: src/pages/stock/StockDetail.tsx:129
#: src/tables/stock/StockItemTable.tsx:240
msgid "Last Updated"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:192
msgid "Minimum Value"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:193
msgid "Maximum Value"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:24
msgid "No data available"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:65
msgid "No Data"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:66
msgid "No pricing data available"
msgstr ""

#: src/pages/part/pricing/PricingPanel.tsx:77
msgid "Loading pricing data"
msgstr ""

#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:40
#: src/pages/part/pricing/SaleHistoryPanel.tsx:36
#: src/tables/ColumnRenderers.tsx:201
#: src/tables/build/BuildOrderTestTable.tsx:150
#: src/tables/plugin/PluginListTable.tsx:139
msgid "Date"
msgstr ""

#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:47
msgid "Purchase Price"
msgstr ""

#: src/pages/part/pricing/SaleHistoryPanel.tsx:22
msgid "Sale Order"
msgstr ""

#: src/pages/part/pricing/SaleHistoryPanel.tsx:42
#: src/pages/part/pricing/SaleHistoryPanel.tsx:92
msgid "Sale Price"
msgstr ""

#: src/pages/part/pricing/SupplierPricingPanel.tsx:65
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:83
msgid "Supplier Price"
msgstr ""

#: src/pages/part/pricing/VariantPricingPanel.tsx:30
#: src/pages/part/pricing/VariantPricingPanel.tsx:94
msgid "Variant Part"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:93
msgid "Edit Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:102
#: src/tables/purchasing/PurchaseOrderTable.tsx:128
#: src/tables/purchasing/PurchaseOrderTable.tsx:140
msgid "Add Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:127
msgid "Supplier Reference"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:158
#: src/pages/sales/ReturnOrderDetail.tsx:137
#: src/pages/sales/SalesOrderDetail.tsx:137
msgid "Completed Line Items"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:159
#: src/pages/sales/ReturnOrderDetail.tsx:126
#: src/pages/sales/SalesOrderDetail.tsx:130
#~ msgid "Order Currency,"
#~ msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:165
#: src/pages/sales/ReturnOrderDetail.tsx:144
#: src/pages/sales/SalesOrderDetail.tsx:153
msgid "Order Currency"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:172
#: src/pages/sales/ReturnOrderDetail.tsx:151
#: src/pages/sales/SalesOrderDetail.tsx:160
msgid "Total Cost"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:207
#: src/pages/sales/ReturnOrderDetail.tsx:186
#: src/pages/sales/SalesOrderDetail.tsx:195
msgid "Created On"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:252
#: src/pages/sales/ReturnOrderDetail.tsx:231
#: src/pages/sales/SalesOrderDetail.tsx:266
msgid "Order Details"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:280
#: src/pages/sales/ReturnOrderDetail.tsx:258
#: src/pages/sales/SalesOrderDetail.tsx:297
msgid "Extra Line Items"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:337
msgid "Issue Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:345
msgid "Cancel Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:353
msgid "Hold Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:361
msgid "Complete Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:427
#: src/pages/sales/ReturnOrderDetail.tsx:428
#: src/pages/sales/SalesOrderDetail.tsx:468
msgid "Order Actions"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:101
#: src/pages/sales/SalesOrderDetail.tsx:107
#: src/tables/sales/SalesOrderTable.tsx:124
msgid "Customer Reference"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:315
msgid "Edit Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:324
#: src/tables/sales/ReturnOrderTable.tsx:119
#: src/tables/sales/ReturnOrderTable.tsx:128
msgid "Add Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:336
msgid "Issue Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:344
msgid "Cancel Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:347
msgid "Order canceled"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:352
msgid "Hold Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:360
msgid "Complete Return Order"
msgstr ""

#: src/pages/sales/SalesIndex.tsx:38
msgid "Customers"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:145
msgid "Completed Shipments"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:243
msgid "Edit Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:252
#: src/tables/sales/SalesOrderTable.tsx:84
#: src/tables/sales/SalesOrderTable.tsx:96
msgid "Add Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:256
#~ msgid "Pending Shipments"
#~ msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:313
msgid "Shipments"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:369
msgid "Issue Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:377
msgid "Cancel Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:385
msgid "Hold Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:393
msgid "Complete Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:432
msgid "Ship Order"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:117
msgid "Parent Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:135
msgid "Sublocations"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:147
#: src/tables/stock/StockLocationTable.tsx:54
msgid "External"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:153
#: src/tables/stock/StockLocationTable.tsx:63
msgid "Location Type"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:164
msgid "Top level stock location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:175
msgid "Location Details"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:201
msgid "Default Parts"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:220
#: src/pages/stock/LocationDetail.tsx:351
#: src/tables/stock/StockLocationTable.tsx:123
msgid "Edit Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:241
#: src/pages/stock/LocationDetail.tsx:356
msgid "Delete Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:244
msgid "Items Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:245
msgid "Action for stock items in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:250
msgid "Child Locations Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:251
msgid "Action for child locations in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:346
msgid "Location Actions"
msgstr ""

#: src/pages/stock/StockDetail.tsx:108
msgid "Base Part"
msgstr ""

#: src/pages/stock/StockDetail.tsx:115
msgid "Stock Status"
msgstr ""

#: src/pages/stock/StockDetail.tsx:155
#~ msgid "Link custom barcode to stock item"
#~ msgstr ""

#: src/pages/stock/StockDetail.tsx:161
#~ msgid "Unlink custom barcode from stock item"
#~ msgstr ""

#: src/pages/stock/StockDetail.tsx:188
msgid "Installed In"
msgstr ""

#: src/pages/stock/StockDetail.tsx:204
msgid "Consumed By"
msgstr ""

#: src/pages/stock/StockDetail.tsx:205
#~ msgid "Edit stock item"
#~ msgstr ""

#: src/pages/stock/StockDetail.tsx:213
#: src/tables/build/BuildAllocatedStockTable.tsx:64
#: src/tables/stock/StockTrackingTable.tsx:96
msgid "Build Order"
msgstr ""

#: src/pages/stock/StockDetail.tsx:217
#~ msgid "Delete stock item"
#~ msgstr ""

#: src/pages/stock/StockDetail.tsx:291
msgid "Stock Details"
msgstr ""

#: src/pages/stock/StockDetail.tsx:297
msgid "Stock Tracking"
msgstr ""

#: src/pages/stock/StockDetail.tsx:350
msgid "Test Data"
msgstr ""

#: src/pages/stock/StockDetail.tsx:364
msgid "Installed Items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:371
msgid "Child Items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:425
msgid "Edit Stock Item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:433
#~ msgid "Duplicate stock item"
#~ msgstr ""

#: src/pages/stock/StockDetail.tsx:452
msgid "Delete Stock Item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:504
msgid "Stock Operations"
msgstr ""

#: src/pages/stock/StockDetail.tsx:509
msgid "Count stock"
msgstr ""

#: src/pages/stock/StockDetail.tsx:519
#: src/tables/stock/StockItemTable.tsx:449
msgid "Add stock"
msgstr ""

#: src/pages/stock/StockDetail.tsx:527
#: src/tables/stock/StockItemTable.tsx:458
msgid "Remove stock"
msgstr ""

#: src/pages/stock/StockDetail.tsx:534
msgid "Transfer"
msgstr ""

#: src/pages/stock/StockDetail.tsx:535
#: src/tables/stock/StockItemTable.tsx:478
msgid "Transfer stock"
msgstr ""

#: src/pages/stock/StockDetail.tsx:546
msgid "Stock Item Actions"
msgstr ""

#: src/tables/ColumnRenderers.tsx:30
msgid "Part is not active"
msgstr ""

#: src/tables/ColumnRenderers.tsx:35
msgid "Part is locked"
msgstr ""

#: src/tables/ColumnRenderers.tsx:57
msgid "No location set"
msgstr ""

#: src/tables/ColumnRenderers.tsx:228
#: src/tables/sales/SalesOrderShipmentTable.tsx:79
msgid "Shipment Date"
msgstr ""

#: src/tables/ColumnRenderers.tsx:248
#: src/tables/settings/CurrencyTable.tsx:23
msgid "Currency"
msgstr ""

#: src/tables/ColumnSelect.tsx:16
#: src/tables/ColumnSelect.tsx:23
msgid "Select Columns"
msgstr ""

#: src/tables/DownloadAction.tsx:13
#~ msgid "Excel"
#~ msgstr ""

#: src/tables/DownloadAction.tsx:21
msgid "CSV"
msgstr ""

#: src/tables/DownloadAction.tsx:21
#~ msgid "Download selected data"
#~ msgstr ""

#: src/tables/DownloadAction.tsx:22
msgid "TSV"
msgstr ""

#: src/tables/DownloadAction.tsx:23
msgid "Excel (.xlsx)"
msgstr ""

#: src/tables/DownloadAction.tsx:24
#~ msgid "Excel (.xls)"
#~ msgstr ""

#: src/tables/DownloadAction.tsx:36
msgid "Download Data"
msgstr ""

#: src/tables/Filter.tsx:89
#: src/tables/build/BuildOrderTable.tsx:135
msgid "Assigned to me"
msgstr ""

#: src/tables/Filter.tsx:90
#: src/tables/build/BuildOrderTable.tsx:136
msgid "Show orders assigned to me"
msgstr ""

#: src/tables/Filter.tsx:97
msgid "Outstanding"
msgstr ""

#: src/tables/Filter.tsx:98
msgid "Show outstanding orders"
msgstr ""

#: src/tables/Filter.tsx:105
#: src/tables/build/BuildOrderTable.tsx:128
msgid "Overdue"
msgstr ""

#: src/tables/Filter.tsx:106
msgid "Show overdue orders"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:51
msgid "Remove filter"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:130
msgid "Select filter"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:131
msgid "Filter"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:138
#: src/tables/build/BuildOrderTestTable.tsx:134
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:38
#: src/tables/stock/StockItemTestResultTable.tsx:186
msgid "Value"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:140
msgid "Select filter value"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:183
msgid "Table Filters"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:215
msgid "Add Filter"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:224
msgid "Clear Filters"
msgstr ""

#: src/tables/InvenTreeTable.tsx:123
#: src/tables/InvenTreeTable.tsx:431
#: src/tables/InvenTreeTable.tsx:455
msgid "No records found"
msgstr ""

#: src/tables/InvenTreeTable.tsx:466
msgid "Server returned incorrect data type"
msgstr ""

#: src/tables/InvenTreeTable.tsx:474
msgid "Bad request"
msgstr ""

#: src/tables/InvenTreeTable.tsx:477
msgid "Unauthorized"
msgstr ""

#: src/tables/InvenTreeTable.tsx:480
msgid "Forbidden"
msgstr ""

#: src/tables/InvenTreeTable.tsx:483
msgid "Not found"
msgstr ""

#: src/tables/InvenTreeTable.tsx:510
#~ msgid "Are you sure you want to delete the selected records?"
#~ msgstr ""

#: src/tables/InvenTreeTable.tsx:525
msgid "Delete Selected Items"
msgstr ""

#: src/tables/InvenTreeTable.tsx:529
msgid "Are you sure you want to delete the selected items?"
msgstr ""

#: src/tables/InvenTreeTable.tsx:531
msgid "This action cannot be undone!"
msgstr ""

#: src/tables/InvenTreeTable.tsx:535
#~ msgid "Deleted records"
#~ msgstr ""

#: src/tables/InvenTreeTable.tsx:536
#~ msgid "Records were deleted successfully"
#~ msgstr ""

#: src/tables/InvenTreeTable.tsx:545
#~ msgid "Failed to delete records"
#~ msgstr ""

#: src/tables/InvenTreeTable.tsx:594
#: src/tables/InvenTreeTable.tsx:595
#~ msgid "Print actions"
#~ msgstr ""

#: src/tables/InvenTreeTable.tsx:618
#: src/tables/InvenTreeTable.tsx:619
msgid "Barcode actions"
msgstr ""

#: src/tables/InvenTreeTable.tsx:628
msgid "Delete selected records"
msgstr ""

#: src/tables/InvenTreeTable.tsx:649
msgid "Refresh data"
msgstr ""

#: src/tables/InvenTreeTable.tsx:675
msgid "Table filters"
msgstr ""

#: src/tables/TableHoverCard.tsx:35
msgid "item-{idx}"
msgstr ""

#: src/tables/UploadAction.tsx:7
#~ msgid "Upload Data"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:95
msgid "This BOM item is defined for a different parent"
msgstr ""

#: src/tables/bom/BomTable.tsx:110
msgid "Part Information"
msgstr ""

#: src/tables/bom/BomTable.tsx:212
#: src/tables/build/BuildLineTable.tsx:126
#: src/tables/part/PartTable.tsx:125
msgid "External stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:220
#: src/tables/build/BuildLineTable.tsx:89
msgid "Includes substitute stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:229
#: src/tables/build/BuildLineTable.tsx:99
#: src/tables/sales/SalesOrderLineItemTable.tsx:127
msgid "Includes variant stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:254
#: src/tables/part/PartTable.tsx:153
#: src/tables/sales/SalesOrderLineItemTable.tsx:150
#: src/tables/stock/StockItemTable.tsx:216
msgid "Stock Information"
msgstr ""

#: src/tables/bom/BomTable.tsx:285
#: src/tables/build/BuildLineTable.tsx:248
msgid "Consumable item"
msgstr ""

#: src/tables/bom/BomTable.tsx:288
msgid "No available stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:301
#~ msgid "Create BOM Item"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:306
#: src/tables/build/BuildLineTable.tsx:69
msgid "Show testable items"
msgstr ""

#: src/tables/bom/BomTable.tsx:310
#~ msgid "Show asssmbled items"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:311
msgid "Show trackable items"
msgstr ""

#: src/tables/bom/BomTable.tsx:316
#: src/tables/build/BuildLineTable.tsx:64
msgid "Show assembled items"
msgstr ""

#: src/tables/bom/BomTable.tsx:321
msgid "Show items with available stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:326
msgid "Show items on order"
msgstr ""

#: src/tables/bom/BomTable.tsx:330
msgid "Validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:331
msgid "Show validated items"
msgstr ""

#: src/tables/bom/BomTable.tsx:331
#~ msgid "Edit Bom Item"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:333
#~ msgid "Bom item updated"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:335
#: src/tables/bom/UsedInTable.tsx:74
msgid "Inherited"
msgstr ""

#: src/tables/bom/BomTable.tsx:336
#: src/tables/bom/UsedInTable.tsx:75
msgid "Show inherited items"
msgstr ""

#: src/tables/bom/BomTable.tsx:340
msgid "Allow Variants"
msgstr ""

#: src/tables/bom/BomTable.tsx:341
msgid "Show items which allow variant substitution"
msgstr ""

#: src/tables/bom/BomTable.tsx:345
#: src/tables/bom/UsedInTable.tsx:79
#: src/tables/build/BuildLineTable.tsx:58
msgid "Optional"
msgstr ""

#: src/tables/bom/BomTable.tsx:346
#: src/tables/bom/UsedInTable.tsx:80
msgid "Show optional items"
msgstr ""

#: src/tables/bom/BomTable.tsx:348
#~ msgid "Delete Bom Item"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:349
#~ msgid "Bom item deleted"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:350
#: src/tables/build/BuildLineTable.tsx:53
msgid "Consumable"
msgstr ""

#: src/tables/bom/BomTable.tsx:351
msgid "Show consumable items"
msgstr ""

#: src/tables/bom/BomTable.tsx:351
#~ msgid "Are you sure you want to remove this BOM item?"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:354
#~ msgid "Validate BOM line"
#~ msgstr ""

#: src/tables/bom/BomTable.tsx:355
#: src/tables/part/PartTable.tsx:282
msgid "Has Pricing"
msgstr ""

#: src/tables/bom/BomTable.tsx:356
msgid "Show items with pricing"
msgstr ""

#: src/tables/bom/BomTable.tsx:378
#: src/tables/bom/BomTable.tsx:511
msgid "Import BOM Data"
msgstr ""

#: src/tables/bom/BomTable.tsx:388
#: src/tables/bom/BomTable.tsx:523
msgid "Add BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:393
msgid "BOM item created"
msgstr ""

#: src/tables/bom/BomTable.tsx:400
msgid "Edit BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:402
msgid "BOM item updated"
msgstr ""

#: src/tables/bom/BomTable.tsx:409
msgid "Delete BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:410
msgid "BOM item deleted"
msgstr ""

#: src/tables/bom/BomTable.tsx:423
#: src/tables/bom/BomTable.tsx:426
#: src/tables/bom/BomTable.tsx:517
msgid "Validate BOM"
msgstr ""

#: src/tables/bom/BomTable.tsx:427
msgid "Do you want to validate the bill of materials for this assembly?"
msgstr ""

#: src/tables/bom/BomTable.tsx:430
msgid "BOM validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:442
msgid "BOM item validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:451
msgid "Failed to validate BOM item"
msgstr ""

#: src/tables/bom/BomTable.tsx:463
msgid "View BOM"
msgstr ""

#: src/tables/bom/BomTable.tsx:472
msgid "Validate BOM Line"
msgstr ""

#: src/tables/bom/BomTable.tsx:489
msgid "Edit Substitutes"
msgstr ""

#: src/tables/bom/BomTable.tsx:539
#: src/tables/part/PartParameterTable.tsx:193
#: src/tables/part/PartTestTemplateTable.tsx:252
msgid "Part is Locked"
msgstr ""

#: src/tables/bom/BomTable.tsx:544
msgid "Bill of materials cannot be edited, as the part is locked"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:33
#: src/tables/build/BuildLineTable.tsx:63
#: src/tables/part/ParametricPartTable.tsx:233
#: src/tables/part/PartTable.tsx:190
#: src/tables/stock/StockItemTable.tsx:300
msgid "Assembly"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:85
msgid "Show active assemblies"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:89
#: src/tables/part/PartTable.tsx:214
#: src/tables/part/PartVariantTable.tsx:30
msgid "Trackable"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:90
msgid "Show trackable assemblies"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:54
msgid "Allocated to Output"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:55
msgid "Show items allocated to a build output"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:76
#: src/tables/part/PartPurchaseOrdersTable.tsx:125
#: src/tables/sales/SalesOrderAllocationTable.tsx:62
msgid "Order Status"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:96
#: src/tables/sales/SalesOrderAllocationTable.tsx:75
msgid "Allocated Quantity"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:116
#: src/tables/sales/SalesOrderAllocationTable.tsx:94
msgid "Available Quantity"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:126
#: src/tables/build/BuildOrderTestTable.tsx:176
#: src/tables/build/BuildOrderTestTable.tsx:200
#: src/tables/build/BuildOutputTable.tsx:312
msgid "Build Output"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:143
msgid "Edit Build Item"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:153
msgid "Delete Build Item"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:43
#: src/tables/stock/StockItemTable.tsx:305
msgid "Allocated"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:44
msgid "Show allocated lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:49
msgid "Show lines with available stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:54
msgid "Show consumable lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:59
msgid "Show optional lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:68
#: src/tables/part/PartTable.tsx:208
msgid "Testable"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:73
#: src/tables/stock/StockItemTable.tsx:364
msgid "Tracked"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:74
msgid "Show tracked lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:108
#: src/tables/sales/SalesOrderLineItemTable.tsx:133
msgid "In production"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:136
msgid "Insufficient stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:152
#: src/tables/sales/SalesOrderLineItemTable.tsx:121
#: src/tables/stock/StockItemTable.tsx:186
msgid "No stock available"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:201
msgid "Gets Inherited"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:210
msgid "Unit Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:267
#: src/tables/sales/SalesOrderLineItemTable.tsx:230
msgid "Create Build Order"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:298
msgid "Allocate Stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:305
msgid "Order Stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:312
msgid "Build Stock"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:111
msgid "Show active orders"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:116
msgid "Cascade"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:117
msgid "Display recursive child orders"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:123
#: src/tables/part/PartPurchaseOrdersTable.tsx:126
#: src/tables/purchasing/PurchaseOrderTable.tsx:56
#: src/tables/sales/ReturnOrderTable.tsx:47
#: src/tables/sales/SalesOrderTable.tsx:54
msgid "Filter by order status"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:130
msgid "Show overdue status"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:141
#: src/tables/purchasing/PurchaseOrderTable.tsx:65
#: src/tables/sales/ReturnOrderTable.tsx:56
#: src/tables/sales/SalesOrderTable.tsx:63
msgid "Filter by project code"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:146
#: src/tables/purchasing/PurchaseOrderTable.tsx:70
#: src/tables/sales/ReturnOrderTable.tsx:61
#: src/tables/sales/SalesOrderTable.tsx:68
msgid "Has Project Code"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:147
#: src/tables/purchasing/PurchaseOrderTable.tsx:71
#: src/tables/sales/ReturnOrderTable.tsx:62
#: src/tables/sales/SalesOrderTable.tsx:69
msgid "Filter by whether the purchase order has a project code"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:152
msgid "Filter by user who issued this order"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:158
#: src/tables/purchasing/PurchaseOrderTable.tsx:76
#: src/tables/sales/ReturnOrderTable.tsx:67
#: src/tables/sales/SalesOrderTable.tsx:74
msgid "Filter by responsible owner"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:75
#: src/tables/build/BuildOrderTestTable.tsx:111
#: src/tables/stock/StockItemTestResultTable.tsx:257
#: src/tables/stock/StockItemTestResultTable.tsx:329
#: src/tables/stock/StockItemTestResultTable.tsx:384
msgid "Add Test Result"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:82
#: src/tables/stock/StockItemTestResultTable.tsx:259
msgid "Test result added"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:110
#: src/tables/stock/StockItemTestResultTable.tsx:174
msgid "No Result"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:220
msgid "Show build outputs currently in production"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:161
#~ msgid "Delete build output"
#~ msgstr ""

#: src/tables/build/BuildOutputTable.tsx:173
#: src/tables/build/BuildOutputTable.tsx:211
msgid "Add Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:216
msgid "Complete selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:226
msgid "Scrap selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:236
msgid "Cancel selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:252
msgid "Allocate"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:253
msgid "Allocate stock to build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:259
msgid "Deallocate"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:260
msgid "Deallocate stock from build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:267
msgid "Complete build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:276
msgid "Scrap"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:277
msgid "Scrap build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:280
#~ msgid "Allocated Items"
#~ msgstr ""

#: src/tables/build/BuildOutputTable.tsx:287
msgid "Cancel build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:325
msgid "Batch"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:340
msgid "Allocated Lines"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:355
msgid "Required Tests"
msgstr ""

#: src/tables/company/AddressTable.tsx:121
#: src/tables/company/AddressTable.tsx:185
msgid "Add Address"
msgstr ""

#: src/tables/company/AddressTable.tsx:126
msgid "Address created"
msgstr ""

#: src/tables/company/AddressTable.tsx:135
msgid "Edit Address"
msgstr ""

#: src/tables/company/AddressTable.tsx:143
msgid "Delete Address"
msgstr ""

#: src/tables/company/AddressTable.tsx:144
msgid "Are you sure you want to delete this address?"
msgstr ""

#: src/tables/company/CompanyTable.tsx:71
#~ msgid "New Company"
#~ msgstr ""

#: src/tables/company/CompanyTable.tsx:74
#: src/tables/company/CompanyTable.tsx:123
msgid "Add Company"
msgstr ""

#: src/tables/company/CompanyTable.tsx:96
msgid "Show active companies"
msgstr ""

#: src/tables/company/CompanyTable.tsx:101
msgid "Show companies which are suppliers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:106
msgid "Show companies which are manufacturers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:111
msgid "Show companies which are customers"
msgstr ""

#: src/tables/company/ContactTable.tsx:71
msgid "Edit Contact"
msgstr ""

#: src/tables/company/ContactTable.tsx:78
msgid "Add Contact"
msgstr ""

#: src/tables/company/ContactTable.tsx:89
msgid "Delete Contact"
msgstr ""

#: src/tables/company/ContactTable.tsx:129
msgid "Add contact"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:130
msgid "File uploaded"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:131
msgid "File {0} uploaded successfully"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:142
msgid "Upload Error"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:143
msgid "File could not be uploaded"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:191
msgid "Upload Attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:201
msgid "Edit Attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:215
msgid "Delete Attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:225
msgid "Is Link"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:226
msgid "Show link attachments"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:230
msgid "Is File"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:231
msgid "Show file attachments"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:240
msgid "Add attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:251
msgid "Add external link"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:254
#~ msgid "Upload attachment"
#~ msgstr ""

#: src/tables/general/AttachmentTable.tsx:299
msgid "No attachments found"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:337
msgid "Drag attachment file here to upload"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:86
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:251
#: src/tables/sales/ReturnOrderLineItemTable.tsx:60
#: src/tables/sales/SalesOrderLineItemTable.tsx:196
msgid "Add Line Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:98
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:271
#: src/tables/sales/ReturnOrderLineItemTable.tsx:72
#: src/tables/sales/SalesOrderLineItemTable.tsx:214
msgid "Edit Line Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:106
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:279
#: src/tables/sales/ReturnOrderLineItemTable.tsx:80
#: src/tables/sales/SalesOrderLineItemTable.tsx:222
msgid "Delete Line Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:142
msgid "Add Extra Line Item"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:202
msgid "Machine restarted"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:212
#: src/tables/machine/MachineListTable.tsx:263
msgid "Edit machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:226
#: src/tables/machine/MachineListTable.tsx:267
msgid "Delete machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:227
msgid "Machine successfully deleted."
msgstr ""

#: src/tables/machine/MachineListTable.tsx:231
msgid "Are you sure you want to remove the machine \"{0}\"?"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:255
#: src/tables/machine/MachineListTable.tsx:432
msgid "Restart required"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:259
msgid "Machine Actions"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:272
msgid "Restart"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:274
msgid "Restart machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:276
msgid "manual restart required"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:292
msgid "Machine information"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:303
#: src/tables/machine/MachineListTable.tsx:599
msgid "Machine Type"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:316
msgid "Machine Driver"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:329
msgid "Initialized"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:350
#: src/tables/machine/MachineTypeTable.tsx:261
msgid "Errors"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:358
#: src/tables/machine/MachineTypeTable.tsx:269
msgid "No errors reported"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:378
msgid "Machine Settings"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:389
msgid "Driver Settings"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:494
#~ msgid "Create machine"
#~ msgstr ""

#: src/tables/machine/MachineListTable.tsx:506
msgid "Add machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:561
msgid "Machine detail"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:608
msgid "Driver"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:73
msgid "Builtin driver"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:89
msgid "Machine type not found."
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:97
msgid "Machine type information"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:110
#: src/tables/machine/MachineTypeTable.tsx:217
msgid "Slug"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:118
#: src/tables/machine/MachineTypeTable.tsx:238
msgid "Provider plugin"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:130
#: src/tables/machine/MachineTypeTable.tsx:250
msgid "Provider file"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:135
#: src/tables/machine/MachineTypeTable.tsx:255
#: src/tables/plugin/PluginListTable.tsx:180
#: src/tables/plugin/PluginListTable.tsx:568
msgid "Builtin"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:146
msgid "Available drivers"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:196
msgid "Machine driver not found."
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:204
msgid "Machine driver information"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:224
msgid "Machine type"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:327
msgid "Builtin type"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:336
msgid "Machine type detail"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:346
msgid "Machine driver detail"
msgstr ""

#: src/tables/notifications/NotificationsTable.tsx:26
msgid "Age"
msgstr ""

#: src/tables/notifications/NotificationsTable.tsx:40
#: src/tables/plugin/PluginErrorTable.tsx:37
msgid "Message"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:74
msgid "Click to edit"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:82
#~ msgid "Edit parameter"
#~ msgstr ""

#: src/tables/part/ParametricPartTable.tsx:127
msgid "Add Part Parameter"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:141
#: src/tables/part/PartParameterTable.tsx:130
#: src/tables/part/PartParameterTable.tsx:153
msgid "Edit Part Parameter"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:224
msgid "Show active parts"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:229
msgid "Show locked parts"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:234
msgid "Show assembly parts"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:68
#: src/tables/part/PartTable.tsx:196
msgid "Include Subcategories"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:69
msgid "Include subcategories in results"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:74
msgid "Show structural categories"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:78
#: src/tables/part/PartTable.tsx:294
msgid "Subscribed"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:79
msgid "Show categories to which the user is subscribed"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:86
msgid "New Part Category"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:112
msgid "Add Part Category"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:38
#: src/tables/part/PartCategoryTemplateTable.tsx:131
msgid "Add Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:46
msgid "Edit Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:54
msgid "Delete Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:76
msgid "Parameter Template"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:93
#~ msgid "[{0}]"
#~ msgstr ""

#: src/tables/part/PartParameterTable.tsx:97
msgid "Internal Units"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:114
msgid "New Part Parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:139
#: src/tables/part/PartParameterTable.tsx:161
msgid "Delete Part Parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:179
msgid "Add parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:198
msgid "Part parameters cannot be edited, as the part is locked"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:212
#: src/tables/stock/StockItemTable.tsx:335
msgid "Include Variants"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:31
msgid "Checkbox"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:32
msgid "Show checkbox templates"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:36
msgid "Has choices"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:37
msgid "Show templates with choices"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:41
#: src/tables/part/PartTable.tsx:220
msgid "Has Units"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:42
msgid "Show templates with units"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:85
msgid "Add Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:100
msgid "Edit Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:111
msgid "Delete Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:141
msgid "Add parameter template"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:77
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:162
msgid "Total Quantity"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:115
msgid "Pending"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:116
msgid "Show pending orders"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:121
msgid "Show received items"
msgstr ""

#: src/tables/part/PartTable.tsx:77
msgid "Minimum stock"
msgstr ""

#: src/tables/part/PartTable.tsx:179
msgid "Filter by part active status"
msgstr ""

#: src/tables/part/PartTable.tsx:185
msgid "Filter by part locked status"
msgstr ""

#: src/tables/part/PartTable.tsx:191
msgid "Filter by assembly attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:197
msgid "Include parts in subcategories"
msgstr ""

#: src/tables/part/PartTable.tsx:203
msgid "Filter by component attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:209
msgid "Filter by testable attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:215
msgid "Filter by trackable attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:221
msgid "Filter by parts which have units"
msgstr ""

#: src/tables/part/PartTable.tsx:226
msgid "Has IPN"
msgstr ""

#: src/tables/part/PartTable.tsx:227
msgid "Filter by parts which have an internal part number"
msgstr ""

#: src/tables/part/PartTable.tsx:232
msgid "Has Stock"
msgstr ""

#: src/tables/part/PartTable.tsx:233
msgid "Filter by parts which have stock"
msgstr ""

#: src/tables/part/PartTable.tsx:239
msgid "Filter by parts which have low stock"
msgstr ""

#: src/tables/part/PartTable.tsx:244
msgid "Purchaseable"
msgstr ""

#: src/tables/part/PartTable.tsx:245
msgid "Filter by parts which are purchaseable"
msgstr ""

#: src/tables/part/PartTable.tsx:250
msgid "Salable"
msgstr ""

#: src/tables/part/PartTable.tsx:251
msgid "Filter by parts which are salable"
msgstr ""

#: src/tables/part/PartTable.tsx:256
#: src/tables/part/PartTable.tsx:260
#: src/tables/part/PartVariantTable.tsx:25
msgid "Virtual"
msgstr ""

#: src/tables/part/PartTable.tsx:257
msgid "Filter by parts which are virtual"
msgstr ""

#: src/tables/part/PartTable.tsx:261
msgid "Not Virtual"
msgstr ""

#: src/tables/part/PartTable.tsx:266
msgid "Is Template"
msgstr ""

#: src/tables/part/PartTable.tsx:267
msgid "Filter by parts which are templates"
msgstr ""

#: src/tables/part/PartTable.tsx:272
msgid "Is Revision"
msgstr ""

#: src/tables/part/PartTable.tsx:273
msgid "Filter by parts which are revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:277
msgid "Has Revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:278
msgid "Filter by parts which have revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:283
msgid "Filter by parts which have pricing information"
msgstr ""

#: src/tables/part/PartTable.tsx:289
msgid "Filter by parts which have available stock"
msgstr ""

#: src/tables/part/PartTable.tsx:295
msgid "Filter by parts to which the user is subscribed"
msgstr ""

#: src/tables/part/PartTable.tsx:300
msgid "Has Stocktake"
msgstr ""

#: src/tables/part/PartTable.tsx:301
msgid "Filter by parts which have stocktake information"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:50
msgid "Test is defined for a parent template part"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:64
msgid "Template Details"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:74
msgid "Results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:76
msgid "No Results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:106
#: src/tables/stock/StockItemTestResultTable.tsx:365
msgid "Required"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:107
msgid "Show required tests"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:111
#: src/tables/settings/TemplateTable.tsx:167
#: src/tables/settings/TemplateTable.tsx:283
msgid "Enabled"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:112
msgid "Show enabled tests"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:116
msgid "Requires Value"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:117
msgid "Show tests that require a value"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:121
msgid "Requires Attachment"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:122
msgid "Show tests that require an attachment"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:126
msgid "Include Inherited"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:127
msgid "Show tests from inherited templates"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:131
msgid "Has Results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:132
msgid "Show tests which have recorded results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:154
#: src/tables/part/PartTestTemplateTable.tsx:237
msgid "Add Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:170
msgid "Edit Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:181
msgid "Delete Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:183
msgid "This action cannot be reversed"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:185
msgid "Any tests results associated with this template will be deleted"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:204
msgid "View Parent Part"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:257
msgid "Part templates cannot be edited, as the part is locked"
msgstr ""

#: src/tables/part/PartThumbTable.tsx:201
msgid "Select"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:16
msgid "Show active variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:20
msgid "Template"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:21
msgid "Show template variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:26
msgid "Show virtual variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:31
msgid "Show trackable variants"
msgstr ""

#: src/tables/part/RelatedPartTable.tsx:84
msgid "Add Related Part"
msgstr ""

#: src/tables/part/RelatedPartTable.tsx:99
msgid "Delete Related Part"
msgstr ""

#: src/tables/part/RelatedPartTable.tsx:106
msgid "Add related part"
msgstr ""

#: src/tables/plugin/PluginErrorTable.tsx:29
msgid "Stage"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:95
msgid "Plugin with key {pluginKey} not found"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:97
msgid "An error occurred while fetching plugin details"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:113
#~ msgid "Plugin with id {id} not found"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:122
msgid "Plugin information"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:134
msgid "Author"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:134
#~ msgid "Plugin Actions"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:138
#: src/tables/plugin/PluginListTable.tsx:141
#~ msgid "Edit plugin"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:152
#: src/tables/plugin/PluginListTable.tsx:153
#~ msgid "Reload"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:154
msgid "Plugin is not active"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:163
msgid "Package information"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:169
msgid "Package Name"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:175
msgid "Installation Path"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:185
msgid "Package"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:197
msgid "Plugin settings"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:214
msgid "Plugin is active"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:220
msgid "Plugin is inactive"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:227
msgid "Plugin is not installed"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:253
msgid "Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:287
msgid "Description not available"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:319
msgid "Confirm plugin activation"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:320
msgid "Confirm plugin deactivation"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:325
msgid "The selected plugin will be activated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:326
msgid "The selected plugin will be deactivated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:334
msgid "Activate Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:338
#~ msgid "Deactivate Plugin"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:354
#~ msgid "The following plugin will be activated"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:355
#~ msgid "The following plugin will be deactivated"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:362
msgid "Deactivate"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:366
#~ msgid "Confirm"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:373
msgid "Activate"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Activating plugin"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Deactivating plugin"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:392
#~ msgid "Plugin updated"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:394
#~ msgid "The plugin was activated"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:395
#~ msgid "The plugin was deactivated"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:403
#~ msgid "Error updating plugin"
#~ msgstr ""

#: src/tables/plugin/PluginListTable.tsx:406
msgid "Uninstall"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:438
msgid "Install plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:451
msgid "Install"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:452
msgid "Plugin installed successfully"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:457
msgid "Uninstall Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:469
msgid "Confirm plugin uninstall"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:472
msgid "The selected plugin will be uninstalled."
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:473
msgid "This action cannot be undone."
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:477
msgid "Plugin uninstalled successfully"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:484
msgid "Delete Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:485
msgid "Deleting this plugin configuration will remove all associated settings and data. Are you sure you want to delete this plugin?"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:498
msgid "Plugins reloaded"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:499
msgid "Plugins were reloaded successfully"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:515
msgid "Reload Plugins"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:524
msgid "Install Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:544
msgid "Plugin Detail"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:573
msgid "Sample"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:578
#: src/tables/stock/StockItemTable.tsx:340
msgid "Installed"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:615
#~ msgid "Plugin detail"
#~ msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:59
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:108
msgid "Add Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:60
#~ msgid "Parameter updated"
#~ msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:70
msgid "Edit Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:73
#~ msgid "Parameter deleted"
#~ msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
#~ msgid "Are you sure you want to delete this parameter?"
#~ msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:78
msgid "Delete Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartTable.tsx:63
#~ msgid "Create Manufacturer Part"
#~ msgstr ""

#: src/tables/purchasing/ManufacturerPartTable.tsx:100
#~ msgid "Manufacturer part updated"
#~ msgstr ""

#: src/tables/purchasing/ManufacturerPartTable.tsx:112
#~ msgid "Manufacturer part deleted"
#~ msgstr ""

#: src/tables/purchasing/ManufacturerPartTable.tsx:114
#~ msgid "Are you sure you want to remove this manufacturer part?"
#~ msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:102
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:339
msgid "Import Line Items"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:135
msgid "Part Description"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:201
msgid "Supplier Code"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:208
msgid "Supplier Link"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:215
msgid "Manufacturer Code"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:229
msgid "Destination"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:300
msgid "Receive line item"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
#: src/tables/sales/ReturnOrderLineItemTable.tsx:142
#: src/tables/sales/SalesOrderLineItemTable.tsx:240
msgid "Add line item"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:354
msgid "Receive items"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:93
msgid "MPN"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:122
msgid "Base units"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:170
msgid "Supplier part created"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:176
msgid "Add supplier part"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:188
msgid "Show active supplier parts"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:192
msgid "Active Part"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:193
msgid "Show active internal parts"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:193
#~ msgid "Supplier part updated"
#~ msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:197
msgid "Active Supplier"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:198
msgid "Show active suppliers"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:205
#~ msgid "Supplier part deleted"
#~ msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:207
#~ msgid "Are you sure you want to remove this supplier part?"
#~ msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:114
msgid "Received Date"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:128
msgid "Show items which have been received"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:133
msgid "Filter by line item status"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:158
msgid "Receive Item"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:262
msgid "Allocate stock"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:272
msgid "Build stock"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:289
#: src/tables/stock/StockItemTable.tsx:507
msgid "Order stock"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:40
msgid "Create Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:50
msgid "Delete Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:58
msgid "Edit Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:66
msgid "Shipment Reference"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:71
msgid "Items"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:83
msgid "Delivery Date"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:107
msgid "Complete Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:133
msgid "Add shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:146
msgid "Shipped"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:147
msgid "Show shipments which have been shipped"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:151
msgid "Delivered"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:152
msgid "Show shipments which have been delivered"
msgstr ""

#: src/tables/settings/CurrencyTable.tsx:28
msgid "Rate"
msgstr ""

#: src/tables/settings/CurrencyTable.tsx:40
msgid "Exchange rates updated"
msgstr ""

#: src/tables/settings/CurrencyTable.tsx:46
msgid "Exchange rate update error"
msgstr ""

#: src/tables/settings/CurrencyTable.tsx:57
msgid "Refresh currency exchange rates"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:50
msgid "Add Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:60
msgid "Edit Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:68
msgid "Delete Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:100
msgid "Add custom unit"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:31
msgid "When"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:41
msgid "Error Information"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:51
#~ msgid "Delete error report"
#~ msgstr ""

#: src/tables/settings/ErrorTable.tsx:53
msgid "Delete Error Report"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:55
msgid "Are you sure you want to delete this error report?"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:57
msgid "Error report deleted"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:79
#: src/tables/settings/FailedTasksTable.tsx:59
msgid "Error Details"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:26
#: src/tables/settings/PendingTasksTable.tsx:19
#: src/tables/settings/ScheduledTasksTable.tsx:19
msgid "Task"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:32
#: src/tables/settings/PendingTasksTable.tsx:24
msgid "Task ID"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:36
#: src/tables/stock/StockItemTestResultTable.tsx:211
msgid "Started"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:42
msgid "Stopped"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:48
msgid "Attempts"
msgstr ""

#: src/tables/settings/GroupTable.tsx:90
msgid "Group with id {id} not found"
msgstr ""

#: src/tables/settings/GroupTable.tsx:92
msgid "An error occurred while fetching group details"
msgstr ""

#: src/tables/settings/GroupTable.tsx:116
msgid "Permission set"
msgstr ""

#: src/tables/settings/GroupTable.tsx:177
msgid "Delete group"
msgstr ""

#: src/tables/settings/GroupTable.tsx:178
msgid "Group deleted"
msgstr ""

#: src/tables/settings/GroupTable.tsx:180
msgid "Are you sure you want to delete this group?"
msgstr ""

#: src/tables/settings/GroupTable.tsx:185
#: src/tables/settings/GroupTable.tsx:197
msgid "Add group"
msgstr ""

#: src/tables/settings/GroupTable.tsx:210
msgid "Edit group"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:38
msgid "Delete Import Session"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:44
#: src/tables/settings/ImportSessionTable.tsx:131
msgid "Create Import Session"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:69
msgid "Uploaded"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:79
msgid "Imported Rows"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:109
#: src/tables/settings/TemplateTable.tsx:289
msgid "Model Type"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:110
#: src/tables/settings/TemplateTable.tsx:290
msgid "Filter by target model type"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:116
msgid "Filter by import session status"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:122
msgid "Filter by user"
msgstr ""

#: src/tables/settings/PendingTasksTable.tsx:38
msgid "Arguments"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:42
msgid "Add Project Code"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:54
msgid "Edit Project Code"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:62
msgid "Delete Project Code"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:92
msgid "Add project code"
msgstr ""

#: src/tables/settings/ScheduledTasksTable.tsx:25
msgid "Last Run"
msgstr ""

#: src/tables/settings/ScheduledTasksTable.tsx:47
msgid "Next Run"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:85
msgid "Template not found"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:87
msgid "An error occurred while fetching template details"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:120
#~ msgid "{templateTypeTranslation} with id {id} not found"
#~ msgstr ""

#: src/tables/settings/TemplateTable.tsx:124
#~ msgid "An error occurred while fetching {templateTypeTranslation} details"
#~ msgstr ""

#: src/tables/settings/TemplateTable.tsx:146
#~ msgid "actions"
#~ msgstr ""

#: src/tables/settings/TemplateTable.tsx:177
msgid "Modify"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:178
msgid "Modify template file"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:234
#: src/tables/settings/TemplateTable.tsx:302
msgid "Edit Template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:242
msgid "Delete template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Add new"
#~ msgstr ""

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Create new"
#~ msgstr ""

#: src/tables/settings/TemplateTable.tsx:248
msgid "Add Template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:261
msgid "Add template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:284
msgid "Filter by enabled status"
msgstr ""

#: src/tables/settings/UserTable.tsx:82
msgid "User with id {id} not found"
msgstr ""

#: src/tables/settings/UserTable.tsx:84
msgid "An error occurred while fetching user details"
msgstr ""

#: src/tables/settings/UserTable.tsx:102
msgid "Is Active"
msgstr ""

#: src/tables/settings/UserTable.tsx:103
msgid "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
msgstr ""

#: src/tables/settings/UserTable.tsx:107
msgid "Is Staff"
msgstr ""

#: src/tables/settings/UserTable.tsx:108
msgid "Designates whether the user can log into the django admin site."
msgstr ""

#: src/tables/settings/UserTable.tsx:112
msgid "Is Superuser"
msgstr ""

#: src/tables/settings/UserTable.tsx:113
msgid "Designates that this user has all permissions without explicitly assigning them."
msgstr ""

#: src/tables/settings/UserTable.tsx:123
msgid "You cannot edit the rights for the currently logged-in user."
msgstr ""

#: src/tables/settings/UserTable.tsx:154
msgid "No groups"
msgstr ""

#: src/tables/settings/UserTable.tsx:245
msgid "Delete user"
msgstr ""

#: src/tables/settings/UserTable.tsx:246
msgid "User deleted"
msgstr ""

#: src/tables/settings/UserTable.tsx:248
msgid "Are you sure you want to delete this user?"
msgstr ""

#: src/tables/settings/UserTable.tsx:254
#: src/tables/settings/UserTable.tsx:272
msgid "Add user"
msgstr ""

#: src/tables/settings/UserTable.tsx:262
msgid "Added user"
msgstr ""

#: src/tables/settings/UserTable.tsx:285
msgid "Show active users"
msgstr ""

#: src/tables/settings/UserTable.tsx:289
msgid "Staff"
msgstr ""

#: src/tables/settings/UserTable.tsx:290
msgid "Show staff users"
msgstr ""

#: src/tables/settings/UserTable.tsx:294
msgid "Superuser"
msgstr ""

#: src/tables/settings/UserTable.tsx:295
msgid "Show superusers"
msgstr ""

#: src/tables/settings/UserTable.tsx:305
msgid "Edit user"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:39
#: src/tables/stock/LocationTypesTable.tsx:109
msgid "Add Location Type"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:47
msgid "Edit Location Type"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:55
msgid "Delete Location Type"
msgstr ""

#: src/tables/stock/LocationTypesTable.tsx:63
msgid "Icon"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:101
msgid "This stock item is in production"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:110
msgid "This stock item has been assigned to a sales order"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:119
msgid "This stock item has been assigned to a customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:128
msgid "This stock item is installed in another stock item"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:137
msgid "This stock item has been consumed by a build order"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:146
msgid "This stock item has expired"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:150
msgid "This stock item is stale"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:161
msgid "This stock item is fully allocated"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:168
msgid "This stock item is partially allocated"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:196
msgid "This stock item has been depleted"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:232
msgid "Stocktake Date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:236
msgid "Expiry Date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:260
msgid "Stock Value"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:290
msgid "Show stock for active parts"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:295
msgid "Filter by stock status"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:301
msgid "Show stock for assmebled parts"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:306
msgid "Show items which have been allocated"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:311
msgid "Show items which are available"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:315
#: src/tables/stock/StockLocationTable.tsx:44
msgid "Include Sublocations"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:316
msgid "Include stock in sublocations"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:320
msgid "Depleted"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:321
msgid "Show depleted stock items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:326
msgid "Show items which are in stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:331
msgid "Show items which are in production"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:336
msgid "Include stock items for variant parts"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:341
msgid "Show stock items which are installed in other items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:345
msgid "Sent to Customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:346
msgid "Show items which have been sent to a customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:350
msgid "Is Serialized"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:351
msgid "Show items which have a serial number"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:358
msgid "Has Batch Code"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:359
msgid "Show items which have a batch code"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:365
msgid "Show tracked items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:369
msgid "Has Purchase Price"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:370
msgid "Show items which have a purchase price"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:378
msgid "External Location"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:379
msgid "Show items in an external location"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:451
msgid "Add a new stock item"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:460
msgid "Remove some quantity from a stock item"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:482
msgid "Move Stock items to new locations"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:489
msgid "Change stock status"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:491
msgid "Change the status of stock items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:498
msgid "Merge stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:500
msgid "Merge stock items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:509
#: src/tables/stock/StockItemTable.tsx:516
msgid "Order new stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:514
msgid "Assign to customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:523
msgid "Delete stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:525
msgid "Delete stock items"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:131
msgid "Test"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:157
msgid "Test result for installed stock item"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:168
msgid "Result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:190
msgid "Attachment"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:206
msgid "Test station"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:226
msgid "Finished"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:268
#: src/tables/stock/StockItemTestResultTable.tsx:339
msgid "Edit Test Result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:270
msgid "Test result updated"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:276
#: src/tables/stock/StockItemTestResultTable.tsx:348
msgid "Delete Test Result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:278
msgid "Test result deleted"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:292
msgid "Test Passed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:293
msgid "Test result has been recorded"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:300
msgid "Failed to record test result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:317
msgid "Pass Test"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:366
msgid "Show results for required tests"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:370
msgid "Include Installed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:371
msgid "Show results for installed stock items"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:375
#: src/tables/stock/TestStatisticsTable.tsx:74
msgid "Passed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:376
msgid "Show only passed tests"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:38
#~ msgid "structural"
#~ msgstr ""

#: src/tables/stock/StockLocationTable.tsx:43
#~ msgid "external"
#~ msgstr ""

#: src/tables/stock/StockLocationTable.tsx:45
msgid "Include sublocations in results"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:50
msgid "Show structural locations"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:55
msgid "Show external locations"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:59
msgid "Has location type"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:64
msgid "Filter by location type"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:107
#: src/tables/stock/StockLocationTable.tsx:133
msgid "Add Stock Location"
msgstr ""

#: src/tables/stock/StockTrackingTable.tsx:64
msgid "Added"
msgstr ""

#: src/tables/stock/StockTrackingTable.tsx:69
msgid "Removed"
msgstr ""

#: src/tables/stock/StockTrackingTable.tsx:198
msgid "No user information"
msgstr ""

#: src/tables/stock/TestStatisticsTable.tsx:46
#: src/tables/stock/TestStatisticsTable.tsx:76
msgid "Total"
msgstr ""

#: src/tables/stock/TestStatisticsTable.tsx:75
msgid "Failed"
msgstr ""

#: src/views/MobileAppView.tsx:22
msgid "Mobile viewport detected"
msgstr ""

#: src/views/MobileAppView.tsx:25
msgid "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
msgstr ""

#: src/views/MobileAppView.tsx:31
msgid "Read the docs"
msgstr ""

#: src/views/MobileAppView.tsx:35
msgid "Ignore and continue to Desktop view"
msgstr ""
