---
title: Release 0.1.5
---

## Release 0.1.5

[Release 0.1.5](https://github.com/inventree/InvenTree/releases/tag/0.1.5) (January 2021) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Target Date for Build Order

[#1168](https://github.com/inventree/InvenTree/pull/1168) introduces the concept of *Target Date* for a Build Order. This is the intended completion date for the build. If the date is reached but the build is not yet complete, the build is consider *overdue*.

Refer to the [build documentation](../manufacturing/build.md#overdue-builds) for more information.

### Target Date for Sales Order

[#1177](https://github.com/inventree/InvenTree/pull/1177) introduces the concept of *Target Date* for a Sales Order. This is the intended shipment date for the order. If the date is reached but the order is not yet complete, the order is considered *overdue*.

### Target Date for Purchase Order

[#1232](https://github.com/inventree/InvenTree/pull/1232) introduces the concept *Target Date* for a Purchase Order. This is the expected delivery date for the order. If the date is reached but the order is not yet complete, the order is considered *overdue*.

### Stock Item Expiry

[#1202](https://github.com/inventree/InvenTree/pull/1202) introduces the concept of an *Expiry Date* for Stock Items. For further information, refer to the [expiry documentation](../stock/expiry.md).

### Stock Ownership

[#1155](https://github.com/inventree/InvenTree/pull/1155) adds ownership to stock locations and items. Ownership allows control over which user group or individual user can edit and manage specific stock locations and items.
For further information, refer to the [ownership documentation](../stock/owner.md).

### Calendar Views

[#1208](https://github.com/inventree/InvenTree/pull/1208) adds interactive calendar displays to assist with order scheduling. Calendar displays are available for:

- Build Orders
- Purchase Orders
- Sales Orders

### Improved Label Printing

[#1212](https://github.com/inventree/InvenTree/pull/1212) significantly improves the existing label printing functionality. Documentation for the new label printing system [can be found here](../report/labels.md).

### Improved Report Printing

[#1242](https://github.com/inventree/InvenTree/pull/1242) significantly improves the existing report printing functionality, allowing multiple reports to be generated into a single PDF.

### Improved Permission System

[#1221](https://github.com/inventree/InvenTree/pull/1221) adds new permission roles as follows:

- **Part** role split into *Part* and *Part Category* roles
- **Stock** role spit into *Stock Item* and *Stock Location* roles

## Major Bug Fixes

| PR | Description |
| --- | --- |
| [#1144](https://github.com/inventree/InvenTree/pull/1144) | Fixes infinite loop recursion when displaying BOM table |
| [#1175](https://github.com/inventree/InvenTree/pull/1175) | Fixes display of buttons in particular StockItem tab |
| [#1196](https://github.com/inventree/InvenTree/pull/1195) | Fixes issue where forms incorrectly required date fields to be filled |
| [#1197](https://github.com/inventree/InvenTree/pull/1197) | Fixes variable scope issue which caused problems with BOM creation |
