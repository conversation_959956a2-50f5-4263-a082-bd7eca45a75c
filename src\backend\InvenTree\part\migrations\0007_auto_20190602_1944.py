# Generated by Django 2.2 on 2019-06-02 09:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0006_auto_20190526_1215'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='part',
            name='buildable',
        ),
        migrations.RemoveField(
            model_name='part',
            name='consumable',
        ),
        migrations.AddField(
            model_name='part',
            name='assembly',
            field=models.BooleanField(default=False, help_text='Can this part be built from other parts?', verbose_name='Assembly'),
        ),
        migrations.AddField(
            model_name='part',
            name='component',
            field=models.BooleanField(default=True, help_text='Can this part be used to build other parts?', verbose_name='Component'),
        ),
        migrations.AlterField(
            model_name='bomitem',
            name='part',
            field=models.ForeignKey(help_text='Select parent part', limit_choices_to={'active': True, 'assembly': True}, on_delete=django.db.models.deletion.CASCADE, related_name='bom_items', to='part.Part'),
        ),
        migrations.AlterField(
            model_name='bomitem',
            name='sub_part',
            field=models.ForeignKey(help_text='Select part to be used in BOM', limit_choices_to={'active': True, 'component': True}, on_delete=django.db.models.deletion.CASCADE, related_name='used_in', to='part.Part'),
        ),
    ]
