# Generated by Django 4.2.12 on 2024-05-08 01:38

from django.db import migrations


def forward(apps, schema_editor):
    """Find and delete any BuildItem instances which have a null BuildLine field."""
    
    BuildItem = apps.get_model('build', 'BuildItem')

    items = BuildItem.objects.filter(build_line=None)

    if items.count() > 0:
        print(f"Deleting {items.count()} BuildItem objects with null BuildLine field")
        items.delete()


class Migration(migrations.Migration):

    dependencies = [
        ('build', '0049_alter_builditem_build_line'),
    ]

    operations = [
        migrations.RunPython(forward, reverse_code=migrations.RunPython.noop),
    ]
