# Generated by Django 2.2 on 2019-06-04 12:17

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('company', '0005_auto_20190525_2356'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference', models.CharField(help_text='Order reference', max_length=64, unique=True)),
                ('description', models.CharField(help_text='Order description', max_length=250)),
                ('creation_date', models.DateField(auto_now=True)),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, help_text='Order notes')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('supplier', models.ForeignKey(help_text='Company', on_delete=django.db.models.deletion.CASCADE, related_name='Orders', to='company.Company')),
            ],
            options={
                'abstract': False,
                'verbose_name': 'Purchase Order'
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderLineItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Item quantity', validators=[django.core.validators.MinValueValidator(0)])),
                ('reference', models.CharField(blank=True, help_text='Line item reference', max_length=100)),
                ('received', models.PositiveIntegerField(default=0, help_text='Number of items received')),
                ('order', models.ForeignKey(help_text='Purchase Order', on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='order.PurchaseOrder')),
            ],
            options={
                'abstract': False,
                'verbose_name': 'Purchase Order Line Item'
            },
        ),
    ]
