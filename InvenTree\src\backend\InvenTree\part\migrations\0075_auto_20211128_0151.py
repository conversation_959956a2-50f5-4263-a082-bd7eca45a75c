# Generated by Django 3.2.5 on 2021-11-28 01:51

import InvenTree.fields
import InvenTree.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0074_partcategorystar'),
    ]

    operations = [
        migrations.AddField(
            model_name='partattachment',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external URL', null=True, verbose_name='Link'),
        ),
        migrations.AlterField(
            model_name='partattachment',
            name='attachment',
            field=models.FileField(blank=True, help_text='Select file to attach', null=True, upload_to='attachments', verbose_name='Attachment'),
        ),
    ]
