---
title: Release 0.7.0
---

## Release: 0.7.0

0.7.0 is a major feature release of the InvenTree software project. For a comprehsive list of changes associated with this release, please refer to the [InvenTree GitHub page](https://github.com/inventree/InvenTree/milestone/10).

### Plugins

In addition to providing a slew of new features and stability improvements (as listed below), this version focuses heavily on improvements to the [plugin system](../plugins/index.md). The plugin ecosystem has received a major overhaul, and now provides a number of new plugin "mixins" for supporting custom functionality. The plugin system will continue to receive attention over the next major release cycle.


!!! warning "Plugin Changes"
    Version `0.7.0` introduces some major changes (improvements) to the plugin interface. If you are updating from a previous version, any custom plugins you have installed may also need to be updated.

### Unit Testing

This release also provides a marked improvement in unit testing and code coverage. The project is now above 85% code coverage! This is a great milestone, but we can do better! We will continue to improve our unit testing to ensure that InvenTree remains a high quality, stable software product.

## New Features

### Order Target Dates

[#2684](https://github.com/inventree/InvenTree/pull/2684) adds a `target_date` field to both the PurchaseOrderLineItem and SalesOrderLineItem models, separate to the `target_date` field on the parent _Order_ models. This allows expected shipment dates to be specified for individual line items.

### It's a Date!

[#2685](https://github.com/inventree/InvenTree/pull/2685) adds the ability for users to customize how dates are formatted and displayed in the web interface.

### Serialize Incoming Stock

[#2686](https://github.com/inventree/InvenTree/pull/2686) provides the ability to add batch codes and serial numbers to incoming stock items received against purchase orders.

### Persistent Forms

[#2687](https://github.com/inventree/InvenTree/pull/2687) adds the ability for modal forms to be "persistent" - i.e. they are not immediately dismissed after successful submission. This allows (for example) the "Create Part" form to be used multiple times in succession, allowing quick creation of parts by the user.

### Stock Scheduling

[#2695](https://github.com/inventree/InvenTree/pull/2695) adds a new "Scheduling" tab to the "Part" display. This displays projected / predicted future stock levels, based on pending orders.

### Automatic Stock Allocation

[#2713](https://github.com/inventree/InvenTree/pull/2713) adds a feature to "automatically" allocate stock items to a build order.

### Extra, Extra

[#2714](https://github.com/inventree/InvenTree/pull/2714) adds "extra line items" to purchase orders and sales orders. This allows tracking of line items which are not associated with a part in the database (e.g. fees, charges)

### Notifications

[#2372](https://github.com/inventree/InvenTree/pull/2372) provides an overhaul of notifications, allowing users to view their notifications directly in the InvenTree interface.

### Why are you hiding my name?
[#2861](https://github.com/inventree/InvenTree/pull/2861) adds several changes to enable admins to remove more of InvenTrees branding. Change logo, hide the about-modal for all but superusers and add custom messages to login and main navbar. Check out [the docs](../start/config.md#customization-options)

### Label Printing Plugin

[#2768](https://github.com/inventree/InvenTree/pull/2768) adds a new LabelPrinting plugin class, which allows printing labels directly to external printers (e.g. via local network).

### New Search Menu

[#2783](https://github.com/inventree/InvenTree/pull/2783) introduces a new quick search menu which provides more comprehensive search results for quicker data access.

### BOM Stock Data

The Bill of Materials tables now display a more comprehensive view of available stock, including stock for variant parts, and stock for substitute parts. This allows users a better picture of what stock is actually available for use.

### Docker Improvements

Multiple improvements have been made to the docker installation process, most notably updated docker-compose files for development and production setups. Docker setup time and complexity should now be significantly reduced.

### Build Order Improvements

[#2893](https://github.com/inventree/InvenTree/pull/2893) provides a number of improvements to the build order process. Most notable is a complete overhaul of the "build output" window, providing a more efficient user experience.

### Multi Level BOM Fix

[#2901](https://github.com/inventree/InvenTree/pull/2901) overhauls the way that multi level BOMs are displayed. Instead of loading *all* BOM data by default, a flat top-level BOM is first loaded, and then the user has the option to load BOMs from subassemblies into the same table.

### QR code scanner

[#2779](https://github.com/inventree/InvenTree/pull/2779) provides a QR code scanner which can be used to quickly scan InvenTree generated QR codes using webcams or mobile devices. This feature requires secure (HTTPS) connection to the server.

### Order, Order

[#2770](https://github.com/inventree/InvenTree/pull/2770) implements a major overhaul of the "order parts" wizard, with the form now making use of the API rather than being rendered on the server.

### Panel Plugins

[#2937](https://github.com/inventree/InvenTree/pull/2937) adds a new type of plugin mixin, which allows rendering of custom "panels" on certain pages. This is a powerful new plugin feature which allows custom UI elements to be generated with ease. REMOVED AFTER 0.17.0

## Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#2869](https://github.com/inventree/InvenTree/pull/2869) | Fixes Part API bug |
