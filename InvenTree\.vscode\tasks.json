{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format

  // the problemMatchers should prevent vscode from asking how it should check the output

  "version": "2.0.0",
  "tasks": [
    {
      "label": "worker",
      "type": "shell",
      "command": "invoke worker",
      "problemMatcher": [],
    },
    {
      "label": "clean-settings",
      "type": "shell",
      "command": "invoke int.clean-settings",
      "problemMatcher": [],
    },
    {
      "label": "delete-data",
      "type": "shell",
      "command": "invoke dev.delete-data",
      "problemMatcher": [],
    },
    {
      "label": "migrate",
      "type": "shell",
      "command": "invoke migrate",
      "problemMatcher": [],
    },
    {
      "label": "server",
      "type": "shell",
      "command": "invoke dev.server",
      "problemMatcher": [],
    },
    {
      "label": "setup-dev",
      "type": "shell",
      "command": "invoke dev.setup-dev",
      "problemMatcher": [],
    },
    {
      "label": "setup-test",
      "type": "shell",
      "command": "invoke dev.setup-test -i --path dev/inventree-demo-dataset",
      "problemMatcher": [],
    },
    {
      "label": "superuser",
      "type": "shell",
      "command": "invoke superuser",
      "problemMatcher": [],
    },
    {
      "label": "test",
      "type": "shell",
      "command": "invoke dev.test",
      "problemMatcher": [],
    },
    {
      "label": "update",
      "type": "shell",
      "command": "invoke update",
      "problemMatcher": [],
    },
  ]
}
