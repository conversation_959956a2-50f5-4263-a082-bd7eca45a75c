---
title: Release 0.4.0
---

## Release 0.4.0

[Release 0.4.0](https://github.com/inventree/InvenTree/releases/tag/0.4.0) provides a number of major new features and improvements, as well as some crucial bug fixes:

### Dynamic Reloading

[#1811](https://github.com/inventree/InvenTree/pull/1811) provides dynamic loading of detail views via the left-hand navigation menu. This significantly improves perceived speed by the user, as the entire page does not have to be reloaded.

### Test Report

[#1842](https://github.com/inventree/InvenTree/pull/1842) adds "installed items" to the available context data for a StockItem TestReport template.

### Search Bar

[#1838](https://github.com/inventree/InvenTree/pull/1838) adds a "live autocomplete" feature to the search bar. Typing a search query into the search bar will now reveal a drop-down menu showing the matching parts. The default search results page can still be accessed by pressing "Enter" to complete the query.

### Settings

[#1859](https://github.com/inventree/InvenTree/pull/1859) introduces the concept of "per-user settings". These settings do not apply globally, only to the current user.

[#1863](https://github.com/inventree/InvenTree/pull/1863) refactors the existing settings display with a more moden feel.

## Major Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#1823](https://github.com/inventree/InvenTree/pull/1823) | Fixes link formatting issues for ManufacturerParts |
| [#1824](https://github.com/inventree/InvenTree/pull/1824) | Selects correct currency code when creating a new PurchaseOrderLineItem |
| [#1867](https://github.com/inventree/InvenTree/pull/1867) | Fixes long-running bug when deleting sequential items via the API |
