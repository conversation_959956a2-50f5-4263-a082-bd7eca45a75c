---
title: Release 0.2.1
---

## Release 0.2.1

[Release 0.2.1](https://github.com/inventree/InvenTree/releases/tag/0.2.1) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Email Support

[#1304](https://github.com/inventree/InvenTree/pull/1304) adds support for email support. Initially, this is used for sending users emails to perform a password reset

!!! warning "Configuration Required"
    Refer to the [email configuration
    options](../start/config.md#email-settings).

### Manufacturer Parts

[#1417](https://github.com/inventree/InvenTree/pull/1417) adds a new model and
database table to InvenTree: `ManufacturerPart`. In older versions, manufacturer
data was stored inside a `SupplierPart`. With this new model, the data is now
stored independently. Users can use either manufacturer or supplier parts as
sourcing information for a part. Soon, InvenTree will allow the use of
manufacturer data directly in purchase orders.

Details on how to create and manage manufacturer parts were added
[here](../purchasing/manufacturer.md#add-manufacturer-part)

### URL-style QR Code for StockItem

[#1462](https://github.com/inventree/InvenTree/pull/1417) adds the ability to
create a QR code containing the URL of a StockItem, which can be opened directly
on a portable device using the camera or a QR code scanner. More details [here](../report/labels.md).

## Major Bug Fixes

| PR | Description |
| --- | --- |
| [#1453](https://github.com/inventree/InvenTree/pull/1453) | Adds *detail* API endpoint for the `PartParameter` model, which was previously missing. |
| [#1478](https://github.com/inventree/InvenTree/pull/1478) | Fixes a bug which exposed database username and password in the log files |
