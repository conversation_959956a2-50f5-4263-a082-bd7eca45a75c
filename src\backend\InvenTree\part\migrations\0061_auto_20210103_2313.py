# Generated by Django 3.0.7 on 2021-01-03 12:13

import InvenTree.fields
from django.db import migrations, models
import django.db.models.deletion
import mptt.fields
import part.settings


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0055_auto_20201117_1453'),
        ('part', '0060_merge_20201112_1722'),
    ]

    operations = [
        migrations.AlterField(
            model_name='part',
            name='IPN',
            field=models.CharField(blank=True, help_text='Internal Part Number', max_length=100, null=True, verbose_name='IPN'),
        ),
        migrations.AlterField(
            model_name='part',
            name='assembly',
            field=models.BooleanField(default=part.settings.part_assembly_default, help_text='Can this part be built from other parts?', verbose_name='Assembly'),
        ),
        migrations.AlterField(
            model_name='part',
            name='category',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Part category', null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='parts', to='part.PartCategory', verbose_name='Category'),
        ),
        migrations.AlterField(
            model_name='part',
            name='default_location',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Where is this item normally stored?', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_parts', to='stock.StockLocation', verbose_name='Default Location'),
        ),
        migrations.AlterField(
            model_name='part',
            name='description',
            field=models.CharField(help_text='Part description', max_length=250, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='part',
            name='is_template',
            field=models.BooleanField(default=part.settings.part_template_default, help_text='Is this part a template part?', verbose_name='Is Template'),
        ),
        migrations.AlterField(
            model_name='part',
            name='keywords',
            field=models.CharField(blank=True, help_text='Part keywords to improve visibility in search results', max_length=250, null=True, verbose_name='Keywords'),
        ),
        migrations.AlterField(
            model_name='part',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external URL', null=True, verbose_name='Link'),
        ),
        migrations.AlterField(
            model_name='part',
            name='name',
            field=models.CharField(help_text='Part name', max_length=100, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='part',
            name='notes',
            field=models.TextField(blank=True, help_text='Part notes - supports Markdown formatting', null=True, verbose_name='Notes'),
        ),
        migrations.AlterField(
            model_name='part',
            name='revision',
            field=models.CharField(blank=True, help_text='Part revision or version number', max_length=100, null=True, verbose_name='Revision'),
        ),
        migrations.AlterField(
            model_name='part',
            name='variant_of',
            field=models.ForeignKey(blank=True, help_text='Is this part a variant of another part?', limit_choices_to={'active': True, 'is_template': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='variants', to='part.Part', verbose_name='Variant Of'),
        ),
        migrations.AlterField(
            model_name='part',
            name='virtual',
            field=models.BooleanField(default=part.settings.part_virtual_default, help_text='Is this a virtual part, such as a software product or license?', verbose_name='Virtual'),
        ),
    ]
