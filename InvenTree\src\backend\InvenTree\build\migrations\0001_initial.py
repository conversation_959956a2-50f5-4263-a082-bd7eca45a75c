# Generated by Django 2.2 on 2019-05-20 12:04

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Build',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Brief description of the build', max_length=100)),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Number of parts to build', validators=[django.core.validators.MinValueValidator(1)])),
                ('status', models.PositiveIntegerField(choices=[(10, 'Pending'), (20, 'Allocated'), (30, 'Cancelled'), (40, 'Complete')], default=10, help_text='Build status', validators=[django.core.validators.MinValueValidator(0)])),
                ('batch', models.CharField(blank=True, help_text='Batch code for this build output', max_length=100, null=True)),
                ('creation_date', models.DateField(auto_now=True)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('URL', models.URLField(blank=True, help_text='Link to external URL')),
                ('notes', models.TextField(blank=True, help_text='Extra build notes')),
            ],
        ),
        migrations.CreateModel(
            name='BuildItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Stock quantity to allocate to build', validators=[django.core.validators.MinValueValidator(1)])),
                ('build', models.ForeignKey(help_text='Build to allocate parts', on_delete=django.db.models.deletion.CASCADE, related_name='allocated_stock', to='build.Build')),
            ],
        ),
    ]
