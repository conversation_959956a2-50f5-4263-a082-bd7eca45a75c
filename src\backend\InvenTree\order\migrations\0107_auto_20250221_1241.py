# Generated by Django 4.2.19 on 2025-02-21 12:41

import InvenTree.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0106_purchaseorder_start_date_returnorder_start_date_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseorder',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='returnorder',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='returnorderlineitem',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='purchaseorderextraline',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='salesorderextraline',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='returnorderextraline',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
        migrations.AlterField(
            model_name='salesordershipment',
            name='link',
            field=models.TextField(null=True, blank=True)  # Temporary change to force new ALTER COLUMN operation in the next migration
        ),
    ]
