---
title: Release 0.2.4
---

## Release 0.2.4

[Release 0.2.4](https://github.com/inventree/InvenTree/releases/tag/0.2.4) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Pricing

This release of InvenTree introduces a number of key improvements regarding pricing:

#### Total Price for Purchase Orders

[#1666](https://github.com/inventree/InvenTree/pull/1666) adds a *Total Price* column to the Purchase Order display.

#### Total Price for Sales Orders

[#1669](https://github.com/inventree/InvenTree/pull/1669) adds a *Total Price* column to the Sales Order display

#### Internal Price

[#1634](https://github.com/inventree/InvenTree/pull/1634) adds the concept of *Internal Price* for a Part.

#### Order Price Preview

[#1682](https://github.com/inventree/InvenTree/pull/1682) adds pricing preview when ordering parts

### Purchase Order Destination

[#1587](https://github.com/inventree/InvenTree/pull/1587) displays the desired destination for items received against a Purchase Order

### Part Allocation Display

[#1316](https://github.com/inventree/InvenTree/pull/1316) improves the display of stock allocations for a given Part

### Part Import Wizard

[#1588](https://github.com/inventree/InvenTree/pull/1588) adds a wizard for importing part data into the database

## Major Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#1684](https://github.com/inventree/InvenTree/pull/1684) | Ensure tree structures are rebuilt after data import |
| [#1691](https://github.com/inventree/InvenTree/pull/1691) | Fixes rendering bug when purchase price is set to zero |
| [#1692](https://github.com/inventree/InvenTree/pull/1692) | Allows sorting of *Part* table by *Category* field |
| [#1700](https://github.com/inventree/InvenTree/pull/1700) | Fixes a long-running API bug where default field values were not observed |
| [#1705](https://github.com/inventree/InvenTree/pull/1705) | Fixes unique-constraint validation bug for API |
| [#1706](https://github.com/inventree/InvenTree/pull/1706) | Fixes table sorting bug in build order allocations table |
| [#1707](https://github.com/inventree/InvenTree/pull/1707) | Fixes multiple bugs in the *order* API |
| [#1710](https://github.com/inventree/InvenTree/pull/1710) | Allows direct upload of Part and Company images via the REST API |
| [#1719](https://github.com/inventree/InvenTree/pull/1719) | Fixes nesting display for stock item test results |
| [#1722](https://github.com/inventree/InvenTree/pull/1722) | Fixes some issues with internal pricing calculations |
