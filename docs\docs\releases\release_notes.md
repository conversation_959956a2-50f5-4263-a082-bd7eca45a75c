---
title: Release Notes
---

## InvenTree Versioning

The InvenTree project follows a formalized release numbering scheme, according to the [semantic versioning specification](https://semver.org/).

### Stable Branch

The head of the *stable* code branch represents the most recent stable tagged release of InvenTree.

!!! info "{{ icon("brand-docker") }} Stable Docker"
    To pull down the latest *stable* release of InvenTree in docker, use `inventree/inventree:stable`

### Development Branch

The head of the *master* code branch represents the "latest and greatest" working codebase. All features and bug fixes are merged into the master branch, in addition to relevant stable release branches.

!!! info "{{ icon("brand-docker") }} Latest Docker"
    To pull down the latest *development* version of InvenTree in docker, use `inventree/inventree:latest`

## Stable Releases


{% include "release_table.html" %}

## Upcoming Features

In-progress and upcoming features can be viewed on [GitHub](https://github.com/inventree/inventree/pulls), where the InvenTree source code is hosted.

## Suggest Something New

To suggest a new feature (or report a bug) raise an [issue on GitHub](https://github.com/inventree/inventree/issues).
