msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Italian\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: it\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Devi abilitare l'autenticazione a due fattori prima di fare qualsiasi altra cosa."

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "Endpoint API non trovato"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr "L'elenco degli articoli o dei filtri devono essere forniti per le operazioni di massa"

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr "Gli articoli devono essere forniti come elenco"

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Lista elementi fornita non valida"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr "I filtri devono essere forniti come dizionario"

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Filtri forniti non validi"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr "Tutti i filtri devono essere usati solo con true"

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr "Nessun elemento corrisponde ai criteri forniti"

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "L'utente non ha i permessi per vedere questo modello"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "Email (ancora)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "Conferma indirizzo email"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "È necessario digitare la stessa e-mail ogni volta."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "L'indirizzo email principale fornito non è valido."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "L'indirizzo di posta elettronica fornito non è approvato."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Unità fornita non valida ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Nessun valore specificato"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Impossibile convertire {original} in {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Quantità inserita non valida"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "I dettagli dell'errore possono essere trovati nel pannello di amministrazione"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Inserisci la data"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Valore decimale non valido"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Note"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Il valore '{name}' non è nel formato del pattern"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Il valore fornito non corrisponde al modello richiesto: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Impossibile serializzare più di 1000 elementi contemporaneamente"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Numero seriale vuoto"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Seriale Duplicato"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Gruppo non valido: {group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "L'intervallo di gruppo {group} supera la quantità consentita ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Nessun numero di serie trovato"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr "Il numero dei numeri seriali univoci ({n}) deve essere uguale alla quantità ({q})"

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Rimuovi i tag HTML da questo valore"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "I dati contengono un contenuto in markdown proibito"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Errore di connessione"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Il server ha risposto con un codice di stato non valido"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Si è verificata un'eccezione"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Il server ha risposto con valore Content-Length non valido"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Immagine troppo grande"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Il download dell'immagine ha superato la dimensione massima"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Il server remoto ha restituito una risposta vuota"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "L'URL fornito non è un file immagine valido"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabo"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgaro"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Ceco"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danese"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Tedesco"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Greco"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Inglese"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spagnolo"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spagnolo (Messicano)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estone"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persiano"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finlandese"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Francese"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Ebraico"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Ungherese"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiano"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Giapponese"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Coreano"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Lituano"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Lettone"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Olandese"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norvegese"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polacco"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portoghese"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portoghese (Brasile)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumeno"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russo"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovacco"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Sloveno"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbo"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Svedese"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thailandese"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turco"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ucraino"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamita"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Cinese (Semplificato)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Cinese (Tradizionale)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Accedi all'app"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "Email"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Errore nell'eseguire la convalida del plugin"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "I metadati devono essere un oggetto python dict"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Metadati Plugin"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "Campo di metadati JSON, da utilizzare con plugin esterni"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Schema formattato impropriamente"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Formato chiave sconosciuta"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Formato chiave mancante"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "Il campo di riferimento non può essere vuoto"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "Il campo deve corrispondere al modello richiesto"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Numero di riferimento troppo grande"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Scelta non valida"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Nome"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Descrizione"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Descrizione (opzionale)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Percorso"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Nomi duplicati non possono esistere sotto lo stesso genitore"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Note di Markdown (opzionale)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Dati del Codice a Barre"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Dati Codice a Barre applicazioni di terze parti"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Codice a Barre"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Codice univoco del codice a barre"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Trovato codice a barre esistente"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Fallimento Attività"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Attività di lavoro in background '{f}' fallita dopo {n} tentativi"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Errore del server"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "Un errore è stato loggato dal server."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Deve essere un numero valido"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Valuta"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Selezionare la valuta dalle opzioni disponibili"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Valore non valido"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Immagine Remota"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL del file immagine remota"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Il download delle immagini da URL remoto non è abilitato"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Impossibile scaricare l'immagine dall'URL remoto"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr "Aggiornamento disponibile"

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr "È disponibile un aggiornamento per InvenTree"

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Unità fisica non valida"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Non è un codice valuta valido"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Stato dell'ordine"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Produzione Genitore"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Includi Varianti"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Articolo"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Categoria"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Produzione Antenata"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Assegnato a me"

#: build/api.py:154
msgid "Assigned To"
msgstr "Assegnato a"

#: build/api.py:189
msgid "Created before"
msgstr "Creato prima"

#: build/api.py:193
msgid "Created after"
msgstr "Creato dopo"

#: build/api.py:197
msgid "Has start date"
msgstr "Ha data d'inizio"

#: build/api.py:205
msgid "Start date before"
msgstr "Data d'inizio prima"

#: build/api.py:209
msgid "Start date after"
msgstr "Data d'inizio dopo"

#: build/api.py:213
msgid "Has target date"
msgstr "Ha data di fine"

#: build/api.py:221
msgid "Target date before"
msgstr "Data obiettivo prima"

#: build/api.py:225
msgid "Target date after"
msgstr "Data obiettivo dopo"

#: build/api.py:229
msgid "Completed before"
msgstr "Completato prima"

#: build/api.py:233
msgid "Completed after"
msgstr "Completato dopo"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr "Data minima"

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr "Data massima"

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Escludi Albero"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "La produzione deve essere annullata prima di poter essere eliminata"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Consumabile"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Opzionale"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Assemblaggio"

#: build/api.py:450
msgid "Tracked"
msgstr "Monitorato"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Testabile"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "Ordine In Corso"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Allocato"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Disponibile"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Ordine di Produzione"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Posizione"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Ordini di Produzione"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "Assembly BOM non è stato convalidato"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "L'ordine di generazione non può essere creato per una parte inattiva"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "L'ordine di compilazione non può essere creato per una parte sbloccata"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr "Gli ordini di costruzione possono essere eseguiti solo esternamente per gli articoli acquistabili"

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "L'utente o il gruppo responsabile deve essere specificato"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "L'ordine di costruzione della parte non può essere cambiata"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr "La data di scadenza deve essere successiva alla data d'inizio"

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Riferimento Ordine Di Produzione"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Riferimento"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Breve descrizione della build (facoltativo)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Ordine di produzione a cui questa produzione viene assegnata"

#: build/models.py:273
msgid "Select part to build"
msgstr "Selezionare parte da produrre"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Numero di riferimento ordine di vendita"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Ordine di vendita a cui questa produzione viene assegnata"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Posizione Di Origine"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Seleziona la posizione da cui prelevare la giacenza (lasciare vuoto per prelevare da qualsiasi posizione di magazzino)"

#: build/models.py:300
msgid "External Build"
msgstr "Build Esterno"

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr "Questo ordine di produzione è eseguito esternamente"

#: build/models.py:306
msgid "Destination Location"
msgstr "Posizione Della Destinazione"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Seleziona il luogo in cui gli articoli completati saranno immagazzinati"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Quantità Produzione"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Numero di articoli da costruire"

#: build/models.py:322
msgid "Completed items"
msgstr "Articoli completati"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Numero di articoli di magazzino che sono stati completati"

#: build/models.py:328
msgid "Build Status"
msgstr "Stato Produzione"

#: build/models.py:333
msgid "Build status code"
msgstr "Codice stato di produzione"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Codice Lotto"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Codice del lotto per questa produzione"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Data di creazione"

#: build/models.py:356
msgid "Build start date"
msgstr "Data inizio produzione"

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr "Data d'inizio programmata per questo ordine di produzione"

#: build/models.py:363
msgid "Target completion date"
msgstr "Data completamento obiettivo"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Data di completamento della produzione. Dopo tale data la produzione sarà in ritardo."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Data di completamento"

#: build/models.py:378
msgid "completed by"
msgstr "Completato da"

#: build/models.py:387
msgid "Issued by"
msgstr "Rilasciato da"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Utente che ha emesso questo ordine di costruzione"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Responsabile"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Utente o gruppo responsabile di questo ordine di produzione"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Collegamento esterno"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Link a URL esterno"

#: build/models.py:410
msgid "Build Priority"
msgstr "Priorità di produzione"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Priorità di questo ordine di produzione"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Codice del progetto"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Codice del progetto per questo ordine di produzione"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Impossibile scaricare l'attività per completare le allocazioni di build"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "L'ordine di produzione {build} è stato completato"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "L'ordine di produzione è stato completato"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Deve essere fornita un numero di serie per gli articoli rintracciabili"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Nessun output di produzione specificato"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "La produzione è stata completata"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "L'output della produzione non corrisponde all'ordine di compilazione"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "La quantità deve essere maggiore di zero"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "La quantità non può essere maggiore della quantità in uscita"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr "La produzione non ha superati tutti i test richiesti"

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "L'output della build {serial} non ha superato tutti i test richiesti"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Elemento di Riga Ordine di Produzione"

#: build/models.py:1602
msgid "Build object"
msgstr "Crea oggetto"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Quantità"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Quantità richiesta per l'ordine di costruzione"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "L'elemento di compilazione deve specificare un output poiché la parte principale è contrassegnata come rintracciabile"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "La quantità assegnata ({q}) non deve essere maggiore della quantità disponibile ({a})"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "L'articolo in giacenza è sovrallocato"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "La quantità di assegnazione deve essere maggiore di zero"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "La quantità deve essere 1 per lo stock serializzato"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "L'articolo in stock selezionato non corrisponde alla voce nella BOM"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Articoli in magazzino"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Origine giacenza articolo"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Quantità di magazzino da assegnare per la produzione"

#: build/models.py:1924
msgid "Install into"
msgstr "Installa in"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Destinazione articolo in giacenza"

#: build/serializers.py:115
msgid "Build Level"
msgstr "Livello Produzione"

#: build/serializers.py:124
msgid "Part Name"
msgstr "Nome Articolo"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Etichetta Codice Progetto"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Genera Output"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "L'output generato non corrisponde alla produzione principale"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "L'output non corrisponde alle parti dell'ordine di produzione"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Questa produzione è stata già completata"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Questo output non è stato completamente assegnato"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Inserisci la quantità per l'output di compilazione"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Quantità totale richiesta per articoli rintracciabili"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Quantità totale richiesta, poiché la fattura dei materiali contiene articoli rintracciabili"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Codice Seriale"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Inserisci i numeri di serie per gli output di compilazione (build option)"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Posizione dello stock per l'output della produzione"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Numeri di Serie Assegnazione automatica"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Assegna automaticamente gli articoli richiesti con i numeri di serie corrispondenti"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "I seguenti numeri di serie sono già esistenti o non sono validi"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "Deve essere fornito un elenco dei risultati di produzione"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Posizione dello stock per l'output di produzione rimosso"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Scarta Assegnazioni"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Scartare tutte le assegnazioni di magazzino per gli output rimossi"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Motivo dell'eliminazione degli output di compilazione"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Posizione per gli output di build completati"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Accetta Assegnazione Incompleta"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Completa l'output se le scorte non sono state interamente assegnate"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Consuma Giacenze Allocate"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Consuma tutte le scorte che sono già state assegnate a questa produzione"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Rimuovi Output Incompleti"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Elimina gli output di produzione che non sono stati completati"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Non permesso"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Accetta come consumato da questo ordine di produzione"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Non assegnare prima di aver completato questo ordine di produzione"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Giacenza in eccesso assegnata"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Come si desidera gestire gli elementi extra giacenza assegnati all'ordine di produzione"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Alcuni articoli di magazzino sono stati assegnati in eccedenza"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Accetta Non Assegnato"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Accetta che gli elementi in giacenza non sono stati completamente assegnati a questo ordine di produzione"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "La giacenza richiesta non è stata completamente assegnata"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Accetta Incompleta"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Accetta che il numero richiesto di output di produzione non sia stato completato"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "La quantità di produzione richiesta non è stata completata"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "L'ordine di costruzione ha ancora degli ordini di costruzione figli"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "L'ordine di costruzione deve essere in stato di produzione"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "L'ordine di produzione ha output incompleti"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Linea di produzione"

#: build/serializers.py:877
msgid "Build output"
msgstr "Genera Output"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "L'output di produzione deve puntare alla stessa produzione"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Articolo linea di produzione"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "gli elementi degli articoli della distinta base devono puntare alla stessa parte dell'ordine di produzione"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "L'articolo deve essere disponibile"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Quantità disponibile ({q}) superata"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "L'output di produzione deve essere specificato per l'ubicazione delle parti tracciate"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "L'output di produzione non deve essere specificato per l'ubicazione delle parti non tracciate"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Deve essere indicata l'allocazione dell'articolo"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Posizione dello stock in cui le parti devono prelevate (lasciare vuoto per prelevare da qualsiasi luogo)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Escludi Ubicazione"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Escludi gli elementi stock da questa ubicazione selezionata"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Scorte Intercambiabili"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Gli elementi in magazzino in più sedi possono essere utilizzati in modo intercambiabile"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Sostituisci Giacenze"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Consenti l'allocazione delle parti sostitutive"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Articoli Opzionali"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Assegna gli elementi opzionali della distinta base all'ordine di produzione"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Impossibile avviare l'attività di auto-allocazione"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "Riferimento BOM"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "Identificativo dell'Articolo BOM"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "Nome Articolo BOM"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "Costruzione"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Articolo Fornitore"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Quantità assegnata"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Riferimento Ordine Di Costruzione"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Nome Categoria Articolo"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Tracciabile"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Ereditato"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Consenti Le Varianti"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "Distinta base (Bom)"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "Ordinato"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "In Produzione"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr "Pianificato per la produzione"

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Scorte esterne"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Disponibilità in magazzino"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Disponibili scorte alternative"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Disponibili varianti delle scorte"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "In attesa"

#: build/status_codes.py:12
msgid "Production"
msgstr "Produzione"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "In Attesa"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Annullato"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Completo"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Giacenza richiesta per l'ordine di produzione"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr "L'ordine di produzione {build} richiede articoli aggiuntivi"

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Ordine di produzione in ritardo"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "L'ordine di produzione {bo} è in ritardo"

#: common/api.py:688
msgid "Is Link"
msgstr "È Un Connegamento"

#: common/api.py:696
msgid "Is File"
msgstr "E' un file"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "L'utente non ha il permesso di eliminare questi allegati"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "L'utente non ha il permesso di eliminare questo allegato"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Codice valuta non valido"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Codice valuta duplicato"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Nessun codice valuta valido fornito"

#: common/currency.py:146
msgid "No plugin"
msgstr "Nessun plugin"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Aggiornato"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Orario dell'ultimo aggiornamento"

#: common/models.py:138
msgid "Update By"
msgstr "Aggiornato da"

#: common/models.py:139
msgid "User who last updated this object"
msgstr "Utente che per ultimo ha aggiornato questo oggetto"

#: common/models.py:164
msgid "Unique project code"
msgstr "Codice unico del progetto"

#: common/models.py:171
msgid "Project description"
msgstr "Descrizione del progetto"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Utente o gruppo responsabile di questo progetto"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "Tasto impostazioni"

#: common/models.py:780
msgid "Settings value"
msgstr "Valore impostazioni"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "Il valore specificato non è un opzione valida"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Il valore deve essere un valore booleano"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Il valore deve essere un intero"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr "Il valore deve essere un numero valido"

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr "Il valore non supera i controlli di convalida"

#: common/models.py:914
msgid "Key string must be unique"
msgstr "La stringa chiave deve essere univoca"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Utente"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Quantità prezzo limite"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Prezzo"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Prezzo unitario in quantità specificata"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Scadenza"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Scadenza in cui questa notifica viene ricevuta"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Nome per questa notifica"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Attivo"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "È questa notifica attiva"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Token"

#: common/models.py:1437
msgid "Token for access"
msgstr "Token per l'accesso"

#: common/models.py:1445
msgid "Secret"
msgstr "Segreto"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Segreto condiviso per HMAC"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "ID Messaggio"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Identificatore unico per questo messaggio"

#: common/models.py:1563
msgid "Host"
msgstr "Host"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Host da cui questo messaggio è stato ricevuto"

#: common/models.py:1572
msgid "Header"
msgstr "Intestazione"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Intestazione di questo messaggio"

#: common/models.py:1580
msgid "Body"
msgstr "Contenuto"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Contenuto di questo messaggio"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Scadenza in cui questo messaggio è stato ricevuto"

#: common/models.py:1596
msgid "Worked on"
msgstr "Lavorato il"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Il lavoro su questo messaggio è terminato?"

#: common/models.py:1723
msgid "Id"
msgstr "Id"

#: common/models.py:1725
msgid "Title"
msgstr "Titolo"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Collegamento"

#: common/models.py:1729
msgid "Published"
msgstr "Pubblicato"

#: common/models.py:1731
msgid "Author"
msgstr "Autore"

#: common/models.py:1733
msgid "Summary"
msgstr "Riepilogo"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Letto"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Queste notizie sull'elemento sono state lette?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Immagine"

#: common/models.py:1753
msgid "Image file"
msgstr "File immagine"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "Tipo di modello di destinazione per questa immagine"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "ID modello di destinazione per questa immagine"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Unità Personalizzata"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "Il simbolo dell'unità deve essere univoco"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Il nome dell'unità deve essere un identificatore valido"

#: common/models.py:1843
msgid "Unit name"
msgstr "Nome dell'unità"

#: common/models.py:1850
msgid "Symbol"
msgstr "Simbolo"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Simbolo unità opzionale"

#: common/models.py:1857
msgid "Definition"
msgstr "Definizione"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Definizione unità"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Allegato"

#: common/models.py:1933
msgid "Missing file"
msgstr "File mancante"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Link esterno mancante"

#: common/models.py:1971
msgid "Model type"
msgstr "Tipo modello"

#: common/models.py:1972
msgid "Target model type for image"
msgstr "Tipo di modello di destinazione per l'immagine"

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Seleziona file da allegare"

#: common/models.py:1996
msgid "Comment"
msgstr "Commento"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Commento allegato"

#: common/models.py:2013
msgid "Upload date"
msgstr "Data caricamento"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Data di caricamento del file"

#: common/models.py:2018
msgid "File size"
msgstr "Dimensione file"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Dimensioni file in byte"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Tipo di modello specificato per l'allegato non valido"

#: common/models.py:2077
msgid "Custom State"
msgstr "Stato Personalizzato"

#: common/models.py:2078
msgid "Custom States"
msgstr "Stati Personalizzati"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "Imposta Stato Di Riferimento"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "Set di stato esteso con questo stato personalizzato"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Chiave Logica"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Chiave logica dello stato che è uguale a questo stato personalizzato nella logica commerciale"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Valore"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr "Valore numerico che verrà salvato nel database dei modelli"

#: common/models.py:2102
msgid "Name of the state"
msgstr "Nome dello Stato"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Etichetta"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "Etichetta che verrà visualizzata nel frontend"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Colore"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "Colore che verrà visualizzato nel frontend"

#: common/models.py:2128
msgid "Model"
msgstr "Modello"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "Modello a cui questo stato è associato"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "Il modello deve essere selezionato"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "La chiave deve essere selezionata"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "La chiave logica deve essere selezionata"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "La chiave deve essere diversa dalla chiave logica"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr "Deve essere fornita una classe di stato di riferimento valida"

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "La chiave deve essere diversa dalle chiavi logiche dello stato di riferimento"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "La chiave logica deve essere nelle chiavi logiche dello stato di riferimento"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "Il nome deve essere diverso dai nomi dello stato di riferimento"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Elenco Selezioni"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "Elenchi di Selezione"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "Nome dell'elenco di selezione"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "Descrizione della lista di selezione"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Bloccato"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "Questa lista di selezione è bloccata?"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "Questo elenco di selezione può essere utilizzato?"

#: common/models.py:2255
msgid "Source Plugin"
msgstr "Plugin Sorgente"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr "Plugin che fornisce l'elenco di selezione"

#: common/models.py:2261
msgid "Source String"
msgstr "Stringa Sorgente"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr "Stringa opzionale che identifica il sorgente usato per questa lista"

#: common/models.py:2271
msgid "Default Entry"
msgstr "Voce Predefinita"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr "Voce predefinita per questo elenco di selezione"

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Creato"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr "Data e ora in cui è stato creato l'elenco di selezione"

#: common/models.py:2283
msgid "Last Updated"
msgstr "Ultimo aggiornamento"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr "Data e ora in cui l'elenco di selezione è stato aggiornato"

#: common/models.py:2318
msgid "Selection List Entry"
msgstr "Voce Lista Selezione"

#: common/models.py:2319
msgid "Selection List Entries"
msgstr "Voci Lista Selezione"

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr "Elenco di selezione a cui appartiene questa voce"

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr "Valore della voce della lista di selezione"

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr "Etichetta per la voce elenco di selezione"

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr "Descrizione della voce della lista di selezione"

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr "Questa voce della lista di selezione è attiva?"

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "Scansione Codice A Barre"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Dati"

#: common/models.py:2377
msgid "Barcode data"
msgstr "Dati del Codice a Barre"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "Utente che ha scannerizzato il codice a barre"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Data e ora"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "Data e ora della scansione del codice a barre"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "Endpoint URL che ha elaborato il codice a barre"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Contesto"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "Dati contestuali per la scansione del codice a barre"

#: common/models.py:2415
msgid "Response"
msgstr "Risposta"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "Dati di risposta dalla scansione del codice a barre"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Risultato"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "La scansione del codice a barre è riuscita?"

#: common/models.py:2505
msgid "An error occurred"
msgstr "Si è verificato un errore"

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr "Messaggio email"

#: common/models.py:2574
msgid "Email Messages"
msgstr "Messaggi email"

#: common/models.py:2581
msgid "Announced"
msgstr "Annunciato"

#: common/models.py:2583
msgid "Sent"
msgstr "Inviato"

#: common/models.py:2584
msgid "Failed"
msgstr "Fallito"

#: common/models.py:2587
msgid "Delivered"
msgstr "Consegnato"

#: common/models.py:2595
msgid "Confirmed"
msgstr "Confermato"

#: common/models.py:2601
msgid "Inbound"
msgstr "Ricevuti"

#: common/models.py:2602
msgid "Outbound"
msgstr "In uscita"

#: common/models.py:2607
msgid "No Reply"
msgstr "Nessuna risposta"

#: common/models.py:2608
msgid "Track Delivery"
msgstr "Traccia La Consegna"

#: common/models.py:2609
msgid "Track Read"
msgstr "Conferma di lettura"

#: common/models.py:2610
msgid "Track Click"
msgstr "Tracciare i clic delle email"

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr "ID Globale"

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr "Identificatore per questo messaggio (potrebbe essere fornito da un sistema esterno)"

#: common/models.py:2633
msgid "Thread ID"
msgstr "ID discussione"

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr "Identificatore per questo thread del messaggio (potrebbe essere fornito da un sistema esterno)"

#: common/models.py:2644
msgid "Thread"
msgstr "Discussione"

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr "Thread collegato a questo messaggio"

#: common/models.py:2661
msgid "Prioriy"
msgstr "Priorità"

#: common/models.py:2703
msgid "Email Thread"
msgstr "Discussione Email"

#: common/models.py:2704
msgid "Email Threads"
msgstr "Discussioni Email"

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Chiave"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr "Chiave univoca per questa discussione (usata per identificare la discussione)"

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr "Identificatore univoco per questa discussione"

#: common/models.py:2724
msgid "Started Internal"
msgstr "Avviato internamente"

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr "Questa discussione è iniziata internamente?"

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr "Data e ora in cui la discussione è stata creata"

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr "Data e ora in cui la discussione è stata aggiornata"

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nuovo {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Un nuovo ordine è stato creato e assegnato a te"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} cancellato"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Un ordine assegnato a te è stato annullato"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Elemento ricevuto"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Gli elementi sono stati ricevuti a fronte di un ordine di acquisto"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Gli articoli sono stati ricevuti contro un ordine di reso"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr "Indica se l'impostazione è sovrascritta da una variabile ambiente"

#: common/serializers.py:147
msgid "Override"
msgstr "Sovrascrivi"

#: common/serializers.py:486
msgid "Is Running"
msgstr "In Esecuzione"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Attività in sospeso"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Attività pianificate"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Attività Fallite"

#: common/serializers.py:519
msgid "Task ID"
msgstr "ID Attività"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "ID attività univoco"

#: common/serializers.py:521
msgid "Lock"
msgstr "Blocco"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Tempo di blocco"

#: common/serializers.py:523
msgid "Task name"
msgstr "Nome attività"

#: common/serializers.py:525
msgid "Function"
msgstr "Funzione"

#: common/serializers.py:525
msgid "Function name"
msgstr "Nome della funzione"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Argomenti"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Argomenti attività"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Argomenti Parole Chiave"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Argomenti parole chiave attività"

#: common/serializers.py:640
msgid "Filename"
msgstr "Nome del file"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Tipo di modello"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "L'utente non ha il permesso di creare o modificare allegati per questo modello"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr "Lista di selezione bloccata"

#: common/setting/system.py:97
msgid "No group"
msgstr "Nessun gruppo"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "L'URL del sito è bloccato dalla configurazione"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Riavvio richiesto"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "È stata modificata un'impostazione che richiede un riavvio del server"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Migrazioni in sospeso"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Numero di migrazioni del database in sospeso"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr "Codici di avviso attivi"

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr "Un dizionario di codici di avviso attivi"

#: common/setting/system.py:193
msgid "Instance ID"
msgstr "ID istanza"

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr "Identificatore unico per questa istanza InvenTree"

#: common/setting/system.py:199
msgid "Announce ID"
msgstr "Annuncio ID"

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr "Annuncia l'ID dell'istanza del server nelle informazioni sullo stato del server (non autenticato)"

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Nome Istanza Del Server"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Descrittore stringa per l'istanza del server"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Utilizza nome istanza"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Usa il nome dell'istanza nella barra del titolo"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Limita visualizzazione `Informazioni`"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Mostra la modalità `Informazioni` solo ai superusers"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Nome azienda"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Nome interno dell'azienda"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "URL Base"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "URL di base per l'istanza del server"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Valuta predefinita"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Selezionare la valuta di base per i calcoli dei prezzi"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Valute Supportate"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Elenco dei codici valuta supportati"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Intervallo Aggiornamento Valuta"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Quanto spesso aggiornare i tassi di cambio (impostare a zero per disabilitare)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "giorni"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Plugin di aggiornamento della valuta"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Plugin di aggiornamento valuta da usare"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Scarica dall'URL"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Consenti il download di immagini e file remoti da URL esterno"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Limite Dimensione Download"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Dimensione massima consentita per il download dell'immagine remota"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "User-agent utilizzato per scaricare dall'URL"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Consenti di sovrascrivere l'user-agent utilizzato per scaricare immagini e file da URL esterno (lasciare vuoto per il predefinito)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Convalida URL rigoroso"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Richiede specifico schema quando si convalidano gli URL"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Aggiorna intervallo di controllo"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Quanto spesso controllare gli aggiornamenti (impostare a zero per disabilitare)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Backup automatico"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Abilita il backup automatico di database e file multimediali"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Intervallo Di Backup Automatico"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Definisci i giorni intercorrenti tra un backup automatico e l'altro"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Intervallo Eliminazione Attività"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "I risultati delle attività in background verranno eliminati dopo un determinato numero di giorni"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Intervallo Di Cancellazione Registro Errori"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "I log di errore verranno eliminati dopo il numero specificato di giorni"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Intervallo Di Cancellazione Notifica"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Le notifiche dell'utente verranno eliminate dopo il numero di giorni specificato"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr "Intervallo Eliminazione Email"

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr "I messaggi e-mail verranno eliminati dopo il numero specificato di giorni"

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Supporto Codice A Barre"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Abilita il supporto per lo scanner di codice a barre nell'interfaccia web"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "Memorizza Risultati Barcode"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "Memorizza i risultati della scansione del codice a barre nel database"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "Numero Massimo Scansioni Barcode"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "Numero massimo di risultati della scansione del codice a barre da memorizzare"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Codice a barre inserito scaduto"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Tempo di ritardo di elaborazione codice a barre"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Codice a Barre Supporto Webcam"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Consenti la scansione del codice a barre tramite webcam nel browser"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Visualizza dati codice a barre"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Visualizza i dati del codice a barre nel browser come testo"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "Plugin Generazione Codice A Barre"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "Plugin da usare per la generazione interna di codice a barre"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Revisioni Articolo"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Abilita il campo revisione per l'articolo"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "Solo revisione assemblaggio"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "Consenti revisioni solo per articoli di assemblaggio"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Consenti l'eliminazione dall'assemblaggio"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Permetti l'eliminazione degli articoli che sono usati in un assemblaggio"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN Regex"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Schema di espressione regolare per l'articolo corrispondente IPN"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Consenti duplicati IPN"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Permetti a più articoli di condividere lo stesso IPN"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Permetti modifiche al part number interno (IPN)"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Consenti di modificare il valore del part number durante la modifica di un articolo"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Copia I Dati Della distinta base dell'articolo"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Copia i dati della Distinta Base predefinita quando duplichi un articolo"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Copia I Dati Parametro dell'articolo"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Copia i dati dei parametri di default quando si duplica un articolo"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Copia I Dati dell'Articolo Test"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Copia i dati di prova di default quando si duplica un articolo"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Copia Template Parametri Categoria"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Copia i modelli dei parametri categoria quando si crea un articolo"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Modello"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Gli articoli sono modelli per impostazione predefinita"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Gli articoli possono essere assemblate da altri componenti per impostazione predefinita"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Componente"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Gli articoli possono essere assemblati da altri componenti per impostazione predefinita"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Acquistabile"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Gli articoli sono acquistabili per impostazione predefinita"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Vendibile"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Gli articoli sono acquistabili per impostazione predefinita"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Gli articoli sono tracciabili per impostazione predefinita"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtuale"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Gli articoli sono virtuali per impostazione predefinita"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Mostra articoli correlati"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Visualizza parti correlate per ogni articolo"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Dati iniziali dello stock"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Consentire la creazione di uno stock iniziale quando si aggiunge una nuova parte"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Dati iniziali del fornitore"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Consentire la creazione dei dati iniziali del fornitore quando si aggiunge una nuova parte"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Formato di visualizzazione del nome articolo"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Formato per visualizzare il nome dell'articolo"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Icona predefinita Categoria Articolo"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Icona predefinita Categoria Articolo (vuoto significa nessuna icona)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Forza Unità Parametro"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Se le unità sono fornite, i valori dei parametri devono corrispondere alle unità specificate"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Prezzi Minimi Decimali"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Numero minimo di decimali da visualizzare quando si visualizzano i dati dei prezzi"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Prezzi Massimi Decimali"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Numero massimo di decimali da visualizzare quando si visualizzano i dati dei prezzi"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Usa Prezzi Fornitore"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Includere le discontinuità di prezzo del fornitore nei calcoli generali dei prezzi"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Ignora la Cronologia Acquisti"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Cronologia dei prezzi dell'ordine di acquisto del fornitore superati con discontinuità di prezzo"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Utilizzare i prezzi degli articoli in stock"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Utilizzare i prezzi dei dati di magazzino inseriti manualmente per il calcolo dei prezzi"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Età dei prezzi degli articoli in stock"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Escludere dal calcolo dei prezzi gli articoli in giacenza più vecchi di questo numero di giorni"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Utilizza Variazione di Prezzo"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Includi la variante dei prezzi nei calcoli dei prezzi complessivi"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Solo Varianti Attive"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Utilizza solo articoli di varianti attive per calcolare i prezzi delle varianti"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr "Aggiornamento Automatico Prezzi"

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr "Aggiorna automaticamente il prezzo degli articoli quando i dati interni cambiano"

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Intervallo Di Ricostruzione Dei Prezzi"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Numero di giorni prima che il prezzo dell'articolo venga aggiornato automaticamente"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Prezzi interni"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Abilita prezzi interni per gli articoli"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Sovrascrivi Prezzo Interno"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Se disponibile, i prezzi interni sostituiscono i calcoli della fascia di prezzo"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Abilita stampa etichette"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Abilita la stampa di etichette dall'interfaccia web"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "Etichetta Immagine DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Risoluzione DPI quando si generano file di immagine da fornire ai plugin di stampa per etichette"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Abilita Report di Stampa"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Abilita generazione di report di stampa"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Modalità Debug"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Genera report in modalità debug (output HTML)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Registro errori"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Errori di log che si verificano durante la generazione dei report"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Dimensioni pagina"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Dimensione predefinita della pagina per i report PDF"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Seriali Unici Globali"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "I numeri di serie per gli articoli di magazzino devono essere univoci"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Elimina scorte esaurite"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Determina il comportamento predefinito quando un articolo a magazzino è esaurito"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Modello Codice a Barre"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Modello per la generazione di codici batch predefiniti per gli elementi stock"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Scadenza giacenza"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Abilita funzionalità di scadenza della giacenza"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Vendi giacenza scaduta"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Consenti la vendita di stock scaduti"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Tempo di Scorta del Magazzino"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Numero di giorni in cui gli articoli in magazzino sono considerati obsoleti prima della scadenza"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Crea giacenza scaduta"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Permetti produzione con stock scaduto"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Controllo della proprietà della giacenza"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Abilita il controllo della proprietà sulle posizioni e gli oggetti in giacenza"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Icona Predefinita Ubicazione di Magazzino"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Icona Predefinita Ubicazione di Magazzino (vuoto significa nessuna icona)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Mostra articoli a magazzino installati"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Visualizza gli articoli a magazzino installati nelle tabelle magazzino"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Verificare la distinta base durante l'installazione degli articoli"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Gli articoli di magazzino installati devono esistere nella distinta base per l'articolo principale"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Consenti trasferimento magazzino esaurito"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Consenti il trasferimento di articoli non disponibili a magazzino tra le diverse ubicazioni di magazzino"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Modello Di Riferimento Ordine Di Produzione"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di produzione"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "È richiesto il Proprietario Responsabile"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "A ogni ordine deve essere assegnato un proprietario responsabile"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "Richiede Articolo Attivo"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "Impedisci la creazione di ordini di produzione per gli articolo inattivi"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "Richiede Articolo Bloccato"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "Impedisci la creazione di ordini di costruzione per le parti sbloccate"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "Richiede un BOM valido"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Previene la creazione di ordini di costruzione a meno che BOM non sia stato convalidato"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "Richiedi Ordini Dei Figli Chiusi"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "Impedisci il completamento dell'ordine di costruzione fino alla chiusura di tutti gli ordini figli"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr "Ordini di Produzione Esterni"

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr "Abilita funzionalità ordini di produzione esterni"

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Blocca Fino Al Passaggio Dei Test"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Impedisci che gli output di costruzione siano completati fino al superamento di tutti i test richiesti"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Abilita Ordini Di Reso"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Abilita la funzionalità ordine di reso nell'interfaccia utente"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Motivo di Riferimento per ordine di reso"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di reso"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Modifica Ordini Di Reso Completati"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Consenti la modifica degli ordini di reso dopo che sono stati completati"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Modello Di Riferimento Ordine Di Vendita"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di vendita"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Spedizione Predefinita Ordine Di Vendita"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Abilita la creazione di spedizioni predefinite con ordini di vendita"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Modifica Ordini Di Vendita Completati"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Consenti la modifica degli ordini di vendita dopo che sono stati spediti o completati"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Segna gli ordini spediti come completati"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Gli ordini di vendita contrassegnati come spediti saranno automaticamente completati, bypassando lo stato \"spedito\""

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Modello di Riferimento Ordine D'Acquisto"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di acquisto"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Modifica Ordini Di Acquisto Completati"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Consenti la modifica degli ordini di acquisto dopo che sono stati spediti o completati"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr "Converti Valuta"

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr "Converti il valore dell'elemento in valuta base quando si riceve lo stock"

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Completa Automaticamente Gli Ordini D'Acquisto"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Contrassegna automaticamente gli ordini di acquisto come completi quando tutti gli elementi della riga sono ricevuti"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Abilita password dimenticata"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Abilita la funzione password dimenticata nelle pagine di accesso"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Abilita registrazione"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Abilita auto-registrazione per gli utenti nelle pagine di accesso"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "SSO abilitato"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "Abilita SSO nelle pagine di accesso"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "Abilita registrazione SSO"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Abilita l'auto-registrazione tramite SSO per gli utenti nelle pagine di accesso"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "Abilita sincronizzazione dei gruppi SSO"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Abilita la sincronizzazione dei gruppi InvenTree con i gruppi forniti dall'IdP"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "Chiave gruppo SSO"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "Il nome dell'attributo di richiesta di gruppi fornito dall'IdP"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "Mappa del gruppo SSO"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Una mappatura dai gruppi SSO ai gruppi InvenTree locali. Se il gruppo locale non esiste, verrà creato."

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "Rimuovere i gruppi al di fuori dell'SSO"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Indica se i gruppi assegnati all'utente debbano essere rimossi se non sono backend dall'IdP. La disattivazione di questa impostazione potrebbe causare problemi di sicurezza"

#: common/setting/system.py:959
msgid "Email required"
msgstr "Email richiesta"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Richiedi all'utente di fornire una email al momento dell'iscrizione"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "Riempimento automatico degli utenti SSO"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Compila automaticamente i dettagli dell'utente dai dati dell'account SSO"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "Posta due volte"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Al momento della registrazione chiedere due volte all'utente l'indirizzo di posta elettronica"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Password due volte"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Al momento della registrazione chiedere agli utenti due volte l'inserimento della password"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Domini consentiti"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Limita la registrazione a determinati domini (separati da virgola, a partire da @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Gruppo iscrizione"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Gruppo a cui i nuovi utenti sono assegnati alla registrazione. Se la sincronizzazione di gruppo SSO è abilitata, questo gruppo è impostato solo se nessun gruppo può essere assegnato dall'IdP."

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "Applica MFA"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Gli utenti devono utilizzare la sicurezza a due fattori."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Controlla i plugin all'avvio"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Controlla che tutti i plugin siano installati all'avvio - abilita in ambienti contenitore"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Controlla gli aggiornamenti dei plugin"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Abilita controlli periodici per gli aggiornamenti dei plugin installati"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "Abilita l'integrazione URL"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Attiva plugin per aggiungere percorsi URL"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Attiva integrazione navigazione"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Abilita i plugin per l'integrazione nella navigazione"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "Abilita l'app integrata"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Abilita plugin per aggiungere applicazioni"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Abilita integrazione pianificazione"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Abilita i plugin per eseguire le attività pianificate"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Abilita eventi integrati"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Abilita plugin per rispondere agli eventi interni"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "Abilita integrazione interfaccia"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "Abilita i plugin per l'integrazione nell'interfaccia utente"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr "Abilita integrazione email"

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr "Abilita i plugin per elaborare le email in uscita/in arrivo"

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr "Abilita codici progetto"

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr "Abilita i codici del progetto per tracciare i progetti"

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr "Abilita Cronologia Magazzino"

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr "Abilita la funzionalità per registrare i livelli storici e il valore del magazzino"

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Escludi Posizioni Esterne"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Inventario periodico automatico"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Visualizza i nomi completi degli utenti"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Mostra nomi completi degli utenti invece che nomi utente"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr "Visualizza Profili Utente"

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr "Visualizza i profili degli utenti sulla pagina del loro profilo"

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Abilita Dati Stazione Di Prova"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Abilita la raccolta dati della stazione di prova per i risultati del test"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Visualizzazione dell'etichetta in linea"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Visualizza le etichette PDF nel browser, invece di scaricare come file"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Stampante per etichette predefinita"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Configura quale stampante di etichette deve essere selezionata per impostazione predefinita"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Visualizzazione dell'etichetta in linea"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Visualizza le etichette PDF nel browser, invece di scaricare come file"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Cerca Articoli"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Mostra articoli della ricerca nella finestra di anteprima"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Trova Articoli del Fornitore"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Mostra articoli del fornitore nella finestra di anteprima"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Cerca Articoli Produttore"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Mostra articoli del produttore nella finestra di anteprima"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Nascondi Articoli Inattivi"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Escludi articoli inattivi dalla finestra di anteprima della ricerca"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Cerca Categorie"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Mostra categorie articolo nella finestra di anteprima di ricerca"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Cerca Giacenze"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Mostra articoli in giacenza nella finestra di anteprima della ricerca"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Nascondi elementi non disponibili"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Escludi gli elementi stock che non sono disponibili dalla finestra di anteprima di ricerca"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Cerca Ubicazioni"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Mostra ubicazioni delle giacenze nella finestra di anteprima di ricerca"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Cerca Aziende"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Mostra le aziende nella finestra di anteprima di ricerca"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Cerca Ordini Di Produzione"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Mostra gli ordini di produzione nella finestra di anteprima di ricerca"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Cerca Ordini di Acquisto"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Mostra gli ordini di acquisto nella finestra di anteprima di ricerca"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Escludi Ordini D'Acquisto Inattivi"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Escludi ordini di acquisto inattivi dalla finestra di anteprima di ricerca"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Cerca Ordini Di Vendita"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Visualizzazione degli ordini di vendita nella finestra di anteprima della ricerca"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Escludi Ordini Di Vendita Inattivi"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Escludi ordini di vendita inattivi dalla finestra di anteprima di ricerca"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Ricerca Spedizione Ordine Di Vendita"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Mostra le spedizioni di ordini di vendita nella finestra di anteprima di ricerca"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Cerca Ordini Di Reso"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Visualizza gli ordini di reso nella finestra di anteprima di ricerca"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Escludi Ordini Inattivi Di Reso"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Escludi ordini di reso inattivi dalla finestra di anteprima di ricerca"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Risultati Dell'Anteprima Di Ricerca"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Numero di risultati da visualizzare in ciascuna sezione della finestra di anteprima della ricerca"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Ricerca con regex"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Abilita le espressioni regolari nelle query di ricerca"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Ricerca Parole Intere"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Le query di ricerca restituiscono i risultati per intere corrispondenze di parola"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Ricerca Note"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "Le query di ricerca restituiscono i risultati per le corrispondenze dalle note dell'elemento"

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Il tasto Esc chiude i moduli"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Utilizzare il tasto Esc per chiudere i moduli modali"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Barra di navigazione fissa"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "La posizione della barra di navigazione è fissata nella parte superiore dello schermo"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr "Icone per la Navigazione"

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr "Visualizza le icone nella barra di navigazione"

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Formato Data"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Formato predefinito per visualizzare le date"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr "Mostra L'Ultimo Breadcrumb"

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr "Mostra la pagina corrente nel Breadcrumb"

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr "Mostra tutte lo posizioni del magazzino nelle tabelle"

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr "Disabilitato: Mostra il percorso completo al passaggio del mouse. Abilitato: Mostra il percorso completo come testo."

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr "Mostra tutte le categorie degli articoli nelle tabelle"

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr "Disabilitato: Mostra il percorso completo delle categorie al passaggio del mouse. Abilitato: Mostra il percorso completo delle categorie come testo."

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Ricevi segnalazioni di errore"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Ricevi notifiche per errori di sistema"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Ultime stampanti usate"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Salva le ultime stampanti usate da un'utente"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Nessun tipo di modello allegato fornito"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Tipo di modello allegato non valido"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "I posti minimi non possono essere superiori al massimo"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Il numero massimo di posti non può essere inferiore al minimo"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Un dominio vuoto non è consentito."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Nome dominio non valido: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "Il valore deve essere maiuscolo"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "Il valore deve essere un identificatore variabile valido"

#: company/api.py:141
msgid "Part is Active"
msgstr "L'articolo è attivo"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Il produttore è attivo"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "L'articolo fornitore è attivo"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "L'articolo interno è attivo"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Il fornitore è attivo"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Produttore"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Azienda"

#: company/api.py:316
msgid "Has Stock"
msgstr "Ha Scorte"

#: company/models.py:120
msgid "Companies"
msgstr "Aziende"

#: company/models.py:148
msgid "Company description"
msgstr "Descrizione azienda"

#: company/models.py:149
msgid "Description of the company"
msgstr "Descrizione dell'azienda"

#: company/models.py:155
msgid "Website"
msgstr "Sito Web"

#: company/models.py:156
msgid "Company website URL"
msgstr "Sito web aziendale"

#: company/models.py:162
msgid "Phone number"
msgstr "Telefono"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Numero di telefono di contatto"

#: company/models.py:171
msgid "Contact email address"
msgstr "Indirizzo email"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Contatto"

#: company/models.py:178
msgid "Point of contact"
msgstr "Punto di contatto"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Collegamento alle informazioni aziendali esterne"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Questa azienda è attiva?"

#: company/models.py:203
msgid "Is customer"
msgstr "È un cliente"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Vendi oggetti a questa azienda?"

#: company/models.py:209
msgid "Is supplier"
msgstr "È un fornitore"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Acquistate articoli da questa azienda?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "È un produttore"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Questa azienda produce articoli?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Valuta predefinita utilizzata per questa azienda"

#: company/models.py:231
msgid "Tax ID"
msgstr "Partita IVA"

#: company/models.py:232
msgid "Company Tax ID"
msgstr "Codice Fiscale Azienda"

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Indirizzo"

#: company/models.py:355
msgid "Addresses"
msgstr "Indirizzi"

#: company/models.py:412
msgid "Select company"
msgstr "Seleziona azienda"

#: company/models.py:417
msgid "Address title"
msgstr "Titolo indirizzo"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Titolo che descrive la voce indirizzo"

#: company/models.py:424
msgid "Primary address"
msgstr "Indirizzo Principale"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Imposta come indirizzo primario"

#: company/models.py:430
msgid "Line 1"
msgstr "Linea 1"

#: company/models.py:431
msgid "Address line 1"
msgstr "Indirizzo (linea 1)"

#: company/models.py:437
msgid "Line 2"
msgstr "Linea 2"

#: company/models.py:438
msgid "Address line 2"
msgstr "Indirizzo (linea 2)"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "CAP"

#: company/models.py:451
msgid "City/Region"
msgstr "Città/Regione"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Codice postale città/regione"

#: company/models.py:458
msgid "State/Province"
msgstr "Stato/Provincia"

#: company/models.py:459
msgid "State or province"
msgstr "Stato o provincia"

#: company/models.py:465
msgid "Country"
msgstr "Nazione"

#: company/models.py:466
msgid "Address country"
msgstr "Indirizzo Paese"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Note di spedizione del corriere"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Note per il corriere di spedizione"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Note di spedizione interne"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Note di spedizione per uso interno"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Collegamento alle informazioni sull'indirizzo (esterno)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Codice articolo produttore"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Articolo di base"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Seleziona articolo"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Seleziona Produttore"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "Codice articolo produttore (MPN)"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Codice articolo produttore"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "URL dell'articolo del fornitore"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Descrizione articolo costruttore"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Parametro articolo produttore"

#: company/models.py:635
msgid "Parameter name"
msgstr "Nome parametro"

#: company/models.py:642
msgid "Parameter value"
msgstr "Valore del parametro"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Unità"

#: company/models.py:650
msgid "Parameter units"
msgstr "Unità parametri"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Le unità del pacchetto devono essere compatibili con le unità dell'articolo base"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Le unità del pacchetto devono essere maggiori di zero"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "L'articolo del costruttore collegato deve riferirsi alla stesso articolo"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Fornitore"

#: company/models.py:829
msgid "Select supplier"
msgstr "Seleziona fornitore"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Unità di giacenza magazzino fornitore"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Questo articolo fornitore è attivo?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Selezionare un produttore"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "URL dell'articolo del fornitore"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Descrizione articolo fornitore"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Nota"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "costo base"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Onere minimo (ad esempio tassa di stoccaggio)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Confezionamento"

#: company/models.py:892
msgid "Part packaging"
msgstr "Imballaggio del pezzo"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Quantità Confezione"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Quantità totale fornita in una singola confezione. Lasciare vuoto per gli articoli singoli."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "multiplo"

#: company/models.py:919
msgid "Order multiple"
msgstr "Ordine multiplo"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Quantità disponibile dal fornitore"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Disponibilità Aggiornata"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Data dell’ultimo aggiornamento dei dati sulla disponibilità"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Sconto Prezzo Fornitore"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr "Restituisce la rappresentazione stringa per l'indirizzo primario. Questa proprietà esiste per la compatibilità all'indietro."

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Valuta predefinita utilizzata per questo fornitore"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Nome Azienda"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "In magazzino"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr "Errore durante l'esportazione dei dati"

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr "Il plugin di esportazione dati ha restituito un formato di dati errato"

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Formato di esportazione"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "Seleziona il formato del file di esportazione"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Esporta Plugin"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "Seleziona plugin di esportazione"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Ulteriori informazioni sullo stato di questo articolo"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Chiave di stato personalizzata"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Personalizzato"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Classe"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Valori"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Inviato"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Codice di stato non valido"

#: importer/models.py:73
msgid "Data File"
msgstr "File dati"

#: importer/models.py:74
msgid "Data file to import"
msgstr "File dati da importare"

#: importer/models.py:83
msgid "Columns"
msgstr "Colonne"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr "Tipo di modello di destinazione per questa sessione di importazione"

#: importer/models.py:96
msgid "Import status"
msgstr "Stato importazione"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Valori predefiniti del campo"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "Sostituisci campo"

#: importer/models.py:120
msgid "Field Filters"
msgstr "Filtri campo"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "Alcuni campi richiesti non sono stati mappati"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "La colonna è già mappata a un campo del database"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "Il campo è già mappato a una colonna di dati"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "La mappatura delle colonne deve essere collegata a una sessione di importazione valida"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "La colonna non esiste nel file dati"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "Il campo non esiste nel modello di destinazione"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "Il campo selezionato è di sola lettura"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "Sessione d'importazione"

#: importer/models.py:471
msgid "Field"
msgstr "Campo"

#: importer/models.py:473
msgid "Column"
msgstr "Colonna"

#: importer/models.py:542
msgid "Row Index"
msgstr "Indice riga"

#: importer/models.py:545
msgid "Original row data"
msgstr "Dati riga originali"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Errori"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Valido"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Formato file dati non supportato"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Apertura del file dati non riuscita"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Dimensioni file dati non valida"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "Campi predefiniti non validi"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "Sostituzione campo non valida"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "Filtri campo non validi"

#: importer/serializers.py:177
msgid "Rows"
msgstr "Righe"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Elenco degli ID delle righe da accettare"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Nessuna riga fornita"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "La riga non appartiene a questa sessione"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "La riga contiene dati non validi"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "La riga è già stata completata"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Inizializzazione..."

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Mappatura Colonne"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Importazione dei dati"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Elaborazione dei dati"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Il file di dati supera il limite di dimensione massima"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Il file dati non contiene intestazioni"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Il file dati contiene troppe colonne"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Il file di dati contiene troppe righe"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Il valore deve essere un oggetto dizionario valido"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Copie"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Numero di copie da stampare per ogni etichetta"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Connesso"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Sconosciuto"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Stampando"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Nessun file multimediale"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Inceppamento della carta"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Disconnesso"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Stampante Etichetta"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Stampa direttamente etichette per vari articoli."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Posizione Della Stampante"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Ambito della stampante a una posizione specifica"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Nome della macchina"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Tipo di macchina"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Tipo di macchina"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Driver"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Driver utilizzato per la macchina"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Le macchine possono essere disabilitate"

#: machine/models.py:95
msgid "Driver available"
msgstr "Driver disponibile"

#: machine/models.py:100
msgid "No errors"
msgstr "Nessun errore"

#: machine/models.py:105
msgid "Initialized"
msgstr "Inizializzato"

#: machine/models.py:117
msgid "Machine status"
msgstr "Stato della Macchina"

#: machine/models.py:145
msgid "Machine"
msgstr "Macchina"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Configurazione Macchina"

#: machine/models.py:162
msgid "Config type"
msgstr "Tipo di configurazione"

#: order/api.py:121
msgid "Order Reference"
msgstr "Riferimento ordine"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "In Sospeso"

#: order/api.py:165
msgid "Has Project Code"
msgstr "Ha il codice del progetto"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Creato Da"

#: order/api.py:183
msgid "Created Before"
msgstr "Creato prima"

#: order/api.py:187
msgid "Created After"
msgstr "Creato dopo"

#: order/api.py:191
msgid "Has Start Date"
msgstr "Ha data d'inizio"

#: order/api.py:199
msgid "Start Date Before"
msgstr "Data d'inizio prima"

#: order/api.py:203
msgid "Start Date After"
msgstr "Data d'inizio dopo"

#: order/api.py:207
msgid "Has Target Date"
msgstr "Ha data di fine"

#: order/api.py:215
msgid "Target Date Before"
msgstr "Data obiettivo prima"

#: order/api.py:219
msgid "Target Date After"
msgstr "Data obiettivo dopo"

#: order/api.py:270
msgid "Has Pricing"
msgstr "Prezzo Articolo"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr "Completato prima"

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "Completato dopo"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr "Ordine di Produzione Esterno"

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Ordine"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "Ordine completato"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Articolo interno"

#: order/api.py:578
msgid "Order Pending"
msgstr "Ordine in sospeso"

#: order/api.py:958
msgid "Completed"
msgstr "Completato"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "Ha Spedizione"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Ordine D'Acquisto"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Ordini di Vendita"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Restituisci ordine"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Prezzo Totale"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Prezzo totale dell'ordine"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Valuta ordine"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Valuta per questo ordine (lasciare vuoto per usare il valore predefinito dell'azienda)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr "Questo ordine è bloccato e non può essere modificato"

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "Il contatto non corrisponde all'azienda selezionata"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr "La data d'inizio deve essere precedente alla data di fine"

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Descrizione dell'ordine (opzionale)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Seleziona il codice del progetto per questo ordine"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Collegamento a un sito web esterno"

#: order/models.py:458
msgid "Start date"
msgstr "Data iniziale"

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr "Data d'inizio programmata per questo ordine"

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Data scadenza"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Data prevista per la consegna dell'ordine. L'ordine scadrà dopo questa data."

#: order/models.py:487
msgid "Issue Date"
msgstr "Data di emissione"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Data di emissione ordine"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Utente o gruppo responsabile di questo ordine"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Punto di contatto per questo ordine"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Indirizzo dell'azienda per questo ordine"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Riferimento ordine"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Stato"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Stato ordine d'acquisto"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Azienda da cui sono stati ordinati gli articoli"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Riferimento fornitore"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Codice di riferimento ordine fornitore"

#: order/models.py:654
msgid "received by"
msgstr "ricevuto da"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Data ordine completato"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Destinazione"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr "Destinazione per gli elementi ricevuti"

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Il fornitore dell'articolo deve corrispondere al fornitore dell'ordine di produzione"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "L'elemento di riga non corrisponde all'ordine di acquisto"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "La quantità deve essere un numero positivo"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Cliente"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Azienda da cui sono stati ordinati gli elementi"

#: order/models.py:1318
msgid "Sales order status"
msgstr "Stato ordine di vendita"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Riferimento Cliente "

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Codice di riferimento Ordine del Cliente"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Data di spedizione"

#: order/models.py:1343
msgid "shipped by"
msgstr "spedito da"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "L'ordine è già stato completato"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "L'ordine è già stato annullato"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Solo un ordine aperto può essere contrassegnato come completo"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "L'ordine non può essere completato in quanto ci sono spedizioni incomplete"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "L'ordine non può essere completato perché ci sono allocazioni incomplete"

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "L'ordine non può essere completato perché ci sono elementi di riga incompleti"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr "L'ordine è bloccato e non può essere modificato"

#: order/models.py:1711
msgid "Item quantity"
msgstr "Quantità Elementi"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Riferimento Linea Elemento"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Note linea elemento"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Data di destinazione per questa voce di riga (lasciare vuoto per utilizzare la data di destinazione dall'ordine)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Descrizione della parte (opzionale)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Contesto aggiuntivo per questa voce"

#: order/models.py:1788
msgid "Unit price"
msgstr "Prezzo unitario"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "Riga ordine d'acquisto"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "L'articolo del fornitore deve corrispondere al fornitore"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr "L'ordine di produzione deve essere contrassegnato come esterno"

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr "Gli ordini di costruzione possono essere collegati solo alle parti di assemblaggio"

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr "L'articolo dell'ordine di produzione deve corrispondere all'articolo della riga"

#: order/models.py:1884
msgid "Supplier part"
msgstr "Articolo Fornitore"

#: order/models.py:1891
msgid "Received"
msgstr "Ricevuto"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Numero di elementi ricevuti"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Prezzo di Acquisto"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Prezzo di acquisto unitario"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr "Ordine di produzione esterno che deve essere eseguito da questo articolo"

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "Riga Extra ordine di acquisto"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "Articolo ordine di vendita"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Un articolo virtuale non può essere assegnato ad un ordine di vendita"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Solo gli articoli vendibili possono essere assegnati a un ordine di vendita"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Prezzo di Vendita"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Prezzo unitario di vendita"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Spedito"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Quantità spedita"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "Spedizione dell'ordine di vendita"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Data di spedizione"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Data di consegna"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Data di consegna della spedizione"

#: order/models.py:2221
msgid "Checked By"
msgstr "Verificato Da"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Utente che ha controllato questa spedizione"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Spedizione"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Numero di spedizione"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Numero di monitoraggio"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Informazioni di monitoraggio della spedizione"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "Numero Fattura"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Numero di riferimento per la fattura associata"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "La spedizione è già stata spedita"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "La spedizione non ha articoli di stock assegnati"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "Riga Extra ordine di vendita"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "Assegnazione Ordini Di Vendita"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "L'elemento di magazzino non è stato assegnato"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Impossibile allocare l'elemento stock a una linea con un articolo diverso"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Impossibile allocare stock a una riga senza un articolo"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "La quantità di ripartizione non puo' superare la disponibilità della giacenza"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "La quantità deve essere 1 per l'elemento serializzato"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "L'ordine di vendita non corrisponde alla spedizione"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "La spedizione non corrisponde all'ordine di vendita"

#: order/models.py:2451
msgid "Line"
msgstr "Linea"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Riferimento della spedizione ordine di vendita"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Elemento"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Seleziona elemento stock da allocare"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Inserisci la quantità assegnata alla giacenza"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Riferimento ordine di reso"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Società a cui vengono restituiti gli articoli"

#: order/models.py:2625
msgid "Return order status"
msgstr "Stato ordine di reso"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "Articolo Linea Ordine Reso"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr "L'elemento stock deve essere specificato"

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr "Quantità di reso superiore alla quantità di scorta"

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr "La quantità di reso deve essere maggiore di zero"

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr "Quantità non valida per l'elemento stock serializzato"

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Seleziona l'elemento da restituire dal cliente"

#: order/models.py:2910
msgid "Received Date"
msgstr "Data di ricezione"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr ""

#: order/models.py:2923
msgid "Outcome"
msgstr "Risultati"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr ""

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr ""

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:90
msgid "Order ID"
msgstr "ID Ordine"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "ID dell'ordine da duplicare"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "Copia Linee"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr "Copia gli elementi di riga dall'ordine originale"

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr "Copia Linee Extra"

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr "Copia gli elementi di riga extra dall'ordine originale"

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Elementi Riga"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "Duplica Ordine"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr "Specifica le opzioni per duplicare questo ordine"

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "ID dell'ordine non corretto"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Nome Fornitore"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "L'ordine non può essere cancellato"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Consenti di chiudere l'ordine con elementi di riga incompleti"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "L'ordine ha elementi di riga incompleti"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "L'ordine non è aperto"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Calcola automaticamente il prezzo di acquisto in base ai dati del fornitore articolo"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Valuta prezzo d'acquisto"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Unisci elementi"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "Codice articolo"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Numero Dell'articolo Interno"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "Numero Articolo Interno"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "L'articolo del fornitore deve essere specificato"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "L'ordine di acquisto deve essere specificato"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "Il fornitore deve essere abbinato all'ordine d'acquisto"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "L'ordine di acquisto deve essere abbinato al fornitore"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Elemento Riga"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Seleziona la posizione di destinazione per gli elementi ricevuti"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Inserisci il codice univoco per gli articoli in arrivo"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Data di Scadenza"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr "Inserisci la data di scadenza per gli articoli in arrivo"

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Inserisci i numeri di serie per gli articoli stock in arrivo"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:826
msgid "Barcode"
msgstr "Codice a Barre"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Codice a barre scansionato"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Il codice a barre è già in uso"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Gli elementi di linea devono essere forniti"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "La destinazione deve essere specificata"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "I valori dei codici a barre forniti devono essere univoci"

#: order/serializers.py:1066
msgid "Shipments"
msgstr "Spedizioni"

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Spedizioni Completate"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Valuta prezzo di vendita"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr "Elementi Assegnati"

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Nessun dettaglio di spedizione fornito"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "L'elemento di riga non è associato a questo ordine"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "La quantità deve essere positiva"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Inserisci i numeri di serie da assegnare"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "La spedizione è già stata spedita"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "La spedizione non è associata con questo ordine"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Nessuna corrispondenza trovata per i seguenti numeri di serie"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "I seguenti numeri di serie non sono disponibili"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr ""

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr ""

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr ""

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr ""

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2136
msgid "Line price currency"
msgstr ""

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Perso"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Reso"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "In corso"

#: order/status_codes.py:105
msgid "Return"
msgstr "Indietro"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Riparare"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Sostituire"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Rimborso"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Rifiuta"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Ordine D'Acquisto in ritardo"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "L'ordine d'acquisto {po} è in ritardo"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Ordini Di Vendita in ritardo"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "L'ordine di vendita {so} è ora in ritardo"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr "Ordini di Reso in Ritardo"

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr "L'ordine di reso {ro} è ora in ritardo"

#: part/api.py:111
msgid "Starred"
msgstr "Preferiti"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "Filtra per categorie preferite"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Profondità"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "Filtra per profondità categoria"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "Livello principale"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "Filtra per categorie di primo livello"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "Cascata"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "Includi sottocategorie nei risultati filtrati"

#: part/api.py:185
msgid "Parent"
msgstr "Genitore"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Filtra per categoria genitore"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "Escludi sottocategorie sotto la categoria specificata"

#: part/api.py:434
msgid "Has Results"
msgstr "Ha Risultati"

#: part/api.py:660
msgid "Is Variant"
msgstr "È una Variante"

#: part/api.py:668
msgid "Is Revision"
msgstr "E' una revisione"

#: part/api.py:678
msgid "Has Revisions"
msgstr "Ha revisioni"

#: part/api.py:859
msgid "BOM Valid"
msgstr "BOM Valido"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "L'articolo assemblato è provabile"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "Il componente è provabile"

#: part/api.py:1576
msgid "Uses"
msgstr "Utilizzi"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Categoria Articoli"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Categorie Articolo"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Posizione Predefinita"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Posizione predefinita per gli articoli di questa categoria"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Strutturale"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Le parti non possono essere assegnate direttamente a una categoria strutturale, ma possono essere assegnate a categorie subordinate."

#: part/models.py:134
msgid "Default keywords"
msgstr "Keywords predefinite"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Parole chiave predefinite per gli articoli in questa categoria"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Icona"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Icona (facoltativa)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Non puoi rendere principale questa categoria di articoli perché alcuni articoli sono già assegnati!"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Articoli"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "Impossibile eliminare questo articolo perché è bloccato"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Impossibile eliminare questo articolo perché è ancora attivo"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Non è possibile eliminare questo articolo in quanto è utilizzato in una costruzione"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "L'articolo '{self}' non può essere usata nel BOM per '{parent}' (ricorsivo)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "L'articolo '{parent}' è usato nel BOM per '{self}' (ricorsivo)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN deve corrispondere al modello regex {pattern}"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "L'articolo non può essere una revisione di se stesso"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Non puoi fare la revisione di un articolo che è già una revisione"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "Il codice di revisione deve essere specificato"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "Le revisioni sono consentite solo per le parti di assemblaggio"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "Non è possibile effettuare la revisione di un articolo modello"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "L'articolo genitore deve puntare allo stesso modello"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Esiste già un elemento stock con questo numero seriale"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "Non è consentito duplicare IPN nelle impostazioni dell'articolo"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "La revisione dell'articolo duplicata esiste già."

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Un articolo con questo Nome, IPN e Revisione esiste già."

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Gli articoli non possono essere assegnati a categorie articolo principali!"

#: part/models.py:1051
msgid "Part name"
msgstr "Nome articolo"

#: part/models.py:1056
msgid "Is Template"
msgstr "È Template"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Quest'articolo è un articolo di template?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Questa parte è una variante di un altro articolo?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Variante Di"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Descrizione della parte (opzionale)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Parole Chiave"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Parole chiave per migliorare la visibilità nei risultati di ricerca"

#: part/models.py:1093
msgid "Part category"
msgstr "Categoria articolo"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN - Numero di riferimento interno"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Numero di revisione o di versione"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Revisione"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "Questo articolo è una revisione di un altro articolo?"

#: part/models.py:1119
msgid "Revision Of"
msgstr "Revisione di"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Dove viene normalmente immagazzinato questo articolo?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Fornitore predefinito"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Articolo fornitore predefinito"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Scadenza Predefinita"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Scadenza (in giorni) per gli articoli in giacenza di questo pezzo"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Scorta Minima"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Livello minimo di giacenza consentito"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Unita di misura per questo articolo"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Questo articolo può essere costruito da altri articoli?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Questo articolo può essere utilizzato per costruire altri articoli?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Questo articolo ha il tracciamento per gli elementi unici?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "Questo articolo può avere delle prove registrate?"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Quest'articolo può essere acquistato da fornitori esterni?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Questo pezzo può essere venduto ai clienti?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Quest'articolo è attivo?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "Gli articoli bloccati non possono essere modificati"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "È una parte virtuale, come un prodotto software o una licenza?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "Somma di controllo Distinta Base"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Somma di controllo immagazzinata Distinta Base"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "Distinta Base controllata da"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "Data di verifica Distinta Base"

#: part/models.py:1312
msgid "Creation User"
msgstr "Creazione Utente"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Utente responsabile di questo articolo"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Vendita multipla"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Valuta utilizzata per calcolare i prezzi"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Costo Minimo Distinta Base"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Costo minimo dei componenti dell'articolo"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Costo Massimo Distinta Base"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Costo massimo dei componenti dell'articolo"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Importo Acquisto Minimo"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Costo minimo di acquisto storico"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Importo massimo acquisto"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Costo massimo di acquisto storico"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Prezzo Interno Minimo"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Costo minimo basato su interruzioni di prezzo interne"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Prezzo Interno Massimo"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Costo massimo basato su interruzioni di prezzo interne"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Prezzo Minimo Fornitore"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Prezzo minimo articolo da fornitori esterni"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Prezzo Massimo Fornitore"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Prezzo massimo dell'articolo proveniente da fornitori esterni"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Variazione di costo minimo"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Costo minimo calcolato di variazione dell'articolo"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Massima variazione di costo"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Costo massimo calcolato di variazione dell'articolo"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Costo Minimo"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Sovrascrivi il costo minimo"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Costo Massimo"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Sovrascrivi il costo massimo"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Costo minimo totale calcolato"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Costo massimo totale calcolato"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Prezzo Di Vendita Minimo"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Prezzo minimo di vendita basato sulle interruzioni di prezzo"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Prezzo Di Vendita Massimo"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Prezzo massimo di vendita basato sulle interruzioni di prezzo"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Costo Di Vendita Minimo"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Prezzo storico minimo di vendita"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Costo Di Vendita Minimo"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Prezzo storico massimo di vendita"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Articolo per l'inventario"

#: part/models.py:3444
msgid "Item Count"
msgstr "Contatore Elemento"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Numero di scorte individuali al momento dell'inventario"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Totale delle scorte disponibili al momento dell'inventario"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Data"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Data in cui è stato effettuato l'inventario"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Costo Minimo Scorta"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Costo minimo stimato di magazzino a disposizione"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Costo Massimo Scorte"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Costo massimo stimato di magazzino a disposizione"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "Aggiungi Prezzo Ribassato di Vendita dell'Articolo"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "Modello Prove Articolo"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Nome modello non valido - deve includere almeno un carattere alfanumerico"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "Le scelte devono essere uniche"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "Il modello di prova può essere creato solo per gli articoli testabili"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "Il modello di test con la stessa chiave esiste già per l'articolo"

#: part/models.py:3684
msgid "Test Name"
msgstr "Nome Test"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Inserisci un nome per la prova"

#: part/models.py:3691
msgid "Test Key"
msgstr "Chiave Di Prova"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "Chiave semplificata per la prova"

#: part/models.py:3699
msgid "Test Description"
msgstr "Descrizione Di Prova"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Inserisci descrizione per questa prova"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Abilitato"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3709
msgid "Required"
msgstr "Richiesto"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Questa prova è necessaria per passare?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Valore richiesto"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Questa prova richiede un valore quando si aggiunge un risultato di prova?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Allegato Richiesto"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Questa prova richiede un file allegato quando si aggiunge un risultato di prova?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr ""

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "Scelte valide per questo test (separate da virgole)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "Modello parametro articolo"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "I parametri della casella di controllo non possono avere unità"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "I parametri della casella di controllo non possono avere scelte"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "Il nome del modello del parametro deve essere univoco"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Nome Parametro"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Unità fisiche per questo parametro"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Descrizione del parametro"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Casella di spunta"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Questo parametro è una casella di spunta?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Scelte valide per questo parametro (separato da virgola)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr "Lista di selezione per questo parametro"

#: part/models.py:3931
msgid "Part Parameter"
msgstr "Parametri Articolo"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "Il parametro non può essere modificato - l'articolo è bloccato"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Scelta non valida per il valore del parametro"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Articolo principale"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Modello Parametro"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Valore del Parametro"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Note opzionali elemento"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "Modello Parametro Categoria Articolo"

#: part/models.py:4176
msgid "Default Value"
msgstr "Valore Predefinito"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Valore Parametro Predefinito"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "L'articolo nella distinta base non può essere modificato - l'assemblaggio è bloccato"

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "L'articolo nella distinta base non può essere modificato - l'assemblaggio della variante è bloccato"

#: part/models.py:4363
msgid "Select parent part"
msgstr "Seleziona articolo principale"

#: part/models.py:4373
msgid "Sub part"
msgstr "Articolo subordinato"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Seleziona l'articolo da utilizzare nella Distinta Base"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "Quantità Distinta Base per questo elemento Distinta Base"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Questo elemento della Distinta Base è opzionale"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Questo elemento della Distinta Base è consumabile (non è tracciato negli ordini di produzione)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "Riferimento Elemento Distinta Base"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "Note Elemento Distinta Base"

#: part/models.py:4451
msgid "Checksum"
msgstr "Codice di controllo"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "Codice di controllo Distinta Base"

#: part/models.py:4457
msgid "Validated"
msgstr "Convalidato"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Questo articolo della distinta base è stato validato"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Viene Ereditato"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Questo elemento della Distinta Base viene ereditato dalle Distinte Base per gli articoli varianti"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Gli elementi in giacenza per gli articoli varianti possono essere utilizzati per questo elemento Distinta Base"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "La quantità deve essere un valore intero per gli articoli rintracciabili"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "L'articolo subordinato deve essere specificato"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "Elemento Distinta Base Sostituito"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "La parte sostituita non può essere la stessa dell'articolo principale"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Elemento principale Distinta Base"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Sostituisci l'Articolo"

#: part/models.py:4798
msgid "Part 1"
msgstr "Articolo 1"

#: part/models.py:4806
msgid "Part 2"
msgstr "Articolo 2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "Seleziona Prodotto Relativo"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr "Nota per questa relazione"

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Non si può creare una relazione tra l'articolo e sé stesso"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "La relazione duplicata esiste già"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "Categoria Superiore"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "Categoria articolo principale"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Sottocategorie"

#: part/serializers.py:202
msgid "Results"
msgstr "Risultati"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "Numero di risultati registrati rispetto a questo modello"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Valuta di acquisto di questo articolo in stock"

#: part/serializers.py:275
msgid "File is not an image"
msgstr "Il file non è un immagine"

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "Numero di articoli che utilizzano questo modello"

#: part/serializers.py:480
msgid "Original Part"
msgstr "Articolo Originale"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Seleziona l'articolo originale da duplicare"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Copia immagine"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Copia immagine dall'articolo originale"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Copia Distinta Base"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Copia fattura dei materiali dall'articolo originale"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Copia parametri"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Copia i dati dei parametri dall'articolo originale"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Copia note"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Copia note dall'articolo originale"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr "Copia Test"

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr "Copia modelli di test dall'articolo originale"

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Quantità iniziale"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Specificare la quantità iniziale disponibile per questo Articolo. Se la quantità è zero, non viene aggiunta alcuna quantità."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Ubicazione Iniziale Magazzino"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Specificare l'ubicazione iniziale del magazzino per questo Articolo"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Seleziona il fornitore (o lascia vuoto per saltare)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Seleziona il produttore (o lascia vuoto per saltare)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Codice articolo Produttore"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "L'azienda selezionata non è un fornitore valido"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "L'azienda selezionata non è un produttore valido"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "L'articolo del produttore che corrisponde a questo MPN esiste già"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "L'articolo del fornitore che corrisponde a questo SKU esiste già"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Nome Categoria"

#: part/serializers.py:936
msgid "Building"
msgstr "In Costruzione"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr "Quantità di questo articolo attualmente in produzione"

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Articoli in magazzino"

#: part/serializers.py:968
msgid "Revisions"
msgstr "Revisioni"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Fornitori"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Giacenze Totali"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "Scorte Non Assegnate"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "Scorta Variante"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Duplica articolo"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Copia i dati iniziali da un altro Articolo"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Stock iniziale"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Crea Articolo con quantità di scorta iniziale"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Informazioni Fornitore"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Aggiungi le informazioni iniziali del fornitore per questo articolo"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Copia Parametri Categoria"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Copia i parametri dai modelli della categoria articolo selezionata"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr ""

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr ""

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr ""

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Convalida l'intera Fattura dei Materiali"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Puoi produrre"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Prezzo Minimo"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr ""

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr ""

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Prezzo Massimo"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr ""

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr ""

#: part/serializers.py:1498
msgid "Update"
msgstr "Aggiorna"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Aggiorna i prezzi per questo articolo"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr ""

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr ""

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr ""

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1716
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Seleziona l'articolo da cui copiare la distinta base"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Rimuovi Dati Esistenti"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Rimuovi elementi distinta base esistenti prima di copiare"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Includi Ereditato"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Includi gli elementi Distinta Base ereditati da prodotti template"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Salta Righe Non Valide"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Abilita questa opzione per saltare le righe non valide"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Copia Articoli sostitutivi"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Copia articoli sostitutivi quando duplichi gli elementi distinta base"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Notifica di magazzino bassa"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Lo stock disponibile per {part.name} è sceso sotto il livello minimo configurato"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr "Obbligatorio"

#: plugin/api.py:107
msgid "Sample"
msgstr "Campione"

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Installato"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "Il plugin non può essere eliminato in quanto è attualmente attivo"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Nessuna azione specificata"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Nessuna azione corrispondente trovata"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Nessuna corrispondenza trovata per i dati del codice a barre"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Corrispondenza trovata per i dati del codice a barre"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Il modello non è supportato"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "Istanza del modello non trovata"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Il codice a barre corrisponde a un elemento esistente"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Nessun articolo corrispondente trovato"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Nessun fornitore articolo corrispondente trovato"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Trovati più articoli fornitori corrispondenti"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "Nessuna plugin corrispondente trovato per i dati del codice a barre"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Corrispondenza Articoli del Fornitore"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "L'articolo è già stato ricevuto"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "Nessun plugin corrisponde al codice a barre del fornitore"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Trovati più articoli corrispondenti"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Nessun elemento corrispondente trovato"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Nessun ordine di vendita fornito"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Il codice a barre non corrisponde a un articolo di magazzino valido"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "L'elemento in magazzino non corrisponde alla riga"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Scorte insufficienti disponibili"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Articolo di magazzino assegnato all'ordine di vendita"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Informazioni non sufficienti"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "Trovato elemento corrispondente"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "L'articolo del fornitore non corrisponde alla riga"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "La riga è già stata completata"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Ulteriori informazioni richieste per ricevere la voce"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Ricevuta la linea dell'ordine d'acquisto"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "Impossibile ricevere l'elemento della linea"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Codice a barre scansionato"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Nome del modello per generare codice a barre per"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "Chiave primaria dell'oggetto modello per generare codice a barre per"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Ordine di acquisto per allocare oggetti contro"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr "L'ordine di acquisto non è aperto"

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "Fornitore per ricevere articoli da"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Ordine di acquisto per ricevere oggetti contro"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "L'ordine di acquisto non è stato effettuato"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Posizione in cui ricevere gli articoli"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Non è possibile selezionare una posizione strutturale"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr "L'ordine di vendita non è aperto"

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Quantità da assegnare"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Stampa etichetta fallita"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "Errore nel rendering dell'etichetta in PDF"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "Errore nel rendering dell'etichetta in HTML"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Nessun elemento fornito da stampare"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Nome Plugin"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Tipo di funzionalità"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Etichetta Funzionalità"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Titolo della funzionalità"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Descrizione Funzionalità"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Icona Funzionalità"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Opzioni Funzionalità"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Contesto Funzionalità"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Sorgente Funzionalità (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree Codice a Barre"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Fornisce supporto nativo per codici a barre"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "Contributi d'InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "Formato Interno Del Codice A Barre"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "Seleziona un formato di codice a barre interno"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "Codici a barre JSON (leggibile dall'uomo)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "Codici a barre corti (ottimizzato per la dimensione)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "Prefisso Codice A Barre Corto"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr "Livelli"

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr "Dati Scorte"

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Rallentamenti in entrata notifiche url"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "Questo URL è stato utilizzato per inviare messaggi a un canale rallentato"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Apri collegamento"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr ""

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr ""

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr ""

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr ""

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr ""

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr ""

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Configurazione Plugin"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Configurazioni Plugin"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Key dei plugin"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "PluginName del plugin"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Nome Pacchetto"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Il plugin è attivo"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Plugin di esempio"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Plugin Integrato"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:209
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr ""

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Nessun autore trovato"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr ""

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr ""

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr ""

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "Abilita PO"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "Abilita funzionalità PO nell'interfaccia InvenTree"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr ""

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Key richiesta per accedere alle API esterne"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Numerico"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "Un'impostazione numerica"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Scegli l'impostazione"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "Un'impostazione con scelte multiple"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Abilita Pannelli della Parte"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Abilita pannelli personalizzati per le viste Parte"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Abilita i Pannelli Ordine D'Acquisto"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Abilita pannelli personalizzati per le viste Ordine d'Acquisto"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Abilita Pannelli Interrotti"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Abilita pannelli interrotti per testing"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Abilita Pannello Dinamico"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Abilita pannelli dinamici per testing"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Pannello Articolo"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Elemento Dashboard Interrotto"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Questo è un elemento di dashborad interrotto - non sarà renderizzato!"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Elemento Dashboard d'Esempio"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Questo è un esempio di elemento dashboard. Renderizza una semplice stringa di contenuto HTML."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Elemento Dashboard Contesto"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Elemento Dashboard Amministratore"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Questo è un elemento dashboard solo per amministratori."

#: plugin/serializers.py:86
msgid "Source File"
msgstr "File Sorgente"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Percorso del file sorgente per l'integrazione amministrazione"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Dati di contesto opzionali per l'integrazione amministrazione"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "URL di origine"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Fonte per il pacchetto - questo può essere un registro personalizzato o un percorso VCS"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Nome per il Pacchetto Plugin - può anche contenere un indicatore di versione"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versione"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Conferma installazione plugin"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Questo plugin verrà installato ora nell'istanza corrente. L'istanza andrà in manutenzione."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installazione non confermata"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Deve essere fornito uno dei nomi del pacchetto URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr ""

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr ""

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr ""

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr ""

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr ""

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr ""

#: report/api.py:114
msgid "Plugin not found"
msgstr ""

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr ""

#: report/helpers.py:46
msgid "Letter"
msgstr ""

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:217
msgid "Template name"
msgstr "Nome modello"

#: report/models.py:223
msgid "Template description"
msgstr "Descrizione del template"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr "Allega al Modello su Stampa"

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Salva l'output del report come allegato contro l'istanza del modello collegato durante la stampa"

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Formato del nome file"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:287
msgid "Template is enabled"
msgstr ""

#: report/models.py:294
msgid "Target model type for template"
msgstr ""

#: report/models.py:314
msgid "Filters"
msgstr "Filtri"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr ""

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr ""

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr ""

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Larghezza [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Larghezza dell'etichetta, specificata in mm"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Altezza [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Larghezza dell'etichetta, specificata in mm"

#: report/models.py:780
msgid "Error printing labels"
msgstr ""

#: report/models.py:799
msgid "Snippet"
msgstr ""

#: report/models.py:800
msgid "Report snippet file"
msgstr "Report file snippet"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Descrizione file snippet"

#: report/models.py:825
msgid "Asset"
msgstr "Risorsa"

#: report/models.py:826
msgid "Report asset file"
msgstr "Report file risorsa"

#: report/models.py:833
msgid "Asset file description"
msgstr "File risorsa descrizione"

#: report/serializers.py:96
msgid "Select report template"
msgstr ""

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:137
msgid "Select label template"
msgstr ""

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr ""

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr ""

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Distinta base"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Materiali necessari"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Emesso"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Richiesto Per"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Inviato da"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Il fornitore è stato eliminato"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Prezzo Unitario"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Totale"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Numero Seriale"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Assegnazioni"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Lotto"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr ""

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Test Report Elemento Stock"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Elementi installati"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Seriale"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Risultati Test"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr ""

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Passaggio"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Fallito"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Nessun risultato (richiesto)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Nessun risultato"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr ""

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr ""

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr ""

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr ""

#: stock/api.py:283
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr ""

#: stock/api.py:340
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr "Nome della parte (maiuscole e minuscole)"

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr "Il nome della parte contiene (maiuscole e minuscole)"

#: stock/api.py:594
msgid "Part name (regex)"
msgstr "Nome della parte (regex)"

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr "IPN della parte (maiuscole e minuscole)"

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr "IPN della parte contiene (maiuscole e minuscole)"

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr "IPN della parte (regex)"

#: stock/api.py:623
msgid "Minimum stock"
msgstr "Giacenza minima"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "Giacenza massima"

#: stock/api.py:630
msgid "Status Code"
msgstr "Codici di stato"

#: stock/api.py:670
msgid "External Location"
msgstr "Ubicazione Esterna"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:868
msgid "Part Tree"
msgstr ""

#: stock/api.py:890
msgid "Updated before"
msgstr ""

#: stock/api.py:894
msgid "Updated after"
msgstr ""

#: stock/api.py:898
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:902
msgid "Stocktake After"
msgstr ""

#: stock/api.py:907
msgid "Expiry date before"
msgstr ""

#: stock/api.py:911
msgid "Expiry date after"
msgstr ""

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "Obsoleto"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "La quantità è richiesta"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "Deve essere fornita un articolo valido"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr ""

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr ""

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "I numeri di serie non possono essere forniti per un articolo non tracciabile"

#: stock/models.py:72
msgid "Stock Location type"
msgstr ""

#: stock/models.py:73
msgid "Stock Location types"
msgstr ""

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr ""

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Ubicazione magazzino"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Posizioni magazzino"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Proprietario"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Seleziona Owner"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Gli elementi di magazzino non possono essere direttamente situati in un magazzino strutturale, ma possono essere situati in ubicazioni secondarie."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Esterno"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Si tratta di una posizione esterna al magazzino"

#: stock/models.py:233
msgid "Location type"
msgstr ""

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr ""

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Non puoi rendere strutturale questa posizione di magazzino perché alcuni elementi di magazzino sono già posizionati al suo interno!"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr "L'articolo deve essere specificato"

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Gli articoli di magazzino non possono essere ubicati in posizioni di magazzino strutturali!"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Non è possibile creare un elemento di magazzino per articoli virtuali"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr ""

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "La quantità deve essere 1 per elementi con un numero di serie"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Il numero di serie non può essere impostato se la quantità è maggiore di 1"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "L'elemento non può appartenere a se stesso"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "L'elemento deve avere un riferimento di costruzione se is_building=True"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "Il riferimento di costruzione non punta allo stesso oggetto dell'articolo"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Elemento di magazzino principale"

#: stock/models.py:1028
msgid "Base part"
msgstr "Articolo base"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Seleziona un fornitore articolo corrispondente per questo elemento di magazzino"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Dove si trova questo articolo di magazzino?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "Imballaggio di questo articolo di magazzino è collocato in"

#: stock/models.py:1064
msgid "Installed In"
msgstr "Installato In"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "Questo elemento è stato installato su un altro elemento?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Numero di serie per questo elemento"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Codice lotto per questo elemento di magazzino"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Quantità disponibile"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Genera Costruzione"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Costruisci per questo elemento di magazzino"

#: stock/models.py:1130
msgid "Consumed By"
msgstr ""

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr ""

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Origina Ordine di Acquisto"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Ordine d'acquisto per questo articolo in magazzino"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Destinazione Ordine di Vendita"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Data di scadenza per l'elemento di magazzino. Le scorte saranno considerate scadute dopo questa data"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Elimina al esaurimento"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Cancella questo Elemento di Magazzino quando la giacenza è esaurita"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Prezzo di acquisto unitario al momento dell’acquisto"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "Convertito in articolo"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "L'articolo non è impostato come tracciabile"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "La quantità deve essere un numero intero"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr ""

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr "I numeri di serie devono essere forniti come elenco"

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "La quantità non corrisponde ai numeri di serie"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "L'elemento di magazzino è stato assegnato a un ordine di vendita"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "L'elemento di magazzino è installato in un altro elemento"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "L'elemento di magazzino contiene altri elementi"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "L'elemento di magazzino è stato assegnato a un cliente"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "L'elemento di magazzino è attualmente in produzione"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Il magazzino serializzato non può essere unito"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Duplica elementi di magazzino"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "Gli elementi di magazzino devono riferirsi allo stesso articolo"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "Gli elementi di magazzino devono riferirsi allo stesso articolo fornitore"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "I codici di stato dello stock devono corrispondere"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Le giacenze non possono essere spostate perché non disponibili"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Note d'ingresso"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Il valore deve essere fornito per questo test"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "L'allegato deve essere caricato per questo test"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2951
msgid "Test result"
msgstr "Risultato Test"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Test valore output"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Risultato della prova allegato"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Note del test"

#: stock/models.py:2978
msgid "Test station"
msgstr ""

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2985
msgid "Started"
msgstr ""

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2992
msgid "Finished"
msgstr ""

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Elemento principale"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr ""

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Inserisci i numeri di serie per i nuovi elementi"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr ""

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "Scaduto"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Elementi secondari"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr ""

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Inserisci il numero di elementi di magazzino da serializzare"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "La quantità non deve superare la quantità disponibile ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Posizione magazzino di destinazione"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Numeri di serie non possono essere assegnati a questo articolo"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "Numeri di serie già esistenti"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Seleziona elementi di magazzino da installare"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr ""

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr ""

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "Aggiungi nota di transazione (opzionale)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr ""

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "Elemento di magazzino non disponibile"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "L'articolo selezionato non è nella Fattura dei Materiali"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr ""

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Posizione di destinazione per gli elementi disinstallati"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Seleziona l'articolo in cui convertire l'elemento di magazzino"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "L'articolo selezionato non è una valida opzione per la conversione"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr ""

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr ""

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr ""

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr ""

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Sottoallocazioni"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "L'articolo deve essere vendibile"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "L'elemento è assegnato a un ordine di vendita"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "Elemento assegnato a un ordine di costruzione"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Cliente a cui assegnare elementi di magazzino"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "L'azienda selezionata non è un cliente"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Note sull'assegnazione delle scorte"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "Deve essere fornito un elenco degli elementi di magazzino"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Note di fusione di magazzino"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Consenti fornitori non corrispondenti"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Consenti di unire gli elementi di magazzino che hanno fornitori diversi"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Consenti stato non corrispondente"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Consenti di unire gli elementi di magazzino con diversi codici di stato"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Devono essere riforniti almeno due elementi in magazzino"

#: stock/serializers.py:1551
msgid "No Change"
msgstr ""

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Valore di chiave primaria StockItem"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Note sugli spostamenti di magazzino"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr ""

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Attenzione necessaria"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Danneggiato"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Distrutto"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Respinto"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "In quarantena"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Voce di tracciamento stock preesistente"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Elemento stock creato"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Elemento stock modificato"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Numero di serie assegnato"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Stock contato"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Stock aggiunto manualmente"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Stock rimosso manualmente"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Posizione cambiata"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Stock aggiornato"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "Installato nell'assemblaggio"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Rimosso dall'assemblaggio"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Componente installato"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Elemento componente rimosso"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Diviso dall'elemento genitore"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Dividi elemento figlio"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Elemento stock raggruppato"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "Convertito in variante"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "Genera l'output dell'ordine creato"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Build order output completato"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "Ordine di costruzione rifiutato"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Impegnato dall'ordine di costruzione"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Spedito contro l'ordine di vendita"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Ricevuto contro l'ordine di acquisto"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Restituito contro l'ordine di ritorno"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Inviato al cliente"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Restituito dal cliente"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Permesso negato"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Non ha i permessi per visualizzare la pagina."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Autenticazione Fallita"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Sei stato disconnesso da InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Pagina Non Trovata"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "La pagina richiesta non esiste"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Errore Interno del Server"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "Il server %(inventree_title)s ha rilevato un errore interno"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Consultare il log degli errori nell'interfaccia di amministrazione per ulteriori dettagli"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Il sito è in manutenzione"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Il sito è attualmente in manutenzione e dovrebbe essere online presto!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "È necessario riavviare il server"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "È stata modificata un'impostazione che richiede un riavvio del server"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Contatta l'amministratore per maggiori informazioni"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr ""

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr ""

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Clicca il seguente link per visualizzare quest'ordine"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "La giacenza è richiesta per il seguente ordine di produzione"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Ordine di produzione %(build)s - produzione %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Clicca il seguente link per visualizzare quest'ordine di produzione"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "I seguenti articoli sono pochi nel magazzino richiesto"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Quantità richiesta"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Stai ricevendo questa email perché sei iscritto alle notifiche per questo articolo "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Clicca il seguente link per visualizzare questo articolo"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Quantità minima"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Utenti"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Selezionare quali utenti sono assegnati a questo gruppo"

#: users/admin.py:137
msgid "Personal info"
msgstr "Informazioni personali"

#: users/admin.py:139
msgid "Permissions"
msgstr "Permessi"

#: users/admin.py:142
msgid "Important dates"
msgstr "Date Importanti"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr ""

#: users/authentication.py:33
msgid "Token has expired"
msgstr ""

#: users/models.py:100
msgid "API Token"
msgstr ""

#: users/models.py:101
msgid "API Tokens"
msgstr ""

#: users/models.py:137
msgid "Token Name"
msgstr ""

#: users/models.py:138
msgid "Custom token name"
msgstr ""

#: users/models.py:144
msgid "Token expiry date"
msgstr ""

#: users/models.py:152
msgid "Last Seen"
msgstr ""

#: users/models.py:153
msgid "Last time the token was used"
msgstr ""

#: users/models.py:157
msgid "Revoked"
msgstr ""

#: users/models.py:235
msgid "Permission set"
msgstr "Impostazione autorizzazioni"

#: users/models.py:244
msgid "Group"
msgstr "Gruppo"

#: users/models.py:248
msgid "View"
msgstr "Visualizza"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Autorizzazione a visualizzare gli articoli"

#: users/models.py:252
msgid "Add"
msgstr "Aggiungi"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Autorizzazione ad aggiungere elementi"

#: users/models.py:256
msgid "Change"
msgstr "Modificare"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Permessi per modificare gli elementi"

#: users/models.py:262
msgid "Delete"
msgstr "Elimina"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Autorizzazione ad eliminare gli elementi"

#: users/models.py:501
msgid "Bot"
msgstr ""

#: users/models.py:502
msgid "Internal"
msgstr ""

#: users/models.py:504
msgid "Guest"
msgstr ""

#: users/models.py:513
msgid "Language"
msgstr ""

#: users/models.py:514
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:519
msgid "Theme"
msgstr ""

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr ""

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr ""

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:541
msgid "Position"
msgstr ""

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr ""

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr ""

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr ""

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr ""

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr "Amministratore"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Ordine di acquisto"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Ordini di Vendita"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Ordini di reso"

#: users/serializers.py:196
msgid "Username"
msgstr "Nome utente"

#: users/serializers.py:199
msgid "First Name"
msgstr "Nome"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Nome dell'utente"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Cognome"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Cognome dell'utente"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "Indirizzo email dell'utente"

#: users/serializers.py:326
msgid "Staff"
msgstr "Staff"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Questo utente ha i permessi dello staff"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Superuser"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "Questo utente è un superutente"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Questo account utente è attivo"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Il tuo account è stato creato."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Si prega di utilizzare la funzione di reimpostazione password per accedere"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Benvenuto in InvenTree"

