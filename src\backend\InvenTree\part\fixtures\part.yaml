# Create some fasteners

- model: part.part
  pk: 1
  fields:
    name: 'M2x4 LPHS'
    description: 'M2x4 low profile head screw'
    category: 8
    link: http://www.acme.com/parts/m2x4lphs
    creation_date: '2018-01-01'
    purchaseable: True
    testable: False
    tree_id: 5
    level: 0
    lft: 1
    rght: 2

- model: part.part
  pk: 2
  fields:
    name: 'M3x12 SHCS'
    description: 'M3x12 socket head cap screw'
    category: 8
    creation_date: '2019-02-02'
    tree_id: 6
    level: 0
    lft: 1
    rght: 2

# Create some resistors

- model: part.part
  pk: 3
  fields:
    name: 'R_2K2_0805'
    description: '2.2kOhm resistor in 0805 package'
    category: 2
    creation_date: '2020-03-03'
    tree_id: 8
    level: 0
    lft: 1
    rght: 2

- model: part.part
  pk: 4
  fields:
    name: 'R_4K7_0603'
    description: '4.7kOhm resistor in 0603 package'
    category: 2
    creation_date: '2021-04-04'
    default_location: 2  # Home/Bathroom
    tree_id: 9
    level: 0
    lft: 1
    rght: 2

# Create some capacitors
- model: part.part
  pk: 5
  fields:
    name: 'C_22N_0805'
    description: '22nF capacitor in 0805 package'
    purchaseable: true
    category: 3
    creation_date: '2022-05-05'
    tree_id: 3
    level: 0
    lft: 1
    rght: 2

- model: part.part
  pk: 25
  fields:
    name: 'Widget'
    description: 'A watchamacallit'
    category: 7
    creation_date: '2023-06-06'
    salable: true
    assembly: true
    trackable: true
    testable: true
    default_expiry: 10
    tree_id: 10
    level: 0
    lft: 1
    rght: 2

- model: part.part
  pk: 50
  fields:
    name: 'Orphan'
    description: 'A part without a category'
    category: null
    salable: true
    creation_date: '2024-07-07'
    tree_id: 7
    level: 0
    lft: 1
    rght: 2

# A part that can be made from other parts
- model: part.part
  pk: 100
  fields:
    name: 'Bob'
    description: 'Can we build it? Yes we can!'
    notes: 'Some notes associated with this part'
    assembly: true
    salable: true
    purchaseable: false
    creation_date: '2025-08-08'
    category: 7
    active: True
    testable: True
    IPN: BOB
    revision: A2
    tree_id: 2
    level: 0
    lft: 1
    rght: 2

- model: part.part
  pk: 101
  fields:
    name: 'Assembly'
    description: 'A high level assembly part'
    salable: true
    creation_date: '2026-09-09'
    active: True
    tree_id: 1
    level: 0
    lft: 1
    rght: 2

# A 'template' part
- model: part.part
  pk: 10000
  fields:
    name: 'Chair Template'
    description: 'A chair, which is actually just a template part'
    is_template: True
    trackable: false
    testable: true
    creation_date: '2027-10-10'
    salable: true
    category: 7
    tree_id: 4
    level: 0
    lft: 1
    rght: 10

- model: part.part
  pk: 10001
  fields:
    name: 'Blue Chair'
    description: 'A variant chair part which is blue'
    variant_of: 10000
    trackable: false
    testable: true
    creation_date: '2028-11-11'
    category: 7
    tree_id: 4
    level: 1
    lft: 2
    rght: 3

- model: part.part
  pk: 10002
  fields:
    name: 'Red chair'
    description: 'A variant chair part which is red'
    variant_of: 10000
    IPN: "R.CH"
    trackable: false
    testable: true
    salable: true
    creation_date: '2029-12-12'
    category: 7
    tree_id: 4
    level: 1
    lft: 8
    rght: 9

- model: part.part
  pk: 10003
  fields:
    name: 'Green chair'
    description: 'A template chair part which is green'
    variant_of: 10000
    is_template: true
    category: 7
    trackable: false
    testable: true
    creation_date: '2030-01-01'
    tree_id: 4
    level: 1
    lft: 4
    rght: 7

- model: part.part
  pk: 10004
  fields:
    name: 'Green chair variant'
    description: 'A green chair, which is a variant of the chair template'
    variant_of: 10003
    is_template: true
    category: 7
    creation_date: '2031-02-02'
    trackable: true
    testable: true
    tree_id: 4
    level: 2
    lft: 5
    rght: 6

- model: part.partrelated
  pk: 1
  fields:
    part_1: 10003
    part_2: 10004
