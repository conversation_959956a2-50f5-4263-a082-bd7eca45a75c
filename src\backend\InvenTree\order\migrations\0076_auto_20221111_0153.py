# Generated by Django 3.2.16 on 2022-11-11 01:53

import InvenTree.fields
from django.db import migrations
import djmoney.models.validators


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0075_auto_20221110_0108'),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseorderextraline',
            name='price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Unit price', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Price'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='purchase_price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Unit purchase price', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Purchase Price'),
        ),
        migrations.AlterField(
            model_name='salesorderextraline',
            name='price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Unit price', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Price'),
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='sale_price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Unit sale price', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Sale Price'),
        ),
    ]
