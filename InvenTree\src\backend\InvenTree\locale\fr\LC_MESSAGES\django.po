msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: French\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: fr\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Vous devez activer l'authentification à deux facteurs avant toute autre chose."

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "Point de terminaison de l'API introuvable"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr "Liste des éléments ou des filtres à fournir pour les opérations en vrac"

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr "Les éléments doivent être fournis sous forme de liste"

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Liste d'éléments non valide fournie"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr "Les filtres doivent être fournis sous forme de dictionnaire"

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Filtres fournis invalides"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr "Tous les filtres ne doivent être utilisés qu'avec \"true\""

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr "Aucun élément ne correspond aux critères fournis"

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "L'utilisateur n'a pas la permission de voir ce modèle"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "Email (encore)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "Confirmation de l'adresse email"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Vous devez taper le même e-mail à chaque fois."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "L'adresse e-mail principale fournie n'est pas valide."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Le domaine e-mail fourni n'est pas approuvé."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Unité fournie invalide ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Pas de valeur renseignée"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Impossible de convertir {original} en {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Quantité fournie invalide"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Les détails de l'erreur peuvent être trouvées dans le panneau d'administration"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Entrer la date"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Valeur décimale invalide"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Notes"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "La valeur '{name}' n'apparaît pas dans le format du modèle"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "La valeur fournie ne correspond pas au modèle requis : "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Impossible de sérialiser plus de 1000 éléments à la fois"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Chaîne de numéro de série vide"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Numéro de série en doublon"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Groupe invalide : {group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "La plage de groupe {group} dépasse la quantité autorisée ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Aucun numéro de série trouvé"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr "Le nombre de numéros de série uniques ({n}) doit correspondre à la quantité ({q})"

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Retirer les balises HTML de cette valeur"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "Les données contiennent du contenu markdown interdit"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Erreur de connexion"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Le serveur a répondu avec un code de statut invalide"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Une erreur est survenue"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Le serveur a répondu avec une valeur de longueur de contenu invalide"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Image trop volumineuse"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "La taille de l'image dépasse la taille maximale autorisée"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Le serveur distant a renvoyé une réponse vide"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "L'URL fournie n'est pas un fichier image valide"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabe"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgare"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tchèque"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danois"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Allemand"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Grec"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Anglais"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Espagnol"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Espagnol (Mexique)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estonien"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Perse"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finnois"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Français"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hébreu"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Hongrois"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italien"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japonais"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Coréen"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Lituanien"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Letton"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Néerlandais"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norvégien"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polonais"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugais"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugais (Brésilien)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Roumain"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russe"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovaque"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovénien"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbe"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Suédois"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thaïlandais"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turc"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrainien"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamien"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Chinois (Simplifié)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Chinois (Traditionnel)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Se connecter à l'application"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "E-mail"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Erreur lors de l'exécution de la validation du plugin"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Les metadata doivent être un objet python de type \"dict\""

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Métadonnées de l'Extension"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "Champs metadata JSON, pour plugins tiers"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Modèle mal formaté"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Clé de format inconnu spécifiée"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Clé de format requise manquante"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "Le champ de référence ne peut pas être vide"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "La référence doit correspondre au modèle requis"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Le numéro de référence est trop grand"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Choix invalide"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Nom"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Description"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Description (facultative)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Chemin d'accès"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Les noms dupliqués ne peuvent pas exister sous le même parent"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Notes Markdown (option)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Données du code-barres"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Données de code-barres tierces"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Hash du code-barre"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Hachage unique des données du code-barres"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Code-barres existant trouvé"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Échec de la tâche"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "La tâche de travail en arrière-plan '{f}' a échoué après {n} tentatives"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Erreur serveur"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "Une erreur a été loguée par le serveur."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Doit être un nombre valide"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Devise"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Sélectionnez la devise à partir des options disponibles"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Valeur non valide"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Images distantes"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL du fichier image distant"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Le téléchargement des images depuis une URL distante n'est pas activé"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Échec du téléchargement de l'image à partir de l'URL distant"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Unité invalide"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Code de devise invalide"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Statut de la commande"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Fabrication parente"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Inclure les variantes"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Pièce"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Catégorie"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Version Précédente"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Attribué à moi"

#: build/api.py:154
msgid "Assigned To"
msgstr "Attribué à"

#: build/api.py:189
msgid "Created before"
msgstr "Créé avant"

#: build/api.py:193
msgid "Created after"
msgstr "Créé après"

#: build/api.py:197
msgid "Has start date"
msgstr "A une date de début"

#: build/api.py:205
msgid "Start date before"
msgstr "Date de début avant"

#: build/api.py:209
msgid "Start date after"
msgstr "Date de début après"

#: build/api.py:213
msgid "Has target date"
msgstr "A une date butoir"

#: build/api.py:221
msgid "Target date before"
msgstr "Date cible avant"

#: build/api.py:225
msgid "Target date after"
msgstr "Date cible après"

#: build/api.py:229
msgid "Completed before"
msgstr "Terminé avant"

#: build/api.py:233
msgid "Completed after"
msgstr "Terminé après"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr "Date min"

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr "Date maximale"

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Exclure l'arbre"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "La construction doit être annulée avant de pouvoir être supprimée"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Consommable"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Facultatif"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Assemblage"

#: build/api.py:450
msgid "Tracked"
msgstr "Suivi"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Testable"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "Commande en cours"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Allouée"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Disponible"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Ordre de Fabrication"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Emplacement"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Ordres de Fabrication"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "La liste des composants de l'assemblage n'a pas été validée"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Impossible de créer un ordre de fabrication pour une pièce inactive"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Impossible de créer un ordre de fabrication pour une pièce non verrouillée"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr "Les ordres de fabrication ne peuvent être exécutées qu'en externe pour les pièces achetables"

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Un utilisateur ou un groupe responsable doit être spécifié"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "La pièce de commande de construction ne peut pas être changée"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr "La date cible doit être postérieure à la date de début"

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Référence de l' Ordre de Fabrication"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Référence"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Brève description de la fabrication (optionnel)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "BuildOrder associé a cette fabrication"

#: build/models.py:273
msgid "Select part to build"
msgstr "Sélectionnez la pièce à construire"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Bon de commande de référence"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Commande de vente à laquelle cette construction est allouée"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Emplacement d'origine"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Sélectionner l'emplacement à partir duquel le stock doit être pris pour cette construction (laisser vide pour prendre à partir de n'importe quel emplacement de stock)"

#: build/models.py:300
msgid "External Build"
msgstr "Fabrication externe"

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr "Cet ordre de fabrication est exécuté en externe"

#: build/models.py:306
msgid "Destination Location"
msgstr "Emplacement cible"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Sélectionnez l'emplacement où les éléments complétés seront stockés"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Quantité a fabriquer"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Nombre de stock items à construire"

#: build/models.py:322
msgid "Completed items"
msgstr "Articles terminés"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Nombre d'articles de stock qui ont été terminés"

#: build/models.py:328
msgid "Build Status"
msgstr "État de la construction"

#: build/models.py:333
msgid "Build status code"
msgstr "Code de statut de construction"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Code de lot"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Code de lot pour ce build output"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Date de création"

#: build/models.py:356
msgid "Build start date"
msgstr "Début de la fabrication"

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr "Date de début prévue pour cet ordre de construction"

#: build/models.py:363
msgid "Target completion date"
msgstr "Date d'achèvement cible"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Date cible pour l'achèvement de la construction. La construction sera en retard après cette date."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Date d'achèvement"

#: build/models.py:378
msgid "completed by"
msgstr "achevé par"

#: build/models.py:387
msgid "Issued by"
msgstr "Émis par"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Utilisateur ayant émis cette commande de construction"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Responsable"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Utilisateur ou groupe responsable de cet ordre de construction"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Lien Externe"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Lien vers une url externe"

#: build/models.py:410
msgid "Build Priority"
msgstr "Priorité de fabrication"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Priorité de cet ordre de fabrication"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Code du projet"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Code de projet pour cet ordre de construction"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Échec du déchargement de la tâche pour terminer les allocations de construction"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "La commande de construction {build} a été effectuée"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "Une commande de construction a été effectuée"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Les numéros de série doivent être fournis pour les pièces traçables"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Pas d'ordre de production défini"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "L'ordre de production a déjà été réalisé"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "L'ordre de production de correspond pas à l'ordre de commande"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "La quantité doit être supérieure à zéro"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "La quantité ne peut pas être supérieure à la quantité de sortie"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "La sortie de compilation {serial} n'a pas réussi tous les tests requis"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Poste de l'ordre de construction"

#: build/models.py:1602
msgid "Build object"
msgstr "Création de l'objet"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Quantité"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Quantité requise pour la commande de construction"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "L'élément de construction doit spécifier une sortie de construction, la pièce maîtresse étant marquée comme objet traçable"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "La quantité allouée ({q}) ne doit pas excéder la quantité disponible ({a})"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "L'article de stock est suralloué"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "La quantité allouée doit être supérieure à zéro"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "La quantité doit être de 1 pour stock sérialisé"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "L'article de stock sélectionné ne correspond pas à la ligne BOM"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Article en stock"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Stock d'origine de l'article"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Quantité de stock à allouer à la construction"

#: build/models.py:1924
msgid "Install into"
msgstr "Installer dans"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Stock de destination de l'article"

#: build/serializers.py:115
msgid "Build Level"
msgstr "Niveau de construction"

#: build/serializers.py:124
msgid "Part Name"
msgstr "Nom de l'article"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Code du projet Étiquette"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Sortie d'assemblage"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "L'ordre de production ne correspond pas à l'ordre parent"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "La pièce en sortie ne correspond pas à la pièce de l'ordre de construction"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Cet ordre de production a déjà été produit"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Cet ordre de production n'est pas complètement attribué"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Entrer la quantité désiré pour la fabrication"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Quantité entière requise pour les pièces à suivre"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Quantité entière requise, car la facture de matériaux contient des pièces à puce"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Numéros de série"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Entrer les numéros de séries pour la fabrication"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Emplacement de stock pour la sortie de la fabrication"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Allouer automatiquement les numéros de série"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Affecter automatiquement les éléments requis avec les numéros de série correspondants"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "Les numéros de série suivants existent déjà, ou sont invalides"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "Une liste d'ordre de production doit être fourni"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Emplacement du stock pour les sorties épuisées"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Ignorer les allocations"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Abandonner les allocations de stock pour les sorties abandonnées"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Motif de l'élimination des produits de construction(s)"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Emplacement des ordres de production achevés"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Accepter l'allocation incomplète"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Compléter les sorties si le stock n'a pas été entièrement alloué"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Consommation du stock alloué"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Consommer tout stock qui a déjà été alloué à cette construction"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Retirer les sorties incomplètes"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Supprimer toutes les sorties de construction qui n'ont pas été complétées"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Non permis"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Accepter comme consommé par cet ordre de construction"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Désaffecter avant de terminer cette commande de fabrication"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Stock suralloué"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Comment voulez-vous gérer les articles en stock supplémentaires assignés à l'ordre de construction"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Certains articles de stock ont été suralloués"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Accepter les non-alloués"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Accepter les articles de stock qui n'ont pas été complètement alloués à cette ordre de production"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "Le stock requis n'a pas encore été totalement alloué"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Accepter les incomplèts"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Accepter que tous les ordres de production n'aient pas encore été achevés"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "La quantité nécessaire n'a pas encore été complétée"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "L'ordre de construction a des ordres de construction enfants ouverts"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "L'ordre de construction doit être en état de production"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "L'ordre de production a des sorties incomplètes"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Chaîne d'assemblage"

#: build/serializers.py:877
msgid "Build output"
msgstr "Sortie d'assemblage"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "La sortie de la construction doit pointer vers la même construction"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Élément de la ligne de construction"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part doit pointer sur la même pièce que l'ordre de construction"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "L'article doit être en stock"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Quantité disponible ({q}) dépassée"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "La sortie de construction doit être spécifiée pour l'allocation des pièces suivies"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "La sortie de la construction ne peut pas être spécifiée pour l'allocation des pièces non suivies"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Les articles d'allocation doivent être fournis"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Emplacement de stock où les pièces doivent être fournies (laissez vide pour les prendre à partir de n'importe quel emplacement)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Emplacements exclus"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Exclure les articles de stock de cet emplacement sélectionné"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Stock interchangeable"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Les articles de stock à plusieurs emplacements peuvent être utilisés de manière interchangeable"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Stock de substitution"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Autoriser l'allocation de pièces de remplacement"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Objets Optionnels"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Affecter des éléments de nomenclature facultatifs à l'ordre de fabrication"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Échec du démarrage de la tâche d'auto-allocation"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "Référence de la nomenclature"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "ID de la pièce de la nomenclature"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "Nomenclature Nom de la pièce"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "Construire"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Pièce fournisseur"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Quantité allouée"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Référence de construction"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Nom de la catégorie de pièces"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Traçable"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Reçu de quelqu'un"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Autoriser les variantes"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "Article du BOM"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "En Commande"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "En Production"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr "Planifié pour fabrication"

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Stock externe"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Stock disponible"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Stock de substitution disponible"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Stock de variantes disponibles"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "En attente"

#: build/status_codes.py:12
msgid "Production"
msgstr "Fabrication"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "En pause"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Annulé"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Terminé"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Stock requis pour la commande de construction"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Ordre de commande en retard"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "L'ordre de commande {bo} est maintenant en retard"

#: common/api.py:688
msgid "Is Link"
msgstr "C'est un lien"

#: common/api.py:696
msgid "Is File"
msgstr "C'est un fichier"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr ""

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "L'utilisateur n'a pas les permissions de supprimer cette pièce jointe"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Code de devise invalide"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Code de devise en double"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Aucun code de devise valide fourni"

#: common/currency.py:146
msgid "No plugin"
msgstr "Pas de plugin"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Mise à jour"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Date de la dernière mise à jour"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "Code projet unique"

#: common/models.py:171
msgid "Project description"
msgstr "Description du projet"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Utilisateur ou groupe responsable de ce projet"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "Paramétrés des touches"

#: common/models.py:780
msgid "Settings value"
msgstr "Valeur du paramètre"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "La valeur choisie n'est pas une option valide"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "La valeur doit être une valeur booléenne"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "La valeur doit être un nombre entier"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr "Valeur doit être un nombre valide"

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr "La valeur ne passe pas les contrôles de validation"

#: common/models.py:914
msgid "Key string must be unique"
msgstr "La chaîne de caractères constituant la clé doit être unique"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Utilisateur"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Quantité de rupture de prix"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Prix"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Prix unitaire à la quantité spécifiée"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Point final"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Point de terminaison auquel ce webhook est reçu"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Nom de ce webhook"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Actif"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Ce webhook (lien de rappel HTTP) est-il actif"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Jeton"

#: common/models.py:1437
msgid "Token for access"
msgstr "Jeton d'accès"

#: common/models.py:1445
msgid "Secret"
msgstr "Confidentiel"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Secret partagé pour HMAC"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "ID message"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Identifiant unique pour ce message"

#: common/models.py:1563
msgid "Host"
msgstr "Hôte"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Hôte à partir duquel ce message a été reçu"

#: common/models.py:1572
msgid "Header"
msgstr "Entête"

#: common/models.py:1573
msgid "Header of this message"
msgstr "En-tête de ce message"

#: common/models.py:1580
msgid "Body"
msgstr "Corps"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Corps de ce message"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Endpoint à partir duquel ce message a été reçu"

#: common/models.py:1596
msgid "Worked on"
msgstr "Travaillé sur"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Le travail sur ce message est-il terminé ?"

#: common/models.py:1723
msgid "Id"
msgstr "Id"

#: common/models.py:1725
msgid "Title"
msgstr "Titre"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Lien"

#: common/models.py:1729
msgid "Published"
msgstr "Publié"

#: common/models.py:1731
msgid "Author"
msgstr "Auteur"

#: common/models.py:1733
msgid "Summary"
msgstr "Résumé"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Lu"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Cette nouvelle a-t-elle été lue ?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Image"

#: common/models.py:1753
msgid "Image file"
msgstr "Fichier image"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "Type de modèle cible pour cette image"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "ID du modèle cible pour cette image"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Unité personnalisée"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "Le symbole de l'unité doit être unique"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Le nom de l'unité doit être un identifiant valide"

#: common/models.py:1843
msgid "Unit name"
msgstr "Nom de l'unité"

#: common/models.py:1850
msgid "Symbol"
msgstr "Symbole"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Symbole d'unité facultatif"

#: common/models.py:1857
msgid "Definition"
msgstr "Définition"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Définition de l'unité"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Pièce jointe"

#: common/models.py:1933
msgid "Missing file"
msgstr "Fichier manquant"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Lien externe manquant"

#: common/models.py:1971
msgid "Model type"
msgstr "Type de modèle"

#: common/models.py:1972
msgid "Target model type for image"
msgstr "Type de modèle cible pour l'image"

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Sélectionnez un fichier à joindre"

#: common/models.py:1996
msgid "Comment"
msgstr "Commentaire"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Commentaire sur la pièce jointe"

#: common/models.py:2013
msgid "Upload date"
msgstr "Date de téléchargement"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Date de téléchargement du fichier"

#: common/models.py:2018
msgid "File size"
msgstr "Taille du fichier"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Taille du fichier en octets"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Type de modèle non valide spécifié pour la pièce jointe"

#: common/models.py:2077
msgid "Custom State"
msgstr "État personnalisé"

#: common/models.py:2078
msgid "Custom States"
msgstr "États membres de l'Union européenne"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "Ensemble d'états de référence"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "Ensemble d'états étendu à cet état personnalisé"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Clé logique"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Clé logique de l'état qui est égale à cet état personnalisé dans la logique métier"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Valeur"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr "Valeur numérique qui sera enregistrée dans la base de données des modèles"

#: common/models.py:2102
msgid "Name of the state"
msgstr "Nom de l'Etat"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Étiquette"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "Etiquette qui sera affichée dans le frontend"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Couleur"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "Couleur qui sera affichée dans le frontend"

#: common/models.py:2128
msgid "Model"
msgstr "Modèle"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "Modèle cet état est associé à"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "Le modèle doit être sélectionné"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "La clé doit être sélectionnée"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "La clé logique doit être sélectionnée"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "La clé doit être différente de la clé logique"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr "Une classe de statut de référence valide doit être fournie"

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "La clé doit être différente des clés logiques de l'état de référence"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "La clé logique doit se trouver dans les clés logiques de l'état de référence"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "Le nom doit être différent des noms des statuts de référence"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Liste de sélection"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "Listes de sélection"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "Nom de la liste de sélection"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "Description de la liste de sélection"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Verrouillé"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "Cette liste de sélection est-elle verrouillée ?"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "Cette liste de sélection peut-elle être utilisée ?"

#: common/models.py:2255
msgid "Source Plugin"
msgstr "Plug-in source"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr "Plugin qui fournit la liste de sélection"

#: common/models.py:2261
msgid "Source String"
msgstr "Chaîne source"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr "Chaîne facultative identifiant la source utilisée pour cette liste"

#: common/models.py:2271
msgid "Default Entry"
msgstr "Entrée par défaut"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr "Entrée par défaut pour cette liste de sélection"

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Créé le"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr "Date et heure de création de la liste de sélection"

#: common/models.py:2283
msgid "Last Updated"
msgstr "Dernière mise à jour"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr "Date et heure de la dernière mise à jour de la liste de sélection"

#: common/models.py:2318
msgid "Selection List Entry"
msgstr "Entrée de la liste de sélection"

#: common/models.py:2319
msgid "Selection List Entries"
msgstr "Entrées de la liste de sélection"

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr "Liste de sélection à laquelle appartient cette entrée"

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr "Valeur de l'entrée de la liste de sélection"

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr "Étiquette pour l'entrée de la liste de sélection"

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr "Description de l'entrée de la liste de sélection"

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr "Cette entrée de la liste de sélection est-elle active ?"

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "Analyse du code-barres"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Données"

#: common/models.py:2377
msgid "Barcode data"
msgstr "Données du code-barres"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "Utilisateur qui a scanné le code-barres"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Horodatage"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "Date et heure du scan de code-barres"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "Point d'accès à l'URL qui a traité le code-barres"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Contexte"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "Données contextuelles pour la lecture du code-barres"

#: common/models.py:2415
msgid "Response"
msgstr "Réponse"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "Données de réponse provenant de la lecture du code-barres"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Résultat"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "La lecture du code-barres a-t-elle réussi ?"

#: common/models.py:2505
msgid "An error occurred"
msgstr "Une erreur s'est produite"

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr "Message email"

#: common/models.py:2574
msgid "Email Messages"
msgstr "Messages email"

#: common/models.py:2581
msgid "Announced"
msgstr "Annoncé"

#: common/models.py:2583
msgid "Sent"
msgstr "Envoyé"

#: common/models.py:2584
msgid "Failed"
msgstr "Échec"

#: common/models.py:2587
msgid "Delivered"
msgstr "Livré"

#: common/models.py:2595
msgid "Confirmed"
msgstr "Confirmé"

#: common/models.py:2601
msgid "Inbound"
msgstr "Entrant"

#: common/models.py:2602
msgid "Outbound"
msgstr "Sortant"

#: common/models.py:2607
msgid "No Reply"
msgstr "Sans réponse"

#: common/models.py:2608
msgid "Track Delivery"
msgstr "Suivi de livraison"

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr "ID Global"

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr "Identifiant pour ce message (peut être fourni par un système externe)"

#: common/models.py:2633
msgid "Thread ID"
msgstr "ID du sujet de discussion"

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr "Identifiant pour ce fil de message (peut être fourni par un système externe)"

#: common/models.py:2644
msgid "Thread"
msgstr "Fil de discussion"

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr "Fil lié à ce message"

#: common/models.py:2661
msgid "Prioriy"
msgstr "Priorité"

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Clé"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nouveau {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Une nouvelle commande a été créée et vous a été assignée"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} annulé"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Une commande qui vous est assignée a été annulée"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Articles reçus"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Des articles d'un bon de commande ont été reçus"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Les articles ont été reçus dans le cadre d'un ordre de retour"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr ""

#: common/serializers.py:486
msgid "Is Running"
msgstr "En cours d'exécution"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Tâches en attente"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Tâches planifiées"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Tâches échouées"

#: common/serializers.py:519
msgid "Task ID"
msgstr "ID de la tâche"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "ID unique de la tâche"

#: common/serializers.py:521
msgid "Lock"
msgstr "Verrouillé"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Heure verrouillé"

#: common/serializers.py:523
msgid "Task name"
msgstr "Nom de la tâche"

#: common/serializers.py:525
msgid "Function"
msgstr "Fonction"

#: common/serializers.py:525
msgid "Function name"
msgstr "Nom de la fonction"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Arguments"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Arguments tâche"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Mots-clés Arguments"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Mots-clés arguments tâche"

#: common/serializers.py:640
msgid "Filename"
msgstr "Nom du fichier"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Type de modèle"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "L'utilisateur n'a pas le droit de créer ou de modifier des pièces jointes pour ce modèle"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr "La liste de sélection est verrouillée"

#: common/setting/system.py:97
msgid "No group"
msgstr "Pas de groupe"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "L'URL du site est verrouillée par configuration"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Redémarrage nécessaire"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "Un paramètre a été modifié, ce qui nécessite un redémarrage du serveur"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Migration en attente"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Nombre de migrations de base de données en attente"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr "ID de l'instance"

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr "Identifiant unique pour cette instance InvenTree"

#: common/setting/system.py:199
msgid "Announce ID"
msgstr "Annoncer l'ID"

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr "Annoncer l'ID d'instance du serveur dans l'info sur l'état du serveur (non authentifié)"

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Nom de l'instance du serveur"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Chaîne de caractères descriptive pour l'instance serveur"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Utiliser le nom de l'instance"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Utiliser le nom de l’instance dans la barre de titre"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Limiter l'affichage de `about`"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Afficher la modale `about` uniquement aux super-utilisateurs"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Nom de la société"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Nom de société interne"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "URL de base"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "URL de base pour l'instance serveur"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Devise par défaut"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Sélectionnez la devise de base pour les calculs de prix"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Devises supportées"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Liste des codes de devises supportés"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Intervalle de mise à jour des devises"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Fréquence de mise à jour des taux de change (définir à zéro pour désactiver)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "jours"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Plugin de mise à jour de devise"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Plugin de mise à jour des devises à utiliser"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Télécharger depuis l'URL"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Autoriser le téléchargement d'images distantes et de fichiers à partir d'URLs externes"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Limite du volume de téléchargement"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Taille maximale autorisée pour le téléchargement de l'image distante"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "Agent utilisateur utilisé pour télécharger depuis l'URL"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Permettre de remplacer l'agent utilisateur utilisé pour télécharger des images et des fichiers à partir d'URL externe (laisser vide pour la valeur par défaut)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Validation stricte d'URL"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Spécification du schéma nécessaire lors de la validation des URL"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Intervalle de vérification des mises à jour"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "À quelle fréquence vérifier les mises à jour (définir à zéro pour désactiver)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Backup automatique"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Activer le backup automatique de la base de données et des fichiers médias"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Intervalle de sauvegarde automatique"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Spécifiez le nombre de jours entre les sauvegardes automatique"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Intervalle de suppression des tâches"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Les résultats de la tâche en arrière-plan seront supprimés après le nombre de jours spécifié"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Intervalle de suppression du journal d'erreur"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Les logs d'erreur seront supprimés après le nombre de jours spécifié"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Intervalle de suppression du journal de notification"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Les notifications de l'utilisateur seront supprimées après le nombre de jours spécifié"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Support des code-barres"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Activer le support du scanner de codes-barres dans l'interface web"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "Résultats des codes-barres des magasins"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "Stocker les résultats de la lecture du code-barres dans la base de données"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "Scanners de codes-barres Comptage maximal"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "Nombre maximum de résultats de lecture de codes-barres à stocker"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Délai d'entrée du code-barres"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Délai de traitement du code-barres"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Prise en charge de la webcam code-barres"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Autoriser la numérisation de codes-barres via la webcam dans le navigateur"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Code-barres Afficher les données"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Afficher les données du code-barres dans le navigateur sous forme de texte"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "Plugin de génération de codes-barres"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "Plugin à utiliser pour la génération interne de données de code-barres"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Modifications de la pièce"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Activer le champ de modification de la pièce"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "Révision de l'assemblage uniquement"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "N'autoriser les révisions que pour les pièces d'assemblage"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Autoriser la suppression de l'Assemblée"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Permettre la suppression de pièces utilisées dans un assemblage"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "Regex IPN"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Expression régulière pour la correspondance avec l'IPN de la Pièce"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Autoriser les IPN dupliqués"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Permettre à plusieurs pièces de partager le même IPN"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Autoriser l'édition de l'IPN"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Permettre de modifier la valeur de l'IPN lors de l'édition d'une pièce"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Copier les données de la pièce"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Copier les données des paramètres par défaut lors de la duplication d'une pièce"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Copier les données des paramètres de la pièce"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Copier les données des paramètres par défaut lors de la duplication d'une pièce"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Copier les données de test de la pièce"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Copier les données de test par défaut lors de la duplication d'une pièce"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Copier les templates de paramètres de catégorie"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Copier les templates de paramètres de la catégorie lors de la création d'une pièce"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Modèle"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Les pièces sont des templates par défaut"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Les pièces peuvent être assemblées à partir d'autres composants par défaut"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Composant"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Les pièces peuvent être utilisées comme sous-composants par défaut"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Achetable"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Les pièces sont achetables par défaut"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Vendable"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Les pièces sont vendables par défaut"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Les pièces sont traçables par défaut"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtuelle"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Les pièces sont virtuelles par défaut"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Afficher les pièces connexes"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Afficher les pièces connexes à une pièce"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Stock initial"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Permettre la création d'un stock initial lors de l'ajout d'une nouvelle pièce"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Données initiales du fournisseur"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Permettre la création des données initiales du fournisseur lors de l'ajout d'une nouvelle pièce"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Format d'affichage du nom de la pièce"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Format pour afficher le nom de la pièce"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Icône de catégorie par défaut"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Icône par défaut de la catégorie de la pièce (vide signifie aucune icône)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Renforcer les unités des paramètres"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Si des unités sont fournies, les valeurs de paramètre doivent correspondre aux unités spécifiées"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Nombre minimal de décimales"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Nombre minimum de décimales à afficher lors de l'affichage des prix"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Nombre maximal de décimales pour la tarification"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Nombre maximal de décimales à afficher lors du rendu des données de tarification"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Utiliser le prix fournisseur"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Inclure les réductions de prix dans le calcul du prix global"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Remplacer l'historique des achats"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "La tarification historique des bons de commande remplace les réductions de prix des fournisseurs"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Utiliser les prix des articles en stock"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Utiliser les prix des données de stock saisies manuellement pour calculer les prix"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Âge de tarification des articles de stock"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Exclure les articles en stock datant de plus de ce nombre de jours des calculs de prix"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Utiliser les prix variants"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Inclure la tarification variante dans le calcul global des prix"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Variantes actives uniquement"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "N'utiliser que des pièces de variante actives pour calculer le prix de la variante"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Intervalle de regénération des prix"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Nombre de jours avant la mise à jour automatique du prix de la pièce"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Prix internes"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Activer les prix internes pour les pièces"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Substitution du prix interne"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Si disponible, les prix internes remplacent les calculs de la fourchette de prix"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Activer l'impression d'étiquettes"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Activer l'impression d'étiquettes depuis l'interface Web"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "Étiquette image DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Résolution DPI lors de la génération de fichiers image pour fournir aux plugins d'impression d'étiquettes"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Activer les rapports"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Activer la génération de rapports"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Mode Débogage"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Générer des rapports en mode debug (sortie HTML)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Journal des erreurs"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Enregistrer les erreurs qui se produisent lors de la génération de rapports"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Taille de la page"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Taille de page par défaut pour les rapports PDF"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Numéro de Série Universellement Unique"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "Les numéros de série pour les articles en stock doivent être uniques au niveau global"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Supprimer le stock épuisé"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Détermine le comportement par défaut lorsqu'un article de stock est épuisé"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Modèle de code de lot"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Modèle pour générer des codes par défaut pour les articles en stock"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Expiration du stock"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Activer la fonctionnalité d'expiration du stock"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Vendre le stock expiré"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Autoriser la vente de stock expiré"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Délai de péremption du stock"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Nombre de jours pendant lesquels les articles en stock sont considérés comme périmés avant d'expirer"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Construction de stock expirée"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Autoriser la construction avec un stock expiré"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Contrôle de la propriété des stocks"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Activer le contrôle de la propriété sur les emplacements de stock et les articles"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Icône par défaut de l'emplacement du stock"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Icône par défaut de l'emplacement du stock (vide signifie aucune icône)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Afficher les pièces en stock installées"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Affichage des articles en stock installés dans les tableaux de stock"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Vérifier la nomenclature lors de l'installation des articles"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Les articles de stock installés doivent exister dans la nomenclature de la pièce mère"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Autoriser le transfert des produits en rupture de stock"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Permettre le transfert d'articles qui ne sont pas en stock d'un magasin à l'autre"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Modèle de référence de commande de construction"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Modèle requis pour générer le champ de référence de l'ordre de construction"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "Nécessite un Responsable propriétaire"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "Un propriétaire responsable doit être assigné à chaque commande"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "Exiger une partie active"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "Empêcher la création d'un ordre de fabrication pour les pièces inactives"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "Requiert une pièce verrouillée"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "Empêcher la création d'un ordre de fabrication pour les pièces non verrouillées"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "Exiger une nomenclature valide"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Empêcher la création d'un ordre de fabrication si la nomenclature n'a pas été validée"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "Exiger des ordonnances fermées pour les enfants"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "Empêcher l'achèvement de l'ordre de construction jusqu'à ce que tous les ordres d'enfants soient clôturés"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Blocage jusqu'à la réussite des tests"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Empêcher l'achèvement des résultats de la construction jusqu'à ce que tous les tests requis soient réussis"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Activer les retours de commandes"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Activer la fonctionnalité de retour de commande dans l'interface utilisateur"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Modèle de référence de retour de commande"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Modèle requis pour générer le champ de référence de la commande de retour"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Modifier les retours de commandes terminées"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Autoriser la modification des retours après leur enregistrement"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Modèle de référence de bon de commande"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Modèle requis pour générer le champ de référence du bon de commande"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Expédition par défaut du bon de commande"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Activer la création d'expédition par défaut avec les bons de commandes"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Modifier les commandes de vente terminées"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Autoriser la modification des commandes de vente après avoir été expédiées ou complétées"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Marquer les commandes expédiées comme achevées"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Les commandes marquées comme expédiées seront automatiquement complétées, en contournant le statut « expédié »"

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Modèle de référence de commande d'achat"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Modèle requis pour générer le champ de référence de bon de commande"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Modifier les bons de commande terminés"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Autoriser la modification des bons de commande après avoir été expédiés ou complétés"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr "Convertir la monnaie"

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr "Convertir la valeur de l'article dans la devise de base lors de la réception du stock"

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Achat automatique des commandes"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Marquer automatiquement les bons de commande comme terminés lorsque tous les articles de la ligne sont reçus"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Activer les mots de passe oubliés"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Activer la fonction \"Mot de passe oublié\" sur les pages de connexion"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Activer les inscriptions"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Activer l'auto-inscription pour les utilisateurs sur les pages de connexion"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "Activer le SSO"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "Activer le SSO sur les pages de connexion"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "Activer l'inscription SSO"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Activer l'auto-inscription via SSO pour les utilisateurs sur les pages de connexion"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "Activer la synchronisation du groupe SSO"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Permettre la synchronisation des groupes InvenTree avec les groupes fournis par l'IdP"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "Clé du groupe SSO"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "Le nom de l'attribut de revendication de groupe fourni par l'IdP"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "Carte de groupe SSO"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Une correspondance entre les groupes SSO et les groupes InvenTree locaux. Si le groupe local n'existe pas, il sera créé."

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "Supprimer les groupes en dehors de SSO"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Indique si les groupes attribués à l'utilisateur doivent être supprimés s'ils ne sont pas gérés par l'IdP. La désactivation de ce paramètre peut entraîner des problèmes de sécurité"

#: common/setting/system.py:959
msgid "Email required"
msgstr "Email requis"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Exiger que l'utilisateur fournisse un mail lors de l'inscription"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "Saisie automatique des utilisateurs SSO"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Remplir automatiquement les détails de l'utilisateur à partir des données de compte SSO"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "Courriel en double"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Lors de l'inscription, demandez deux fois aux utilisateurs leur mail"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Mot de passe deux fois"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Lors de l'inscription, demandez deux fois aux utilisateurs leur mot de passe"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Domaines autorisés"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Restreindre l'inscription à certains domaines (séparés par des virgules, commençant par @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Grouper sur inscription"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Groupe auquel les nouveaux utilisateurs sont assignés lors de l'enregistrement. Si la synchronisation des groupes SSO est activée, ce groupe n'est défini que si aucun groupe ne peut être attribué par l'IdP."

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "Forcer l'authentification multifacteurs"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Les utilisateurs doivent utiliser l'authentification multifacteurs."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Vérifier les plugins au démarrage"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Vérifier que tous les plugins sont installés au démarrage - activer dans les environnements conteneurs"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Vérifier les mises à jour des plugins"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Activer les vérifications périodiques pour les mises à jour des plugins installés"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "Activer l'intégration d'URL"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Autoriser les plugins à ajouter des chemins URL"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Activer l'intégration de navigation"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Activer les plugins à s'intégrer dans la navigation"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "Activer l'intégration de plugins"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Activer l'intégration de plugin pour ajouter des apps"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Activer l'intégration du planning"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Autoriser les plugins à éxécuter des tâches planifiées"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Activer l'intégration des évènements"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Autoriser les plugins à répondre aux évènements internes"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "Permettre l'intégration de l'interface"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "Permettre aux plugins de s'intégrer dans l'interface utilisateur"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr "Activer les codes de projet"

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr "Activer les codes de projet pour le suivi des projets"

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Exclure les localisations externes"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Période de l'inventaire automatique"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Afficher les noms des utilisateurs"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Afficher les noms complets des utilisateurs au lieu des noms d'utilisateur"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr "Afficher les profils d'utilisateur"

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr "Afficher les profils des utilisateurs sur leur page de profil"

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Activer les données de station de test"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Activer la collecte des données de la station de test pour les résultats de test"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Affichage du libellé en ligne"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Afficher les étiquettes PDF dans le navigateur, au lieu de les télécharger en tant que fichier"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Imprimante d'étiquettes par défaut"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Configurer quelle imprimante d'étiquette doit être sélectionnée par défaut"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Affichage du rapport en ligne"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Afficher les rapports PDF dans le navigateur, au lieu de les télécharger en tant que fichier"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Rechercher de pièces"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Afficher les pièces dans la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Recherche du fournisseur de pièces"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Afficher les pièces du fournisseur dans la fenêtre de prévisualisation de la recherche"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Rechercher les pièces du fabricant"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Afficher les pièces du fabricant dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Masquer les pièces inactives"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Exclure les pièces inactives de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Rechercher des catégories"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Afficher les catégories de pièces dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Rechercher dans le stock"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Afficher les pièces en stock dans la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Cacher les pièces indisponibles"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Exclure les articles en stock qui ne sont pas disponibles de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Chercher des Emplacements"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Afficher les emplacements dans la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Rechercher les entreprises"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Afficher les entreprises dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Rechercher les commandes de construction"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Afficher les commandes de construction dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Rechercher des bons de commande"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Afficher les bons de commande dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Exclure les bons de commande inactifs"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Exclure les commandes d’achat inactives de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Rechercher les bons de commande"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Afficher les bons de commande dans la fenêtre de prévisualisation de la recherche"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Exclure les bons de commande inactives"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Exclure les bons de commande inactifs de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Rechercher les expéditions de commandes clients"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Afficher les envois de commandes dans la fenêtre de prévisualisation de la recherche"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Rechercher les commandes retournées"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Afficher les ordres de retour dans la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Exclure les commandes de retour inactives"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Exclure les ordres de retour inactifs de la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Résultats de l'aperçu de la recherche"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Nombre de résultats à afficher dans chaque section de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Recherche Regex"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Activer les expressions régulières dans les requêtes de recherche"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Recherche de mot complet"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Les requêtes de recherche renvoient des résultats pour des mots entiers"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Notes de recherche"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "Les requêtes de recherche renvoient les résultats correspondant aux notes de l'article"

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "La touche Echap ferme les formulaires"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Utilisez la touche Echap pour fermer les formulaires modaux"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Barre de navigation fixe"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "La position de la barre de navigation est fixée en haut de l'écran"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr "Icônes de navigation"

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr "Afficher les icônes dans la barre de navigation"

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Format de date"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Format préféré pour l'affichage des dates"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr "Afficher le dernier fil d'Ariane"

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr "Afficher la page actuelle dans les fils d'Ariane"

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Recevoir des rapports d'erreur"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Recevoir des notifications en cas d'erreurs du système"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Dernières machines d'impression utilisées"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Sauvegarder les dernières machines d'impression utilisées par un utilisateur"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Aucun type de modèle d'attachement n'est fourni"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Type de modèle de pièce jointe non valide"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Les places minimales ne peuvent être supérieures aux places maximales"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Le nombre maximum de places ne peut être inférieur au nombre minimum de places"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Un domaine vide n'est pas autorisé."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Nom de domaine invalide : {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "La valeur doit être en majuscules"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "La valeur doit être un identifiant de variable valide"

#: company/api.py:141
msgid "Part is Active"
msgstr "La pièce est active"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Le fabricant est actif"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Le fournisseur de la pièce est active"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "La pièce interne est active"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Le fournisseur est actif"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Fabricant"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Société"

#: company/api.py:316
msgid "Has Stock"
msgstr "A du stock"

#: company/models.py:120
msgid "Companies"
msgstr "Entreprises"

#: company/models.py:148
msgid "Company description"
msgstr "Description de la société"

#: company/models.py:149
msgid "Description of the company"
msgstr "Description de la société"

#: company/models.py:155
msgid "Website"
msgstr "Site web"

#: company/models.py:156
msgid "Company website URL"
msgstr "Site Web de la société"

#: company/models.py:162
msgid "Phone number"
msgstr "Numéro de téléphone"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Numéro de téléphone de contact"

#: company/models.py:171
msgid "Contact email address"
msgstr "Adresse e-mail de contact"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Contact"

#: company/models.py:178
msgid "Point of contact"
msgstr "Point de contact"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Lien externe vers les informations de l'entreprise"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Cette entreprise est-elle active ?"

#: company/models.py:203
msgid "Is customer"
msgstr "Le client est-il"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Vendez-vous des objets à cette entreprise?"

#: company/models.py:209
msgid "Is supplier"
msgstr "Le fournisseur est-il"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Est-ce que vous achetez des articles à cette entreprise?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "Le fabricant est-il"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Cette entreprise fabrique-t-elle des pièces?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Devise par défaut utilisée pour cette entreprise"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Adresse"

#: company/models.py:355
msgid "Addresses"
msgstr "Adresses"

#: company/models.py:412
msgid "Select company"
msgstr "Sélectionner une entreprise"

#: company/models.py:417
msgid "Address title"
msgstr "Intitulé de l'adresse"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Titre décrivant la saisie de l'adresse"

#: company/models.py:424
msgid "Primary address"
msgstr "Adresse principale"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Sélectionner comme adresse principale"

#: company/models.py:430
msgid "Line 1"
msgstr "Ligne 1"

#: company/models.py:431
msgid "Address line 1"
msgstr "Adresse"

#: company/models.py:437
msgid "Line 2"
msgstr "Ligne 2"

#: company/models.py:438
msgid "Address line 2"
msgstr "Seconde ligne d'adresse"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "Code postal"

#: company/models.py:451
msgid "City/Region"
msgstr "Ville / Région"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Code postal Ville / Région"

#: company/models.py:458
msgid "State/Province"
msgstr "État / Province"

#: company/models.py:459
msgid "State or province"
msgstr "État ou province"

#: company/models.py:465
msgid "Country"
msgstr "Pays"

#: company/models.py:466
msgid "Address country"
msgstr "Pays"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Notes du livreur"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Instructions pour le livreur"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Notes pour la livraison interne"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Notes internes pour la livraison"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Lien vers les informations de l'adresse (externe)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Pièces du fabricant"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Pièce de base"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Sélectionner une partie"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Sélectionner un fabricant"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "Référence fabricant"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Référence du fabricant"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "URL pour le lien externe de la pièce du fabricant"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Description de la pièce du fabricant"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Paramètre de la pièce du fabricant"

#: company/models.py:635
msgid "Parameter name"
msgstr "Nom du paramètre"

#: company/models.py:642
msgid "Parameter value"
msgstr "Valeur du paramètre"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Unités"

#: company/models.py:650
msgid "Parameter units"
msgstr "Unités du paramètre"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Les unités d'emballage doivent être compatibles avec les unités de base"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Les unités d'emballage doivent être supérieures à zéro"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "La pièce du fabricant liée doit faire référence à la même pièce de base"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Fournisseur"

#: company/models.py:829
msgid "Select supplier"
msgstr "Sélectionner un fournisseur"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Unité de gestion des stocks des fournisseurs"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Cette partie du fournisseur est-elle active ?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Sélectionner un fabricant"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "Lien de la pièce du fournisseur externe"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Description de la pièce du fournisseur"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Note"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "coût de base"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Frais minimums (par exemple frais de stock)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Conditionnement"

#: company/models.py:892
msgid "Part packaging"
msgstr "Conditionnement de l'article"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Nombre de paquet"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Quantité totale fournie dans un emballage unique. Laisser vide pour les articles individuels."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "plusieurs"

#: company/models.py:919
msgid "Order multiple"
msgstr "Commande multiple"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Quantité disponible auprès du fournisseur"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Disponibilité mise à jour"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Date de dernière mise à jour des données de disponibilité"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Rupture de prix pour le fournisseur"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr "Renvoie la représentation sous forme de chaîne de caractères de l'adresse principale. Cette propriété existe pour des raisons de compatibilité ascendante."

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Devise par défaut utilisée pour ce fournisseur"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Nom de l'entreprise"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "En Stock"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr "Une erreur s'est produite lors de l'exportation des données"

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr "Le plugin d'exportation de données renvoie un format de données incorrect"

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Format d'exportation"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "Sélectionner le format du fichier d'exportation"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Plugin d'exportation"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "Sélectionner le plugin d'exportation"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Informations complémentaires sur le statut de ce poste"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Touche d'état personnalisée"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Sur mesure"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Classe"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Valeurs"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Placé"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Code d'état invalide"

#: importer/models.py:73
msgid "Data File"
msgstr "Fichier de données"

#: importer/models.py:74
msgid "Data file to import"
msgstr "Fichier de données à importer"

#: importer/models.py:83
msgid "Columns"
msgstr "Colonnes"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr "Type de modèle cible pour cette session d'importation"

#: importer/models.py:96
msgid "Import status"
msgstr "Statut de l'importation"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Champs par défaut"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "Remplacements de champs"

#: importer/models.py:120
msgid "Field Filters"
msgstr "Filtres de terrain"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "Certains champs obligatoires n'ont pas été cartographiés"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "La colonne est déjà associée à un champ de la base de données"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "Le champ est déjà associé à une colonne de données"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "Le mappage des colonnes doit être lié à une session d'importation valide"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "La colonne n'existe pas dans le fichier de données"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "Le champ n'existe pas dans le modèle cible"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "Le champ sélectionné est en lecture seule"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "Session d'importation"

#: importer/models.py:471
msgid "Field"
msgstr "Champ d'application"

#: importer/models.py:473
msgid "Column"
msgstr "Colonne"

#: importer/models.py:542
msgid "Row Index"
msgstr "Index des lignes"

#: importer/models.py:545
msgid "Original row data"
msgstr "Données de la ligne d'origine"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Erreurs"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Valide"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Format de fichier de données non pris en charge"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Échec de l'ouverture du fichier de données"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Dimensions du fichier de données non valides"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "Valeurs par défaut des champs invalides"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "Remplacements de champs non valides"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "Filtres de champ non valides"

#: importer/serializers.py:177
msgid "Rows"
msgstr "Rangs"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Liste des identifiants de ligne à accepter"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Pas de rangs fournis"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "La ligne n'appartient pas à cette session"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "La ligne contient des données non valides"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "La rangée a déjà été complétée"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Initialisation"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Mappage des colonnes"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Importation de données"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Traitement des données"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Le fichier de données dépasse la taille maximale autorisée"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Le fichier de données ne contient pas d'en-tête"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Le fichier de données contient trop de colonnes"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Le fichier de données contient trop de lignes"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "La valeur doit être un objet dictionnaire valide"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Copies"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Nombre de copies à imprimer pour chaque étiquette"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Connecté"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Inconnu"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Impression"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Aucun média"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Bourrage de papier"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Déconnecté"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Imprimante Etiquette"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Impression directe des étiquettes pour divers articles."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Emplacement Imprimante"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Porter de l'imprimante sur un emplacement spécifique"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Nom de la machine"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Machine Type"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Type de machine"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Pilote"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Pilote utilisé pour la machine"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Les machines peuvent être inactivées"

#: machine/models.py:95
msgid "Driver available"
msgstr "Pilote disponible"

#: machine/models.py:100
msgid "No errors"
msgstr "Aucune erreur"

#: machine/models.py:105
msgid "Initialized"
msgstr "Initialisé"

#: machine/models.py:117
msgid "Machine status"
msgstr "Statut de la machine"

#: machine/models.py:145
msgid "Machine"
msgstr "Machine"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Configuration de la machine"

#: machine/models.py:162
msgid "Config type"
msgstr "Type de configuration"

#: order/api.py:121
msgid "Order Reference"
msgstr "Référence de commande"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "Remarquable"

#: order/api.py:165
msgid "Has Project Code"
msgstr "A le code du projet"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Créé par"

#: order/api.py:183
msgid "Created Before"
msgstr "Créé avant"

#: order/api.py:187
msgid "Created After"
msgstr "Créé après"

#: order/api.py:191
msgid "Has Start Date"
msgstr "A la date de début"

#: order/api.py:199
msgid "Start Date Before"
msgstr "Date de début Avant"

#: order/api.py:203
msgid "Start Date After"
msgstr "Date de début Après"

#: order/api.py:207
msgid "Has Target Date"
msgstr "A une date cible"

#: order/api.py:215
msgid "Target Date Before"
msgstr "Date cible Avant"

#: order/api.py:219
msgid "Target Date After"
msgstr "Date cible Après"

#: order/api.py:270
msgid "Has Pricing"
msgstr "Possède un Tarif"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr "Terminé avant"

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "Terminé après"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Commande"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "Commande Complétée"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Pièces Internes"

#: order/api.py:578
msgid "Order Pending"
msgstr "Commande En Attente"

#: order/api.py:958
msgid "Completed"
msgstr "Terminé"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "Fait l'objet d'une expédition"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Commande d’achat"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Commandes"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Retour de commande"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Prix Total"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Prix total pour cette commande"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Devise de la commande"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Devise de cette commande (laisser vide pour utiliser la devise par défaut de l'entreprise)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr "Cette commande est verrouillée et ne peut être modifiée"

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "Le contact ne correspond pas à l'entreprise sélectionnée"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr "La date de début doit être antérieure à la date cible"

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Description de la commande (facultatif)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Sélectionner le code du projet pour cette commande"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Lien vers une page externe"

#: order/models.py:458
msgid "Start date"
msgstr "Date de début"

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr "Date de début prévue pour cette commande"

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Date Cible"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Date prévue pour la livraison de la commande. La commande sera en retard après cette date."

#: order/models.py:487
msgid "Issue Date"
msgstr "Date d'émission"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Date d'émission de la commande"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Utilisateur ou groupe responsable de cette commande"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Point de contact pour cette commande"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Adresse de l'entreprise pour cette commande"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Référence de la commande"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "État"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Statut de la commande d'achat"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Société de laquelle les articles sont commandés"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Référence du fournisseur"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Code de référence de la commande fournisseur"

#: order/models.py:654
msgid "received by"
msgstr "reçu par"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Date à laquelle la commande a été complété"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Destination"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr "Destination des articles reçus"

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Le fournisseur de la pièce doit correspondre au fournisseur de la commande"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "Le poste ne correspond pas au bon de commande"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "La quantité doit être un nombre positif"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Client"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Société à laquelle les articles sont vendus"

#: order/models.py:1318
msgid "Sales order status"
msgstr "Statut de la commande client"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Référence client "

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Code de référence de la commande du client"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Nom de l’expédition"

#: order/models.py:1343
msgid "shipped by"
msgstr "expédié par"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "La commande est déjà terminée"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "La commande est déjà annulée"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Seule une commande ouverte peut être marquée comme complète"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "La commande ne peut pas être terminée car il y a des envois incomplets"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "L'ordre ne peut pas être achevé car les allocations sont incomplètes"

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "L'ordre ne peut pas être complété car il y a des postes incomplets"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr "La commande est verrouillée et ne peut être modifiée"

#: order/models.py:1711
msgid "Item quantity"
msgstr "Nombre d'élement"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Référence du poste"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Notes sur les postes"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Date cible pour ce poste (laisser vide pour utiliser la date cible de la commande)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Description du poste (facultatif)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Contexte supplémentaire pour cette ligne"

#: order/models.py:1788
msgid "Unit price"
msgstr "Prix unitaire"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "Poste du bon de commande"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "La pièce du fournisseur doit correspondre à celle du fournisseur"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr "Les ordres de fabrication ne peuvent être liées qu'à des pièces d'assemblage"

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr "Les pièces d'ordre de fabrication doivent correspondre la pièce d'objet"

#: order/models.py:1884
msgid "Supplier part"
msgstr "Pièce fournisseur"

#: order/models.py:1891
msgid "Received"
msgstr "Reçu"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Nombre d'éléments reçus"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Prix d'achat"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Prix d'achat unitaire"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "Ligne supplémentaire du bon de commande"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "Poste de commande client"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "La pièce virtuelle ne peut pas être affectée à une commande"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Seules les pièces vendues peuvent être attribuées à une commande"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Prix de vente"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Prix de vente unitaire"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Expédié"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Quantité expédiée"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "Envoi de la commande client"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Date d'expédition"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Date de Livraison"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Date de livraison de l'envoi"

#: order/models.py:2221
msgid "Checked By"
msgstr "Vérifié par"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Utilisateur qui a vérifié cet envoi"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Envoi"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Numéro d'expédition"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "N° de suivi"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Information de suivi des colis"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "N° de facture"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Numéro de référence de la facture associée"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "Le colis a déjà été envoyé"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "L'expédition n'a pas d'articles en stock alloués"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "Ligne supplémentaire de commande client"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "Affectation des commandes clients"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "L'article de stock n'a pas été assigné"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Impossible d'allouer l'article en stock à une ligne avec une autre pièce"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Impossible d'allouer le stock à une ligne sans pièce"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "La quantité d'allocation ne peut pas excéder la quantité en stock"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "La quantité doit être égale à 1 pour un article de stock sérialisé"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "La commande client ne correspond pas à l'expédition"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "L'envoi ne correspond pas à la commande client"

#: order/models.py:2451
msgid "Line"
msgstr "Ligne"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Référence de l'expédition de la commande client"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Article"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Sélectionner l'article de stock à affecter"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Saisir la quantité d'allocation de stock"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Retour Référence de la commande"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Entreprise à l'origine du retour des articles"

#: order/models.py:2625
msgid "Return order status"
msgstr "Statut du retour de commande"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "Poste de l'ordre de retour"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr "L'article en stock doit être spécifié"

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr "La quantité retournée dépasse la quantité en stock"

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr "La quantité retournée doit être supérieure à zéro"

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr "Quantité non valide pour un article de stock sérialisé"

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Sélectionner l'article à retourner par le client"

#: order/models.py:2910
msgid "Received Date"
msgstr "Date de réception"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "La date de réception de cet article en retour"

#: order/models.py:2923
msgid "Outcome"
msgstr "Résultats"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "Résultat pour ce poste"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "Coût associé au retour ou à la réparation de ce poste"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr "Ordre de retour Ligne supplémentaire"

#: order/serializers.py:90
msgid "Order ID"
msgstr "ID de commande"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "ID de l'ordre à dupliquer"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "Copier des lignes"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr "Copier les postes de l'ordre original"

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr "Copier les lignes supplémentaires"

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr "Copier les postes supplémentaires de l'ordre original"

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Postes de travail"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "Lignes achevées"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "Duplicata de commande"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr "Spécifier les options de duplication de cette commande"

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "ID de commande invalide"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Nom du fournisseur"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "La commande ne peut pas être annulée"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Permettre la clôture d'une commande avec des postes incomplets"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "La commande comporte des postes incomplets"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "La commande n'est pas ouverte"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "Tarification automobile"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Calculer automatiquement le prix d'achat sur la base des données de pièces du fournisseur"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Devise du prix d'achat"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Fusionner des éléments"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Fusionner en un seul poste les éléments ayant la même partie, la même destination et la même date cible"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "Unité de gestion des stocks"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Numéro de pièce interne"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "Nom de la pièce interne"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "La pièce du fournisseur doit être spécifiée"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "Le bon de commande doit être spécifié"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "Le fournisseur doit correspondre au bon de commande"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "Le bon de commande doit correspondre au fournisseur"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Poste"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Sélectionner le lieu de destination des envois reçus"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Saisir le code de lot pour les articles de stock entrant"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Date d'expiration"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr "Saisir la date d'expiration des articles de stock entrant"

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Entrez les numéros de série pour les articles de stock entrants"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr "Remplacer les informations d'emballage pour les articles en stock entrants"

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr "Note supplémentaire pour les articles en stock entrant"

#: order/serializers.py:826
msgid "Barcode"
msgstr "Code-barres"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Code-barres scanné"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Le code-barres est déjà utilisé"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Les postes doivent être fournis"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "L'emplacement de la destination doit être spécifié"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "Les valeurs de code-barres fournies doivent être uniques"

#: order/serializers.py:1066
msgid "Shipments"
msgstr "Envois"

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Envois terminés"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Devise du prix de vente"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr "Postes alloués"

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Aucun détail sur l'expédition n'est fourni"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "Le poste n'est pas associé à cette commande"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "La quantité doit être positive"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Entrez les numéros de série à allouer"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "L'envoi a déjà été effectué"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "L'envoi n'est pas associé à cette commande"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Aucune correspondance trouvée pour les numéros de série suivants"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "Les numéros de série suivants sont indisponibles"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "Poste de commande de retour"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "Le poste ne correspond pas à l'ordre de retour"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "Le poste a déjà été reçu"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "Les articles ne peuvent être reçus que pour des commandes en cours"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr "Quantité à retourner"

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "Devise du prix de la ligne"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Perdu"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Retourné"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "En Cours"

#: order/status_codes.py:105
msgid "Return"
msgstr "Retour"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Réparer"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Remplacer"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Remboursement"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Refuser"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Bon de commande en souffrance"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Le bon de commande {po} est maintenant en retard"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Commande en souffrance"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "La commande {so} est maintenant en retard"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr "Ordre de retour en retard"

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr "L'ordre de retour {ro} est maintenant en retard"

#: part/api.py:111
msgid "Starred"
msgstr "Étoilé"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "Filtrer par catégories étoilées"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Profondeur"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "Filtrer par profondeur de catégorie"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "Premier niveau"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "Filtrer par catégories de premier niveau"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "Cascade"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "Inclure les sous-catégories dans les résultats filtrés"

#: part/api.py:185
msgid "Parent"
msgstr "Parent"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Filtrer par catégorie de parents"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "Exclure les sous-catégories de la catégorie spécifiée"

#: part/api.py:434
msgid "Has Results"
msgstr "A des résultats"

#: part/api.py:660
msgid "Is Variant"
msgstr "Est variante"

#: part/api.py:668
msgid "Is Revision"
msgstr "Est la révision"

#: part/api.py:678
msgid "Has Revisions"
msgstr "A des révisions"

#: part/api.py:859
msgid "BOM Valid"
msgstr "Nomenclature valide"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "La pièce d'assemblage est testable"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "Le composant est testable"

#: part/api.py:1576
msgid "Uses"
msgstr "Utilise"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Catégorie de composant"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Catégories de composants"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Emplacement par défaut"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Emplacement par défaut des pièces de cette catégorie"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Structurel"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Les pièces ne peuvent pas être directement affectées à une catégorie structurelle, mais peuvent être affectées à des catégories enfantines."

#: part/models.py:134
msgid "Default keywords"
msgstr "Mots-clés par défaut"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Mots-clés par défaut pour les pièces de cette catégorie"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Icône"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Icône (facultatif)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Vous ne pouvez pas rendre cette catégorie de pièces structurelle car certaines pièces lui sont déjà affectées !"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Pièces"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "Impossible de supprimer cette partie car elle est verrouillée"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Impossible de supprimer cette partie car elle est toujours active"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Impossible de supprimer cette pièce car elle est utilisée dans un assemblage"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "La partie \"{self}\" ne peut pas être utilisée dans la nomenclature de \"{parent}\" (récursif)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "La partie \"{parent}\" est utilisée dans la nomenclature de \"{self}\" (récursif)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "L'IPN doit correspondre au modèle de regex {pattern}"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "Une partie ne peut pas être une révision d'elle-même"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Impossible d'effectuer une révision d'une partie qui est déjà une révision"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "Le code de révision doit être spécifié"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "Les révisions ne sont autorisées que pour les pièces d'assemblage"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "Impossible d'effectuer une révision d'un modèle de pièce"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "La partie parentale doit pointer vers le même modèle"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Il existe déjà un article en stock avec ce numéro de série"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "IPN dupliqué non autorisé dans les paramètres de la pièce"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "La révision de la pièce existe déjà en double."

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Une pièce avec ce nom, IPN et révision existe déjà."

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Les pièces ne peuvent pas être affectées à des catégories de pièces structurelles !"

#: part/models.py:1051
msgid "Part name"
msgstr "Nom de l'article"

#: part/models.py:1056
msgid "Is Template"
msgstr "Est un modèle"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Cette pièce est-elle une pièce modèle ?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Cette pièce est-elle une variante d'une autre pièce ?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Variante de"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Description de la pièce (facultatif)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Mots-clés"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Les mots-clés partiels pour améliorer la visibilité dans les résultats de recherche"

#: part/models.py:1093
msgid "Part category"
msgstr "Catégorie de la pièce"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Numéro de révision ou de version de la pièce"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Révision"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "Cette partie est-elle une révision d'une autre partie ?"

#: part/models.py:1119
msgid "Revision Of"
msgstr "Révision de"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Où cet article est-il normalement stocké ?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Fournisseur par défaut"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Pièce du fournisseur par défaut"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Expiration par défaut"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Délai d'expiration (en jours) pour les articles en stock de cette pièce"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Stock Minimum"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Niveau de stock minimum autorisé"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Unités de mesure pour cette partie"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Cette pièce peut-elle être fabriquée à partir d'autres pièces ?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Cette pièce peut-elle être utilisée pour construire d'autres pièces ?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Cette partie dispose-t-elle d'un suivi pour les articles uniques ?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "Des résultats de tests peuvent-ils être enregistrés pour cette pièce ?"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Cette pièce peut-elle être achetée auprès de fournisseurs externes ?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Cette pièce peut-elle être vendue aux clients ?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Est-ce que cette pièce est active ?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "Les parties verrouillées ne peuvent pas être modifiées"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "S'agit-il d'un élément virtuel, tel qu'un logiciel ou une licence ?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "Somme de contrôle de la nomenclature"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Somme de contrôle de la nomenclature enregistrée"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "Nomenclature vérifiée par"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "Date de vérification de la nomenclature"

#: part/models.py:1312
msgid "Creation User"
msgstr "Création Utilisateur"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Propriétaire responsable de cette pièce"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Ventes multiples"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Devise utilisée pour cacher les calculs de prix"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Coût minimum de la nomenclature"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Coût minimal des composants"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Coût maximal de la nomenclature"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Coût maximal des composants"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Coût d'achat minimum"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Coût d'achat historique minimum"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Coût d'achat maximum"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Coût d'achat historique maximum"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Prix interne minimum"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Coût minimum basé sur des ruptures de prix internes"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Prix interne maximum"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Coût maximum basé sur les écarts de prix internes"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Prix minimum du fournisseur"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Prix minimum des pièces provenant de fournisseurs externes"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Prix maximum du fournisseur"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Prix maximum des pièces provenant de fournisseurs externes"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Coût minimum de la variante"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Calcul du coût minimum des pièces de la variante"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Coût maximal de la variante"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Calcul du coût maximal des pièces de la variante"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Coût minimal"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Remplacer le coût minimum"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Coût maximal"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Dépassement du coût maximal"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Calcul du coût minimum global"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Calcul du coût maximum global"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Prix de vente minimum"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Prix de vente minimum basé sur des ruptures de prix"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Prix de vente maximum"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Prix de vente maximum en fonction des écarts de prix"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Coût minimum de vente"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Prix de vente historique minimum"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Coût de vente maximum"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Prix de vente historique maximum"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Partie pour l'inventaire"

#: part/models.py:3444
msgid "Item Count"
msgstr "Nombre d'articles"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Nombre d'entrées individuelles au moment de l'inventaire"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Stock total disponible au moment de l'inventaire"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Date"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Date de l'inventaire"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Coût minimum du stock"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Coût minimum estimé des stocks disponibles"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Coût maximal du stock"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Coût maximum estimé des stocks disponibles"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "Vente de pièces détachées Prix cassé"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "Modèle de test partiel"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Le nom du modèle n'est pas valide - il doit comporter au moins un caractère alphanumérique"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "Les choix doivent être uniques"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "Les modèles de test ne peuvent être créés que pour les parties testables"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "Un modèle de test avec la même clé existe déjà pour la partie"

#: part/models.py:3684
msgid "Test Name"
msgstr "Nom de test"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Entrez un nom pour le test"

#: part/models.py:3691
msgid "Test Key"
msgstr "Clé de test"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "Clé simplifiée pour le test"

#: part/models.py:3699
msgid "Test Description"
msgstr "Description du test"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Saisir la description de ce test"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Activé"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "Ce test est-il activé ?"

#: part/models.py:3709
msgid "Required"
msgstr "Requis"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Ce test est-il obligatoire pour passer l'examen ?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Valeur requise"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Ce test nécessite-t-il une valeur lors de l'ajout d'un résultat de test ?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Nécessite une pièce jointe"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Ce test nécessite-t-il un fichier joint lors de l'ajout d'un résultat de test ?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "Choix"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "Choix valables pour ce test (séparés par des virgules)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "Modèle de paramètre de pièce"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "Les paramètres des cases à cocher ne peuvent pas avoir d'unités"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "Les paramètres des cases à cocher ne peuvent pas comporter de choix"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "Le nom du modèle de paramètre doit être unique"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Nom du paramètre"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Unités physiques pour ce paramètre"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Description des paramètres"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Case à cocher"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Ce paramètre est-il une case à cocher ?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Choix valables pour ce paramètre (séparés par des virgules)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr "Liste de sélection pour ce paramètre"

#: part/models.py:3931
msgid "Part Parameter"
msgstr "Partie Paramètre"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "Le paramètre ne peut pas être modifié - la pièce est verrouillée"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Choix incorrect pour la valeur du paramètre"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Partie parentale"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Modèle de paramètre"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Valeur du paramètre"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Champ de notes facultatif"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "Catégorie de pièce Modèle de paramètre"

#: part/models.py:4176
msgid "Default Value"
msgstr "Valeur par Défaut"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Valeur par défaut du paramètre"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "L'article de nomenclature ne peut pas être modifié - l'assemblage est verrouillé"

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "Le poste de nomenclature ne peut pas être modifié - l'assemblage de la variante est verrouillé"

#: part/models.py:4363
msgid "Select parent part"
msgstr "Sélectionner la partie parentale"

#: part/models.py:4373
msgid "Sub part"
msgstr "Sous-partie"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Sélectionner la pièce à utiliser dans la nomenclature"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "Quantité de nomenclature pour ce poste de nomenclature"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Ce poste de nomenclature est facultatif"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Ce poste de nomenclature est consommable (il n'est pas suivi dans les ordres de fabrication)."

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "Référence du poste de nomenclature"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "Notes sur les postes de nomenclature"

#: part/models.py:4451
msgid "Checksum"
msgstr "Somme de contrôle"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "Somme de contrôle de la ligne de nomenclature"

#: part/models.py:4457
msgid "Validated"
msgstr "Validée"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Ce poste de nomenclature a été validé"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Obtient l'héritage"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Ce poste de nomenclature est hérité des nomenclatures des composants variants"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Les postes de stock pour les composants variants peuvent être utilisés pour ce poste de nomenclature"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "La quantité doit être un nombre entier pour les pièces pouvant être suivies"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "La sous-partie doit être spécifiée"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "Remplacement d'un poste de nomenclature"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "La pièce de remplacement ne peut pas être identique à la pièce maîtresse"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Poste de nomenclature parent"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Pièce de rechange"

#: part/models.py:4798
msgid "Part 1"
msgstr "Première partie"

#: part/models.py:4806
msgid "Part 2"
msgstr "Partie 2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "Sélectionner une partie connexe"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr "Note pour cette relation"

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Il n'est pas possible de créer une relation entre une pièce et elle-même"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "Une relation en double existe déjà"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "Catégorie de parents"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "Catégorie de pièce mère"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Sous-catégories"

#: part/serializers.py:202
msgid "Results"
msgstr "Résultats"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "Nombre de résultats enregistrés par rapport à ce modèle"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Devise d'achat de l'item"

#: part/serializers.py:275
msgid "File is not an image"
msgstr "Le fichier n'est pas une image"

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "Nombre de pièces utilisant ce modèle"

#: part/serializers.py:480
msgid "Original Part"
msgstr "Partie originale"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Sélectionner la partie originale à dupliquer"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Copier l'image"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Copier l'image à partir de la partie originale"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Copier la nomenclature"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Copie de la nomenclature de la pièce originale"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Copier les paramètres"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Copie des données de paramètres de la pièce d'origine"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Notes sur la copie"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Copier les notes de la partie originale"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr "Test Copie"

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Quantité de stock initial"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Indiquer la quantité de stock initiale pour cette pièce. Si la quantité est égale à zéro, aucun stock n'est ajouté."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Emplacement initial du stock"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Spécifier l'emplacement du stock initial pour cette pièce"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Sélectionner le fournisseur (ou laisser en blanc pour passer)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Sélectionner le fabricant (ou laisser en blanc pour ignorer)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Numéro de pièce du fabricant"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "L'entreprise sélectionnée n'est pas un fournisseur valide"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "L'entreprise sélectionnée n'est pas un fabricant valide"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "La pièce du fabricant correspondant à ce MPN existe déjà"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "La pièce du fournisseur correspondant à cette UGS existe déjà"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Nom catégorie"

#: part/serializers.py:936
msgid "Building"
msgstr "Construction"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr "Quantité de cette pièce actuellement en production"

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr "Quantité exceptionnelle de cette pièce sont planifié à la fabrication"

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Éléments en stock"

#: part/serializers.py:968
msgid "Revisions"
msgstr "Révisions"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Fournisseurs"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Stock total"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "Stock non attribué"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "Variante Stock"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Dupliquer une pièce"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Copier les données initiales d'une autre partie"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Stock initial"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Créer une pièce avec une quantité de stock initiale"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Informations sur le fournisseur"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Ajouter les informations initiales du fournisseur pour cette pièce"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Copier les paramètres de la catégorie"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Copier les modèles de paramètres de la catégorie de pièces sélectionnée"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Image existante"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "Nom de fichier d'une image de pièce existante"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "Le fichier image n'existe pas"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Valider l'ensemble de la nomenclature"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Peut construire"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr "Nécessaire pour fabrication"

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr "Alloué à la fabrication"

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr "Nécessaire pour les commandes"

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr "Alloué aux commandes"

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Prix Minimum"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "Remplacer la valeur calculée pour le prix minimum"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "Prix minimum monnaie"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Prix Maximum"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "Remplacer la valeur calculée pour le prix maximum"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "Devise du prix maximum"

#: part/serializers.py:1498
msgid "Update"
msgstr "Mise à jour"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Mise à jour des prix pour cette pièce"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Impossible de convertir les devises fournies en {default_currency}"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "Le prix minimum ne doit pas être supérieur au prix maximum"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "Le prix maximum ne doit pas être inférieur au prix minimum"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr "Sélectionner l'assemblage parent"

#: part/serializers.py:1716
msgid "Select the component part"
msgstr "Sélectionner le composant"

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Sélectionner la pièce à partir de laquelle copier la nomenclature"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Supprimer les données existantes"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Supprimer les postes de nomenclature existants avant de les copier"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Inclure l'héritage"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Inclure les éléments de nomenclature hérités des pièces modélisées"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Sauter les lignes non valides"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Activez cette option pour ignorer les lignes non valides"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Copier les pièces de remplacement"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Copie de pièces de rechange en cas de duplication de postes de nomenclature"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Notification de stock faible"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Le stock disponible pour {part.name}, est tombé en dessous du niveau minimum configuré"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr "Intégré"

#: plugin/api.py:92
msgid "Mandatory"
msgstr "Obligatoire"

#: plugin/api.py:107
msgid "Sample"
msgstr "Échantillon"

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Installé"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "Le plugin ne peut pas être supprimé car il est actuellement actif"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Aucune action spécifiée"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Aucune action correspondante trouvée"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Aucune correspondance trouvée pour les données du code-barres"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Correspondance trouvée pour les données du code-barres"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Le modèle n'est pas pris en charge"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "L'instance de modèle n'a pas été trouvée"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Le code-barres correspond à l'article existant"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Aucune donnée correspondante n'a été trouvée"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Aucune pièce de fournisseur correspondante n'a été trouvée"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Plusieurs pièces de fournisseurs correspondantes trouvées"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "Aucun plugin correspondant n'a été trouvé pour les données de code-barres"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Pièce du fournisseur assortie"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "L'article a déjà été reçu"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "Pas de correspondance de plugin pour le code-barres du fournisseur"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Plusieurs postes correspondants trouvés"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Aucun poste correspondant n'a été trouvé"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Aucun ordre de vente n'a été fourni"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Le code-barres ne correspond pas à un article de stock existant"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Le poste de stock ne correspond pas au poste individuel"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Stock disponible insuffisant"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Article de stock attribué à la commande client"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Pas assez d'informations"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "Trouvé l'article correspondant"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "La pièce du fournisseur ne correspond pas au poste"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "Le poste est déjà clôturé"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Informations complémentaires requises pour l'obtention d'un poste budgétaire"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Poste de commande reçu"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "Échec de la réception d'un poste"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Données du code-barres scanné"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Nom du modèle pour générer un code-barres"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "Clé primaire d'un modèle objet pour générer un code-barres pour"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Ordre d'achat pour attribuer les articles contre"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr "La commande n'est pas ouverte"

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "Fournisseur pour recevoir des articles de"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Ordre d'achat pour recevoir des articles"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "La commande n'a pas été passée"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Lieu de réception des articles"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Impossible de sélectionner un emplacement structurel"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr "Poste de la commande d'achat sur lequel les articles doivent être réceptionnés"

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr "Affectation automatique des articles en stock à la commande d'achat"

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Commande client à laquelle attribuer des articles"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr "La commande client n'est pas ouverte"

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Poste de commande client sur lequel imputer les articles"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Expédition d'une commande client à laquelle affecter des articles"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "L'envoi a déjà été livré"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Quantité à allouer"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Échec de l'impression de l'étiquette"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "Erreur de rendu de l'étiquette au format PDF"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "Erreur de rendu de l'étiquette en HTML"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Aucun élément fourni pour l'impression"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Nom de l'extension"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Type de fonctionnalité"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Étiquette des caractéristiques"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Titre de la fonctionnalité"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Description de la fonctionnalité"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Icône de fonctionnalité"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Options de fonctionnalité"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Contexte de la fonctionnalité"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Source de la fonctionnalité (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "Codes-barres InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Prise en charge native des codes-barres"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "Contributeurs d'InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "Format de code-barres interne"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "Sélectionner un format de code-barres interne"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "Codes-barres JSON (lisibles par l'homme)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "Codes-barres courts (espace optimisé)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "Préfixe du code-barres abrégé"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "Personnaliser le préfixe utilisé pour les codes-barres courts, peut être utile pour les environnements avec plusieurs instances InvenTree"

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr "Niveaux"

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr "Données sur les stocks"

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr "Inclure les données relatives au stock de pièces"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr "Données de tarification"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr "Inclure les données relatives au prix des pièces"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr "Données du fournisseur"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr "Inclure les données du fournisseur"

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr "Données du fabricant"

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr "Inclure les données du fabricant"

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr "Données sur les suppléants"

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr "Inclure les données des pièces de rechange"

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr "Données de paramètres"

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr "Inclure les données des paramètres de la pièce"

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr "Exportateur de nomenclatures à plusieurs niveaux"

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr "Prise en charge de l'exportation de nomenclatures multiniveaux"

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr "Niveau de la nomenclature"

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr "Substituer {n}"

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr "Fournisseur {n}"

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr "Fournisseur {n} UGS"

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr "Fournisseur {n} MPN"

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr "Fabricant {n}"

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr "Fabricant {n} MPN"

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr "Exporteur générique d'InvenTree"

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr "Prise en charge de l'exportation de données à partir d'InvenTree"

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr "Paramètre de la pièce Exportateur"

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr "Exportateur de données de paramètres de pièces"

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "URL du webhook entrant de Slack"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "URL utilisée pour envoyer des messages à un canal Slack"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Ouvrir le lien"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "Le bureau de change InvenTree"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Intégration du taux de change par défaut"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr "Notifications de pièces"

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr "Informer les utilisateurs des modifications apportées aux pièces"

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr "Envoyer des notifications"

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr "Envoi de notifications aux utilisateurs abonnés en cas de modification d'un élément"

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr "Notification de changement de pièce"

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr "La partie `{part.name}` a été déclenchée par un événement `{part_action}`"

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "Imprimante d'étiquettes PDF InvenTree"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Prise en charge native de l'impression d'étiquettes au format PDF"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Mode débogage"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Activer le mode débogage - renvoie du HTML brut au lieu du PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "Imprimante d'étiquettes InvenTree"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "Fournit un support pour l'impression à l'aide d'une machine"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "dernière utilisation"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "Paramètres"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Taille de la page pour la feuille d'étiquettes"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Sauter les étiquettes"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Sauter ce nombre d'étiquettes lors de l'impression de feuilles d'étiquettes"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Frontière"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "Imprimer une bordure autour de chaque étiquette"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Paysage"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "Imprimer la feuille d'étiquettes en mode paysage"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr "Marge de page"

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr "Marge autour de la page en mm"

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "Imprimante de feuilles d'étiquettes InvenTree"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "Regroupement de plusieurs étiquettes sur une seule feuille"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "L'étiquette est trop grande pour la taille de la page"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "Aucune étiquette n'a été générée"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "Intégration des fournisseurs - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Prise en charge de la lecture des codes-barres DigiKey"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "Le fournisseur qui agit en tant que \"DigiKey\"."

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "Intégration des fournisseurs - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "Permet de scanner les codes-barres LCSC"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "Le fournisseur qui agit en tant que \"LCSC"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "Intégration des fournisseurs - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "Prise en charge de la lecture des codes-barres Mouser"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "Le fournisseur qui joue le rôle de \"Mouser\"."

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "Intégration des fournisseurs - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "Prise en charge de la lecture des codes-barres TME"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "Le fournisseur qui agit en tant que \"TME"

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "Seuls les membres du personnel peuvent administrer les plugins"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "L'installation du plugin est désactivée"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "Installation réussie du plugin"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Installation du plugin dans {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "Le plugin n'a pas été trouvé dans le registre"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "Le plugin n'est pas un plugin packagé"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "Le nom du paquet du plugin n'a pas été trouvé"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "La désinstallation des plugins est désactivée"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "Le plugin ne peut pas être désinstallé car il est actuellement actif"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr "Le plugin n'est pas installé"

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr "L'installation du plugin n'a pas été trouvée"

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "Désinstallation réussie du plugin"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Configuration du plugin"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Configurations des plugins"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Clé du plugin"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Non du Plugin"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Nom du paquet"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "Nom du paquet installé, si le plugin a été installé via PIP"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Le plugin est-il actif"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Exemple de plugin"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Extension Intégrée"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr "Plugin obligatoire"

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "Plugin Package"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "Extension"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Aucun auteur trouvé"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Le plugin '{p}' n'est pas compatible avec la version actuelle d'InvenTree {v}"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Le plugin nécessite au moins la version {v}"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Le plugin nécessite au maximum la version {v}"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "Activer le PO"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "Activer la fonctionnalité PO dans l'interface InvenTree"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "Clé API"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Clé nécessaire pour accéder à l'API externe"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Numérique"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "Un cadre numérique"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Choix des paramètres"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "Un cadre avec des choix multiples"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Exemple de plugin de change"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "Les contributeurs d'InvenTree"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Activer le panneau de pièces"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Activer les panneaux personnalisés pour les vues de pièces"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Activer les panneaux de commande"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Activer les panneaux personnalisés pour les vues des bons de commande"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Activer les panneaux brisés"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Activer les panneaux brisés pour les tests"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Activer le panneau dynamique"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Activer les panneaux dynamiques pour les tests"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Panneau partiel"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Élément du tableau de bord cassé"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Il s'agit d'un élément de tableau de bord cassé - il ne s'affiche pas !"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Exemple de tableau de bord"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Il s'agit d'un exemple d'élément de tableau de bord. Il rend une simple chaîne de contenu HTML."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Contexte Élément du tableau de bord"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Élément du tableau de bord de l'administrateur"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Il s'agit d'un élément du tableau de bord réservé aux administrateurs."

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Fichier source"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Chemin d'accès au fichier source pour l'intégration de l'administrateur"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Données contextuelles facultatives pour l'intégration de l'administrateur"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "URL de la source"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Source du paquet - il peut s'agir d'un registre personnalisé ou d'un chemin VCS"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Nom du paquet de plugins - peut également contenir un indicateur de version"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Version"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Identifiant de version du plugin. Laissez vide pour la dernière version."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Confirmer l'installation du plugin"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Ceci installera ce plugin dans l'instance actuelle. L'instance sera mise en maintenance."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installation non confirmée"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Le nom de l'emballage ou l'URL doivent être fournis"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Recharge complète"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Effectuer un rechargement complet du registre des plugins"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Forcer le rechargement"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Forcer le rechargement du registre des plugins, même s'il est déjà chargé"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Collecter les plugins"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Collecter les plugins et les ajouter au registre"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "Activer le plugin"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "Activer ce plugin"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "Supprimer la configuration"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "Supprimer la configuration du plugin de la base de données"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "Articles"

#: report/api.py:114
msgid "Plugin not found"
msgstr "Plugin non trouvé"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr "Le plugin ne prend pas en charge l'impression d'étiquettes"

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr "Dimensions de l'étiquette non valides"

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr "Aucun élément valide n'a été fourni au modèle"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Juridique"

#: report/helpers.py:46
msgid "Letter"
msgstr "Lettre"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr "Le fichier modèle portant ce nom existe déjà"

#: report/models.py:217
msgid "Template name"
msgstr "Nom du modèle"

#: report/models.py:223
msgid "Template description"
msgstr "Description du modèle"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr "Numéro de révision (auto-incrémentation)"

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr "Joindre au modèle sur l'impression"

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Enregistrer le rapport en tant que pièce jointe à l'instance de modèle liée lors de l'impression"

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Modèle de nom de fichier"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr "Modèle de génération de noms de fichiers"

#: report/models.py:287
msgid "Template is enabled"
msgstr "Le modèle est activé"

#: report/models.py:294
msgid "Target model type for template"
msgstr "Type de modèle cible pour le modèle"

#: report/models.py:314
msgid "Filters"
msgstr "Filtres"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "Filtres de requête de modèle (liste de paires clé/valeur séparées par des virgules)"

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "Fichier modèle"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "Taille des pages pour les rapports PDF"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "Rendre le rapport en orientation paysage"

#: report/models.py:393
msgid "Merge"
msgstr "Fusionner"

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr "Rapport généré à partir du modèle {self.name}"

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr "Erreur dans la génération du rapport"

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Largeur [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Largeur de l'étiquette, spécifiée en mm"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Hauteur [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Hauteur de l'étiquette, spécifiée en mm"

#: report/models.py:780
msgid "Error printing labels"
msgstr "Erreur d'impression des étiquettes"

#: report/models.py:799
msgid "Snippet"
msgstr "Extrait "

#: report/models.py:800
msgid "Report snippet file"
msgstr "Fichier d'extrait de rapport"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Description du fichier d'extrait"

#: report/models.py:825
msgid "Asset"
msgstr "Elément"

#: report/models.py:826
msgid "Report asset file"
msgstr "Rapport sur le fichier d'actifs"

#: report/models.py:833
msgid "Asset file description"
msgstr "Description du fichier d'actifs"

#: report/serializers.py:96
msgid "Select report template"
msgstr "Sélectionner un modèle de rapport"

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr "Liste des clés primaires des éléments à inclure dans le rapport"

#: report/serializers.py:137
msgid "Select label template"
msgstr "Sélection du modèle d'étiquette"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr "Plugin d'impression"

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr "Sélectionner le plugin à utiliser pour l'impression des étiquettes"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR code"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR code"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Nomenclature"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Matériel nécessaire"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Image partielle"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Délivré"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Requis pour"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Émis par"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Le fournisseur a été supprimé"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Prix unitaire"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Postes supplémentaires"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Total"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Numéro de série"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Allocations"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Lot"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Postes d'emplacement de stock"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Rapport de test des articles en stock"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Éléments installés"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Numéro de série"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Résultats des tests"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Test"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Passez"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Échec"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Pas de résultat (obligatoire)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Pas de résultat"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Le fichier d'actifs n'existe pas"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Fichier image non trouvé"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "la balise part_image nécessite une instance de Part"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "la balise company_image nécessite une instance d'entreprise"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "Filtrer par profondeur de localisation"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr "Filtrer par lieux de premier niveau"

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "Inclure les sous-emplacements dans les résultats filtrés"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "Emplacement parent"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "Filtrer par emplacement parent"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr "Nom de la pièce (insensible à la casse)"

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr "Le nom de la pièce contient (insensible à la casse)"

#: stock/api.py:594
msgid "Part name (regex)"
msgstr "Nom de la pièce (regex)"

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr "Partie IPN (insensible à la casse)"

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr "La partie IPN contient (insensible à la casse)"

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr "Partie IPN (regex)"

#: stock/api.py:623
msgid "Minimum stock"
msgstr "Stock minimum"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "Stock maximum"

#: stock/api.py:630
msgid "Status Code"
msgstr "Code de statut"

#: stock/api.py:670
msgid "External Location"
msgstr "Emplacement externe"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr "Consommé par l'ordre de construction"

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr "Installé dans un autre article en stock"

#: stock/api.py:868
msgid "Part Tree"
msgstr "Arbre en pièces détachées"

#: stock/api.py:890
msgid "Updated before"
msgstr "Mise à jour avant"

#: stock/api.py:894
msgid "Updated after"
msgstr "Mise à jour après"

#: stock/api.py:898
msgid "Stocktake Before"
msgstr "Inventaire avant"

#: stock/api.py:902
msgid "Stocktake After"
msgstr "Inventaire après"

#: stock/api.py:907
msgid "Expiry date before"
msgstr "Date d'expiration avant"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "Date d’expiration après"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "Périmé"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "La quantité est requise"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "La partie valide doit être fournie"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "Le fournisseur donné n'existe pas"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "La pièce du fournisseur a une taille d'emballage définie, mais le drapeau use_pack_size n'est pas activé"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Les numéros de série ne peuvent pas être fournis pour une pièce non traçable"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "Type d'emplacement du stock"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "Types d'emplacements de stock"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Icône par défaut pour tous les lieux qui n'ont pas d'icône (facultatif)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Emplacement du stock"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Emplacement des stocks"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Propriétaire"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Sélectionner un propriétaire"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Les articles en stock ne peuvent pas être directement placés dans un emplacement de stock structurel, mais peuvent être placés dans des emplacements subordonnés."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Externe"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Il s'agit d'un emplacement de stock externe"

#: stock/models.py:233
msgid "Location type"
msgstr "Type d'emplacement"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "Type d'emplacement du stock de cet emplacement"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Vous ne pouvez pas rendre ce magasin structurel car certains articles de stock y sont déjà localisés !"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr "La pièce doit être spécifiée"

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Les articles en stock ne peuvent pas être localisés dans des emplacements de stock structurel !"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Il n'est pas possible de créer un article de stock pour les pièces virtuelles"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Le type de pièce ('{self.supplier_part.part}') doit être {self.part}"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "La quantité doit être de 1 pour un article avec un numéro de série"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Le numéro de série ne peut pas être défini si la quantité est supérieure à 1"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "L'objet ne peut pas s'appartenir à lui-même"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "L'élément doit avoir une référence de construction si is_building=True"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "La référence de construction ne pointe pas vers le même objet de pièce"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Poste de stock parent"

#: stock/models.py:1028
msgid "Base part"
msgstr "Pièce de base"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Sélectionnez une pièce fournisseur correspondante pour cet article en stock"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Où se trouve cet article en stock ?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "L'emballage de cet article en stock est stocké dans"

#: stock/models.py:1064
msgid "Installed In"
msgstr "Installé dans"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "L'article a été installé dans un autre article ?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Numéro de série pour cet article"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Code de lot pour cet article de stock"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Quantité en stock"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Source Construire"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Construire pour cet article en stock"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "Consommé par"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "Ordre de construction qui a consommé cet article de stock"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Bon de commande source"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Commande d'achat pour cet article en stock"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Destination de la commande client"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Date d'expiration de l'article en stock. Le stock sera considéré comme périmé après cette date"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Supprimer lors de l'épuisement"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Supprimer ce poste de stock lorsque le stock est épuisé"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Prix d'achat de l'unité unique au moment de l'achat"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "Converti en partie"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "La pièce n'est pas définie comme pouvant faire l'objet d'un suivi"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "La quantité doit être un nombre entier"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "La quantité ne doit pas dépasser la quantité disponible en stock ({self.quantity})"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr "Les numéros de série doivent être fournis sous forme de liste"

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "La quantité ne correspond pas au nombre de numéros de série"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "Le modèle de test n'existe pas"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "Un article de stock a été affecté à une commande client"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "L'article de stock est installé dans un autre article"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "L'article de stock contient d'autres articles"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "Un article de stock a été affecté à un client"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "L'article de stock est actuellement en production"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Le stock sérialisé ne peut pas être fusionné"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Articles de stock en double"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "Les articles en stock doivent se référer à la même pièce"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "Les articles en stock doivent se référer à la même pièce du fournisseur"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "Les codes d'état des stocks doivent correspondre"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "StockItem ne peut pas être déplacé car il n'est pas en stock"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr "Suivi des articles en stock"

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Notes d'entrée"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr "Résultat du test de l'article en stock"

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Une valeur doit être fournie pour ce test"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "La pièce jointe doit être téléchargée pour ce test"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr "Valeur non valide pour ce test"

#: stock/models.py:2951
msgid "Test result"
msgstr "Résultat du test"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Valeur de sortie du test"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Pièce jointe au résultat du test"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Notes de test"

#: stock/models.py:2978
msgid "Test station"
msgstr "Station de test"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "L'identifiant de la station de test où le test a été effectué"

#: stock/models.py:2985
msgid "Started"
msgstr "Commencé"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "Horodatage du début du test"

#: stock/models.py:2992
msgid "Finished"
msgstr "Fini"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "Horodatage de la fin du test"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "Code de lot généré"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "Sélectionner l'ordre de construction"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "Sélectionner l'article de stock pour lequel le code de lot doit être généré"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "Sélectionnez l'emplacement pour lequel vous souhaitez générer un code de lot"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "Sélectionnez la partie pour laquelle vous souhaitez générer un code de lot"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "Sélectionner un ordre d'achat"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "Saisir la quantité pour le code de lot"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "Numéro de série généré"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "Sélectionner la pièce pour laquelle un numéro de série doit être généré"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "Nombre de numéros de série à générer"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "Modèle de test pour ce résultat"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "L'ID du modèle ou le nom du test doit être fourni"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "L'heure de fin du test ne peut être antérieure à l'heure de début du test"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Article Parent"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr "Article de stock parent"

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Utiliser la taille de l'emballage lors de l'ajout : la quantité définie est le nombre d'emballages"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Entrez les numéros de série pour les nouveaux articles"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "Référence du fournisseur"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "Expiré"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Éléments enfants"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr "Suivi des éléments"

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Prix d'achat de cet article en stock, par unité ou par paquet"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Entrez le nombre d'articles en stock à sérialiser"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "La quantité ne doit pas dépasser la quantité disponible en stock ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Emplacement du stock de destination"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Les numéros de série ne peuvent pas être assignés à cette pièce"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "Les numéros de série existent déjà"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Sélectionner l'article de stock à installer"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "Quantité à installer"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "Saisir la quantité d'articles à installer"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "Ajouter une note de transaction (facultatif)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "La quantité à installer doit être d'au moins 1"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "L'article en stock n'est pas disponible"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "La pièce sélectionnée ne figure pas dans la nomenclature"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "La quantité à installer ne doit pas dépasser la quantité disponible"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Emplacement de destination de l'élément désinstallé"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Sélectionner la pièce à convertir en article de stock"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "La partie sélectionnée n'est pas une option valide pour la conversion"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Impossible de convertir un article de stock auquel un SupplierPart a été attribué"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "Code d'état de l'article en stock"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "Sélectionner les articles en stock pour modifier leur statut"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "Aucun article en stock n'a été sélectionné"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Sous-localisations"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "Emplacement du stock mère"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "La pièce doit être vendable"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "L'article est affecté à une commande client"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "L'article est attribué à un ordre de fabrication"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Affectation d'articles en stock par le client"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "L'entreprise sélectionnée n'est pas un client"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Notes d'affectation des stocks"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "Une liste des articles en stock doit être fournie"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Notes sur les fusions d'actions"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Autoriser les fournisseurs non concordants"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Permettre la fusion d'articles en stock avec des pièces de fournisseurs différents"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Autoriser la non-concordance des statuts"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Permettre la fusion d'articles en stock ayant des codes de statut différents"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Au moins deux articles en stock doivent être fournis"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "Pas de changement"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Valeur de la clé primaire StockItem"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr "L'article n'est plus en stock"

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Notes sur les transactions boursières"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr "Numéro de série suivant"

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr "Numéro de série précédent"

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Attention requise"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Endommagé"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Détruit"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Rejeté"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "En quarantaine"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Ancienne entrée de suivi de stock"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Article en stock créé"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Article de stock modifié"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Numéro de série attribué"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Stock comptabilisé"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Stock ajouté manuellement"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Stock supprimé manuellement"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Emplacement modifié"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Stock mis à jour"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "Installé dans l'assemblage"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Retiré de l'assemblage"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Composant installé"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Composant retiré"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Séparer de l'élément parent"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Fractionner l'élément enfant"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Articles de stock fusionnés"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "Converti en variante"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "La sortie de l'ordre de construction a été créée"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Sortie de l'ordre de construction terminée"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "La sortie de l'ordre de construction a été refusée"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Consommé par ordre de construction"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Commandes expédiées vs. ventes"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Livraisons reçues vs. commandes réalisées"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Livraisons retournées vs. commandes retournées"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Envoyé au client"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Retourné par le client"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Autorisation refusée"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Vous n'êtes pas autorisé à consulter cette page."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Échec de l'authentification"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Vous avez été déconnecté•e d'InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Page non trouvée"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "La page demandée n'existe pas"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Erreur de serveur interne"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "Le serveur de %(inventree_title)s a soulevé une erreur interne"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Se référer au journal des erreurs dans l'interface d'administration pour plus de détails"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Le site est en maintenance"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Le site est actuellement en maintenance et devrait être rétabli sous peu !"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Redémarrage du serveur nécessaire"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Une option de configuration a été modifiée, ce qui nécessite un redémarrage du serveur"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Contactez votre administrateur système pour plus d'informations"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Migrations de bases de données en attente"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Des migrations de bases de données sont en cours et requièrent une attention particulière"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Cliquez sur le lien suivant pour consulter cette commande"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Du stock est requis pour la commande de construction suivante"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Ordre de construction %(build)s - construction %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Cliquez sur le lien suivant pour voir cet ordre de construction"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Les pièces suivantes sont en rupture de stock"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Quantité requise"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Vous recevez cet e-mail parce que vous êtes abonné aux notifications pour cette partie "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Cliquez sur le lien suivant pour visualiser cette partie"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Quantité minimale"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr "Vous recevez cet e-mail parce que vous êtes abonné aux notifications relatives à cette partie ou à une catégorie dont elle fait partie "

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Utilisateurs"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Sélectionner quels utilisateurs sont assignés à ce groupe"

#: users/admin.py:137
msgid "Personal info"
msgstr "Informations personnelles"

#: users/admin.py:139
msgid "Permissions"
msgstr "Droits"

#: users/admin.py:142
msgid "Important dates"
msgstr "Dates importantes"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Le jeton a été révoqué"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Le jeton a expiré"

#: users/models.py:100
msgid "API Token"
msgstr "Jeton API"

#: users/models.py:101
msgid "API Tokens"
msgstr "Jetons API"

#: users/models.py:137
msgid "Token Name"
msgstr "Nom du jeton"

#: users/models.py:138
msgid "Custom token name"
msgstr "Nom du jeton personnalisé"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Date d'expiration du jeton"

#: users/models.py:152
msgid "Last Seen"
msgstr "Dernière visite"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Dernière utilisation du jeton"

#: users/models.py:157
msgid "Revoked"
msgstr "Révoquée"

#: users/models.py:235
msgid "Permission set"
msgstr "Droit défini"

#: users/models.py:244
msgid "Group"
msgstr "Groupe"

#: users/models.py:248
msgid "View"
msgstr "Vue"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Droit de voir des éléments"

#: users/models.py:252
msgid "Add"
msgstr "Ajouter"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Droit d'ajouter des éléments"

#: users/models.py:256
msgid "Change"
msgstr "Modifier"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Droit de modifier des élément"

#: users/models.py:262
msgid "Delete"
msgstr "Supprimer"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Droit de supprimer des éléments"

#: users/models.py:501
msgid "Bot"
msgstr "Bot"

#: users/models.py:502
msgid "Internal"
msgstr "Interne"

#: users/models.py:504
msgid "Guest"
msgstr "Invité"

#: users/models.py:513
msgid "Language"
msgstr "Langue"

#: users/models.py:514
msgid "Preferred language for the user"
msgstr "Langue préférée de l'utilisateur"

#: users/models.py:519
msgid "Theme"
msgstr "Thème"

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr "Paramètres pour l'interface web sous forme de JSON - ne pas modifier manuellement !"

#: users/models.py:525
msgid "Widgets"
msgstr "Raccourcis"

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr "Paramètres des widgets du tableau de bord sous forme de JSON - ne pas modifier manuellement !"

#: users/models.py:534
msgid "Display Name"
msgstr "Nom d'affichage"

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr "Nom d'affichage choisi pour l'utilisateur"

#: users/models.py:541
msgid "Position"
msgstr "Localisation"

#: users/models.py:542
msgid "Main job title or position"
msgstr "Intitulé de l'emploi principal ou du poste"

#: users/models.py:549
msgid "User status message"
msgstr "Message sur le statut de l'utilisateur"

#: users/models.py:556
msgid "User location information"
msgstr "Informations sur la localisation de l'utilisateur"

#: users/models.py:561
msgid "User is actively using the system"
msgstr "L'utilisateur utilise activement le système"

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr "Coordonnées préférées de l'utilisateur"

#: users/models.py:574
msgid "User Type"
msgstr "Type d'utilisateur"

#: users/models.py:575
msgid "Which type of user is this?"
msgstr "De quel type d'utilisateur s'agit-il ?"

#: users/models.py:581
msgid "Organisation"
msgstr "Organisation"

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr "Organisation/affiliation principale de l'utilisateur"

#: users/models.py:590
msgid "Primary Group"
msgstr "Groupe primaire"

#: users/models.py:591
msgid "Primary group for the user"
msgstr "Groupe principal de l'utilisateur"

#: users/ruleset.py:26
msgid "Admin"
msgstr "Administrateur"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Bons de commande"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Ventes"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Commandes de retour"

#: users/serializers.py:196
msgid "Username"
msgstr "Nom d'utilisateur"

#: users/serializers.py:199
msgid "First Name"
msgstr "Prénom"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Prénom de l'utilisateur"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Nom"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Nom de famille de l'utilisateur"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "Adresse e-mail de l'utilisateur"

#: users/serializers.py:326
msgid "Staff"
msgstr "Staff"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Cet utilisateur a-t-il les permissions du staff"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Super-utilisateur"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "Cet utilisateur est-il un super-utilisateur"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Ce compte d'utilisateur est-il actif"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr "Seul un superutilisateur peut modifier ce champ"

#: users/serializers.py:376
msgid "Password"
msgstr "Mot de passe"

#: users/serializers.py:377
msgid "Password for the user"
msgstr "Mot de passe pour l'utilisateur"

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr "Écraser l'alerte sur les règles de mot de passe"

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr "Seuls les membres du personnel peuvent créer de nouveaux utilisateurs"

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr "Vous n'avez pas le droit de créer des utilisateurs"

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Votre compte a été créé."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Veuillez utiliser la fonction de réinitialisation du mot de passe pour vous connecter"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Bienvenue dans InvenTree"

