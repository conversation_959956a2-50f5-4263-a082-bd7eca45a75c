msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Serbian (Latin)\n"
"Language: sr_CS\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: sr-CS\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr ""

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "API krajnja tačka nije pronađena"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Lista nevalidiranih stavki"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Dati su neispravni filteri"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "Korisnik nema dozvolu za pregled ovog modela"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "E-pošta (ponovo)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "Potvrda adrese e-pošte"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Svaki put morate upisati istu e-poštu."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Navedena primarna adresa e-pošte nije važeća."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Navedeni domen adrese e-pošte nije prihvaćen."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Data je nevažeća jedinica ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Nije navedena vrednost"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Nije moguće konvertovati {original} u {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Isporučena nevažeća količina"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Detalji o grešci se mogu naći u admin sekciji"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Unesite datum"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Neispravna decimalna vrednost"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Napomene"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Vrednost '{name}' se ne pojavljuje u formatu obrasca"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Navedena vrednost ne odgovara traženom obrascu: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Nije moguće dati više od 1000 serijskih brojeva stavkama odjednom"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Serijski broj nije popunjen"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Dupliciraj serijski broj"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Nevažeća grupa: {group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Raspon grupe {group} prelazi dozvoljenu količinu ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Nisu pronađeni serijski brojevi"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Uklonite HTML oznake iz ove vrednosti"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "Podatak sadrži zabranjen jezički sadržaj"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Greška u povezivanju"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Server je odgovorio nevažećim statusnim kodom"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Došlo je do izuzetka"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Server je odgovorio nevažećom vrednošću dužina sadržaja"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Veličina slike je prevelika"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Preuzimanje slike premašilo je maksimalnu veličinu"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Udaljeni server vratio je prazan odgovor"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Navedeni URL nije važeća slikovna datoteka"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabski"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bugarski"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Češki"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danski"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Nemački"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Grčki"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Engleski"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Španski"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Španski (Meksiko)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estonijski"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persijski"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finski"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Francuski"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Jevrejski"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindu"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Mađarski"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italijanski"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japanski"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Korejski"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Litvanski"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Letonijski"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Holandski"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norveški"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Poljski"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugalski"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugalski (Brazil)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumunski"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Ruski"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovački"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovenski"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Srpski"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Švedski"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Tajlandski"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turski"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrajinski"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vijetnamski"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Kineski (Uprošćeni)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Kineski (Tradicionalni)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Prijavljivanje na aplikaciju"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "E-Pošta"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Greška prilikom validacije ekstenzije"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Metapodaci moraju biti \"python dict\" objekat"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Metapodaci dodatka"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "Polje metapodataka JSON, za korištenje eksternih dodataka"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Neispravno formatiran obrazac"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Naveden je ključ nepoznatog formata"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Nedostaje potreban ključ formata"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "Polje za reference ne može biti prazno"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "Referenca mora odgovarati traženom obrascu"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Broj reference je predugačak"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Nevažeći izvor"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Ime"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Opis"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Opis (Opciono)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Putanja"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Dvostruka imena ne mogu postojati pod istom nadredjenom grupom"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Zabeleške (Opciono)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Podaci sa barkoda"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Podaci sa barkoda trećih lica"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Heš barkoda"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Jedinstveni hash barkoda"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Postojeći barkod pronađen"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Neuspešan zadatak"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Pozadinski proces '{f}' neuspešan posle {n} pokušaja"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Greška servera"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "Server je zabležio grešku."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Mora biti važeći broj"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Valuta"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Odaberite valutu među dostupnim opcijama"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Nevažeća vrednost"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Udaljena slika"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL udaljene slike"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Preuzimanje slika s udaljenog URL-a nije omogućeno"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Neuspešno preuzimanje slike sa udaljene URL"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Nevažeća jedinica mere"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Nevažeći kod valute"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Status naloga"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Roditeljski proizvod"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Uključi varijante"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Deo"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Kategorija"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Proizvod predaka"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Dodeljeno meni"

#: build/api.py:154
msgid "Assigned To"
msgstr "Dodeljeno"

#: build/api.py:189
msgid "Created before"
msgstr "Kreirano pre"

#: build/api.py:193
msgid "Created after"
msgstr "Kreirano nakon"

#: build/api.py:197
msgid "Has start date"
msgstr ""

#: build/api.py:205
msgid "Start date before"
msgstr ""

#: build/api.py:209
msgid "Start date after"
msgstr ""

#: build/api.py:213
msgid "Has target date"
msgstr ""

#: build/api.py:221
msgid "Target date before"
msgstr "Ciljni datum pre"

#: build/api.py:225
msgid "Target date after"
msgstr "Ciljni datum nakon"

#: build/api.py:229
msgid "Completed before"
msgstr "Završeno pre"

#: build/api.py:233
msgid "Completed after"
msgstr "Završeno nakon"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr ""

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr ""

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Ne uključuj stablo"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "Proizvod mora biti poništen pre nego što se izbriše"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Potrošni materijal"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Opciono"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Sklapanje"

#: build/api.py:450
msgid "Tracked"
msgstr "Praćeno"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Proverljivo"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "Neizmirena narudžbina"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Alocirano"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Dostupno"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Nalog za izradu"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Lokacija"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Nalozi za izradu"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "BOM za sastavljanje nije potvrđeno"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Nalog za izradu se ne može kreirati za neaktivan deo"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Nalog za izradu se ne može kreirati za zaključan deo"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Odgovorni korisnik ili grupa mora biti određena"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "Deo u nalogu za izradu ne može se izmeniti"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Reference naloga za pravljenje"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referenca"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Kratak opis izrade (nije obavezno)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Nalog za izradu za koju je ova izrada alocirana"

#: build/models.py:273
msgid "Select part to build"
msgstr "Izaberi deo za izgradnju"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Referenca naloga za prodaju"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Nalog za prodaju za koju je ova izrada alocirana"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Lokacija izvora"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Izaberi lokaciju zaliha za ovu izgradnju (ostaviti prazno ako hoćete bilo koju lokaciju zaliha"

#: build/models.py:300
msgid "External Build"
msgstr ""

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr "Lokacija odredišta"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Izaberi lokaciju gde će se završene stavke skladištiti"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Količina izgradnje"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Broj stavki za izgradnju"

#: build/models.py:322
msgid "Completed items"
msgstr "Kompletirane stavke"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Broj stavki u zalihama koje su kompletirane"

#: build/models.py:328
msgid "Build Status"
msgstr "Status izgradnje"

#: build/models.py:333
msgid "Build status code"
msgstr "Kod statusa izgradnje"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Kod serije"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Kod izgradnje za ovaj izlaz"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "datum kreiranja"

#: build/models.py:356
msgid "Build start date"
msgstr ""

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:363
msgid "Target completion date"
msgstr "Datum ciljanog završetka"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Ciljani datum za završetak izgradnje. Izgradnja će biti u prekoračenju nakon ovog datuma"

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Datum završetka"

#: build/models.py:378
msgid "completed by"
msgstr "kompletirano od "

#: build/models.py:387
msgid "Issued by"
msgstr "izdato od"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Korisnik koji je izdao nalog za izgradnju"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Odgovoran"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Korisnik ili grupa koja je odgovorna za ovaj nalog za izgradnju"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Spoljašnja konekcija"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Link za eksterni URL"

#: build/models.py:410
msgid "Build Priority"
msgstr "Prioritet izgradnje"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Prioritet ovog naloga za izgradnju"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Kod projekta"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Kod projekta za ovaj nalog za izgradnju"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Nije uspelo preuzimanje zadataka da bi se dovršila alokacija izgradnje"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Nalog za izgradnju {build} je kompletiran"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "Nalog za izgradnju je kompletiran"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Za delove koji mogu da se prate moraju se dostaviri serijski brojevi"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Nije određen izlaz izgradnje"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "Izlaz izgradnje je već kompletiran"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "Izlaz izgradnje se ne slaže sa Nalogom za izgradnju"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "Količina mora biti veća od nule"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "Količina ne sme da bude veća od izlazne količine"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Izlaz izgradnje {serial} nije zadovoljio zahtevane testove"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Stavka porudžbine naloga za izgradnju"

#: build/models.py:1602
msgid "Build object"
msgstr "Objekat izgradnje"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Količina"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Potrebna količina za nalog za izgradnju"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Stavka izgradnje mora imati izlaz izgradnje, jer je nadređeni deo markiran da može da se prati"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Alocirana količina ({q}) ne sme da bude veća od količine dostupnih zaliha ({a})"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "Stavka zaliha je prealocirana"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "Količina alokacije mora da bude veća od nule"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "Količina mora da bude 1 za zalihe koje su serijalizovane"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "Izabrana stavka zaliha se ne slaže sa porudžbinom sa spiska materijala"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Stavka zaliha"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Izvor stavke zaliha"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Količina zaliha za alociranje za izgradnju"

#: build/models.py:1924
msgid "Install into"
msgstr "Ugradi u"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Stavka zaliha odredišta"

#: build/serializers.py:115
msgid "Build Level"
msgstr "Nivo izgradnje"

#: build/serializers.py:124
msgid "Part Name"
msgstr "Ime dela"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Naziv koda projekta"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Izlaz izgradnje"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "Izlaz izgradnje se ne slaže sa nadređenom izgradnjom"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "Izlazni deo se ne slaže sa delom Naloga za Izgradnju"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Ovaj izlaz izgradnje je već kompletiran"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Ovaj izlaz izgradnje nije u potpunosti alociran"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Unesi količinu za izlaz izgradnje"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Brojčana količina potrebna za delove koji mogu da se prate"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Potrebna je brojčana količina, jer opis materijala sadrži delove koji se mogu pratiti"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Serijski brojevi"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Unesi serijske brojeve za izlaz izgradnje"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Lokacija zaliha za izlaz izgradnje"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Automatski alociraj serijske brojeve"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Automatski alociraj tražene stavke sa odgovarajućim serijskim brojevima"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "Sledeći serijski brojevi već postoje ili su neispravni"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "Lista izlaza izgradnje se mora obezbediti"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Lokacija zaliha za otpisane izlaze"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Odbaci alokacije"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Odbaci bilo kojiu alokaciju zaliha za otpisane izlaze"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Razlog za otpisane izlaz(e) izgradnje"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Lokacija za završene izlaze izgradnje"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Prihvati nekompletirane Alokacije"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "kompletiraj izlaze ako zalihe nisu u potpunosti alocirane"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Troši alocirane zalihe"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Troši bilo koje zalihe koje su već alocirane za ovu izgradnju"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Ukloni nekompletirane izlaze"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Izbriši svei izlaze izgradnje koji nisu kompletirani"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Nije dozvoljeno"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Prihvati kao potrošeno od strane ovog naloga za izgradnju"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Izmesti bre završetka ovog naloga za izgradnju"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Sveukupne izdvojene zalihe"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Šta želite da radite sa viškom stavki u zalihama koje su dodeljene nalogu za izgradnju?"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Neke stavke zaliha su prealocirane"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Prihvati nealocirano"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Prihvati da stavke zaliha nisu u potpunosti alocirane za ovaj nalog za izgradnju"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "Tražene zalihe nisu u potpunosti alocirane"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Prihvati nekompletirano"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Prihvati da je traženi broj izlaza izgradnje nekompletan"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "Traženi broj izgradnji nije kompletan"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "Nalog za izgradnju ima otvoren potčinjene naloge za izgradnju"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "Nalog za izgradnju mora biti u stanju produkcije"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "Nalog za izgradnju ima nekompletne izlaze"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Porudžbina izgradnje"

#: build/serializers.py:877
msgid "Build output"
msgstr "Izlaz izgradnje"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "Izlaz izgradnje mora da referencira istu izgradnju"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Stavka porudžbine za izradu"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part mora da se referencira istom delu kao u nalogu za izgradnju"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "Stavka mora da bude u zalihama"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Dostupna količina ({q}) premašena"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Izlaz izgradnje mora da određen za alokaciju praćenih delova"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Izlaz izgradnje ne može biti određen za alokaciju nepraćenih delova"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Stavke alociranja se moraju odrediti"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Lokacija zaliha koje će da budu izvor delova (ostavi prazno ukoliko uzimate sa bilo koje lokacije)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Isključi lokaciju"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Isključi stavke zaliha za ovu selektovanu lokaciju"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Zamenljive zalihe"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Stavke zaliha koje su na različitim lokacijama se mogu međusobno menjati"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Zamenske zalihe"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Dozvoli alociranje delova koji su zamenski"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Opcionalne stavke"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Alociraj opcione BOM stavke na nalog za izgradnju"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Greška prilikom startovanja auto alociranja"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "Referenca BOM"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "BOM ID dela"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "BOM ime dela"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr ""

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Deo dobavljača"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Alocirana količina"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Referenca izgradnje"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Ime kategorije dela"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Može da se prati"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Nasleđen"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Dozvoli varijante"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "BOM stavka"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "Po narudžbini"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "U proizvodnji"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Spoljašnje zalihe"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Dostupne zalihe"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Dostupne zamenske zalihe"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Dostupne varijante zaliha"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Na čekanju"

#: build/status_codes.py:12
msgid "Production"
msgstr "Proizvodnja"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "Na čekanju"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Otkazano"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Gotovo"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Potrebne su zalihe za nalog izgranje"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Prekoračeni nalog za izgradnju"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Nalog za izgradnju {bo} je sada prekoračen"

#: common/api.py:688
msgid "Is Link"
msgstr "je link"

#: common/api.py:696
msgid "Is File"
msgstr "je datoteka"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "Korisnik nema potrebne dozvole da bi izbrisao ove atačmente"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "Korisnik nema dozvolu da izbriše ovaj atačment"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Neispravan kod valute"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Napravi duplikat koda valute"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Nisu obezbeđeni ispravni kodovi valuta"

#: common/currency.py:146
msgid "No plugin"
msgstr "Nema dodataka"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Ažurirano"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Vreme poslednjeg ažuriranja"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "Jedinstveni kod projekta"

#: common/models.py:171
msgid "Project description"
msgstr "Opis projekta"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Korisnik ili grupa odgovorni za ovaj projkat"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "Ključ za podešavanje"

#: common/models.py:780
msgid "Settings value"
msgstr "Vrednost podešavanja"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "Izabrana vrednost nije ispravna opcija"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Vrednost mora da bude boolean tipa"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Vrednost mora da bude integer tipa"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr "Vrednost mora biti broj"

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr "Vrednost ne prolazi test ispravnosti"

#: common/models.py:914
msgid "Key string must be unique"
msgstr "Tekstualni ključ mora da bude jedinstven"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Korisnik"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Prelomna količina cene"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Cena"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Cena jedinice za određenu količinu"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Krajnja tačka"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Krajnja tačka na kojoj je primljen zahtev za izmenu web stranice"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Ime ovog zahteva za izmenu stranice"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Aktivan"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Da li je ovaj zahtev za izmenu aktivan?"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Token"

#: common/models.py:1437
msgid "Token for access"
msgstr "Token za pristup"

#: common/models.py:1445
msgid "Secret"
msgstr "Tajna"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Deljena tajna za HMAC"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "ID poruke"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Jedinstveni identifikator za ovu poruku"

#: common/models.py:1563
msgid "Host"
msgstr "Računar"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Računar koji je primio ovu poruku"

#: common/models.py:1572
msgid "Header"
msgstr "Zaglavlje"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Zaglavlje ove poruke"

#: common/models.py:1580
msgid "Body"
msgstr "Telo"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Telo ove poruke"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Krajnja tačka na kojoj je ova poruka primljena"

#: common/models.py:1596
msgid "Worked on"
msgstr "Radilo se na "

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Da li je rad sa ovom porukom završen?"

#: common/models.py:1723
msgid "Id"
msgstr "Id"

#: common/models.py:1725
msgid "Title"
msgstr "Naslov"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Link"

#: common/models.py:1729
msgid "Published"
msgstr "Objavljeno"

#: common/models.py:1731
msgid "Author"
msgstr "Autor"

#: common/models.py:1733
msgid "Summary"
msgstr "Rezime"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Čitaj"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Da li je ova stavka vesti pročitana"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Slika"

#: common/models.py:1753
msgid "Image file"
msgstr "Datoteka slike"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "Ciljni tip modela za ovu sliku"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "Ciljni ID modela za ovu sliku"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Posebna jedinica"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "Simbol jedinice mora biti jedinstven"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Ime jedinice mora da bude ispravan identifikator"

#: common/models.py:1843
msgid "Unit name"
msgstr "Ime jedinice"

#: common/models.py:1850
msgid "Symbol"
msgstr "Simbol"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Opcioni simbol  jedinice"

#: common/models.py:1857
msgid "Definition"
msgstr "Definicija"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Definicija jedinice"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Prilog"

#: common/models.py:1933
msgid "Missing file"
msgstr "Nedostaje datoteka"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Nedostaje eksterni link"

#: common/models.py:1971
msgid "Model type"
msgstr ""

#: common/models.py:1972
msgid "Target model type for image"
msgstr ""

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Izaberite datoteku za prilog"

#: common/models.py:1996
msgid "Comment"
msgstr "Komentar"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Komentar priloga"

#: common/models.py:2013
msgid "Upload date"
msgstr "Datum učitavanja"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Datum kada je datoteka učitana"

#: common/models.py:2018
msgid "File size"
msgstr "Veličina datoteke"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Veličina datoteke u bajtovima"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Određen je neispravan tip modela za prilog"

#: common/models.py:2077
msgid "Custom State"
msgstr "Posebno stanje"

#: common/models.py:2078
msgid "Custom States"
msgstr "Posebna stanja"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "Referentni status podešen"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "Status je podešen i produžen je sa ovim posebnim stanjem"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Logički ključ"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Stanje logičkog ključa je jednako posebnom ključu u poslovnoj logici"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Vrednost"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr "Numerička vrednost koja će biti sačuvana u bazi podataka modela"

#: common/models.py:2102
msgid "Name of the state"
msgstr "Ime stanja"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Etiketa"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "Etiketa koja će biti prikazana na korisničkoj strani"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Boja"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "Boja koja će biti prikazana na korisničkoj strani"

#: common/models.py:2128
msgid "Model"
msgstr "Model"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "Model ovog stanja je povezan sa "

#: common/models.py:2144
msgid "Model must be selected"
msgstr "Model mora biti izabran"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "Ključ mora biti izabran"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "Logički ključ  mora biti izabran"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "Ključ mora da se razlikuje od logičkog ključa"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr "Validna referenca statusa klase mora biti dostavljena"

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "Ključ mora biti različit od logičkog ključa referentnog statusa"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "Logički ključ mora biti među logičkim ključevima referentnog statusa"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "Naziv mora biti različit od naziva u statusu reference"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Lista odabira"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "Liste odabira"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "Ime liste odabira"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "Opis liste odabira"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Zaključano"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "Da li je ova lista odabira zaključana?"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "Da li se ova lista odabira može koristiti?"

#: common/models.py:2255
msgid "Source Plugin"
msgstr "Ekstenzija/dodatak za izvor"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr "Ekstenzija koja pruža listu odabira"

#: common/models.py:2261
msgid "Source String"
msgstr "String izvora"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr "Opcioni string koji identifikuje izvor koji se koristi za ovu listu"

#: common/models.py:2271
msgid "Default Entry"
msgstr "Podrazumevani unos"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr "Podrazumevani unos za ovu listu odabira"

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Kreirano"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr "Datum i vreme kada je ova lista odabira kreirana"

#: common/models.py:2283
msgid "Last Updated"
msgstr "Poslednje ažuriranje"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr "Datum i vreme kada je ova lista odabira ažurirana"

#: common/models.py:2318
msgid "Selection List Entry"
msgstr "Unos liste odabira"

#: common/models.py:2319
msgid "Selection List Entries"
msgstr "Unosi liste odabira"

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr "Lista odabira kojoj ovaj unos pripada"

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr "Vrednost ovog unosa liste odabira"

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr "Naziv ovog unosa liste odabira"

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr "Opis ovog unosa liste odabira"

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr "Da li je unos ove liste odabira aktivan?"

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "Skeniranje bar koda"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Podaci"

#: common/models.py:2377
msgid "Barcode data"
msgstr "Podaci bar koda"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "Korisnik koji je skenirao bar kod"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Vremenski trag"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "Datum i vreme skeniranja bar koda"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "URL krajnja tačka kojaj je obradila bar kod"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Kontekst"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "Kontekst podataka za skeniranje bar koda"

#: common/models.py:2415
msgid "Response"
msgstr "Odgovor"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "Podaci odgovora za skeniranje bar koda"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Rezultat"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "Da li je skeniranje bar koda bilo uspešno?"

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr ""

#: common/models.py:2574
msgid "Email Messages"
msgstr ""

#: common/models.py:2581
msgid "Announced"
msgstr ""

#: common/models.py:2583
msgid "Sent"
msgstr ""

#: common/models.py:2584
msgid "Failed"
msgstr ""

#: common/models.py:2587
msgid "Delivered"
msgstr ""

#: common/models.py:2595
msgid "Confirmed"
msgstr ""

#: common/models.py:2601
msgid "Inbound"
msgstr ""

#: common/models.py:2602
msgid "Outbound"
msgstr ""

#: common/models.py:2607
msgid "No Reply"
msgstr ""

#: common/models.py:2608
msgid "Track Delivery"
msgstr ""

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr ""

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr ""

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr ""

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr ""

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Ključ"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Novo {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Nova narudžbina je kreirana i dodeljena vama"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} poništeno"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Narudžbina koja je bila dodeljena vama je otkazana"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Stavke primljene"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Stavke su primljene uprkos nalogu za kupovinu"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Stavke su primljene uprkos nalogu za povrat"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr ""

#: common/serializers.py:486
msgid "Is Running"
msgstr "Pokrenuto je"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Čekaju se zadaci"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Planirani zadaci"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Propali zadaci"

#: common/serializers.py:519
msgid "Task ID"
msgstr "ID zadatka"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "Jedinstveni ID zadatka"

#: common/serializers.py:521
msgid "Lock"
msgstr "Zaključaj"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Vreme zaključavanja"

#: common/serializers.py:523
msgid "Task name"
msgstr "Naziv zadatka"

#: common/serializers.py:525
msgid "Function"
msgstr "Funkcija"

#: common/serializers.py:525
msgid "Function name"
msgstr "Ime funkcije"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Argumenti"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Argumenti zadatka"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Ključne reči argumenata"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Ključne reči argumenata zadatka"

#: common/serializers.py:640
msgid "Filename"
msgstr "Ime datoteke"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Tip modela"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Korisnik nema dozvolu da napravi ili izmeni priloge za ovaj model"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr "Lista odabira je zaključana"

#: common/setting/system.py:97
msgid "No group"
msgstr "Nema grupe"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "URL sajta je zaključan od strane konfiguracije"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Ponovno pokretanje potrebno"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "Podešavanje je izmenjeno i zahteva ponovno pokretanje servera"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Migracije na čekanju"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Broj migracija baze podataka koje su na čekanju"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:199
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Ime instance servera"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Stringovni opis instance servera"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Ime instance korisnika"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Koristi ime instance u naslovnoj liniji"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Zabrani prikazivanje `O nama`"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Prikaži `O nama` samo superkorisnicima"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Ime kompanije"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Interno ime kompanije"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "Osnovni URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "Osnovni URL za instancu servera"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Podrazumevana valuta"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Izaberi osnovnu valutu za određivanje cena"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Podržane valute"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Lista kodova podržanih valuta"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Interval ažuriranja valuta"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Koliko često ažurirati devizne kurseve (podesi na nulu za onemogućti)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "dani"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Dodatak za ažuriranje valute"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Dodatak za ažuriranje valute koji će se koristiti"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Skini sa URL"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Dozvoli skidanje sa udaljenih lokacija slika i datoteka"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Ograničenje veličine skidanja"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Maksimalna dozvoljena veličina slike koja se skida"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "Korisnik-agent koji se koristi za skidanje sa URL"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Dozvoli premošćavanje koji će se korisnik-agent koristiti za skidanje slika i datoteka sa spoljašnjeg URL (ostavi prazno da se podesi kao podrazumevano)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Stroga validacija URL"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Traži specifikaciju za validaciju URL-ova"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Ažuriraj interval provere"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Koliko često da proveravam za nova ažuriranja? (podesi na nulu da bi isključio)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Automatsko pravljenje rezervne kopije"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Omogući automatsko pravljenje rezervne kopije baze podataka i medijskih datoteka"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Automatski interval pravljenja rezervnih kopija"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Odredi broj dana između automatskih pravljenja rezervnih kopija"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Interval brisanja zadataka"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Rezultati pozadinskih zadataka biće izbrisani nakon određenog broja dana "

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Interval brisanja evidencije grešaka"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Evidencija grešaka biće izbrisana nakon određenog broja dana"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Interval brisanja obaveštenja"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Korisnička obaveštenja biće izbrisana nakon određenog broja dana"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Podrška za bar kod"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Omogući podršku za bar kod skener preko interfejsa stranice"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "Uskladišti rezultate bar koda"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "Uskladišti rezultate bar koda u bazu podataka"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "Maksimalan broj skeniranja bar koda"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "Maksimalan broj rezultata skeniranja bar koda koji treba da se skladišti"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Kašnjenje unosa bar koda"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Vreme kašnjena obrađivanja ulaza bar koda"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Podrška za bar kod veb kameru"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Omogući skeniranje bar koda pomoću veb kamere u pretraživaču"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Prikaži podatke bar koda"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Prikaži podatke bar koda u pretraživaču kao tekst"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "Dodatak za generisanje bar koda"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "Dodatak koji će se koristiti kao interni generator podataka bar koda"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Revizije dela"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Omogući polje za reviziju dela"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "Jedino revizija sastavljanja "

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "Dozvoli jedino revizije za sastavne delove"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Dozvoli brisanje iz sastavljanja"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Dozvoli brisanje delova koji su korišćeni u sastavljanju"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "Interni broj dela regex"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Regularni obrazac izraza za podudaranje IPN dela"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Dozvoli duple IPN"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Dozvoli da više delova dele isti IPN"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Dozvoli izmenu IPN"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Dozvoli izmenu IPN vrednosti u toku izmene dela"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Kopiraj BOM podatke dela"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Podrazumevaj kopiranje BOM podataka prilikom pravljenja duplikata dela "

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Kopiraj podatke parametara dela"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Podrazumevaj kopiranje podataka parametara dela prilikom pravljenja duplikata dela"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Kopiraj podatke testiranja dela"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Podrazumevaj kopiranje podataka testiranja dela prilikom pravljenja duplikata dela"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Kopiraj šablone parametara kategorije"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Kopiraj šablone parametara kategorije prilikom pravljenja dela"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Šablon"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Podrazumevano je da su delovi šabloni"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Podrazumevano je da se delovi mogu sastavljati od drugih komponenti"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Komponenta"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Podrazumevano je da se delovi mogu koristi kao pod-komponente"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Može da se kupi"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Podrazumevano je da se delovi mogu kupiti"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Može da se proda"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "podrazumevano je da delovi mogu da se prodaju"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Podrazumevano je da delovi mogu da se prate"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtuelni"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Podrazumevano je da su delovi virtuelni"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Prikaži povezane delove"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Prikaži povezane delove za deo"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Inicijalni podaci zaliha"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Dozvoli kreiranje inicijalne alihe prilikom dodavanja novog dela"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Podaci inicijalnog dobavljača"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Dozvoli kreiranje inicijalnog dobavljača prilikom dodavanja novog dela"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Format prikazivanja imena dela"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Format u kome će se prikazivati ime dela"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Podrazumevana ikona za kategoriju dela"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Podrazumevana ikona za kategoriju dela (prazno znači bez ikone)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Zahtevaj jedinice parametara"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Ako su jedinice date, vrednosti parametara moraju odgovarati datim jedinicama"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Minimalan broj decimalnih mesta za cene"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Minimalan broj decimalnih mesta prilikom generisanja cenovnih podataka"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Maksimalan broj decimalnih mesta za cene"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Maksimalan broj decimalnih mesta prilikom generisanja cenovnih podataka"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Koristi cene dobavljača"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Uključi pauziranje cene dobavljača u sveukupnom računanju cene"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Premosti istorijat kupovina"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Prethodne cene narudžbenice zamenjuje pauze cena dobavljača"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Koristi cene stavki u zalihama"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Koristi cene koje su ručno unete u podatke zaliha"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Godina cena stavki u zalihama"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Odstrani stavke zaliha iz kalkulacija cena, koje su starije od ovog broja dana"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Koristi drugačije cene"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Uključi drugačije cene u sveukupnim kalkulacijama cene"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Samo aktivne varijante"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Koristi samo aktivne varijante za određivanje varijante cene"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Vremenski period za ponovno određivanje cena"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Broj dana koji treba da prođe da bi se cene delova automatski ažurirale"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Interne cene"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Omogući interne cene za delove"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Premošćavanje internih cena"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Ako su dostupne, interne cene premošćuju kalkulacije opsega cena"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Omogući štampanje etiketa"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Omogući štampanje etiketa preko web interfejsa"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "DPI slike etikete"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "DPI rezolucija prilikom generisanja slikovne datoteke za dodatak koji štampa etikete"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Omogući izveštaje"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Omogući generisanje izveštaja"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Mod otklanjanja grešaka"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Generiši izveštaje u modu za otklanjanje grešaka (izlaz je u HTML)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Greške evidencije izveštaja"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Greške evidentiranja koje se dese prilikom generisanja izveštaja"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Veličina stranice"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Podrazumevana veličina strane za izveštaje u PDF formatu"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Globalno jedinstveni serijski brojevi"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "Serijski brojevi za stavke zaliha moraju da budu globalno jedinstveni"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Obriši ispražnjene zalihe"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Ovo određuje podrazumevano ponašanje kada je stavka zaliha istrošena"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Šablon koda serije"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Šablon za generisanje podrazumevanih kodova serije stavki u zalihama"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Datum isteka zaliha"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Omogući funkcionalnost isteka zaliha"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Prodaja isteklih zaliha"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Dozvoli prodaju isteklih zaliha"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Vreme zastarevanja zaliha"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Broj dana tokom kojih će se stavke zaliha smatrati zastarelim pre isteka"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Izrada sa isteklim zalihama"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Dozvoli izradu sa isteklim zalihama"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Vlasnička kontrola zaliha"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Omogući vlasničku kontrolu nad lokacijama zaliha i stavkama"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Podrazumevana ikonica lokacije zaliha"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Podrazumevana ikonica lokacije zaliha (prazno znači da nema ikonice)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Prikaži instalirane stavke sa zaliha"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Prikaži instalirane stavke sa zaliha u stok tabelama"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Proveri spisak materijala pri instalaciji stavki"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Instalirane stavke sa zaliha moraju postojati u spisku materijala nadređenog dela"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Dozvoli transfer van zaliha"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Dozvoli da stavke sa zaliha koje nisu na zalihama budu premeštane između lokacija zaliha"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Referentni šablon naloga za izradu"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Potreban šablon za generisanje referentnog polja naloga za izradu"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "Potreban odgovoran vlasnik"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "Odgovoran vlasnik mora biti dodeljen svakom nalogu"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "Potreban aktivan deo"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "Spreči kreiranje naloga za izradu za neaktivne delove"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "Potreban zaključan deo"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "Spreči kreiranje nalogaza izradu za otključane delove"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "Potreban validan spisak materijala"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Spreči kreiranje naloga za izradu pre validacije spiska materijala"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "Potrebno završavanje podređenih naloga"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "Spreči završavanje naloga za izradu pre završavanja svih podređenih naloga"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Blokiraj dok ne prođe test"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Spreči završavanje naloga za izradu pre uspešnog završetka svih testova"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Omogući naloge za vraćanje"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Omogući funkcionalnost vraćana u korisničkom interfejsu"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Referentni šablon naloga za vraćanje"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Potreban šablon pri generisanju referentnog polja naloga za vraćanje"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Izmeni završene naloge za vraćanje"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Dozvoli izmenu naloga za vraćanje nakon što su završeni"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Referentni šablon naloga za prodaju"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Potreban šablon pri generisanju referentnog polja naloga za prodaju"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Podrazumevana isporuka naloga za prodaju"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Omogućava kreiranje podrazumevane isporuke sa nalozima za prodaju"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Izmeni završene naloge za prodaju"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Dozvoli izmenu naloga za prodaju nakon što su isporučeni ili završeni"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Označi isporučene naloge kao završene"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Nalozi za prodaju označeni kao isporučeni će automatski biti završeni, zaobilazeći status isporučen"

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Referentni šablon naloga za kupovinu"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Potreban šablon pri generisanju referentnog polja naloga za kupovinu"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Izmeni završene naloge za kupovinu"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Dozvoli izmenu naloga za kupovinu nakon što su isporučeni ili završeni"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Automatski završi naloge za kupovinu"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Automatski označi naloge za kupovinu kao završene kada su primljene sve stavke porudžbine"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Omogući zaboravljenu lozinku"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Omogući funkcionalnost zaboravljene lozinke na stranicama za prijavljivanje"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Omogući registraciju"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Omogući registraciju korisnicima na stranicama za prijavljivanje"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "Omogući SSO"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "Omogući SSO na stranicama za prijavljivanje"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "Omogući SSO registraciju"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Omogući registraciju preko SSO za korisnike na stranicaa za prijavljivanje"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "Omogući SSO sinhronizaciju grupa"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Omogući sinhronizaciju grupa aplikacije sa grupama IdP-a"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "SSO ključ grupe"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "Nazivi grupa dobijaju atribute od IdP-a"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "Mapiranje SSO grupa"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Mapiranje SSO grupa u lokalne grupe aplikacije. Ukoliko lokalna grupa ne postoji, biće kreirana."

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "Ukloni grupe van SSO"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Da li će grupe dodeljene korisnicima biti uklonjene ukoliko nisu podržane IdP-om. Onemogućavanje ovoga može dovesti do problema."

#: common/setting/system.py:959
msgid "Email required"
msgstr "Email neophodan"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Zahtevaj od korisnika da dostavi mejl prilikom registracije"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "Automatski popuni SSO korisnike"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Automatski popuni korisnikove podatke iz SSO naloga"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "Email dva puta"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Pitaj korisnika dva puta za email prilikom registracije"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Lozinka dva puta"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Pitaj korisnika dva puta za lozinku prilikom registracije"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Dozvoljeni domeni"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Ograniči registraciju na određene domene (razdvojeni zapetom, počinju sa @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Grupa pri registrovanju"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Grupa kojoj se novi korisnici dodeljuju pri registraciji. Ukoliko je SSO group sync omogućen, ova grupa će se dodavati ukoliko korisnik ne može da dobije grupu iz IdP-a."

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "Nametni MFA"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Korisnici moraju koristiti multifaktorsku bezbednost"

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Proveri plugine pri pokretanju"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Proveri da li su svi pluginovi instalirani pri pokretanju - omogućeni u kontejnerskim okruženjima"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Proveri ažuriranja pluginova"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Omogući periodično proveranje pluginova"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "Omogući URL integraciju"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Omogući da pluginovi dodaju URL rute"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Omogući integraciju u navigaciju"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Omogući integraciju pluginova u navigaciju"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "Omogući integraciju aplikacija"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Omogući pluginovima da dodaju aplikacije"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Omogući integraciju planiranja"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Omogući da plugini izvršavaju planirane zadatke"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Omogući integraciju događaja"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Omogući da plugini odgovaraju na unutrašnje događaje"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "Omogući integraciju interfejsa"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "Omogući integraciju pluginova u korisnički interfejs"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Ne uključuj eksterne lokacije"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Period automatskog popisa"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Prikaži puna imena korisnika"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Prikaži puna imena korisnika umesto korisničkih imena"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Omogući podatke test stanica"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Omogući prikupljanje podataka sa test stanica radi rezultata testova"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Inlajn prikaz natpisa"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Prikaži PDF natpise u pretraživaču umesto preuzimanja kao fajla"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Podrazumevani štampač natpisa"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Konfiguriši koji štampač natpisa će biti izabran kao podrazumevani"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Inlajn prikaz izveštaja"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Prikaži PDF izveštaje u pretraživaču umesto preuzimanja kao fajla"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Pretraga delova"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Prikaži delove u prozoru za pregled pretrage"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Pretraga delova dobavljača"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Prikaži delove dobavljača u prozoru za pregled pretrage"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Pretraga delova proizvođača"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Prikaži delove proizvođača u prozoru za pregled pretrage"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Sakrij neaktivne delove"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Isključeni neaktivni delovi iz prozora za pregled pretrage"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Pretraga kategorija"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Prikaži kategorije delova u prozoru za pregled pretrage"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Pretraga zaliha"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Prikaži stavke sa zaliha u prozoru za pregled pretrage"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Sakrij stavke sa zaliha koje nisu dostupne"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Ne uključuj stavke sa zaliha koje nisu dostupne u prozor za pregled pretrage"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Pretraga lokacija"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Prikaži lokacije zaliha u prozoru za pregled pretrage"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Pretraga kompanija"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Prikaži kompanije u prozoru za pregled pretrage"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Pretraga naloga za izradu"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Prikaži naloge za izradu u prozoru za pregled pretrage"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Pretraga naloga za kupovinu"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Prikaži naloge za kupovinu u prozoru za pregled pretrage"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Ne uključuj neaktivne naloge za kupovinu"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Ne uključuj neaktivne naloge za kupovinu u prozor za pregled pretrage"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Pretraga naloga za prodaju"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Prikaži naloge za prodaju u prozoru za pregled pretrage"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Ne uključuj neaktivne naloge za prodaju"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Ne uključuj neaktivne naloge za prodaju u prozor za pregled pretrage"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Pretraga naloga za vraćanje"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Prikazuj naloge za vraćanje u prozoru za pregled pretrage"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Ne uključuj neaktivne naloge za vraćanje"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Ne uključuj neaktivne naloge za vraćanje u prozor za pregled pretrage"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Prikazivanje rezultata pretrage"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Broj rezultata koji će se prikazivati u svakoj sekciji prozora za pregled pretrage"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex pretraga"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Omogući regularne izraze u upitima pretrage"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Pretraga celih reči"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Upiti pretrage vraćaju rezultate za poklapanje celih reči"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Escape dugme zatvara forme"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Koristi escape dugme za zatvaranje modalnih formi"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Fiksirana traka za navigaciju"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "Pozicija trake za navigaciju je fiksirana na vrhu ekrana"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Format datuma"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Željeni format za prikazivanje datuma"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Primaj izveštaje o greškama"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Primaj notifikacije za sistemske greške"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Poslednje korišćene mašine za štampanje"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Sačuvaj poslednju korišćenu mašinu za štampanje za korisnika"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Nije dostavljen tip modela priloga"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Nevažeći tip modela priloga"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Minimalno mesta ne sme biti veće od maksimalno mesta"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Maksimalno mesta ne sme biti manje od minimalno mesta"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Prazan domen nije dozvoljen."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Nevažeće ime domena: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "Vrednost mora biti napisana velikim slovima"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "Vrednost mora biti važeći identifikator promenljive"

#: company/api.py:141
msgid "Part is Active"
msgstr "Deo je aktivan"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Proizvođač je aktivan"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Deo dobavljača je aktivan"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Interni deo je aktivan"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Dobavljač je aktivan"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Proizvođač"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Kompanija"

#: company/api.py:316
msgid "Has Stock"
msgstr "Ima zalihe"

#: company/models.py:120
msgid "Companies"
msgstr "Kompanije"

#: company/models.py:148
msgid "Company description"
msgstr "Opis kompanije"

#: company/models.py:149
msgid "Description of the company"
msgstr "Opis kompanije"

#: company/models.py:155
msgid "Website"
msgstr "Vebsajt"

#: company/models.py:156
msgid "Company website URL"
msgstr "Vebsajt kompanije"

#: company/models.py:162
msgid "Phone number"
msgstr "Broj telefona"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Broj telefona kontakta"

#: company/models.py:171
msgid "Contact email address"
msgstr "Email adresa kontakta"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Kontakt"

#: company/models.py:178
msgid "Point of contact"
msgstr "Osoba za kontakt"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Link ka eksternim informacijama o kompaniji"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Da li je ova kompanija aktivna?"

#: company/models.py:203
msgid "Is customer"
msgstr "Je mušterija"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Da li prodajete stavke ovoj kompaniji?"

#: company/models.py:209
msgid "Is supplier"
msgstr "Je dobavljač"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Da li kupujete stavke od ove kompanije?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "Je proizvođač"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Da li ova kompanija proizvodi delove?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Podrazumevana valuta za ovu kompaniju"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Adrese"

#: company/models.py:355
msgid "Addresses"
msgstr "Adrese"

#: company/models.py:412
msgid "Select company"
msgstr "Izaberi kompaniju"

#: company/models.py:417
msgid "Address title"
msgstr "Naslov adrese"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Naslov koji opisuje adresu"

#: company/models.py:424
msgid "Primary address"
msgstr "Primarna adresa"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Postavi kao primarnu adresu"

#: company/models.py:430
msgid "Line 1"
msgstr "Telefon 1"

#: company/models.py:431
msgid "Address line 1"
msgstr "Adresa 1"

#: company/models.py:437
msgid "Line 2"
msgstr "Telefon 2"

#: company/models.py:438
msgid "Address line 2"
msgstr "Adresa 2"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "Poštanski broj"

#: company/models.py:451
msgid "City/Region"
msgstr "Grad/region"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Poštanski broj"

#: company/models.py:458
msgid "State/Province"
msgstr "Država/provincija"

#: company/models.py:459
msgid "State or province"
msgstr "Država ili provincija"

#: company/models.py:465
msgid "Country"
msgstr "Zemlja"

#: company/models.py:466
msgid "Address country"
msgstr "Adresa zemlje"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Beleške za kurira"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Beleške za kurira"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Interne beleške o isporuci"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Beleške o isporuci za internu upotrebu"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Link za adresne informacije (eksterni)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Deo proizvođača"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Osnovni deo"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Izaberi deo"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Izaberi proizvođača"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "Broj dela proizvođača"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Broj dela proizvođača"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "URL za link eksternog dela proizvođača"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Opis dela proizvođača"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Parametar dela proizvođača"

#: company/models.py:635
msgid "Parameter name"
msgstr "Naziv parametra"

#: company/models.py:642
msgid "Parameter value"
msgstr "Vrednost parametra"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Jedinice"

#: company/models.py:650
msgid "Parameter units"
msgstr "Jedinice parametra"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Jedinice pakovanja moraju biti kompatibilne sa osnovnim jedinicama dela"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Jedinice pakovanja moraju biti veće od nule"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "Povezani delovi dobavljača moraju referencirati isti osnovni deo"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Dobavljač"

#: company/models.py:829
msgid "Select supplier"
msgstr "Izaberi dobavljača"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Jedinica za držanje dobavljačevih zaliha"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Da li je ovaj deo dobavljača aktivan?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Izaberi deo proizvođača"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "URL za link dela eksternog dobavljača"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Opis dela dobavljača"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Beleška"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "osnovni trošak"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimalna naplata (npr. taksa za slaganje)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Pakovanje"

#: company/models.py:892
msgid "Part packaging"
msgstr "Pakovanje delova"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Količina pakovanja"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Ukupna količina dostavljena u jednom pakovanju. Ostaviti prazno za pojedinačne stavke."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "više"

#: company/models.py:919
msgid "Order multiple"
msgstr "Naruči više"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Količine dostupne od dobavljača"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Dostupnost ažurirana"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Datum poslednjeg ažuriranja podataka o dostupnosti"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Smanjenje cene dobavljača"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Podrazumevana valuta koja se koristi za ovog dobavljača"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Naziv kompanije"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "Na zalihama"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Dodatne statusne informacije za ovu stavku"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Prilagođen ključ statusa"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Prilagođeno"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Klasa"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Vrednosti"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Postavljen"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Nevažeći statusni kod"

#: importer/models.py:73
msgid "Data File"
msgstr "Datoteka"

#: importer/models.py:74
msgid "Data file to import"
msgstr "Fajl sa podacima za uvoz"

#: importer/models.py:83
msgid "Columns"
msgstr "Kolone"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:96
msgid "Import status"
msgstr "Status uvoza"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Podrazumevane vrednosti polja"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "Promene polja"

#: importer/models.py:120
msgid "Field Filters"
msgstr "Filteri polja"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "Neka neophodna polja nisu mapirana"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "Kolona je već mapirana u polje u bazi podataka"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "Polje je već mapirano u kolonu sa podacima"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "Mapiranje kolona mora biti linkovano da bi se uvezla važeća sesija"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "Kolona ne postoji u fajlu sa podacima"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "Polje ne postoji u ciljnom modelu"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "Izabrano polje je samo za čitanje"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "Uvezi sesiju"

#: importer/models.py:471
msgid "Field"
msgstr "Polje"

#: importer/models.py:473
msgid "Column"
msgstr "Kolona"

#: importer/models.py:542
msgid "Row Index"
msgstr "Indeks vrsta"

#: importer/models.py:545
msgid "Original row data"
msgstr "Originalni podaci vrste"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Greške"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Važeće"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Nije podržan format fajla sa podacima"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Greška pri otvaranju fajla sa podacima"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Nevažeće dimenzije fajla sa podacima"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "Nevažeće podrazumevane vrednosti polja"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "Nevažeće promene polja"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "Nevažeći filteri polja"

#: importer/serializers.py:177
msgid "Rows"
msgstr "Vrste"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Lista ID-jeva vrsta za prihvatanje"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Nema dostavljenih vrsta"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "Vrsta ne pripada ovoj sesiji"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "Vrsta sadrži nedozvoljene podatke"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "Vrsta je već završena"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Inicijalizacija"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Mapiranje kolona"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Uvoženje podataka"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Obrađivanje podataka"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Veličina fajla sa podacima prelazi maksimalnu veličinu"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Fajl sa podacima ne sadrži hedere"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Fajl sa podacima sadrži previše kolona"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Fajl sa podacima sadrži previše vrsta"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Vrednosti moraju biti validni objekti"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Kopije"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Broj kopija za štampanje od svakog natpisa"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Konektovano"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Nepoznato"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Štampanje"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Nema medijuma"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Papir zaglavljen"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Diskonektovano"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Štampač natpisa"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Direktno štampaj natpise za razne stavke"

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Lokacija štampača"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Podesi štampač na specifičnu lokaciju"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Naziv mašine"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Tip mašine"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Tip mašine"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Drajver"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Drajver koji se koristi za ovu mašinu"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Mašine mogu biti onemogućene"

#: machine/models.py:95
msgid "Driver available"
msgstr "Drajver dostupan"

#: machine/models.py:100
msgid "No errors"
msgstr "Nema grešaka"

#: machine/models.py:105
msgid "Initialized"
msgstr "Inicijalizovano"

#: machine/models.py:117
msgid "Machine status"
msgstr "Status mašine"

#: machine/models.py:145
msgid "Machine"
msgstr "Mašina"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Mašinska konfiguracija"

#: machine/models.py:162
msgid "Config type"
msgstr "Tip konfiguracije"

#: order/api.py:121
msgid "Order Reference"
msgstr "Referenca naloga"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "Izvanredno"

#: order/api.py:165
msgid "Has Project Code"
msgstr "Ima šifru projekta"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Kreirano do strane"

#: order/api.py:183
msgid "Created Before"
msgstr "Kreirano pre"

#: order/api.py:187
msgid "Created After"
msgstr "Kreirano nakon"

#: order/api.py:191
msgid "Has Start Date"
msgstr ""

#: order/api.py:199
msgid "Start Date Before"
msgstr ""

#: order/api.py:203
msgid "Start Date After"
msgstr ""

#: order/api.py:207
msgid "Has Target Date"
msgstr ""

#: order/api.py:215
msgid "Target Date Before"
msgstr "Krajnji datum pre"

#: order/api.py:219
msgid "Target Date After"
msgstr "Krajnji datum nakon"

#: order/api.py:270
msgid "Has Pricing"
msgstr "Ima cenu"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr "Završen pre"

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "Završen nakon"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Nalog"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "Nalog završen"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Interni deo"

#: order/api.py:578
msgid "Order Pending"
msgstr "Nalog na čekanju"

#: order/api.py:958
msgid "Completed"
msgstr "Završeno"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "Ima isporuku"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Nalog za kupovinu"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Nalog za prodaju"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Nalog za vraćanje"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Ukupna cena"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Totalna cena ovog naloga"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Valuta naloga"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Valuta za ovaj nalog (ostaviti prazno za podrazumevanu valutu kompanije)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "Kontakt se ne poklapa sa izabranom kompanijom"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Opis naloga (opciono)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Izaberi šifru projekta za ovaj nalog"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Link ka eksternoj stranici"

#: order/models.py:458
msgid "Start date"
msgstr ""

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Ciljani datum"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Očekivani datum za isporuku. Nalog će biti zastareo nakon ovog datuma."

#: order/models.py:487
msgid "Issue Date"
msgstr "Datum izdavanja"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Datum kada je nalog izdat"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Korisnik ili grupa odgovorni za ovaj nalog"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Lice za kontakt za ovaj nalog"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Adresa kompanije za ovaj nalog"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Referenca naloga"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Status"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Status naloga za kupovinu"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Kompanija od koje su stavke naručene"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Referenca dobavljača"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Referentni kod dobavljača naloga"

#: order/models.py:654
msgid "received by"
msgstr "primljeno od strane"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Datum kada je nalog završen"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Odredište"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr "Odredište za primljene stavke"

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Dobavljač dela se mora poklapati sa dobavljačem naloga za kupovinu"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "Stavka porudžbine se ne poklapa sa nalogom za kupovinu"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "Količina mora biti pozitivan broj"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Mušterija"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Kompanija kojoj se prodaju stavke"

#: order/models.py:1318
msgid "Sales order status"
msgstr "Status naloga za prodaju"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Referenca mušterije"

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Referentni kod mušterijinog naloga"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Datum isporuke"

#: order/models.py:1343
msgid "shipped by"
msgstr "isporučeno od strane"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "Nalog je već završen"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "Nalog je već otkazan"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Samo otvoren nalog može biti označen kao završen"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Nalog ne može biti završen jer ima nepotpunih isporuka"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "Nalog ne može biti završen jer ima nepotpunih alokacija"

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Nalog ne može biti završen jer ima nezavršenih stavki porudbžine"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1711
msgid "Item quantity"
msgstr "Količina stavki"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Referenca stavke porudbžine"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Beleške stavke porudbžine"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Krajnji datum za ovu stavku porudbćine (ostaviti prazno za krajnji datum sa naloga)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Opis stavke porudžbine (opciono)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Dodatni kontekst za ovu porudžbinu"

#: order/models.py:1788
msgid "Unit price"
msgstr "Cena jedinice"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "Stavka porudžbine naloga za kupovinu"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "Deo dobavljača se mora poklapati sa dobavljačem"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr "Deo dobavljača"

#: order/models.py:1891
msgid "Received"
msgstr "Primljeno"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Broj primljenih stavki"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Kupovna cena"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Kupovna cena jedinice"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "Dodatna porudbžina naloga za kupovinu"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "Stavka porudžbine naloga za prodaju"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtuelni deo ne može biti dodeljen nalogu za prodaju"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Samo delovi koji se mogu prodati mogu biti dodeljeni nalogu za prodaju"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Prodajna cena"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Prodajna cena jedinice"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Poslato"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Isporučena količina"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "Isporuka naloga za prodaju"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Datum isporuke"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Datum dostavljanja"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Datum dostavljanja isporuke"

#: order/models.py:2221
msgid "Checked By"
msgstr "Provereno od strane"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Korisnik koji je proverio ovu isporuku"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Isporuka"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Broj isporuke"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Broj praćenja"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Informacije o praćenju isporuke"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "Broj računa"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Referentni broj za dodeljeni račun"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "Isporuka je već poslata"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "Isporuka nema alocirane stavke sa zaliha"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "Dodatne porudbžine naloga za prodaju"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "Alokacije naloga za prodaju"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "Stavka sa zaliha nije dodeljena"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Ne mogu se alocirati stavke sa zaliha porudbžini sa drugačijim delom"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Ne mogu se alocirati zalihe porudbžini bez dela"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Alocirana količina ne sme da pređe količinu zaliha"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "Količina mora biti 1 za serijalizovane stavke sa zaliha"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "Nalog za prodaju se ne poklapa sa isporukom"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Isporuka se ne poklapa sa nalogom za prodaju"

#: order/models.py:2451
msgid "Line"
msgstr "Porudbžina"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Referenca isporuke naloga za prodaju"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Stavka"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Izaberi stavku sa zaliha za alokaciju"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Unesi količinu za alokaciju zaliha"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Referenca naloga za vraćanje"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Kompanija čije stavke su vraćene"

#: order/models.py:2625
msgid "Return order status"
msgstr "Status naloga za vraćanje"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "Vrati stavku porudbžine"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr "Stavka sa zaliha mora biti određena"

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr "Količina vraćanja je premašila količinu zaliha"

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr "Količina vraćanja mora biti veća od nule"

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr "Nevažeća količina za serijalizovane stavke sa zaliha"

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Izaberi stavku za vraćanje od mušterije"

#: order/models.py:2910
msgid "Received Date"
msgstr "Primljeno datuma"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "Datum kada je ova vraćena stavka primljena"

#: order/models.py:2923
msgid "Outcome"
msgstr "Ishod"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "Ishod za ovu stavku porudžbine"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "Trošak asociran sa popravkom ili vraćanjem ove stavke porudžbine"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr "Doda"

#: order/serializers.py:90
msgid "Order ID"
msgstr "ID naloga"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "ID naloga koji će se duplirati"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "Kopiraj porudžbine"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr "Kopiraj stavke porudžbine sa originalnog naloga"

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr "Kopiraj dodatne porudžbine"

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr "Kopiraj dodatne stavke porudžbine sa originalnog naloga"

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Stavke porudbžine"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "Završene porudbžine"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "Dupliraj nalog"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr "Odredi opcije za dupliranje ovog naloga"

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "Nevažeći ID naloga"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Naziv dobavljača"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "Nalog ne može biti otkazan"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Dozvoli da nalog bude zatvoren sa nepotpunim porudžbinama"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "Nalog ima nepotpune stavke porudžbine"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "Nalog nije otvoren"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "Automatske cene"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Automatski izračunaj kupovnu cenu na osnovu podataka o delovima dobavljača"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Valuta kupovne cene"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Spoj stavke"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Spoj stavke sa istim delom, odredištem i ciljanim datumom u jednu stavku porudžbine"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "Jedinica za praćenje zaliha"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Interni broj dela"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "Interni naziv dela"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "Deo dobavljača mora biti određen"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "Nalog za kupovinu mora biti određen"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "Dobavljač mora da se poklapa sa nalogom za kupovinu"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "Nalog za kupovinu mora da se poklapa sa dobavljačem"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Stavka porudbžine"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Izaberi odredišnu lokaciju za primljene stavke"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Unesi šifru ture za nadolazeće stavke sa zaliha"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Datum isteka"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Unesi serijske brojeve za nadolazeće stavke sa zaliha"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr "Promeni informacije o pakovanju za nadolazeće stavke sa zaliha"

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr "Dodatne beleške za nadolazeće stavke sa zaliha"

#: order/serializers.py:826
msgid "Barcode"
msgstr "Bar kod"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Skeniran bar kod"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Bar kod je već u upotrebi"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Stavke porudžbine moraju biti dostavljene"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "Odredišna lokacija mora biti određena"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "Pružene vrednosti bar kodova moraju biti jedinstvene"

#: order/serializers.py:1066
msgid "Shipments"
msgstr "Isporuke"

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Završene isporuke"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Valuta prodajne cene"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr "Alocirane stavke"

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Nisu dostavljeni detalji isporuke"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "Stavka porudžbine nije asocirana sa ovim nalogom"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "Količina mora biti pozitivna"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Unesi serijske brojeve za alokaciju"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "Isporuka je već isporučena"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "Isporuka nije povezana sa ovim nalogom"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Nema pronađenih poklapanja za sledeće serijske brojeve"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "Sledeći serijski brojevi su nedostupni"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "Stavka porudžbine naloga za vraćanje"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "Stavka porudžbine se ne poklapa sa nalogom za vraćanje"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "Stavka porudžbine je već primljena"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "Stavke se mogu primiti samo na osnovu naloga koji su u toku"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr "Količina za vraćanje"

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "Valuta cene porudžbine"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Izgubljeno"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Vraćeno"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "U progresu"

#: order/status_codes.py:105
msgid "Return"
msgstr "Vrati"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Popravi"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Zameni"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Refundiraj"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Odbij"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Istekli nalozi za kupovinu"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Nalog za kupovinu {po} je sada istekao"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Istekli nalozi za prodaju"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Nalog za prodaju {so} je sada istekao"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:111
msgid "Starred"
msgstr "Označeno zvezdicom"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "Filtiraj po kategorijama označenim zvezdicom"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Dubina"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "Filtriraj po dubini kategorije"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "Vrhovni"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "Filtriraj po vrhovnim kategorijama"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "Kaskadno"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "Uključi pod-kategorije u filtriranim rezultatima"

#: part/api.py:185
msgid "Parent"
msgstr "Nadređen"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Filtriraj po nadređenoj kategoriji"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "Ne uključuj pod-kategorije pod specifičnom kategorijom"

#: part/api.py:434
msgid "Has Results"
msgstr "Ima rezultate"

#: part/api.py:660
msgid "Is Variant"
msgstr ""

#: part/api.py:668
msgid "Is Revision"
msgstr "Je revizija"

#: part/api.py:678
msgid "Has Revisions"
msgstr "Ima revizije"

#: part/api.py:859
msgid "BOM Valid"
msgstr "Spisak materijala validan"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "Deo sklopa se može testirati"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "Deo komponente se može testirati"

#: part/api.py:1576
msgid "Uses"
msgstr "Koristi"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Kategorija dela"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Kategorije delova"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Podrazumevana lokacija"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Podrazumevana lokacija za delove ove kategorije"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Strukturno"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Delovi ne mogu biti direktno dodeljene strukturnoj kategoriji, ali mogu biti dodeljeni podređenim kategorijama."

#: part/models.py:134
msgid "Default keywords"
msgstr "Podrazumevane ključne reči"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Podrazumevane ključne reči za delove ove kategorije"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Ikonica"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Ikonica (opciono)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Ova kategorija dela se ne može podesiti kao strukturna jer već ima dodeljene neke delove!"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Delovi"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "Ovaj deo se ne može izbrisati jer je zaključan"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Ovaj deo se ne može izbrisati jer je i dalje aktivan"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Ovaj deo se ne može obrisati jer se koristi u sklopu"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Deo '{self}' ne može biti korišćen u spisku materijala za '{parent}' (recursive)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Deo '{parent}' se koristi u spisku materijala za '{self}' (recursive)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "Interni broj dela se mora slagati sa regex šablonom {pattern}"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "Deo ne može biti revizija samog sebe"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Ne može se kreirati revizija dela koji je već revizija"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "Šifra revizije mora biti dostavljena"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "Revizije su dozvoljene samo za delove sklopove"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "Ne može se izvršiti revizija šablonskog dela"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "Nadređeni deo mora biti vezan sa istim šablonom"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Stavka sa ovim serijskim brojem već postoji"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "Duplirani interni brojevi dela nisu dozvoljeni u podešavanjima dela"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "Identična revizija dela već postoji"

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Deo sa ovim nazivom, internim brojem dela i revizijom već postoji"

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Delovi ne mogu biti dodeljeni strukturnim kategorijama delova!"

#: part/models.py:1051
msgid "Part name"
msgstr "Naziv dela"

#: part/models.py:1056
msgid "Is Template"
msgstr "Jeste šablon"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Da li je ovaj deo šablonski deo?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Da li je ovaj deo varijanta drugog dela?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Varijanta od"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Opis dela (opciono)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Ključne reči"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Ključne reči dela da bi se poboljšala vidljivost u rezultatima pretrage"

#: part/models.py:1093
msgid "Part category"
msgstr "Kategorija dela"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "Interni broj dela"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Revizija dela ili broj verzije"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Revizija"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "Da li je ovaj deo revizija drugog dela?"

#: part/models.py:1119
msgid "Revision Of"
msgstr "Revizija od"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Gde je ova stavka inače skladištena?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Podrazumevani dobavljač"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Podrazumevani deo dobavljača"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Podrazumevani istek"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Vreme isteka (u danima) za stavke sa zaliha ovog dela"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Minimalne zalihe"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Minimalni dozvoljen nivo zaliha"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Jedinice mere za ovaj deo"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Da li ovaj deo može biti izgrađen od drugih delova?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Da li ovaj deo može biti korišćen za izradu drugih delova?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Da li ovaj deo ima praćenje za više stavki?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "Da li ovaj deo može imati svoje rezultate testa?"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Da li ovaj deo može biti kupljen od eksternih dobavljača?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Da li ovaj deo može biti prodat mušterijama?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Da li je ovaj deo aktivan?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "Zaključani delovi se ne mogu menjati"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Da li je ovo virtuelni deo, kao na primer softver ili licenca?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "Suma spiska materijala"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Uskladištena suma spiska materijala"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "Spisak materijala proveren od strane"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "Spisak materijala proveren datuma"

#: part/models.py:1312
msgid "Creation User"
msgstr "Korisnik koji je kreirao"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Vlasnik odgovoran za ovaj deo"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Prodaj više"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Valuta korišćena za vršenje proračuna o cenama"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Minimalna vrednost spiska materijala"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Minimalna vrednost komponenti delova"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Maksimalna vrednost spiska materijala"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Maksimalna vrednost komponenti delova"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Minimalna kupovna vrednost"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Minimalna istorijska kupovna vrednost"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Maksimalna kupovna vrednost"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Maksimalna istorijska kupovna vrednost"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Minimalna interna cena"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Minimalna cena bazirana na internim sniženjima cena"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Maksimalna interna cena"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Maksimalna vrednost bazirana na internim sniženjima cena"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Minimalna cena dobavljača"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Minimalna cena dela od eksternih dobavljača"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Maksimalna cena dobavljača"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Maksimalna cena dela od eksternih dobavljača"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Minimalna vrednost varijanti"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Izračunata minimalna vrednost varijanti delova"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Maksimalna vrednost varijanti"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Izračunata maksimalna vrednost varijanti delova"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Minimalna vrednost"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Promeni minimalnu vrednost"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Maksimalna vrednost"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Promeni maksimalnu vrednost"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Ukupna izračunata minimalna vrednost"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Ukupna izračunata maksimalna vrednost"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Minimalna prodajna cena"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Minimalna prodajna cena bazirana na osnovu sniženja cena"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Maksimalna prodajna cena"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Maksimalna prodajna cena bazirana na osnovu sniženja cena"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Minimalna prodajna vrednost"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Minimalna istorijska prodajna cena"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Maksimalna prodajna vrednost"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Maksimalna istorijska prodajna cena"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Deo za popis"

#: part/models.py:3444
msgid "Item Count"
msgstr "Broj stavki"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Broj individualnih unosa zaliha u vreme popisa"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Ukupne dostupne zalihe za vreme popisa"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Datum"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Datum kada je izvršen popis"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Minimalna vrednost zaliha"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Procenjena minimalna vrednost trenutnih zaliha"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Maksimalna vrednost zaliha"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Procenjena maksimalna vrednost trenutnih zaliha"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "Smanjenje prodajne cene dela"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "Šablon testa dela"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Nevažeći naziv šablona - mora da uključuje bar jedan alfanumerički karakter"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "Izbori moraju biti jedinstveni"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "Test šabloni mogu biti kreirani samo za delove koje je moguće testirati"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "Test šablon sa istim ključem već postoji za ovaj deo"

#: part/models.py:3684
msgid "Test Name"
msgstr "Naziv testa"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Unesi naziv za ovaj test"

#: part/models.py:3691
msgid "Test Key"
msgstr "Test ključ"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "Pojednostavljen ključ za test"

#: part/models.py:3699
msgid "Test Description"
msgstr "Opis testa"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Unesi opis za ovaj test"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Omogućen"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "Da li je ovaj test omogućen?"

#: part/models.py:3709
msgid "Required"
msgstr "Neophodno"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Da li je neophodno da ovaj test prođe?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Zahteva vrednost"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Da li ovaj test zahteva vrednost prilikom dodavanja rezultata testa?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Zahteva prilog"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Da li ovaj test zahteva fajl kao prilog prilikom dodavanja rezultata testa?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "Izbori"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "Validni izbori za ovaj test (razdvojeni zapetom)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "Šablon parametra dela"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "Checkbox parametri ne mogu imati jedinice"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "Checkbox parametri ne mogu imati izbore"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "Ime šablona parametra mora biti jedinstveno"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Naziv parametra"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Fizičke jedinice za ovaj parametar"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Opis parametra"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Polje za potvrdu"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Da li je ovaj parametar checkbox?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Validni izbori za ovaj parametar (razdvojeni zapetom)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr "Lista izbora za ovaj parametar"

#: part/models.py:3931
msgid "Part Parameter"
msgstr "Parametar dela"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "Parametar se ne može modifikovati - deo je zaključan"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Nije validan izbor za vrednost parametra"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Nadređeni deo"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Šablon parametra"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Vrednost parametra"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Opciona beleška"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "Šablon parametara kategorije dela"

#: part/models.py:4176
msgid "Default Value"
msgstr "Podrazumevana vrednost"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Podrazumevana vrednost parametra"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "Stavke sa spiska materijala se ne mogu modifikovati - sklapanje je zaključano"

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "Stavke sa spiska materijala se ne mogu modifikovati - sklapanje varijanti je zaključano"

#: part/models.py:4363
msgid "Select parent part"
msgstr "Izaberi nadređeni deo"

#: part/models.py:4373
msgid "Sub part"
msgstr "Pod-deo"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Izaberi deo koji će biti korišćen u spisku materijala"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "Količina spiskova materijala za ovu stavku sa spiska materijala"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Ova stavka sa spiska materijala je opciona"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Ova stavka sa spiska materijala se može potrošiti (nije praćena u nalozima za izradu)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "Referenca stavke sa spiska materijala"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "Beleške stavki sa spiska materijala"

#: part/models.py:4451
msgid "Checksum"
msgstr "Suma"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "Suma spiska materijala"

#: part/models.py:4457
msgid "Validated"
msgstr "Validirano"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Ova stavka sa spiska materijala je validirana"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Biva nasleđeno"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Ova stavka sa spiska materijala je nasleđivana od spiska materijala za varijante delova"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Stavke sa zaliha za varijante delova se mogu koristiti za ovu stavku sa spiska materijala"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "Količina mora biti ceo broj za delove koji se mogu pratiti"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "Zamenski deo mora biti određen"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "Zamenska stavka sa spiska materijala"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "Zamenski deo ne može biti isti kao glavni deo"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Nadređena stavka sa spiska materijala"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Zamenski deo"

#: part/models.py:4798
msgid "Part 1"
msgstr "Deo 1"

#: part/models.py:4806
msgid "Part 2"
msgstr "Deo 2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "Izaberi povezan deo"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr "Beleška za ovu relaciju"

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Relacija između delova ne može biti kreirana između jednog istog dela"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "Identična veza već postoji"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "Nadređena kategorija"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "Nadređena kategorija dela"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Podkategorije"

#: part/serializers.py:202
msgid "Results"
msgstr "Rezultati"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "Broj rezultata napravljenih na osnovu ovog šablona"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Valuta kupovine za ovu stavku sa zaliha"

#: part/serializers.py:275
msgid "File is not an image"
msgstr ""

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "Broj delova koji koriste ovaj šablon"

#: part/serializers.py:480
msgid "Original Part"
msgstr "Originalni deo"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Izaberi originalni deo za duplikaciju"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Kopiraj sliku"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Kopiraj sliku sa originalnog dela"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Kopiraj spisak materijala"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Kopiraj spisak materijala sa originalnog dela"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Kopiraj parametre"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Kopiraj parametarske podatke sa originalnog dela"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Kopiraj beleške"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Kopiraj beleške sa originalnog dela"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Inicijalna količina zaliha"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Odredi inicijalnu količinu zaliha za ovaj deo. Ukoliko je količina nula, neće biti dodate zalihe."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Inicijalna lokacija zaliha"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Odredi inicijalnu lokaciju zaliha za ovaj deo"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Izaberi dobavljača (ostavi prazno za preskakanje)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Izaberi proizvođača (ostavi prazno za preskakanje)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Broj dela proizvođača"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "Izabrana kompanija nije validan dobavljač"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "Izabrana kompanija nije validan proizvođač"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "Deo proizvođača koji se poklapa sa ovim brojem dela proizvođača već postoji"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "Deo dobavljača koji se opklapa sa ovim brojem dela dobavljača već postoji"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Naziv kategorije"

#: part/serializers.py:936
msgid "Building"
msgstr "Izrađivanje"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Stavke sa zaliha"

#: part/serializers.py:968
msgid "Revisions"
msgstr "Revizije"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Dobavljači"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Ukupne zalihe"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "Nealocirane zalihe"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "Varijante zaliha"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Dupliraj deo"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Kopiraj inicijalne podatke od drugog dela"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Početne zalihe"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Kreiraj deo sa početnom količinom zaliha"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Informacije o dobavljaču"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Dodaj inicijalne informacije o dobavljaču za ovaj deo"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Kopiraj parametre kategorije"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Kopiraj parametarske šablone sa izabrane kategorije dela"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Postojeća slika"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "Ime fajla postojeće slike dela"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "Fajl sa slikom ne postoji"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Validiraj ceo spisak materijala"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Može se izgraditi"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Minimalna cena"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "Izmeni izračunatu vrednost za minimalnu cenu"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "Minimalna valuta cene"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Maksimalna cena"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "Izmeni izračunatu vrednost maksimalne cene"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "Maksimalna valuta cene"

#: part/serializers.py:1498
msgid "Update"
msgstr "Ažuriraj"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Ažuriraj cene za ovaj deo"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Nija moguća konverzija iz dostavljen valute u {default_currency}"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "Minimalna cena ne sme biti veća od maksimalne cene"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "Maksimalna cena ne sme biti manja od minimalne cene"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr "Izaberi nadređeni sklop"

#: part/serializers.py:1716
msgid "Select the component part"
msgstr "Izaberi komponentu dela"

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Izaberi deo sa kog će se kopirati spisak materijala"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Ukloni postojeće podatke"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Ukloni postojeće stavke sa spiska materijala pre kopiranja"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Uključi nasleđeno"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Uključi stavke sa spiska materijala koje su nasleđene od šablonskih delova"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Preskoči nevažeće vrste"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Omogući ovu opciju za preskakanje nevažećih vrsta"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Kopiraj zamenske delove"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Kopiraj zamenske delove prilikom duplikacije stavki sa spiska materijala"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Notifikacija o niskim zalihama"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Dostupne zalihe za {part.name} su pale ispod konfigurisanog minimalnog nivoa"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:107
msgid "Sample"
msgstr ""

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Instalirano"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "Plugin ne može biti obrisan jer je trenutno aktivan"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Nema određene akcije"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Nema poklapajuće akcije"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Nema poklapanja za podatke sa bar koda"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Pronađeno poklapanje za podatke sa bar koda"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Model nije podržan"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "Instanca modela nije pronađena"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Bar kod se poklapa sa postojećom stavkom"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Nema podudarajućih podataka o delovima"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Nema pronađenih podudarajućih delova dobavljača"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Više podudarajućih delova dobavljača pronađeno"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "Nema podudarajućeg plugina za podatke sa bar koda"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Podudarajući deo dobavljača"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Stavka je već primljena"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "Nijedan plugin se ne poklapa sa dobavljačevim bar kodom"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Više pronađenih poklapajućih stavki porudžbine"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Nema podudarajućih stavki porudbžine pronađenih"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Nema dostavljenih naloga za prodaju"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Bar kod se ne poklapa sa postojećom stavkom sa zaliha"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Stavka sa zaliha se ne podudara sa stavkom porudbžine"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Nedovoljno dostupnih stavki"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Stavka alocirana nalogu za prodaju"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Nema dovoljno informacija"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "Pronađena stavka"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "Deo dobavljača se ne poklapa sa stavkom porudžbine"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "Stavka porudžbine je već završena"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Dalje informacije neophodne za primanje ove stavke"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Primljene stavke sa naloga za kupovinu"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "Greška pri prijemu stavke porudžbine"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Skenirani podaci bar koda"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Ime modela za koji će se generisati bar kod"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "Primarni ključ modela objeka za koji će se generisati bar kod"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Nalog za kupovinu na osnovu kog će se alocirati stavke"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "Dobavljač od kog će se primiti stavke"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Nalog za kupovinu na osnovu kog će se primati stavke"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Nalog za kupovinu nije ispostavljen"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Lokacija na kojoj će se primati stavke"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Nije moguće izabrati strukturnu lokaciju"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr "Nalog za kupovinu na osnovu kog će se primati stavke porudžbine"

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr "Nalog za kupovinu na osnovu kog će se primati stavke porudžbine"

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Nalog za prodaju na osnovu kog će se alocirati stavke"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Stavka naloga za prodaju na osnovu koje će se alocirati stavka"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Isporuka"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Isporuka je već isporučena"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Količina za alociranje"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Greška pri štampanju natpisa"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "Greška pri renderovanju natpisa u PDF"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "Greška pri renderovanju natpisa u HTML"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Nijedna stavka nije poslata na štampu"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Naziv plugina"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Tip opcije"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Natpis opcije"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Naslov opcije"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Opis opcije"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Ikonica opcija"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Podešavanja opcija"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Kontekst opcije"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Izvor opcije (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "Bar kodovi"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Pruža ugrađenu podršku za bar kodove"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "Ljudi koji doprinose"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "Interni bar kod format"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "Izaberi interni format bar koda"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON bar kodovi (ljudski čitljivi)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "Kratki bar kodovi (optimizacija prostora)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "Kratki prefiks bar koda"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "Prilagođava prefiks koji se koristi za kratke bar kodove, može biti korisno u okruženjima sa više instanci aplikacije"

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Slack dolazeći webhook url"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "URL koji služi za slanje poruka na Slack kanal"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Otvori link"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "Razmena valuta"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Podrazumevana integracija razmene valuta"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "PDF štampanje natpisa"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Pruža podršku za štampanje PDF natpisa"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Debug mod"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Omogućava debug mod - vraća sirov HTML umesto PDF-a"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "Mašinsko štampanje natpisa"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "Pruža podršku za štampanje pomoću mašine"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "poslednji put korišćeno"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "Opcije"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Veličina strane za stranu sa natpisima"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Preskoči natpise"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Preskoči ovaj broj natpisa pri štampanju strane sa natpisima"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Border"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "Štampa border oko svakog natpisa"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Lendskejp"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "Štampa stranu sa natpisima u landscape modu"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "Štampač natpisa"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "Stavlja više natpisa na jednu stranu"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "Natpis je prevelik za veličinu stranice"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "Nijedan natpis nije generisan"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "Integracija dobavljača - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Pruža podršku za skeniranje DigiKey bar kodova"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "Dobavljač koji se ponaša kao 'DigiKey'"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "Integracija dobavljača - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "Pruža podršku za skeniranje LCSC bar kodova"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "Dobavljač koji se ponađa kao 'LCSC'"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "Integracija dobavljača - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "Pruža podršku za skeniranje Mouser bar kodova"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "Dobavljač koji se ponaša kao 'Mouser'"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "Integracija dobavljača - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "Pruža podršku za skeniranje TME bar kodova"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "Dobavljač koji se ponaša kao 'TME'"

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "Samo osoblje može da administrira pluginove"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "Instalacija plugina je onemogućena"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "Plugin instaliran uspešno"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Instaliran plugin na {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "Plugin nije pronađen u registru"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "Plugin nije paket plugin"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "Ime paketa plugina nije pronađeno"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "Deinstaliranje plugina je onemogućeno"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "Plugin ne može biti deinstaliran jer je trenutno aktivan"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr "Plugin nije instaliran"

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr "Instalacija plugina nije pronađena"

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "Plugin uspešno deinstaliran"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Konfiguracija plugina"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Konfiguracije plugina"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Ključ plugina"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Ime plugina"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Ime paketa"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "Ime instaliranog paketa, ukoliko je plugin instaliran preko PIP"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Da li je plugin aktivan"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Pokazni plugin"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Ugrađen plugin"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "Paket plugin"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "Plugin"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Nije pronađen autor"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Plugin '{p}' nije kompatibilan sa trenutnom verzijom aplikacije {v}"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Plugin zahteva najmanje verziju {v}"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Plugin zahteva najviše verziju {v}"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "Omogući PO"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "Omogući PO funkcionalnost u interfejsu"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "API ključ"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Ključ neophodan za pristup eksternom API-ju"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Numeričko"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "Numeričko podešavanje"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Izaberi podešavanje"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "Podešavanje sa više izbora"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Plugin za razmenu pokazne valute"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "Ljudi koji doprinose projektu"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Omogući panele delova"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Omogući prilagođene panele za pregled delova"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Omogući panele za naloge za kupovinu"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Omogući prilagođene panele za pregled naloga za kupovinu"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Omogući pokvarene panele"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Omogući pokvarene panele za testiranje"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Omogući dinamičke panele"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Omogući dinamičke panele za testiranje"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Panel dela"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Pokvarena stavka menija"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Ovo je pokvarena stavka menija - neće se prikazivati!"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Pokazna stavka menija"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Ovo je pokazna stavka na meniju. Prikazuje jednostavan HTML."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Stavka kontekst menija"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Stavka administratorskog menija"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Ovo je meni samo za administratore"

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Izvorni fajl"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Putanja za fajl za administratorsku integraciju"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Opcioni podaci za administratorsku integraciju"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Izvorni URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Izvor za paket"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Ima za paket pluginova - može takođe da sadrži i indikator verzije"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Verzija"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Specifikator verzije za plugin. Ostaviti prazno za najnoviju verziju."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Potvrdi instalaciju plugina"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Ovo će instalirati ovaj plugin na trenutnu instancu. Instanca će preći u mod održavanja."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Instalacija nije potvrđena"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Ili ime paketa ili URL moraju biti dostavljeni"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Potpuno ponovno učitavanje"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Izvrši potpuno ponovno učitavanje registra pluginova"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Nasilno ponovo učitaj"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Nasilno ponovo učitaj registar pluginova, iako je već učitan"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Skupi plugine"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Skupi plugine i dodaj ih u registar"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "Aktiviraj plugin"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "Aktiviraj ovaj plugin"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "Obriši konfiguraciju"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "Obriši konfiguraciju plugina iz baze podataka"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "Stavke"

#: report/api.py:114
msgid "Plugin not found"
msgstr "Plugin nije pronađen"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr "Plugin ne podržava štampanje natpisa"

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr "Nevažeće dimenzije natpisa"

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr "Nema važećih stavki dostavljenih šablonu"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Legal"

#: report/helpers.py:46
msgid "Letter"
msgstr "Letter"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr "Šablonski fajl sa ovim imenom već postoji"

#: report/models.py:217
msgid "Template name"
msgstr "Ime šablona"

#: report/models.py:223
msgid "Template description"
msgstr "Opis šablona"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr "Broj revizija (auto-inkrement)"

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr "Priloži kao model uz štampanje"

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Sačuvaj izveštaj kao prilog uz uvezanu instancu modela prilikom štampanja"

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Šablon imena fajlova"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr "Šablon za generisanje imena fajlova"

#: report/models.py:287
msgid "Template is enabled"
msgstr "Šablon je omogućen"

#: report/models.py:294
msgid "Target model type for template"
msgstr "Tip ciljanog modela za šablon"

#: report/models.py:314
msgid "Filters"
msgstr "Filteri"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "Filteri upita nad šablonima (lista razdvojena zarezom ključ=vrednost parova)"

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "Šablonski fajl"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "Veličina strane za PDF izveštaje"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "Renderuj izveštaj u landscape orijentaciji"

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Širina (u milimetrima)"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Širina natpisa, u milimetrima"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Visina (u milimetrima)"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Visina natpisa, u milimetrima"

#: report/models.py:780
msgid "Error printing labels"
msgstr ""

#: report/models.py:799
msgid "Snippet"
msgstr "Isečak"

#: report/models.py:800
msgid "Report snippet file"
msgstr "Izveštaj isečka fajla"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Opis isečka fajla"

#: report/models.py:825
msgid "Asset"
msgstr "Sredstvo"

#: report/models.py:826
msgid "Report asset file"
msgstr "Izveštaj fajla sredstva"

#: report/models.py:833
msgid "Asset file description"
msgstr "Opis fajla sredstva"

#: report/serializers.py:96
msgid "Select report template"
msgstr "Izaberi šablon izveštaja"

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr "Spisak primarnih ključeva stavke koje će biti uključene u ovaj izveštaj"

#: report/serializers.py:137
msgid "Select label template"
msgstr "Izaberi šablon natpisa"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr "Plugin za štampanje"

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr "Izaberi plugin za korišćenje prilikom štampanja natpisa"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR kod"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR kod"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Spisak materijala"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Materijali neophodni"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Slika dela"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Izdato"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Potrebno za"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Izdato od"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Dobavljač je obrisan"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Cena po jedinici"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Dodatne stavke porudžbine"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Ukupno"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Serijski broj"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Alokacije"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Serija"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Stavke na lokaciji zaliha"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Izveštaj sa testa za stavku sa zaliha"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Instalirane stavke"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Serijski"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Rezultati testa"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Test"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Uspešno"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Neuspešno"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Nema rezultata (neophodno)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Nema rezultata"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Fajl ne postoji"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Slika nije pronađena"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image tag zahteva instancu dela"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "company_image tag zahteva instancu kompanije"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "Filtriraj po dubini lokacije"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr "Filtriraj po nadređenim lokacijama"

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "Uključi podlokacije u filtriranim rezultatima"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "Nadređena lokacija"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "Filtriraj po nadređenoj lokaciji"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr "Ime dela"

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr "Ime dela sadrži"

#: stock/api.py:594
msgid "Part name (regex)"
msgstr "Ime dela (regex)"

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr "Interni deo broja"

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr "Interni deo broja sadrži (osetljivo na velika i mala slova)"

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr "Interni broj dela (regex)"

#: stock/api.py:623
msgid "Minimum stock"
msgstr "Minimalne zalihe"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "Maksimalne zalihe"

#: stock/api.py:630
msgid "Status Code"
msgstr "Statusni kod"

#: stock/api.py:670
msgid "External Location"
msgstr "Eksterna lokacija"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr "Potrošeno od strane naloga za izradu"

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr "Instalirano u drugu stavku sa zaliha"

#: stock/api.py:868
msgid "Part Tree"
msgstr "Stablo dela"

#: stock/api.py:890
msgid "Updated before"
msgstr "Ažurirano pre"

#: stock/api.py:894
msgid "Updated after"
msgstr "Ažurirano nakon"

#: stock/api.py:898
msgid "Stocktake Before"
msgstr "Popis pre"

#: stock/api.py:902
msgid "Stocktake After"
msgstr "Popis nakon"

#: stock/api.py:907
msgid "Expiry date before"
msgstr "Datum isteka pre"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "Datum isteka nakon"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "Zastarelo"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "Količina je neophodna"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "Validan deo mora biti dosavljen"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "Dati dobavljač ne postoji"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Deo dobavljača ima definisanu veličinu pakovanja, ali fleg use_pack_size nije postavljen"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Serijski brojevi ne mogu biti dostavljeni za deo koji nije moguće pratiti"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "Tip lokacije zaliha"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "Tipovi lokacija zaliha"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Podrazumevana ikonica za sve lokacije koje nemaju podešenu ikonicu (opciono)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Lokacija zaliha"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Lokacija zaliha"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Vlasnik"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Izaberi vlasnika"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Stavke sa zaliha ne mogu biti direktno locirane u strukturnim lokacijama zaliha, ali mogu biti locirane u podređenim lokacijama."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Eksterna"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Ovo je eksterna lokacija zaliha"

#: stock/models.py:233
msgid "Location type"
msgstr "Tip lokacije"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "Tip lokacija zaliha za ovu lokaciju"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Ne možete postaviti ovu lokaciju zaliha kao strukturnu jer su već neke stavke locirane na njoj!"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr "Deo mora biti određen"

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Stavka sa zaliha ne može biti locirana u strukturnim lokacijama zaliha!"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Stavka sa zaliha ne može biti kreirana za virtuelne delove"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Deo tipa ('{self.supplier_part.part}') mora biti {self.part}"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "Količina mora biti 1 za stavku sa serijskim brojem"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Serijski broj ne može biti postavljen ukoliko je količina veća od 1"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "Stavka ne može da pripada samoj sebi"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "Stavka mora da ima referencu izgradnje ukoliko is_building=True"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "Referenca izgradnje ne ukazuje na isti objekat dela"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Nadređena stavka sa zaliha"

#: stock/models.py:1028
msgid "Base part"
msgstr "Osnovni deo"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Izaberi odgovarajući deo dobavljača za ovu stavku sa zaliha"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Gde je locirana ova stavka sa zaliha?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "Pakovanje u kom je ova stavka sa zaliha"

#: stock/models.py:1064
msgid "Installed In"
msgstr "Instalirano u"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "Da li je ova stavka instalirana u drugu stavku?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Serijski broj za ovu stavku"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Šifra ture za ovu stavku sa zaliha"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Količina zaliha"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Izvorna gradnja"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Nalog za ovu stavku sa zaliha"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "Potrošeno od strane"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "Nalog za izradu koji je potrošio ovu stavku sa zaliha"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Izvorni nalog za kupovinu"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Nalog za kupovinu za ovu stavku sa zaliha"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Odredište naloga za prodaju"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Datum isteka za stavku sa zaliha. Zalihe će se smatrati isteklim nakon ovog datuma"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Obriši kad je potrošeno"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Obriši ovu stavku sa zaliha kada su zalihe potrošene"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Cena kupovine jedne jedinice u vreme kupovine"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "Konvertovano u deo"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "Deo nije postavljen kao deo koji je moguće pratiti"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "Količina mora biti ceo broj"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Količina ne sme da pređe dostupnu količinu zaliha ({self.quantity})"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr "Serijski brojevi moraju biti dostavljeni kao lista"

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "Količine se ne poklapaju sa serijskim brojevima"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "Test šablon ne postoji"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "Stavka sa zaliha je dodeljena nalogu za prodaju"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "Stavka sa zaliha je instalirana u drugu stavku"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "Stavka sa zaliha sadrži druge stavke"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "Stavka sa zaliha je dodeljena mušteriji"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "Stavka sa zaliha je trenutno u produkciji"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Serijalizovane zalihe se ne mogu spojiti"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Dupliraj stavke sa zaliha"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "Stavke sa zaliha se moraju odnositi na isti deo"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "Stavke sa zaliha se moraju odnositi na isti deo dobavljača"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "Statusne šifre zaliha moraju da se poklapaju"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Stavka se ne može pomeriti jer nije na zalihama"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr "Praćenje stavke sa zaliha"

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Ulazne beleške"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr "Rezultat testa stavke sa zaliha"

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Vrednost mora biti dostavljena za ovaj test"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "Prilog mora biti dostavljen za ovaj test"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr "Nevažeća vrednost za ovaj test"

#: stock/models.py:2951
msgid "Test result"
msgstr "Rezultat testa"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Vrednost završetka testa"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Prilog uz test rezultat"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Beleške sa testa"

#: stock/models.py:2978
msgid "Test station"
msgstr "Stanica za testiranje"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "Identifikator stanice za testiranje gde je test izvršen"

#: stock/models.py:2985
msgid "Started"
msgstr "Započeto"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "Vreme početka testa"

#: stock/models.py:2992
msgid "Finished"
msgstr "Završeno"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "Vreme završetka testa"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "Generisana šifra ture"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "Izaberi nalog za izradu"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "Izaberi stavku sa zaliha za koju će se generisati šifra ture"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "Izaberi lokaciju za koju će se generisati šifra ture"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "Izaberi deo za koji će se generisati šifra ture"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "Izaberi nalog za kupovinu"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "Unesi količinu za šifru ture"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "Generisan serijski broj"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "Izaberi deo za koji će se generisati serijski broj"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "Količina serijskih brojeva koji će se generisati"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "Test šablon za ovaj rezultat"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "ID šablona ili ime testa mora biti dostavljeno"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "Vreme završetka testa ne može biti pre vremena početka testa"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Nadređena stavka"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr "Nadređena stavka sa zaliha"

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Koristi pakovanja prilikom dodavanja: količina je definisana brojem pakovanja"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Unesi serijske brojeve za nove stavke"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "Dobavljački broj dela"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "Isteklo"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Podređene stavke"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr "Stavke za praćenje"

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Nabavna cena ove stavke, po jedinici ili pakovanju"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Unesi broj stavka sa zaliha za serijalizaciju"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Količina ne sme da pređe dostupnu količinu zaliha ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Odredišna lokacija zaliha"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Serijski brojevi ne mogu biti dodeljeni ovom delu"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "Serijski broj već postoji"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Izaberi stavku za instaliranje"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "Količina za instaliranje"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "Izaberi količinu stavki za instaliranje"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "Dodaj beleške transakcija (opciono)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "Količina za instaliranje mora biti najmanje 1"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "Stavka je nedostupna"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "Izabrani deo nije na spisku materijala"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "Količina za instaliranje ne sme preći dostupnu količinu"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Odredišna lokacija za deinstalirane stavke"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Izaberi deo u koji će se konvertovati stavka"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "Izabrani deo nije validna opcija za konverziju"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Ne može se konvertovati stavka sa dodeljenim delom dobavljača"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "Statusni kod stavke sa zaliha"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "Izaberi stavke kojoj će se promeniti status"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "Nije izabrana stavka"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Podlokacije"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "Lokacija nadređenih zaliha"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "Deo mora biti za prodaju"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "Stavka je alocirana nalogu za prodaju"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "Stavka je alocirana nalogu za izradu"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Mušterija kojoj će se dodeliti stavke sa zaliha"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "Izabrana kompanija nije mušterija"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Beleške dodeljivanja zaliha"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "Lista stavki mora biti dostavljena"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Beleške spajanja zaliha"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Dozvoli neslagajuće dobavljače"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Dozvoli spajanje stavki sa različitim delovima dobavljača"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Dozvoli neslagajući status"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Dozvoli spajanje stavki sa različitim statusnim kodovima"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Bar dve stavke moraju biti dostavljene"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "Nema promena"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Vrednost primarnog ključa stavke"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr "Stavka nije na zalihama"

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Beleške transakcija zaliha"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "Uredu"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Potrebna pažnja"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Oštećeno"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Uništeno"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Odbijeno"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "U karantinu"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Nasleđeni unos za praćenje zaliha"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Stavka na zalihi stvorena"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Izmenjena stavka u zalihama"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Dodeljen serijski broj"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Zalihe prebrojane"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Zalihe dodane ručno"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Zaliha ručno uklonjena"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Lokacija promenjena"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Zaliha obnovljena"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "Instalisan u sklopu"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Skinuto sa sklopa"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Instalirana stavka komponente"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Uklonjena stavka komponente"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Odvoj od nadređene stavke"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Podeli podređenu stavku"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Spojene stavke zaliha"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "Pretvoreno u varijaciju"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "Započeta obrada naloga za izradu"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Završena obrada naloga za izradu"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "Odbijen rezultat naloga za izradu"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Potrošeno od strane nalga za izradu"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Isporučeno prema nalogu za prodaju"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Primljeno prema nalogu za kupovinu"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Vraćeno prema nalogu za vraćanje"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Poslato mušteriji"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Vratila mušterija"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Nemate dozvolu"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Nemate dozvolu da vidite ovu stranicu"

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Greška prilikom autentifikacije"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Odjavljeni ste sa aplikacije"

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Strana nije pronađena"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "Tražena strana ne postoji"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Interna serverska greška"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s server ima internu grešku"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Pogledaj logove greške u admin interfejsu za više detalja"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Sajt je u fazi održavanja"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Sajt je trenutno u fazi održavanja i biće aktivan uskoro!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Potrebno restartovanje servera"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Promenjena je konfiguraciona opcija koja zahteva restartovanje servera"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Kontaktirajte administratora za više informacija"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Čekaju se migracije nad bazom podataka"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Postoje migracije nad bazom podataka koje zahtevaju pažnju"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Klikni na sledeći link da vidiš ovaj nalog"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Zalihe su potrebne za sledeći nalog za izradu"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Nalog za izradu %(build)s - %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klikni na sledeći link da vidiš ovaj nalog za izradu"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Sledeći delovi ima nedovoljno na potrebnim zalihama"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Potrebna količina"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Primili ste ovaj email jer ste pretplaćeni na notifikacije za ovaj deo"

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klikni na sledeći link da bi video ovaj deo"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimalna količina"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Korisnici"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Odaberi koje korisnike želiš da dodaš u ovu grupu"

#: users/admin.py:137
msgid "Personal info"
msgstr "Lični podaci"

#: users/admin.py:139
msgid "Permissions"
msgstr "Dozvole"

#: users/admin.py:142
msgid "Important dates"
msgstr "Značajni datumi"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Token je opozvan"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token je istekao"

#: users/models.py:100
msgid "API Token"
msgstr "API token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API tokeni"

#: users/models.py:137
msgid "Token Name"
msgstr "Naziv tokena"

#: users/models.py:138
msgid "Custom token name"
msgstr "Prilagođen naziv tokena"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Datum isteka tokena"

#: users/models.py:152
msgid "Last Seen"
msgstr "Poslednji put aktivan"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Poslednji put kada je token korišćen"

#: users/models.py:157
msgid "Revoked"
msgstr "Opozvano"

#: users/models.py:235
msgid "Permission set"
msgstr "Skup dozvola"

#: users/models.py:244
msgid "Group"
msgstr "Grupa"

#: users/models.py:248
msgid "View"
msgstr "Pregledaj"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Dozvola za pregled stavki"

#: users/models.py:252
msgid "Add"
msgstr "Dodaj"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Dozvola za dodavanje stavki"

#: users/models.py:256
msgid "Change"
msgstr "Promeni"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Dozvole za izmenu stavki"

#: users/models.py:262
msgid "Delete"
msgstr "Obriši"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Dozvola za brisanje stavki"

#: users/models.py:501
msgid "Bot"
msgstr ""

#: users/models.py:502
msgid "Internal"
msgstr ""

#: users/models.py:504
msgid "Guest"
msgstr ""

#: users/models.py:513
msgid "Language"
msgstr ""

#: users/models.py:514
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:519
msgid "Theme"
msgstr ""

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr ""

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr ""

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:541
msgid "Position"
msgstr ""

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr ""

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr ""

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr ""

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr ""

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr "Admin"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Nalozi za kupovinu"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Nalozi za prodaju"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Nalozi za vraćanje"

#: users/serializers.py:196
msgid "Username"
msgstr "Korisničko ime"

#: users/serializers.py:199
msgid "First Name"
msgstr "Ime"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Ime korisnika"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Prezime"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Prezime korisnika"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "Adresa E-pošte korisnika"

#: users/serializers.py:326
msgid "Staff"
msgstr "Osoblje"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Da li ovaj korisnik ima dozvole koje ima osoblje?"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Super korisnik"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "Da li je ovaj korisnik Super korisnik?"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Da li je nalog ovog korisnika aktivan?"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Vaš nalog je kreiran"

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Molimo vas koristite opciju resetovanja lozinke da biste se prijavili"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Dobrodošli u InvenTree"

