---
title: Release 0.4.5
---

## Release 0.4.5

### Lazy Loading

[#1941](https://github.com/inventree/InvenTree/pull/1941) defers sending API requests for tables which are not yet displayed. This greatly improves initial page render speed, as API queries are made on a "just in time" basis.

### Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#1931](https://github.com/inventree/InvenTree/pull/1931) | Fixes bug in nginx.conf configuration file |
| [#1932](https://github.com/inventree/InvenTree/pull/1932) | Fixes some form rendering issues for the SupplierPart and ManufactuerPart models |
| [#1934](https://github.com/inventree/InvenTree/pull/1934) | Addresses datetime issue in background worker heartbeat task |
| [#1938](https://github.com/inventree/InvenTree/pull/1938) | Fixes nefarious bug which prevent token auth from working for media files when behind a nginx proxy |
| [#1939](https://github.com/inventree/InvenTree/pull/1939) | Fixes bug in stock transfer form |
| [#1940](https://github.com/inventree/InvenTree/pull/1940) | Fixes multi-item actions in stock tables |
