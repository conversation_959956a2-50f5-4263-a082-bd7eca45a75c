# Generated by Django 3.2.5 on 2021-10-14 06:32

import re

from django.db import migrations


def build_refs(apps, schema_editor):
    """
    Rebuild the integer "reference fields" for existing Build objects
    """

    BuildOrder = apps.get_model('build', 'build')

    for build in BuildOrder.objects.all():

        ref = 0

        result = re.match(r"^(\d+)", build.reference)

        if result and len(result.groups()) == 1:
            try:
                ref = int(result.groups()[0])
            except Exception:  # pragma: no cover
                ref = 0

        # Clip integer value to ensure it does not overflow database field
        if ref > 0x7fffffff:
            ref = 0x7fffffff

        build.reference_int = ref
        build.save()


class Migration(migrations.Migration):

    atomic = False

    dependencies = [
        ('build', '0031_build_reference_int'),
    ]

    operations = [
        migrations.RunPython(
            build_refs,
            reverse_code=migrations.RunPython.noop
        )
    ]
