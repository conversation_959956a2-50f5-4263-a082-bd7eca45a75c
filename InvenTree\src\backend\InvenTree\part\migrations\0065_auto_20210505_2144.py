# Generated by Django 3.2 on 2021-05-05 21:44

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0064_auto_20210404_2016'),
    ]

    operations = [
        migrations.AddField(
            model_name='part',
            name='base_cost',
            field=models.DecimalField(decimal_places=3, default=0, help_text='Minimum charge (e.g. stocking fee)', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='base cost'),
        ),
        migrations.AddField(
            model_name='part',
            name='multiple',
            field=models.PositiveIntegerField(default=1, help_text='Sell multiple', validators=[django.core.validators.MinValueValidator(1)], verbose_name='multiple'),
        ),
    ]
