# Runs on releases

name: Publish release
on:
  release:
    types: [published]
permissions:
  contents: read
env:
  python_version: 3.9

jobs:
  stable:
    runs-on: ubuntu-24.04
    name: Write release to stable branch
    permissions:
      contents: write
      pull-requests: write
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # pin@v5.0.0
        with:
          persist-credentials: false
      - name: Version Check
        run: |
          pip install --require-hashes -r contrib/dev_reqs/requirements.txt
          python3 .github/scripts/version_check.py
      - name: Push to Stable Branch
        uses: ad-m/github-push-action@d91a481090679876dfc4178fef17f286781251df # pin@v0.8.0
        if: env.stable_release == 'true'
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: stable
          force: true

  build:
    runs-on: ubuntu-24.04
    name: Build and attest frontend
    permissions:
      id-token: write
      contents: write
      attestations: write
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # pin@v5.0.0
        with:
          persist-credentials: false
      - name: Environment Setup
        uses: ./.github/actions/setup
        with:
          npm: true
      - name: Install dependencies
        run: cd src/frontend && yarn install
      - name: Build frontend
        run: cd src/frontend && npm run compile && npm run build
      - name: Create SBOM for frontend
        uses: anchore/sbom-action@da167eac915b4e86f08b264dbdbc867b61be6f0c # pin@v0
        with:
          artifact-name: frontend-build.spdx
          path: src/frontend
      - name: Write version file - SHA
        run: cd src/backend/InvenTree/web/static/web/.vite && echo "$GITHUB_SHA" > sha.txt
      - name: Write version file - TAG
        run: cd src/backend/InvenTree/web/static/web/.vite && echo "${REF_NAME}" > tag.txt
        env:
          REF_NAME: ${{ github.ref_name }}
      - name: Zip frontend
        run: |
          cd src/backend/InvenTree/web/static/web
          zip -r ../frontend-build.zip * .vite
      - name: Attest Build Provenance
        id: attest
        uses: actions/attest-build-provenance@e8998f949152b193b063cb0ec769d69d929409be # pin@v1
        with:
          subject-path: "${{ github.workspace }}/src/backend/InvenTree/web/static/frontend-build.zip"

      - name: Upload frontend
        uses: svenstaro/upload-release-action@81c65b7cd4de9b2570615ce3aad67a41de5b1a13 # pin@2.11.2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: src/backend/InvenTree/web/static/frontend-build.zip
          asset_name: frontend-build.zip
          tag: ${{ github.ref }}
          overwrite: true
      - name: Upload Attestation
        uses: svenstaro/upload-release-action@81c65b7cd4de9b2570615ce3aad67a41de5b1a13 # pin@2.11.2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          asset_name: frontend-build.intoto.jsonl
          file: ${{ steps.attest.outputs.bundle-path}}
          tag: ${{ github.ref }}
          overwrite: true

  docs:
    runs-on: ubuntu-24.04
    name: Build and publish documentation
    permissions:
      contents: write
    env:
      INVENTREE_DB_ENGINE: sqlite3
      INVENTREE_DB_NAME: inventree
      INVENTREE_MEDIA_ROOT: /home/<USER>/work/InvenTree/test_inventree_media
      INVENTREE_STATIC_ROOT: /home/<USER>/work/InvenTree/test_inventree_static
      INVENTREE_BACKUP_DIR: /home/<USER>/work/InvenTree/test_inventree_backup
      INVENTREE_SITE_URL: http://localhost:8000
      INVENTREE_DEBUG: true

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # pin@v5.0.0
        with:
          persist-credentials: false
      - name: Environment Setup
        uses: ./.github/actions/setup
        with:
          install: true
          npm: true
      - name: Install dependencies
        run: |
          pip install --require-hashes -r contrib/dev_reqs/requirements.txt
          pip install --require-hashes -r docs/requirements.txt
      - name: Build documentation
        run: |
          invoke build-docs --mkdocs
      - name: Zip build docs
        run: |
          cd docs/site
          zip -r docs-html.zip *
      - name: Publish documentation
        uses: svenstaro/upload-release-action@81c65b7cd4de9b2570615ce3aad67a41de5b1a13 # pin@2.11.2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: docs/site/docs-html.zip
          asset_name: docs-html.zip
          tag: ${{ github.ref }}
          overwrite: true
