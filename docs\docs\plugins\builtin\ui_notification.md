---
title: UI Notification Plugin
---

## UI Notification Plugin

This plugin provides a mechanism to send notifications to users via the InvenTree User Interface (UI). It implements the [NotificationMixin](../mixins/notification.md) mixin class, allowing it to send notifications based on events defined in the InvenTree system.

## UI Display

Any notifications which are generated by the InvenTree core system will be sent to users via this plugin. The notifications will be displayed in the UI:

### Notification Indicator

A notification indicator will appear in the top right corner of the InvenTree UI, indicating the number of unread notifications.
