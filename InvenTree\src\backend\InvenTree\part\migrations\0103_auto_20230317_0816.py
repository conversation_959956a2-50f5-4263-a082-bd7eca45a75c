# Generated by Django 3.2.18 on 2023-03-17 08:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0102_auto_20230314_0112'),
    ]

    operations = [
        migrations.AddField(
            model_name='bomitem',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.AddField(
            model_name='partparametertemplate',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
    ]
