
As a MIT licensed open-source project, there are not legal obligations for the InvenTree project team or any contributor to provide support or maintenance. The software is provided "as-is" - without warranty of any kind.
However, the project team is committed to providing a reliable and [secure](../security.md) code base with predictable upgrade paths.

## Team organisation

There is no legal entity that governs the InvenTree project at this time.
The project is managed by a small team of volunteer developers, who are responsible for the ongoing development and maintenance of the software.

### Maintainer

InvenTree was started by and is run by SchrodingersGat, who is the maintainer of the project following the [BDFL model](https://en.wikipedia.org/wiki/Benevolent_dictator_for_life).
Releases are made by the maintainer, who is responsible for the overall direction of the project. Any directional decisions are at the sole discretion of the maintainer.

### Core Team

A number of developers are selected to join the `core team` to help with triage and review of issues and pull requests. Core team members are part of the @inventree/triage team on GitHub and might choose to make that association public. Team members might be listed on the website.

The core team has moderation, triage and review permissions on most repositories of the organisation. There is no requirement (or assumption) of effort spend on the project.

### Proofreaders

The InvenTree project is translated into a number of languages using a web translation tool that requires approval of submitted strings. Members of the community can apply to be elevated to the role of `proofreader` for a specific language.
Proofreaders are enabled to approve translations, at which point they are automatically proposed for inclusion in the code base.

A list of proofreaders is being maintained on [GitHub](https://github.com/inventree/InvenTree/discussions/9073)

## Write access to the code base

Write access to the code base is granted to a very small number of developers. In daily operations only the [maintainer](#maintainer) is expected to make changes to the master or stable branches.

To reduce the risk of losing the possibility to address security issues in a timely manner additional members are granted access.

The following users have write access to the code base at this time:

- [SchrodingersGat](https://github.com/SchrodingersGat)
- [matmair](https://github.com/matmair)
