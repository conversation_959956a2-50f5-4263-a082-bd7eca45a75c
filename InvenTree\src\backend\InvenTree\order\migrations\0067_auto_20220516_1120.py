# Generated by Django 3.2.13 on 2022-05-16 11:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0066_alter_purchaseorder_supplier'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='metadata',
            field=models.JSO<PERSON>ield(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
    ]
