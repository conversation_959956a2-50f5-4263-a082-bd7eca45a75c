---
title: App Navigation
---


## Home Screen

The app *home screen* provides quick-access buttons for stock view and actions:

{{ image("app/home.png", "Home screen") }}

## Tab Display

Some screens provide multiple tabbed views, which are displayed at the top of the screen:

{{ image("app/app_tabs.png", "App tabs") }}

Tabs can be navigated by pressing on the text of each tab, or by scrolling the screen left or right.

## Global Actions

The *Global Action* buttons are visible on most screens, displayed in the bottom left corner of the screen:

{{ image("app/app_global_navigation.png", "Global navigation actions") }}

### Open Drawer Menu

The {{ icon("list") }} action opens the *Drawer Menu*, which is a quick-access menu for global navigation:

{{ image("app/drawer.png", "Open drawer menu") }}

The *Drawer Menu* can be accessed in the following ways:

- From the *Home Screen* select the *Drawer* icon in the top-left corner of the screen
- From any other screen, long-press the *Back* button in the top-left corner of the screen

### Search

The {{ icon("search", title="Search") }} action opens the [Search](./search.md) screen

### Scan Barcode

The {{ icon("barcode", title="Scan") }} action opens the [barcode scan](./barcode.md#global-scan) window, which allows quick access to the barcode scanning functionality.

## Context Actions

Within a given view, certain context actions may be available. If there are contextual actions which can be performed, they are displayed in the bottom right corner:

{{ image("app/context_actions.png", "Context actions") }}

!!! tip "Barcode Actions"
    Available barcode actions are displayed in a separate context action menu
