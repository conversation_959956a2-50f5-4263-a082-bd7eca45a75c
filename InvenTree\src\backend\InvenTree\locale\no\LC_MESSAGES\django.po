msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Norwegian\n"
"Language: no_NO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: no\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr ""

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "API-endepunkt ikke funnet"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr ""

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr ""

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "Brukeren har ikke rettigheter til å se denne modellen"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "E-post (gjenta)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "Bekreft e-postaddresse"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Du må angi samme e-post hver gang."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Den oppgitte primære e-postadressen er ikke gyldig."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Det oppgitte e-postdomenet er ikke godkjent."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Ugyldig enhet angitt ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Ingen verdi angitt"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Kunne ikke konvertere {original} til {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Ugyldig mengde oppgitt"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Feildetaljer kan finnes i admin-panelet"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Oppgi dato"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr ""

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Notater"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Verdi '{name}' vises ikke i mønsterformat"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Angitt verdi samsvarer ikke med påkrevd mønster: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr ""

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Tom serienummerstreng"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Duplisert serienummer"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr ""

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Gruppesekvens {group} overskrider tillatt antall ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Ingen serienummer funnet"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Fjern HTML-tagger fra denne verdien"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Tilkoblingsfeil"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Serveren svarte med ugyldig statuskode"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Det har oppstått et unntak"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Serveren svarte med ugyldig \"Content-Length\"-verdi"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Bildestørrelsen er for stor"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Bildenedlasting overskred maksimal størrelse"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Ekstern server returnerte tomt svar"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Angitt URL er ikke en gyldig bildefil"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabisk"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgarsk"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tsjekkisk"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Dansk"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Tysk"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Gresk"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Engelsk"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spansk"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spansk (Meksikansk)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estisk"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persisk"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finsk"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Fransk"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebraisk"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Ungarsk"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiensk"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japansk"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Koreansk"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr ""

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Latvisk"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Nederlandsk"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norsk"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polsk"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugisisk"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugisisk (Brasil)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumensk"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russisk"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovakisk"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovensk"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbisk"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Svensk"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thailandsk"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Tyrkisk"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrainsk"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamesisk"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Kinesisk (forenklet)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Kinesisk (tradisjonell)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr ""

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "E-post"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Feil under validering av utvidelse"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Metadata må være et python dict-objekt"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Utvidelse-metadata"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "JSON-metadatafelt, for bruk av eksterne utvidelser"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Uriktig formatert mønster"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Ukjent formatnøkkel spesifisert"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Mangler nødvendig formatnøkkel"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "Referansefeltet kan ikke være tomt"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "Referansen må samsvare påkrevd mønster"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Referansenummeret er for stort"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Ugyldig valg"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Navn"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Beskrivelse"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Beskrivelse (valgfritt)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Sti"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Duplikatnavn kan ikke eksistere under samme overordnede"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Markdown-notater (valgfritt)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Strekkodedata"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Tredjeparts strekkodedata"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Strekkode-hash"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Unik hash av strekkodedata"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Eksisterende strekkode funnet"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr ""

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Serverfeil"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "En feil har blitt logget av serveren."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Må være et gyldig tall"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Valuta"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Velg valuta ut fra tilgjengelige alternativer"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Ugyldig verdi"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Eksternt bilde"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URLtil ekstern bildefil"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Nedlasting av bilder fra ekstern URL er ikke aktivert"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr ""

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Ugyldig fysisk enhet"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Ikke en gyldig valutakode"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Ordrestatus"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Overordnet produksjon"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr ""

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Del"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Kategori"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr ""

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr ""

#: build/api.py:154
msgid "Assigned To"
msgstr ""

#: build/api.py:189
msgid "Created before"
msgstr ""

#: build/api.py:193
msgid "Created after"
msgstr ""

#: build/api.py:197
msgid "Has start date"
msgstr ""

#: build/api.py:205
msgid "Start date before"
msgstr ""

#: build/api.py:209
msgid "Start date after"
msgstr ""

#: build/api.py:213
msgid "Has target date"
msgstr ""

#: build/api.py:221
msgid "Target date before"
msgstr ""

#: build/api.py:225
msgid "Target date after"
msgstr ""

#: build/api.py:229
msgid "Completed before"
msgstr ""

#: build/api.py:233
msgid "Completed after"
msgstr ""

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr ""

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr ""

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr ""

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "Produksjonen må avbrytes før den kan slettes"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Forbruksvare"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Valgfritt"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Sammenstilling"

#: build/api.py:450
msgid "Tracked"
msgstr "Spores"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr ""

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr ""

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Tildelt"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Tilgjengelig"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Produksjonsordre"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Plassering"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Produksjonsordrer"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "Sammenstillings-BOMen er ikke godkjent"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Produksjonsordre kan ikke opprettes for en inaktiv del"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Produksjonsordre kan ikke opprettes for en ulåst del"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Ansvarlig bruker eller gruppe må spesifiseres"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "Produksjonsordrens del kan ikke endres"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Produksjonsordre-referanse"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referanse"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Kort beskrivelse av produksjonen (valgfritt)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Produksjonsordre som denne produksjonen er tildelt"

#: build/models.py:273
msgid "Select part to build"
msgstr "Velg del å produsere"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Salgsordrereferanse"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Salgsordren denne produksjonen er tildelt til"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Kildeplassering"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Velg plassering å ta lagerbeholdning fra for denne produksjonen (la stå tomt for a ta fra alle lagerplasseringer)"

#: build/models.py:300
msgid "External Build"
msgstr ""

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr "Fullført plassering"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Velg plassering der fullførte artikler vil bli lagret"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Produksjonsmengde"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Antall lagervarer å produsere"

#: build/models.py:322
msgid "Completed items"
msgstr "Fullførte artikler"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Antall lagervarer som er fullført"

#: build/models.py:328
msgid "Build Status"
msgstr "Produksjonsstatus"

#: build/models.py:333
msgid "Build status code"
msgstr "Produksjonsstatuskode"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Batchkode"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Batchkode for denne produksjonsartikkelen"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Opprettelsesdato"

#: build/models.py:356
msgid "Build start date"
msgstr ""

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:363
msgid "Target completion date"
msgstr "Forventet sluttdato"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Måldato for ferdigstillelse. Produksjonen vil være forfalt etter denne datoen."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Fullført dato"

#: build/models.py:378
msgid "completed by"
msgstr "fullført av"

#: build/models.py:387
msgid "Issued by"
msgstr "Utstedt av"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Brukeren som utstedte denne produksjonsordren"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Ansvarlig"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Bruker eller gruppe ansvarlig for produksjonsordren"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Ekstern lenke"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Lenke til ekstern URL"

#: build/models.py:410
msgid "Build Priority"
msgstr "Produksjonsprioritet"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Produksjonsordrens prioritet"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Prosjektkode"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Prosjektkode for denne produksjonsordren"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Kunne ikke delegere bort oppgaven for å fullføre tildelinger"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Produksjonsordre {build} er fullført"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "En produksjonsordre er fullført"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Serienumre må angis for sporbare deler"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Ingen produksjonsartikkel spesifisert"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "Produksjonsartikkelen er allerede fullført"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "Produksjonsartikkelen samsvarer ikke med produksjonsordren"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "Mengden må være større enn null"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "Kvantitet kan ikke være større enn utgangsantallet"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Produksjonsartikkel {serial} har ikke bestått alle påkrevde tester"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Produksjonsartikkel"

#: build/models.py:1602
msgid "Build object"
msgstr "Produksjonsobjekt"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Antall"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Påkrevd antall for produksjonsordre"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Produksjonselement må spesifisere en produksjonsartikkel, da master-del er merket som sporbar"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Tildelt antall ({q}) kan ikke overstige tilgjengelig lagerbeholdning ({a})"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "Lagervaren er overtildelt"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "Tildelingsantall må være større enn null"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "Mengden må være 1 for serialisert lagervare"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "Valgt lagervare samsvarer ikke med BOM-linjen"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Lagervare"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Kildelagervare"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Lagerantall å tildele til produksjonen"

#: build/models.py:1924
msgid "Install into"
msgstr "Monteres i"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Lagervare for montering"

#: build/serializers.py:115
msgid "Build Level"
msgstr ""

#: build/serializers.py:124
msgid "Part Name"
msgstr "Delnavn"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Etikett for prosjektkode"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Produksjonsartikkel"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "Produksjonsartikkel samsvarer ikke med overordnet produksjon"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "Resultatdel samsvarer ikke med produksjonsordredel"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Denne produksjonsartikkelen er allerede fullført"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Denne produksjonsartikkelen er ikke fullt tildelt"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Angi antall for produksjonsartikkel"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Heltallsverdi kreves for sporbare deler"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Heltallsverdi kreves, da stykklisten inneholder sporbare deler"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Serienummer"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Angi serienummer for produksjonsartikler"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Lagerplassering for produksjonsartikkel"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Automatisk tildeling av serienummer"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Automatisk tildeling av nødvendige artikler med tilsvarende serienummer"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "Følgende serienummer finnes allerede eller er ugyldige"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "En liste over produksjonsartikler må oppgis"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Lagerplassering for skrotede produksjonsartikler"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Forkast tildelinger"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Forkast tildelinger fra skrotede produksjonsartikler"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Grunn for skroting av produksjonsartikler"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Plassering for ferdige produksjonsartikler"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Godta ufullstendig tildeling"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Fullfør artikler dersom lagerbeholdning ikke er fullt tildelt"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Bruk tildelt lagerbeholdning"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Bruk all lagerbeholdning som allerede er tildelt denne produksjonen"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Fjern ufullstendige artikler"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Slett alle produksjonsartikler som ikke er fullført"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Ikke tillatt"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Godta som brukt av denne produksjonsordren"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Fjern tildeling før produksjonsordren fullføres"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Overtildelt lagerbeholdning"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Hvordan vil du håndtere ekstra lagervarer tildelt produksjonsordren"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Noen lagervarer har blitt overtildelt"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Godta ikke tildelt"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Godta at lagervarer ikke er fullt tildelt til denne produksjonsordren"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "Nøvendig lagerbeholdning er ikke fullt tildelt"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Godta uferdig"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Godta at nødvendig antall fullførte produksjonsartikler ikke er nådd"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "Nødvendig produksjonsmengde er ikke nådd"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr ""

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr ""

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "Produksjonsordren har uferdige artikler"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Produksjonslinje"

#: build/serializers.py:877
msgid "Build output"
msgstr "Produksjonsartikkel"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "Produksjonsartikkel må peke til samme produksjon"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Produksjonsartikkel"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part må peke på den samme delen som produksjonsordren"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "Artikkelen må være på lager"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Tilgjengelig antall ({q}) overskredet"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Produksjonsartikkel må spesifiseres for tildeling av sporede deler"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Produksjonsartikkel kan ikke spesifiseres for tildeling av usporede deler"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Tildelingsartikler må oppgis"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Lagerplassering hvor deler skal hentes (la stå tomt for å ta fra alle plasseringer)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Eksluderer plassering"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Ekskluder lagervarer fra denne valgte plasseringen"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Utskiftbar lagerbeholdning"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Lagervarer ved flere plasseringer kan brukes om hverandre"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Erstatning-lagerbeholdning"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Tilatt tildelling av erstatningsdeler"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Valgfrie artikler"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Tildel valgfrie BOM-artikler til produksjonsordre"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Kunne ikke starte auto-tideling"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "BOM-referanse"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr ""

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr ""

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr ""

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Leverandørdel"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Tildelt antall"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Produksjonsreferanse"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Delkategorinavn"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Sporbar"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Nedarvet"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Tillat Varianter"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "BOM-artikkel"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "I bestilling"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "I produksjon"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Ekstern lagerbeholdning"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Tilgjengelig lagerbeholdning"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Tilgjengelige erstatningsvarer"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Tilgjengelige variantvarer"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Ventende"

#: build/status_codes.py:12
msgid "Production"
msgstr "Produksjon"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr ""

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Kansellert"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Fullført"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Lagerbeholdning kreves for produksjonsordre"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Forfalt produksjonsordre"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Produksjonsordre {bo} er nå forfalt"

#: common/api.py:688
msgid "Is Link"
msgstr "Er lenke"

#: common/api.py:696
msgid "Is File"
msgstr "Er fil"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr ""

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "Brukeren har ikke tillatelse til å slette dette vedlegget"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Ugyldig valutakode"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Valutakode eksisterer allerede"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Ingen gyldige valutakoder angitt"

#: common/currency.py:146
msgid "No plugin"
msgstr "Ingen programtillegg"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Oppdatert"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Tidsstempel for forrige oppdatering"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "Unik prosjektkode"

#: common/models.py:171
msgid "Project description"
msgstr "Prosjektbeskrivelse"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Bruker eller gruppe ansvarlig for dette prosjektet"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr ""

#: common/models.py:780
msgid "Settings value"
msgstr "Innstillings verdi"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "Valgt verdi er ikke et gyldig alternativ"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Verdien må være en boolsk verdi"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Verdien må være et heltall"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:914
msgid "Key string must be unique"
msgstr "Nøkkelstreng må være unik"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Bruker"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Antall for prisbrudd"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Pris"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Enhetspris på spesifisert antall"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Endepunkt"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Endepunktet hvor denne webhooken er mottatt"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Navn for webhooken"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Aktiv"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Er webhooken aktiv"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Sjetong"

#: common/models.py:1437
msgid "Token for access"
msgstr "Nøkkel for tilgang"

#: common/models.py:1445
msgid "Secret"
msgstr "Hemmelig"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Delt hemmlighet for HMAC"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "Melding ID"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Unik Id for denne meldingen"

#: common/models.py:1563
msgid "Host"
msgstr "Vert"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Verten denne meldingen ble mottatt fra"

#: common/models.py:1572
msgid "Header"
msgstr "Tittel"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Overskrift for denne meldingen"

#: common/models.py:1580
msgid "Body"
msgstr "Brødtekst"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Innholdet i meldingen"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Endepunktet meldingen ble mottatt fra"

#: common/models.py:1596
msgid "Worked on"
msgstr "Arbeidet med"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Var arbeidet med denne meldingen ferdig?"

#: common/models.py:1723
msgid "Id"
msgstr ""

#: common/models.py:1725
msgid "Title"
msgstr "Tittel"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Lenke"

#: common/models.py:1729
msgid "Published"
msgstr "Publisert"

#: common/models.py:1731
msgid "Author"
msgstr "Forfatter"

#: common/models.py:1733
msgid "Summary"
msgstr "Sammendrag"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Les"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Er dette nyhetselementet lest?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Bilde"

#: common/models.py:1753
msgid "Image file"
msgstr "Bildefil"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr ""

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr ""

#: common/models.py:1791
msgid "Custom Unit"
msgstr ""

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "Enhetssymbolet må være unikt"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Enhetsnavn må være en gyldig identifikator"

#: common/models.py:1843
msgid "Unit name"
msgstr "Enhetsnavn"

#: common/models.py:1850
msgid "Symbol"
msgstr "Symbol"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Valgfritt enhetssymbol"

#: common/models.py:1857
msgid "Definition"
msgstr "Definisjon"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Enhetsdefinisjon"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Vedlegg"

#: common/models.py:1933
msgid "Missing file"
msgstr "Fil mangler"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Mangler eksternlenke"

#: common/models.py:1971
msgid "Model type"
msgstr ""

#: common/models.py:1972
msgid "Target model type for image"
msgstr ""

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Velg fil å legge ved"

#: common/models.py:1996
msgid "Comment"
msgstr "Kommentar"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Vedleggskommentar"

#: common/models.py:2013
msgid "Upload date"
msgstr "Opplastet dato"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Datoen som filen ble lastet opp"

#: common/models.py:2018
msgid "File size"
msgstr "Filstørrelse"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Filstørrelse i byte"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Ugyldig modelltype spesifisert for vedlegg"

#: common/models.py:2077
msgid "Custom State"
msgstr ""

#: common/models.py:2078
msgid "Custom States"
msgstr ""

#: common/models.py:2083
msgid "Reference Status Set"
msgstr ""

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr ""

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Verdi"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2102
msgid "Name of the state"
msgstr ""

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr ""

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr ""

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr ""

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr ""

#: common/models.py:2128
msgid "Model"
msgstr ""

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr ""

#: common/models.py:2144
msgid "Model must be selected"
msgstr ""

#: common/models.py:2147
msgid "Key must be selected"
msgstr ""

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr ""

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr ""

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr ""

#: common/models.py:2222
msgid "Selection Lists"
msgstr ""

#: common/models.py:2227
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2234
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr ""

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2255
msgid "Source Plugin"
msgstr ""

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2261
msgid "Source String"
msgstr ""

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2271
msgid "Default Entry"
msgstr ""

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Opprettet"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2283
msgid "Last Updated"
msgstr "Sist oppdatert"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2318
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2319
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2372
msgid "Barcode Scan"
msgstr ""

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr ""

#: common/models.py:2377
msgid "Barcode data"
msgstr ""

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr ""

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr ""

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr ""

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Kontekst"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2415
msgid "Response"
msgstr ""

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Resultat"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr ""

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr ""

#: common/models.py:2574
msgid "Email Messages"
msgstr ""

#: common/models.py:2581
msgid "Announced"
msgstr ""

#: common/models.py:2583
msgid "Sent"
msgstr ""

#: common/models.py:2584
msgid "Failed"
msgstr ""

#: common/models.py:2587
msgid "Delivered"
msgstr ""

#: common/models.py:2595
msgid "Confirmed"
msgstr ""

#: common/models.py:2601
msgid "Inbound"
msgstr ""

#: common/models.py:2602
msgid "Outbound"
msgstr ""

#: common/models.py:2607
msgid "No Reply"
msgstr ""

#: common/models.py:2608
msgid "Track Delivery"
msgstr ""

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr ""

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr ""

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr ""

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr ""

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Nøkkel"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Ny {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "En ny ordre har blitt opprettet og tilordnet til deg"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} kansellert"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "En ordre som er tildelt til deg ble kansellert"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Artikler mottatt"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Artikler har blitt mottatt mot en innkjøpsordre"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Artikler har blitt mottatt mot en returordre"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr ""

#: common/serializers.py:486
msgid "Is Running"
msgstr "Kjører"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Ventende oppgaver"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Planlagte oppgaver"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Mislykkede oppgaver"

#: common/serializers.py:519
msgid "Task ID"
msgstr "Oppgave-ID"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "Unik oppgave-ID"

#: common/serializers.py:521
msgid "Lock"
msgstr "Lås"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Låsetidspunkt"

#: common/serializers.py:523
msgid "Task name"
msgstr "Oppgavenavn"

#: common/serializers.py:525
msgid "Function"
msgstr "Funksjon"

#: common/serializers.py:525
msgid "Function name"
msgstr "Funksjonsnavn"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Argumenter"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Oppgaveargumenter"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Nøkkelordargumenter"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Nøkkelordargumenter for oppgave"

#: common/serializers.py:640
msgid "Filename"
msgstr "Filnavn"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Modelltype"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Brukeren har ikke tillatelse tillatelse å opprette eller endre vedlegg for denne modellen"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "Ingen gruppe"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "Nettstedets URL er låst av konfigurasjon"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Omstart kreves"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "En innstilling har blitt endret som krever en omstart av serveren"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Ventende migrasjoner"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Antall ventende databasemigreringer"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:199
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Navn på serverinstans"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Strengbeskrivelse for serverinstansen"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Bruk instansnavn"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Bruk instansnavnet på tittellinjen"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Begrens visning av 'om'"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Vis `about`-modal kun til superbrukere"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Firmanavn"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Internt firmanavn"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "Base-URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "Base-URL for serverinstans"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Standardvaluta"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Velg grunnvalutaen for prisberegninger"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Støttede valutaer"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Liste over støttede valutakoder"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Oppdateringsintervall for valuta"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Hvor ofte valutakurser skal oppdateres (sett til null for å deaktiverere)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "dager"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Valutaoppdaterings-plugin"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Valgt valutaoppdaterings-plugin"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Last ned fra URL"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Tillat nedlastning av eksterne bilder og filer fra ekstern URL"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Nedlastingsgrense"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Maksimal tillatt nedlastingsstørrelse for eksternt bilde"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "User-Agent brukt for å laste ned fra URL"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Tillat overstyring av User-Agent brukt for å laste ned bilder og filer fra eksterne URLer (lå stå blank for standard)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Streng URL-validering"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Krev skjemaspesifikasjon ved validering av URLer"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Intervall for oppdateringssjekk"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Tidsintervall for å se etter oppdateringer(sett til null for å skru av)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Automatisk sikkerhetskopiering"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Aktiver automatisk sikkerhetskopiering av database og mediafiler"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Automatisk sikkerhetskopieringsintervall"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Angi antall dager mellom automatiske sikkerhetskopieringshendelser"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Slettingsintervall for oppgaver"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Bakgrunnsoppgaveresultater vil bli slettet etter antall angitte dager"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Slettingsintervall for feillogg"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Feilloggene vil bli slettet etter et angitt antall dager"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Slettingsintervall for varsler"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Brukervarsler slettes etter angitt antall dager"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Strekkodestøtte"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Aktiver støtte for strekkodeleser i webgrensesnittet"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr ""

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr ""

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr ""

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr ""

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Innlesingsforsinkelse for strekkode"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Tidsforsinkelse for behandling av strekkode"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Støtte for strekkodewebkamera"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Tillat strekkodelesning via webkamera i nettleseren"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Vis Strekkodedata"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Vis strekkodedata som tekst"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr ""

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Delrevisjoner"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Aktiver revisjonsfeltet for Del"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr ""

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr ""

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN regex"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Regulært uttrykksmønster for matching av internt delnummer"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Tilat duplikat av internt delnummer"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Tillat flere deler å dele samme interne delnummer"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Tillat redigering av internt delnummer"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Tillat endring av IPN-verdien mens du redigerer en del"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Kopier BOM-data fra del"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Kopier BOM-data som standard når du dupliserer en del"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Kopier parameterdata fra del"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Kopier parameterdata som standard ved duplisering av en del"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Kopier testdata fra del"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Kopier testdata som standard ved duplisering av en del"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Kopier designmaler for kategoriparametere"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Kopier parametermaler for kategori ved oppretting av en del"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Mal"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Deler er maler som standard"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Deler kan settes sammen fra andre komponenter som standard"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Komponent"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Deler kan bli brukt som underkomponenter som standard"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Kjøpbar"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Deler er kjøpbare som standard"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Salgbar"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Deler er salgbare som standard"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Deler er sporbare som standard"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtuelle"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Deler er virtuelle som standard"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Vis relaterte deler"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Vis relaterte deler i en del"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Innledende lagerbeholdningsdata"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Tillat oppretting av innledende lagerbeholdning når en ny del opprettes"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Innledende leverandørdata"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Tillat oppretting av innledende leverandørdata når en ny del opprettes"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Visningsformat for delnavn"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Format for å vise delnavnet"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Standardikon for delkategorier"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Standardikon for delkategorier (tomt betyr ingen ikon)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Tving parameterenheter"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Hvis det er angitt en enhet, skal parameterverdiene samsvare med de angitte enhetene"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Minimum antall desimalplasser for priser"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Minimum antall desimalplasser som skal vises når man gjengir prisdata"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Maksimalt antall desimalplasser for priser"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Maksimalt antall desimalplasser som skal vises når man gjengir prisdata"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Bruk leverandørpriser"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Inkluder leverandørprisbrudd i beregninger av totalpriser"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Innkjøpshistorikkoverstyring"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Historiske innkjøpspriser overstyrer leverandørprisnivåer"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Bruk lagervarepriser"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Bruk priser fra manuelt innlagte lagervarer for prisberegninger"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Lagervare prisalder"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Unnta lagervarer som er eldre enn dette antall dager fra prisberegninger"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Bruk Variantpriser"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Inkluder variantpriser i beregninger av totale priser"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Kun aktive varianter"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Bruk kun aktive variantdeler til beregning av variantprising"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Intervall for rekalkulering av priser"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Antall dager før delpriser blir automatisk oppdatert"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Interne Priser"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Aktiver interne priser for deler"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Intern prisoverstyring"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Hvis tilgjengelig, overstyrer interne priser kalkulering av prisområde"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Aktiver etikettutskrift"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Aktiver utskrift av etiketter fra nettleseren"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "Etikettbilde-DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "DPI-oppløsning når når det genereres bildefiler for sending til utvidelser for etikettutskrift"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Aktiver Rapporter"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Aktiver generering av rapporter"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Feilsøkingsmodus"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Generer rapporter i feilsøkingsmodus (HTML-output)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr ""

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr ""

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Sidestørrelse"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Standard sidestørrelse for PDF-rapporter"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Globalt Unike Serienummer"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "Serienummer for lagervarer må være globalt unike"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Slett oppbrukt lagerbeholdning"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr ""

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Batchkodemal"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Mal for generering av standard batchkoder for lagervarer"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Lagerbeholdning utløper"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Aktiver funksjonalitet for utløp av lagerbeholdning"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Selg utløpt lagerbeholdning"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Tillat salg av utgått lagerbeholdning"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Foreldet lagerbeholdning tidsintervall"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Antall dager før lagervarer er ansett som foreldet før utløp"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Produsér Utløpt Lagerbeholdning"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Tillat produksjon med utløpt lagerbeholdning"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Kontroll over eierskap av lagerbeholdning"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Aktiver eierskap over lagerplasseringer og -varer"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Lagerplassering standard ikon"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Lagerplassering standard ikon (tomt betyr ingen ikon)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Vis installerte lagervarer"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Vis installerte lagervarer i lagertabeller"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr ""

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr ""

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr ""

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr ""

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Produksjonsordre-referansemønster"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Nødvendig mønster for å generere Produksjonsordre-referansefeltet"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr ""

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr ""

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr ""

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr ""

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr ""

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr ""

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr ""

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr ""

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr ""

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr ""

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr ""

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Aktiver returordrer"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Aktiver returordrefunksjonalitet i brukergrensesnittet"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Returordre-referansemønster"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr ""

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Rediger fullførte returordrer"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Tillat redigering av returordrer etter de er fullført"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Salgsordre-referansemønster"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Påkrevd mønster for å generere salgsordrereferansefelt"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Salgsordre standard fraktmetode"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Aktiver opprettelse av standard forsendelse med salgsordrer"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Rediger fullførte salgsordrer"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Tillat redigering av salgsordrer etter de har blitt sendt eller fullført"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr ""

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr ""

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Referansemønster for innkjøpsordre"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Obligatorisk mønster for generering av referansefelt for innkjøpsordre"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Rediger fullførte innkjøpsordre"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Tillat redigering av innkjøpsordre etter at de har blitt sendt eller fullført"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Autofullfør innkjøpsordrer"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Automatisk merk innkjøpsordre som fullført når alle ordrelinjer er mottatt"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Aktiver passord glemt"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Ativer funskjon for glemt passord på innloggingssidene"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Aktiver registrering"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Aktiver egenregistrerting for brukerer på påloggingssidene"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "Aktiver SSO"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "Aktiver SSO på innloggingssidene"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "Aktiver SSO-registrering"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Aktiver selvregistrering via SSO for brukere på innloggingssiden"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr ""

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr ""

#: common/setting/system.py:937
msgid "SSO group key"
msgstr ""

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr ""

#: common/setting/system.py:943
msgid "SSO group map"
msgstr ""

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr ""

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr ""

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr ""

#: common/setting/system.py:959
msgid "Email required"
msgstr "E-postadresse kreves"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Krevt at brukere angir e-post ved registrering"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "Auto-utfyll SSO-brukere"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Fyll automatisk ut brukeropplysninger fra SSO-kontodata"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "E-post to ganger"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Spør brukeren om e-post to ganger ved registrering"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Passord to ganger"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Spør brukeren om passord to ganger ved registrering"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Tillatte domener"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Begrens registrering til bestemte domener (kommaseparert, begynner med @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Gruppe ved registrering"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr ""

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "Krev MFA"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Brukere må bruke flerfaktorsikkerhet."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Sjekk utvidelser ved oppstart"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Sjekk at alle utvidelser er installert ved oppstart - aktiver i containermiljøer"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr ""

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr ""

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "Aktiver URL-integrasjon"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Tillat utvidelser å legge til URL-ruter"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Aktiver navigasjonsintegrasjon"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Tillat utvidelser å integrere mot navigasjon"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "Aktiver app-integrasjon"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Tillat utvidelser å legge til apper"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Aktiver tidsplanintegrasjon"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Tillat utvidelser å kjøre planlagte oppgaver"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Aktiver hendelsesintegrasjon"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Tillat utvidelser å reagere på interne hendelser"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Ekskluder eksterne plasseringer"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Automatisk varetellingsperiode"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Vis brukernes fulle navn"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Vis brukernes fulle navn istedet for brukernavn"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr ""

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr ""

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Innebygd etikettvisning"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Vis PDF-etiketter i nettleseren fremfor å lastes ned som en fil"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Standard etikettskriver"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Konfigurer hvilken etikettskriver som skal være valgt som standard"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Innebygd rapportvisning"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Vis PDF-rapporter i nettleseren fremfor å lastes ned som en fil"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Søk i Deler"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Vis deler i forhåndsvsningsvinduet for søk"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Søk i Leverandørdeler"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Vis leverandørdeler i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Søk i Produsentdeler"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Vis produsentdeler i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Skjul Inaktive Deler"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Ekskluder inaktive deler fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Søk i kategorier"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Vis delkategorier i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Søk i lagerbeholdning"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Vis lagervarer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Skjul utilgjengelige Lagervarer"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Ekskluder lagervarer som ikke er tilgjengelige fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Søk i Plasseringer"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Vis lagerplasseringer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Søk i Firma"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Vis firma i forhåndsvsningsvinduet for søk"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Søk i Produksjonsordrer"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Vis produksjonsordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Søk i Innkjøpsordrer"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Vis innkjøpsordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Ekskluder inaktive Innkjøpsordrer"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Ekskluder inaktive innkjøpsordrer fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Søk i Salgsordrer"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Vis salgsordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Ekskluder Inaktive Salgsordrer"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Ekskluder inaktive salgsordrer fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Søk i Returordrer"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Vis returordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Ekskluder Inaktive Returordrer"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Ekskluder inaktive returordrer fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Forhåndsvisning av søkeresultater"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Antall resultater å vise i hver seksjon av søkeresultatsforhåndsvisningen"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex-søk"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Aktiver regulære uttrykk i søkeord"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Helordsøk"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Søk returnerer resultater for treff med hele ord"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Escape-knappen lukker skjemaer"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Bruk Escape-knappen for å lukke modal-skjemaer"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Fast navigasjonsbar"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "Navigasjonsbarens posisjon er fast på toppen av skjermen"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Datoformat"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Foretrukket format for å vise datoer"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Motta feilrapporter"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Motta varsler om systemfeil"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr ""

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr ""

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Ingen modelltype angitt for vedlegg"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Ugyldig modelltype for vedlegg"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Minste antall plasser kan ikke være mer enn største antall plasser"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Største antall plasser kan ikke være mindre enn minste antall plasser"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Et tomt domene er ikke tillatt."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Ugyldig domenenavn: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "Delen er aktiv"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Leverandør er aktiv"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Leverandørdel er aktiv"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Intern del er aktiv"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Leverandør er aktiv"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Produsent"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Firma"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:120
msgid "Companies"
msgstr "Firmaer"

#: company/models.py:148
msgid "Company description"
msgstr "Beskrivelse av firma"

#: company/models.py:149
msgid "Description of the company"
msgstr "Beskrivelse av firmaet"

#: company/models.py:155
msgid "Website"
msgstr "Nettside"

#: company/models.py:156
msgid "Company website URL"
msgstr "Bedriftens nettside URL"

#: company/models.py:162
msgid "Phone number"
msgstr "Telefonnummer"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Kontakt-telefonnummer"

#: company/models.py:171
msgid "Contact email address"
msgstr "Kontakt e-post"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Kontakt"

#: company/models.py:178
msgid "Point of contact"
msgstr "Kontaktpunkt"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Link til ekstern bedriftsinformasjon"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Er firmaet aktivt?"

#: company/models.py:203
msgid "Is customer"
msgstr "Er kunde"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Selger du varer til dette firmaet?"

#: company/models.py:209
msgid "Is supplier"
msgstr "Er leverandør"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Kjøper du varer fra dette firmaet?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "Er produsent"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Produserer dette firmaet deler?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Standardvaluta brukt for dette firmaet"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Adresse"

#: company/models.py:355
msgid "Addresses"
msgstr "Adresser"

#: company/models.py:412
msgid "Select company"
msgstr "Velg selskap"

#: company/models.py:417
msgid "Address title"
msgstr "Adressetittel"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Tittel som beskriver addressen"

#: company/models.py:424
msgid "Primary address"
msgstr "Hovedadresse"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Sett som hovedadresse"

#: company/models.py:430
msgid "Line 1"
msgstr "Linje 1"

#: company/models.py:431
msgid "Address line 1"
msgstr "Adresselinje 1"

#: company/models.py:437
msgid "Line 2"
msgstr "Linje 2"

#: company/models.py:438
msgid "Address line 2"
msgstr "Adresselinje 2"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "Postnummer"

#: company/models.py:451
msgid "City/Region"
msgstr "Poststed/område"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Postnummerets by/område"

#: company/models.py:458
msgid "State/Province"
msgstr "Delstat/provins"

#: company/models.py:459
msgid "State or province"
msgstr "Delstat eller provins"

#: company/models.py:465
msgid "Country"
msgstr "Land"

#: company/models.py:466
msgid "Address country"
msgstr "Adressens land"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Notater til transportør"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Notater for transportør"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Interne fraktnotater"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Fraktnotater for internt bruk"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Lenke til adresseinformasjon (ekstern)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Produsentdeler"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Basisdel"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Velg del"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Velg produsent"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "MPN"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Produsentens varenummer"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "URL for ekstern produsentdel-lenke"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Produsentens delbeskrivelse"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Produsentdel parameter"

#: company/models.py:635
msgid "Parameter name"
msgstr "Parameternavn"

#: company/models.py:642
msgid "Parameter value"
msgstr "Parameterverdi"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Enheter"

#: company/models.py:650
msgid "Parameter units"
msgstr "Parameterenheter"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Pakkeenhetene må være komptible med delens basisenhet"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Pakkeenhet må være mer enn null"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "Den sammenkoblede produsentdelen må referere til samme basisdel"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Leverandør"

#: company/models.py:829
msgid "Select supplier"
msgstr "Velg leverandør"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Leverandørens lagerbeholdningsenhet"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Er denne leverandørdelen aktiv?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Velg produsentdel"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "URL for ekstern leverandørdel-lenke"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Leverandørens delbeskrivelse"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Notat"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "grunnkostnad"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimum betaling (f.eks. lageravgift)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Emballasje"

#: company/models.py:892
msgid "Part packaging"
msgstr "Delemballasje"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Pakkeantall"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Totalt antall i en enkelt pakke. La være tom for enkeltenheter."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "flere"

#: company/models.py:919
msgid "Order multiple"
msgstr "Bestill flere"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Antall tilgjengelig fra leverandør"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Tilgjengelighet oppdatert"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Dato for siste oppdatering av tilgjengelighetsdata"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Leverandørens prisbrudd"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Standardvaluta brukt for denne leverandøren"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Bedriftsnavn"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "På lager"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Plassert"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:73
msgid "Data File"
msgstr "Datafil"

#: importer/models.py:74
msgid "Data file to import"
msgstr ""

#: importer/models.py:83
msgid "Columns"
msgstr ""

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:96
msgid "Import status"
msgstr ""

#: importer/models.py:106
msgid "Field Defaults"
msgstr ""

#: importer/models.py:113
msgid "Field Overrides"
msgstr ""

#: importer/models.py:120
msgid "Field Filters"
msgstr ""

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr ""

#: importer/models.py:471
msgid "Field"
msgstr ""

#: importer/models.py:473
msgid "Column"
msgstr ""

#: importer/models.py:542
msgid "Row Index"
msgstr ""

#: importer/models.py:545
msgid "Original row data"
msgstr ""

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr ""

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Gyldig"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr ""

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr ""

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr ""

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:177
msgid "Rows"
msgstr ""

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr ""

#: importer/serializers.py:191
msgid "No rows provided"
msgstr ""

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr ""

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr ""

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr ""

#: importer/status_codes.py:13
msgid "Initializing"
msgstr ""

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr ""

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr ""

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr ""

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr ""

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr ""

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr ""

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr ""

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr ""

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr ""

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr ""

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Ukjent"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr ""

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr ""

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr ""

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr ""

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr ""

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr ""

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr ""

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr ""

#: machine/models.py:25
msgid "Name of machine"
msgstr ""

#: machine/models.py:29
msgid "Machine Type"
msgstr ""

#: machine/models.py:29
msgid "Type of machine"
msgstr ""

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr ""

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr ""

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr ""

#: machine/models.py:95
msgid "Driver available"
msgstr ""

#: machine/models.py:100
msgid "No errors"
msgstr ""

#: machine/models.py:105
msgid "Initialized"
msgstr ""

#: machine/models.py:117
msgid "Machine status"
msgstr ""

#: machine/models.py:145
msgid "Machine"
msgstr ""

#: machine/models.py:157
msgid "Machine Config"
msgstr ""

#: machine/models.py:162
msgid "Config type"
msgstr ""

#: order/api.py:121
msgid "Order Reference"
msgstr "Ordrereferanse"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr ""

#: order/api.py:165
msgid "Has Project Code"
msgstr ""

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Opprettet av"

#: order/api.py:183
msgid "Created Before"
msgstr ""

#: order/api.py:187
msgid "Created After"
msgstr ""

#: order/api.py:191
msgid "Has Start Date"
msgstr ""

#: order/api.py:199
msgid "Start Date Before"
msgstr ""

#: order/api.py:203
msgid "Start Date After"
msgstr ""

#: order/api.py:207
msgid "Has Target Date"
msgstr ""

#: order/api.py:215
msgid "Target Date Before"
msgstr ""

#: order/api.py:219
msgid "Target Date After"
msgstr ""

#: order/api.py:270
msgid "Has Pricing"
msgstr ""

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr ""

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr ""

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Ordre"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr ""

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Intern del"

#: order/api.py:578
msgid "Order Pending"
msgstr ""

#: order/api.py:958
msgid "Completed"
msgstr "Fullført"

#: order/api.py:1214
msgid "Has Shipment"
msgstr ""

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Innkjøpsordre"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Salgsordre"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Returordre"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Total pris"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Total pris for denne ordren"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Ordrevaluta"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Valuta for denne ordren (la stå tom for å bruke firmastandard)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "Kontakten samsvarer ikke med valgt firma"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Ordrebeskrivelse (valgfritt)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Velg prosjektkode for denne ordren"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Lenke til ekstern side"

#: order/models.py:458
msgid "Start date"
msgstr ""

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Måldato"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Forventet dato for levering av ordre. Bestillingen vil være forfalt etter denne datoen."

#: order/models.py:487
msgid "Issue Date"
msgstr "Sendt dato"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Dato bestillingen ble sendt"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Bruker eller gruppe ansvarlig for ordren"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Kontaktpunkt for denne ordren"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Selskapsadresse for denne ordren"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Ordrereferanse"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Status"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Status for innkjøpsordre"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Firma som varene blir bestilt fra"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Leverandørreferanse"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Leverandørens ordrereferanse"

#: order/models.py:654
msgid "received by"
msgstr "mottatt av"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Dato ordre ble fullført"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Destinasjon"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr ""

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Delleverandør må matche PO-leverandør"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "Linjeelementet samsvarer ikke med innkjøpsordre"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "Mengde må være positiv"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Kunde"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Firma som varene selges til"

#: order/models.py:1318
msgid "Sales order status"
msgstr ""

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Kundereferanse "

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Kundens ordrereferanse"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Forsendelsesdato"

#: order/models.py:1343
msgid "shipped by"
msgstr "sendt av"

#: order/models.py:1382
msgid "Order is already complete"
msgstr ""

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr ""

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Kun en åpen ordre kan merkes som fullført"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Bestillingen kan ikke fullføres da det finnes ufullstendige forsendelser"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Denne ordren kan ikke fullføres da det fortsatt er ufullstendige artikler"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1711
msgid "Item quantity"
msgstr "Antall"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Linjereferanse"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Linjenotater"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Måldato for denne linjen (la stå tomt for å bruke måldatoen fra ordren)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Linjeelementbeskrivelse (valgfritt)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Ytterligere kontekst for denne linjen"

#: order/models.py:1788
msgid "Unit price"
msgstr "Enhetspris"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "Delens leverandør må samsvare med leverandør"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr "Leverandørdel"

#: order/models.py:1891
msgid "Received"
msgstr "Mottatt"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Antall enheter mottatt"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Innkjøpspris"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Enhet-innkjøpspris"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtuell del kan ikke tildeles salgsordre"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Kun salgbare deler kan tildeles en salgsordre"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Salgspris"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Enhets-salgspris"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Sendt"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Sendt antall"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Dato for forsendelse"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Leveringsdato"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Dato for levering av forsendelse"

#: order/models.py:2221
msgid "Checked By"
msgstr "Sjekket Av"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Brukeren som sjekket forsendelsen"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Forsendelse"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Forsendelsesnummer"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Sporingsnummer"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Sporingsinformasjon for forsendelse"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "Fakturanummer"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Referansenummer for tilknyttet faktura"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "Forsendelsen er allerede sendt"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "Forsendelsen har ingen tildelte lagervarer"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "Lagervarer er ikke blitt tildelt"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Kan ikke tildele lagervare til en linje med annen del"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Kan ikke tildele lagerbeholdning til en linje uten en del"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Tildelingsantall kan ikke overstige tilgjengelig lagerbeholdning"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "Antall må være 1 for serialisert lagervare"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "Salgsordre samsvarer ikke med forsendelse"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Forsendelsen samsvarer ikke med salgsordre"

#: order/models.py:2451
msgid "Line"
msgstr "Linje"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Forsendelsesreferanse for salgsordre"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Artikkel"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Velg lagervare å tildele"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Angi lagertildelingsmengde"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Returordre-referanse"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Firmaet delen skal returneres fra"

#: order/models.py:2625
msgid "Return order status"
msgstr "Returordrestatus"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Velg artikkel som skal returneres fra kunde"

#: order/models.py:2910
msgid "Received Date"
msgstr "Mottatt Dato"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "Datoen denne returartikkelen ble mottatt"

#: order/models.py:2923
msgid "Outcome"
msgstr "Utfall"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "Utfall for dette linjeelementet"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "Kostnad forbundet med retur eller reparasjon for dette linjeelementet"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:90
msgid "Order ID"
msgstr ""

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:96
msgid "Copy Lines"
msgstr ""

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Linjeelementer"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Leverandørnavn"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "Ordren kan ikke kanselleres"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Tillat ordre å lukkes med ufullstendige linjeelementer"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "Ordren har ufullstendige linjeelementer"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "Ordren er ikke åpen"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr ""

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Innkjøpsvaluta"

#: order/serializers.py:656
msgid "Merge Items"
msgstr ""

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "SKU-kode"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Internt delnummer"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "Leverandørdel må angis"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "Innkjøpsordre må angis"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "Leverandør må samsvare med innkjøpsordre"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "Innkjøpsordre må samsvare med leverandør"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Ordrelinje"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Velg lagerplassering for mottatte enheter"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Angi batchkode for innkommende lagervarer"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Utløpsdato"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Angi serienummer for innkommende lagervarer"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:826
msgid "Barcode"
msgstr "Strekkode"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Skannet strekkode"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Strekkode allerede i bruk"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Linjeelementer må være oppgitt"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "Målplassering må angis"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "Angitte strekkodeverdier må være unike"

#: order/serializers.py:1066
msgid "Shipments"
msgstr ""

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Fullførte forsendelser"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Valuta for salgspris"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Ingen forsendelsesopplysninger oppgitt"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "Linjeelement er ikke knyttet til denne ordren"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "Mengden må være positiv"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Skriv inn serienummer for å tildele"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "Forsendelsen er allerede sendt"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "Forsendelsen er ikke knyttet til denne ordren"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Ingen treff funnet for følgende serienummer"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr ""

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "Returordrelinje"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "Linjeelementet samsvarer ikke med returordre"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "Linjeelementet er allerede mottatt"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "Artikler kan bare mottas mot ordrer som pågår"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "Valuta for linje"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Tapt"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Returnert"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "Pågående"

#: order/status_codes.py:105
msgid "Return"
msgstr "Retur"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Reparasjon"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Erstatt"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Refusjon"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Avvis"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Forfalt Innkjøpsordre"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Innkjøpsordre {po} er nå forfalt"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Forfalt Salgsordre"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Salgsordre {so} er nå forfalt"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:111
msgid "Starred"
msgstr ""

#: part/api.py:113
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr ""

#: part/api.py:130
msgid "Filter by category depth"
msgstr ""

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr ""

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr ""

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:185
msgid "Parent"
msgstr ""

#: part/api.py:187
msgid "Filter by parent category"
msgstr ""

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:434
msgid "Has Results"
msgstr ""

#: part/api.py:660
msgid "Is Variant"
msgstr ""

#: part/api.py:668
msgid "Is Revision"
msgstr ""

#: part/api.py:678
msgid "Has Revisions"
msgstr ""

#: part/api.py:859
msgid "BOM Valid"
msgstr ""

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1511
msgid "Component part is testable"
msgstr ""

#: part/api.py:1576
msgid "Uses"
msgstr ""

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Delkategori"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Delkategorier"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Standard plassering"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Standardplassering for deler i denne kategorien"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Strukturell"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Deler kan ikke tilordnes direkte til en strukturell kategori, men kan tilordnes til underkategorier."

#: part/models.py:134
msgid "Default keywords"
msgstr "Standard nøkkelord"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Standard nøkkelord for deler i denne kategorien"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Ikon"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Ikon (valgfritt)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Du kan ikke gjøre denne delkategorien strukturell fordi noen deler allerede er tilordnet den!"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Deler"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr ""

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr ""

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Delen '{self}' kan ikke brukes i BOM for '{parent}' (rekursiv)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Delen '{parent}' er brukt i BOM for '{self}' (rekursiv)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN må samsvare med regex-mønsteret {pattern}"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:724
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Lagervare med dette serienummeret eksisterer allerede"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "Duplikat av internt delnummer er ikke tillatt i delinnstillinger"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Del med dette Navnet, internt delnummer og Revisjon eksisterer allerede."

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Deler kan ikke tilordnes strukturelle delkategorier!"

#: part/models.py:1051
msgid "Part name"
msgstr "Delnavn"

#: part/models.py:1056
msgid "Is Template"
msgstr "Er Mal"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Er delen en maldel?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Er delen en variant av en annen del?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Variant av"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Delbeskrivelse (valgfritt)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Nøkkelord"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Del-nøkkelord for å øke synligheten i søkeresultater"

#: part/models.py:1093
msgid "Part category"
msgstr "Delkategori"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr ""

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Delrevisjon eller versjonsnummer"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Revisjon"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1119
msgid "Revision Of"
msgstr ""

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Hvor er denne artikkelen vanligvis lagret?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Standard leverandør"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Standard leverandørdel"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Standard utløp"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Utløpstid (i dager) for lagervarer av denne delen"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Minimal lagerbeholdning"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Minimum tillatt lagernivå"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Måleenheter for denne delen"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Kan denne delen bygges fra andre deler?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Kan denne delen brukes til å bygge andre deler?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Har denne delen sporing av unike artikler?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Kan denne delen kjøpes inn fra eksterne leverandører?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Kan denne delen selges til kunder?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Er denne delen aktiv?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Er dette en virtuell del, som et softwareprodukt eller en lisens?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "Kontrollsum for BOM"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Lagret BOM-kontrollsum"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "Stykkliste sjekket av"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "Stykkliste sjekket dato"

#: part/models.py:1312
msgid "Creation User"
msgstr "Opprettingsbruker"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Eier ansvarlig for denne delen"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Selg flere"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Valuta som brukes til å bufre prisberegninger"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Minimal BOM-kostnad"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Minste kostnad for komponentdeler"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Maksimal BOM-kostnad"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Maksimal kostnad for komponentdeler"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Minimal innkjøpskostnad"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Minimal historisk innkjøpskostnad"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Maksimal innkjøpskostnad"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Maksimal historisk innkjøpskostnad"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Minimal intern pris"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Minimal kostnad basert på interne prisbrudd"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Maksimal intern pris"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Maksimal kostnad basert på interne prisbrudd"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Minimal leverandørpris"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Minimumspris for del fra eksterne leverandører"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Maksimal leverandørpris"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Maksimalpris for del fra eksterne leverandører"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Minimal Variantkostnad"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Beregnet minimal kostnad for variantdeler"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Maksimal Variantkostnad"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Beregnet maksimal kostnad for variantdeler"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Minimal kostnad"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Overstyr minstekostnad"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Maksimal kostnad"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Overstyr maksimal kostnad"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Beregnet samlet minimal kostnad"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Beregnet samlet maksimal kostnad"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Minimal salgspris"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Minimal salgspris basert på prisbrudd"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Maksimal Salgspris"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Maksimal salgspris basert på prisbrudd"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Minimal Salgskostnad"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Minimal historisk salgspris"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Maksimal Salgskostnad"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Maksimal historisk salgspris"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Del for varetelling"

#: part/models.py:3444
msgid "Item Count"
msgstr "Antall"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Antall individuelle lagerenheter på tidspunkt for varetelling"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Total tilgjengelig lagerbeholdning på tidspunkt for varetelling"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Dato"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Dato for utført lagertelling"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Minimal lagerkostnad"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Estimert minimal kostnad for lagerbeholdning"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Maksimal lagerkostnad"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Estimert maksimal kostnad for lagerbeholdning"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3595
msgid "Part Test Template"
msgstr ""

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "Valg må være unike"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr ""

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3684
msgid "Test Name"
msgstr "Testnavn"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Angi et navn for testen"

#: part/models.py:3691
msgid "Test Key"
msgstr ""

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3699
msgid "Test Description"
msgstr "Testbeskrivelse"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Legg inn beskrivelse for denne testen"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Aktivert"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3709
msgid "Required"
msgstr "Påkrevd"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Er det påkrevd at denne testen bestås?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Krever verdi"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Krever denne testen en verdi når det legges til et testresultat?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Krever vedlegg"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Krever denne testen et filvedlegg når du legger inn et testresultat?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "Valg"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "Sjekkboksparameter kan ikke ha enheter"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "Sjekkboksparameter kan ikke ha valg"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "Navn på parametermal må være unikt"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Parameternavn"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Fysisk enheter for denne parameteren"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Parameterbeskrivelse"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Sjekkboks"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Er dette parameteret en sjekkboks?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Gyldige valg for denne parameteren (kommaseparert)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3931
msgid "Part Parameter"
msgstr ""

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Ugyldig valg for parameterverdi"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Overordnet del"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Parametermal"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Parameterverdi"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Valgfritt notatfelt"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4176
msgid "Default Value"
msgstr "Standardverdi"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Standard Parameterverdi"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4363
msgid "Select parent part"
msgstr "Velg overordnet del"

#: part/models.py:4373
msgid "Sub part"
msgstr "Underordnet del"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Velg del som skal brukes i BOM"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "BOM-antall for denne BOM-artikkelen"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Denne BOM-artikkelen er valgfri"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Denne BOM-artikkelen er forbruksvare (den spores ikke i produksjonsordrer)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "BOM-artikkelreferanse"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "BOM-artikkelnotater"

#: part/models.py:4451
msgid "Checksum"
msgstr "Kontrollsum"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "BOM-linje kontrollsum"

#: part/models.py:4457
msgid "Validated"
msgstr "Godkjent"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Denne BOM-artikkelen er godkjent"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Arves"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Denne BOM-artikkelen er arvet fra stykkliste for variantdeler"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Lagervarer for variantdeler kan brukes for denne BOM-artikkelen"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "Antall må være heltallsverdi for sporbare deler"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "Underordnet del må angis"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "BOM-artikkel erstatning"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "Erstatningsdel kan ikke være samme som hoveddelen"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Overordnet BOM-artikkel"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Erstatningsdel"

#: part/models.py:4798
msgid "Part 1"
msgstr "Del 1"

#: part/models.py:4806
msgid "Part 2"
msgstr "Del 2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "Velg relatert del"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Del-forhold kan ikke opprettes mellom en del og seg selv"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "Duplikatforhold eksisterer allerede"

#: part/serializers.py:116
msgid "Parent Category"
msgstr ""

#: part/serializers.py:117
msgid "Parent part category"
msgstr ""

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Underkategorier"

#: part/serializers.py:202
msgid "Results"
msgstr ""

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Innkjøpsvaluta for lagervaren"

#: part/serializers.py:275
msgid "File is not an image"
msgstr ""

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:480
msgid "Original Part"
msgstr "Original Del"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Velg original del å duplisere"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Kopier Bilde"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Kopier bilde fra originaldel"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Kopier Stykkliste"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Kopier stykkliste fra original del"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Kopier parametere"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Kopier parameterdata fra originaldel"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Kopier notater"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Kopier notater fra originaldel"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Innledende lagerbeholdning"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Angi initiell lagermengde for denne delen. Hvis antall er null, er ingen lagerbeholdning lagt til."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Innledende lagerplassering"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Angi initiell lagerplasering for denne delen"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Velg leverandør (eller la stå tom for å hoppe over)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Velg produsent (eller la stå tom for å hoppe over)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Produsentens delenummer"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "Valgt firma er ikke en gyldig leverandør"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "Valgt firma er ikke en gyldig produsent"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "Produsentdel som matcher dette MPN-et, finnes allerede"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "Leverandørdel som matcher denne SKU-en, finnes allerede"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Kategorinavn"

#: part/serializers.py:936
msgid "Building"
msgstr "Produseres"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Lagervarer"

#: part/serializers.py:968
msgid "Revisions"
msgstr ""

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Leverandører"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Total lagerbeholdning"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:992
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Dupliser del"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Kopier innledende data fra en annen del"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Innledende lagerbeholdning"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Lag en del med innledende lagermengde"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Leverandøropplysninger"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Legg til innledende leverandørinformasjon for denne delen"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Kopier kategoriparametre"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Kopier parametermaler fra valgt delkategori"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Eksisterende bilde"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "Filnavn for et eksisterende del-bilde"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "Bildefilen finnes ikke"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Godkjenn hele Stykklisten"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Kan Produsere"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Minstepris"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "Overstyr beregnet verdi for minimumspris"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "Valuta for minstepris"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Makspris"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "Overstyr beregnet verdi for maksimal pris"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "Valuta for maksimal pris"

#: part/serializers.py:1498
msgid "Update"
msgstr "Oppdater"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Oppdater priser for denne delen"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Kan ikke konvertere fra gitte valutaer til {default_currency}"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "Minsteprisen kan ikke være større enn maksimal pris"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "Maksimal pris kan ikke være mindre enn minstepris"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1716
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Velg del å kopiere BOM fra"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Fjern eksisterende data"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Fjern eksisterende BOM-artikler før kopiering"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Inkluder arvede"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Inkluder BOM-artikler som er arvet fra maldeler"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Hopp over ugyldige rader"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Aktiver dette alternativet for å hoppe over ugyldige rader"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Kopier erstatningsdeler"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Kopier erstatningsdeler når BOM-elementer dupliseres"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Varsel om lav lagerbeholdning"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Tilgjengelig lagerbeholdning for {part.name} har falt under det konfigurerte minimumsnivået"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:107
msgid "Sample"
msgstr ""

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Installert"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Ingen handling spesifisert"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Ingen samsvarende handling funnet"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Ingen treff funnet for strekkodedata"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Treff funnet for strekkodedata"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Strekkode samsvarer med ekisterende element"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Ingen samsvarende del-data funnet"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Finner ingen matchende leverandørdeler"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Flere samsvarende leverandørdeler funnet"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Fant leverandørdel"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Artikkelen er allerede mottatt"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Flere samsvarende elementer funnet"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Ingen samsvarende element funnet"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Strekkoden samsvarer ikke med eksisterende lagervare"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Lagervare samsvarer ikke med linjeelement"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Utilstrekkelig lagerbeholdning"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Lagervaren er tildelt en salgsordre"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Ikke nok informasjon"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Mer informasjon nødvendig for å motta artikkelen"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Mottok ordreartikkelen"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Skannet strekkodedata"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Innkjøpsordre å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Innkjøpsordre å motta artikler mot"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Innkjøpsordren har ikke blitt sendt"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Plassering å motta deler til"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Kan ikke velge en strukturell plassering"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Salgsordre å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Salgsordrelinje å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Salgsordre-forsendelse å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Forsendelsen er allerede levert"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Antall å tildele"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Utskrift av etikett mislyktes"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr ""

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree-strekkoder"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Gir innebygd støtte for strekkoder"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "InvenTree-bidragsytere"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Slack innkommende webhook"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "URL brukt til å sende meldinger til en Slack-kanal"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Åpne lenke"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree valutautveksling"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Standard valutaintegrasjon"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF etikettskriver"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Gir innebygd støtte for å skrive ut PDF-etiketter"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Feilsøkingsmodus"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Aktiver feilsøkingsmodus - returnerer rå HTML i stedet for PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Sidestørrelse på etikett-arket"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Hopp over etiketter"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Hopp over dette antallet etiketter når det skrives ut etiketterark"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Kantlinjer"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "Skriv ut en kant rundt hver etikett"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Liggende"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "Skriv ut etikett-arket i liggende modus"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "InvenTree etikett-ark skriver"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "Sprer ut flere etiketter på ett enkelt ark"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "Etiketten er for stor for sidestørrelse"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "Ingen etiketter ble generert"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "Leverandørintegrasjon - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Gir støtte for å skanne DigiKey-strekkoder"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "Leverandøren som fungerer som 'DigiKey'"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "Leverandørintegrasjon - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "Gir støtte for å skanne LCSC-strekkoder"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "Leverandøren som fungerer som \"LCSC\""

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "Leverandørintegrasjon - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "Gir støtte for å skanne Mouser-strekkoder"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "Leverandøren som fungerer som 'Mouser'"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "Leverandørintegrasjon - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "Gir støtte for å skanne TME-strekkoder"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "Leverandøren som fungerer som \"TME\""

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "Installasjon av utvidelse vellykket"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Installerte utvidelsen til {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Konfigurasjon av utvidelse"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Konfigurasjon av utvidelser"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Utvidelsens \"Key\""

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Navn på utvidelsen"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Pakkenavn"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Er utvidelsen aktiv"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Eksempel-utvidelse"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Innebygd utvidelse"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:209
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "Utvidelse"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Ingen forfatter funnet"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Utvidensen '{p}' er ikke kompatibel med nåværende InvenTree-versjon {v}"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Utvidelsen krever minst versjon {v}"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Utvidelsen krever maks versjon {v}"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "Aktiver PO"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "Aktiver Innkjøpsordrefunksjonalitet i InvenTree-grensesnittet"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "API-nøkkel"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Nøkkel kreves for tilgang til eksternt API"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Numerisk"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "En numerisk innstilling"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Valginnstilling"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "En innstilling med flere valg"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Eksempel valutakonverterings-utvidelse"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree-bidragsytere"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Kilde-URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Kilde for pakken - dette kan være et egendefinert register eller en VCS-sti"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Navn på utvidelsespakke – kan også inneholde en versjonsindikator"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versjon"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Bekreft installasjon av utvidelse"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Dette vil installere denne utvidelsen nå i gjeldende instans. Instansen vil gå i vedlikehold."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installasjonen ble ikke bekreftet"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Enten pakkenavn eller URL må angis"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Full omlasting"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Utfør en full omlasting av utvidelsesregisteret"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Tvangsomlasting"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Tving en omlasting av utvidelsesregisteret, selv om det allerede er lastet"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Hent inn utvidelser"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Hent inn utvidelser og legg dem til i registeret"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "Aktivér utvidelse"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "Aktivér denne utvidelsen"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr ""

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr ""

#: report/api.py:114
msgid "Plugin not found"
msgstr ""

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr ""

#: report/helpers.py:46
msgid "Letter"
msgstr ""

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:217
msgid "Template name"
msgstr "Malnavn"

#: report/models.py:223
msgid "Template description"
msgstr ""

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Filnavnmønster"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:287
msgid "Template is enabled"
msgstr ""

#: report/models.py:294
msgid "Target model type for template"
msgstr ""

#: report/models.py:314
msgid "Filters"
msgstr "Filtre"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr ""

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "Sidestørrelse for PDF-rapporter"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "Generer rapport i landskapsorientering"

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Bredde [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Etikettbredde, spesifisert i mm"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Høyde [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Etiketthøyde, spesifisert i mm"

#: report/models.py:780
msgid "Error printing labels"
msgstr ""

#: report/models.py:799
msgid "Snippet"
msgstr "Snutt"

#: report/models.py:800
msgid "Report snippet file"
msgstr "Rapportsnuttfil"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Filbeskrivelse for snutt"

#: report/models.py:825
msgid "Asset"
msgstr "Ressurs"

#: report/models.py:826
msgid "Report asset file"
msgstr "Rapportressursfil"

#: report/models.py:833
msgid "Asset file description"
msgstr "Ressursfilbeskrivelse"

#: report/serializers.py:96
msgid "Select report template"
msgstr ""

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:137
msgid "Select label template"
msgstr ""

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR-kode"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR-kode"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Stykkliste (BOM)"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Nødvendige materialer"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Bilde av del"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Utstedt"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Kreves for"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Utstedt av"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Leverandør ble slettet"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Enhetspris"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Ekstra linjeelementer"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr ""

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Serienummer"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Tildelinger"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Parti"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Artikler ved lagerplassering"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Testrapport for lagervare"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Installerte artikler"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Serienummer"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Testresultater"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr ""

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Bestått"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Mislykket"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Ingen resultat (obligatorisk)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Ingen resultat"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Asset-filen eksisterer ikke"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Bildefil ikke funnet"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image-taggen krever en Part-instans"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "company_image-taggen krever en Company-instans"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr ""

#: stock/api.py:340
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:594
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:623
msgid "Minimum stock"
msgstr ""

#: stock/api.py:627
msgid "Maximum stock"
msgstr ""

#: stock/api.py:630
msgid "Status Code"
msgstr "Statuskode"

#: stock/api.py:670
msgid "External Location"
msgstr "Ekstern plassering"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:868
msgid "Part Tree"
msgstr "Del-tre"

#: stock/api.py:890
msgid "Updated before"
msgstr ""

#: stock/api.py:894
msgid "Updated after"
msgstr ""

#: stock/api.py:898
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:902
msgid "Stocktake After"
msgstr ""

#: stock/api.py:907
msgid "Expiry date before"
msgstr "Utløpsdato før"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "Utløpsdato etter"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "Foreldet"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "Antall kreves"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "Gyldig del må oppgis"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "Oppgitt leverandørdel eksisterer ikke"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Leverandørdelen har en pakkestørrelse definert, men flagget \"use_pack_size\" er ikke satt"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Serienumre kan ikke angis for en ikke-sporbar del"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "Lagerplasseringstype"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "Lagerplasseringstyper"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Standard ikom for alle plasseringer som ikke har satt et ikon (valgfritt)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Lagerplassering"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Lagerplasseringer"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Eier"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Velg eier"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Lagervarer kan ikke knyttes direkte mot en strukturell lagerplassering, men kan knyttes mot underplasseringer."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Ekstern"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Dette er en ekstern lagerplassering"

#: stock/models.py:233
msgid "Location type"
msgstr "Plasseringstype"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "Lagerplasseringstype for denne plasseringen"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "De kan ikke gjøre denne plasseringen strukturell, da noen lagervarer allerede er plassert i den!"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr ""

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Lagervarer kan ikke plasseres i strukturelle plasseringer!"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Lagervare kan ikke opprettes for virtuelle deler"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Deltype ('{self.supplier_part.part}') må være {self.part}"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "Antall må være 1 for produkt med et serienummer"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Serienummeret kan ikke angis hvis antall er større enn 1"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "Elementet kan ikke tilhøre seg selv"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "Elementet må ha en produksjonsrefereanse om is_building=True"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "Produksjonsreferanse peker ikke til samme del-objekt"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Overordnet lagervare"

#: stock/models.py:1028
msgid "Base part"
msgstr "Basisdel"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Velg en tilsvarende leverandørdel for denne lagervaren"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Hvor er denne lagervaren plassert?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "Inpakningen denne lagervaren er lagret i"

#: stock/models.py:1064
msgid "Installed In"
msgstr "Installert i"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "Er denne artikkelen montert i en annen artikkel?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Serienummer for denne artikkelen"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Batchkode for denne lagervaren"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Lagerantall"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Kildeproduksjon"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Produksjon for denne lagervaren"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "Brukt av"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "Produksjonsordren som brukte denne lagervaren"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Kildeinnkjøpsordre"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Innkjøpsordre for denne lagervaren"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Tildelt Salgsordre"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Utløpsdato for lagervare. Lagerbeholdning vil bli ansett som utløpt etter denne datoen"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Slett når oppbrukt"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Slett lagervaren når beholdningen er oppbrukt"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Innkjøpspris per enhet på kjøpstidspunktet"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "Konvertert til del"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "Delen er ikke angitt som sporbar"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "Antall må være heltall"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Antall kan ikke overstige tilgjengelig lagerbeholdning ({self.quantity})"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "Antallet stemmer ikke overens med serienumrene"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "Lagervare har blitt tildelt en salgsordre"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "Lagervare er montert i en annen artikkel"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "Lagervare inneholder andre artikler"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "Lagervare har blitt tildelt til en kunde"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "Lagervare er for tiden i produksjon"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Serialisert lagerbeholdning kan ikke slås sammen"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Duplisert lagervare"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "Lagervarer må referere til samme del"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "Lagervarer må referere til samme leverandørdel"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "Lagerstatuskoder må være like"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Lagervare kan ikke flyttes fordi den ikke er på lager"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Oppføringsnotater"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Verdi må angis for denne testen"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "Vedlegg må lastes opp for denne testen"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2951
msgid "Test result"
msgstr "Testresultat"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Testens verdi"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Vedlegg til testresultat"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Testnotater"

#: stock/models.py:2978
msgid "Test station"
msgstr ""

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2985
msgid "Started"
msgstr ""

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2992
msgid "Finished"
msgstr ""

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Overodnet element"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Bruk pakningsstørrelse når du legger til: antall definert er antall pakker"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Angi serienummer for nye artikler"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "Leverandørens delnummer"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "Utløpt"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Underordnede artikler"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Innkjøpspris for denne lagervaren, per enhet eller forpakning"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Angi antall lagervarer som skal serialiseres"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Antall kan ikke overstige tilgjengelig lagerbeholdning ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Til Lagerplassering"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Serienummer kan ikke tilordnes denne delen"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "Seriernummer eksisterer allerede"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Velg lagervare å montere"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "Antall å installere"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "Angi antallet elementer som skal installeres"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "Legg til transaksjonsnotat (valgfritt)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "Antall å installere må være minst 1"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "Lagervaren er utilgjengelig"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "Valgt del er ikke i stykklisten"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "Antall å installere må ikke overskride tilgjengelig antall"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Lagerplassering for den avinstallerte artikkelen"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Velg del å konvertere lagervare til"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "Valgt del er ikke et gyldig alternativ for konvertering"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Kan ikke konvertere lagerprodukt med tildelt leverandørdel"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "Lagervare statuskode"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "Velg lagervarer for å endre status"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "Ingen lagervarer valgt"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Underplasseringer"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "Delen må være salgbar"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "Artikkelen er tildelt en salgsordre"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "Artikkelen er tildelt en produksjonsordre"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Kunde å tilordne lagervarer"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "Valgt firma er ikke en kunde"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Lagervare-tildelignsnotater"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "En liste av lagervarer må oppgis"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Notater om lagersammenslåing"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Tillat forskjellige leverandører"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Tillat lagervarer med forskjellige leverandørdeler å slås sammen"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Tillat forskjellig status"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Tillat lagervarer med forskjellige statuskoder å slås sammen"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Minst to lagervarer må oppgis"

#: stock/serializers.py:1551
msgid "No Change"
msgstr ""

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Lagervare primærnøkkel verdi"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Lager transaksjonsnotater"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr ""

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Trenger oppmerksomhet"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Skadet"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Ødelagt"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Avvist"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "I Karantene"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Gammel lagervare sporingsoppføring"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Lagevare opprettet"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Redigerte lagervare"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Tildelte serienummer"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Lager opptelt"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Lagerbeholdning manuelt lagt til"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Lagerbeholdning manuelt fjernet"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Posisjon endret"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Lagerbeholdning oppdatert"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "Montert i sammenstilling"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Fjernet fra sammenstilling"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Montert komponentartikkel"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Fjernet komponentartikkel"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Skill ut fra overordnet artikkel"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Skill ut fra underartikkel"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Sammenslåtte lagervarer"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "Konvertert til variant"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "Produksjonsartikkel opprettet"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Produksjonsartikkel fullført"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "Produksjonsartikkel avvist"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Brukt av produksjonsordre"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Sendt mot salgsordre"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Mottatt mot innkjøpsordre"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Returnert mot returordre"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Sendt til kunde"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Returnert av kunde"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Tilgang nektet"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Du har ikke tillatelse til å se denne siden."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Autentiseringsfeil"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Du har blitt logget ut av InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Side ikke funnet"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "Forespurt side eksisterer ikke"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Intern serverfeil"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s serveren reiste en intern feil"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Se feilloggen i admingrensesnittet for flere detaljer"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Nettstedet er i vedlikehold"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Siden er for øyeblikket i vedlikehold og bør være oppe igjen snart!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Omstart av server kreves"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "En konfigurasjonsinnstilling har blitt endret som krever en omstart av serveren"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Kontakt systemadministratoren for mer informasjon"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Ventende database-migrasjoner"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Det er ventende database-migrasjoner som krever oppmerksomhet"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Klikk på følgende lenke for å se denne ordren"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Lagerbeholdning kreves for følgende produksjonsordre"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Produksjonsordre %(build)s - produserer %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klikk på følgende lenke for å se denne produksjonsordren"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Følgende deler har for lav lagerbeholdning"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Antall som kreves"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Du mottar denne e-posten fordi du abonnerer på varsler for denne delen "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klikk på følgende lenke for å se denne delen"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimum antall"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Brukere"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Velg hvilke brukere som er tilordnet denne gruppen"

#: users/admin.py:137
msgid "Personal info"
msgstr "Personlig informasjon"

#: users/admin.py:139
msgid "Permissions"
msgstr "Tillatelser"

#: users/admin.py:142
msgid "Important dates"
msgstr "Viktige datoer"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Token er tilbakekalt"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token har utløpt"

#: users/models.py:100
msgid "API Token"
msgstr "API-Token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API-Tokener"

#: users/models.py:137
msgid "Token Name"
msgstr "Tokennavn"

#: users/models.py:138
msgid "Custom token name"
msgstr "Egendefinert tokennavn"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Token utløpsdato"

#: users/models.py:152
msgid "Last Seen"
msgstr "Sist sett"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Sist gang tokenet ble brukt"

#: users/models.py:157
msgid "Revoked"
msgstr "Tilbakekalt"

#: users/models.py:235
msgid "Permission set"
msgstr "Tillatelse satt"

#: users/models.py:244
msgid "Group"
msgstr "Gruppe"

#: users/models.py:248
msgid "View"
msgstr "Visning"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Tillatelse til å se elementer"

#: users/models.py:252
msgid "Add"
msgstr "Legg til"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Tillatelse til å legge til elementer"

#: users/models.py:256
msgid "Change"
msgstr "Endre"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Tillatelse til å endre elementer"

#: users/models.py:262
msgid "Delete"
msgstr "Slett"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Tillatelse til å slette elementer"

#: users/models.py:501
msgid "Bot"
msgstr ""

#: users/models.py:502
msgid "Internal"
msgstr ""

#: users/models.py:504
msgid "Guest"
msgstr ""

#: users/models.py:513
msgid "Language"
msgstr ""

#: users/models.py:514
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:519
msgid "Theme"
msgstr ""

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr ""

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr ""

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:541
msgid "Position"
msgstr ""

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr ""

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr ""

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr ""

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr ""

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr "Administrator"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Innkjøpsordrer"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Salgsordre"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Returordrer"

#: users/serializers.py:196
msgid "Username"
msgstr "Brukernavn"

#: users/serializers.py:199
msgid "First Name"
msgstr "Fornavn"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Fornavn på brukeren"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Etternavn"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Etternavn på brukeren"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "E-postadressen til brukeren"

#: users/serializers.py:326
msgid "Staff"
msgstr "Personale"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Har denne brukeren personelltillatelser"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Superbruker"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "Er denne brukeren en superbruker"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Er denne brukerkontoen aktiv"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Din konto er opprettet."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Vennligst bruk funksjonen for å tilbakestille passord for å logge inn"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Velkommen til InvenTree"

