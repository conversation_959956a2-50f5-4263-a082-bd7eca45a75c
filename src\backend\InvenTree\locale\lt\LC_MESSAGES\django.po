msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Lithuanian\n"
"Language: lt_LT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && (n%100>19 || n%100<11) ? 0 : (n%10>=2 && n%10<=9) && (n%100>19 || n%100<11) ? 1 : n%1!=0 ? 2: 3);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: lt\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Prieš atliekant bet kokius veiksmus, privalote įjungti dviejų veiksnių autentifikavimą."

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "API galinis taškas nerastas"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr "Masiniam veiksmui turi būti pateiktas elementų arba filtrų sąrašas"

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr "Elementai turi būti pateikti kaip sąrašas"

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Pateiktas neteisingas elementų sąrašas"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr "\"Filtrai turi būti pateikti kaip žodynas"

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Pateikti neteisingi filtrai"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr "Filtras „all“ gali būti naudojamas tik su reikšme „true“"

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr "Nė vienas elementas neatitinka pateiktų kriterijų"

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "Vartotojas neturi teisių peržiūrėti šio modelio"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "El. paštas (pakartotinai)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "El. pašto adreso patvirtinimas"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Kiekvieną kartą turite įvesti tą patį el. pašto adresą."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Pateiktas pagrindinis el. pašto adresas neteisingas."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Pateiktas el. pašto domenas nepatvirtintas."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Neteisingai nurodytas vienetas ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Nepateikta reikšmė"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Nepavyko konvertuoti {original} į {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Pateiktas neteisingas kiekis"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Išsami klaidos informacija pateikta administravimo skydelyje"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Įveskite datą"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Neteisinga dešimtainė reikšmė"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Pastabos"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Reikšmė „{name}“ neatitinka šablono formato"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Pateikta reikšmė neatitinka reikalaujamo šablono: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Negalima iš karto susieti daugiau nei 1000 elementų"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Nepateiktas serijos numeris"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Pasikartojantis serijinis numeris"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Neteisinga grupė: {group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Grupės {group} kiekis viršija leistiną kiekį ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Serijos numerių nerasta"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Pašalinkite HTML žymes iš šios reikšmės"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "Duomenyse yra draudžiamo „markdown“ turinio"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Ryšio klaida"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Serveris grąžino netinkamą būsenos kodą"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Įvyko išimtis"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Serveris grąžino neteisingą „Content-Length“ reikšmę"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Paveikslėlio dydis per didelis"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Paveikslėlio atsisiuntimas viršijo maksimalų leistiną dydį"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Nutolęs serveris grąžino tuščią atsakymą"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Nurodytas URL nėra tinkamas paveikslėlio failas"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabų"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgarų"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Čekų"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danų"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Vokiečių"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Graikų"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Anglų"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Ispanų"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Ispanų (Meksikos)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estų"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Persų (Farsi)"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Suomių"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Prancūzų"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebrajų"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Vengrų"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italų"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japonų"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Korėjiečių"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Lietuvių"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Latvių"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Olandų"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norvegų"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Lenkų"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugalų"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugalų (Brazilijos)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumunų"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Rusų"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovakų"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovėnų"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbų"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Švedų"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Tajų"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turkų"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrainiečių"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamiečių"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Kinų (supaprastinta)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Kinų (tradicinė)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Prisijungti prie programos"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "El. paštas"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Klaida vykdant įskiepio patvirtinimą"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Metaduomenys turi būti „Python“ žodyno tipo objektas"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Įskiepio metaduomenys"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "JSON metaduomenų laukas, skirtas naudoti išoriniams įskiepiams"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Netinkamai suformuotas šablonas"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Nurodytas nežinomas formato raktas"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Trūksta būtino formato rakto"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "\"Nuorodos laukas negali būti tuščias"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "Nuoroda turi atitikti reikalaujamą šabloną"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Nuorodos numeris per didelis"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Neteisingas pasirinkimas"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Pavadinimas"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Aprašymas"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Aprašymas (neprivalomas)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Kelias"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Po tuo pačiu pirminiu elementu negali būti pasikartojančių pavadinimų"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Pastabos su „Markdown“ (neprivalomas)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Brūkšninio kodo duomenys"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Trečiosios šalies brūkšninio kodo duomenys"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Brūkšninio kodo maiša"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Unikali brūkšninio kodo duomenų maiša\""

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Rastas esamas brūkšninis kodas"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Užduoties klaida"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Foninė užduotis '{f}' nepavyko po {n} bandymų"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Serverio klaida"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "Serveris užfiksavo klaidą."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Turi būti teisingas skaičius"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Valiuta"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Pasirinkite valiutą iš galimų variantų"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Neteisinga reikšmė"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Nutolęs paveikslėlis"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "Nutolusio paveikslėlio failo URL"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Paveikslėlių atsisiuntimas iš nutolusio URL neįjungtas"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Nepavyko atsisiųsti paveikslėlio iš nutolusio URL"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Neteisingas fizinis vienetas"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Netinkamas valiutos kodas"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Užsakymo būsena"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Pirminė gamyba"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Įtraukti variantus"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Detalė"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Kategorija"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Ankstesnė gamyba"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Priskirta man"

#: build/api.py:154
msgid "Assigned To"
msgstr "Priskirta"

#: build/api.py:189
msgid "Created before"
msgstr "Sukurta prieš"

#: build/api.py:193
msgid "Created after"
msgstr "Sukurta po"

#: build/api.py:197
msgid "Has start date"
msgstr "Turi pradžios datą"

#: build/api.py:205
msgid "Start date before"
msgstr "Pradžios data prieš"

#: build/api.py:209
msgid "Start date after"
msgstr "Pradžios data po"

#: build/api.py:213
msgid "Has target date"
msgstr "Turi tikslinę datą"

#: build/api.py:221
msgid "Target date before"
msgstr "Tikslinė data prieš"

#: build/api.py:225
msgid "Target date after"
msgstr "Tikslinė data po"

#: build/api.py:229
msgid "Completed before"
msgstr "Užbaigta prieš"

#: build/api.py:233
msgid "Completed after"
msgstr "Užbaigta po"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr "Minimali data"

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr "Maksimali data"

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Neįtraukti medžio struktūros"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "Prieš ištrinant gamybą, ji turi būti atšaukta"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Sunaudojama"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Pasirinktinai"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Surinkimas"

#: build/api.py:450
msgid "Tracked"
msgstr "Sekama"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Testuojama"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "Liko neįvykdytų užsakymų"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Priskirta"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Prieinama"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Gamybos užsakymas"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Vieta"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Gamybos užsakymai"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "Surinkimo BOM nėra patvirtintas"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Negalima sukurti gamybos užsakymo neaktyviai detalei"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Negalima sukurti gamybos užsakymo atrakintai detalei"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Turi būti nurodytas atsakingas vartotojas arba grupė"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "Gamybos užsakymo detalės keisti negalima"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr "Tikslinė data turi būti po pradžios datos"

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Gamybos užsakymo nuoroda"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Nuoroda"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Trumpas gamybos aprašymas (neprivalomas)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Gamybos užsakymas, kuriam ši gamyba priskirta"

#: build/models.py:273
msgid "Select part to build"
msgstr "Pasirinkite detalę gamybai"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Pardavimo užsakymo nuoroda"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Pardavimo užsakymas, kuriam ši gamyba priskirta"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Šaltinio vieta"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Pasirinkite vietą atsargoms paimti šiai gamybai (palikite tuščią, jei tinka bet kuri vieta)"

#: build/models.py:300
msgid "External Build"
msgstr ""

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr "Paskirties vieta"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Pasirinkite vietą, kur bus laikomos užbaigtos prekės"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Gamybos kiekis"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Atsargų kiekis, kurias reikia pagaminti"

#: build/models.py:322
msgid "Completed items"
msgstr "Užbaigtos prekės"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Užbaigtų atsargų elementų skaičius"

#: build/models.py:328
msgid "Build Status"
msgstr "Gamybos būsena"

#: build/models.py:333
msgid "Build status code"
msgstr "Gamybos būsenos kodas"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Partijos kodas"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Šios gamybos partijos kodas"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Sukūrimo data"

#: build/models.py:356
msgid "Build start date"
msgstr "Gamybos pradžios data"

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr "Planuojama šio gamybos užsakymo pradžios data"

#: build/models.py:363
msgid "Target completion date"
msgstr "Tikslinė užbaigimo data"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Planuojama gamybos pabaigos data. Po šios datos gamyba bus pavėluota."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Užbaigimo data"

#: build/models.py:378
msgid "completed by"
msgstr "Užbaigė"

#: build/models.py:387
msgid "Issued by"
msgstr "Išdavė"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Vartotojas, kuris išdavė šį gamybos užsakymą"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Atsakingas"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Vartotojas ar grupė, atsakinga už šį gamybos užsakymą"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Išorinė nuoroda"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Nuoroda į išorinį URL"

#: build/models.py:410
msgid "Build Priority"
msgstr "Gamybos prioritetas"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Šio gamybos užsakymo prioritetas"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Projekto kodas"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Šio gamybos užsakymo projekto kodas"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Nepavyko perduoti užduoties, kad būtų atlikti gamybos paskirstymai"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Gamybos užsakymas {build} užbaigtas"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "Gamybos užsakymas užbaigtas"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Sekamoms detalėms būtina nurodyti serijos numerius"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Nepateiktas gamybos rezultatas"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "Gamybos rezultatas jau užbaigtas"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "Gamybos rezultatas neatitinka gamybos užsakymo"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "Kiekis turi būti didesnis nei nulis"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "Kiekis negali viršyti rezultato kiekio"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Gamybos rezultatas {serial} nepraėjo visų privalomų testų"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Gamybos užsakymo eilutės įrašas"

#: build/models.py:1602
msgid "Build object"
msgstr "Gamybos objektas"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Kiekis"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Reikalingas kiekis gamybos užsakymui"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Gamybos elementas turi nurodyti rezultatą, nes pagrindinė detalė pažymėta kaip sekama"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Priskirtas kiekis ({q}) negali viršyti galimo atsargų kiekio ({a})"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "Atsargų elementas per daug paskirstytas"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "Priskirtas kiekis turi būti didesnis nei nulis"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "Atsargoms su serijos numeriais kiekis turi būti 1"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "Pasirinktas atsargų elementas neatitinka BOM eilutės"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Atsargų elementas"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Šaltinio atsargų elementas"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Atsargų kiekis, skirtas paskirstyti į gamybą"

#: build/models.py:1924
msgid "Install into"
msgstr "Įdiegti į"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Paskirties atsargų elementas"

#: build/serializers.py:115
msgid "Build Level"
msgstr "Gamybos lygis"

#: build/serializers.py:124
msgid "Part Name"
msgstr "Detalės pavadinimas"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Projekto kodo etiketė"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Gamybos rezultatas"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "Gamybos rezultatas neatitinka pirminės gamybos"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "Rezultato detalė neatitinka gamybos užsakymo detalės"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Šis gamybos rezultatas jau užbaigtas"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Šis gamybos rezultatas nėra visiškai paskirstytas"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Įveskite kiekį gamybos rezultatui"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Sekamoms detalėms reikalingas sveikasis kiekis"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Reikalingas sveikasis kiekis, nes komplektavimo žiniaraštyje yra sekamų detalių"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Serijos numeriai"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Įveskite serijos numerius gamybos rezultatams"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Atsargų vieta gamybos rezultatams"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Automatiškai priskirti serijos numerius"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Automatiškai priskirti reikalingas prekes su atitinkančiais serijos numeriais"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "Šie serijos numeriai jau egzistuoja arba yra neteisingi"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "Turi būti pateiktas gamybos rezultatų sąrašas"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Atsargų vieta brokuotiems rezultatams"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Atmesti priskyrimus"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Atmesti visus atsargų priskyrimus brokuotiems rezultatams"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Priežastis, dėl kurios gamybos rezultatas(-ai) buvo nurašytas(-i)"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Vieta, kur laikomi užbaigti gamybos rezultatai"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Priimti nepilną priskyrimą"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Užbaigti rezultatus, net jei atsargos dar nėra pilnai priskirtos"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Sunaudoti priskirtas atsargas"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Sunaudoti bet kokias šiai gamybai jau priskirtas atsargas"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Pašalinti nebaigtus rezultatus"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Ištrinti visus nebaigtus gamybos rezultatus"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Neleidžiama"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Priimti kaip sunaudotą šio gamybos užsakymo metu"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Panaikinkite priskyrimus prieš užbaigiant šį gamybos užsakymą"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Per daug paskirstytos atsargos"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Kaip norite elgtis su papildomai šiam gamybos užsakymui priskirtomis atsargomis"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Kai kurios atsargos paskirstytos per daug"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Priimti nepriskirtą"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Priimti, kad atsargos nebuvo visiškai priskirtos šiam gamybos užsakymui"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "Reikalingos atsargos nėra visiškai priskirtos"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Priimti nepilną"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Priimti, kad ne visi reikalingi gamybos rezultatai buvo užbaigti"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "Reikalingas gamybos kiekis nebuvo užbaigtas"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "Gamybos užsakymas turi nebaigtų antrinių gamybų"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "Gamybos užsakymas turi būti gamybos būsenoje"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "Gamybos užsakymas turi nebaigtų rezultatų"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Gamybos eilutė"

#: build/serializers.py:877
msgid "Build output"
msgstr "Gamybos rezultatas"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "Gamybos rezultatas turi būti susietas su ta pačia gamyba"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Gamybos eilutės įrašas"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part turi būti ta pati detalė kaip ir gamybos užsakyme"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "Prekė turi būti atsargose"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Viršytas prieinamas kiekis ({q})"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Sekamų detalių priskyrymui turi būti nurodytas gamybos rezultatas"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Negalima nurodyti gamybos rezultato nesekamoms detalėms"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Turi būti pateikti paskirstymo elementai"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Atsargų vieta, iš kurios bus imamos detalės (palikite tuščią, jei tinka bet kuri vieta)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Neįtraukti vietos"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Neįtraukti atsargų iš šios pasirinktos vietos"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Keičiamos atsargos"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Atsargos iš skirtingų vietų gali būti naudojamos pakaitomis"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Pakaitinės atsargos"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Leisti priskirti pakaitines detales"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Pasirenkami elementai"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Priskirti papildomus BOM elementus gamybos užsakymui"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Nepavyko paleisti automatinio paskirstymo užduoties"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "BOM nuoroda"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "BOM detalės ID"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "BOM detalės pavadinimas"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "Gamyba"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Tiekėjo detalė"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Priskirtas kiekis"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Gamybos nuoroda"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Detalės kategorijos pavadinimas"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Sekama"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Paveldėta"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Leisti variantus"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "BOM elementas"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "Užsakyta"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "Gamyboje"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Išorinės atsargos"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Prieinamos atsargos"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Prieinamos pakaitinės atsargos"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Prieinamos variantų atsargos"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Laukiama"

#: build/status_codes.py:12
msgid "Production"
msgstr "Gamyba"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "Sulaikyta"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Atšaukta"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Užbaigta"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Atsargos, reikalingos gamybos užsakymui"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Vėluojantis gamybos užsakymas"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Gamybos užsakymas {bo} dabar vėluoja"

#: common/api.py:688
msgid "Is Link"
msgstr "Yra nuoroda"

#: common/api.py:696
msgid "Is File"
msgstr "Yra failas"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "Vartotojas neturi teisės ištrinti šių priedų"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "Vartotojas neturi teisės ištrinti šio priedo"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Netinkamas valiutos kodas"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Pasikartojantis valiutos kodas"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Nepateikta jokių galiojančių valiutos kodų"

#: common/currency.py:146
msgid "No plugin"
msgstr "Nėra papildinio"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Atnaujinta"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Paskutinio atnaujinimo laiko žymė"

#: common/models.py:138
msgid "Update By"
msgstr ""

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "Unikalus projekto kodas"

#: common/models.py:171
msgid "Project description"
msgstr "Projekto aprašymas"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Vartotojas arba grupė, atsakinga už šį projektą"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "Nustatymo raktas"

#: common/models.py:780
msgid "Settings value"
msgstr "Nustatymo reikšmė"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "Pasirinkta reikšmė yra netinkama"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Reikšmė turi būti loginė (taip/ne)"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Reikšmė turi būti sveikasis skaičius"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr "Reikšmė turi būti tinkamas skaičius"

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr "Reikšmė neatitinka patikros taisyklių"

#: common/models.py:914
msgid "Key string must be unique"
msgstr "Raktas turi būti unikalus"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Vartotojas"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Kiekio ribinis taškas kainai"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Kaina"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Vieneto kaina nurodytam kiekiui"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Galutinis taškas"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Galutinis taškas, kuriuo priimamas šis webhook'as"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Šio webhook'o pavadinimas"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Aktyvus"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Ar šis webhook'as aktyvus"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Raktas"

#: common/models.py:1437
msgid "Token for access"
msgstr "Prieigos raktas"

#: common/models.py:1445
msgid "Secret"
msgstr "Slaptas raktas"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Bendras slaptas HMAC raktas"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "Pranešimo ID"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Unikalus pranešimo identifikatorius"

#: common/models.py:1563
msgid "Host"
msgstr "Pagrindinis serveris"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Serveris, iš kurio gautas pranešimas"

#: common/models.py:1572
msgid "Header"
msgstr "Antraštė"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Šio pranešimo antraštė"

#: common/models.py:1580
msgid "Body"
msgstr "Turinys"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Šio pranešimo turinys"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Galutinis taškas, kuriame gautas pranešimas"

#: common/models.py:1596
msgid "Worked on"
msgstr "Apdorota"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Ar darbas su šiuo pranešimu baigtas?"

#: common/models.py:1723
msgid "Id"
msgstr "ID"

#: common/models.py:1725
msgid "Title"
msgstr "Pavadinimas"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Nuoroda"

#: common/models.py:1729
msgid "Published"
msgstr "Paskelbta"

#: common/models.py:1731
msgid "Author"
msgstr "Autorius"

#: common/models.py:1733
msgid "Summary"
msgstr "Santrauka"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Perskaityta"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Ar ši naujiena buvo perskaityta?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Paveikslėlis"

#: common/models.py:1753
msgid "Image file"
msgstr "Paveikslėlio failas"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "Modelio tipas, kuriam priskiriamas šis paveikslėlis"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "Modelio ID, kuriam priskiriamas šis paveikslėlis"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Pasirinktinis vienetas"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "Vieneto simbolis turi būti unikalus"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Vieneto pavadinimas turi būti tinkamas identifikatorius"

#: common/models.py:1843
msgid "Unit name"
msgstr "Vieneto pavadinimas"

#: common/models.py:1850
msgid "Symbol"
msgstr "Simbolis"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Nebūtinas vieneto simbolis"

#: common/models.py:1857
msgid "Definition"
msgstr "Apibrėžimas"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Vieneto apibrėžimas"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Priedas"

#: common/models.py:1933
msgid "Missing file"
msgstr "Trūksta failo"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Trūksta išorinės nuorodos"

#: common/models.py:1971
msgid "Model type"
msgstr "Modelio tipas"

#: common/models.py:1972
msgid "Target model type for image"
msgstr "Modelio tipas, kuriam skirtas paveikslėlis"

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Pasirinkite failą priedui"

#: common/models.py:1996
msgid "Comment"
msgstr "Komentaras"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Komentaras prie priedo"

#: common/models.py:2013
msgid "Upload date"
msgstr "Įkėlimo data"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Failo įkėlimo data"

#: common/models.py:2018
msgid "File size"
msgstr "Failo dydis"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Failo dydis baitais"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Netinkamas modelio tipas priedui"

#: common/models.py:2077
msgid "Custom State"
msgstr "Pasirinktinė būsena"

#: common/models.py:2078
msgid "Custom States"
msgstr "Pasirinktinės būsenos"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "Nuorodos būsenų rinkinys"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "Būsenų rinkinys, papildomas šia pasirinktine būsena"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Loginis raktas"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Loginis būsenos raktas, atitinkantis šią pasirinkitinę būseną"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Reikšmė"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr "Skaitinė reikšmė, saugoma modelio duomenų bazėje"

#: common/models.py:2102
msgid "Name of the state"
msgstr "Būsenos pavadinimas"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Etiketė"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "Etiketė, rodoma vartotojo sąsajoje"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Spalva"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "Spalva, rodoma vartotojo sąsajoje"

#: common/models.py:2128
msgid "Model"
msgstr "Modelis"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "Modelis, su kuriuo susieta būsena"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "Turi būti pasirinktas modelis"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "Turi būti pasirinktas raktas"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "Turi būti pasirinktas loginis raktas"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "Raktas turi skirtis nuo loginio rakto"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr "Turi būti pateikta tinkama nuorodos būsenos klasė"

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "Raktas turi skirtis nuo nuorodos būsenų loginių raktų"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "Loginis raktas turi būti tarp nuorodos būsenų loginių raktų"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "Pavadinimas turi skirtis nuo nuorodos būsenų pavadinimų"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Pasirinkimų sąrašas"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "Pasirinkimų sąrašai"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "Pasirinkimų sąrašo pavadinimas"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "Pasirinkimų sąrašo aprašymas"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Užrakinta"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "Ar šis sąrašas užrakintas?"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "Ar šį pasirinkimų sąrašą galima naudoti?"

#: common/models.py:2255
msgid "Source Plugin"
msgstr "Šaltinio papildinys"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr "Papildinys, pateikiantis šį pasirinkimų sąrašą"

#: common/models.py:2261
msgid "Source String"
msgstr "Šaltinio eilutė"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr "Neprivaloma eilutė, identifikuojanti šaltinį, naudotą šiam sąrašui"

#: common/models.py:2271
msgid "Default Entry"
msgstr "Numatytasis įrašas"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr "Numatytasis šio pasirinkimų sąrašo įrašas"

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Sukurta"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr "Data ir laikas, kada buvo sukurtas pasirinkimų sąrašas"

#: common/models.py:2283
msgid "Last Updated"
msgstr "Paskutinį kartą atnaujinta"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr "Data ir laikas, kada paskutinį kartą buvo atnaujintas sąrašas"

#: common/models.py:2318
msgid "Selection List Entry"
msgstr "Pasirinkimų sąrašo įrašas"

#: common/models.py:2319
msgid "Selection List Entries"
msgstr "Pasirinkimų sąrašo įrašai"

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr "Pasirinkimų sąrašas, kuriam priklauso šis įrašas"

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr "Pasirinkimų sąrašo įrašo reikšmė"

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr "Pasirinkimų įrašo etiketė"

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr "Pasirinkimų įrašo aprašymas"

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr "Ar šis sąrašo įrašas aktyvus?"

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "Brūkšninio kodo nuskaitymas"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Data"

#: common/models.py:2377
msgid "Barcode data"
msgstr "Brūkšninio kodo duomenys"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "Vartotojas, nuskaitęs brūkšninį kodą"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Laiko žymė"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "Brūkšninio kodo nuskaitymo data ir laikas"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "URL galutinis taškas, kuris apdorojo brūkšninį kodą"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Kontekstas"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "Konteksto duomenys brūkšninio kodo nuskaitymui"

#: common/models.py:2415
msgid "Response"
msgstr "Atsakas"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "Atsako duomenys iš brūkšninio kodo nuskaitymo"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Rezultatas"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "Ar brūkšninio kodo nuskaitymas buvo sėkmingas?"

#: common/models.py:2505
msgid "An error occurred"
msgstr ""

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr ""

#: common/models.py:2574
msgid "Email Messages"
msgstr ""

#: common/models.py:2581
msgid "Announced"
msgstr ""

#: common/models.py:2583
msgid "Sent"
msgstr ""

#: common/models.py:2584
msgid "Failed"
msgstr ""

#: common/models.py:2587
msgid "Delivered"
msgstr ""

#: common/models.py:2595
msgid "Confirmed"
msgstr ""

#: common/models.py:2601
msgid "Inbound"
msgstr ""

#: common/models.py:2602
msgid "Outbound"
msgstr ""

#: common/models.py:2607
msgid "No Reply"
msgstr ""

#: common/models.py:2608
msgid "Track Delivery"
msgstr ""

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr ""

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr ""

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr ""

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr ""

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr ""

#: common/models.py:2703
msgid "Email Thread"
msgstr ""

#: common/models.py:2704
msgid "Email Threads"
msgstr ""

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Raktas"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Naujas {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Sukurta nauja užsakymo užduotis ir priskirta jums"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} atšaukta"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Užsakymas, kuris buvo jums priskirtas, buvo atšauktas"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Gautos prekės"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Prekės buvo gautos pagal pirkimo užsakymą"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Prekės buvo gautos pagal grąžinimo užsakymą"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr "Nurodo, ar nustatymą pakeičia aplinkos kintamasis"

#: common/serializers.py:147
msgid "Override"
msgstr "Nepaisyti"

#: common/serializers.py:486
msgid "Is Running"
msgstr "Vykdoma"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Laukiančios užduotys"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Suplanuotos užduotys"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Nepavykusios užduotys"

#: common/serializers.py:519
msgid "Task ID"
msgstr "Užduoties ID"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "Unikalus užduoties ID"

#: common/serializers.py:521
msgid "Lock"
msgstr "Užraktas"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Užrakto laikas"

#: common/serializers.py:523
msgid "Task name"
msgstr "Užduoties pavadinimas"

#: common/serializers.py:525
msgid "Function"
msgstr "Funkcija"

#: common/serializers.py:525
msgid "Function name"
msgstr "Funkcijos pavadinimas"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Argumentai"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Užduoties argumentai"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Rakto argumentai"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Užduoties rakto argumentai"

#: common/serializers.py:640
msgid "Filename"
msgstr "Failo pavadinimas"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Modelio tipas"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Vartotojas neturi leidimo kurti ar redaguoti šio modelio priedų"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr "Pasirinkimų sąrašas yra užrakintas"

#: common/setting/system.py:97
msgid "No group"
msgstr "Nėra grupės"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "Svetainės URL yra užrakintas konfigūracijoje"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Reikalingas paleidimas iš naujo"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "Nustatymas buvo pakeistas ir reikia paleisti serverį iš naujo"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Laukiančios migracijos"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Laukiančių duomenų bazės migracijų skaičius"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr "Egzemplioriaus ID"

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr "Unikalus identifikatorius šiam InvenTree egzemplioriui"

#: common/setting/system.py:199
msgid "Announce ID"
msgstr "Pranešimo ID"

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr "Skelbti serverio egzemplioriaus ID serverio būsenos informacijoje (neprisijungus)"

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Serverio egzemplioriaus pavadinimas"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Serverio egzemplioriaus pavadinimas kaip eilutė"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Naudoti egzemplioriaus pavadinimą"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Naudoti egzemplioriaus pavadinimą antraštės juostoje"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Apriboti  `apie` rodymą"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "`Apie` langą rodyti tik super-vartotojams"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Įmonės pavadinimas"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Vidinis įmonės pavadinimas"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "Pagrindinis URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "Pagrindinis URL šiam serverio egzemplioriui"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Numatytoji valiuta"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Pasirinkti pagrindinę valiutą kainų skaičiavimui"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Palaikomos valiutos"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Palaikomų valiutų kodų sąrašas"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Valiutų atnaujinimo intervalas"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Kaip dažnai atnaujinti valiutų kursus (nulis – išjungti)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "dienos"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Valiutų atnaujinimo papildinys"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Naudotinas valiutų atnaujinimo papildinys"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Atsisiųsti iš URL"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Leisti atsisiųsti išorinius paveikslėlius ir failus iš nuorodų"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Atsisiuntimo dydžio riba"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Didžiausias leistinas atsisiunčiamo paveikslėlio dydis"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "Naudojamas user-agent atsisiuntimui iš URL"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Leisti pakeisti user-agent, naudojamą atsisiunčiant paveikslėlius ir failus iš išorinio URL (palikite tuščią, jei naudoti numatytąjį)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Griežtas URL tikrinimas"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Reikalauti schemos nurodymo tikrinant URL"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Atnaujinimų tikrinimo intervalas"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Kaip dažnai tikrinti atnaujinimus (nulis – išjungti)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Automatinė atsarginė kopija"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Įjungti automatinį duomenų bazės ir failų atsarginį kopijavimą"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Automatinio atsarginės kopijos kūrimo intervalas"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Nurodykite dienų skaičių tarp atsarginių kopijų kūrimo"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Užduočių ištrynimo intervalas"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Foninių užduočių rezultatai bus ištrinti po nurodyto dienų skaičiaus"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Klaidų žurnalo ištrynimo intervalas"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Klaidų žurnalai bus ištrinti po nurodyto dienų skaičiaus"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Pranešimų ištrynimo intervalas"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Vartotojų pranešimai bus ištrinti po nurodyto dienų skaičiaus"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Brūkšninių kodų palaikymas"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Įjungti brūkšninių kodų skaitytuvo palaikymą žiniatinklio sąsajoje"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "Išsaugoti brūkšninių kodų nuskaitymus"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "Brūkšninių kodų nuskaitymo rezultatus išsaugoti duomenų bazėje"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "Maksimalus nuskaitymų skaičius"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "Maksimalus saugomų brūkšninių kodų nuskaitymų skaičius"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Brūkšninio kodo įvesties delsimas"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Brūkšninio kodo įvesties apdorojimo delsos laikas"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Brūkšninių kodų palaikymas per kamerą"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Leisti brūkšninių kodų nuskaitymą per naršyklės kamerą"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Rodyti brūkšninio kodo duomenis"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Rodyti brūkšninio kodo duomenis naršyklėje kaip tekstą"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "Brūkšninio kodo generavimo papildinys"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "Papildinys vidiniam brūkšninių kodų generavimui"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Detalių versijos"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Įjungti versijos lauką detalėms"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "Tik surinkimo versijoms"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "Leisti versijas tik surenkamoms detalėms"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Leisti pašalinti iš surinkimo"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Leisti ištrinti detales, kurios yra naudojamos surinkimuose"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN reguliarioji išraiška"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Reguliariosios išraiškos šablonas detalių IPN tikrinimui"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Leisti pasikartojančius IPN"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Leisti kelioms detalėms turėti tą patį IPN"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Leisti redaguoti IPN"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Leisti keisti IPN reikšmę redaguojant detalę"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Kopijuoti detalės BOM duomenis"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Kopijuoti BOM duomenis pagal nutylėjimą dubliuojant detalę"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Kopijuoti detalės parametrus"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Kopijuoti parametrų duomenis pagal nutylėjimą dubliuojant detalę"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Kopijuoti detalės testavimo duomenis"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Kopijuoti testavimo duomenis pagal nutylėjimą dubliuojant detalę"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Kopijuoti kategorijų parametrų šablonus"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Kopijuoti kategorijų parametrų šablonus kuriant detalę"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Šablonas"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Detalės pagal nutylėjimą yra šablonai"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Detalės pagal nutylėjimą gali būti surenkamos iš kitų komponentų"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Komponentas"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Detalės pagal nutylėjimą gali būti naudojamos kaip sub-komponentai"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Galima įsigyti"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Detalės pagal nutylėjimą gali būti įsigyjamos"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Parduodama"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Detalės pagal nutylėjimą gali būti parduodamos"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Detalės pagal nutylėjimą gali būti sekamos"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtuali"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Detalės pagal nutylėjimą yra virtualios"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Rodyti susijusias detales"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Rodyti susijusias detales pasirinktai detalei"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Pradiniai atsargų duomenys"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Leisti sukurti pradinę atsargą pridedant naują detalę"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Pradiniai tiekėjo duomenys"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Leisti sukurti pradinius tiekėjo duomenis pridedant naują detalę"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Detalės pavadinimo rodymo formatas"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Detalės pavadinimo rodymo formatas"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Detalės kategorijos numatytoji piktograma"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Detalės kategorijos numatytoji piktograma (tuščia reiškia, kad nenaudojama)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Reikalauti parametrų vienetų"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Jei nurodyti vienetai, parametro reikšmės turi atitikti nurodytus vienetus"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Mažiausias kainos dešimtainių skaičių kiekis"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Minimalus dešimtainių skaitmenų skaičius rodomas kainodaros duomenyse"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Didžiausias kainos dešimtainių skaičių kiekis"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Didžiausias dešimtainių skaitmenų skaičius rodomas kainodaros duomenyse"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Naudoti tiekėjo kainas"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Įtraukti tiekėjų kainų lygius į bendrą kainodaros skaičiavimą"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Pirkimų istorija keičia kainas"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Istorinės pirkimo kainos pakeičia tiekėjo kainų lygius"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Naudoti atsargų kainas"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Naudoti kainas iš rankiniu būdu įvestų atsargų duomenų kainodaros skaičiavimui"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Atsargų kainų galiojimo trukmė"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Pašalinti senesnes nei nurodytas dienų skaičius atsargas iš kainodaros skaičiavimų"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Naudoti variantų kainas"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Įtraukti variantų kainas į bendrą kainodaros skaičiavimą"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Tik aktyvūs variantai"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Naudoti tik aktyvius detalių variantus kainodarai"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr ""

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Kainodaros atnaujinimo intervalas"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Dienų skaičius iki automatinio detalių kainų atnaujinimo"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Vidinės kainos"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Įjungti vidines kainas detalėms"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Vidinės kainos viršenybė"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Jei yra, vidinės kainos pakeičia bendrus kainodaros skaičiavimus"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Įjungti etikečių spausdinimą"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Įjungti etikečių spausdinimą iš žiniatinklio sąsajos"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "Etiketės vaizdo DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "DPI raiška generuojant vaizdus etikečių spausdinimo papildiniams"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Įjungti ataskaitas"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Įjungti ataskaitų generavimą"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Derinimo režimas"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Generuoti ataskaitas derinimo režimu (HTML išvestis)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Registruoti ataskaitų klaidas"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Registruoti klaidas, įvykusias generuojant ataskaitas"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Puslapio dydis"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Numatytasis PDF ataskaitų puslapio dydis"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Globaliai unikalūs serijiniai numeriai"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "Atsargų serijos numeriai turi būti globaliai unikalūs"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Ištrinti išnaudotas atsargas"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Nustato numatytą elgseną, kai atsargos yra išnaudotos"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Partijos kodo šablonas"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Šablonas numatytiesiems atsargų partijos kodams generuoti"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Atsargų galiojimas"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Įjungti atsargų galiojimo funkcionalumą"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Parduoti pasibaigusias galioti atsargas"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Leisti parduoti pasibaigusias galioti atsargas"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Atsargų senėjimo laikas"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Dienų skaičius, po kurio atsargos laikomos pasenusiomis iki jų galiojimo pabaigos"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Naudoti pasibaigusias galioti atsargas gamyboje"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Leisti naudoti pasibaigusias galioti atsargas gamyboje"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Atsargų nuosavybės kontrolė"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Įjungti atsargų vietų ir vienetų nuosavybės kontrolę"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Atsargų vietos numatytoji piktograma"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Atsargų vietos numatytoji piktograma (tuščia reiškia nenaudojama)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Rodyti sumontuotas atsargas"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Rodyti sumontuotas atsargas atsargų lentelėse"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Tikrinti BOM montuojant atsargas"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Sumontuotos atsargos turi būti pirminio gaminio BOM"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Leisti perkelti neturimas atsargas"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Leisti perkelti atsargas tarp vietų net jei jų nėra atsargose"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Gamybos užsakymo nuorodos šablonas"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Privalomas šablonas gamybos užsakymo nuorodos laukui generuoti"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "Reikalauti atsakingo savininko"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "Kiekvienam užsakymui turi būti priskirtas atsakingas savininkas"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "Reikalauti aktyvios detalės"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "Neleidžia kurti gamybos užsakymų neaktyvioms detalėms"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "Reikalauti užrakintos detalės"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "Neleidžia kurti gamybos užsakymų neužrakintoms detalėms"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "Reikalauti galiojančio komplektavimo sąrašo (BOM)"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Neleidžia kurti gamybos užsakymų, kol BOM nėra patvirtintas"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "Reikalauti uždarytų antrinių užsakymų"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "Neleidžia užbaigti gamybos užsakymo, kol visi antriniai užsakymai neuždaryti"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Blokuoti, kol testai bus išlaikyti"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Neleidžia užbaigti gaminių, kol visi privalomi testai nėra išlaikyti"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Įjungti grąžinimo užsakymus"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Įjungia grąžinimo užsakymų funkciją vartotojo sąsajoje"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Grąžinimo užsakymo nuorodos šablonas"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Būtinas šablonas grąžinimo užsakymo nuorodos laukui generuoti"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Redaguoti užbaigtus grąžinimo užsakymus"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Leisti redaguoti grąžinimo užsakymus po jų užbaigimo"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Pardavimo užsakymo nuorodos šablonas"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Būtinas šablonas pardavimo užsakymo nuorodos laukui generuoti"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Numatytasis siuntinys pardavimo užsakymui"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Leisti automatiškai sukurti siuntinį kartu su pardavimo užsakymu"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Redaguoti užbaigtus pardavimo užsakymus"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Leisti redaguoti pardavimo užsakymus po jų išsiuntimo arba užbaigimo"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Pažymėti išsiųstus užsakymus kaip užbaigtus"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Pardavimo užsakymai, pažymėti kaip išsiųsti, bus automatiškai užbaigti, praleidžiant būseną „išsiųsta“"

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Pirkimo užsakymo nuorodos šablonas"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Būtinas šablonas pirkimo užsakymo nuorodos laukui generuoti"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Redaguoti užbaigtus pirkimo užsakymus"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Leisti redaguoti pirkimo užsakymus po jų išsiuntimo arba užbaigimo"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr "Konvertuoti valiutą"

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr "Konvertuoti prekių vertę į pagrindinę valiutą priimant prekes"

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Automatiškai užbaigti pirkimo užsakymus"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Automatiškai pažymėti pirkimo užsakymus kaip užbaigtus, kai visos eilutės yra gautos"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Įjungti pamiršto slaptažodžio funkciją"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Leisti naudoti pamiršto slaptažodžio funkciją prisijungimo puslapyje"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Įjungti registraciją"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Leisti vartotojams savarankiškai registruotis prisijungimo puslapyje"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "Įjungti vieningą prisijungimą (SSO)"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "Įjungti vieningą prisijungimą (SSO) prisijungimo puslapyje"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "Įjungti registraciją per SSO"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Leisti vartotojams registruotis per SSO prisijungimo puslapyje"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "Įjungti SSO grupių sinchronizavimą"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Įjungti InvenTree grupių sinchronizavimą su tapatybės tiekėjo (IdP) grupėmis"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "SSO grupės raktas"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "Grupių atributo pavadinimas, kurį pateikia tapatybės tiekėjas (IdP)"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "SSO grupių susiejimas"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "SSO grupių susiejimas su vietinėmis InvenTree grupėmis. Jei vietinė grupė neegzistuoja, ji bus sukurta."

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "Pašalinti grupes, nepriklausančias SSO"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Ar pašalinti vartotojui priskirtas grupes, jei jos nėra pateikiamos per IdP. Išjungus gali kilti saugumo problemų"

#: common/setting/system.py:959
msgid "Email required"
msgstr "El. paštas privalomas"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Reikalauti vartotojo el. pašto registracijos metu"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "Automatiškai užpildyti SSO naudotojų duomenis"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Automatiškai užpildyti vartotojo informaciją pagal SSO paskyros duomenis"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "Įvesti el. paštą du kartus"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Registracijos metu prašyti vartotojų du kartus įvesti el. paštą"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Įvesti slaptažodį du kartus"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Registracijos metu prašyti vartotojų du kartus įvesti slaptažodį"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Leidžiami domenai"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Riboti registraciją tik tam tikriems domenams (atskiriama kableliais, prasideda @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Grupė registruojantis"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Grupė, į kurią priskiriami nauji vartotojai registracijos metu. Jei įjungta SSO grupių sinchronizacija, ši grupė nustatoma tik tuo atveju, jei grupė negaunama iš IdP."

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "Reikalauti kelių veiksnių autentifikacijos (MFA)"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Vartotojai privalo naudoti kelių veiksnių apsaugą."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Tikrinti įskiepius paleidimo metu"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Tikrina, ar visi įskiepiai įdiegti paleidžiant – naudoti konteinerių aplinkose"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Tikrinti įskiepių atnaujinimus"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Įjungti periodinius įdiegtų įskiepių atnaujinimų tikrinimus"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "Įjungti URL integravimą"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Leisti įskiepiams pridėti URL maršrutus"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Įjungti navigacijos integraciją"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Leisti įskiepiams integruotis į navigaciją"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "Įjungti programų integraciją"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Leisti įskiepiams pridėti programas"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Įjungti planavimo integraciją"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Leisti įskiepiams vykdyti suplanuotas užduotis"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Įjungti įvykių integraciją"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Leisti įskiepiams reaguoti į vidinius įvykius"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "Įjungti sąsajos integraciją"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "Leisti įskiepiams integruotis į vartotojo sąsają"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr ""

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr "Įjungti projektų kodus"

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr "Įjungti projektų kodų naudojimą projektų sekimui"

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Neįtraukti išorinių vietų"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Automatinės inventorizacijos periodas"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Rodyti pilnus vartotojų vardus"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Rodyti pilnus vardus vietoj vartotojo vardų"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr "Rodyti vartotojų profilius"

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr "Rodyti vartotojų profilius jų paskyros puslapyje"

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Įjungti bandymų stoties duomenis"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Įjungti bandymų stoties duomenų rinkimą testų rezultatams"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Etikečių peržiūra naršyklėje"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Rodyti PDF etiketes naršyklėje vietoje failo atsisiuntimo"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Numatytasis etikečių spausdintuvas"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Nustatyti, kuris etikečių spausdintuvas būtų pasirenkamas pagal nutylėjimą"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Ataskaitų peržiūra naršyklėje"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Rodyti PDF ataskaitas naršyklėje vietoje failo atsisiuntimo"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Ieškoti detalių"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Rodyti detales paieškos peržiūros lange"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Ieškoti tiekėjų detalių"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Rodyti tiekėjų detales paieškos peržiūros lange"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Ieškoti gamintojų detalių"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Rodyti gamintojų detales paieškos peržiūros lange"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Slėpti neaktyvias detales"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Neaktyvios detalės nebus rodomos paieškos peržiūros lange"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Ieškoti kategorijų"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Rodyti detalių kategorijas paieškos peržiūros lange"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Ieškoti atsargų"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Rodyti atsargas paieškos peržiūros lange"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Slėpti neprieinamas atsargas"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Neprieinamos atsargos nebus rodomos paieškos peržiūros lange"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Ieškoti vietų"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Rodyti atsargų vietas paieškos peržiūros lange"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Ieškoti įmonių"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Rodyti įmones paieškos peržiūros lange"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Ieškoti gamybos užsakymų"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Rodyti gamybos užsakymus paieškos peržiūros lange"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Ieškoti pirkimo užsakymų"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Rodyti pirkimo užsakymus paieškos peržiūros lange"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Išskirti neaktyvius pirkimo užsakymus"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Neaktyvūs pirkimo užsakymai nebus rodomi paieškos peržiūros lange"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Ieškoti pardavimo užsakymų"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Rodyti pardavimo užsakymus paieškos peržiūros lange"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Išskirti neaktyvius pardavimo užsakymus"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Neaktyvūs pardavimo užsakymai nebus rodomi paieškos peržiūros lange"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Ieškoti pardavimo užsakymų siuntų"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Rodyti pardavimo užsakymų siuntas paieškos peržiūros lange"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Ieškoti grąžinimo užsakymų"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Rodyti grąžinimo užsakymus paieškos peržiūros lange"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Išskirti neaktyvius grąžinimo užsakymus"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Neaktyvūs grąžinimo užsakymai nebus rodomi paieškos peržiūros lange"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Paieškos peržiūros rezultatų kiekis"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Rezultatų skaičius, rodomas kiekviename paieškos peržiūros lango skyriuje"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Reguliariųjų išraiškų paieška"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Įjungti reguliariųjų išraiškų palaikymą paieškoje"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Ieškoti viso žodžio atitikmenų"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Paieškos užklausos grąžins tik tikslius viso žodžio atitikmenis"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Ieškoti pastabose"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "Paieškos rezultatai apima atitikmenis iš įrašo pastabų"

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Escape klavišas uždaro formas"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Naudoti Escape klavišą modalinių formų uždarymui"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Fiksuotas naršymo meniu"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "Naršymo meniu pozicija fiksuota ekrano viršuje"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr ""

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr "Naršymo piktogramos"

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr "Rodyti piktogramas naršymo juostoje"

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Datos formatas"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Pageidaujamas datos rodymo formatas"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr "Rodyti paskutinę naršymo grandį"

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr "Rodyti aktyvų puslapį naršymo grandinėje"

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Gauti klaidų ataskaitas"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Gauti pranešimus apie sistemos klaidas"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Paskutiniai naudoti spausdintuvai"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Išsaugoti paskutinius naudotojo naudotus spausdintuvus"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Nepateiktas priedų modelio tipas"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Neteisingas priedų modelio tipas"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Mažiausias vietų skaičius negali būti didesnis nei didžiausias"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Didžiausias vietų skaičius negali būti mažesnis nei mažiausias"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Tuščias domenas neleidžiamas."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Neteisingas domeno pavadinimas: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "Reikšmė turi būti didžiosiomis raidėmis"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "Reikšmė turi būti galiojantis kintamojo identifikatorius"

#: company/api.py:141
msgid "Part is Active"
msgstr "Detalė yra aktyvi"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Gamintojas yra aktyvus"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Tiekėjo detalė yra aktyvi"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Vidinė detalė yra aktyvi"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Tiekėjas yra aktyvus"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Gamintojas"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Įmonė"

#: company/api.py:316
msgid "Has Stock"
msgstr "Turi atsargų"

#: company/models.py:120
msgid "Companies"
msgstr "Įmonės"

#: company/models.py:148
msgid "Company description"
msgstr "Įmonės aprašymas"

#: company/models.py:149
msgid "Description of the company"
msgstr "Įmonės aprašymas"

#: company/models.py:155
msgid "Website"
msgstr "Tinklalapis"

#: company/models.py:156
msgid "Company website URL"
msgstr "Įmonės tinklalapio URL"

#: company/models.py:162
msgid "Phone number"
msgstr "Telefono numeris"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Kontaininis telefono numeris"

#: company/models.py:171
msgid "Contact email address"
msgstr "Kontaktinis el. pašto adresas"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Kontaktinis asmuo"

#: company/models.py:178
msgid "Point of contact"
msgstr "Kontaktinis asmuo"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Nuoroda į išorinę įmonės informaciją"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Ar ši įmonė aktyvi?"

#: company/models.py:203
msgid "Is customer"
msgstr "Yra klientas"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Ar parduodate prekes šiai įmonei?"

#: company/models.py:209
msgid "Is supplier"
msgstr "Yra tiekėjas"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Ar perkate prekes iš šios įmonės?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "Yra gamintojas"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Ar ši įmonė gamina detales?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Numatytoji valiuta, naudojama šiai įmonei"

#: company/models.py:231
msgid "Tax ID"
msgstr ""

#: company/models.py:232
msgid "Company Tax ID"
msgstr ""

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Adresas"

#: company/models.py:355
msgid "Addresses"
msgstr "Adresai"

#: company/models.py:412
msgid "Select company"
msgstr "Pasirinkite įmonę"

#: company/models.py:417
msgid "Address title"
msgstr "Adreso pavadinimas"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Pavadinimas, apibūdinantis adreso įrašą"

#: company/models.py:424
msgid "Primary address"
msgstr "Pagrindinis adresas"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Nustatyti kaip pagrindinį adresą"

#: company/models.py:430
msgid "Line 1"
msgstr "1-a eilutė"

#: company/models.py:431
msgid "Address line 1"
msgstr "Adreso 1-a eilutė"

#: company/models.py:437
msgid "Line 2"
msgstr "2-a eilutė"

#: company/models.py:438
msgid "Address line 2"
msgstr "Adreso 2-a eilutė"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "Pašto kodas"

#: company/models.py:451
msgid "City/Region"
msgstr "Miestas / regionas"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Pašto kodas, miestas / regionas"

#: company/models.py:458
msgid "State/Province"
msgstr "Valstija / provincija"

#: company/models.py:459
msgid "State or province"
msgstr "Valstija arba provincija"

#: company/models.py:465
msgid "Country"
msgstr "Šalis"

#: company/models.py:466
msgid "Address country"
msgstr "Adreso šalis"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Kurjerio siuntos pastabos"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Pastabos siuntų kurjeriui"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Vidinės siuntos pastabos"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Siuntimo pastabos vidiniam naudojimui"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Nuoroda į adreso informaciją (išorinė)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Gamintojo detalė"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Pagrindinė detalė"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Pasirinkite detalę"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Pasirinkite gamintoją"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "MPN"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Gamintojo detalės numeris (MPN)"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "Išorinės nuorodos į gamintojo detalės URL"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Gamintojo detalės aprašymas"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Gamintojo detalės parametras"

#: company/models.py:635
msgid "Parameter name"
msgstr "Parametro pavadinimas"

#: company/models.py:642
msgid "Parameter value"
msgstr "Parametro reikšmė"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Vienetai"

#: company/models.py:650
msgid "Parameter units"
msgstr "Parametro vienetai"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Pakuotės vienetai turi atitikti pagrindinės detalės vienetus"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Pakuotės vienetų kiekis turi būti didesnis už nulį"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "Susieta gamintojo detalė turi nurodyti tą pačią pagrindinę detalę"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Tiekėjas"

#: company/models.py:829
msgid "Select supplier"
msgstr "Pasirinkite tiekėją"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Tiekėjo sandėlio numeris (SKU)"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Ar ši tiekėjo detalė aktyvi?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Pasirinkite gamintojo detalę"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "Išorinės nuorodos į tiekėjo detalės URL"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Tiekėjo detalės aprašymas"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Pastaba"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "bazinė kaina"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimalus mokestis (pvz., sandėliavimo mokestis)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Pakuotė"

#: company/models.py:892
msgid "Part packaging"
msgstr "Detalės pakuotė"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Pakuotės kiekis"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Bendras kiekis vienoje pakuotėje. Palikite tuščią, jei prekė tiekiama po vieną."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "daugiklis"

#: company/models.py:919
msgid "Order multiple"
msgstr "Užsakymo daugiklis"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Tiekėjo turimas kiekis"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Prieinamumas atnaujintas"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Paskutinio prieinamumo duomenų atnaujinimo data"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Tiekėjo kainos ribos"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr "Grąžina pagrindinio adreso tekstinę išraišką. Ši savybė egzistuoja dėl suderinamumo su ankstesnėmis versijomis."

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Numatytoji valiuta, naudojama šiam tiekėjui"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Įmonės pavadinimas"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "Sandėlyje"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr "Įvyko klaida eksportuojant duomenis"

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr "Duomenų eksporto įskiepis grąžino neteisingą duomenų formatą"

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Eksporto formatas"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "Pasirinkite eksporto failo formatą"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Eksporto įskiepis"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "Pasirinkite eksporto įskiepį"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Papildoma būsena šiam elementui"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Pasirinktinės būsenos raktas"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Pasirinktinis"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Klasė"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Reikšmės"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Pateiktas"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Neteisingas būsenos kodas"

#: importer/models.py:73
msgid "Data File"
msgstr "Duomenų failas"

#: importer/models.py:74
msgid "Data file to import"
msgstr "Duomenų failas, kurį reikia importuoti"

#: importer/models.py:83
msgid "Columns"
msgstr "Stulpeliai"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr "Importavimo sesijos tikslinio modelio tipas"

#: importer/models.py:96
msgid "Import status"
msgstr "Importavimo būsena"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Laukų numatytosios reikšmės"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "Laukų perrašymai"

#: importer/models.py:120
msgid "Field Filters"
msgstr "Laukų filtrai"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "Kai kurie privalomi laukai nėra susieti"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "Šis stulpelis jau yra susietas su duomenų bazės lauku"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "Šis laukas jau yra susietas su duomenų stulpeliu"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "Stulpelių susiejimas turi būti susietas su galiojančia importavimo sesija"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "Stulpelis neegzistuoja duomenų faile"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "Laukas neegzistuoja tiksliniame modelyje"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "Pasirinktas laukas yra tik skaitomas"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "Importavimo sesija"

#: importer/models.py:471
msgid "Field"
msgstr "Laukas"

#: importer/models.py:473
msgid "Column"
msgstr "Stulpelis"

#: importer/models.py:542
msgid "Row Index"
msgstr "Eilutės indeksas"

#: importer/models.py:545
msgid "Original row data"
msgstr "Pradiniai eilutės duomenys"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Klaidos"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Galiojantis"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Nepalaikomas duomenų failo formatas"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Nepavyko atidaryti duomenų failo"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Neteisingi duomenų failo matmenys"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "Neteisingos laukų numatytosios reikšmės"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "Neteisingi laukų perrašymai"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "Neteisingi laukų filtrai"

#: importer/serializers.py:177
msgid "Rows"
msgstr "Eilutės"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Eilučių ID sąrašas, kurias priimti"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Nepateikta jokių eilučių"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "Eilutė nepriklauso šiai sesijai"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "Eilutėje yra neteisingų duomenų"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "Eilutė jau buvo užbaigta"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Inicijuojama"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Susiejami stulpeliai"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Importuojami duomenys"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Apdorojami duomenys"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Duomenų failas viršija maksimalų dydžio limitą"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Duomenų faile nėra antraščių"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Duomenų faile per daug stulpelių"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Duomenų faile per daug eilučių"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Reikšmė turi būti teisingas žodyno objektas"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Kopijos"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Etiketės spausdinamų kopijų skaičius"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Prijungta"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Nežinoma"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Spausdinama"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Nėra laikmenos"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Popieriaus užstrigimas"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Atjungta"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Etikečių spausdintuvas"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Tiesiogiai spausdinti etiketes įvairiems elementams."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Spausdintuvo vieta"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Apriboti spausdintuvo veikimą konkrečia vieta"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Įrenginio pavadinimas"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Įrenginio tipas"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Įrenginio rūšis"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Tvarkyklė"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Įrenginiui naudojama tvarkyklė"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Įrenginiai gali būti išjungti"

#: machine/models.py:95
msgid "Driver available"
msgstr "Tvarkyklė prieinama"

#: machine/models.py:100
msgid "No errors"
msgstr "Klaidų nėra"

#: machine/models.py:105
msgid "Initialized"
msgstr "Inicializuota"

#: machine/models.py:117
msgid "Machine status"
msgstr "Įrenginio būsena"

#: machine/models.py:145
msgid "Machine"
msgstr "Įrenginys"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Įrenginio konfigūracija"

#: machine/models.py:162
msgid "Config type"
msgstr "Konfigūracijos tipas"

#: order/api.py:121
msgid "Order Reference"
msgstr "Užsakymo nuoroda"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "Neįvykdyta"

#: order/api.py:165
msgid "Has Project Code"
msgstr "Turi projekto kodą"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Sukūrė"

#: order/api.py:183
msgid "Created Before"
msgstr "Sukurta prieš"

#: order/api.py:187
msgid "Created After"
msgstr "Sukurta po"

#: order/api.py:191
msgid "Has Start Date"
msgstr "Turi pradžios datą"

#: order/api.py:199
msgid "Start Date Before"
msgstr "Pradžios data prieš"

#: order/api.py:203
msgid "Start Date After"
msgstr "Pradžios data po"

#: order/api.py:207
msgid "Has Target Date"
msgstr "Turi tikslinę datą"

#: order/api.py:215
msgid "Target Date Before"
msgstr "Tikslinė data prieš"

#: order/api.py:219
msgid "Target Date After"
msgstr "Tikslinė data po"

#: order/api.py:270
msgid "Has Pricing"
msgstr "Turi kainodarą"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr "Užbaigta prieš"

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "Užbaigta po"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Užsakymas"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "Užsakymas įvykdytas"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Vidinė detalė"

#: order/api.py:578
msgid "Order Pending"
msgstr "Užsakymas laukia vykdymo"

#: order/api.py:958
msgid "Completed"
msgstr "Užbaigta"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "Turi siuntą"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Pirkimo užsakymas"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Pardavimo užsakymas"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Grąžinimo užsakymas"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Bendra kaina"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Bendra kaina už šį užsakymą"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Užsakymo valiuta"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Užsakymo valiuta (palikite tuščią, jei norite naudoti įmonės numatytąją valiutą)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr "Šis užsakymas užrakintas ir negali būti keičiamas"

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "Kontaktas nesutampa su pasirinkta įmone"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr "Pradžios data turi būti prieš tikslinę datą"

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Užsakymo aprašymas (neprivalomas)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Pasirinkite projekto kodą šiam užsakymui"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Nuoroda į išorinį puslapį"

#: order/models.py:458
msgid "Start date"
msgstr "Pradžios data"

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr "Numatyta pradžios data šiam užsakymui"

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Tikslinė data"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Tikėtina užsakymo pristatymo data. Užsakymas bus vėluojantis po šios datos."

#: order/models.py:487
msgid "Issue Date"
msgstr "Išdavimo data"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Data, kada užsakymas buvo išduotas"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Vartotojas arba grupė, atsakinga už šį užsakymą"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Kontaktinis asmuo šiam užsakymui"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Įmonės adresas šiam užsakymui"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Užsakymo nuoroda"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Būsena"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Pirkimo užsakymo būsena"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Įmonė, iš kurios užsakomos prekės"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Tiekėjo nuoroda"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Tiekėjo užsakymo nuorodos kodas"

#: order/models.py:654
msgid "received by"
msgstr "gavo"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Data, kada užsakymas buvo užbaigtas"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Paskirties vieta"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr "Paskirties vieta gautoms prekėms"

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Detalių tiekėjas turi atitikti pirkimo užsakymo tiekėją"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "Eilutės įrašas neatitinka pirkimo užsakymo"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "Kiekis turi būti teigiamas skaičius"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Klientas"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Įmonė, kuriai prekės parduodamos"

#: order/models.py:1318
msgid "Sales order status"
msgstr "Pardavimo užsakymo būsena"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Kliento nuoroda"

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Kliento užsakymo nuorodos kodas"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Siuntos data"

#: order/models.py:1343
msgid "shipped by"
msgstr "išsiuntė"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "Užsakymas jau baigtas"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "Užsakymas jau atšauktas"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Tik atviras užsakymas gali būti pažymėtas kaip užbaigtas"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Užsakymas negali būti užbaigtas, nes yra neišsiųstų siuntų"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "Užsakymas negali būti užbaigtas, nes yra nepriskirtų prekių"

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Užsakymas negali būti užbaigtas, nes yra neužbaigtų eilučių"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr "Užsakymas užrakintas ir negali būti keičiamas"

#: order/models.py:1711
msgid "Item quantity"
msgstr "Prekės kiekis"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Eilutės įrašo nuoroda"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Eilutės įrašo pastabos"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Tikslinė šio eilutės įrašo data (palikite tuščią, jei norite naudoti užsakymo tikslinę datą)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Eilutės įrašo aprašymas (neprivalomas)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Papildomas kontekstas šiai eilutei"

#: order/models.py:1788
msgid "Unit price"
msgstr "Vieneto kaina"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "Pirkimo užsakymo eilutės įrašas"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "Tiekėjo detalė turi atitikti tiekėją"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr "Tiekėjo detalė"

#: order/models.py:1891
msgid "Received"
msgstr "Gauta"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Gautų prekių kiekis"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Pirkimo kaina"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Vieneto pirkimo kaina"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "Pirkimo užsakymo papildoma eilutė"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "Pardavimo užsakymo eilutės įrašas"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtuali detalė negali būti priskirta pardavimo užsakymui"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Tik parduodamos detalės gali būti priskirtos pardavimo užsakymui"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Pardavimo kaina"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Vieneto pardavimo kaina"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Išsiųsta"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Išsiųstas kiekis"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "Pardavimo užsakymo siunta"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Siuntos data"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Pristatymo data"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Siuntos pristatymo data"

#: order/models.py:2221
msgid "Checked By"
msgstr "Patikrino"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Vartotojas, patikrinęs šią siuntą"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Siunta"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Siuntos numeris"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Sekimo numeris"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Siuntos sekimo informacija"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "Sąskaitos faktūros numeris"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Nuorodos numeris susijusiai sąskaitai faktūrai"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "Siunta jau buvo išsiųsta"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "Siunta neturi priskirtų prekių"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "Pardavimo užsakymo papildoma eilutė"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "Pardavimo užsakymo paskirstymas"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "Prekė nėra priskirta"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Negalima priskirti prekių eilutei su skirtinga detale"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Negalima priskirti prekių eilutei, jei joje nėra detalės"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Priskiriamas kiekis negali viršyti atsargų kiekio"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "Kiekis turi būti 1, jei prekė turi serijos numerį"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "Pardavimo užsakymas nesutampa su siunta"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Siunta nesutampa su pardavimo užsakymu"

#: order/models.py:2451
msgid "Line"
msgstr "Eilutė"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Pardavimo užsakymo siuntos nuoroda"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Prekė"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Pasirinkite atsargų elementą priskyrimui"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Įveskite prekių priskyrimo kiekį"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Grąžinimo užsakymo nuoroda"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Įmonė, iš kurios grąžinamos prekės"

#: order/models.py:2625
msgid "Return order status"
msgstr "Grąžinimo užsakymo būsena"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "Grąžinimo užsakymo eilutės įrašas"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr "Turi būti nurodytas atsargų elementas"

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr "Grąžinamo kiekis viršija prekių kiekį"

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr "Grąžinamo kiekis turi būti daugiau nei nulis"

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr "Neteisingas kiekis serijinio numerio prekei"

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Pasirinkite prekę grąžinimui iš kliento"

#: order/models.py:2910
msgid "Received Date"
msgstr "Gavimo data"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "Data, kada ši grąžinta prekė buvo gauta"

#: order/models.py:2923
msgid "Outcome"
msgstr "Rezultatas"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "Rezultatas šiam eilutės įrašui"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "Išlaidos, susijusios su šio eilutės įrašo grąžinimu ar remontu"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr "Grąžinimo užsakymo papildoma eilutė"

#: order/serializers.py:90
msgid "Order ID"
msgstr "Užsakymo ID"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "Užsakymo, kurį reikia dubliuoti, ID"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "Kopijuoti eilutes"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr "Kopijuoti eilutės įrašus iš pradinio užsakymo"

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr "Kopijuoti papildomas eilutes"

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr "Kopijuoti papildomas eilutes iš pradinio užsakymo"

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Eilutės įrašai"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "Užbaigtos eilutės"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "Dubliuoti užsakymą"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr "Nurodykite užsakymo dubliavimo parinktis"

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "Neteisingas užsakymo ID"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Tiekėjo pavadinimas"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "Užsakymo atšaukti negalima"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Leisti užbaigti užsakymą su neužbaigtais eilutės įrašais"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "Užsakyme yra neužbaigtų eilutės įrašų"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "Užsakymas nėra atidarytas"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "Automatinis kainų nustatymas"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Automatiškai apskaičiuoti pirkimo kainą pagal tiekėjo detalės duomenis"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Pirkimo kainos valiuta"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Sujungti elementus"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Sujungti elementus su ta pačia detale, paskirtimi ir tiksline data į vieną eilutės įrašą"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "SKU"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Vidinis detalės numeris"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "Vidinis detalės pavadinimas"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "Turi būti nurodyta tiekėjo detalė"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "Turi būti nurodytas pirkimo užsakymas"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "Tiekėjas turi atitikti pirkimo užsakymą"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "Pirkimo užsakymas turi atitikti tiekėją"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Eilutės įrašas"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Pasirinkite paskirties vietą gautiems elementams"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Įveskite partijos kodą gaunamoms atsargoms"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Galiojimo data"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr "Įveskite galiojimo datą gaunamoms atsargoms"

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Įveskite gaunamų atsargų serijos numerius"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr "Pakeisti gaunamų atsargų pakavimo informaciją"

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr "Papildoma pastaba gaunamoms atsargoms"

#: order/serializers.py:826
msgid "Barcode"
msgstr "Brūkšninis kodas"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Nuskaitytas brūkšninis kodas"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Brūkšninis kodas jau naudojamas"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Turi būti pateikti eilutės įrašai"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "Turi būti nurodyta paskirties vieta"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "Pateiktos brūkšninių kodų reikšmės turi būti unikalios"

#: order/serializers.py:1066
msgid "Shipments"
msgstr "Siuntos"

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Užbaigtos siuntos"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Pardavimo kainos valiuta"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr "Paskirstyti elementai"

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Nepateikta siuntos informacija"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "Eilutės įrašas nėra susijęs su šiuo užsakymu"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "Kiekis turi būti teigiamas"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Įveskite priskiriamus serijos numerius"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "Siunta jau išsiųsta"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "Siunta nėra susieta su šiuo užsakymu"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Nerasta atitikmenų šiems serijos numeriams"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "Šie serijos numeriai nepasiekiami"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "Grąžinimo užsakymo eilutės įrašas"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "Eilutės įrašas neatitinka grąžinimo užsakymo"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "Eilutės įrašas jau gautas"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "Elementai gali būti priimami tik pagal vykdomus užsakymus"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr "Grąžinamas kiekis"

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "Eilutės kainos valiuta"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Prarasta"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Grąžinta"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "Vykdoma"

#: order/status_codes.py:105
msgid "Return"
msgstr "Grąžinimas"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Remontas"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Keitimas"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Pinigų grąžinimas"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Atmesti"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Vėluojantis pirkimo užsakymas"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Pirkimo užsakymas {po} dabar vėluoja"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Vėluojantis pardavimo užsakymas"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Pardavimo užsakymas {so} dabar vėluoja"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr "Vėluojantis grąžinimo užsakymas"

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr "Grąžinimo užsakymas {ro} dabar vėluoja"

#: part/api.py:111
msgid "Starred"
msgstr "Pažymėta žvaigždute"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "Filtruoti pagal pažymėtas kategorijas"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Gylis"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "Filtruoti pagal kategorijos gylį"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "Aukščiausio lygio"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "Filtruoti pagal aukščiausio lygio kategorijas"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "Kaskada"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "Įtraukti sub-kategorijas į filtravimo rezultatus"

#: part/api.py:185
msgid "Parent"
msgstr "Pirminė kategorija"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Filtruoti pagal pirminę kategoriją"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "Neįtraukti sub-kategorijų po nurodyta kategorija"

#: part/api.py:434
msgid "Has Results"
msgstr "Turi rezultatų"

#: part/api.py:660
msgid "Is Variant"
msgstr "Yra variantas"

#: part/api.py:668
msgid "Is Revision"
msgstr "Yra versija"

#: part/api.py:678
msgid "Has Revisions"
msgstr "Turi versijų"

#: part/api.py:859
msgid "BOM Valid"
msgstr "BOM galiojantis"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "Surinkimo detalė gali būti testuojama"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "Komponento detalė gali būti testuojama"

#: part/api.py:1576
msgid "Uses"
msgstr "Naudoja"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Detalių kategorija"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Detalių kategorijos"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Numatytoji vieta"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Numatytoji vieta detalėms šioje kategorijoje"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Struktūrinė"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Detalės negali būti priskirtos struktūrinei kategorijai tiesiogiai, bet gali būti priskirtos jos subkategorijoms."

#: part/models.py:134
msgid "Default keywords"
msgstr "Numatytieji raktažodžiai"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Numatytieji raktažodžiai detalėms šioje kategorijoje"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Piktograma"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Piktograma (neprivaloma)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Negalite paversti šios detalių kategorijos struktūrine, nes kai kurios detalės jau jai priskirtos!"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Detalės"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "Negalima ištrinti šios detalės, nes ji užrakinta"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Negalima ištrinti šios detalės, nes ji vis dar aktyvi"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Negalima ištrinti šios detalės, nes ji naudojama sirinkime"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Detalė „{self}“ negali būti naudojama detalių sąraše „{parent}“ (rekursyviai)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Detalė „{parent}“ naudojama detalių sąraše „{self}“ (rekursyviai)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN turi atitikti regex šabloną {pattern}"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "Detalė negali būti savo pačios versija"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Negalima sukurti detalės versijos, jei tai jau yra kita versija"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "Turi būti nurodytas versijos kodas"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "Versijos leidžiamos tik surinkimo detalėms"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "Negalima sukurti šabloninės detalės versijos"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "Pagrindinė detalė turi būti susieta su tuo pačiu šablonu"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Atsargų elementas su šiuo serijos numeriu jau egzistuoja"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "IPN dublikatų detalių nustatymuose naudoti negalima"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "Tokia detalės versija jau egzistuoja."

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Detalė su tokiu pavadinimu, IPN ir versija jau egzistuoja."

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Detalės negali būti priskirtos struktūrinėms detalių kategorijoms!"

#: part/models.py:1051
msgid "Part name"
msgstr "Detalės pavadinimas"

#: part/models.py:1056
msgid "Is Template"
msgstr "Yra šablonas"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Ar ši detalė yra šabloninė detalė?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Ar ši detalė yra kitos detalės variantas?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Variantas iš"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Detalės aprašymas (neprivalomas)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Raktažodžiai"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Detalės raktažodžiai, skirti pagerinti matomumą paieškos rezultatuose"

#: part/models.py:1093
msgid "Part category"
msgstr "Detalės kategorija"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Detalės versija arba numeris"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Versija"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "Ar ši detalė yra kitos detalės versija?"

#: part/models.py:1119
msgid "Revision Of"
msgstr "Versija iš"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Kur ši detalė paprastai laikoma?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Numatytasis tiekėjas"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Numatytoji tiekėjo detalė"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Numatytasis galiojimo laikas"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Šios detalės atsargų galiojimo laikas (dienomis)"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Minimalus atsargų kiekis"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Mažiausias leidžiamas atsargų kiekis"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Šios detalės matavimo vienetai"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Ar ši detalė gali būti pagaminta iš kitų detalių?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Ar ši detalė gali būti naudojama kitoms detalėms gaminti?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Ar ši detalė turi unikalių vienetų sekimą?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "Ar šiai detalei gali būti priskirti bandymų rezultatai?"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Ar ši detalė gali būti perkama iš išorinių tiekėjų?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Ar ši detalė gali būti parduodama klientams?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Ar ši detalė yra aktyvi?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "Užrakintos detalės negali būti redaguojamos"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Ar tai virtuali detalė, pavyzdžiui, programinė įranga ar licencija?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "BOM kontrolinė suma"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Išsaugota BOM kontrolinė suma"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "Detalių sąrašą patikrino"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "Detalių sąrašo patikrinimo data"

#: part/models.py:1312
msgid "Creation User"
msgstr "Sukūręs vartotojas"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Atsakingas vartotojas už šią detalę"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Parduodamas kiekis"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Valiuta, naudojama kainų skaičiavimams kaupti"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Minimali BOM kaina"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Minimali komponentų detalių kaina"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Maksimali BOM kaina"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Maksimali komponentų detalių kaina"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Minimali pirkimo kaina"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Mažiausia istorinė pirkimo kaina"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Maksimali pirkimo kaina"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Didžiausia istorinė pirkimo kaina"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Minimali vidinė kaina"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Mažiausia kaina pagal vidinius kainų intervalus"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Maksimali vidinė kaina"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Didžiausia kaina pagal vidinius kainų intervalus"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Mažiausia tiekėjo kaina"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Mažiausia detalės kaina iš išorinių tiekėjų"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Didžiausia tiekėjo kaina"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Didžiausia detalės kaina iš išorinių tiekėjų"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Mažiausia varianto kaina"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Apskaičiuota minimali variantų detalių kaina"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Didžiausia varianto kaina"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Apskaičiuota didžiausia variantų detalių kaina"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Minimali kaina"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Nepaisyti minimalios kainos"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Maksimali kaina"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Nepaisyti maksimalios kainos"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Apskaičiuota bendra minimali kaina"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Apskaičiuota bendra maksimali kaina"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Minimali pardavimo kaina"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Mažiausia pardavimo kaina pagal kainų intervalus"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Didžiausia pardavimo kaina"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Didžiausia pardavimo kaina pagal kainų intervalus"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Mažiausia pardavimo kaina"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Mažiausia istorinė pardavimo kaina"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Didžiausia pardavimo kaina"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Didžiausia istorinė pardavimo kaina"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Detalė inventorizacijai"

#: part/models.py:3444
msgid "Item Count"
msgstr "Vienetų skaičius"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Atsargų įrašų skaičius inventorizacijos metu"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Bendras prieinamas atsargų kiekis inventorizacijos metu"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Data"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Inventorizacijos atlikimo data"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Minimali atsargų kaina"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Apytikslė minimali turimų atsargų kaina"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Maksimali atsargų kaina"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Apytikslė maksimali turimų atsargų kaina"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "Detalės kainų intervalai pardavimui"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "Detalės bandymų šablonas"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Netinkamas šablono pavadinimas - turi būti bent vienas raidinis ar skaitinis simbolis"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "Pasirinkimai turi būti unikalūs"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "Bandymų šablonus galima kurti tik testuojamoms detalėms"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "Detalė jau turi bandymų šabloną su tokiu pačiu raktu"

#: part/models.py:3684
msgid "Test Name"
msgstr "Bandymo pavadinimas"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Įveskite bandymo pavadinimą"

#: part/models.py:3691
msgid "Test Key"
msgstr "Bandymo raktas"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "Supaprastintas bandymo raktas"

#: part/models.py:3699
msgid "Test Description"
msgstr "Bandymo aprašymas"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Įveskite šio bandymo aprašymą"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Įjungta"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "Ar šis bandymas įjungtas?"

#: part/models.py:3709
msgid "Required"
msgstr "Privalomas"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Ar šį bandymą būtina išlaikyti?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Reikalauja reikšmės"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Ar šiam bandymui reikia įvesti reikšmę pridedant rezultatą?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Reikalauja priedo"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Ar šiam bandymui reikia pridėti failą su rezultatu?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "Pasirinkimai"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "Galimi pasirinkimai šiam bandymui (atskirti kableliais)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "Detalės parametro šablonas"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "Žymimojo laukelio parametrai negali turėti matavimo vienetų"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "Žymimojo laukelio parametrai negali turėti pasirinkimų"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "Parametro šablono pavadinimas turi būti unikalus"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Parametro pavadinimas"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Fiziniai šio parametro vienetai"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Parametro aprašymas"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Žymimasis laukelis"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Ar šis parametras yra žymimasis laukelis?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Galimi pasirinkimai šiam parametrui (atskirti kableliais)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr "Pasirinkimų sąrašas šiam parametrui"

#: part/models.py:3931
msgid "Part Parameter"
msgstr "Detalės parametras"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "Parametro keisti negalima - detalė užrakinta"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Neteisingas pasirinkimas parametro reikšmei"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Pirminė detalė"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Parametro šablonas"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Parametro reikšmė"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Neprivalomas pastabų laukas"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "Detalių kategorijos parametro šablonas"

#: part/models.py:4176
msgid "Default Value"
msgstr "Numatytoji reikšmė"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Numatytoji parametro reikšmė"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "BOM elemento keisti negalima - surinkimas užrakintas"

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "BOM elemento keisti negalima - varianto surinkimas užrakintas"

#: part/models.py:4363
msgid "Select parent part"
msgstr "Pasirinkite pirminę detalę"

#: part/models.py:4373
msgid "Sub part"
msgstr "Pavaldi detalė"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Pasirinkite detalę, naudojamą BOM"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "BOM reikalingas šios detalės kiekis"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Šis BOM elementas yra pasirenkamas"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Šis BOM elementas yra sunaudojamas (nesekamas gamybos užsakymuose)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "BOM nuoroda"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "BOM pastabos"

#: part/models.py:4451
msgid "Checksum"
msgstr "Kontrolinė suma"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "BOM eilutės kontrolinė suma"

#: part/models.py:4457
msgid "Validated"
msgstr "Patvirtinta"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Šis BOM elementas patvirtintas"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Paveldima"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Šį BOM elementą paveldi variantų sąrašai"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Šiam BOM elementui galima naudoti variantinių detalių atsargas"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "Sekamoms detalėms kiekis turi būti sveikasis skaičius"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "Turi būti nurodyta pavaldi detalė"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "BOM elemento pakaitalas"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "Pakaitinė detalė negali būti tokia pati kaip pagrindinė detalė"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Pagrindinis BOM elementas"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Pakaitinė detalė"

#: part/models.py:4798
msgid "Part 1"
msgstr "Detalė 1"

#: part/models.py:4806
msgid "Part 2"
msgstr "Detalė 2"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "Pasirinkite susijusią detalę"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr "Pastaba šiam ryšiui"

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Detalių ryšio negalima sukurti tarp detalės ir jos pačios"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "Toks ryšys jau egzistuoja"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "Pagrindinė kategorija"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "Pagrindinė detalių kategorija"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Subkategorijos"

#: part/serializers.py:202
msgid "Results"
msgstr "Rezultatai"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "Rezultatų skaičius, susietas su šiuo šablonu"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Šio atsargų elemento pirkimo valiuta"

#: part/serializers.py:275
msgid "File is not an image"
msgstr "Failas nėra paveikslėlis"

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "Detalių, naudojančių šį šabloną, skaičius"

#: part/serializers.py:480
msgid "Original Part"
msgstr "Pradinė detalė"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Pasirinkite pradinę detalę kopijavimui"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Kopijuoti paveikslėlį"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Kopijuoti paveikslėlį iš pradinės detalės"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Kopijuoti BOM"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Kopijuoti komplektavimo žiniaraštį iš pradinės detalės"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Kopijuoti parametrus"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Kopijuoti parametrų duomenis iš pradinės detalės"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Kopijuoti pastabas"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Kopijuoti pastabas iš pradinės detalės"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Pradinis atsargų kiekis"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Nurodykite pradinį atsargų kiekį šiai detalei. Jei kiekis nulis - atsargos nebus pridėtos."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Pradinė atsargų vieta"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Nurodykite pradinę atsargų vietą šiai detalei"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Pasirinkite tiekėją (arba palikite tuščią, jei nenorite nurodyti)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Pasirinkite gamintoją (arba palikite tuščią, jei nenorite nurodyti)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Gamintojo detalės numeris"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "Pasirinkta įmonė nėra galiojantis tiekėjas"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "Pasirinkta įmonė nėra galiojantis gamintojas"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "Detalė su šiuo gamintojo numeriu (MPN) jau egzistuoja"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "Tiekėjo detalė su šiuo SKU jau egzistuoja"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Kategorijos pavadinimas"

#: part/serializers.py:936
msgid "Building"
msgstr "Surinkimas"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Atsargos"

#: part/serializers.py:968
msgid "Revisions"
msgstr "Versijos"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Tiekėjai"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Bendros atsargos"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "Nepriskirtos atsargos"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "Variantų atsargos"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Kopijuoti detalę"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Kopijuoti pradinius duomenis iš kitos detalės"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Pradinės atsargos"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Sukurti detalę su pradiniu atsargų kiekiu"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Tiekėjo informacija"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Pridėti pradinę tiekėjo informaciją šiai detalei"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Kopijuoti kategorijos parametrus"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Kopijuoti parametrų šablonus iš pasirinktos detalių kategorijos"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Esamas paveikslėlis"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "Esamos detalės paveikslėlio failo pavadinimas"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "Paveikslėlio failas neegzistuoja"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Patvirtinti visą komplektavimo žiniaraštį"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Galima surinkti"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Mažiausia kaina"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "Pakeisti apskaičiuotą mažiausią kainą"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "Mažiausios kainos valiuta"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Didžiausia kaina"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "Pakeisti apskaičiuotą didžiausią kainą"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "Didžiausios kainos valiuta"

#: part/serializers.py:1498
msgid "Update"
msgstr "Atnaujinti"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Atnaujinti šios detalės kainodarą"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Nepavyko konvertuoti iš nurodytų valiutų į {default_currency}"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "Mažiausia kaina negali būti didesnė už didžiausią kainą"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "Didžiausia kaina negali būti mažesnė už mažiausią kainą"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr "Pasirinkite pirminį surinkimą"

#: part/serializers.py:1716
msgid "Select the component part"
msgstr "Pasirinkite komponentinę detalę"

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Pasirinkite detalę, iš kurios kopijuoti BOM"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Pašalinti esamus duomenis"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Pašalinti esamus BOM elementus prieš kopijuojant"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Įtraukti paveldėtus"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Įtraukti BOM elementus, paveldėtus iš šabloninių detalių"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Praleisti netinkamas eilutes"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Įjunkite šią parinktį, jei norite praleisti netinkamas eilutes"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Kopijuoti pakaitines detales"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Kopijuoti pakaitines detales, kai kopijuojami BOM elementai"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Perspėjimas apie mažas atsargas"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Galimas atsargų kiekis detalei {part.name} nukrito žemiau nustatyto minimalaus lygio"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr "Integruotas"

#: plugin/api.py:92
msgid "Mandatory"
msgstr "Privalomas"

#: plugin/api.py:107
msgid "Sample"
msgstr "Pavyzdys"

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Įdiegtas"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "Įskiepis negali būti ištrintas, nes šiuo metu yra aktyvus"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Nenurodytas joks veiksmas"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Atitinkantis veiksmas nerastas"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Brūkšninio kodo duomenims atitikmens nerasta"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Rastas atitikmuo brūkšninio kodo duomenims"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Modelis nepalaikomas"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "Modelio egzempliorius nerastas"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Brūkšninis kodas atitinka esamą elementą"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Atitinkančių detalės duomenų nerasta"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Tiekėjo detalių atitikmenų nerasta"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Rastos kelios atitinkančios tiekėjo detalės"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "Brūkšninio kodo duomenims atitinkančio įskiepio nerasta"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Atitinkanti tiekėjo detalė"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Prekė jau buvo priimta"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "Tiekėjo brūkšniniam kodui įskiepis nerastas"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Rasti keli atitinkantys eilutės įrašai"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Atitinkančios eilutės įrašo nerasta"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Nepateiktas pardavimo užsakymas"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Brūkšninis kodas neatitinka jokio esamo atsargų elemento"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Atsargų elementas neatitinka eilutės įrašo"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Nepakanka atsargų"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Atsargų elementas priskirtas pardavimo užsakymui"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Nepakanka informacijos"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "Rastas atitinkantis elementas"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "Tiekėjo detalė neatitinka eilutės įrašo"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "Eilutės įrašas jau užbaigtas"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Norint priimti eilutės įrašą, reikia daugiau informacijos"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Gauta pirkimo užsakymo eilutė"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "Nepavyko priimti eilutės įrašo"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Nuskaityti brūkšninio kodo duomenys"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Modelio pavadinimas, kuriam generuoti brūkšninį kodą"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "Modelio objekto pirminis raktas, kuriam generuoti brūkšninį kodą"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Pirkimo užsakymas, kuriam priskirti prekes"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr "Pirkimo užsakymas nėra atidarytas"

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "Tiekėjas, iš kurio priimamos prekės"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Pirkimo užsakymas, pagal kurį priimamos prekės"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Pirkimo užsakymas nebuvo pateiktas"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Vieta, į kurią bus priimtos prekės"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Negalima pasirinkti struktūrinės vietos"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr "Pirkimo užsakymo eilutės įrašas, pagal kurį priimamos prekės"

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr "Automatiškai priskirti atsargas pirkimo užsakymui"

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Pardavimo užsakymas, kuriam priskirti prekes"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr "Pardavimo užsakymas nėra atidarytas"

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Pardavimo užsakymo eilutės įrašas, kuriam priskirti prekes"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Pardavimo užsakymo siunta, kuriai priskirti prekes"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Siunta jau buvo pristatyta"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Kiekis, kurį reikia priskirti"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Etiketės spausdinimas nepavyko"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "Klaida generuojant etiketę į PDF formatą"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "Klaida generuojant etiketę į HTML formatą"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Nenurodyta jokių elementų spausdinimui"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Įskiepio pavadinimas"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Ypatybės tipas"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Ypatybės etiketė"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Ypatybės pavadinimas"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Ypatybės aprašymas"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Ypatybės piktograma"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Ypatybės parinktys"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Ypatybės kontekstas"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Ypatybės šaltinis (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree brūkšniniai kodai"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Suteikia vietinį brūkšninių kodų palaikymą"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "InvenTree bendraautoriai"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "Vidinis brūkšninio kodo formatas"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "Pasirinkite vidinį brūkšninio kodo formatą"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON brūkšniniai kodai (skaitomi žmogui)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "Trumpi brūkšniniai kodai (optimizuoti vietai)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "Trumpo brūkšninio kodo priešdėlis"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "Pritaikykite trumpųjų brūkšninių kodų priešdėlį - naudinga, kai naudojami keli InvenTree egzemplioriai"

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr "Automatinis užsakymų išdavimas"

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr "Automatiškai išduoti užsakymus nustatytą tikslinę dieną"

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr "Automatinis gamybos užsakymų išdavimas"

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr "Automatiškai išduoti gamybos užsakymus nustatytą tikslinę dieną"

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr "Automatinis pirkimo užsakymų išdavimas"

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr "Automatiškai išduoti pirkimo užsakymus nustatytą tikslinę dieną"

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr "Automatinis pardavimo užsakymų išdavimas"

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr "Automatiškai išduoti pardavimo užsakymus nustatytą tikslinę dieną"

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr "Automatinis grąžinimo užsakymų išdavimas"

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr "Automatiškai išduoti grąžinimo užsakymus nustatytą tikslinę dieną"

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr "Išduoti atgaline data datuotus užsakymus"

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr "Automatiškai išduoti užsakymus, kurių data yra atgalinė"

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr "Lygiai"

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr "Atsargų duomenys"

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr "Įtraukti detalių atsargų duomenis"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr "Kainodaros duomenys"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr "Įtraukti detalių kainodaros duomenis"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr "Tiekėjų duomenys"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr "Įtraukti tiekėjų duomenis"

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr "Gamintojų duomenys"

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr "Įtraukti gamintojų duomenis"

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr "Pakaitinių detalių duomenys"

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr "Įtraukti pakaitinių detalių duomenis"

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr "Parametrų duomenys"

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr "Įtraukti detalių parametrų duomenis"

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr "Daugiapakopis BOM eksportuotojas"

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr "Suteikia galimybę eksportuoti daugiapakopius BOM"

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr "BOM lygis"

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr "Pakaitalas {n}"

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr "Tiekėjas {n}"

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr "Tiekėjo {n} SKU"

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr "Tiekėjo {n} MPN"

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr "Gamintojas {n}"

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr "Gamintojo {n} MPN"

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr "InvenTree bendras eksportuotojas"

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr "Suteikia galimybę eksportuoti duomenis iš InvenTree"

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr "Detalių parametrų eksportuotojas"

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr "Eksportuoja detalių parametrų duomenis"

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Slack įeinančio webhook'o URL"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "URL, naudojamas pranešimams siųsti į Slack kanalą"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Atidaryti nuorodą"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree valiutų keitimo integracija"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Numatytoji valiutų keitimo integracija"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr "Detalių pranešimai"

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr "Informuoti vartotojus apie detalių pakeitimus"

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr "Siųsti pranešimus"

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr "Siųsti pranešimus apie detalių pakeitimus prenumeratoriams"

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr "Pranešimas apie pakeistą detalę"

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr "Detalei `{part.name}` įvyko `{part_action}` įvykis"

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF etikečių spausdintuvas"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Suteikia vidinį palaikymą PDF etikečių spausdinimui"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Derinimo režimas"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Įjungti derinimo režimą - grąžina neapdorotą HTML vietoje PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree automatinis etikečių spausdintuvas"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "Suteikia palaikymą spausdinimui naudojant įrenginį"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "paskutinį kartą naudota"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "Parinktys"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Etikečių lapo puslapio dydis"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Praleisti etiketes"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Spausdinant etikečių lapą, praleisti nurodytą etikečių skaičių"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Rėmelis"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "Spausdinti rėmelį aplink kiekvieną etiketę"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Gulsčias"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "Spausdinti etikečių lapą gulsčiu režimu"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr "Puslapio paraštė"

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr "Paraštė aplink puslapį (mm)"

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "InvenTree etikečių lapo spausdintuvas"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "Išdėsto kelias etiketes viename lape"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "Etiketė per didelė šiam puslapio dydžiui"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "Etikečių nesugeneruota"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "Tiekėjo integracija - DigiKey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Suteikia palaikymą DigiKey brūkšninių kodų nuskaitymui"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "Tiekėjas, veikiantis kaip 'DigiKey'"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "Tiekėjo integracija - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "Suteikia palaikymą LCSC brūkšninių kodų nuskaitymui"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "Tiekėjas, veikiantis kaip 'LCSC'"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "Tiekėjo integracija - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "Suteikia palaikymą Mouser brūkšninių kodų nuskaitymui"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "Tiekėjas, veikiantis kaip 'Mouser'"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "Tiekėjo integracija - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "Suteikia palaikymą TME brūkšninių kodų nuskaitymui"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "Tiekėjas, veikiantis kaip 'TME'"

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "Tik personalo vartotojai gali tvarkyti įskiepius"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "Įskiepių diegimas išjungtas"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "Įskiepis sėkmingai įdiegtas"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Įskiepis įdiegtas į {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "Įskiepis neaptiktas registre"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "Įskiepis nėra supakuotas įskiepis"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "Įskiepio paketo pavadinimas nerastas"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "Įskiepių šalinimas išjungtas"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "Įskiepis negali būti pašalintas, nes šiuo metu yra aktyvus"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr "Įskiepis neįdiegtas"

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr "Įskiepio diegimas nerastas"

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "Įskiepis sėkmingai pašalintas"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Įskiepio konfigūracija"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Įskiepių konfigūracijos"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Įskiepio raktas"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Įskiepio pavadinimas"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Paketų pavadinimas"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "Įdiegto paketo pavadinimas, jei įskiepis buvo įdiegtas per PIP"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Ar įskiepis yra aktyvus"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Pavyzdinis įskiepis"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Integruotas įskiepis"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr "Privalomas įskiepis"

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "Pakuotės įskiepis"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "Įskiepis"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Autorius nerastas"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Įskiepis '{p}' nesuderinamas su dabartine InvenTree versija {v}"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Įskiepiui reikalinga bent versija {v}"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Įskiepiui reikalinga ne aukštesnė nei versija {v}"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "Įjungti pirkimo užsakymus"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "Įjungti pirkimo užsakymų funkcionalumą InvenTree sąsajoje"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "API raktas"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Raktas, reikalingas pasiekti išorinį API"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Skaitmeninis"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "Skaitmeninė nuostata"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Pasirinkimo nuostata"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "Nuostata su keliais pasirinkimais"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Pavyzdinis valiutų keitimo įskiepis"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree bendraautoriai"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Įjungti detalių skydelius"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Įjungti pasirinktinius skydelius detalių rodiniuose"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Įjungti pirkimo užsakymų skydelius"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Įjungti pasirinktinius skydelius pirkimo užsakymų rodiniuose"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Įjungti klaidų turinčius skydelius"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Įjungti klaidų turinčius skydelius testavimui"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Įjungti dinaminį skydelį"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Įjungti dinaminius skydelius testavimui"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Detalės skydelis"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Sugedęs valdymo skydelio elementas"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Tai sugedęs valdymo skydelio elementas - jis nebus atvaizduotas!"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Pavyzdinis valdymo skydelio elementas"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Tai pavyzdinis valdymo skydelio elementas. Jis pateikia paprastą HTML turinį."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Kontekstinis valdymo skydelio elementas"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Administratoriaus valdymo skydelio elementas"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Tai tik administratoriui skirtas valdymo skydelio elementas."

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Pirminis failas"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Kelias iki pirminio failo administratoriaus integracijai"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Nebūtini kontekstiniai duomenys administratoriaus integracijai"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Šaltinio URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Paketo šaltinis - tai gali būti pasirinktinė registravimo sistema arba VCS kelias"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Įskiepio paketo pavadinimas – taip pat gali būti nurodyta versija"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versija"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Įskiepio versijos nurodymas. Palikite tuščią, jei norite naudoti naujausią versiją."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Patvirtinti įskiepio įdiegimą"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Šis veiksmas įdiegs įskiepį į esamą egzempliorių. Sistema bus perjungta į priežiūros režimą."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Įdiegimas nepatvirtintas"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Turi būti pateiktas paketo pavadinimas arba URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Pilnas perkrovimas"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Vykdyti pilną įskiepių registro perkrovimą"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Priverstinis perkrovimas"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Priverstinai perkrauti įskiepių registrą, net jei jis jau įkeltas"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Surinkti įskiepius"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Surinkti įskiepius ir įtraukti juos į registrą"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "Aktyvuoti įskiepį"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "Aktyvuoti šį įskiepį"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "Ištrinti konfigūraciją"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "Ištrinti įskiepio konfigūraciją iš duomenų bazės"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "Elementai"

#: report/api.py:114
msgid "Plugin not found"
msgstr "Įskiepis nerastas"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr "Įskiepis nepalaiko etikečių spausdinimo"

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr "Neleistini etiketės matmenys"

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr "Šablonui nepateikti galimi elementai"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Legal"

#: report/helpers.py:46
msgid "Letter"
msgstr "Letter"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr "Šablono failas tokiu pavadinimu jau egzistuoja"

#: report/models.py:217
msgid "Template name"
msgstr "Šablono pavadinimas"

#: report/models.py:223
msgid "Template description"
msgstr "Šablono aprašymas"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr "Versijos numeris (didinamas automatiškai)"

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr "Pridėti prie modelio spausdinant"

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Išsaugoti ataskaitą kaip priedą susietam modelio egzemplioriui spausdinimo metu"

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Failo vardo šablonas"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr "Šablonas failų vardų generavimui"

#: report/models.py:287
msgid "Template is enabled"
msgstr "Šablonas įjungtas"

#: report/models.py:294
msgid "Target model type for template"
msgstr "Šablonui skirtas modelio tipas"

#: report/models.py:314
msgid "Filters"
msgstr "Filtrai"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "Šablono užklausos filtrai (kableliais atskirtas key=value porų sąrašas)"

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "Šablono failas"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "Puslapio dydis PDF ataskaitoms"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "Rodyti ataskaitą gulsčiai"

#: report/models.py:393
msgid "Merge"
msgstr ""

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr "Ataskaita sugeneruota iš šablono {self.name}"

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr "Klaida generuojant ataskaitą"

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Plotis [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Etiketės plotis, nurodytas milimetrais"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Aukštis [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Etiketės aukštis, nurodytas milimetrais"

#: report/models.py:780
msgid "Error printing labels"
msgstr "Klaida spausdinant etiketes"

#: report/models.py:799
msgid "Snippet"
msgstr "Fragmentas"

#: report/models.py:800
msgid "Report snippet file"
msgstr "Ataskaitos fragmento failas"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Fragmento failo aprašymas"

#: report/models.py:825
msgid "Asset"
msgstr "Išteklius"

#: report/models.py:826
msgid "Report asset file"
msgstr "Ataskaitos ištekliaus failas"

#: report/models.py:833
msgid "Asset file description"
msgstr "Ištekliaus failo aprašymas"

#: report/serializers.py:96
msgid "Select report template"
msgstr "Pasirinkite ataskaitos šabloną"

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr "Elementų pirminių raktų sąrašas, įtrauktinas į ataskaitą"

#: report/serializers.py:137
msgid "Select label template"
msgstr "Pasirinkite etiketės šabloną"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr "Spausdinimo įskiepis"

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr "Pasirinkite įskiepį etikečių spausdinimui"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR kodas"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR kodas"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Komplektavimo žiniaraštis (BOM)"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Reikalingos medžiagos"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Detalės vaizdas"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Išduota"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Reikalinga objektui"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Išdavė"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Tiekėjas buvo ištrintas"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Vieneto kaina"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Papildomi eilutės įrašai"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Iš viso"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Serijos numeris"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Priskyrimai"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Partija"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Atsargų vietos elementai"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Atsargų elemento bandymo ataskaita"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Sumontuoti elementai"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Serija"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Bandymo rezultatai"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Bandymas"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Pavyko"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Nepavyko"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Nėra rezultato (privaloma)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Nėra rezultato"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Ištekliaus failas neegzistuoja"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Paveikslėlio failas nerastas"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "Žyma part_image reikalauja detalės (Part) egzemplioriaus"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "Žyma company_image reikalauja įmonės (Company) egzemplioriaus"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "Filtruoti pagal vietos gylį"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr "Filtruoti pagal aukščiausio lygio vietas"

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "Įtraukti sub-vietas į filtravimo rezultatus"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "Pirminė vieta"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "Filtruoti pagal pirminę vietą"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr "Detalės pavadinimas (neskiria didžiųjų ir mažųjų raidžių)"

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr "Detalės pavadinimas turi (neskiria didžiųjų ir mažųjų raidžių)"

#: stock/api.py:594
msgid "Part name (regex)"
msgstr "Detalės pavadinimas (reguliarioji išraiška)"

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr "Detalės IPN (neskiria didžiųjų ir mažųjų raidžių)"

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr "Detalės IPN turi (neskiria didžiųjų ir mažųjų raidžių)"

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr "Detalės IPN (reguliarioji išraiška)"

#: stock/api.py:623
msgid "Minimum stock"
msgstr "Mažiausias kiekis"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "Didžiausias kiekis"

#: stock/api.py:630
msgid "Status Code"
msgstr "Būsenos kodas"

#: stock/api.py:670
msgid "External Location"
msgstr "Išorinė vieta"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr "Sunaudota gamybos užsakyme"

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr "Sumontuota kitame atsargų elemente"

#: stock/api.py:868
msgid "Part Tree"
msgstr "Detalių medis"

#: stock/api.py:890
msgid "Updated before"
msgstr "Atnaujinta iki"

#: stock/api.py:894
msgid "Updated after"
msgstr "Atnaujinta po"

#: stock/api.py:898
msgid "Stocktake Before"
msgstr "Inventorizacija iki"

#: stock/api.py:902
msgid "Stocktake After"
msgstr "Inventorizacija po"

#: stock/api.py:907
msgid "Expiry date before"
msgstr "Galiojimo data iki"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "Galiojimo data po"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "Pasenusi"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "Reikalingas kiekis"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "Turi būti pateikta galiojanti detalė"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "Nurodyta tiekėjo detalė neegzistuoja"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Tiekėjo detalė turi nustatytą pakuotės dydį, bet nepažymėtas požymis use_pack_size"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Serijos numeriai negali būti pateikti detalei, kurios negalima sekti"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "Atsargų vietos tipas"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "Atsargų vietos tipai"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Numatytoji piktograma visoms vietoms, kurioms nepaskirta piktograma (neprivaloma)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Atsargų vieta"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Atsargų vietos"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Savininkas"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Pasirinkite savininką"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Atsargos negali būti tiesiogiai patalpintos į struktūrines atsargų vietas, bet gali būti patalpinti į jų sub-vietas."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Išorinė"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Tai yra išorinė atsargų vieta"

#: stock/models.py:233
msgid "Location type"
msgstr "Vietos tipas"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "Šios vietos atsargų vietos tipas"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Negalite padaryti šios atsargų vietos struktūrine, nes joje jau yra atsargų!"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr "Turi būti nurodyta detalė"

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Atsargos negali būti patalpintos į struktūrines atsargų vietas!"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Atsargų elementas negali būti sukurtas virtualioms detalėms"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Detalės tipas ('{self.supplier_part.part}') turi būti {self.part}"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "Elemento, turinčio serijos numerį, kiekis turi būti 1"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Serijos numeris negali būti nustatytas, jei kiekis didesnis nei 1"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "Elementas negali priklausyti pats sau"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "Elementas turi turėti surinkimo nuorodą, jei is_building=True"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "Surinkimo nuoroda nenurodo į tą pačią detalę"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Pirminis atsargų elementas"

#: stock/models.py:1028
msgid "Base part"
msgstr "Pagrindinė detalė"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Pasirinkite atitinkančią tiekėjo detalę šiam atsargų elementui"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Kur yra šis atsargų elementas?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "Pakuotė, kurioje laikomas šis atsargų elementas"

#: stock/models.py:1064
msgid "Installed In"
msgstr "Sumontuotas į"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "Ar šis elementas yra sumontuotas kitame elemente?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Šio elemento serijos numeris"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Šio atsargų elemento partijos kodas"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Atsargų kiekis"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Surinkimo šaltinis"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Surinkimas šiam atsargų elementui"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "Sunaudojo"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "Gamybos užsakymas, kuris sunaudojo šį atsargų elementą"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Pirkimo užsakymo šaltinis"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Pirkimo užsakymas šiam atsargų elementui"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Pardavimo užsakymo paskirtis"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Atsargų elemento galiojimo data. Po šios datos atsargos bus laikomos pasibaigusiomis"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Ištrinti išnaudojus"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Ištrinti šį atsargų elementą, kai atsargos bus išnaudotos"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Vieneto pirkimo kaina pirkimo metu"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "Konvertuota į detalę"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "Detalė nenustatyta kaip sekama"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "Kiekis turi būti sveikasis skaičius"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Kiekis negali viršyti galimų atsargų kiekio ({self.quantity})"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr "Serijos numeriai turi būti pateikti sąraše"

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "Kiekis nesutampa su serijos numeriais"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "Bandomasis šablonas neegzistuoja"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "Atsargų elementas buvo priskirtas pardavimo užsakymui"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "Atsargų elementas sumontuotas kitame elemente"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "Atsargų elementas turi kitų elementų"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "Atsargų elementas buvo priskirtas klientui"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "Atsargų elementas šiuo metu gaminamas"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Su serijos numeriais pažymėtų atsargų sujungti negalima"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Pasikartojantys atsargų elementai"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "Atsargų elementai turi būti susiję su ta pačia detale"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "Atsargų elementai turi būti susiję su ta pačia tiekėjo detale"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "Atsargų būsenos kodai turi sutapti"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Atsargų elemento negalima perkelti, nes jo nėra sandėlyje"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr "Atsargų elemento sekimas"

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Įrašo pastabos"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr "Atsargų elemento bandymo rezultatas"

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Šiam bandymui turi būti pateikta reikšmė"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "Šiam bandymui turi būti įkeltas priedas"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr "Netinkama reikšmė šiam bandymui"

#: stock/models.py:2951
msgid "Test result"
msgstr "Bandymo rezultatas"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Bandymo išvesties reikšmė"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Bandymo rezultato priedas"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Bandymo pastabos"

#: stock/models.py:2978
msgid "Test station"
msgstr "Bandymų stotis"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "Bandymų stoties identifikatorius, kurioje atliktas bandymas"

#: stock/models.py:2985
msgid "Started"
msgstr "Pradėta"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "Bandymo pradžios laiko žyma"

#: stock/models.py:2992
msgid "Finished"
msgstr "Pabaigta"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "Bandymo pabaigos laiko žyma"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "Sugeneruotas partijos kodas"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "Pasirinkite gamybos užsakymą"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "Pasirinkite atsargų elementą partijos kodui sugeneruoti"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "Pasirinkite vietą partijos kodui sugeneruoti"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "Pasirinkite detalę partijos kodui sugeneruoti"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "Pasirinkite pirkimo užsakymą"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "Įveskite kiekį partijos kodui"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "Sugeneruotas serijos numeris"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "Pasirinkite detalę serijos numeriui sugeneruoti"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "Kiekis serijos numerių, kuriuos reikia sugeneruoti"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "Bandymo šablonas šiam rezultatui"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "Turi būti pateiktas šablono ID arba bandymo pavadinimas"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "Bandymo pabaigos laikas negali būti ankstesnis nei pradžios laikas"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Pirminis elementas"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr "Pirminis atsargų elementas"

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Naudoti pakuotės dydį pridedant: nurodytas kiekis yra pakuočių skaičius"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Įveskite serijos numerius naujiems elementams"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "Tiekėjo detalės numeris"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "Nebegaliojantis"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Antriniai elementai"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr "Sekami elementai"

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Šio atsargų elemento pirkimo kaina, vienetui arba pakuotei"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Įveskite atsargų elementų, kuriuos reikia serializuoti, skaičių"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Kiekis negali viršyti galimų atsargų kiekio ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Paskirties atsargų vieta"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Šiai detalei negali būti priskirti serijos numeriai"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "Serijos numeriai jau egzistuoja"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Pasirinkite atsargų elementą montavimui"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "Montuojamas kiekis"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "Įveskite montuojamų elementų kiekį"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "Pridėkite operacijos pastabą (neprivaloma)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "Montuojamas kiekis turi būti bent 1"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "Atsargų elementas nepasiekiamas"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "Pasirinktos detalės nėra komplektavimo žiniaraštyje"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "Montuojamas kiekis negali viršyti turimo kiekio"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Paskirties vieta išmontuotam elementui"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Pasirinkite detalę, į kurią konvertuoti atsargų elementą"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "Pasirinkta detalė netinkama konvertavimui"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Negalima konvertuoti atsargų elemento, kuriam priskirta tiekėjo detalė"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "Atsargų elemento būsenos kodas"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "Pasirinkite atsargų elementus būsenai pakeisti"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "Nepasirinkti jokie atsargų elementai"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Sub-vietos"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "Pirminė atsargų vieta"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "Detalė turi būti parduodama"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "Elementas priskirtas pardavimo užsakymui"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "Elementas priskirtas gamybos užsakymui"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Klientas, kuriam priskiriami atsargų elementai"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "Pasirinkta įmonė nėra klientas"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Atsargų priskyrimo pastabos"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "Turi būti pateiktas atsargų elementų sąrašas"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Atsargų sujungimo pastabos"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Leisti skirtingus tiekėjus"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Leisti sujungti atsargų elementus su skirtingomis tiekėjų detalėmis"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Leisti skirtingas būsenas"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Leisti sujungti atsargų elementus su skirtingais būsenos kodais"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Turi būti pateikti bent du atsargų elementai"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "Be pakeitimų"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Atsargų elemento pirminio rakto reikšmė"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr "Atsargų elemento nėra sandėlyje"

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Atsargų operacijos pastabos"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr "Kitas serijos numeris"

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr "Ankstesnis serijos numeris"

#: stock/status_codes.py:11
msgid "OK"
msgstr "Gerai"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Reikia dėmesio"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Sugadinta"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Sunaikinta"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Atmesta"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "Karantinuota"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Senesnis atsargų sekimo įrašas"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Atsargų elementas sukurtas"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Redaguotas atsargų elementas"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Priskirtas serijos numeris"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Atsargos suskaičiuotos"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Atsargos pridėtos rankiniu būdu"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Atsargos pašalintos rankiniu būdu"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Vieta pakeista"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Atsargos atnaujintos"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "Sumontuota į surinkimą"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Pašalinta iš surinkimo"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Sumontuotas komponentas"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Pašalintas komponentas"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Atskirtas nuo pirminio elemento"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Atskirtas antrinis elementas"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Sujungti atsargų elementai"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "Konvertuota į variantą"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "Sukurtas gamybos užsakymo rezultatas"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Gamybos užsakymo rezultatas užbaigtas"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "Gamybos užsakymo rezultatas atmestas"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Sunaudota gamybos užsakyme"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Išsiųsta pagal pardavimo užsakymą"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Gauta pagal pirkimo užsakymą"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Grąžinta pagal grąžinimo užsakymą"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Išsiųsta klientui"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Grąžinta iš kliento"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Leidimas nesuteiktas"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Jūs neturite leidimo peržiūrėti šio puslapio."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Autentifikacijos klaida"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Jūs buvote atjungtas nuo InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Puslapis nerastas"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "Prašomas puslapis neegzistuoja"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Vidinė serverio klaida"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s serveris sukėlė vidinę klaidą"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Daugiau informacijos rasite klaidų žurnale administravimo sąsajoje"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Svetainė tvarkoma"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Šiuo metu svetainė tvarkoma ir netrukus vėl bus pasiekiama!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Reikia paleisti serverį iš naujo"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Buvo pakeista konfigūracija, todėl reikia paleisti serverį iš naujo"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Norėdami gauti daugiau informacijos, kreipkitės į sistemos administratorių"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Laukiančios duomenų bazės migracijos"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Yra laukiančių duomenų bazės migracijų, kurioms reikia dėmesio"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Spustelėkite šią nuorodą, norėdami peržiūrėti šį užsakymą"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Šiam gamybos užsakymui reikia atsargų"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Gamybos užsakymas %(build)s - surenkama %(quantity)s × %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Spustelėkite šią nuorodą, norėdami peržiūrėti šį gamybos užsakymą"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Šioms detalėms trūksta reikalingų atsargų"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Reikalingas kiekis"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Jūs gavote šį el. laišką, nes esate užsiprenumeravęs(-usi) pranešimus apie šią detalę"

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Spustelėkite šią nuorodą, norėdami peržiūrėti šią detalę"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimalus kiekis"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr "Jūs gavote šį el. laišką, nes esate užsiprenumeravęs(-usi) pranešimus apie šią detalę arba kategoriją, kuriai ji priklauso "

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Vartotojai"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Pasirinkite, kurie vartotojai priskirti šiai grupei"

#: users/admin.py:137
msgid "Personal info"
msgstr "Asmeninė informacija"

#: users/admin.py:139
msgid "Permissions"
msgstr "Leidimai"

#: users/admin.py:142
msgid "Important dates"
msgstr "Svarbios datos"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Raktas buvo atšauktas"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Raktas nebegalioja"

#: users/models.py:100
msgid "API Token"
msgstr "API prieigos raktas"

#: users/models.py:101
msgid "API Tokens"
msgstr "API prieigos raktai"

#: users/models.py:137
msgid "Token Name"
msgstr "Rakto pavadinimas"

#: users/models.py:138
msgid "Custom token name"
msgstr "Pasirinktas rakto pavadinimas"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Rakto galiojimo data"

#: users/models.py:152
msgid "Last Seen"
msgstr "Paskutinį kartą naudotas"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Paskutinis rakto naudojimo laikas"

#: users/models.py:157
msgid "Revoked"
msgstr "Atšauktas"

#: users/models.py:235
msgid "Permission set"
msgstr "Leidimų rinkinys"

#: users/models.py:244
msgid "Group"
msgstr "Grupė"

#: users/models.py:248
msgid "View"
msgstr "Peržiūra"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Leidimas peržiūrėti objektus"

#: users/models.py:252
msgid "Add"
msgstr "Pridėti"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Leidimas pridėti objektus"

#: users/models.py:256
msgid "Change"
msgstr "Keisti"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Leidimas redaguoti objektus"

#: users/models.py:262
msgid "Delete"
msgstr "Ištrinti"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Leidimas ištrinti objektus"

#: users/models.py:501
msgid "Bot"
msgstr "Botas"

#: users/models.py:502
msgid "Internal"
msgstr "Vidinis"

#: users/models.py:504
msgid "Guest"
msgstr "Svečias"

#: users/models.py:513
msgid "Language"
msgstr "Kalba"

#: users/models.py:514
msgid "Preferred language for the user"
msgstr "Pageidaujama vartotojo kalba"

#: users/models.py:519
msgid "Theme"
msgstr "Tema"

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr "Žiniatinklio sąsajos nustatymai (JSON formatu) - neredaguoti rankiniu būdu!"

#: users/models.py:525
msgid "Widgets"
msgstr "Valdikliai"

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr "Prietaisų skydelio valdiklių nustatymai (JSON formatu) - neredaguoti rankiniu būdu!"

#: users/models.py:534
msgid "Display Name"
msgstr "Rodomas vardas"

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr "Pasirinktas vartotojo rodomas vardas"

#: users/models.py:541
msgid "Position"
msgstr "Pareigos"

#: users/models.py:542
msgid "Main job title or position"
msgstr "Pagrindinės pareigos arba darbo pavadinimas"

#: users/models.py:549
msgid "User status message"
msgstr "Vartotojo būsenos žinutė"

#: users/models.py:556
msgid "User location information"
msgstr "Vartotojo buvimo vietos informacija"

#: users/models.py:561
msgid "User is actively using the system"
msgstr "Vartotojas aktyviai naudoja sistemą"

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr "Pageidaujama vartotojo kontaktinė informacija"

#: users/models.py:574
msgid "User Type"
msgstr "Vartotojo tipas"

#: users/models.py:575
msgid "Which type of user is this?"
msgstr "Kokio tipo tai vartotojas?"

#: users/models.py:581
msgid "Organisation"
msgstr "Organizacija"

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr "Pagrindinė vartotojo organizacija / priklausomybė"

#: users/models.py:590
msgid "Primary Group"
msgstr "Pagrindinė grupė"

#: users/models.py:591
msgid "Primary group for the user"
msgstr "Pagrindinė vartotojo grupė"

#: users/ruleset.py:26
msgid "Admin"
msgstr "Administratorius"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Pirkimo užsakymai"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Pardavimo užsakymai"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Grąžinimo užsakymai"

#: users/serializers.py:196
msgid "Username"
msgstr "Vartotojo vardas"

#: users/serializers.py:199
msgid "First Name"
msgstr "Vardas"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Vartotojo vardas"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Pavardė"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Vartotojo pavardė"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "Vartotojo el. pašto adresas"

#: users/serializers.py:326
msgid "Staff"
msgstr "Personalas"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Ar šis vartotojas turi personalo leidimus"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Supervartotojas"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "Ar šis vartotojas yra supervartotojas"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Ar ši vartotojo paskyra yra aktyvi"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr "Tik supervartotojas gali keisti šį lauką"

#: users/serializers.py:376
msgid "Password"
msgstr ""

#: users/serializers.py:377
msgid "Password for the user"
msgstr ""

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr "Tik personalo vartotojai gali kurti naujus vartotojus"

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr "Neturite leidimo kurti vartotojų"

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Jūsų paskyra sukurta."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Prisijungimui naudokite slaptažodžio atstatymo funkciją"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Sveiki atvykę į InvenTree"

