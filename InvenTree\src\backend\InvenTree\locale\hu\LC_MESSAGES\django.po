msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Hungarian\n"
"Language: hu_HU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: hu\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr ""

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "API funkciót nem találom"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr ""

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Érvénytelen szűrők vannak megadva"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:493
msgid "No data provided"
msgstr ""

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "Nincs jogosultságod az adatok megtekintéséhez"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "Email (újra)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "Email cím megerősítés"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Mindig ugyanazt az email címet kell beírni."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "A megadott elsődleges email cím nem valós."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "A megadott email domain nincs jóváhagyva."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Érvénytelen mennyiségi egység ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Nincs érték megadva"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "{original} átváltása {unit}-ra sikertelen"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Nem megfelelő mennyiség"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "A hiba részleteit megtalálod az admin panelen"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Dátum megadása"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Érvénytelen decimális érték"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Megjegyzések"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "A(z) '{name}' érték nem a szükséges minta szerinti"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "A megadott érték nem felel meg a szükséges mintának: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr ""

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Üres sorozatszám"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Duplikált sorozatszám"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Érvénytelen csoport: {group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Csoport tartomány {group} több mint az engedélyezett ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Nem található sorozatszám"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr ""

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "HTML tag-ek eltávolítása ebből az értékből"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Csatlakozási hiba"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "A kiszolgáló érvénytelen státuszkóddal válaszolt"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Kivétel történt"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "A kiszolgáló érvénytelen Content-Length értéket adott"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "A kép mérete túl nagy"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "A kép letöltés meghaladja a maximális méretet"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "A kiszolgáló üres választ adott"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "A megadott URL nem egy érvényes kép fájl"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arab"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bolgár"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Cseh"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Dán"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Német"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Görög"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Angol"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spanyol"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spanyol (Mexikói)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Észt"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Fárszi/Perzsa"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finn"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Francia"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Héber"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Magyar"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Olasz"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japán"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Koreai"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Litván"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Litván"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Holland"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norvég"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Lengyel"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugál"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugál (Brazíliai)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Román"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Orosz"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Szlovák"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Szlovén"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Szerb"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Svéd"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Tháj"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Török"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrán"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnámi"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Kínai (egyszerűsített)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Kínai (Hagyományos)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Bejelentkezés az appba"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "Email"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr "Hiba a plugin validálása közben"

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "A meta adatnak egy python dict objektumnak kell lennie"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Plugin meta adatok"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "JSON meta adat mező, külső pluginok számára"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Helytelenül formázott minta"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Ismeretlen formátum kulcs lett megadva"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Hiányzó formátum kulcs"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "Az azonosító mező nem lehet üres"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "Az azonosítónak egyeznie kell a mintával"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "Azonosító szám túl nagy"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Érvénytelen választás"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Név"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Leírás"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Leírás (opcionális)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Elérési út"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Duplikált nevek nem lehetnek ugyanazon szülő alatt"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Markdown megjegyzések (opcionális)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Vonalkód adat"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Harmadik féltől származó vonalkód adat"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Vonalkód hash"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Egyedi vonalkód hash"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Létező vonalkód"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Feladat hiba"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Kiszolgálóhiba"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "A kiszolgáló egy hibaüzenetet rögzített."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Érvényes számnak kell lennie"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Pénznem"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Válassz pénznemet a lehetőségek közül"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Érvénytelen érték"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Távoli kép"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "A távoli kép URL-je"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Képek letöltése távoli URL-ről nem engedélyezett"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr ""

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr ""

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr ""

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Érvénytelen fizikai mértékegység"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "Érvénytelen pénznem kód"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Rendelés állapota"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Szülő gyártás"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Változatokkal együtt"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Alkatrész"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Kategória"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Szülő Gyártás"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Hozzám rendelt"

#: build/api.py:154
msgid "Assigned To"
msgstr "Hozzárendelve"

#: build/api.py:189
msgid "Created before"
msgstr "Ez előtt létrehozva"

#: build/api.py:193
msgid "Created after"
msgstr "Létrehozva ez után"

#: build/api.py:197
msgid "Has start date"
msgstr ""

#: build/api.py:205
msgid "Start date before"
msgstr ""

#: build/api.py:209
msgid "Start date after"
msgstr ""

#: build/api.py:213
msgid "Has target date"
msgstr ""

#: build/api.py:221
msgid "Target date before"
msgstr ""

#: build/api.py:225
msgid "Target date after"
msgstr ""

#: build/api.py:229
msgid "Completed before"
msgstr ""

#: build/api.py:233
msgid "Completed after"
msgstr ""

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr "Ettől a dátumtól"

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr "Eddig a dátumig"

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Fa kihagyása"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "A gyártást be kell fejezni a törlés előtt"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Fogyóeszköz"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Opcionális"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Gyártmány"

#: build/api.py:450
msgid "Tracked"
msgstr "Követett"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Ellenőrizhető"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr ""

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Lefoglalva"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Elérhető"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Gyártási utasítás"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Hely"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Gyártási utasítások"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "Az alkatrészjegyzék még nincs jóváhagyva"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Nem lehet inaktív alkatrészre Gyártást kezdeményezni"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Nem lehet lezáratlan alkatrészre Gyártást kezdeményezni"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr ""

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Meg kell adni felelős felhasználót vagy csoportot"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "Gyártási rendelés alkatrész nem változtatható"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Gyártási utasítás azonosító"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Azonosító"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Gyártás rövid leírása (opcionális)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Gyártás, amihez ez a gyártás hozzá van rendelve"

#: build/models.py:273
msgid "Select part to build"
msgstr "Válassz alkatrészt a gyártáshoz"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Vevői rendelés azonosító"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Vevői rendelés amihez ez a gyártás hozzá van rendelve"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Forrás hely"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Válassz helyet ahonnan készletet vegyünk el ehhez a gyártáshoz (hagyd üresen ha bárhonnan)"

#: build/models.py:300
msgid "External Build"
msgstr "Külső gyártás"

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr ""

#: build/models.py:306
msgid "Destination Location"
msgstr "Cél hely"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Válassz helyet ahol a kész tételek tárolva lesznek"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Gyártási mennyiség"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Gyártandó készlet tételek száma"

#: build/models.py:322
msgid "Completed items"
msgstr "Kész tételek"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Elkészült készlet tételek száma"

#: build/models.py:328
msgid "Build Status"
msgstr "Gyártási állapot"

#: build/models.py:333
msgid "Build status code"
msgstr "Gyártás státusz kód"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Batch kód"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Batch kód a gyártás kimenetéhez"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Létrehozás dátuma"

#: build/models.py:356
msgid "Build start date"
msgstr ""

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:363
msgid "Target completion date"
msgstr "Befejezés cél dátuma"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Cél dátum a gyártás befejezéséhez. Ez után késettnek számít majd."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Befejezés dátuma"

#: build/models.py:378
msgid "completed by"
msgstr "elkészítette"

#: build/models.py:387
msgid "Issued by"
msgstr "Indította"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Felhasználó aki ezt a gyártási utasítást kiállította"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Felelős"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Felhasználó vagy csoport aki felelős ezért a gyártásért"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Külső link"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Link külső URL-re"

#: build/models.py:410
msgid "Build Priority"
msgstr "Priorítás"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Gyártási utasítás priorítása"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Projektszám"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Projekt kód a gyártáshoz"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr ""

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr ""

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "A gyártási foglalások teljesítése háttérfeladat elvégzése nem sikerült"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "A {build} gyártási utasítás elkészült"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "Gyártási utasítás elkészült"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Egyedi követésre jelölt alkatrészeknél kötelező sorozatszámot megadni"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Nincs gyártási kimenet megadva"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "Gyártási kimenet már kész"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "Gyártási kimenet nem egyezik a gyártási utasítással"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "Mennyiségnek nullánál többnek kell lennie"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "A mennyiség nem lehet több mint a gyártási mennyiség"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr ""

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "A {serial} gyártási kimenet nem felelt meg az összes kötelező teszten"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Gyártási Rendelés Sor Tétel"

#: build/models.py:1602
msgid "Build object"
msgstr "Gyártás objektum"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Mennyiség"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Gyártáshoz szükséges mennyiség"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Gyártási tételnek meg kell adnia a gyártási kimenetet, mivel a fő darab egyedi követésre kötelezett"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "A lefoglalt mennyiség ({q}) nem lépheti túl a szabad készletet ({a})"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "Készlet túlfoglalva"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "Lefoglalt mennyiségnek nullánál többnek kell lennie"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "Egyedi követésre kötelezett tételeknél a menyiség 1 kell legyen"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "A készlet tétel nem egyezik az alkatrészjegyzékkel"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Készlet tétel"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Forrás készlet tétel"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Készlet mennyiség amit foglaljunk a gyártáshoz"

#: build/models.py:1924
msgid "Install into"
msgstr "Beépítés ebbe"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Cél készlet tétel"

#: build/serializers.py:115
msgid "Build Level"
msgstr "Gyártási Szint"

#: build/serializers.py:124
msgid "Part Name"
msgstr "Alkatrész neve"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Projekt kód címke"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Gyártás kimenet"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "Gyártási kimenet nem egyezik a szülő gyártással"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "Kimeneti alkatrész nem egyezik a gyártási utasításban lévő alkatrésszel"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Ez a gyártási kimenet már elkészült"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Ez a gyártási kimenet nincs teljesen lefoglalva"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Add meg a mennyiséget a gyártás kimenetéhez"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Egész számú mennyiség szükséges az egyedi követésre kötelezett alkatrészeknél"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Egész számú mennyiség szükséges, mivel az alkatrészjegyzék egyedi követésre kötelezett alkatrészeket tartalmaz"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Sorozatszámok"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Add meg a sorozatszámokat a gyártás kimenetéhez"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Legyártott készlet helye"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Sorozatszámok automatikus hozzárendelése"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Szükséges tételek automatikus hozzárendelése a megfelelő sorozatszámokkal"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "A következő sorozatszámok már léteznek vagy nem megfelelőek"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "A gyártási kimenetek listáját meg kell adni"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Selejtezet gyártási kimenetek helye"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Foglalások törlése"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Selejtezett kimenetek foglalásainak felszabadítása"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Selejtezés oka"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "A kész gyártási kimenetek helye"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Hiányos foglalás elfogadása"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Kimenetek befejezése akkor is ha a készlet nem\n"
"lett teljesen lefoglalva"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Lefoglalt készlet felhasználása"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Az összes ehhez a gyártáshoz lefoglalt készlet felhasználása"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Befejezetlen kimenetek törlése"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "A nem befejezett gyártási kimenetek törlése"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Nem engedélyezett"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Gyártásban fel lett használva"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Foglalás felszabadítása a készre jelentés előtt"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr "Túlfoglalt készlet"

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Hogyan kezeljük az gyártáshoz rendelt egyéb készletet"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Pár készlet tétel túl lett foglalva"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Kiosztatlanok elfogadása"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Fogadd el hogy a készlet tételek nincsenek teljesen lefoglalva ehhez a gyártási utastáshoz"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "A szükséges készlet nem lett teljesen lefoglalva"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Befejezetlenek elfogadása"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Fogadd el hogy a szükséges számú gyártási kimenet nem lett elérve"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "Szükséges gyártási mennyiség nem lett elérve"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "A Gyártásnak nyitott leszármazott Gyártása van"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "A Gyártásnak folyamatban kell lennie"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "A gyártási utasítás befejezetlen kimeneteket tartalmaz"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Gyártás sor"

#: build/serializers.py:877
msgid "Build output"
msgstr "Gyártás kimenet"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "A gyártási kimenetnek ugyanarra a gyártásra kell mutatnia"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Gyártás sor tétel"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part ugyanarra az alkatrészre kell mutasson mint a gyártási utasítás"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "A tételnek kell legyen készlete"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Rendelkezésre álló mennyiség ({q}) túllépve"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Gyártási kimenetet meg kell adni a követésre kötelezett alkatrészek lefoglalásához"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Gyártási kimenetet nem lehet megadni a követésre kötelezett alkatrészek lefoglalásához"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "A lefoglalandó tételeket meg kell adni"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Készlet hely ahonnan az alkatrészek származnak (hagyd üresen ha bárhonnan)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Hely kizárása"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Készlet tételek kizárása erről a kiválasztott helyről"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Felcserélhető készlet"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "A különböző helyeken lévő készlet egyenrangúan felhasználható"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Készlet helyettesítés"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Helyettesítő alkatrészek foglalásának engedélyezése"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Opcionális tételek"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Opcionális tételek lefoglalása a gyártáshoz"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Nem sikerült az automatikus lefoglalás feladatot elindítani"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "Alkatrészjegyzék Hivatkozás"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "Alkatrészjegyzék Cikk Azonosító"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "Alkatrészjegyzék Alkatrész Név"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "Gyártás"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Beszállítói alkatrész"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Lefoglalt mennyiség"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Gyártási Hivatkozás"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Alkatrész kategória Neve"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Követésre kötelezett"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Örökölt"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Változatok"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "Alkatrészjegyzék tétel"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "Rendelve"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "Gyártásban"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr ""

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Külső raktárkészlet"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Elérhető készlet"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Elérhető Helyettesítő Készlet"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Elérhető Készlet Változatokból"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Függőben"

#: build/status_codes.py:12
msgid "Production"
msgstr "Folyamatban"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "Felfüggesztve"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Törölve"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Kész"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "A gyártási utasításhoz készlet szükséges"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr ""

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Késésben lévő gyártás"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "A {bo} gyártás most már késésben van"

#: common/api.py:688
msgid "Is Link"
msgstr "Ez egy hivatkozás"

#: common/api.py:696
msgid "Is File"
msgstr "Ez egy állomány"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "A felhasználó nem jogosult ezen mellékletek törlésére"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "A felhasználó nem jogosult ezen melléklet törlésére"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Érvénytelen valuta kód"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Létező valuta kód"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Hiányzó érvényes valuta kód"

#: common/currency.py:146
msgid "No plugin"
msgstr "Nincsen plugin"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Frissítve"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Legutóbbi frissítés időpontja"

#: common/models.py:138
msgid "Update By"
msgstr "Frissítette"

#: common/models.py:139
msgid "User who last updated this object"
msgstr ""

#: common/models.py:164
msgid "Unique project code"
msgstr "Egyedi projektszám"

#: common/models.py:171
msgid "Project description"
msgstr "Projekt leírása"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "A projektért felelős felhasználó vagy csoport"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "Beállítási kulcs"

#: common/models.py:780
msgid "Settings value"
msgstr "Beállítás értéke"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "A kiválasztott érték nem egy érvényes lehetőség"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Az érték bináris kell legyen"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Az érték egész szám kell legyen"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:914
msgid "Key string must be unique"
msgstr "Kulcs string egyedi kell legyen"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Felhasználó"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Ársáv mennyiség"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Ár"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Egységár egy meghatározott mennyiség esetén"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Végpont"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Végpont ahol ez a webhook érkezik"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Webhook neve"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Aktív"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Aktív-e ez a webhook"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Token"

#: common/models.py:1437
msgid "Token for access"
msgstr "Token a hozzáféréshez"

#: common/models.py:1445
msgid "Secret"
msgstr "Titok"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Megosztott titok a HMAC-hoz"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "Üzenet azonosító"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Egyedi azonosító ehhez az üzenethez"

#: common/models.py:1563
msgid "Host"
msgstr "Kiszolgáló"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Kiszolgáló ahonnan ez az üzenet érkezett"

#: common/models.py:1572
msgid "Header"
msgstr "Fejléc"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Üzenet fejléce"

#: common/models.py:1580
msgid "Body"
msgstr "Törzs"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Üzenet törzse"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Végpont amin ez az üzenet érkezett"

#: common/models.py:1596
msgid "Worked on"
msgstr "Dolgozott rajta"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "Befejeződött a munka ezzel az üzenettel?"

#: common/models.py:1723
msgid "Id"
msgstr "Azonosító"

#: common/models.py:1725
msgid "Title"
msgstr "Cím"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Link"

#: common/models.py:1729
msgid "Published"
msgstr "Közzétéve"

#: common/models.py:1731
msgid "Author"
msgstr "Szerző"

#: common/models.py:1733
msgid "Summary"
msgstr "Összefoglaló"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Elolvasva"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Elolvasva?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Kép"

#: common/models.py:1753
msgid "Image file"
msgstr "Képfájl"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "A képhez tartozó model típus"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "A képhez tartozó model azonosító"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Egyedi mértékegység"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "A mértékegység szimbólumának egyedinek kell lennie"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "A mértékegységnek valós azonosítónak kell lennie"

#: common/models.py:1843
msgid "Unit name"
msgstr "Egység neve"

#: common/models.py:1850
msgid "Symbol"
msgstr "Szimbólum"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Opcionális mértékegység szimbólum"

#: common/models.py:1857
msgid "Definition"
msgstr "Definíció"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Mértékegység definíció"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Melléklet"

#: common/models.py:1933
msgid "Missing file"
msgstr "Hiányzó fájl"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Hiányzó külső link"

#: common/models.py:1971
msgid "Model type"
msgstr "Modell típusa"

#: common/models.py:1972
msgid "Target model type for image"
msgstr ""

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Válaszd ki a mellekelni kívánt fájlt"

#: common/models.py:1996
msgid "Comment"
msgstr "Megjegyzés"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Melléklet megjegyzés"

#: common/models.py:2013
msgid "Upload date"
msgstr "Feltöltés dátuma"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "A fájl feltöltésének dátuma"

#: common/models.py:2018
msgid "File size"
msgstr "Fájl mérete"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Fájlméret bájtban"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "A melléklet model típusa érvénytelen"

#: common/models.py:2077
msgid "Custom State"
msgstr "Egyedi Állapot"

#: common/models.py:2078
msgid "Custom States"
msgstr "Egyedi Állapotok"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "Hivatkozott Állapot Készlet"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "Az az Állapot készlet, melyet ez az egyedi állapot kibővít"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Logikai kulcs"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Az állapot logikai kulcsa amely megegyezik az üzleti logika egyedi állapotával"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Érték"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2102
msgid "Name of the state"
msgstr "Az állapot neve"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Címke"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "A felületen megjelenített címke"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Szín"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "A felöleten megjelenő szín"

#: common/models.py:2128
msgid "Model"
msgstr "Model"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "A Model amihez ez az állapot tartozik"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "Modelt választani kötelező"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "Kulcsot választani kötelező"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "Logikai kulcsot választani kötelező"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "A kulcs és a logikai kulcs nem lehet azonos"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "A kulcsnak eltérőnek kell lennie a hivatkozott állapotok logikai kulcsaitól"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "A logikai kulcsnak szerepelnie kell a hivatkozott állapotok logikai kulcsai közt"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr ""

#: common/models.py:2222
msgid "Selection Lists"
msgstr ""

#: common/models.py:2227
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2234
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Lezárt"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2255
msgid "Source Plugin"
msgstr "Forrás plugin"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2261
msgid "Source String"
msgstr "Forrás szöveg"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2271
msgid "Default Entry"
msgstr "Alapértelmezett bejegyzés"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Létrehozva"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2283
msgid "Last Updated"
msgstr "Utoljára módosítva"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2318
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2319
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "Vonalkód beolvasás"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Adat"

#: common/models.py:2377
msgid "Barcode data"
msgstr "Vonalkód adat"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr ""

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Időbélyeg"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr ""

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Kontextus"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2415
msgid "Response"
msgstr "Válasz"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Eredmény"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr ""

#: common/models.py:2505
msgid "An error occurred"
msgstr "Hiba történt"

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr ""

#: common/models.py:2573
msgid "Email Message"
msgstr "E-mail üzenet"

#: common/models.py:2574
msgid "Email Messages"
msgstr "E-mail üzenetek"

#: common/models.py:2581
msgid "Announced"
msgstr "Bejelentve"

#: common/models.py:2583
msgid "Sent"
msgstr "Elküldve"

#: common/models.py:2584
msgid "Failed"
msgstr "Megbukott"

#: common/models.py:2587
msgid "Delivered"
msgstr "Kiszállítva"

#: common/models.py:2595
msgid "Confirmed"
msgstr "Megerősítve"

#: common/models.py:2601
msgid "Inbound"
msgstr "Bejövő"

#: common/models.py:2602
msgid "Outbound"
msgstr "Kimenő"

#: common/models.py:2607
msgid "No Reply"
msgstr "Nincs válasz"

#: common/models.py:2608
msgid "Track Delivery"
msgstr "Kiszállítás követése"

#: common/models.py:2609
msgid "Track Read"
msgstr ""

#: common/models.py:2610
msgid "Track Click"
msgstr "Kattintások nyomkövetése"

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr "Globális ID"

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr ""

#: common/models.py:2633
msgid "Thread ID"
msgstr "Szál ID"

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr ""

#: common/models.py:2644
msgid "Thread"
msgstr "Szál"

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr ""

#: common/models.py:2661
msgid "Prioriy"
msgstr "Prioritás"

#: common/models.py:2703
msgid "Email Thread"
msgstr "Email szál"

#: common/models.py:2704
msgid "Email Threads"
msgstr "Email szálak"

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Kulcs"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr ""

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr ""

#: common/models.py:2724
msgid "Started Internal"
msgstr ""

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr ""

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr ""

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr ""

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Új {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Egy új megrendelés létrehozva, és hozzád rendelve"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} megszakítva"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Egy hozzád rendelt megrendelés megszakítva"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Készlet érkezett"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Készlet érkezett egy beszerzési megrendeléshez"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Készlet érkezett vissza egy visszavétel miatt"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr ""

#: common/serializers.py:147
msgid "Override"
msgstr "Felülbírálás"

#: common/serializers.py:486
msgid "Is Running"
msgstr "Folyamatban"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Folyamatban lévő feladatok"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Ütemezett Feladatok"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Hibás feladatok"

#: common/serializers.py:519
msgid "Task ID"
msgstr "Feladat ID"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "Egyedi feladat ID"

#: common/serializers.py:521
msgid "Lock"
msgstr "Zárol"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Zárolási idő"

#: common/serializers.py:523
msgid "Task name"
msgstr "Feladat neve"

#: common/serializers.py:525
msgid "Function"
msgstr "Funkció"

#: common/serializers.py:525
msgid "Function name"
msgstr "Funkció neve"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Paraméterek"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Feladat paraméterei"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Kulcsszó paraméterek"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Feladat kulcsszó paraméterek"

#: common/serializers.py:640
msgid "Filename"
msgstr "Fájlnév"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Modell típusa"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "A felhasználónak nincs joga létrehozni vagy módosítani ehhez a modelhez tartozó mellékleteket"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "Nincs csoport"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "A site URL blokkolva van a konfigurációban"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Újraindítás szükséges"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "Egy olyan beállítás megváltozott ami a kiszolgáló újraindítását igényli"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Függőben levő migrációk"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Függőben levő adatbázis migrációk"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr ""

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr ""

#: common/setting/system.py:193
msgid "Instance ID"
msgstr "Példány azonosító"

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:199
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Kiszolgáló példány neve"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "String leíró a kiszolgáló példányhoz"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Példány név használata"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Példány név használata a címsorban"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Verzió infók megjelenítésének tiltása"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Verzió infók megjelenítése csak admin felhasználóknak"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Cég neve"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Belső cégnév"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "Kiindulási URL"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "Kiindulási URL a kiszolgáló példányhoz"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Alapértelmezett pénznem"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Válassz alap pénznemet az ár számításokhoz"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Támogatott valuták"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Támogatott valuták listája"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Árfolyam frissítési gyakoriság"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Milyen gyakran frissítse az árfolyamokat (nulla a kikapcsoláshoz)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "nap"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Árfolyam frissítő plugin"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Kiválasztott árfolyam frissítő plugin"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Letöltés URL-ről"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Képek és fájlok letöltésének engedélyezése külső URL-ről"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Letöltési méret korlát"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Maximum megengedett letöltési mérete a távoli képeknek"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "Felhasznált User-agent az URL-ről letöltéshez"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "A külső URL-ről letöltéshez használt user-agent felülbírálásának engedélyezése (hagyd üresen az alapértelmezéshez)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Erős URL validáció"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Sablon specifikáció igénylése az URL validálásnál"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Frissítés keresés gyakorisága"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Milyen gyakran ellenőrizze van-e új frissítés (0=soha)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Automatikus biztonsági mentés"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Adatbázis és média fájlok automatikus biztonsági mentése"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Automata biztonsági mentés gyakorisága"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Hány naponta készüljön automatikus biztonsági mentés"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Feladat törlési gyakoriság"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Háttérfolyamat eredmények törlése megadott nap eltelte után"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Hibanapló törlési gyakoriság"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Hibanapló bejegyzések törlése megadott nap eltelte után"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Értesítés törlési gyakoriság"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Felhasználói értesítések törlése megadott nap eltelte után"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr ""

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr ""

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr ""

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Vonalkód támogatás"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Vonalkód olvasó támogatás engedélyezése a web felületen"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr ""

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr ""

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr ""

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr ""

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Vonalkód beadási késleltetés"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Vonalkód beadáskor a feldolgozás késleltetési ideje"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Webkamerás vonalkód olvasás"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Webkamerás kódolvasás engedélyezése a böngészőből"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Vonalkód Adat Megjelenítése"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Vonalkód adat megjelenítése a böngészőben szövegként"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "Vonalkód Generáló Plugin"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "Belső vonalkód generálásra használatos plugin"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Alkatrész változatok"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Alkatrész változat vagy verziószám tulajdonság használata"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "Csak Összeállítás Verzió"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "Csak összeállított alkatrészeknek lehessen verziója"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Lehessen törölni az Összeállításból"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Lehessen olyan alkatrészt törölni ami Összeállításban szerepel"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "IPN reguláris kifejezés"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Reguláris kifejezés ami illeszkedik az alkatrész IPN-re"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Többször is előforduló IPN engedélyezése"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Azonos IPN használható legyen több alkatrészre is"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "IPN szerkesztésének engedélyezése"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "IPN megváltoztatásánsak engedélyezése az alkatrész szerkesztése közben"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Alkatrészjegyzék adatok másolása"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Alkatrész másoláskor az alkatrészjegyzék adatokat is másoljuk alapból"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Alkatrész paraméterek másolása"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Alkatrész másoláskor a paramétereket is másoljuk alapból"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Alkatrész teszt adatok másolása"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Alkatrész másoláskor a tesztek adatait is másoljuk alapból"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Kategória paraméter sablonok másolása"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Kategória paraméter sablonok másolása alkatrész létrehozásakor"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Sablon"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Alkatrészek alapból sablon alkatrészek legyenek"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Alkatrészeket alapból lehessen gyártani másik alkatrészekből"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Összetevő"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Alkatrészek alapból használhatók összetevőként más alkatrészekhez"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Beszerezhető"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Alkatrészek alapból beszerezhetők legyenek"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Értékesíthető"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Alkatrészek alapból eladhatók legyenek"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Alkatrészek alapból követésre kötelezettek legyenek"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtuális"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Alkatrészek alapból virtuálisak legyenek"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Kapcsolódó alkatrészek megjelenítése"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Alkatrész kapcsolódó alkatrészeinek megjelenítése"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Kezdeti készlet adatok"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Kezdeti készlet létrehozása új alkatrész felvételekor"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Kezdeti beszállítói adatok"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Kezdeti beszállítói adatok létrehozása új alkatrész felvételekor"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Alkatrész név megjelenítés formátuma"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Formátum az alkatrész név megjelenítéséhez"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Alkatrész kategória alapértelmezett ikon"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Alkatrész kategória alapértelmezett ikon (üres ha nincs)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Csak választható mértékegységek"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "A megadott mértékegység csak a beállított lehetőségekből legyen elfogadva"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Áraknál használt tizedesjegyek min. száma"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Tizedejegyek minimális száma az árak megjelenítésekor"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Áraknál használt tizedesjegyek max. száma"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Tizedejegyek maximális száma az árak megjelenítésekor"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Beszállítói árazás használata"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Beszállítói ársávok megjelenítése az általános árkalkulációkban"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Beszerzési előzmények felülbírálása"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Beszerzési árelőzmények felülírják a beszállítói ársávokat"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Készlet tétel ár használata"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "A kézzel bevitt készlet tétel árak használata az árszámításokhoz"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Készlet tétel ár kora"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Az ennyi napnál régebbi készlet tételek kizárása az árszámításból"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Alkatrészváltozat árak használata"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Alkatrészváltozat árak megjelenítése az általános árkalkulációkban"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Csak az aktív változatokat"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Csak az aktív alkatrészváltozatok használata az árazásban"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr "Árazás automatikus frissítése"

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr ""

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Árazás újraszámítás gyakoriság"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Árak automatikus frissítése ennyi nap után"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Belső árak"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Alkatrészekhez belső ár engedélyezése"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Belső ár felülbírálása"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Ha elérhetőek az árkalkulációkban a belső árak lesznek alapul véve"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Címke nyomtatás engedélyezése"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Címke nyomtatás engedélyezése a web felületről"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "Címke kép DPI"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Képek felbontása amik átadásra kerülnek címkenyomtató pluginoknak"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Riportok engedélyezése"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Riportok előállításának engedélyezése"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Debug mód"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Riportok előállítása HTML formátumban (hibakereséshez)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Jelentési hibák naplózása"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Jelentések generálása közben jelentkező hibák naplózása"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Lapméret"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Alapértelmezett lapméret a PDF riportokhoz"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Globálisan egyedi sorozatszámok"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "A sorozatszámoknak egyedinek kell lennie a teljes készletre vonatkozóan"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Kimerült készlet törlése"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Alapértelmezett művelet mikor a készlet tétel elfogy"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Batch kód sablon"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Sablon a készlet tételekhez alapértelmezett batch kódok előállításához"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Készlet lejárata"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Készlet lejárat kezelésének engedélyezése"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Lejárt készlet értékesítése"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Lejárt készlet értékesítésének engedélyezése"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Álló készlet ideje"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Napok száma amennyivel a lejárat előtt a készlet tételeket állottnak vesszük"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Lejárt készlet gyártása"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Gyártás engedélyezése lejárt készletből"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Készlet tulajdonosok kezelése"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Tulajdonosok kezelésének engedélyezése a készlet helyekre és tételekre"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Hely alapértelmezett ikon"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Hely alapértelmezett ikon (üres ha nincs)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Beépített készlet megjelenítése"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Beépített készlet tételek megjelenítése a készlet táblákban"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Tételek telepítésekor a darabjegyzék ellenőrzése"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "A beépített tételeknek a szülő elem darabjegyzékében szerepelniük kell"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Lehet Hiányzó Készletet Mozgatni"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Lehet-e olyan készleteket mozgatni készlethelyek között amik nincsenek raktáron"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Gyártási utasítás azonosító minta"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Szükséges minta a gyártási utasítás azonosító mező előállításához"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "Felelős tulajdonos szükséges"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "Minden rendeléshez felelőst kell rendelni"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "Szükséges Aktív Alkatrész"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "Inaktív alkatrészekre nem lehet Gyártási Rendelést létrehozni"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "Elvárás a Lezárt Alkatrész"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "Megakadályozza, hogy nem lezárt alkatrészekre gyártási rendelést lehessen indítani"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "Jóváhagyott Alkatrészjegyzék Kötelező"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Megakadályozza gyártási rendelés készítését ha nincsen az Alkatrészjegyzék jóváhagyva"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "Leszármazott Gyártásoknak Lezártnak Kell Lennie"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "Amíg minden leszármazott gyártás le nincsen zárva nem lehet a szülő gyártást lezárni"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr ""

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr ""

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Blokkolás a tesztek sikeres végrehajtásáig"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Nem lehet gyártási tételt befejezni amíg valamennyi kötelező teszt sikeres nem lett"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Visszavétel engedélyezése"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Visszavételek engedélyezése a felületen"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Visszavétel azonosító minta"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Szükséges minta a visszavétel azonosító mező előállításához"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Befejezett visszavétel szerkesztése"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Visszavétel szerkesztésének engedélyezése befejezés után"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Vevői rendelés azonosító minta"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Szükséges minta a vevői rendelés azonosító mező előállításához"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Vevői rendeléshez alapértelmezett szállítmány"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Szállítmány automatikus létrehozása az új vevő rendelésekhez"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Befejezett vevői rendelés szerkesztése"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Vevői rendelések szerkesztésének engedélyezése szállítás vagy befejezés után"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Leszállított Rendelések Készre jelölése"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Leszállítottnak jelölt Értékesítési rendelések automatikusan Kész-re lesznek állítva, a \"Leszállított\" állapot átugrásával"

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Beszerzési rendelés azonosító minta"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Szükséges minta a beszerzési rendelés azonosító mező előállításához"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Befejezett beszerzési rendelés szerkesztése"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Beszérzési rendelések szerkesztésének engedélyezése kiküldés vagy befejezés után"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr "Jelenlegi pénznem"

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Beszerzési rendelések automatikus befejezése"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "A beszerzési rendelés automatikus befejezése ha minden sortétel beérkezett"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Elfelejtett jelszó engedélyezése"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Elfelejtett jelszó funkció engedélyezése a bejentkező oldalon"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Regisztráció engedélyezése"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Felhaszálók önkéntes regisztrációjának engedélyezése a bejelentkező oldalon"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "SSO engedélyezése"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "SSO engedélyezése a bejelentkező oldalon"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "SSO regisztráció engedélyezése"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Felhaszálók önkéntes regisztrációjának engedélyezése SSO-n keresztül a bejelentkező oldalon"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "SSO csoport szinkronizálás engedélyezése"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Az InvenTree csoportok szinkronizálása a hitelesítésszolgáltatóhoz"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "SSO csoport kulcs"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "A csoportkérés tulajdonság neve amit a hitelesítésszolgáltató nyújt"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "SSO csoport hozzárendelés"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Az SSO csoportok hozzárendelése az InvenTree csoportokhoz. Ha a helyi csoport nem létezik, létre lesz hozva."

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "Az SSO-n kívüli csoportok eltávolítása"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Ha egy felhasználóhoz rendelt csoport nem létezik az azonosításszolgáltatóban azt eltávolítsuk el. Ennek a kikapcsolása biztonsági problémákhoz vezethet"

#: common/setting/system.py:959
msgid "Email required"
msgstr "Email szükséges"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Kötelező email megadás regisztrációkor"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "SSO felhasználók automatikus kitöltése"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Felhasználó adatainak automatikus kitöltése az SSO fiókadatokból"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "Email kétszer"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Regisztráláskor kétszer kérdezze a felhasználó email címét"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Jelszó kétszer"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "Regisztráláskor kétszer kérdezze a felhasználó jelszavát"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Engedélyezett domainek"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Feliratkozás korlátozása megadott domain-ekre (vesszővel elválasztva, @-al kezdve)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Csoport regisztráláskor"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Ehhez a csoporthoz lesznek az új felhasználók rendelve. Ha az SSO csoport szinkronizálás engedélyezve van, akkor ez a csoport csak akkor lesz hozzárendelve a felhasználóhoz ha az azonosítás szolgáltató semmilyen csoportot nem rendelt hozzá."

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "Többfaktoros hitelesítés kényszerítése"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "A felhasználóknak többfaktoros hitelesítést kell használniuk."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Pluginok ellenőrzése indításkor"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Ellenőrizze induláskor hogy minden plugin telepítve van - engedélyezd konténer környezetben (docker)"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Plugin frissítések ellenőrzése"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Frissítések periódikus ellenőrzésének engedélyezése a telepített pluginokra"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "URL integráció engedélyezése"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "URL útvonalalak hozzáadásának engedélyezése a pluginok számára"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Navigációs integráció engedélyezése"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Navigációs integráció engedélyezése a pluginok számára"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "App integráció engedélyezése"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "App hozzáadásának engedélyezése a pluginok számára"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Ütemezés integráció engedélyezése"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Háttérben futó feladatok hozzáadásának engedélyezése a pluginok számára"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Esemény integráció engedélyezése"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Belső eseményekre reagálás engedélyezése a pluginok számára"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr "Email integráció engedélyezése"

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr ""

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr "Projektszámok engedélyezése"

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr ""

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr ""

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Külső helyek nélkül"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr ""

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Automatikus leltár időpontja"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr ""

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr ""

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr ""

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr ""

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr ""

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Felhasználók teljes nevének megjelenítése"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Felhasználói név helyett a felhasználók teljes neve jelenik meg"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr "Felhasználói profilok megjelenítése"

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Teszt állomás adatok engedélyezése"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Tesztállomás adatok gyűjtésének teszt eredménybe gyűjtésének engedélyezése"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Beágyazott címke megjelenítés"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "PDF címkék megjelenítése a böngészőben letöltés helyett"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Alapértelmezett címkenyomtató"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Melyik címkenyomtató legyen az alapértelmezett"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Beágyazott riport megjelenítés"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "PDF riport megjelenítése a böngészőben letöltés helyett"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Alkatrészek keresése"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Alkatrészek megjelenítése a keresési előnézetben"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Beszállítói alkatrészek keresése"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Beszállítói alkatrészek megjelenítése a keresési előnézetben"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Gyártói alkatrészek keresése"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Gyártói alkatrészek megjelenítése a keresési előnézetben"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Inaktív alkatrészek elrejtése"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Inaktív alkatrészek kihagyása a keresési előnézet találataiból"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Kategóriák keresése"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Alkatrész kategóriák megjelenítése a keresési előnézetben"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Készlet keresése"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Készlet tételek megjelenítése a keresési előnézetben"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Nem elérhető készlet tételek elrejtése"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Nem elérhető készlet kihagyása a keresési előnézet találataiból"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Helyek keresése"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Készlet helyek megjelenítése a keresési előnézetben"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Cégek keresése"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Cégek megjelenítése a keresési előnézetben"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Gyártási utasítások keresése"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Gyártási utasítások megjelenítése a keresés előnézet ablakban"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Beszerzési rendelések keresése"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Beszerzési rendelések megjelenítése a keresési előnézetben"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Inaktív beszerzési rendelések kihagyása"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Inaktív beszerzési rendelések kihagyása a keresési előnézet találataiból"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Vevői rendelések keresése"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Vevői rendelések megjelenítése a keresési előnézetben"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Inaktív vevői rendelések kihagyása"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Inaktív vevői rendelések kihagyása a keresési előnézet találataiból"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Értékesítési rendelés szállítmányok"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Értékesítési rendelések megjelenítése a keresési előnézetben"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Visszavétel keresése"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Visszavételek megjelenítése a keresés előnézet ablakban"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Inaktív visszavételek kihagyása"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Inaktív visszavételek kihagyása a keresési előnézet találataiból"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Keresési előnézet eredményei"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "A keresési előnézetben megjelenítendő eredmények száma szekciónként"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex keresés"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Reguláris kifejezések engedélyezése a keresésekben"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Teljes szó keresés"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "A keresések csak teljes szóra egyező találatokat adjanak"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Megjegyzések keresése"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "ESC billentyű zárja be a formot"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "ESC billentyű használata a modális formok bezárásához"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Rögzített menüsor"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "A menü pozíciója mindig rögzítve a lap tetején"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr "Fix táblázat fejlécek"

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr ""

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr "Navigációs ikonok"

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr "Ikonok megjelenítése a navigációs sávon"

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Dátum formátum"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Preferált dátum formátum a dátumok kijelzésekor"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr ""

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr ""

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr ""

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr ""

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr ""

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr ""

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Hibariportok fogadása"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Értesítések fogadása a rendszerhibákról"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Utoljára használt nyomtató gépek"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Az utoljára használt nyomtató tárolása a felhasználóhoz"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "A melléklethez nem tartozik model típus"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Érvénytelen melléklet model típus"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "A legkisebb helyiérték nem lehet nagyobb mint a legnagyobb helyiérték"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "A legnagyobb helyiérték nem lehet kisebb mint a legkisebb helyiérték"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Üres domain nem engedélyezett."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Érvénytelen domain név: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "Az alkatrész aktív"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "A Gyártó Aktív"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "A Szállítói Alkatrész Aktív"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "A saját alkatrész Aktív"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "A Beszállító Aktív"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Gyártó"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Cég"

#: company/api.py:316
msgid "Has Stock"
msgstr "Van készleten"

#: company/models.py:120
msgid "Companies"
msgstr "Cégek"

#: company/models.py:148
msgid "Company description"
msgstr "Cég leírása"

#: company/models.py:149
msgid "Description of the company"
msgstr "A cég leírása"

#: company/models.py:155
msgid "Website"
msgstr "Weboldal"

#: company/models.py:156
msgid "Company website URL"
msgstr "Cég weboldala"

#: company/models.py:162
msgid "Phone number"
msgstr "Telefonszám"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Kapcsolattartó telefonszáma"

#: company/models.py:171
msgid "Contact email address"
msgstr "Kapcsolattartó email címe"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Névjegy"

#: company/models.py:178
msgid "Point of contact"
msgstr "Kapcsolattartó"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Link a külső céginformációhoz"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Ez a vállalat aktív?"

#: company/models.py:203
msgid "Is customer"
msgstr "Vevő"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Értékesítesz alkatrészeket ennek a cégnek?"

#: company/models.py:209
msgid "Is supplier"
msgstr "Beszállító"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Vásárolsz alkatrészeket ettől a cégtől?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "Gyártó"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Gyárt ez a cég alkatrészeket?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Cég által használt alapértelmezett pénznem"

#: company/models.py:231
msgid "Tax ID"
msgstr "Adószám"

#: company/models.py:232
msgid "Company Tax ID"
msgstr "Céges adószám"

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Cím"

#: company/models.py:355
msgid "Addresses"
msgstr "Címek"

#: company/models.py:412
msgid "Select company"
msgstr "Cég kiválasztása"

#: company/models.py:417
msgid "Address title"
msgstr "Cím megnevezése"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Címhez tartozó leírás, megnevezés"

#: company/models.py:424
msgid "Primary address"
msgstr "Elsődleges cím"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Beállítás elsődleges címként"

#: company/models.py:430
msgid "Line 1"
msgstr "1. sor"

#: company/models.py:431
msgid "Address line 1"
msgstr "Cím első sora"

#: company/models.py:437
msgid "Line 2"
msgstr "2. sor"

#: company/models.py:438
msgid "Address line 2"
msgstr "Cím második sora"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "Irányítószám"

#: company/models.py:451
msgid "City/Region"
msgstr "Város/Régió"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Irányítószám város/régió"

#: company/models.py:458
msgid "State/Province"
msgstr "Állam/Megye"

#: company/models.py:459
msgid "State or province"
msgstr "Állam vagy megye"

#: company/models.py:465
msgid "Country"
msgstr "Ország"

#: company/models.py:466
msgid "Address country"
msgstr "Cím országa"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Megjegyzés a futárnak"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Futárnak szóló megjegyzések"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Belső szállítási megjegyzések"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Szállítási megjegyzések belső használatra"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Link a címinformációkhoz (külső)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Gyártói alkatrész"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Kiindulási alkatrész"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Válassz alkatrészt"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Gyártó kiválasztása"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "MPN (Gyártói cikkszám)"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Gyártói cikkszám"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "URL link a gyártói alkatrészhez"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Gyártói alkatrész leírása"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Gyártói Cikkszám"

#: company/models.py:635
msgid "Parameter name"
msgstr "Paraméter neve"

#: company/models.py:642
msgid "Parameter value"
msgstr "Paraméter értéke"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Mértékegység"

#: company/models.py:650
msgid "Parameter units"
msgstr "Paraméter mértékegység"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "A csomagolási egységnek kompatibilisnek kell lennie az alkatrész mértékegységgel"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Csomagolási mennyiségnek nullánál többnek kell lennie"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "Kapcsolódó gyártói alkatrésznek ugyanarra a kiindulási alkatrészre kell hivatkoznia"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Beszállító"

#: company/models.py:829
msgid "Select supplier"
msgstr "Beszállító kiválasztása"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Beszállítói cikkszám"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Ez a szállítói termék aktív?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Gyártói alkatrész kiválasztása"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "URL link a beszállítói alkatrészhez"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Beszállítói alkatrész leírása"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Megjegyzés"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "alap költség"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimális díj (pl. tárolási díj)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Csomagolás"

#: company/models.py:892
msgid "Part packaging"
msgstr "Alkatrész csomagolás"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Csomagolási mennyiség"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Egy csomagban kiszállítható mennyiség, hagyd üresen az egyedi tételeknél."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "többszörös"

#: company/models.py:919
msgid "Order multiple"
msgstr "Többszörös rendelés"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Beszállítónál elérhető mennyiség"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Elérhetőség frissítve"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Utolsó elérhetőségi adat frissítés"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Beszállítói Ár Kedvezmény"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Beszállító által használt alapértelmezett pénznem"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Cégnév"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "Készleten"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Export formátum"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Export plugin"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "További állapot információk erről a tételről"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Saját Állapot Kulcs"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Egyedi"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Osztály"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Értékek"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Kiküldve"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:73
msgid "Data File"
msgstr "Adat fájl"

#: importer/models.py:74
msgid "Data file to import"
msgstr "Importálandó adatfájl"

#: importer/models.py:83
msgid "Columns"
msgstr "Oszlopok"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:96
msgid "Import status"
msgstr "Betöltés állapota"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Mező Alapértelmezett Érték"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "Mező Felülbírálás"

#: importer/models.py:120
msgid "Field Filters"
msgstr "Mező Szűrők"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "Néhány kötelező mező nem került hozzárendelésre"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "Oszlop már adatbázis mezőhöz lett rendelve"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "Adatbázis mező már adatfájl oszlophoz lett rendelve"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "Az oszlop összerendelésnek egy helyes importálási művelethez kell kapcsolódnia"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "Az Oszlop nem létezik ebben a fájlban"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "A mező nem létezik a cél adatszerkezetben"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "Kijelölt mező csak olvasható"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "Importálási művelet"

#: importer/models.py:471
msgid "Field"
msgstr "Mező"

#: importer/models.py:473
msgid "Column"
msgstr "Oszlop"

#: importer/models.py:542
msgid "Row Index"
msgstr "Sor száma"

#: importer/models.py:545
msgid "Original row data"
msgstr "Eredeti sor adat"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Hibák"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Érvényes"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Nem támogatott adatfájl formátum"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Adatfájl megnyitása sikertelen"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Az adatállomány méretei - szélessége nem megfelelő"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "Érvénytelen mező alapértelmezések"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "Érvénytelen mező felülbírálások"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "Érvénytelen mező szűrések"

#: importer/serializers.py:177
msgid "Rows"
msgstr "Sorok"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Az elfogadható azonosítók listája"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Nincs sor megadva"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "A sor nem az aktuális művelethez kapcsolódik"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "A Sor érvénytelen adatot tartalmaz"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "A sor már be lett fejezve"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Előkészítés"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Oszlopok összerendelése"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Adatok importálása"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Adatok feldolgozása"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Adatfájl meghaladja a maximális méretet"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Adatfájlból hiányzik a fejléc"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Túl sok oszlop az adatfájlban"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Túl sok sor az adatfájlban"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Az értéknek a érvényes szótár elemnek kell lennie"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Másolatok"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Címkénkénti nyomtatandó mennyiség"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Csatlakoztatba"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Ismeretlen"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Nyomtatás"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Nincs papír"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Elakadt a papír"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Nincs kapcsolat"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Címkenyomtató"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Közvetlen címkenyomtatás különféle tételekre."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Nyomtató helye"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Nyomtató korlátozása egy készlethelyhez"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Gép neve"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Géptípus"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Gép típusa"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Illesztőprogram"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Berendezéshez használható meghajtó"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "A berendezések letilthatók"

#: machine/models.py:95
msgid "Driver available"
msgstr "Meghajtó elérhető"

#: machine/models.py:100
msgid "No errors"
msgstr "Nincsen hiba"

#: machine/models.py:105
msgid "Initialized"
msgstr "Inicializálva"

#: machine/models.py:117
msgid "Machine status"
msgstr "Gép állapot"

#: machine/models.py:145
msgid "Machine"
msgstr "Gép"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Gép konfiguráció"

#: machine/models.py:162
msgid "Config type"
msgstr "Konfiguráció típusa"

#: order/api.py:121
msgid "Order Reference"
msgstr "Rendelés azonosítója"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "Kintlévő"

#: order/api.py:165
msgid "Has Project Code"
msgstr "Van projektszáma"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Készítette"

#: order/api.py:183
msgid "Created Before"
msgstr "Ez előtt létrehozva"

#: order/api.py:187
msgid "Created After"
msgstr "Létrehozva ez után"

#: order/api.py:191
msgid "Has Start Date"
msgstr ""

#: order/api.py:199
msgid "Start Date Before"
msgstr ""

#: order/api.py:203
msgid "Start Date After"
msgstr ""

#: order/api.py:207
msgid "Has Target Date"
msgstr ""

#: order/api.py:215
msgid "Target Date Before"
msgstr ""

#: order/api.py:219
msgid "Target Date After"
msgstr ""

#: order/api.py:270
msgid "Has Pricing"
msgstr "Van árazás"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr ""

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "Befejezve ez után"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr ""

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Rendelés"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "A rendelés teljesítve"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Belső alkatrész"

#: order/api.py:578
msgid "Order Pending"
msgstr "A rendelés függőben"

#: order/api.py:958
msgid "Completed"
msgstr "Kész"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "Van kiszállítás"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Beszerzési rendelés"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Vevői rendelés"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Visszavétel"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Teljes ár"

#: order/models.py:91
msgid "Total price for this order"
msgstr "A rendelés teljes ára"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Rendelés pénzneme"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Megrendeléshez használt pénznem (hagyd üresen a cégnél alapértelmezetthez)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "A kapcsolattartó nem egyezik a kiválasztott céggel"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Rendelés leírása (opcionális)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Válassz projektszámot ehhez a rendeléshez"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Link külső weboldalra"

#: order/models.py:458
msgid "Start date"
msgstr "Kezdés dátuma"

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Cél dátum"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Várt teljesítési dátuma a megrendelésnek. Ezután már késésben lévőnek számít majd."

#: order/models.py:487
msgid "Issue Date"
msgstr "Kiállítás dátuma"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Kiállítás dátuma"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Felhasználó vagy csoport aki felelőse ennek a rendelésnek"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Kapcsolattartó ehhez a rendeléshez"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Cég címei ehhez a rendeléshez"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Rendelés azonosító"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Állapot"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Beszerzési rendelés állapota"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Cég akitől a tételek beszerzésre kerülnek"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Beszállítói azonosító"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Beszállítói rendelés azonosító kód"

#: order/models.py:654
msgid "received by"
msgstr "érkeztette"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Rendelés teljesítési dátuma"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Cél"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr ""

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Az alkatrész beszállítója meg kell egyezzen a beszerzési rendelés beszállítójával"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr "Sortétel nem egyezik a beszerzési megrendeléssel"

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "Mennyiség pozitív kell legyen"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Vevő"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Cég akinek a tételek értékesítésre kerülnek"

#: order/models.py:1318
msgid "Sales order status"
msgstr "Értékesítési rendelés állapot"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Vevői azonosító "

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Megrendelés azonosító kódja a vevőnél"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Kiszállítás dátuma"

#: order/models.py:1343
msgid "shipped by"
msgstr "szállította"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "Rendelés már teljesítve"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "Rendelés már visszavonva"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Csak nyitott rendelés jelölhető késznek"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "A rendelés nem jelölhető késznek mivel függő szállítmányok vannak"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "A rendelés nem jelölhető késznek mivel nem teljesített sortételek vannak"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1711
msgid "Item quantity"
msgstr "Tétel mennyiség"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Sortétel azonosító"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Sortétel megjegyzései"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Cél dátuma ennek a sortételnek (hagyd üresen a rendelés céldátum használatához)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Sortétel leírása (opcionális)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "További kontextus ehhez a sorhoz"

#: order/models.py:1788
msgid "Unit price"
msgstr "Egységár"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "Vevői Rendelés Sortétel"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "Beszállítói alkatrésznek egyeznie kell a beszállítóval"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr ""

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr ""

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr ""

#: order/models.py:1884
msgid "Supplier part"
msgstr "Beszállítói alkatrész"

#: order/models.py:1891
msgid "Received"
msgstr "Beérkezett"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Érkezett tételek száma"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Beszerzési ár"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Beszerzési egységár"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr ""

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "Vevői Rendelés Extra Sor"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "Vevői Rendelés Sortétel"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtuális alkatrészt nem lehet vevői rendeléshez adni"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Csak értékesíthető alkatrészeket lehet vevői rendeléshez adni"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Eladási ár"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Eladási egységár"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Kiszállítva"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Szállított mennyiség"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "Vevői Rendelés Szállítása"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Szállítás dátuma"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Szállítási dátum"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Kézbesítés dátuma"

#: order/models.py:2221
msgid "Checked By"
msgstr "Ellenőrizte"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Felhasználó aki ellenőrizte ezt a szállítmányt"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Szállítmány"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Szállítmány száma"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Nyomkövetési szám"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Szállítmány nyomkövetési információ"

#: order/models.py:2246
msgid "Invoice Number"
msgstr "Számlaszám"

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr "Hozzátartozó számla referencia száma"

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr "Szállítmány már elküldve"

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr "Szállítmány nem tartalmaz foglalt készlet tételeket"

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr "Vevői Rendelés Extra Sor"

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr "Vevői rendeléshez foglalások"

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr "Készlet tétel nincs hozzárendelve"

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Nem foglalható készlet egy másik fajta alkatrész sortételéhez"

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr "Nem foglalható készlet egy olyan sorhoz amiben nincs alkatrész"

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "A lefoglalandó mennyiség nem haladhatja meg a készlet mennyiségét"

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr "Egyedi követésre kötelezett tételeknél a menyiség 1 kell legyen"

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr "Vevői rendelés nem egyezik a szállítmánnyal"

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Szállítmány nem egyezik a vevői rendeléssel"

#: order/models.py:2451
msgid "Line"
msgstr "Sor"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr "Vevői rendelés szállítmány azonosító"

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Tétel"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr "Válaszd ki a foglalásra szánt készlet tételt"

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr "Készlet foglalási mennyiség megadása"

#: order/models.py:2600
msgid "Return Order reference"
msgstr "Visszavétel azonosító"

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr "Cég akitől a tételek visszavételre kerülnek"

#: order/models.py:2625
msgid "Return order status"
msgstr "Visszavétel állapota"

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr "Visszavétel sortétel"

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr "Válaszd ki a vevőtől visszavenni kívánt tételt"

#: order/models.py:2910
msgid "Received Date"
msgstr "Visszavétel dátuma"

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr "Mikor lett visszavéve a tétel"

#: order/models.py:2923
msgid "Outcome"
msgstr "Kimenetel"

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr "Sortétel végső kimenetele"

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr "Sortétel visszaküldésének vagy javításának költsége"

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr "Visszavétel extra tétel"

#: order/serializers.py:90
msgid "Order ID"
msgstr "Rendelés azonosító"

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr "A duplikálandó megrendelés száma"

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "Sorok másolása"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Sortételek"

#: order/serializers.py:122
msgid "Completed Lines"
msgstr "Kész sorok"

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:389
msgid "Supplier Name"
msgstr "Beszállító neve"

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr "A rendelést nem lehet törölni"

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr "Rendelés lezárása teljesítetlen sortételek esetén is"

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr "A rendelésben teljesítetlen sortételek vannak"

#: order/serializers.py:611
msgid "Order is not open"
msgstr "A rendelés nem nyitott"

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr "Automata árazás"

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Beszerzési ár automatikus számítása a beszállítói alkatrész adatai alapján"

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr "Beszérzési ár pénzneme"

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Elemek összevonása"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Azonos forrás és cél dátumú Alkatrész tételeinek összevonása egy tételre"

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "SKU (leltári azonosító)"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr "Belső cikkszám"

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr "Belső cikkszám"

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr "Beszállítói alkatrészt meg kell adni"

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr "Beszerzési rendelést meg kell adni"

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr "A beszállítónak egyeznie kell a beszerzési rendelésben lévővel"

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr "A beszerzési rendelésnek egyeznie kell a beszállítóval"

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr "Sortétel"

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr "Válassz cél helyet a beérkezett tételeknek"

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr "Írd be a batch kódját a beérkezett tételeknek"

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr "Lejárati dátum"

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr "Írd be a sorozatszámokat a beérkezett tételekhez"

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr "Bejövő készlettételek csomagolási információjának felülbírálata"

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr "Kiegészítő megjegyzés beérkező készlettételekhez"

#: order/serializers.py:826
msgid "Barcode"
msgstr "Vonalkód"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr "Beolvasott vonalkód"

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr "Ez a vonalkód már használva van"

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr "Sortételt meg kell adni"

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr "A cél helyet kötelező megadni"

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr "Megadott vonalkódoknak egyedieknek kel lenniük"

#: order/serializers.py:1066
msgid "Shipments"
msgstr "Szállítások"

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr "Kész szállítmányok"

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr "Eladási ár pénzneme"

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr "Nincsenek szállítmány részletek megadva"

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr "Sortétel nincs hozzárendelve ehhez a rendeléshez"

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr "Mennyiség pozitív kell legyen"

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr "Írd be a sorozatszámokat a kiosztáshoz"

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr "Szállítmány kiszállítva"

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr "Szállítmány nincs hozzárendelve ehhez a rendeléshez"

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr "Nincs találat a következő sorozatszámokra"

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr ""

#: order/serializers.py:1982
msgid "Return order line item"
msgstr "Visszavétel sortétel"

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr "Sortétel nem egyezik a visszavétellel"

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr "A sortétel már beérkezett"

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr "Csak folyamatban lévő megrendelés tételeit lehet bevételezni"

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2136
msgid "Line price currency"
msgstr "Sortétel pénzneme"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Elveszett"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Visszaküldve"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "Folyamatban"

#: order/status_codes.py:105
msgid "Return"
msgstr "Visszavétel"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Javítás"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Csere"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Visszatérítés"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Elutasított"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr "Késésben lévő beszerzés"

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "A {po} beszerzési rendelés most már késésben van"

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr "Késésben lévő vevői rendelés"

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "A {so} vevői rendelés most már késésben van"

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:111
msgid "Starred"
msgstr "Csillagozott"

#: part/api.py:113
msgid "Filter by starred categories"
msgstr "Csillagozottra szűrés"

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr "Mélység"

#: part/api.py:130
msgid "Filter by category depth"
msgstr "Kategória mélységre szűrés"

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr "Felső szint"

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr "Csúcs készlethelyre szűrés"

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr "Lépcsőzetes"

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr "Szűrt eredmények tartalmazzák az alkategóriákat"

#: part/api.py:185
msgid "Parent"
msgstr "Szülő"

#: part/api.py:187
msgid "Filter by parent category"
msgstr "Szülő kategóriára szűrés"

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr "Az adott kategória alkategóriáinak kihagyása"

#: part/api.py:434
msgid "Has Results"
msgstr "Van találat"

#: part/api.py:660
msgid "Is Variant"
msgstr "Változat-e"

#: part/api.py:668
msgid "Is Revision"
msgstr "Változat"

#: part/api.py:678
msgid "Has Revisions"
msgstr "Vannak Változatok"

#: part/api.py:859
msgid "BOM Valid"
msgstr "Alkatrészjegyzék ellenőrizve"

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr "Összeállított Alkatrész ellenőrizhető"

#: part/api.py:1511
msgid "Component part is testable"
msgstr "Összetevő alkatrész ellenőrizhető"

#: part/api.py:1576
msgid "Uses"
msgstr "Használ"

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Alkatrész kategória"

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr "Alkatrész kategóriák"

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr "Alapértelmezett hely"

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr "Ebben a kategóriában lévő alkatrészek helye alapban"

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr "Szerkezeti"

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "A szerkezeti alkatrész kategóriákhoz nem lehet direktben alkatrészeket hozzáadni, csak az alkategóriáikhoz."

#: part/models.py:134
msgid "Default keywords"
msgstr "Alapértelmezett kulcsszavak"

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr "Ebben a kategóriában évő alkatrészek kulcsszavai alapban"

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Ikon"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Ikon (opcionális)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Nem lehet az alkatrészkategóriát szerkezeti kategóriává tenni, mert már vannak itt alkatrészek!"

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Alkatrészek"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr "Lezárt alkatrész nem törölhető"

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr "Aktív alkatrész nem törölhető"

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Összeállításban felhasznált alkatrész nem törölhető"

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Az '{self}' alkatrész nem használható a '{parent}' alkatrészjegyzékében (mert rekurzív lenne)"

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Az '{parent}' alkatrész szerepel a '{self}' alkatrészjegyzékében (rekurzív)"

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "Az IPN belső cikkszámnak illeszkednie kell a {pattern} regex mintára"

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr "Alkatrész nem lehes saját magának verziója"

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Nem lehet olyan alkatrészből új verziót csinálni ami már eleve egy verzió"

#: part/models.py:724
msgid "Revision code must be specified"
msgstr "Verzió kódot meg kell adni"

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr "Verziók csak összeállított alkatrészeknél engedélyezettek"

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr "Nem lehet sablon alkatrészből új verziót csinálni"

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr "A szülő alkatrésznek azonos sablonra kell mutatnia"

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr "Létezik már készlet tétel ilyen a sorozatszámmal"

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr "Azonos IPN nem engedélyezett az alkatrészekre, már létezik ilyen"

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr "Adott alkatrész verzióból már létezik egy."

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Ilyen nevű, IPN-ű és reviziójú alkatrész már létezik."

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Szerkezeti kategóriákhoz nem lehet alkatrészeket rendelni!"

#: part/models.py:1051
msgid "Part name"
msgstr "Alkatrész neve"

#: part/models.py:1056
msgid "Is Template"
msgstr "Sablon-e"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr "Ez egy sablon alkatrész?"

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr "Ez az alkatrész egy másik változata?"

#: part/models.py:1068
msgid "Variant Of"
msgstr "Ebből a sablonból"

#: part/models.py:1075
msgid "Part description (optional)"
msgstr "Alkatrész leírása (opcionális)"

#: part/models.py:1082
msgid "Keywords"
msgstr "Kulcsszavak"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr "Alkatrész kulcsszavak amik segítik a megjelenést a keresési eredményekben"

#: part/models.py:1093
msgid "Part category"
msgstr "Alkatrész kategória"

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN (Belső Cikkszám)"

#: part/models.py:1108
msgid "Part revision or version number"
msgstr "Alkatrész változat vagy verziószám (pl. szín, hossz, revízió, stb.)"

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr "Változat"

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr "Ez egy másik alkatrész egy verziója?"

#: part/models.py:1119
msgid "Revision Of"
msgstr "Ennek a verziója"

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr "Alapban hol tároljuk ezt az alkatrészt?"

#: part/models.py:1190
msgid "Default Supplier"
msgstr "Alapértelmezett beszállító"

#: part/models.py:1191
msgid "Default supplier part"
msgstr "Alapértelmezett beszállítói alkatrész"

#: part/models.py:1198
msgid "Default Expiry"
msgstr "Alapértelmezett lejárat"

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr "Lejárati idő (napban) ennek az alkatrésznek a készleteire"

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Minimális készlet"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr "Minimálisan megengedett készlet mennyiség"

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr "Alkatrész mértékegysége"

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr "Gyártható-e ez az alkatrész más alkatrészekből?"

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr "Felhasználható-e ez az alkatrész más alkatrészek gyártásához?"

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr "Kell-e külön követni az egyes példányait ennek az alkatrésznek?"

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr "Lehet ehhez az alkatrészhez több ellenőrzési eredményt rögzíteni?"

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr "Rendelhető-e ez az alkatrész egy külső beszállítótól?"

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr "Értékesíthető-e önmagában ez az alkatrész a vevőknek?"

#: part/models.py:1258
msgid "Is this part active?"
msgstr "Aktív-e ez az alkatrész?"

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr "Lezárt alkatrészt nem lehet szerkeszteni"

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Ez egy virtuális nem megfogható alkatrész, pl. szoftver vagy licenc?"

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr "Alkatrészjegyzék ellenőrző összeg"

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr "Tárolt alkatrészjegyzék ellenőrző összeg"

#: part/models.py:1291
msgid "BOM checked by"
msgstr "Alkatrészjegyzéket ellenőrizte"

#: part/models.py:1296
msgid "BOM checked date"
msgstr "Alkatrészjegyzék ellenőrzési dátuma"

#: part/models.py:1312
msgid "Creation User"
msgstr "Létrehozó"

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr "Alkatrész felelőse"

#: part/models.py:2257
msgid "Sell multiple"
msgstr "Több értékesítése"

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr "Árszámítások gyorstárazásához használt pénznem"

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr "Minimum alkatrészjegyzék költség"

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr "Összetevők minimum költsége"

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr "Maximum alkatrészjegyzék költség"

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr "Összetevők maximum költsége"

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr "Minimum beszerzési ár"

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr "Eddigi minimum beszerzési költség"

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr "Maximum beszerzési ár"

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr "Eddigi maximum beszerzési költség"

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr "Minimum belső ár"

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr "Minimum költség a belső ársávok alapján"

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr "Maximum belső ár"

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr "Maximum költség a belső ársávok alapján"

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr "Minimum beszállítói ár"

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr "Minimum alkatrész ár a beszállítóktól"

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr "Maximum beszállítói ár"

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr "Maximum alkatrész ár a beszállítóktól"

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr "Minimum alkatrészváltozat ár"

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr "Alkatrészváltozatok számolt minimum költsége"

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr "Maximum alkatrészváltozat ár"

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr "Alkatrészváltozatok számolt maximum költsége"

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr "Minimum költség"

#: part/models.py:3372
msgid "Override minimum cost"
msgstr "Minimum költség felülbírálása"

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr "Maximum költség"

#: part/models.py:3379
msgid "Override maximum cost"
msgstr "Maximum költség felülbírálása"

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr "Számított általános minimum költség"

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr "Számított általános maximum költség"

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr "Minimum eladási ár"

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr "Minimum eladási ár az ársávok alapján"

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr "Maximum eladási ár"

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr "Maximum eladási ár az ársávok alapján"

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr "Minimum eladási költség"

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr "Eddigi minimum eladási ár"

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr "Maximum eladási költség"

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr "Eddigi maximum eladási ár"

#: part/models.py:3439
msgid "Part for stocktake"
msgstr "Leltározható alkatrész"

#: part/models.py:3444
msgid "Item Count"
msgstr "Tételszám"

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr "Egyedi készlet tételek száma a leltárkor"

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr "Teljes készlet a leltárkor"

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Dátum"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr "Leltározva ekkor"

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr "Minimum készlet érték"

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr "Becsült minimum raktárkészlet érték"

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr "Maximum készlet érték"

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr "Becsült maximum raktárkészlet érték"

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr "Alkatrész értékesítési ársáv"

#: part/models.py:3595
msgid "Part Test Template"
msgstr "Alkatrész Teszt Sablon"

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Hibás sablon név - legalább egy alfanumerikus karakter kötelező"

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr "A lehetőségek egyediek kell legyenek"

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "Teszt sablont csak ellenőrizhetőre beállított alkatrészhez lehet csinálni"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr "Már létezik ilyen azonosítójú Teszt sablon ehhez az alkatrészhez"

#: part/models.py:3684
msgid "Test Name"
msgstr "Teszt név"

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr "Add meg a teszt nevét"

#: part/models.py:3691
msgid "Test Key"
msgstr "Teszt azonosító"

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr "Egyszerűsített Teszt azonosító"

#: part/models.py:3699
msgid "Test Description"
msgstr "Teszt leírása"

#: part/models.py:3700
msgid "Enter description for this test"
msgstr "Adj hozzá egy leírást ehhez a teszthez"

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Engedélyezve"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr "Teszt engedélyezve?"

#: part/models.py:3709
msgid "Required"
msgstr "Kötelező"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr "Szükséges-e hogy ez a teszt sikeres legyen?"

#: part/models.py:3715
msgid "Requires Value"
msgstr "Kötelező érték"

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr "Szükséges-e hogy ennek a tesztnek az eredményéhez kötelezően érték legyen rendelve?"

#: part/models.py:3721
msgid "Requires Attachment"
msgstr "Kötelező melléklet"

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Szükséges-e hogy ennek a tesztnek az eredményéhez kötelezően fájl melléklet legyen rendelve?"

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr "Lehetőségek"

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr "Választható lehetőségek ehhez a Teszthez (vesszővel elválasztva)"

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr "Alkatrész Paraméter Sablon"

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr "Jelölőnégyzet paraméternek nem lehet mértékegysége"

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr "Jelölőnégyzet paraméternek nem lehetnek választási lehetőségei"

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr "A paraméter sablon nevének egyedinek kell lennie"

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Paraméter neve"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr "Paraméter mértékegysége"

#: part/models.py:3865
msgid "Parameter description"
msgstr "Paraméter leírása"

#: part/models.py:3871
msgid "Checkbox"
msgstr "Jelölőnégyzet"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr "Ez a paraméter egy jelölőnégyzet?"

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Választható lehetőségek (vesszővel elválasztva)"

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3931
msgid "Part Parameter"
msgstr "Alkatrész Paraméter"

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr "Lezárt alkatrész Paramétere nem szerkeszthető"

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr "Hibás választás a paraméterre"

#: part/models.py:4046
msgid "Parent Part"
msgstr "Szülő alkatrész"

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr "Paraméter sablon"

#: part/models.py:4060
msgid "Parameter Value"
msgstr "Paraméter értéke"

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr "Opcionális megjegyzés mező"

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr "Alkatrészcsoport Paraméter Sablon"

#: part/models.py:4176
msgid "Default Value"
msgstr "Alapértelmezett érték"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr "Alapértelmezett paraméter érték"

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr "Alkatrészjegyzék nem szerkeszthető mert az összeállítás le van zárva"

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "Alkatrészjegyzék nem szerkeszthető mert az összeállítás változat le van zárva"

#: part/models.py:4363
msgid "Select parent part"
msgstr "Szülő alkatrész kiválasztása"

#: part/models.py:4373
msgid "Sub part"
msgstr "Al alkatrész"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr "Válaszd ki az alkatrészjegyzékben használandó alkatrészt"

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr "Alkatrészjegyzék mennyiség ehhez az alkatrészjegyzék tételhez"

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr "Ez az alkatrészjegyzék tétel opcionális"

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Ez az alkatrészjegyzék tétel fogyóeszköz (készlete nincs követve a gyártásban)"

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr "Alkatrészjegyzék tétel azonosító"

#: part/models.py:4445
msgid "BOM item notes"
msgstr "Alkatrészjegyzék tétel megjegyzései"

#: part/models.py:4451
msgid "Checksum"
msgstr "Ellenőrző összeg"

#: part/models.py:4452
msgid "BOM line checksum"
msgstr "Alkatrészjegyzék sor ellenőrző összeg"

#: part/models.py:4457
msgid "Validated"
msgstr "Jóváhagyva"

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr "Ez a BOM tétel jóvá lett hagyva"

#: part/models.py:4463
msgid "Gets inherited"
msgstr "Öröklődött"

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Ezt az alkatrészjegyzék tételt az alkatrész változatok alkatrészjegyzékei is öröklik"

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Alkatrészváltozatok készlet tételei használhatók ehhez az alkatrészjegyzék tételhez"

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr "A mennyiség egész szám kell legyen a követésre kötelezett alkatrészek esetén"

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr "Al alkatrészt kötelező megadni"

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr "Alkatrészjegyzék tétel helyettesítő"

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr "A helyettesítő alkatrész nem lehet ugyanaz mint a fő alkatrész"

#: part/models.py:4774
msgid "Parent BOM item"
msgstr "Szülő alkatrészjegyzék tétel"

#: part/models.py:4782
msgid "Substitute part"
msgstr "Helyettesítő alkatrész"

#: part/models.py:4798
msgid "Part 1"
msgstr "1.rész"

#: part/models.py:4806
msgid "Part 2"
msgstr "2.rész"

#: part/models.py:4807
msgid "Select Related Part"
msgstr "Válassz kapcsolódó alkatrészt"

#: part/models.py:4814
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr "Alkatrész kapcsolat nem hozható létre önmagával"

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr "Már létezik duplikált alkatrész kapcsolat"

#: part/serializers.py:116
msgid "Parent Category"
msgstr "Szülő Kategória"

#: part/serializers.py:117
msgid "Parent part category"
msgstr "Felsőbb szintű alkatrész kategória"

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr "Alkategóriák"

#: part/serializers.py:202
msgid "Results"
msgstr "Eredmények"

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr "Eszerint a sablon szerint rögzített eredmények száma"

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr "Beszerzési pénzneme ennek a készlet tételnek"

#: part/serializers.py:275
msgid "File is not an image"
msgstr ""

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr "Ennyi alkatrész használja ezt a sablont"

#: part/serializers.py:480
msgid "Original Part"
msgstr "Eredeti alkatrész"

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr "Válassz eredeti alkatrészt a másoláshoz"

#: part/serializers.py:486
msgid "Copy Image"
msgstr "Kép másolása"

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr "Kép másolása az eredeti alkatrészről"

#: part/serializers.py:493
msgid "Copy BOM"
msgstr "Alkatrészjegyzék másolása"

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr "Alkatrészjegyzék másolása az eredeti alkatrészről"

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr "Paraméterek másolása"

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr "Paraméterek másolása az eredeti alkatrészről"

#: part/serializers.py:507
msgid "Copy Notes"
msgstr "Megjegyzések másolása"

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr "Megjegyzések másolása az eredeti alkatrészről"

#: part/serializers.py:514
msgid "Copy Tests"
msgstr "Teszt másolása"

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr "Teszt sablonok másolása az eredeti alkatrészről"

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr "Kezdeti készlet mennyiség"

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Add meg a kezdeti készlet mennyiséget. Ha nulla akkor nem lesz készlet létrehozva."

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr "Kezdeti készlet hely"

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr "Add meg a kezdeti készlet helyét"

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr "Válassz beszállítót (hagyd üresen ha nem kell létrehozni)"

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Válassz gyártót (hagyd üresen ha nem kell létrehozni)"

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr "Gyártói cikkszám"

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr "A kiválasztott cég nem érvényes beszállító"

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr "A kiválasztott cég nem érvényes gyártó"

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr "Van már ilyen gyártói alkatrész"

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr "Van már ilyen beszállítói alkatrész"

#: part/serializers.py:907
msgid "Category Name"
msgstr "Kategória neve"

#: part/serializers.py:936
msgid "Building"
msgstr "Gyártásban"

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr "Készlet tételek"

#: part/serializers.py:968
msgid "Revisions"
msgstr "Verziók"

#: part/serializers.py:972
msgid "Suppliers"
msgstr "Beszállítók"

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Teljes készlet"

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr "Nem lefoglalt készlet"

#: part/serializers.py:992
msgid "Variant Stock"
msgstr "Variánsok Raktárkészlet"

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr "Alkatrész másolása"

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr "Kezdeti adatok másolása egy másik alkatrészről"

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr "Kezdeti készlet"

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr "Kezdeti készlet mennyiség létrehozása"

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr "Beszállító információ"

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr "Kezdeti beszállító adatok hozzáadása"

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr "Kategória paraméterek másolása"

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr "Paraméter sablonok másolása a kiválasztott alkatrész kategóriából"

#: part/serializers.py:1054
msgid "Existing Image"
msgstr "Meglévő kép"

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr "A meglévő alkatrész képfájl neve"

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr "A képfájl nem létezik"

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr "Teljes alkatrészjegyzék jóváhagyása"

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr "Gyártható"

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr "Értékesítési rendeléshez szükséges"

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr "Értékesítési rendeléshez lefoglalva"

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr "Minimum ár"

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr "Számított minimum ár felülbírálása"

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr "Minimum ár pénzneme"

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr "Maximum ár"

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr "Számított maximum ár felülbírálása"

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr "Maximum ár pénzneme"

#: part/serializers.py:1498
msgid "Update"
msgstr "Frissítés"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr "Alkatrész árak frissítése"

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Megadott pénznem átváltása {default_currency}-re sikertelen"

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr "A Minimum ár nem lehet nagyobb mint a Maximum ár"

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr "A Maximum ár nem lehet kisebb mint a Minimum ár"

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr "Szülő összeállítás kiválasztása"

#: part/serializers.py:1716
msgid "Select the component part"
msgstr "Összetevő alkatrész kijelölése"

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr "Válassz alkatrészt ahonnan az alkatrészjegyzéket másoljuk"

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr "Létező adat törlése"

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr "Meglévő alkatrészjegyzék tételek törlése a másolás előtt"

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr "Örököltekkel együtt"

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr "Sablon alkatrészektől örökölt alkatrészjegyzék tételek használata"

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr "Hibás sorok kihagyása"

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr "Engedély a hibás sorok kihagyására"

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr "Helyettesítő alkatrészek másolása"

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Helyettesítő alkatrészek másolása az alkatrészjegyzék tételek másolásakor"

#: part/tasks.py:40
msgid "Low stock notification"
msgstr "Alacsony készlet értesítés"

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "A {part.name} alkatrész rendelkezésre álló készlete a megadott minimum alá csökkent"

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr "Nincs lejárati dátuma"

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr "{abs(days_diff)} napja lejárt"

#: part/tasks.py:97
msgid "Expires today"
msgstr "Ma jár le"

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr "{days_until_expiry} nap"

#: plugin/api.py:78
msgid "Builtin"
msgstr "Beépülő"

#: plugin/api.py:92
msgid "Mandatory"
msgstr "Kötelező"

#: plugin/api.py:107
msgid "Sample"
msgstr "Minta"

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Beépítve"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr "Plugin nem törölhető mivel még aktív"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Nincs megadva művelet"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Nincs egyező művelet"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Nincs egyező vonalkód"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Egyezés vonalkódra"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Model nem támogatott"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "Model példány hiányzik"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Ez a vonalkód már egy másik tételé"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Nem található megfelelő alkatrész adat"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Nem található megfelelő beszállítói alkatrész"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Több beszállítói alkatrész található"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Beszállítói alkatrész található"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Ez a termék már bevételezve"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Több egyező sortétel is található"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Nincs egyező sortétel"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Nincs értékesítési rendelés biztosítva"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Vonalkód nem egyezik egy létező készlet tétellel sem"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Készlet tétel nem egyezik a sortétellel"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Nincs elegendő"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Készlet tétel lefoglalva egy vevői rendeléshez"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Nincs elég információ"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "A tétel bevételezéséhez további információ szükséges"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Beszerzési rendelés tétele bevételezve"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Beolvasott vonalkód"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Vonalkód generáláshoz kiválaszottt model neve"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "A vonalkódnyomtatáshoz kiválaszott model objektum azonosítója"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Tételekhez rendelendő Beszerzési Rendelés"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Bevételezési tételekhez rendelendő Beszerzési Rendelés"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Beszerzési rendelés nincs elküdve"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Bevételezés erre a készlet helyre"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Struktúrális hely nem választható"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Tételekhez rendelendő Vevői Rendelés"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr "Értékesítési rendelés nincs függőben"

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Tételekhez rendelendő vevői rendelés sortétel"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Tételekhez rendelendő vevői rendelés szállítmány"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Szállítmány kiszállítva"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Lefoglalandó mennyiség"

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr "Címkenyomtatás sikertelen"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "A címke PDF nyomtatása sikertelen"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "A címke HTML nyomtatása sikertelen"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Nincs elem a nyomtatáshoz"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Plugin neve"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Képesség típusa"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Funkció ikonja"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InventTree vonalkódok"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Alapvető vonalkód támogatást ad"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr "InvenTree fejlesztők"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "Belső Vonalkód Formátum"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "Belső vonalkód formátum kiválasztása"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON vonalkód (olvasható)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "Rövid vonalkód (tömörebb)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "Rövid Vonalkód Előtag"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "A rövid vonalkódok előtagjának beállítása hasznos lehet, ha több InvenTree példányt is használnak egy környezetben"

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr "Értékesítési rendelés automatikus kiállítása"

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr "Értékesítési rendelés automatikus kiállítása a hozzárendelt céldátum napján"

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr "Szintek"

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr "Készlet adatok"

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr "Árazási adatok"

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr "Beszállítói adatok"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr "Helyettesítő adatok"

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr "Paraméter adat"

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr "Többszintű alkatrészjegyzék exportáló"

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr "Alkatrészjegyzék szint"

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr "Beszállító {n}"

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr "Beszállítói {n} raktári cikkszám"

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr "Beszállítói {n} gyártói cikkszám"

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr "Gyártó {n}"

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr "InvenTree általános exportáló"

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr "Slack bejövő webhook URL"

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr "URL az üzenetek küldéséhez egy a slack channel-re"

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Link megnyitása"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree Pénzváltó"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Alapértelmezett pénzváltó integráció"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr "Értesítések küldése"

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF címkenyomtató"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "PDF címkék nyomtatásához beépített támogatás"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr "Debug mód"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Debug mód engedélyezése - nyers HTML-t ad vissza PDF helyett"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree címkenyomtató"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "Nyomtatási támogatást nyújt egy Berendezés által"

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr "utoljára használva"

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "Opciók"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Címke oldal méret"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Címkék kihagyása"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Hagyjon ki ennyi számú címkét a címke ívek nyomtatásakor"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Szegély"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr "Az egyes címkék körüli margó"

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Fekvő"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr "Fekvő módban nyomtatás"

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr "Oldalmargó"

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr "Inventree Címke Ív Nyomtató"

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr "Több címke egy ívre helyezése"

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr "Címke túl nagy a lapmérethez képest"

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr "Nem készült címke"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr "Gyártói Integráció - Digikey"

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr "DigiKey vonalkódok támogatása"

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr "A 'DigiKey' beszállító"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr "Gyártói Integráció - LCSC"

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr "LCSC vonalkódok támogatása"

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr "Az 'LCSC' beszállító"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr "Gyártói Integráció - Mouser"

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr "Mouser vonalkódok támogatása"

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr "A 'Mouser' beszállító"

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr "Gyártói Integráció - TME"

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr "TME vonalkódok támogatása"

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr "A 'TME' beszállító"

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr "Csak a személyzeti felhasználók adminisztrálhatják a pluginokat"

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr "Plugin telepítés letiltva"

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr "Plugin telepítése sikeres"

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Plugin telepítve ide: {path}"

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr "Ez a plugin nem található a tárolóban"

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr "A plugin nem egy csomagolt plugin"

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr "Plugin csomag neve nem található"

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr "Plugin eltávolítás letiltva"

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "Plugin nem eltávolítható mivel még aktív"

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr "Plugin eltávolítása sikeres"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Plugin beállítás"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Plugin beállítások"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Plugin kulcsa"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "PluginNeve a pluginnak"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Csomag neve"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "A telepített csomag neve, ha a plugin a PIP-el lett telepítve"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Aktív-e a plugin"

#: plugin/models.py:175
msgid "Sample plugin"
msgstr "Példa plugin"

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr "Beépített plugin"

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr "Kötelező plugin"

#: plugin/models.py:209
msgid "Package Plugin"
msgstr "Csomag plugin"

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr "Bővítmény"

#: plugin/plugin.py:384
msgid "No author found"
msgstr "Nincs szerző"

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "A '{p}' plugin nem kompatibilis az aktuális applikáció verzióval {v}"

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "A pluginhoz minimum {v} verzió kell"

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "A pluginhoz maximum {v} verzió kell"

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr "Beszerzési rendelések engedélyezése"

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr "Beszerzési rendelések funkcióinak engedélyezése az InvenTree felületén"

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr "API kulcs"

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr "Kulcs szükséges a külső API eléréséhez"

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr "Numerikus"

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr "Egy numerikus beállítás"

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr "Választás beállításai"

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr "Egy beállítás több választási lehetőséggel"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Minta árfolyamváltó plugin"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree fejlesztők"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Alkatrész panel"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Forrás fájl"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Forrás URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Csomag forrása - ez lehet egy registry vagy VCS útvonal"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Plugin csomag neve - verzió megjelölést is tartalmazhat"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Verzió"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Verzió azonosító a pluginhoz. Hagyd üresen a legújabb verzióhoz."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Bővítmény telepítésének megerősítése"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Ez telepíti ezt a plugint az aktuális példányra. A példány karbantartási módba megy."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Tlepítés nincs megerősítve"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Vagy csomag nevet vagy URL-t meg kell adni"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Teljes újratöltés"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "A plugin tárolók teljes újratöltése"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Kényszerített újratöltés"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Akkor is töltse újra a plugin tárolót ha már be lett töltve"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Pluginok begyűjtése"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Pluginok begyűjtése és a tárolóhoz adása"

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr "Plugin aktiválása"

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr "Plugin bekapcsolása"

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr "Konfiguráció törlése"

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr "Plugin konfiguráció törlése az adatbázisból"

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr "Tételek"

#: report/api.py:114
msgid "Plugin not found"
msgstr "Plugin nem található"

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr "Plugin nem támogatja a címkenyomtatást"

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr "Érvénytelen címke méretek"

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr "Nincs érvényes tétel megadva a sablonhoz"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Jogi információk"

#: report/helpers.py:46
msgid "Letter"
msgstr "„Letter” méret"

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr "Ilyen nevű Sablon fájl már létezik"

#: report/models.py:217
msgid "Template name"
msgstr "Sablon neve"

#: report/models.py:223
msgid "Template description"
msgstr "Sablon leírása"

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr "Verziószám (automatikusan nő)"

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:281
msgid "Filename Pattern"
msgstr "Fájlnév minta"

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr "Minta a fájlnevek előállításához"

#: report/models.py:287
msgid "Template is enabled"
msgstr "Sablon engedélyezve"

#: report/models.py:294
msgid "Target model type for template"
msgstr "A sablon által célzott model típus"

#: report/models.py:314
msgid "Filters"
msgstr "Szűrők"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "Sablon lekérdezés szűrők (vesszővel elválasztott kulcs=érték párok)"

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "Sablon file"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr "Lapméret a PDF riportokhoz"

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr "Jelentés fekvő nézetben"

#: report/models.py:393
msgid "Merge"
msgstr "Összevonás"

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr "Szintaxis hiba a sablonban"

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Szélesség [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Címke szélessége, mm-ben"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Magasság [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Címke magassága, mm-ben"

#: report/models.py:780
msgid "Error printing labels"
msgstr "Címkenyomtatási hiba"

#: report/models.py:799
msgid "Snippet"
msgstr "Részlet"

#: report/models.py:800
msgid "Report snippet file"
msgstr "Riport részlet fájl"

#: report/models.py:807
msgid "Snippet file description"
msgstr "Részlet fájl leírása"

#: report/models.py:825
msgid "Asset"
msgstr "Eszköz"

#: report/models.py:826
msgid "Report asset file"
msgstr "Riport asset fájl"

#: report/models.py:833
msgid "Asset file description"
msgstr "Asset fájl leírása"

#: report/serializers.py:96
msgid "Select report template"
msgstr "Riport sablon kiválasztása"

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr "A jelentésben levő tételek azonosítója"

#: report/serializers.py:137
msgid "Select label template"
msgstr "Címke sablon választás"

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr "Nyomtató plugin"

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr "Címkenyomtató plugin kiválasztása"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR kód"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR kód"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Alkatrészjegyzék"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Szükséges alapanyagok"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Alkatrész képe"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Kiküldve"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Szükséges ehhez"

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Kiállította"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Beszállító törölve lett"

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr "Rendelés részletei"

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Egységár"

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Egyéb tételek"

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Összesen"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr "Sorozatszám"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Foglalások"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr "Köteg"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Készlethely tételek"

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Készlet tétel teszt riport"

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr "Beépített tételek"

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Sorozatszám"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Teszt eredmények"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Teszt"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Sikeres"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Sikertelen"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Nincs eredmény (szükséges)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Nincs eredmény"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "A fájl nem létezik"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "A képfile nem található"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image elem csak alkatrész példánynál használható"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "company_image elem csak cég példánynál használható"

#: stock/api.py:283
msgid "Filter by location depth"
msgstr "Hely mélységre szűrés"

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr "Csúcs készlethelyre szűrés"

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr "Szűrt eredmények tartalmazzák az alhelyeket"

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr "Szülő hely"

#: stock/api.py:340
msgid "Filter by parent location"
msgstr "Szülő helyre szűrés"

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:594
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr "Alkatrész IPN (regexp)"

#: stock/api.py:623
msgid "Minimum stock"
msgstr "Minimális készlet"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "Maximális készlet"

#: stock/api.py:630
msgid "Status Code"
msgstr "Státuszkód"

#: stock/api.py:670
msgid "External Location"
msgstr "Külső hely"

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:868
msgid "Part Tree"
msgstr "Alkatrész fa"

#: stock/api.py:890
msgid "Updated before"
msgstr ""

#: stock/api.py:894
msgid "Updated after"
msgstr "Frissítve ez után"

#: stock/api.py:898
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:902
msgid "Stocktake After"
msgstr ""

#: stock/api.py:907
msgid "Expiry date before"
msgstr "Lejárat előtt"

#: stock/api.py:911
msgid "Expiry date after"
msgstr "Lejárat után"

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr "Állott"

#: stock/api.py:1015
msgid "Quantity is required"
msgstr "Mennyiség megadása kötelező"

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr "Egy érvényes alkatrészt meg kell adni"

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr "A megadott beszállítói alkatrész nem létezik"

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "A beszállítói alkatrészhez van megadva csomagolási mennyiség, de a use_pack_size flag nincs beállítva"

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Sorozatszámot nem lehet megadni nem követésre kötelezett alkatrész esetén"

#: stock/models.py:72
msgid "Stock Location type"
msgstr "Készlethely típus"

#: stock/models.py:73
msgid "Stock Location types"
msgstr "Készlethely típusok"

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Alapértelmezett ikon azokhoz a helyekhez, melyeknek nincs ikonja beállítva (válaszható)"

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr "Készlet hely"

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr "Készlethelyek"

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Tulajdonos"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Tulajdonos kiválasztása"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "A szerkezeti raktári helyekre nem lehet direktben raktározni, csak az al-helyekre."

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr "Külső"

#: stock/models.py:227
msgid "This is an external stock location"
msgstr "Ez egy külső készlethely"

#: stock/models.py:233
msgid "Location type"
msgstr "Helyszín típusa"

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr "Tárolóhely típus"

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Nem lehet ezt a raktári helyet szerkezetivé tenni, mert már vannak itt tételek!"

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr "a(z) {field} nem létezik"

#: stock/models.py:608
msgid "Part must be specified"
msgstr ""

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr "A szerkezeti raktári helyre nem lehet készletet felvenni!"

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr "Virtuális alkatrészből nem lehet készletet létrehozni"

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "A beszállítói alkatrész típusa ('{self.supplier_part.part}') mindenképpen {self.part} kellene, hogy legyen"

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr "Mennyiség 1 kell legyen a sorozatszámmal rendelkező tételnél"

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Nem lehet sorozatszámot megadni ha a mennyiség több mint egy"

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr "A tétel nem tartozhat saját magához"

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr "A tételnek kell legyen gyártási azonosítója ha az is_bulding igaz"

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr "Gyártási azonosító nem ugyanarra az alkatrész objektumra mutat"

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr "Szülő készlet tétel"

#: stock/models.py:1028
msgid "Base part"
msgstr "Kiindulási alkatrész"

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr "Válassz egy egyező beszállítói alkatrészt ehhez a készlet tételhez"

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr "Hol található ez az alkatrész?"

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr "A csomagolása ennek a készlet tételnek itt van tárolva"

#: stock/models.py:1064
msgid "Installed In"
msgstr "Beépítve ebbe"

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr "Ez a tétel be van építve egy másik tételbe?"

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr "Sorozatszám ehhez a tételhez"

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr "Batch kód ehhez a készlet tételhez"

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr "Készlet mennyiség"

#: stock/models.py:1120
msgid "Source Build"
msgstr "Forrás gyártás"

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr "Gyártás ehhez a készlet tételhez"

#: stock/models.py:1130
msgid "Consumed By"
msgstr "Felhasználva ebben"

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr "Felhasználva ebben a gyártásban"

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr "Forrás beszerzési rendelés"

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr "Beszerzés ehhez a készlet tételhez"

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr "Cél vevői rendelés"

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Készlet tétel lejárati dátuma. A készlet lejártnak tekinthető ezután a dátum után"

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr "Törlés ha kimerül"

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr "Készlet tétel törlése ha kimerül"

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr "Egy egység beszerzési ára a beszerzés időpontjában"

#: stock/models.py:1234
msgid "Converted to part"
msgstr "Alkatrésszé alakítva"

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr "Az alkatrész nem követésre kötelezett"

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr "Mennyiség egész szám kell legyen"

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "A mennyiség nem haladhatja meg az elérhető készletet ({self.quantity})"

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr "A mennyiség nem egyezik a megadott sorozatszámok számával"

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr "Ez a Teszt sablon nem létezik"

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr "Készlet tétel hozzárendelve egy vevői rendeléshez"

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr "Készlet tétel beépül egy másikba"

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr "A készlet tétel más tételeket tartalmaz"

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr "Készlet tétel hozzárendelve egy vevőhöz"

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr "Készlet tétel gyártás alatt"

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr "Követésre kötelezett készlet nem vonható össze"

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr "Duplikált készlet tételek vannak"

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr "A készlet tétel ugyanarra az alkatrészre kell vonatkozzon"

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr "A készlet tétel ugyanarra a beszállítói alkatrészre kell vonatkozzon"

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr "Készlet tételek állapotainak egyeznie kell"

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Készlet tétel nem mozgatható mivel nincs készleten"

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr "Készlettörténet"

#: stock/models.py:2847
msgid "Entry notes"
msgstr "Bejegyzés megjegyzései"

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr "Készlet Tétel Ellenőrzés Eredménye"

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr "Ehhez a teszthez meg kell adni értéket"

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr "Ehhez a teszthez fel kell tölteni mellékletet"

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr "A teszt eredménye érvénytelen"

#: stock/models.py:2951
msgid "Test result"
msgstr "Teszt eredménye"

#: stock/models.py:2958
msgid "Test output value"
msgstr "Teszt kimeneti értéke"

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Teszt eredmény melléklet"

#: stock/models.py:2970
msgid "Test notes"
msgstr "Tesztek megjegyzései"

#: stock/models.py:2978
msgid "Test station"
msgstr "Teszt állomás"

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr "A tesztet elvégző tesztállomás azonosítója"

#: stock/models.py:2985
msgid "Started"
msgstr "Elkezdődött"

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr "A teszt indításának időpontja"

#: stock/models.py:2992
msgid "Finished"
msgstr "Befejezve"

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr "A teszt befejezésének időpontja"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "Generált köteg kód"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "Gyártási rendelés kiválasztása"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "Készlettétel amihez a köteg kódot generáljuk"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "Készlethely amihez a köteg kódot generáljuk"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "Alkatrész amihez a köteg kódot generáljuk"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "Beszerzési rendelés kiválasztása"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "Adja meg a mennyiséget a köteg kódhoz"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "Generált sorozatszám"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "Válassza ki az alkatrészt amihez sorozatszámot akar generálni"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "Hány sorozatszámot generáljunk"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "Az eredmény Teszt sablonja"

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr "Sablon azonosító vagy Teszt név szükséges"

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr "A tesztet nem lehet a kezdésnél hamarabb befejezni"

#: stock/serializers.py:451
msgid "Parent Item"
msgstr "Szülő tétel"

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr "Szülő készlet tétel"

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Csomagolási mennyiség használata: a megadott mennyiség ennyi csomag"

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr "Írd be a sorozatszámokat az új tételekhez"

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr "Beszállítói Cikkszám"

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr "Lejárt"

#: stock/serializers.py:652
msgid "Child Items"
msgstr "Gyermek tételek"

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr "Nyilvántartott tételek"

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Készlet tétel beszerzési ára, per darab vagy csomag"

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr "Add meg hány készlet tételt lássunk el sorozatszámmal"

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "A mennyiség nem lépheti túl a rendelkezésre álló készletet ({q})"

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr "Cél készlet hely"

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr "Sorozatszámokat nem lehet hozzárendelni ehhez az alkatrészhez"

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr "A sorozatszámok már léteznek"

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr "Válaszd ki a beépítésre szánt készlet tételt"

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr "Beépítendő mennyiség"

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr "Adja meg a beépítendő mennyiséget"

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr "Tranzakció megjegyzés hozzáadása (opcionális)"

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr "A beépítendő mennyiség legalább 1 legyen"

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr "Készlet tétel nem elérhető"

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr "A kiválasztott alkatrész nincs az alkatrészjegyzékben"

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr "A beépítendő mennyiség nem haladhatja meg az elérhető mennyiséget"

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr "Cél hely a kiszedett tételeknek"

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr "Válassz alkatrészt amire konvertáljuk a készletet"

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr "A kiválasztott alkatrész nem megfelelő a konverzióhoz"

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Készlet tétel hozzárendelt beszállítói alkatrésszel nem konvertálható"

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr "Készlet tétel státusz kódja"

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr "Válaszd ki a státuszváltásra szánt készlet tételeket"

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr "Nincs készlet tétel kiválasztva"

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr "Alhelyek"

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr "Felsőbb szintű készlet hely"

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr "Az alkatrésznek értékesíthetőnek kell lennie"

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr "A tétel egy vevő rendeléshez foglalt"

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr "A tétel egy gyártási utasításhoz foglalt"

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr "Vevő akihez rendeljük a készlet tételeket"

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr "A kiválasztott cég nem egy vevő"

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr "Készlet hozzárendelés megjegyzései"

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr "A készlet tételek listáját meg kell adni"

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr "Készlet összevonás megjegyzései"

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr "Nem egyező beszállítók megengedése"

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Különböző beszállítói alkatrészekből származó készletek összevonásának engedélyezése"

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr "Nem egyező állapotok megjelenítése"

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr "Különböző állapotú készletek összevonásának engedélyezése"

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr "Legalább két készlet tételt meg kell adni"

#: stock/serializers.py:1551
msgid "No Change"
msgstr "Nincs változás"

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr "Készlet tétel elsődleges kulcs értéke"

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr "Készlet tranzakció megjegyzései"

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr "Következő sorozatszám"

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "Rendben"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Ellenőrizendő"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Sérült"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Megsemmisült"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Elutasított"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "Karanténban"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Örökölt készlet követési bejegyzés"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Készlet tétel létrehozva"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Szerkeszett készlet tétel"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Hozzárendelt sorozatszám"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Készlet leleltározva"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Készlet manuálisan hozzáadva"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Készlet manuálisan elvéve"

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr "Hely megváltozott"

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr "Készletadatok frissítve"

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr "Gyártmányba beépült"

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr "Gyártmányból eltávolítva"

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr "Beépült összetevő tétel"

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr "Eltávolított összetevő tétel"

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr "Szülő tételből szétválasztva"

#: stock/status_codes.py:72
msgid "Split child item"
msgstr "Szétválasztott gyermek tétel"

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr "Összevont készlet tétel"

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr "Alkatrészváltozattá alakítva"

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr "Gyártási utasítás kimenete elkészült"

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr "Gyártási utasítás kimenete kész"

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr "Gyártási utasítás kimenete elutasítva"

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr "Gyártásra felhasználva"

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr "Vevői rendelésre kiszállítva"

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr "Megrendelésre érkezett"

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr "Visszavéve"

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr "Vevőnek kiszállítva"

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr "Vevőtől visszaérkezett"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Hozzáférés megtagadva"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Nincs jogosultságod az oldal megtekintéséhez."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Hitelesítési hiba"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Kijelentkeztél az InvenTreeből."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Az oldal nem található"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "A kért oldal nem létezik"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Belső kiszolgáló hiba"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "A(z) %(inventree_title)s kiszolgáló belső hibát jelzett"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Nézd meg az admin felületen lévő hibanaplót bővebb információkért"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Az oldal karbantartás alatt"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Az oldal jelenleg karbantartás alatt van, hamarosan újra használható lesz!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Kiszolgáló újraindítása szükséges"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Egy olyan konfigurációs opció megváltozott ami a kiszolgáló újraindítását igényli"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Vedd fel a kapcsolatot a rendszergazdával további információkért"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Függőben levő adatbázis migrációk"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Törődést igénylő függőben levő adatbázis migrációk találhatók"

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr "Konfigurációs hiba"

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Klikk a következő linkre a rendelés megjelenítéséhez"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Készlet szükséges a következő gyártási utasításhoz"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "%(build)s gyártási utasítás - %(quantity)s x %(part)s alkatrész gyártása"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klikk a következő linkre a gyártási utasítás megjelenítéséhez"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "A következő alkatrészek szükséges készlete alacsony"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Szükséges mennyiség"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Ezért kapod ezt a levelet mert értesítést kértél erre az alkatrészre "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klikk a következő linkre az alkatrész megjelenítéséhez"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimum mennyiség"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Felhasználók"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Válaszd ki mely felhasználók tartoznak ehhez a csoporthoz"

#: users/admin.py:137
msgid "Personal info"
msgstr "Személyes adatok"

#: users/admin.py:139
msgid "Permissions"
msgstr "Jogosultságok"

#: users/admin.py:142
msgid "Important dates"
msgstr "Fontos dátumok"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "A token visszavonva"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "A token lejárt"

#: users/models.py:100
msgid "API Token"
msgstr "API Token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API Tokenek"

#: users/models.py:137
msgid "Token Name"
msgstr "Token név"

#: users/models.py:138
msgid "Custom token name"
msgstr "Egyedi token név"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Token lejárati dátum"

#: users/models.py:152
msgid "Last Seen"
msgstr "Utolsó tevékenység"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Token utolsó használata"

#: users/models.py:157
msgid "Revoked"
msgstr "Visszavonva"

#: users/models.py:235
msgid "Permission set"
msgstr "Jogosultságok"

#: users/models.py:244
msgid "Group"
msgstr "Csoport"

#: users/models.py:248
msgid "View"
msgstr "Nézet"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Jogosultság tételek megtekintéséhez"

#: users/models.py:252
msgid "Add"
msgstr "Hozzáad"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Jogosultság tételek hozzáadásához"

#: users/models.py:256
msgid "Change"
msgstr "Módosítás"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Jogosultság tételek szerkesztéséhez"

#: users/models.py:262
msgid "Delete"
msgstr "Törlés"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Jogosultság tételek törléséhez"

#: users/models.py:501
msgid "Bot"
msgstr "Robot"

#: users/models.py:502
msgid "Internal"
msgstr "Belső"

#: users/models.py:504
msgid "Guest"
msgstr "Vendég"

#: users/models.py:513
msgid "Language"
msgstr "Nyelv"

#: users/models.py:514
msgid "Preferred language for the user"
msgstr "Felhasználó által preferált nyelv"

#: users/models.py:519
msgid "Theme"
msgstr "Téma"

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr "Widgetek"

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr "Megjelenítendő név"

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:541
msgid "Position"
msgstr "Pozíció"

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr ""

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr "Felhasználó típusa"

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr "Szervezet"

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr "Elsődleges csoport"

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr "Adminisztrátor"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr "Beszerzési rendelések"

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr "Vevői rendelések"

#: users/ruleset.py:34
msgid "Return Orders"
msgstr "Visszavételek"

#: users/serializers.py:196
msgid "Username"
msgstr "Felhasználónév"

#: users/serializers.py:199
msgid "First Name"
msgstr "Keresztnév"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "A felhasználó keresztneve"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Vezetéknév"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "A felhasználó vezetékneve"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "A felhasználó e-mail címe"

#: users/serializers.py:326
msgid "Staff"
msgstr "Személyzet"

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr "Van-e a felhasználónak személyzeti jogosultsága"

#: users/serializers.py:332
msgid "Superuser"
msgstr "Rendszergazda"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr "A felhasználó rendszergazda-e"

#: users/serializers.py:336
msgid "Is this user account active"
msgstr "Aktív a felhasználói fiók"

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr "Jelszó"

#: users/serializers.py:377
msgid "Password for the user"
msgstr "Felhasználó jelszava"

#: users/serializers.py:383
msgid "Override warning"
msgstr "Figyelmezetés felülbírálása"

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "A fiókod sikeresen létrejött."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr "Kérlek használd a jelszó visszállítás funkciót a belépéshez"

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Üdvözlet az InvenTree-ben"

