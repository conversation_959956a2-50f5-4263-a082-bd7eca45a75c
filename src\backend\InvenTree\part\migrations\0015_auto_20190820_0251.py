# Generated by Django 2.2.4 on 2019-08-20 02:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0014_partparameter'),
    ]

    operations = [
        migrations.CreateModel(
            name='PartParameterTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Parameter Name', max_length=100)),
                ('units', models.CharField(blank=True, help_text='Parameter Units', max_length=25)),
            ],
            options={
                'verbose_name': 'Part Parameter Template',
            },
        ),
        migrations.RemoveField(
            model_name='partparameter',
            name='name',
        ),
        migrations.AlterField(
            model_name='partparameter',
            name='data',
            field=models.CharField(help_text='Parameter Value', max_length=500),
        ),
        migrations.AddField(
            model_name='partparameter',
            name='template',
            field=models.ForeignKey(default=1, help_text='Parameter Template', on_delete=django.db.models.deletion.CASCADE, related_name='instances', to='part.PartParameterTemplate'),
            preserve_default=False,
        ),
    ]
