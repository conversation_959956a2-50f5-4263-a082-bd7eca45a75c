---
title: High level roadmap
---

## General goals

## High level Epics

### 1.0

Smaller items can be viewed [on the milestone](https://github.com/inventree/InvenTree/issues?q=is%3Aissue%20milestone%3A1.0.0).

Aiming to stabelise several aspects of the software:

- Only shipping the new frontend and removing reliance on templating
- Stabelize API for client generation
- Making data import- / export-mechanisms more stable
- Updating and Re-Organising documentation to enable CII best practices compliance

### 2.0

Smaller items can be viewed [on the milestone](https://github.com/inventree/InvenTree/issues?q=is%3Aissue%20milestone%3A2.0.0).

*Proposed* goals:

- Reorganise permission system to support more entrerprise structures and reduce unneeded permissions [EPIC](https://github.com/inventree/InvenTree/issues/7466)
- Add generalised file handling [EPIC](https://github.com/inventree/InvenTree/issues/5703)

### Future

There are several epics that target [the horizion](https://github.com/inventree/InvenTree/issues?q=is%3Aissue%20state%3Aopen%20type%3AEpic).

## Non-Goals

TBD
