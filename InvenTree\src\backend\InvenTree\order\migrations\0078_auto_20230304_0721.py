# Generated by Django 3.2.18 on 2023-03-04 07:21

import InvenTree.fields
from django.db import migrations
import djmoney.models.fields
import djmoney.models.validators


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0077_auto_20230129_0154'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='total_price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Total price for this order', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Total Price'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='total_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='total_price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Total price for this order', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Total Price'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='total_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3),
        ),
    ]
