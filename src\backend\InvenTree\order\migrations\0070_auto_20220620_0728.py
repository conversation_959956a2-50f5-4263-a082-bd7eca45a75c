# Generated by Django 3.2.13 on 2022-06-20 07:28

import InvenTree.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0069_auto_20220524_0508'),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseorder',
            name='notes',
            field=InvenTree.fields.InvenTreeNotesField(blank=True, help_text='Order notes', max_length=50000, null=True, verbose_name='Notes'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='notes',
            field=InvenTree.fields.InvenTreeNotesField(blank=True, help_text='Order notes', max_length=50000, null=True, verbose_name='Notes'),
        ),
        migrations.AlterField(
            model_name='salesordershipment',
            name='notes',
            field=InvenTree.fields.InvenTreeNotesField(blank=True, help_text='Shipment notes', max_length=50000, null=True, verbose_name='Notes'),
        ),
    ]
