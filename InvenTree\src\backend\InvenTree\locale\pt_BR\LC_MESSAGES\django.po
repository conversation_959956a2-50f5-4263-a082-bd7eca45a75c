msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-20 05:37+0000\n"
"PO-Revision-Date: 2025-08-20 05:40\n"
"Last-Translator: \n"
"Language-Team: Portuguese, Brazilian\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: pt-BR\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Você deve habilitar a autenticação de dois fatores antes de fazer qualquer coisa."

#: InvenTree/api.py:356
msgid "API endpoint not found"
msgstr "API endpoint não encontrado"

#: InvenTree/api.py:433
msgid "List of items or filters must be provided for bulk operation"
msgstr "A lista de itens ou filtros devem ser fornecidas para operação em massa"

#: InvenTree/api.py:440
msgid "Items must be provided as a list"
msgstr "Os itens devem ser fornecidos como lista"

#: InvenTree/api.py:448
msgid "Invalid items list provided"
msgstr "Lista de itens inválida fornecida"

#: InvenTree/api.py:454
msgid "Filters must be provided as a dict"
msgstr "Filtros devem ser fornecidos como"

#: InvenTree/api.py:461
msgid "Invalid filters provided"
msgstr "Filtros inválidos fornecidos"

#: InvenTree/api.py:466
msgid "All filter must only be used with true"
msgstr "Todos os filtros devem ser usados apenas como verdadeiro"

#: InvenTree/api.py:471
msgid "No items match the provided criteria"
msgstr "Nenhum item corresponde com os critérios fornecidos"

#: InvenTree/api.py:493
msgid "No data provided"
msgstr "Nenhum dado fornecido"

#: InvenTree/api.py:743
msgid "User does not have permission to view this model"
msgstr "O usuário não tem permissão para visualizar esse modelo"

#: InvenTree/auth_overrides.py:58
msgid "Email (again)"
msgstr "Email (novamente)"

#: InvenTree/auth_overrides.py:62
msgid "Email address confirmation"
msgstr "E-mail de confirmação"

#: InvenTree/auth_overrides.py:85
msgid "You must type the same email each time."
msgstr "Você deve digitar o mesmo e-mail todas às vezes."

#: InvenTree/auth_overrides.py:127 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "O endereço de e-mail fornecido não é válido."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "O domínio de e-mail fornecido não foi aprovado."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Unidade fornecida inválida ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Nenhum valor fornecido"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Não foi possível converter {original} para {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:552 order/models.py:713 order/models.py:1005
msgid "Invalid quantity provided"
msgstr "Quantidade inválida"

#: InvenTree/exceptions.py:135
msgid "Error details can be found in the admin panel"
msgstr "Detalhes do erro podem ser encontrados no painel de administração"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Informe a data"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Valor decimal inválido"

#: InvenTree/fields.py:210 InvenTree/models.py:1061 build/serializers.py:507
#: build/serializers.py:578 build/serializers.py:1789 company/models.py:875
#: order/models.py:1734
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2846 stock/models.py:2970 stock/serializers.py:740
#: stock/serializers.py:916 stock/serializers.py:1058 stock/serializers.py:1372
#: stock/serializers.py:1461 stock/serializers.py:1660
msgid "Notes"
msgstr "Observações"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "O valor '{name}' não aparece no formato padrão"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "O valor fornecido não corresponde ao padrão exigido: "

#: InvenTree/helpers.py:556
msgid "Cannot serialize more than 1000 items at once"
msgstr "Não é possível serializar mais de 1000 itens de uma vez"

#: InvenTree/helpers.py:562
msgid "Empty serial number string"
msgstr "Número serial em branco"

#: InvenTree/helpers.py:591
msgid "Duplicate serial"
msgstr "Número serial duplicado"

#: InvenTree/helpers.py:623 InvenTree/helpers.py:666 InvenTree/helpers.py:684
#: InvenTree/helpers.py:691 InvenTree/helpers.py:710
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Grupo invalido:{group}"

#: InvenTree/helpers.py:654
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Intervalo do grupo {group} excede a quantidade permitida ({expected_quantity})"

#: InvenTree/helpers.py:720
msgid "No serial numbers found"
msgstr "Nenhum número de série foi encontrado"

#: InvenTree/helpers.py:727
#, python-brace-format
msgid "Number of unique serial numbers ({n}) must match quantity ({q})"
msgstr "O número de números seriais únicos ({n}) deve corresponder a quantidade ({q})"

#: InvenTree/helpers.py:857
msgid "Remove HTML tags from this value"
msgstr "Remover as \"tags\" HTML deste valor"

#: InvenTree/helpers.py:936
msgid "Data contains prohibited markdown content"
msgstr "Os dados contêm conteúdo de marcação proibido"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Erro de conexão"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "O servidor respondeu com código de estado inválido"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Ocorreu uma exceção"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "O servidor respondeu com valor inválido do tamanho de conteúdo"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr ""

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "O download da imagem excedeu seu tamanho máximo"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "O servidor remoto retornou uma resposta vazia"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "A URL fornecida não é um arquivo de imagem válido"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Árabe"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Búlgaro"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tcheco"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Dinamarquês"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Alemão"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Grego"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Inglês"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Espanhol"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Espanhol (mexicano)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr ""

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persa"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finlandês"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Francês"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebraico"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Húngaro"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiano"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japonês"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Coreano"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Lituano"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Letão"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Holandês"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norueguês"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polonês"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Português"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Português (Brasil)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Romeno"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russo"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Eslovaco"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Esloveno"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Sérvio"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Sueco"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Tailandês"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turco"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ucraniano"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamita"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Chinês (simplificado)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Chinês (tradicional)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Entrar no aplicativo"

#: InvenTree/magic_login.py:41 company/models.py:170 users/serializers.py:207
msgid "Email"
msgstr "E-mail"

#: InvenTree/models.py:109
msgid "Error running plugin validation"
msgstr ""

#: InvenTree/models.py:186
msgid "Metadata must be a python dict object"
msgstr "Metadados deve ser um objeto dict python"

#: InvenTree/models.py:192
msgid "Plugin Metadata"
msgstr "Plugin de Metadados"

#: InvenTree/models.py:193
msgid "JSON metadata field, for use by external plugins"
msgstr "Campo de metadados JSON, para uso de plugins externos"

#: InvenTree/models.py:376
msgid "Improperly formatted pattern"
msgstr "Padrão formatado incorretamente"

#: InvenTree/models.py:383
msgid "Unknown format key specified"
msgstr "Chave de formato desconhecida especificada"

#: InvenTree/models.py:389
msgid "Missing required format key"
msgstr "Chave de formato obrigatório ausente"

#: InvenTree/models.py:400
msgid "Reference field cannot be empty"
msgstr "O campo de referência não deve ficar vazio"

#: InvenTree/models.py:408
msgid "Reference must match required pattern"
msgstr "A referência deve corresponder ao padrão exigido"

#: InvenTree/models.py:439
msgid "Reference number is too large"
msgstr "O número de referência é muito longo"

#: InvenTree/models.py:737
msgid "Invalid choice"
msgstr "Escolha inválida"

#: InvenTree/models.py:850 common/models.py:1415 common/models.py:1842
#: common/models.py:2101 common/models.py:2226 common/serializers.py:523
#: company/models.py:634 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1051 part/models.py:3849 plugin/models.py:53
#: report/models.py:216 stock/models.py:85
msgid "Name"
msgstr "Nome"

#: InvenTree/models.py:856 build/models.py:251 common/models.py:170
#: common/models.py:2233 common/models.py:2346 company/models.py:562
#: company/models.py:866 order/models.py:435 order/models.py:1770
#: part/models.py:1074 part/models.py:3864 report/models.py:222
#: report/models.py:806 report/models.py:832
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:91
msgid "Description"
msgstr "Descrição"

#: InvenTree/models.py:857 stock/models.py:92
msgid "Description (optional)"
msgstr "Descrição (opcional)"

#: InvenTree/models.py:872 common/models.py:2399
msgid "Path"
msgstr "Caminho"

#: InvenTree/models.py:977
msgid "Duplicate names cannot exist under the same parent"
msgstr "Nomes duplicados não podem existir sob o mesmo parental"

#: InvenTree/models.py:1061
msgid "Markdown notes (optional)"
msgstr "Notas Markdown (opcional)"

#: InvenTree/models.py:1092
msgid "Barcode Data"
msgstr "Dados de código de barras"

#: InvenTree/models.py:1093
msgid "Third party barcode data"
msgstr "Dados de código de barras de terceiros"

#: InvenTree/models.py:1099
msgid "Barcode Hash"
msgstr "Hash de código de barras"

#: InvenTree/models.py:1100
msgid "Unique hash of barcode data"
msgstr "Hash exclusivo de dados de código de barras"

#: InvenTree/models.py:1181
msgid "Existing barcode found"
msgstr "Código de barras existente encontrado"

#: InvenTree/models.py:1263
msgid "Task Failure"
msgstr "Falha na Tarefa"

#: InvenTree/models.py:1264
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Falha na tarefa de trabalho '{f}' em segundo plano após tentativas {n}"

#: InvenTree/models.py:1291
msgid "Server Error"
msgstr "Erro de servidor"

#: InvenTree/models.py:1292
msgid "An error has been logged by the server."
msgstr "Um erro foi registrado pelo servidor."

#: InvenTree/serializers.py:69 part/models.py:4565
msgid "Must be a valid number"
msgstr "Deve ser um número válido"

#: InvenTree/serializers.py:111 company/models.py:221 part/models.py:3284
msgid "Currency"
msgstr "Moeda"

#: InvenTree/serializers.py:114 part/serializers.py:1356
msgid "Select currency from available options"
msgstr "Selecione a moeda entre as opções disponíveis"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Valor inválido"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Imagem remota"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL do arquivo da imagem remota"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Baixar imagens de URL remota não está habilitado"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Falha ao baixar a imagem da URL remota"

#: InvenTree/tasks.py:573
msgid "Update Available"
msgstr "Atualização disponível"

#: InvenTree/tasks.py:574
msgid "An update for InvenTree is available"
msgstr "Uma atualização para o InvenTree está disponível"

#: InvenTree/validators.py:28
msgid "Invalid physical unit"
msgstr "Unidade física inválida"

#: InvenTree/validators.py:34
msgid "Not a valid currency code"
msgstr "O código de moeda não é válido"

#: build/api.py:41 order/api.py:107 order/api.py:266 order/serializers.py:129
msgid "Order Status"
msgstr "Situação de pedido"

#: build/api.py:67 build/models.py:263
msgid "Parent Build"
msgstr "Produção Progenitora"

#: build/api.py:71 build/api.py:792 order/api.py:541 order/api.py:761
#: order/api.py:1165 order/api.py:1414 stock/api.py:550
msgid "Include Variants"
msgstr "Incluir Variáveis"

#: build/api.py:87 build/api.py:458 build/api.py:806 build/models.py:269
#: build/serializers.py:1242 build/serializers.py:1388
#: build/serializers.py:1450 company/models.py:1085 company/serializers.py:456
#: order/api.py:294 order/api.py:298 order/api.py:920 order/api.py:1178
#: order/api.py:1181 order/models.py:1883 order/models.py:2053
#: order/models.py:2054 part/api.py:1209 part/api.py:1212 part/api.py:1278
#: part/api.py:1554 part/models.py:472 part/models.py:3295 part/models.py:3438
#: part/models.py:3496 part/models.py:3517 part/models.py:3539
#: part/models.py:3678 part/models.py:4045 part/models.py:4362
#: part/models.py:4781 part/serializers.py:1881
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:34
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:563 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:445 stock/serializers.py:624 stock/serializers.py:949
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
#: templates/email/stale_stock_notification.html:17
msgid "Part"
msgstr "Parte"

#: build/api.py:107 build/api.py:110 part/api.py:1292 part/api.py:1565
#: part/models.py:1092 part/models.py:3567 part/models.py:4161 stock/api.py:846
msgid "Category"
msgstr "Categoria"

#: build/api.py:118 build/api.py:122
msgid "Ancestor Build"
msgstr "Construção de Ancestrais"

#: build/api.py:139 order/api.py:125
msgid "Assigned to me"
msgstr "Atribuído a mim"

#: build/api.py:154
msgid "Assigned To"
msgstr "Atribuído a"

#: build/api.py:189
msgid "Created before"
msgstr "Criado antes"

#: build/api.py:193
msgid "Created after"
msgstr "Criado após"

#: build/api.py:197
msgid "Has start date"
msgstr "Tem a data inicial"

#: build/api.py:205
msgid "Start date before"
msgstr "Data inicial antes"

#: build/api.py:209
msgid "Start date after"
msgstr "Data de início após"

#: build/api.py:213
msgid "Has target date"
msgstr "Tem data limite"

#: build/api.py:221
msgid "Target date before"
msgstr "Data limite antes"

#: build/api.py:225
msgid "Target date after"
msgstr "Data limite depois"

#: build/api.py:229
msgid "Completed before"
msgstr "Concluído antes"

#: build/api.py:233
msgid "Completed after"
msgstr "Concluído após"

#: build/api.py:236 order/api.py:222
msgid "Min Date"
msgstr "Data Mínima"

#: build/api.py:259 order/api.py:241
msgid "Max Date"
msgstr "Data máxima"

#: build/api.py:284 build/api.py:287 part/api.py:220
msgid "Exclude Tree"
msgstr "Excluir árvore"

#: build/api.py:397
msgid "Build must be cancelled before it can be deleted"
msgstr "A compilação deve ser cancelada antes de ser excluída"

#: build/api.py:441 build/serializers.py:1404 part/models.py:4396
msgid "Consumable"
msgstr "Consumível"

#: build/api.py:444 build/serializers.py:1407 part/models.py:4390
msgid "Optional"
msgstr "Opcional"

#: build/api.py:447 build/serializers.py:1441 common/setting/system.py:457
#: part/models.py:1223 part/serializers.py:1701 part/serializers.py:1710
#: stock/api.py:616
msgid "Assembly"
msgstr "Montagem"

#: build/api.py:450
msgid "Tracked"
msgstr "Rastreado"

#: build/api.py:453 build/serializers.py:1410 part/models.py:1241
msgid "Testable"
msgstr "Testável"

#: build/api.py:463 order/api.py:984
msgid "Order Outstanding"
msgstr "Pedido pendente"

#: build/api.py:473 build/serializers.py:1471 order/api.py:943
msgid "Allocated"
msgstr "Alocado"

#: build/api.py:482 build/models.py:1623 build/serializers.py:1423
msgid "Consumed"
msgstr ""

#: build/api.py:491 company/models.py:930 company/serializers.py:451
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Disponível"

#: build/api.py:829 build/models.py:116 order/models.py:1916
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Ordem da compilação"

#: build/api.py:843 build/api.py:847 build/serializers.py:370
#: build/serializers.py:495 build/serializers.py:565 build/serializers.py:1262
#: build/serializers.py:1266 order/api.py:1225 order/api.py:1230
#: order/serializers.py:768 order/serializers.py:908 order/serializers.py:2015
#: stock/serializers.py:110 stock/serializers.py:628 stock/serializers.py:733
#: stock/serializers.py:911 stock/serializers.py:1454 stock/serializers.py:1767
#: stock/serializers.py:1816 templates/email/stale_stock_notification.html:18
#: users/models.py:555
msgid "Location"
msgstr "Local"

#: build/models.py:117 users/ruleset.py:31
msgid "Build Orders"
msgstr "Ordens de Produções"

#: build/models.py:167
msgid "Assembly BOM has not been validated"
msgstr "O BOM da montagem não foi validado"

#: build/models.py:174
msgid "Build order cannot be created for an inactive part"
msgstr "Ordem de compilação não pode ser criada para uma parte inativa"

#: build/models.py:181
msgid "Build order cannot be created for an unlocked part"
msgstr "Ordem de compilação não pode ser criado para uma parte desbloqueada"

#: build/models.py:199
msgid "Build orders can only be externally fulfilled for purchaseable parts"
msgstr "Criar ordens só pode ser realizado externamente para partes compráveis"

#: build/models.py:206 order/models.py:369
msgid "Responsible user or group must be specified"
msgstr "Usuário ou grupo responsável deve ser especificado"

#: build/models.py:211
msgid "Build order part cannot be changed"
msgstr "Parte do pedido de compilação não pode ser alterada"

#: build/models.py:216 order/models.py:382
msgid "Target date must be after start date"
msgstr "A data limite deve ser posterior à data inicial"

#: build/models.py:244
msgid "Build Order Reference"
msgstr "Referência do pedido de produção"

#: build/models.py:245 build/serializers.py:1401 order/models.py:607
#: order/models.py:1292 order/models.py:1727 order/models.py:2599
#: part/models.py:4436
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:35
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referência"

#: build/models.py:254
msgid "Brief description of the build (optional)"
msgstr "Breve descrição da produção (opcional)"

#: build/models.py:264
msgid "BuildOrder to which this build is allocated"
msgstr "Ordem de produção para qual este serviço está alocado"

#: build/models.py:273
msgid "Select part to build"
msgstr "Selecione a peça para construir"

#: build/models.py:278
msgid "Sales Order Reference"
msgstr "Referência do pedido de venda"

#: build/models.py:283
msgid "SalesOrder to which this build is allocated"
msgstr "Ordem de Venda para qual esta produção está alocada"

#: build/models.py:288 build/serializers.py:1093
msgid "Source Location"
msgstr "Local de Origem"

#: build/models.py:294
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Selecione o local para fazer estoque para esta compilação (deixe em branco para tirar a partir de qualquer local de estoque)"

#: build/models.py:300
msgid "External Build"
msgstr "Produção Externa"

#: build/models.py:301
msgid "This build order is fulfilled externally"
msgstr "Esta ordem de compilação é atendida externamente"

#: build/models.py:306
msgid "Destination Location"
msgstr "Local de Destino"

#: build/models.py:311
msgid "Select location where the completed items will be stored"
msgstr "Selecione o local onde os itens concluídos serão armazenados"

#: build/models.py:315
msgid "Build Quantity"
msgstr "Quantidade de Produção"

#: build/models.py:318
msgid "Number of stock items to build"
msgstr "Número de itens em estoque para produzir"

#: build/models.py:322
msgid "Completed items"
msgstr "Itens concluídos"

#: build/models.py:324
msgid "Number of stock items which have been completed"
msgstr "Número de itens em estoque concluídos"

#: build/models.py:328
msgid "Build Status"
msgstr "Progresso da produção"

#: build/models.py:333
msgid "Build status code"
msgstr "Código de situação da produção"

#: build/models.py:342 build/serializers.py:357 order/serializers.py:784
#: stock/models.py:1101 stock/serializers.py:84 stock/serializers.py:1627
msgid "Batch Code"
msgstr "Código do lote"

#: build/models.py:346 build/serializers.py:358
msgid "Batch code for this build output"
msgstr "Código do lote para esta saída de produção"

#: build/models.py:350 order/models.py:472 order/serializers.py:167
#: part/models.py:1304
msgid "Creation Date"
msgstr "Criado em"

#: build/models.py:356
msgid "Build start date"
msgstr "Data inicial da produção"

#: build/models.py:357
msgid "Scheduled start date for this build order"
msgstr "Data de início agendada para esta ordem de produção"

#: build/models.py:363
msgid "Target completion date"
msgstr "Data alvo final"

#: build/models.py:365
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Data limite para finalização de produção. Estará atrasado a partir deste dia."

#: build/models.py:370 order/models.py:660 order/models.py:2638
msgid "Completion Date"
msgstr "Data de conclusão"

#: build/models.py:378
msgid "completed by"
msgstr "concluído por"

#: build/models.py:387
msgid "Issued by"
msgstr "Emitido por"

#: build/models.py:388
msgid "User who issued this build order"
msgstr "Usuário que emitiu esta ordem de produção"

#: build/models.py:397 common/models.py:179 order/api.py:175
#: order/models.py:497 part/models.py:1321
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Responsável"

#: build/models.py:398
msgid "User or group responsible for this build order"
msgstr "Usuário ou grupo responsável para esta ordem de produção"

#: build/models.py:403 stock/models.py:1094
msgid "External Link"
msgstr "Link Externo"

#: build/models.py:405 common/models.py:1989 part/models.py:1126
#: stock/models.py:1096
msgid "Link to external URL"
msgstr "Link para URL externa"

#: build/models.py:410
msgid "Build Priority"
msgstr "Prioridade de Produção"

#: build/models.py:413
msgid "Priority of this build order"
msgstr "Prioridade desta ordem de compilação"

#: build/models.py:421 common/models.py:149 common/models.py:163
#: order/api.py:161 order/models.py:444
msgid "Project Code"
msgstr "Código do Projeto"

#: build/models.py:422
msgid "Project code for this build order"
msgstr "Código do projeto para esta ordem de compilação"

#: build/models.py:675
msgid "Cannot complete build order with open child builds"
msgstr "Não é possível concluir o pedido de produção com pedidos secundários abertos"

#: build/models.py:680
msgid "Cannot complete build order with incomplete outputs"
msgstr "Não é possível concluir o pedido com saídas incompletas"

#: build/models.py:699 build/models.py:827
msgid "Failed to offload task to complete build allocations"
msgstr "Falha ao descarregar tarefa para concluir alocações de compilação"

#: build/models.py:722
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "O Pedido de produção {build} foi concluído"

#: build/models.py:728
msgid "A build order has been completed"
msgstr "Um pedido de produção foi concluído"

#: build/models.py:908 build/serializers.py:405
msgid "Serial numbers must be provided for trackable parts"
msgstr "Números de série devem ser fornecidos para peças rastreáveis"

#: build/models.py:1039 build/models.py:1124
msgid "No build output specified"
msgstr "Nenhuma saída de produção especificada"

#: build/models.py:1042
msgid "Build output is already completed"
msgstr "Saída da produção já está concluída"

#: build/models.py:1045
msgid "Build output does not match Build Order"
msgstr "Saída da produção não corresponde à Ordem de Produção"

#: build/models.py:1127 build/serializers.py:284 build/serializers.py:333
#: build/serializers.py:961 build/serializers.py:1740 order/models.py:710
#: order/serializers.py:604 order/serializers.py:779 part/serializers.py:1695
#: stock/models.py:941 stock/models.py:1431 stock/models.py:1880
#: stock/serializers.py:711 stock/serializers.py:1616
msgid "Quantity must be greater than zero"
msgstr "Quantidade deve ser maior que zero"

#: build/models.py:1131 build/serializers.py:288
msgid "Quantity cannot be greater than the output quantity"
msgstr "A quantidade não pode ser maior que a quantidade de saída"

#: build/models.py:1194 build/serializers.py:604
msgid "Build output has not passed all required tests"
msgstr "A saída da produção não passou em todos os testes necessários"

#: build/models.py:1197 build/serializers.py:599
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "A saída da produção {serial} não passou em todos os testes necessários"

#: build/models.py:1578
msgid "Build Order Line Item"
msgstr "Item da ordem de produção"

#: build/models.py:1602
msgid "Build object"
msgstr "Compilar objeto"

#: build/models.py:1614 build/models.py:1914 build/serializers.py:272
#: build/serializers.py:318 build/serializers.py:1422 common/models.py:1345
#: order/models.py:1710 order/models.py:2484 order/serializers.py:1663
#: order/serializers.py:2124 part/models.py:3452 part/models.py:4384
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:36
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_stock_report_merge.html:90
#: report/templates/report/inventree_stock_report_merge.html:113
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:699
#: templates/email/build_order_completed.html:18
#: templates/email/stale_stock_notification.html:19
msgid "Quantity"
msgstr "Quantidade"

#: build/models.py:1615
msgid "Required quantity for build order"
msgstr "Quantidade necessária para o pedido de produção"

#: build/models.py:1624
msgid "Quantity of consumed stock"
msgstr ""

#: build/models.py:1712
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Item de produção deve especificar a saída, pois peças mestres estão marcadas como rastreáveis"

#: build/models.py:1723
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Quantidade alocada ({q}) não deve exceder a quantidade disponível em estoque ({a})"

#: build/models.py:1744 order/models.py:2433
msgid "Stock item is over-allocated"
msgstr "O item do estoque está sobre-alocado"

#: build/models.py:1749 order/models.py:2436
msgid "Allocation quantity must be greater than zero"
msgstr "Quantidade alocada deve ser maior que zero"

#: build/models.py:1755
msgid "Quantity must be 1 for serialized stock"
msgstr "Quantidade deve ser 1 para estoque serializado"

#: build/models.py:1815
msgid "Selected stock item does not match BOM line"
msgstr "O item de estoque selecionado não coincide com linha da BOM"

#: build/models.py:1853
msgid "Allocated quantity exceeds available stock quantity"
msgstr ""

#: build/models.py:1904 build/serializers.py:944 build/serializers.py:1254
#: order/serializers.py:1500 order/serializers.py:1521
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:457 stock/serializers.py:101 stock/serializers.py:823
#: stock/serializers.py:1310 stock/serializers.py:1422
msgid "Stock Item"
msgstr "Item de Estoque"

#: build/models.py:1905
msgid "Source stock item"
msgstr "Origem do item em estoque"

#: build/models.py:1915
msgid "Stock quantity to allocate to build"
msgstr "Quantidade do estoque para alocar à produção"

#: build/models.py:1924
msgid "Install into"
msgstr "Instalar em"

#: build/models.py:1925
msgid "Destination stock item"
msgstr "Destino do Item do Estoque"

#: build/serializers.py:115
msgid "Build Level"
msgstr "Nível de produção"

#: build/serializers.py:124
msgid "Part Name"
msgstr "Nome da Peça"

#: build/serializers.py:142
msgid "Project Code Label"
msgstr "Rótulo de código do projeto"

#: build/serializers.py:220 build/serializers.py:970
msgid "Build Output"
msgstr "Saída da Produção"

#: build/serializers.py:232
msgid "Build output does not match the parent build"
msgstr "Saída de produção não coincide com a produção progenitora"

#: build/serializers.py:236
msgid "Output part does not match BuildOrder part"
msgstr "Peça de saída não coincide com a peça da ordem de produção"

#: build/serializers.py:240
msgid "This build output has already been completed"
msgstr "Esta saída de produção já foi concluída"

#: build/serializers.py:254
msgid "This build output is not fully allocated"
msgstr "Esta saída de produção não está totalmente alocada"

#: build/serializers.py:273 build/serializers.py:319
msgid "Enter quantity for build output"
msgstr "Insira a quantidade para construir a saída de produção"

#: build/serializers.py:341
msgid "Integer quantity required for trackable parts"
msgstr "Quantidade inteira necessária para peças rastreáveis"

#: build/serializers.py:347
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Quantidade inteira necessária, pois a lista de materiais contém peças rastreáveis"

#: build/serializers.py:364 order/serializers.py:800 order/serializers.py:1667
#: stock/serializers.py:722
msgid "Serial Numbers"
msgstr "Números de Série"

#: build/serializers.py:365
msgid "Enter serial numbers for build outputs"
msgstr "Digite os números de série para saídas de produção"

#: build/serializers.py:371
msgid "Stock location for build output"
msgstr "Local de estoque para saídas de produção"

#: build/serializers.py:386
msgid "Auto Allocate Serial Numbers"
msgstr "Alocar Números de Série Automaticamente"

#: build/serializers.py:388
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Alocar automaticamente os itens necessários com os números de série correspondentes"

#: build/serializers.py:421 order/serializers.py:886 stock/api.py:1114
#: stock/models.py:1903
msgid "The following serial numbers already exist or are invalid"
msgstr "Os seguintes números de série já existem ou são inválidos"

#: build/serializers.py:463 build/serializers.py:519 build/serializers.py:611
msgid "A list of build outputs must be provided"
msgstr "Uma lista de saídas de produção deve ser fornecida"

#: build/serializers.py:496
msgid "Stock location for scrapped outputs"
msgstr "Local de estoque para saídas eliminadas"

#: build/serializers.py:502
msgid "Discard Allocations"
msgstr "Descartar alocações"

#: build/serializers.py:503
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Descartar quaisquer alocações de estoque para saídas eliminadas"

#: build/serializers.py:508
msgid "Reason for scrapping build output(s)"
msgstr "Motivo para eliminar saída(s) de produção"

#: build/serializers.py:566
msgid "Location for completed build outputs"
msgstr "Local para saídas de produção concluídas"

#: build/serializers.py:574
msgid "Accept Incomplete Allocation"
msgstr "Aceitar Alocação Incompleta"

#: build/serializers.py:575
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Concluir saídas se o estoque não tiver sido totalmente alocado"

#: build/serializers.py:698
msgid "Consume Allocated Stock"
msgstr "Consumir Estoque Alocado"

#: build/serializers.py:699
msgid "Consume any stock which has already been allocated to this build"
msgstr "Consumir qualquer estoque que já tenha sido alocado para esta produção"

#: build/serializers.py:705
msgid "Remove Incomplete Outputs"
msgstr "Remover Saídas Incompletas"

#: build/serializers.py:706
msgid "Delete any build outputs which have not been completed"
msgstr "Excluir quaisquer saídas de produção que não tenham sido completadas"

#: build/serializers.py:733
msgid "Not permitted"
msgstr "Não permitido"

#: build/serializers.py:734
msgid "Accept as consumed by this build order"
msgstr "Aceitar conforme consumido por esta ordem de produção"

#: build/serializers.py:735
msgid "Deallocate before completing this build order"
msgstr "Desatribua antes de completar esta ordem de produção"

#: build/serializers.py:762
msgid "Overallocated Stock"
msgstr ""

#: build/serializers.py:765
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Como deseja manejar itens de estoque extras atribuídos ao pedido de produção"

#: build/serializers.py:776
msgid "Some stock items have been overallocated"
msgstr "Alguns itens de estoque foram sobrecarregados"

#: build/serializers.py:781
msgid "Accept Unallocated"
msgstr "Aceitar não alocados"

#: build/serializers.py:783
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Aceitar que os itens de estoque não foram totalmente alocados para esta encomenda"

#: build/serializers.py:794
msgid "Required stock has not been fully allocated"
msgstr "Estoque obrigatório não foi totalmente alocado"

#: build/serializers.py:799 order/serializers.py:445 order/serializers.py:1568
msgid "Accept Incomplete"
msgstr "Aceitar Incompleto"

#: build/serializers.py:801
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Aceitar que o número requerido de saídas de produção não foi concluído"

#: build/serializers.py:812
msgid "Required build quantity has not been completed"
msgstr "Quantidade de produção requerida não foi concluída"

#: build/serializers.py:824
msgid "Build order has open child build orders"
msgstr "A ordem de produção tem ordens de produção secundárias abertas"

#: build/serializers.py:827
msgid "Build order must be in production state"
msgstr "Ordem de produção deve estar no estado de produção"

#: build/serializers.py:830
msgid "Build order has incomplete outputs"
msgstr "Ordem de produção tem saídas incompletas"

#: build/serializers.py:869
msgid "Build Line"
msgstr "Linha de Produção"

#: build/serializers.py:877
msgid "Build output"
msgstr "Saída da Produção"

#: build/serializers.py:885
msgid "Build output must point to the same build"
msgstr "Saída de produção deve indicar a mesma produção"

#: build/serializers.py:916
msgid "Build Line Item"
msgstr "Item da linha de produção"

#: build/serializers.py:934
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part deve apontar para a mesma parte que a ordem de produção"

#: build/serializers.py:950 stock/serializers.py:1323
msgid "Item must be in stock"
msgstr "O item deve estar em estoque"

#: build/serializers.py:993 order/serializers.py:1554
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Quantidade disponível ({q}) excedida"

#: build/serializers.py:999
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Saída de produção deve ser definida para alocação de peças rastreadas"

#: build/serializers.py:1007
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Saída de produção não pode ser definida para alocação de peças não rastreadas"

#: build/serializers.py:1031 order/serializers.py:1827
msgid "Allocation items must be provided"
msgstr "Alocação de itens precisam ser fornecidos"

#: build/serializers.py:1095
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Localização do estoque onde as peças devem ser originadas (deixe em branco a partir de qualquer local)"

#: build/serializers.py:1104
msgid "Exclude Location"
msgstr "Excluir Local"

#: build/serializers.py:1105
msgid "Exclude stock items from this selected location"
msgstr "Excluir itens de estoque desta localização selecionada"

#: build/serializers.py:1110
msgid "Interchangeable Stock"
msgstr "Estoque Intercambiável"

#: build/serializers.py:1111
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Itens de estoque em múltiplos locais podem ser intercambiáveis"

#: build/serializers.py:1116
msgid "Substitute Stock"
msgstr "Estoque Substituto"

#: build/serializers.py:1117
msgid "Allow allocation of substitute parts"
msgstr "Permitir alocação de peças substitutas"

#: build/serializers.py:1122
msgid "Optional Items"
msgstr "Itens opcionais"

#: build/serializers.py:1123
msgid "Allocate optional BOM items to build order"
msgstr "Alocar itens BOM opcionais para ordem de produção"

#: build/serializers.py:1144
msgid "Failed to start auto-allocation task"
msgstr "Falha ao iniciar tarefa de alocação automática"

#: build/serializers.py:1218
msgid "BOM Reference"
msgstr "Referência do BOM"

#: build/serializers.py:1224
msgid "BOM Part ID"
msgstr "ID da parte BOM"

#: build/serializers.py:1231
msgid "BOM Part Name"
msgstr "Nome da peça BOM"

#: build/serializers.py:1273 build/serializers.py:1458
msgid "Build"
msgstr "Produção"

#: build/serializers.py:1281 company/models.py:703 order/api.py:307
#: order/api.py:312 order/api.py:537 order/serializers.py:596
#: stock/models.py:1037 stock/serializers.py:612
msgid "Supplier Part"
msgstr "Fornecedor da Peça"

#: build/serializers.py:1289 stock/serializers.py:643
msgid "Allocated Quantity"
msgstr "Quantidade Alocada"

#: build/serializers.py:1383
msgid "Build Reference"
msgstr "Referência da produção"

#: build/serializers.py:1393
msgid "Part Category Name"
msgstr "Nome da Categoria"

#: build/serializers.py:1413 common/setting/system.py:481 part/models.py:1235
msgid "Trackable"
msgstr "Rastreável"

#: build/serializers.py:1416
msgid "Inherited"
msgstr "Herdado"

#: build/serializers.py:1419 part/models.py:4469
msgid "Allow Variants"
msgstr "Permitir variantes"

#: build/serializers.py:1425 build/serializers.py:1429 part/models.py:4207
#: part/models.py:4773 stock/api.py:859
msgid "BOM Item"
msgstr "Item BOM"

#: build/serializers.py:1473 company/serializers.py:448
#: order/serializers.py:1253 part/serializers.py:952 part/serializers.py:1277
#: part/serializers.py:1728
msgid "On Order"
msgstr "Em pedido"

#: build/serializers.py:1474 order/serializers.py:1254 part/serializers.py:1281
#: part/serializers.py:1732
msgid "In Production"
msgstr "Em Produção"

#: build/serializers.py:1476 part/serializers.py:943 part/serializers.py:1285
msgid "Scheduled to Build"
msgstr "Agendado para produção"

#: build/serializers.py:1479 part/serializers.py:980
msgid "External Stock"
msgstr "Estoque Externo"

#: build/serializers.py:1480 part/serializers.py:1271 part/serializers.py:1761
msgid "Available Stock"
msgstr "Estoque Disponível"

#: build/serializers.py:1482
msgid "Available Substitute Stock"
msgstr "Estoque Substituto Disponível"

#: build/serializers.py:1485
msgid "Available Variant Stock"
msgstr "Estoque de Variantes Disponível"

#: build/serializers.py:1753
msgid "Consumed quantity exceeds allocated quantity"
msgstr ""

#: build/serializers.py:1790
msgid "Optional notes for the stock consumption"
msgstr ""

#: build/serializers.py:1807
msgid "Build item must point to the correct build order"
msgstr ""

#: build/serializers.py:1812
msgid "Duplicate build item allocation"
msgstr ""

#: build/serializers.py:1830
msgid "Build line must point to the correct build order"
msgstr ""

#: build/serializers.py:1835
msgid "Duplicate build line allocation"
msgstr ""

#: build/serializers.py:1847
msgid "At least one item or line must be provided"
msgstr ""

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Pendentes"

#: build/status_codes.py:12
msgid "Production"
msgstr "Produção"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "Em Espera"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Cancelado"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:554
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Concluído"

#: build/tasks.py:180
msgid "Stock required for build order"
msgstr "Estoque obrigatório para a ordem de produção"

#: build/tasks.py:190
#, python-brace-format
msgid "Build order {build} requires additional stock"
msgstr "Ordem de produção {build} requer estoque adicional"

#: build/tasks.py:214
msgid "Overdue Build Order"
msgstr "Ordem de produção vencido"

#: build/tasks.py:219
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Ordem de produção {bo} está atrasada"

#: common/api.py:688
msgid "Is Link"
msgstr "É um link"

#: common/api.py:696
msgid "Is File"
msgstr "É um arquivo"

#: common/api.py:739
msgid "User does not have permission to delete these attachments"
msgstr "O usuário não tem permissão para deletar esses anexos"

#: common/api.py:756
msgid "User does not have permission to delete this attachment"
msgstr "O usuário não tem permissão para deletar esse anexo"

#: common/currency.py:122
msgid "Invalid currency code"
msgstr "Código de moeda inválido"

#: common/currency.py:124
msgid "Duplicate currency code"
msgstr "Código de moeda duplicado"

#: common/currency.py:129
msgid "No valid currency codes provided"
msgstr "Nenhum código de moeda válido fornecido"

#: common/currency.py:146
msgid "No plugin"
msgstr "Sem extensão"

#: common/models.py:100 common/models.py:125 common/models.py:2734
msgid "Updated"
msgstr "Atualizado"

#: common/models.py:101 common/models.py:126
msgid "Timestamp of last update"
msgstr "Tempo da última atualização"

#: common/models.py:138
msgid "Update By"
msgstr "Atualizado Por"

#: common/models.py:139
msgid "User who last updated this object"
msgstr "Usuário que atualizou este objeto pela última vez"

#: common/models.py:164
msgid "Unique project code"
msgstr "Código único do projeto"

#: common/models.py:171
msgid "Project description"
msgstr "Descrição do projeto"

#: common/models.py:180
msgid "User or group responsible for this project"
msgstr "Usuário ou grupo responsável por este projeto"

#: common/models.py:776 common/models.py:1277 common/models.py:1315
msgid "Settings key"
msgstr "Chave de configurações"

#: common/models.py:780
msgid "Settings value"
msgstr "Valor da Configuração"

#: common/models.py:835
msgid "Chosen value is not a valid option"
msgstr "Valor escolhido não é uma opção válida"

#: common/models.py:851
msgid "Value must be a boolean value"
msgstr "Valor deve ser um valor booleano"

#: common/models.py:859
msgid "Value must be an integer value"
msgstr "Valor deve ser um número inteiro"

#: common/models.py:867
msgid "Value must be a valid number"
msgstr "O valor deve ser um número válido"

#: common/models.py:892
msgid "Value does not pass validation checks"
msgstr "O valor não passa em verificações de validação"

#: common/models.py:914
msgid "Key string must be unique"
msgstr "A frase senha deve ser diferenciada"

#: common/models.py:1323 common/models.py:1324 common/models.py:1428
#: common/models.py:1429 common/models.py:1674 common/models.py:1675
#: common/models.py:2005 common/models.py:2006 common/models.py:2387
#: importer/models.py:100 part/models.py:3546 part/models.py:3574
#: plugin/models.py:350 plugin/models.py:351
#: report/templates/report/inventree_test_report.html:105 users/models.py:130
#: users/models.py:507
msgid "User"
msgstr "Usuário"

#: common/models.py:1346
msgid "Price break quantity"
msgstr "Quantidade de Parcelamentos"

#: common/models.py:1353 company/serializers.py:591 order/models.py:1787
#: order/models.py:2930
msgid "Price"
msgstr "Preço"

#: common/models.py:1354
msgid "Unit price at specified quantity"
msgstr "Preço unitário na quantidade especificada"

#: common/models.py:1405 common/models.py:1590
msgid "Endpoint"
msgstr "Ponto final"

#: common/models.py:1406
msgid "Endpoint at which this webhook is received"
msgstr "Ponto final em qual o webhook foi recebido"

#: common/models.py:1416
msgid "Name for this webhook"
msgstr "Nome para este webhook"

#: common/models.py:1420 common/models.py:2246 common/models.py:2353
#: company/models.py:198 company/models.py:840 machine/models.py:39
#: part/models.py:1258 plugin/models.py:68 stock/api.py:619 users/models.py:201
#: users/models.py:560 users/serializers.py:336
msgid "Active"
msgstr "Ativo"

#: common/models.py:1420
msgid "Is this webhook active"
msgstr "Este webhook está ativo"

#: common/models.py:1436 users/models.py:178
msgid "Token"
msgstr "Ficha"

#: common/models.py:1437
msgid "Token for access"
msgstr "Ficha para acesso"

#: common/models.py:1445
msgid "Secret"
msgstr "Secreto"

#: common/models.py:1446
msgid "Shared secret for HMAC"
msgstr "Segredo compartilhado para HMAC"

#: common/models.py:1554 common/models.py:2624
msgid "Message ID"
msgstr "ID da Mensagem"

#: common/models.py:1555 common/models.py:2614
msgid "Unique identifier for this message"
msgstr "Identificador exclusivo desta mensagem"

#: common/models.py:1563
msgid "Host"
msgstr "Servidor"

#: common/models.py:1564
msgid "Host from which this message was received"
msgstr "Servidor do qual esta mensagem foi recebida"

#: common/models.py:1572
msgid "Header"
msgstr "Cabeçalho"

#: common/models.py:1573
msgid "Header of this message"
msgstr "Cabeçalho da mensagem"

#: common/models.py:1580
msgid "Body"
msgstr "Corpo"

#: common/models.py:1581
msgid "Body of this message"
msgstr "Corpo da mensagem"

#: common/models.py:1591
msgid "Endpoint on which this message was received"
msgstr "Ponto do qual esta mensagem foi recebida"

#: common/models.py:1596
msgid "Worked on"
msgstr "Trabalhado em"

#: common/models.py:1597
msgid "Was the work on this message finished?"
msgstr "O trabalho desta mensagem foi concluído?"

#: common/models.py:1723
msgid "Id"
msgstr "Id"

#: common/models.py:1725
msgid "Title"
msgstr "Título"

#: common/models.py:1727 common/models.py:1988 company/models.py:183
#: company/models.py:486 company/models.py:553 company/models.py:857
#: order/models.py:450 order/models.py:1740 order/models.py:2252
#: part/models.py:1125
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Link"

#: common/models.py:1729
msgid "Published"
msgstr "Publicado"

#: common/models.py:1731
msgid "Author"
msgstr "Autor"

#: common/models.py:1733
msgid "Summary"
msgstr "Resumo"

#: common/models.py:1736 common/models.py:2591
msgid "Read"
msgstr "Lida"

#: common/models.py:1736
msgid "Was this news item read?"
msgstr "Esta notícia do item foi lida?"

#: common/models.py:1753 company/models.py:194 part/models.py:1136
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Imagem"

#: common/models.py:1753
msgid "Image file"
msgstr "Arquivo de imagem"

#: common/models.py:1765
msgid "Target model type for this image"
msgstr "Tipo modelo de destino para esta imagem"

#: common/models.py:1769
msgid "Target model ID for this image"
msgstr "ID do modelo de destino para esta imagem"

#: common/models.py:1791
msgid "Custom Unit"
msgstr "Unidade Personalizada"

#: common/models.py:1809
msgid "Unit symbol must be unique"
msgstr "O símbolo da unidade deve ser único"

#: common/models.py:1824
msgid "Unit name must be a valid identifier"
msgstr "Nome da unidade deve ser um identificador válido"

#: common/models.py:1843
msgid "Unit name"
msgstr "Nome da unidade"

#: common/models.py:1850
msgid "Symbol"
msgstr "Símbolo"

#: common/models.py:1851
msgid "Optional unit symbol"
msgstr "Símbolo de unidade opcional"

#: common/models.py:1857
msgid "Definition"
msgstr "Definição"

#: common/models.py:1858
msgid "Unit definition"
msgstr "Definição de unidade"

#: common/models.py:1916 common/models.py:1979 stock/models.py:2965
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Anexo"

#: common/models.py:1933
msgid "Missing file"
msgstr "Arquivo ausente"

#: common/models.py:1934
msgid "Missing external link"
msgstr "Link externo não encontrado"

#: common/models.py:1971
msgid "Model type"
msgstr "Categoria de Modelo"

#: common/models.py:1972
msgid "Target model type for image"
msgstr "Tipo modelo de destino para esta imagem"

#: common/models.py:1980
msgid "Select file to attach"
msgstr "Selecione arquivo para anexar"

#: common/models.py:1996
msgid "Comment"
msgstr "Comentário"

#: common/models.py:1997
msgid "Attachment comment"
msgstr "Comentário de anexo"

#: common/models.py:2013
msgid "Upload date"
msgstr "Data de envio"

#: common/models.py:2014
msgid "Date the file was uploaded"
msgstr "Data em que o arquivo foi enviado"

#: common/models.py:2018
msgid "File size"
msgstr "Tamanho do arquivo"

#: common/models.py:2018
msgid "File size in bytes"
msgstr "Tamanho do arquivo em bytes"

#: common/models.py:2056 common/serializers.py:672
msgid "Invalid model type specified for attachment"
msgstr "Categoria de modelo especificado inválido para anexo"

#: common/models.py:2077
msgid "Custom State"
msgstr "Estado personalizado"

#: common/models.py:2078
msgid "Custom States"
msgstr "Estados personalizados"

#: common/models.py:2083
msgid "Reference Status Set"
msgstr "Status Referência Definido"

#: common/models.py:2084
msgid "Status set that is extended with this custom state"
msgstr "Conjunto de status estendido com este estado personalizado"

#: common/models.py:2088 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Chave lógica"

#: common/models.py:2090
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Chave lógica de estado que é igual a este estado personalizado na lógica de negócios"

#: common/models.py:2095 common/models.py:2334 company/models.py:641
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2957
msgid "Value"
msgstr "Valor"

#: common/models.py:2096
msgid "Numerical value that will be saved in the models database"
msgstr "Valor numérico que será salvo no banco de dados dos modelos"

#: common/models.py:2102
msgid "Name of the state"
msgstr "Nome do estado"

#: common/models.py:2111 common/models.py:2340 generic/states/serializers.py:22
msgid "Label"
msgstr "Etiqueta"

#: common/models.py:2112
msgid "Label that will be displayed in the frontend"
msgstr "Etiqueta que será exibida no frontend"

#: common/models.py:2119 generic/states/serializers.py:24
msgid "Color"
msgstr "Cor"

#: common/models.py:2120
msgid "Color that will be displayed in the frontend"
msgstr "Cor que será exibida no frontend"

#: common/models.py:2128
msgid "Model"
msgstr "Modelo"

#: common/models.py:2129
msgid "Model this state is associated with"
msgstr "Modelo que este estado está associado a"

#: common/models.py:2144
msgid "Model must be selected"
msgstr "Modelo deve ser selecionado"

#: common/models.py:2147
msgid "Key must be selected"
msgstr "A chave deve ser selecionada"

#: common/models.py:2150
msgid "Logical key must be selected"
msgstr "Chave lógica deve ser selecionada"

#: common/models.py:2154
msgid "Key must be different from logical key"
msgstr "A chave deve diferir da chave lógica"

#: common/models.py:2161
msgid "Valid reference status class must be provided"
msgstr "Uma classe de estado de referência válida deve ser fornecida"

#: common/models.py:2167
msgid "Key must be different from the logical keys of the reference status"
msgstr "A chave deve diferir das chaves lógicas do estado de referência"

#: common/models.py:2174
msgid "Logical key must be in the logical keys of the reference status"
msgstr "A chave lógica deve estar nas chaves lógicas do estado de referência"

#: common/models.py:2181
msgid "Name must be different from the names of the reference status"
msgstr "O nome deve diferir dos nomes do estado de referência"

#: common/models.py:2221 common/models.py:2328 part/models.py:3888
msgid "Selection List"
msgstr "Lista de Seleção"

#: common/models.py:2222
msgid "Selection Lists"
msgstr "Listas de Seleção"

#: common/models.py:2227
msgid "Name of the selection list"
msgstr "Nome da lista de seleção"

#: common/models.py:2234
msgid "Description of the selection list"
msgstr "Descrição da lista de seleção"

#: common/models.py:2240 part/models.py:1263
msgid "Locked"
msgstr "Bloqueado"

#: common/models.py:2241
msgid "Is this selection list locked?"
msgstr "Esta lista de seleção está bloqueada?"

#: common/models.py:2247
msgid "Can this selection list be used?"
msgstr "Esta lista de seleção pode ser usada?"

#: common/models.py:2255
msgid "Source Plugin"
msgstr "Extensão de origem"

#: common/models.py:2256
msgid "Plugin which provides the selection list"
msgstr "Extensão que fornece a lista de seleção"

#: common/models.py:2261
msgid "Source String"
msgstr "Série de Origem"

#: common/models.py:2262
msgid "Optional string identifying the source used for this list"
msgstr "Série opcional identificando a fonte usada para esta lista"

#: common/models.py:2271
msgid "Default Entry"
msgstr "Entrada Padrão"

#: common/models.py:2272
msgid "Default entry for this selection list"
msgstr "Entrada padrão para esta lista de seleção"

#: common/models.py:2277 common/models.py:2729
msgid "Created"
msgstr "Criado em"

#: common/models.py:2278
msgid "Date and time that the selection list was created"
msgstr "Data e hora em que a lista de seleção foi criada"

#: common/models.py:2283
msgid "Last Updated"
msgstr "Última Atualização"

#: common/models.py:2284
msgid "Date and time that the selection list was last updated"
msgstr "Data e hora da última atualização da lista de seleção"

#: common/models.py:2318
msgid "Selection List Entry"
msgstr "Entrada na lista de seleção"

#: common/models.py:2319
msgid "Selection List Entries"
msgstr "Entradas na Lista de Seleção"

#: common/models.py:2329
msgid "Selection list to which this entry belongs"
msgstr "Lista de seleção à qual esta entrada pertence"

#: common/models.py:2335
msgid "Value of the selection list entry"
msgstr "Valor da entrada da lista de seleção"

#: common/models.py:2341
msgid "Label for the selection list entry"
msgstr "Rótulo para a entrada da lista de seleção"

#: common/models.py:2347
msgid "Description of the selection list entry"
msgstr "Descrição da entrada da lista de seleção"

#: common/models.py:2354
msgid "Is this selection list entry active?"
msgstr "Esta entrada da lista de seleção está ativa?"

#: common/models.py:2372
msgid "Barcode Scan"
msgstr "Escaneamento de Código de Barras"

#: common/models.py:2376 importer/models.py:548 part/models.py:4059
msgid "Data"
msgstr "Dados"

#: common/models.py:2377
msgid "Barcode data"
msgstr "Dados de código de barras"

#: common/models.py:2388
msgid "User who scanned the barcode"
msgstr "Usuário que escaneou o código de barras"

#: common/models.py:2393 importer/models.py:69
msgid "Timestamp"
msgstr "Marcador de hora"

#: common/models.py:2394
msgid "Date and time of the barcode scan"
msgstr "Data e hora da verificação do código de barras"

#: common/models.py:2400
msgid "URL endpoint which processed the barcode"
msgstr "O endpoint da URL que processou o código de barras"

#: common/models.py:2407 order/models.py:1777 plugin/serializers.py:93
msgid "Context"
msgstr "Contexto"

#: common/models.py:2408
msgid "Context data for the barcode scan"
msgstr "Dados de contexto para escanear código de barras"

#: common/models.py:2415
msgid "Response"
msgstr "Resposta"

#: common/models.py:2416
msgid "Response data from the barcode scan"
msgstr "Dados de resposta da verificação de código de barras"

#: common/models.py:2422 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2951
msgid "Result"
msgstr "Resultado"

#: common/models.py:2423
msgid "Was the barcode scan successful?"
msgstr "O código de barras foi digitalizado com sucesso?"

#: common/models.py:2505
msgid "An error occurred"
msgstr "Ocorreu um erro"

#: common/models.py:2526
msgid "INVE-E8: Email log deletion is protected. Set INVENTREE_PROTECT_EMAIL_LOG to False to allow deletion."
msgstr "INVE-E8: exclusão de registro de e-mail está protegida. Defina INVENTREE_PROTECT_EMAIL_LOG para Falso para permitir a exclusão."

#: common/models.py:2573
msgid "Email Message"
msgstr "Mensagem de e-mail"

#: common/models.py:2574
msgid "Email Messages"
msgstr "Mensagens de Email"

#: common/models.py:2581
msgid "Announced"
msgstr "Anunciado"

#: common/models.py:2583
msgid "Sent"
msgstr "Enviado"

#: common/models.py:2584
msgid "Failed"
msgstr "Falhou"

#: common/models.py:2587
msgid "Delivered"
msgstr "Entregue"

#: common/models.py:2595
msgid "Confirmed"
msgstr "Confirmado"

#: common/models.py:2601
msgid "Inbound"
msgstr "Entrada"

#: common/models.py:2602
msgid "Outbound"
msgstr "Saída"

#: common/models.py:2607
msgid "No Reply"
msgstr "Não responder"

#: common/models.py:2608
msgid "Track Delivery"
msgstr "Rastrear Entrega"

#: common/models.py:2609
msgid "Track Read"
msgstr "Monitorado"

#: common/models.py:2610
msgid "Track Click"
msgstr "Clique no caminho"

#: common/models.py:2613 common/models.py:2716
msgid "Global ID"
msgstr "ID Global"

#: common/models.py:2626
msgid "Identifier for this message (might be supplied by external system)"
msgstr "Identificador para esta mensagem (pode ser fornecido por sistema externo)"

#: common/models.py:2633
msgid "Thread ID"
msgstr "ID do Tópico"

#: common/models.py:2635
msgid "Identifier for this message thread (might be supplied by external system)"
msgstr "Identificador deste tópico de mensagem (pode ser fornecido por sistema externo)"

#: common/models.py:2644
msgid "Thread"
msgstr "Tópico"

#: common/models.py:2645
msgid "Linked thread for this message"
msgstr "Tópico vinculado para esta mensagem"

#: common/models.py:2661
msgid "Prioriy"
msgstr "Prioridade"

#: common/models.py:2703
msgid "Email Thread"
msgstr "Tópico do e-mail"

#: common/models.py:2704
msgid "Email Threads"
msgstr "Tópicos de e-mail"

#: common/models.py:2710 generic/states/serializers.py:16 plugin/models.py:45
#: users/models.py:119
msgid "Key"
msgstr "Chave"

#: common/models.py:2713
msgid "Unique key for this thread (used to identify the thread)"
msgstr "Chave única para este tópico (usada para identificar o tópico)"

#: common/models.py:2717
msgid "Unique identifier for this thread"
msgstr "Identificador exclusivo deste tópico"

#: common/models.py:2724
msgid "Started Internal"
msgstr "Iniciado interno"

#: common/models.py:2725
msgid "Was this thread started internally?"
msgstr "Este tópico foi iniciado internamente?"

#: common/models.py:2730
msgid "Date and time that the thread was created"
msgstr "Data e hora em que o tópico foi criado"

#: common/models.py:2735
msgid "Date and time that the thread was last updated"
msgstr "Data e hora da última atualização do tópico"

#: common/notifications.py:57
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Novo {verbose_name}"

#: common/notifications.py:59
msgid "A new order has been created and assigned to you"
msgstr "Um novo pedido foi criado e atribuído a você"

#: common/notifications.py:65
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} cancelado"

#: common/notifications.py:67
msgid "A order that is assigned to you was canceled"
msgstr "Um pedido atribuído a você foi cancelado"

#: common/notifications.py:73 common/notifications.py:80 order/api.py:588
msgid "Items Received"
msgstr "Itens Recebidos"

#: common/notifications.py:75
msgid "Items have been received against a purchase order"
msgstr "Os itens de um pedido de compra foram recebidos"

#: common/notifications.py:82
msgid "Items have been received against a return order"
msgstr "Os itens de um pedido de devolução foram recebidos"

#: common/serializers.py:145
msgid "Indicates if the setting is overridden by an environment variable"
msgstr "É indicado se a configuração é substituída por uma variável de ambiente"

#: common/serializers.py:147
msgid "Override"
msgstr "Substituir"

#: common/serializers.py:486
msgid "Is Running"
msgstr "Está em execução"

#: common/serializers.py:492
msgid "Pending Tasks"
msgstr "Tarefas Pendentes"

#: common/serializers.py:498
msgid "Scheduled Tasks"
msgstr "Tarefas Agendadas"

#: common/serializers.py:504
msgid "Failed Tasks"
msgstr "Tarefas com Falhas"

#: common/serializers.py:519
msgid "Task ID"
msgstr "ID da Tarefa"

#: common/serializers.py:519
msgid "Unique task ID"
msgstr "ID Único da Tarefa"

#: common/serializers.py:521
msgid "Lock"
msgstr "Bloquear"

#: common/serializers.py:521
msgid "Lock time"
msgstr "Congelar tempo"

#: common/serializers.py:523
msgid "Task name"
msgstr "Nome da tarefa"

#: common/serializers.py:525
msgid "Function"
msgstr "Função"

#: common/serializers.py:525
msgid "Function name"
msgstr "Nome da função"

#: common/serializers.py:527
msgid "Arguments"
msgstr "Argumentos"

#: common/serializers.py:527
msgid "Task arguments"
msgstr "Argumentos da tarefa"

#: common/serializers.py:530
msgid "Keyword Arguments"
msgstr "Argumentos de Palavra-chave"

#: common/serializers.py:530
msgid "Task keyword arguments"
msgstr "Argumentos Palavra-chave da Tarefa"

#: common/serializers.py:640
msgid "Filename"
msgstr "Nome do arquivo"

#: common/serializers.py:647 importer/models.py:89 report/api.py:40
#: report/models.py:293 report/serializers.py:53
msgid "Model Type"
msgstr "Categoria de Modelo"

#: common/serializers.py:675
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Usuário não tem permissão para criar ou editar anexos para este modelo"

#: common/serializers.py:719 common/serializers.py:822
msgid "Selection list is locked"
msgstr "Lista de seleção bloqueada"

#: common/setting/system.py:97
msgid "No group"
msgstr "Sem Grupo"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "URL do site está bloqueada por configuração"

#: common/setting/system.py:173
msgid "Restart required"
msgstr "Reinicialização necessária"

#: common/setting/system.py:174
msgid "A setting has been changed which requires a server restart"
msgstr "Uma configuração que requer uma reinicialização do servidor foi alterada"

#: common/setting/system.py:180
msgid "Pending migrations"
msgstr "Migrações pendentes"

#: common/setting/system.py:181
msgid "Number of pending database migrations"
msgstr "Número de migrações pendentes na base de dados"

#: common/setting/system.py:186
msgid "Active warning codes"
msgstr "Códigos de aviso ativos"

#: common/setting/system.py:187
msgid "A dict of active warning codes"
msgstr "Um dicionário dos códigos de aviso ativos"

#: common/setting/system.py:193
msgid "Instance ID"
msgstr "ID da instância"

#: common/setting/system.py:194
msgid "Unique identifier for this InvenTree instance"
msgstr "Identificador exclusivo para esta instância do InvenTree"

#: common/setting/system.py:199
msgid "Announce ID"
msgstr "Anúncio ID"

#: common/setting/system.py:201
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr "Anuncie a ID da instância do servidor na informação de estado do servidor (não autenticado)"

#: common/setting/system.py:207
msgid "Server Instance Name"
msgstr "Nome da Instância do Servidor"

#: common/setting/system.py:209
msgid "String descriptor for the server instance"
msgstr "Descritor de frases para a instância do servidor"

#: common/setting/system.py:213
msgid "Use instance name"
msgstr "Usar nome da instância"

#: common/setting/system.py:214
msgid "Use the instance name in the title-bar"
msgstr "Usar o nome da instância na barra de título"

#: common/setting/system.py:219
msgid "Restrict showing `about`"
msgstr "Restringir a exibição 'sobre'"

#: common/setting/system.py:220
msgid "Show the `about` modal only to superusers"
msgstr "Mostrar 'sobre' modal apenas para superusuários"

#: common/setting/system.py:225 company/models.py:142 company/models.py:143
msgid "Company name"
msgstr "Nome da empresa"

#: common/setting/system.py:226
msgid "Internal company name"
msgstr "Nome interno da Empresa"

#: common/setting/system.py:230
msgid "Base URL"
msgstr "URL de Base"

#: common/setting/system.py:231
msgid "Base URL for server instance"
msgstr "URL de base para instância do servidor"

#: common/setting/system.py:237
msgid "Default Currency"
msgstr "Moeda Padrão"

#: common/setting/system.py:238
msgid "Select base currency for pricing calculations"
msgstr "Selecione a moeda base para cálculos de preços"

#: common/setting/system.py:244
msgid "Supported Currencies"
msgstr "Moedas Suportadas"

#: common/setting/system.py:245
msgid "List of supported currency codes"
msgstr "Lista de códigos de moeda suportados"

#: common/setting/system.py:251
msgid "Currency Update Interval"
msgstr "Intervalo de Atualização da Moeda"

#: common/setting/system.py:252
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Com que frequência atualizar as taxas de câmbio (defina como zero para desativar)"

#: common/setting/system.py:254 common/setting/system.py:294
#: common/setting/system.py:307 common/setting/system.py:315
#: common/setting/system.py:322 common/setting/system.py:331
#: common/setting/system.py:340 common/setting/system.py:589
#: common/setting/system.py:617 common/setting/system.py:708
#: common/setting/system.py:1096 common/setting/system.py:1112
msgid "days"
msgstr "dias"

#: common/setting/system.py:258
msgid "Currency Update Plugin"
msgstr "Extensão de Atualização de Moeda"

#: common/setting/system.py:259
msgid "Currency update plugin to use"
msgstr "Extensão de Atualização de Moeda a utilizar"

#: common/setting/system.py:264
msgid "Download from URL"
msgstr "Baixar do URL"

#: common/setting/system.py:265
msgid "Allow download of remote images and files from external URL"
msgstr "Permitir baixar imagens remotas e arquivos de URL externos"

#: common/setting/system.py:270
msgid "Download Size Limit"
msgstr "Limite de tamanho para baixar"

#: common/setting/system.py:271
msgid "Maximum allowable download size for remote image"
msgstr "Tamanho máximo permitido para download da imagem remota"

#: common/setting/system.py:277
msgid "User-agent used to download from URL"
msgstr "Usuário-agente utilizado para baixar da URL"

#: common/setting/system.py:279
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Permitir a substituição de imagens e arquivos usados baixados por usuário-agente (deixar em branco por padrão)"

#: common/setting/system.py:284
msgid "Strict URL Validation"
msgstr "Validação rigorosa de URL"

#: common/setting/system.py:285
msgid "Require schema specification when validating URLs"
msgstr "Exigir especificação de esquema ao validar URLs"

#: common/setting/system.py:290
msgid "Update Check Interval"
msgstr "Atualizar Intervalo de Verificação"

#: common/setting/system.py:291
msgid "How often to check for updates (set to zero to disable)"
msgstr "Frequência para verificar atualizações (defina como zero para desativar)"

#: common/setting/system.py:297
msgid "Automatic Backup"
msgstr "Backup Automático"

#: common/setting/system.py:298
msgid "Enable automatic backup of database and media files"
msgstr "Ativar cópia de segurança automática do banco de dados e arquivos de mídia"

#: common/setting/system.py:303
msgid "Auto Backup Interval"
msgstr "Intervalo de Backup Automático"

#: common/setting/system.py:304
msgid "Specify number of days between automated backup events"
msgstr "Especificar o número de dia entre as cópias de segurança"

#: common/setting/system.py:310
msgid "Task Deletion Interval"
msgstr "Intervalo para Excluir da Tarefa"

#: common/setting/system.py:312
msgid "Background task results will be deleted after specified number of days"
msgstr "Os resultados da tarefa no plano de fundo serão excluídos após um número especificado de dias"

#: common/setting/system.py:319
msgid "Error Log Deletion Interval"
msgstr "Intervalo para Excluir do Registro de Erro"

#: common/setting/system.py:320
msgid "Error logs will be deleted after specified number of days"
msgstr "Registros de erros serão excluídos após um número especificado de dias"

#: common/setting/system.py:326
msgid "Notification Deletion Interval"
msgstr "Intervalo para Excluir de Notificação"

#: common/setting/system.py:328
msgid "User notifications will be deleted after specified number of days"
msgstr "Notificações de usuários será excluído após um número especificado de dias"

#: common/setting/system.py:335
msgid "Email Deletion Interval"
msgstr "Intervalo de Exclusão de e-mail"

#: common/setting/system.py:337
msgid "Email messages will be deleted after specified number of days"
msgstr "Mensagens de e-mail serão excluídas após um determinado número de dias"

#: common/setting/system.py:344
msgid "Protect Email Log"
msgstr "Proteger o Log de E-mail"

#: common/setting/system.py:345
msgid "Prevent deletion of email log entries"
msgstr "Evitar exclusão de entradas de registros de e-mail"

#: common/setting/system.py:350
msgid "Barcode Support"
msgstr "Suporte aos códigos de barras"

#: common/setting/system.py:351
msgid "Enable barcode scanner support in the web interface"
msgstr "Ativar suporte a leitor de código de barras na interface web"

#: common/setting/system.py:356
msgid "Store Barcode Results"
msgstr "Armazenar Resultados do Código de Barras"

#: common/setting/system.py:357
msgid "Store barcode scan results in the database"
msgstr "Armazenar a verificação do código de barras no banco de dados"

#: common/setting/system.py:362
msgid "Barcode Scans Maximum Count"
msgstr "Contagem máxima de códigos de barras"

#: common/setting/system.py:363
msgid "Maximum number of barcode scan results to store"
msgstr "Número máximo de resultados de digitalização de códigos de barras para armazenar"

#: common/setting/system.py:368
msgid "Barcode Input Delay"
msgstr "Atraso na entrada de código de barras"

#: common/setting/system.py:369
msgid "Barcode input processing delay time"
msgstr "Tempo de atraso de processamento de entrada de barras"

#: common/setting/system.py:375
msgid "Barcode Webcam Support"
msgstr "Suporte a webcam com código de barras"

#: common/setting/system.py:376
msgid "Allow barcode scanning via webcam in browser"
msgstr "Permitir a verificação de códigos de barras via webcam no navegador"

#: common/setting/system.py:381
msgid "Barcode Show Data"
msgstr "Código de barras Exibir Dados"

#: common/setting/system.py:382
msgid "Display barcode data in browser as text"
msgstr "Exibir dados do código de barras no navegador como texto"

#: common/setting/system.py:387
msgid "Barcode Generation Plugin"
msgstr "Extensão de geração de códio de barras"

#: common/setting/system.py:388
msgid "Plugin to use for internal barcode data generation"
msgstr "Extensão para usar para geração de dados de código de barras interno"

#: common/setting/system.py:393
msgid "Part Revisions"
msgstr "Revisões de peças"

#: common/setting/system.py:394
msgid "Enable revision field for Part"
msgstr "Ativar campo de revisão para a Peça"

#: common/setting/system.py:399
msgid "Assembly Revision Only"
msgstr "Somente Revisão da Assembleia"

#: common/setting/system.py:400
msgid "Only allow revisions for assembly parts"
msgstr "Permitir revisões apenas para peças de montagem"

#: common/setting/system.py:405
msgid "Allow Deletion from Assembly"
msgstr "Permitir a exclusão da Assembleia"

#: common/setting/system.py:406
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Permitir a exclusão de peças que são usadas em uma montagem"

#: common/setting/system.py:411
msgid "IPN Regex"
msgstr "Regex IPN"

#: common/setting/system.py:412
msgid "Regular expression pattern for matching Part IPN"
msgstr "Padrão de expressão regular para correspondência de Parte IPN"

#: common/setting/system.py:415
msgid "Allow Duplicate IPN"
msgstr "Permitir Duplicação IPN"

#: common/setting/system.py:416
msgid "Allow multiple parts to share the same IPN"
msgstr "Permitir que várias peças compartilhem o mesmo IPN"

#: common/setting/system.py:421
msgid "Allow Editing IPN"
msgstr "Permitir Edição IPN"

#: common/setting/system.py:422
msgid "Allow changing the IPN value while editing a part"
msgstr "Permitir trocar o valor do IPN enquanto se edita a peça"

#: common/setting/system.py:427
msgid "Copy Part BOM Data"
msgstr "Copiar dados da LDM da Peça"

#: common/setting/system.py:428
msgid "Copy BOM data by default when duplicating a part"
msgstr "Copiar dados da LDM por padrão quando duplicar a peça"

#: common/setting/system.py:433
msgid "Copy Part Parameter Data"
msgstr "Copiar Dados de Parâmetro da Peça"

#: common/setting/system.py:434
msgid "Copy parameter data by default when duplicating a part"
msgstr "Copiar dados de parâmetros por padrão quando duplicar uma peça"

#: common/setting/system.py:439
msgid "Copy Part Test Data"
msgstr "Copiar Dados Teste da Peça"

#: common/setting/system.py:440
msgid "Copy test data by default when duplicating a part"
msgstr "Copiar dados de teste por padrão quando duplicar a peça"

#: common/setting/system.py:445
msgid "Copy Category Parameter Templates"
msgstr "Copiar Parâmetros dos Modelos de Categoria"

#: common/setting/system.py:446
msgid "Copy category parameter templates when creating a part"
msgstr "Copiar parâmetros do modelo de categoria quando criar uma peça"

#: common/setting/system.py:451 part/models.py:4053 report/models.py:373
#: report/models.py:660 report/serializers.py:95 report/serializers.py:136
#: stock/serializers.py:247
msgid "Template"
msgstr "Modelo"

#: common/setting/system.py:452
msgid "Parts are templates by default"
msgstr "Peças são modelos por padrão"

#: common/setting/system.py:458
msgid "Parts can be assembled from other components by default"
msgstr "Peças podem ser montadas a partir de outros componentes por padrão"

#: common/setting/system.py:463 part/models.py:1229 part/serializers.py:1715
#: part/serializers.py:1721
msgid "Component"
msgstr "Componente"

#: common/setting/system.py:464
msgid "Parts can be used as sub-components by default"
msgstr "Peças podem ser usadas como sub-componentes por padrão"

#: common/setting/system.py:469 part/models.py:1247
msgid "Purchaseable"
msgstr "Comprável"

#: common/setting/system.py:470
msgid "Parts are purchaseable by default"
msgstr "Peças são compráveis por padrão"

#: common/setting/system.py:475 part/models.py:1253 stock/api.py:620
msgid "Salable"
msgstr "Comercializável"

#: common/setting/system.py:476
msgid "Parts are salable by default"
msgstr "Peças vão vendíveis por padrão"

#: common/setting/system.py:482
msgid "Parts are trackable by default"
msgstr "Peças vão rastreáveis por padrão"

#: common/setting/system.py:487 part/models.py:1269
msgid "Virtual"
msgstr "Virtual"

#: common/setting/system.py:488
msgid "Parts are virtual by default"
msgstr "Peças são virtuais por padrão"

#: common/setting/system.py:493
msgid "Show related parts"
msgstr "Mostrar peças relacionadas"

#: common/setting/system.py:494
msgid "Display related parts for a part"
msgstr "Exibir peças relacionadas com uma peça"

#: common/setting/system.py:499
msgid "Initial Stock Data"
msgstr "Dados Iniciais de Estoque"

#: common/setting/system.py:500
msgid "Allow creation of initial stock when adding a new part"
msgstr "Permitir a criação do estoque inicial quando adicionar uma nova peça"

#: common/setting/system.py:505
msgid "Initial Supplier Data"
msgstr "Dados Iniciais de Fornecedor"

#: common/setting/system.py:507
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Permitir a criação de dados iniciais de fornecedor quando adicionar uma nova peça"

#: common/setting/system.py:513
msgid "Part Name Display Format"
msgstr "Formato de Exibição do Nome da Peça"

#: common/setting/system.py:514
msgid "Format to display the part name"
msgstr "Formato para exibir o nome da peça"

#: common/setting/system.py:520
msgid "Part Category Default Icon"
msgstr "Ícone de Categoria de Peça Padrão"

#: common/setting/system.py:521
msgid "Part category default icon (empty means no icon)"
msgstr "Ícone padrão de categoria de peça (vazio significa sem ícone)"

#: common/setting/system.py:526
msgid "Enforce Parameter Units"
msgstr "Forçar Unidades de Parâmetro"

#: common/setting/system.py:528
msgid "If units are provided, parameter values must match the specified units"
msgstr "Se as unidades são fornecidas, os valores do parâmetro devem corresponder às unidades especificadas"

#: common/setting/system.py:534
msgid "Minimum Pricing Decimal Places"
msgstr "Mínimo de Casas Decimais do Preço"

#: common/setting/system.py:536
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Mínimo número de casas decimais a exibir quando renderizar dados de preços"

#: common/setting/system.py:547
msgid "Maximum Pricing Decimal Places"
msgstr "Máximo Casas Decimais de Preço"

#: common/setting/system.py:549
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Número máximo de casas decimais a exibir quando renderizar dados de preços"

#: common/setting/system.py:560
msgid "Use Supplier Pricing"
msgstr "Usar Preços do Fornecedor"

#: common/setting/system.py:562
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Incluir quebras de preço do fornecedor nos cálculos de preços globais"

#: common/setting/system.py:568
msgid "Purchase History Override"
msgstr "Substituir Histórico de Compras"

#: common/setting/system.py:570
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Histórico do pedido de compra substitui os intervalos dos preços do fornecedor"

#: common/setting/system.py:576
msgid "Use Stock Item Pricing"
msgstr "Usar Preços do Item em Estoque"

#: common/setting/system.py:578
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Usar preço inserido manualmente no estoque para cálculos de valores"

#: common/setting/system.py:584
msgid "Stock Item Pricing Age"
msgstr "Idade do preço do Item em Estoque"

#: common/setting/system.py:586
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Não incluir itens em estoque mais velhos que este número de dias no cálculo de preços"

#: common/setting/system.py:593
msgid "Use Variant Pricing"
msgstr "Usar Preço Variável"

#: common/setting/system.py:594
msgid "Include variant pricing in overall pricing calculations"
msgstr "Incluir preços variáveis nos cálculos de valores gerais"

#: common/setting/system.py:599
msgid "Active Variants Only"
msgstr "Apenas Ativar Variáveis"

#: common/setting/system.py:601
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Apenas usar peças variáveis ativas para calcular preço variáveis"

#: common/setting/system.py:607
msgid "Auto Update Pricing"
msgstr "Atualização automática dos preços"

#: common/setting/system.py:609
msgid "Automatically update part pricing when internal data changes"
msgstr "Atualizar automaticamente o preço da peça quando dados internos forem alterados"

#: common/setting/system.py:615
msgid "Pricing Rebuild Interval"
msgstr "Intervalo de Reconstrução de Preços"

#: common/setting/system.py:616
msgid "Number of days before part pricing is automatically updated"
msgstr "Número de dias antes da atualização automática dos preços das peças"

#: common/setting/system.py:622
msgid "Internal Prices"
msgstr "Preços Internos"

#: common/setting/system.py:623
msgid "Enable internal prices for parts"
msgstr "Habilitar preços internos para peças"

#: common/setting/system.py:628
msgid "Internal Price Override"
msgstr "Substituição de preço interno"

#: common/setting/system.py:630
msgid "If available, internal prices override price range calculations"
msgstr "Se disponível, os preços internos substituem os cálculos da faixa de preços"

#: common/setting/system.py:636
msgid "Enable label printing"
msgstr "Habilitar Impressão de Etiqueta"

#: common/setting/system.py:637
msgid "Enable label printing from the web interface"
msgstr "Ativar impressão de etiqueta pela interface da internet"

#: common/setting/system.py:642
msgid "Label Image DPI"
msgstr "DPI da Imagem na Etiqueta"

#: common/setting/system.py:644
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Resolução de DPI quando gerar arquivo de imagens para fornecer à extensão de impressão de etiquetas"

#: common/setting/system.py:650
msgid "Enable Reports"
msgstr "Ativar Relatórios"

#: common/setting/system.py:651
msgid "Enable generation of reports"
msgstr "Ativar geração de relatórios"

#: common/setting/system.py:656
msgid "Debug Mode"
msgstr "Modo de depuração"

#: common/setting/system.py:657
msgid "Generate reports in debug mode (HTML output)"
msgstr "Gerar relatórios em modo de depuração (saída HTML)"

#: common/setting/system.py:662
msgid "Log Report Errors"
msgstr "Registro de erros"

#: common/setting/system.py:663
msgid "Log errors which occur when generating reports"
msgstr "Registrar erros que ocorrem ao gerar relatórios"

#: common/setting/system.py:668 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:381
msgid "Page Size"
msgstr "Tamanho da página"

#: common/setting/system.py:669
msgid "Default page size for PDF reports"
msgstr "Tamanho padrão da página PDF para relatórios"

#: common/setting/system.py:674
msgid "Globally Unique Serials"
msgstr "Seriais Únicos Globais"

#: common/setting/system.py:675
msgid "Serial numbers for stock items must be globally unique"
msgstr "Números de série para itens de estoque devem ser globalmente únicos"

#: common/setting/system.py:680
msgid "Delete Depleted Stock"
msgstr "Excluir Estoque Esgotado"

#: common/setting/system.py:681
msgid "Determines default behavior when a stock item is depleted"
msgstr "Determina o comportamento padrão, quando um item de estoque é esgotado"

#: common/setting/system.py:686
msgid "Batch Code Template"
msgstr "Modelo de Código de Lote"

#: common/setting/system.py:687
msgid "Template for generating default batch codes for stock items"
msgstr "Modelo para gerar códigos de lote padrão para itens de estoque"

#: common/setting/system.py:691
msgid "Stock Expiry"
msgstr "Validade do Estoque"

#: common/setting/system.py:692
msgid "Enable stock expiry functionality"
msgstr "Ativar função de validade de estoque"

#: common/setting/system.py:697
msgid "Sell Expired Stock"
msgstr "Vender estoque expirado"

#: common/setting/system.py:698
msgid "Allow sale of expired stock"
msgstr "Permitir venda de estoque expirado"

#: common/setting/system.py:703
msgid "Stock Stale Time"
msgstr "Tempo de Estoque Inativo"

#: common/setting/system.py:705
msgid "Number of days stock items are considered stale before expiring"
msgstr "Número de dias em que os itens em estoque são considerados obsoleto antes de vencer"

#: common/setting/system.py:712
msgid "Build Expired Stock"
msgstr "Produzir Estoque Vencido"

#: common/setting/system.py:713
msgid "Allow building with expired stock"
msgstr "Permitir produção com estoque vencido"

#: common/setting/system.py:718
msgid "Stock Ownership Control"
msgstr "Controle de propriedade do estoque"

#: common/setting/system.py:719
msgid "Enable ownership control over stock locations and items"
msgstr "Ativar controle de propriedade sobre locais e itens de estoque"

#: common/setting/system.py:724
msgid "Stock Location Default Icon"
msgstr "Ícone padrão do local de estoque"

#: common/setting/system.py:725
msgid "Stock location default icon (empty means no icon)"
msgstr "Ícone padrão de local de estoque (vazio significa sem ícone)"

#: common/setting/system.py:730
msgid "Show Installed Stock Items"
msgstr "Mostrar Itens de Estoque Instalados"

#: common/setting/system.py:731
msgid "Display installed stock items in stock tables"
msgstr "Exibir itens de estoque instalados nas tabelas de estoque"

#: common/setting/system.py:736
msgid "Check BOM when installing items"
msgstr "Verificar LDM ao instalar itens"

#: common/setting/system.py:738
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Itens do estoque instalado devem existir na LDM para a parte principal"

#: common/setting/system.py:744
msgid "Allow Out of Stock Transfer"
msgstr "Permitir Fora de Transferência"

#: common/setting/system.py:746
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Permitir que os itens que não estão em estoque sejam transferidos entre locais de estoque"

#: common/setting/system.py:752
msgid "Build Order Reference Pattern"
msgstr "Modelo de Referência de Pedidos de Produção"

#: common/setting/system.py:753
msgid "Required pattern for generating Build Order reference field"
msgstr "Modelo necessário para gerar campo de referência do Pedido de Produção"

#: common/setting/system.py:758 common/setting/system.py:818
#: common/setting/system.py:838 common/setting/system.py:874
msgid "Require Responsible Owner"
msgstr "Exigir proprietário responsável"

#: common/setting/system.py:759 common/setting/system.py:819
#: common/setting/system.py:839 common/setting/system.py:875
msgid "A responsible owner must be assigned to each order"
msgstr "Um proprietário responsável deve ser atribuído a cada pedido"

#: common/setting/system.py:764
msgid "Require Active Part"
msgstr "Requer Parte Ativa"

#: common/setting/system.py:765
msgid "Prevent build order creation for inactive parts"
msgstr "Impedir a criação de ordem para partes inativas"

#: common/setting/system.py:770
msgid "Require Locked Part"
msgstr "Exigir parte bloqueada"

#: common/setting/system.py:771
msgid "Prevent build order creation for unlocked parts"
msgstr "Impedir criação de pedidos para peças desbloqueadas"

#: common/setting/system.py:776
msgid "Require Valid BOM"
msgstr "Exigir validade, BOM"

#: common/setting/system.py:777
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Impedir criação de pedido de compilação a menos que LDM tenha sido validada"

#: common/setting/system.py:782
msgid "Require Closed Child Orders"
msgstr "Exigir pedidos secundários fechados"

#: common/setting/system.py:784
msgid "Prevent build order completion until all child orders are closed"
msgstr "Impedir o preenchimento do pedido de construção até que todos os pedidos secundários sejam fechados"

#: common/setting/system.py:790
msgid "External Build Orders"
msgstr "Pedido de Produção Externo"

#: common/setting/system.py:791
msgid "Enable external build order functionality"
msgstr "Ativar funcionalidade de pedido de construção externa"

#: common/setting/system.py:796
msgid "Block Until Tests Pass"
msgstr "Bloquear Até Passagem de Testes"

#: common/setting/system.py:798
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Impedir que as saídas da produção sejam concluídas até que todos os testes necessários passem"

#: common/setting/system.py:804
msgid "Enable Return Orders"
msgstr "Ativar Pedidos de Devolução"

#: common/setting/system.py:805
msgid "Enable return order functionality in the user interface"
msgstr "Ativar funcionalidade de pedido de devolução na interface do usuário"

#: common/setting/system.py:810
msgid "Return Order Reference Pattern"
msgstr "Modelo de Referência de Pedidos de Devolução"

#: common/setting/system.py:812
msgid "Required pattern for generating Return Order reference field"
msgstr "Modelo necessário para gerar campo de referência do Pedido de Devolução"

#: common/setting/system.py:824
msgid "Edit Completed Return Orders"
msgstr "Editar os Pedidos de Devolução Concluídos"

#: common/setting/system.py:826
msgid "Allow editing of return orders after they have been completed"
msgstr "Permitir a edição de pedidos de devolução após serem enviados ou concluídos"

#: common/setting/system.py:832
msgid "Sales Order Reference Pattern"
msgstr "Modelo de Referência de Pedidos de Venda"

#: common/setting/system.py:833
msgid "Required pattern for generating Sales Order reference field"
msgstr "Modelo necessário para gerar campo de referência do Pedido de Venda"

#: common/setting/system.py:844
msgid "Sales Order Default Shipment"
msgstr "Envio Padrão de Pedidos de Venda"

#: common/setting/system.py:845
msgid "Enable creation of default shipment with sales orders"
msgstr "Habilitar criação de envio padrão com Pedidos de Vendas"

#: common/setting/system.py:850
msgid "Edit Completed Sales Orders"
msgstr "Editar os Pedidos de Vendas concluídos"

#: common/setting/system.py:852
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Permitir a edição de pedidos de vendas após serem enviados ou concluídos"

#: common/setting/system.py:858
msgid "Mark Shipped Orders as Complete"
msgstr "Marcar pedidos enviados como concluídos"

#: common/setting/system.py:860
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Pedidos de vendas marcados como enviados automaticamente serão concluídos, ignorando o status \"enviado\""

#: common/setting/system.py:866
msgid "Purchase Order Reference Pattern"
msgstr "Modelo de Referência de Pedidos de Compras"

#: common/setting/system.py:868
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Modelo necessário para gerar campo de referência do Pedido de Compra"

#: common/setting/system.py:880
msgid "Edit Completed Purchase Orders"
msgstr "Editar Pedidos de Compra Concluídos"

#: common/setting/system.py:882
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Permitir a edição de pedidos de compras após serem enviados ou concluídos"

#: common/setting/system.py:888
msgid "Convert Currency"
msgstr "Converter Moeda"

#: common/setting/system.py:889
msgid "Convert item value to base currency when receiving stock"
msgstr "Converter valor de item para moeda base quando receber o estoque"

#: common/setting/system.py:894
msgid "Auto Complete Purchase Orders"
msgstr "Completar automaticamente os pedidos de Compra"

#: common/setting/system.py:896
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Marcar automaticamente os pedidos de compra como concluídos quando todos os itens de linha forem recebidos"

#: common/setting/system.py:903
msgid "Enable password forgot"
msgstr "Ativar senha esquecida"

#: common/setting/system.py:904
msgid "Enable password forgot function on the login pages"
msgstr "Ativar a função \"Esqueci minha senha\" nas páginas de acesso"

#: common/setting/system.py:909
msgid "Enable registration"
msgstr "Ativar cadastro"

#: common/setting/system.py:910
msgid "Enable self-registration for users on the login pages"
msgstr "Ativar auto-registro para usuários na página de entrada"

#: common/setting/system.py:915
msgid "Enable SSO"
msgstr "Ativar SSO"

#: common/setting/system.py:916
msgid "Enable SSO on the login pages"
msgstr "Ativar SSO na página de acesso"

#: common/setting/system.py:921
msgid "Enable SSO registration"
msgstr "Ativar registro SSO"

#: common/setting/system.py:923
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Ativar auto-registro via SSO para usuários nas páginas de login"

#: common/setting/system.py:929
msgid "Enable SSO group sync"
msgstr "Ativar sincronização de grupo SSO"

#: common/setting/system.py:931
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Ativar sincronização de grupos do InvenTree com grupos fornecidos pelo IdP"

#: common/setting/system.py:937
msgid "SSO group key"
msgstr "Chave de grupo SSO"

#: common/setting/system.py:938
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "O nome dos grupos reivindicam o atributo fornecido pelo IdP"

#: common/setting/system.py:943
msgid "SSO group map"
msgstr "Mapa do grupo SSO"

#: common/setting/system.py:945
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Um mapeamento de grupos de SSO para grupos locais de InvenTree. Se o grupo local não existir, será criado."

#: common/setting/system.py:951
msgid "Remove groups outside of SSO"
msgstr "Remover grupos fora do SSO"

#: common/setting/system.py:953
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Se os grupos atribuídos ao usuário devem ser removidos somente se eles não são o backend pelo IdP. Pois, essa configuração desabilitada pode causar problemas de segurança"

#: common/setting/system.py:959
msgid "Email required"
msgstr "Email obrigatório"

#: common/setting/system.py:960
msgid "Require user to supply mail on signup"
msgstr "Exigir do usuário o e-mail no cadastro"

#: common/setting/system.py:965
msgid "Auto-fill SSO users"
msgstr "Auto-preencher usuários SSO"

#: common/setting/system.py:966
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Preencher automaticamente os detalhes do usuário a partir de dados da conta SSO"

#: common/setting/system.py:971
msgid "Mail twice"
msgstr "Enviar email duplo"

#: common/setting/system.py:972
msgid "On signup ask users twice for their mail"
msgstr "Ao se registrar, peça aos usuários duas vezes por seus e-mails"

#: common/setting/system.py:977
msgid "Password twice"
msgstr "Senha duas vezes"

#: common/setting/system.py:978
msgid "On signup ask users twice for their password"
msgstr "No registro pedir aos usuários duas vezes pela senha"

#: common/setting/system.py:983
msgid "Allowed domains"
msgstr "Domínios permitidos"

#: common/setting/system.py:985
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Restringir registros a certos domínios (separados por vírgula, começando com @)"

#: common/setting/system.py:991
msgid "Group on signup"
msgstr "Grupo no cadastro"

#: common/setting/system.py:993
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Grupo ao qual novos usuários serão atribuídos ao registro. Se a sincronização de grupo SSO estiver ativada, este grupo só estará definido se nenhum grupo puder ser atribuído a partir do IdP."

#: common/setting/system.py:999
msgid "Enforce MFA"
msgstr "Forçar AMF"

#: common/setting/system.py:1000
msgid "Users must use multifactor security."
msgstr "Os usuários devem usar uma segurança multifatorial."

#: common/setting/system.py:1005
msgid "Check plugins on startup"
msgstr "Verificar extensões na inicialização"

#: common/setting/system.py:1007
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Checar que todas as extensões instaladas no início — ativar em ambientes de contêineres"

#: common/setting/system.py:1014
msgid "Check for plugin updates"
msgstr "Verificar por atualizações de extensão"

#: common/setting/system.py:1015
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Ativar verificações periódicas de atualizações para a extensão instalados"

#: common/setting/system.py:1021
msgid "Enable URL integration"
msgstr "Ativar integração URL"

#: common/setting/system.py:1022
msgid "Enable plugins to add URL routes"
msgstr "Ativar extensão para adicionar rotas URL"

#: common/setting/system.py:1028
msgid "Enable navigation integration"
msgstr "Ativar integração de navegação"

#: common/setting/system.py:1029
msgid "Enable plugins to integrate into navigation"
msgstr "Ativar extensões para integrar à navegação"

#: common/setting/system.py:1035
msgid "Enable app integration"
msgstr "Ativar integração com aplicativo"

#: common/setting/system.py:1036
msgid "Enable plugins to add apps"
msgstr "Ativar extensões para adicionar aplicativos"

#: common/setting/system.py:1042
msgid "Enable schedule integration"
msgstr "Ativar integração com agendas"

#: common/setting/system.py:1043
msgid "Enable plugins to run scheduled tasks"
msgstr "Ativar extensões para executar tarefas agendadas"

#: common/setting/system.py:1049
msgid "Enable event integration"
msgstr "Ativar integração de eventos"

#: common/setting/system.py:1050
msgid "Enable plugins to respond to internal events"
msgstr "Ativar extensões para responder a eventos internos"

#: common/setting/system.py:1056
msgid "Enable interface integration"
msgstr "Ativar integração de interface"

#: common/setting/system.py:1057
msgid "Enable plugins to integrate into the user interface"
msgstr "Ativar extensões para integrar na interface do usuário"

#: common/setting/system.py:1063
msgid "Enable mail integration"
msgstr "Ativar integração com o e-mail"

#: common/setting/system.py:1064
msgid "Enable plugins to process outgoing/incoming mails"
msgstr "Ativar extensão para processar e-mails de saída/entrada"

#: common/setting/system.py:1070
msgid "Enable project codes"
msgstr "Ativar códigos de projeto"

#: common/setting/system.py:1071
msgid "Enable project codes for tracking projects"
msgstr "Ativar códigos de projeto para rastrear projetos"

#: common/setting/system.py:1076
msgid "Enable Stock History"
msgstr "Ativar Histórico de Ações"

#: common/setting/system.py:1078
msgid "Enable functionality for recording historical stock levels and value"
msgstr "Ativar funcionalidade para gravação de níveis e valor de estoque históricos"

#: common/setting/system.py:1084
msgid "Exclude External Locations"
msgstr "Excluir Locais Externos"

#: common/setting/system.py:1086
msgid "Exclude stock items in external locations from stock history calculations"
msgstr "Excluir itens de estoque em locais externos dos cálculos do histórico de ações"

#: common/setting/system.py:1092
msgid "Automatic Stocktake Period"
msgstr "Período de contagem automática"

#: common/setting/system.py:1093
msgid "Number of days between automatic stock history recording"
msgstr "Número de dias entre gravação automática de histórico de estoque"

#: common/setting/system.py:1099
msgid "Delete Old Stock History Entries"
msgstr "Excluir entradas antigas do histórico do estoque"

#: common/setting/system.py:1101
msgid "Delete stock history entries older than the specified number of days"
msgstr "Eliminar entradas no histórico de ações anteriores ao número especificado de dias"

#: common/setting/system.py:1107
msgid "Stock History Deletion Interval"
msgstr "Intervalo de Exclusão do Histórico"

#: common/setting/system.py:1109
msgid "Stock history entries will be deleted after specified number of days"
msgstr "Histórico de ações de estoque será excluído após um número especificado de dias"

#: common/setting/system.py:1116
msgid "Display Users full names"
msgstr "Exibir nomes completos dos usuários"

#: common/setting/system.py:1117
msgid "Display Users full names instead of usernames"
msgstr "Exibir nomes completos dos usuários em vez de nomes de usuários"

#: common/setting/system.py:1122
msgid "Display User Profiles"
msgstr "Exibir Perfis de Usuário"

#: common/setting/system.py:1123
msgid "Display Users Profiles on their profile page"
msgstr "Exibir Perfis de Usuários em sua página de perfil"

#: common/setting/system.py:1128
msgid "Enable Test Station Data"
msgstr "Ativar Dados da Estação de Teste"

#: common/setting/system.py:1129
msgid "Enable test station data collection for test results"
msgstr "Ativar coleção de dados da estação de teste para resultados de teste"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Mostrar etiqueta em linha"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Mostrar etiquetas em PDF no navegador, ao invés de baixar o arquivo"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Impressora de etiquetas padrão"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Configurar qual impressora de etiqueta deve ser selecionada por padrão"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Mostrar relatório em linha"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Mostrar relatórios em PDF no navegador, ao invés de baixar o arquivo"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Procurar Peças"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Mostrar peças na janela de visualização de pesquisa"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Buscar Peças do Fornecedor"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Mostrar fornecedor de peças na janela de visualização de pesquisa"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Buscar peças do fabricante"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Mostrar fabricante de peças na janela de visualização de pesquisa"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Ocultar peças inativas"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Não incluir peças inativas na janela de visualização de pesquisa"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Pesquisar Categorias"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Mostrar categoria das peças na janela de visualização de pesquisa"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Pesquisar Estoque"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Mostrar itens do estoque na janela de visualização de pesquisa"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Ocultar itens do estoque indisponíveis"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Não incluir itens de estoque que não estão disponíveis na janela de visualização de pesquisa"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Procurar Locais"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Mostrar locais de estoque na janela de visualização de pesquisa"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Pesquisar empresas"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Mostrar empresas na janela de visualização de pesquisa"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Pesquisar Pedidos de Produção"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Mostrar pedidos de produção na janela de visualização de pesquisa"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Pesquisar Pedido de Compras"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Mostrar pedidos de compra na janela de visualização de pesquisa"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Não incluir Pedidos de Compras Inativos"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Não incluir pedidos de compras inativos na janela de visualização de pesquisa"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Pesquisar Pedidos de Vendas"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Exibir pedidos de vendas na janela de visualização de pesquisa"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Não Incluir Pedidos de Compras Inativas"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Não incluir pedidos de vendas inativos na janela de visualização de pesquisa"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Pesquisar envios do Pedido de Venda"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Exibir envios do pedido de venda na janela de pré-visualização"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Procurar Pedidos de Devolução"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Exibir pedidos de devolução na janela de pré-visualização de pesquisa"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Não Incluir Pedidos de Devolução Inativas"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Não incluir pedidos de devolução inativos na janela de visualização de pesquisa"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Ver resultados da pesquisa"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Número de resultados mostrados em cada seção da janela de visualização de pesquisa"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Pesquisa de Regex"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Ativar expressões regulares nas consultas de pesquisa"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Pesquisar Palavras Inteiras"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Consultas de pesquisa retornam resultados para partidas de palavra inteira"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Pesquisar Notas"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "Resultados das consultas de pesquisa para correspondências das notas do item"

#: common/setting/user.py:177
msgid "Escape Key Closes Forms"
msgstr "Tecla Esc Fecha Formulários"

#: common/setting/user.py:178
msgid "Use the escape key to close modal forms"
msgstr "Usar a tecla Esc para fechar fomulários modais"

#: common/setting/user.py:183
msgid "Fixed Navbar"
msgstr "Barra de navegação fixa"

#: common/setting/user.py:184
msgid "The navbar position is fixed to the top of the screen"
msgstr "A posição da barra de navegação está fixa no topo da tela"

#: common/setting/user.py:189
msgid "Fixed Table Headers"
msgstr "Cabeçalhos da Tabela Fixa"

#: common/setting/user.py:190
msgid "Table headers are fixed to the top of the table"
msgstr "Cabeçalhos de tabela são fixos no topo da tabela"

#: common/setting/user.py:195
msgid "Show Spotlight"
msgstr ""

#: common/setting/user.py:196
msgid "Enable spotlight navigation functionality"
msgstr ""

#: common/setting/user.py:201
msgid "Navigation Icons"
msgstr "Ícones de Navegação"

#: common/setting/user.py:202
msgid "Display icons in the navigation bar"
msgstr "Exibir ícones na barra de navegação"

#: common/setting/user.py:207
msgid "Date Format"
msgstr "Formato da data"

#: common/setting/user.py:208
msgid "Preferred format for displaying dates"
msgstr "Formato preferido para exibir datas"

#: common/setting/user.py:221
msgid "Show Stock History"
msgstr "Mostrar histórico de ações"

#: common/setting/user.py:222
msgid "Display stock history information in the part detail page"
msgstr "Mostrar informações do histórico de estoque na página de detalhes do capítulo"

#: common/setting/user.py:227
msgid "Show Last Breadcrumb"
msgstr "Mostrar última navegação"

#: common/setting/user.py:228
msgid "Show the current page in breadcrumbs"
msgstr "Mostrar a página atual em breadcrumbs"

#: common/setting/user.py:233
msgid "Show full stock location in tables"
msgstr "Mostrar localização completa do estoque nas tabelas"

#: common/setting/user.py:235
msgid "Disabled: The full location path is displayed as a hover tooltip. Enabled: The full location path is displayed as plain text."
msgstr "Desativado: O caminho completo da localização é exibido como uma dica do mouse. Ativado: O caminho completo da localização é exibido como texto sem formatação."

#: common/setting/user.py:241
msgid "Show full part categories in tables"
msgstr "Mostrar categorias completas do capítulo nas tabelas"

#: common/setting/user.py:243
msgid "Disabled: The full category path is displayed as a hover tooltip. Enabled: The full category path is displayed as plain text."
msgstr "Desativado: O caminho completo da categoria é exibido como uma dica do mouse. Habilitado: O caminho completo da categoria é exibido como texto sem formatação."

#: common/setting/user.py:249
msgid "Receive error reports"
msgstr "Receber relatório de erros"

#: common/setting/user.py:250
msgid "Receive notifications for system errors"
msgstr "Receber notificações para erros do sistema"

#: common/setting/user.py:255
msgid "Last used printing machines"
msgstr "Últimas máquinas de impressão utilizadas"

#: common/setting/user.py:256
msgid "Save the last used printing machines for a user"
msgstr "Salvar as últimas máquinas de impressão usadas para um usuário"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Nenhum modelo de anexo fornecido"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Categoria de modelo de anexo inválido"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Os lugares mínimos não podem ser maiores que os máximos"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Os lugares maiores não podem ser menores que os máximos"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Um domínio vazio não é permitido."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Nome de domínio inválido: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "Valor deve ser maiúsculo"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "O valor deve ser um identificador de variável válido"

#: company/api.py:141
msgid "Part is Active"
msgstr "A peça está ativa"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Fabricante está ativo"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "A peça do Fornecedor está ativa"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "A peça interna está ativa"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "O fornecedor está Ativo"

#: company/api.py:299 company/models.py:539 company/serializers.py:468
#: part/serializers.py:575
msgid "Manufacturer"
msgstr "Fabricante"

#: company/api.py:306 company/models.py:119 company/models.py:411
#: stock/api.py:877
msgid "Company"
msgstr "Empresa"

#: company/api.py:316
msgid "Has Stock"
msgstr "Tem estoque"

#: company/models.py:120
msgid "Companies"
msgstr "Empresas"

#: company/models.py:148
msgid "Company description"
msgstr "Descrição da empresa"

#: company/models.py:149
msgid "Description of the company"
msgstr "Descrição da empresa"

#: company/models.py:155
msgid "Website"
msgstr "Página Web"

#: company/models.py:156
msgid "Company website URL"
msgstr "URL do Site da empresa"

#: company/models.py:162
msgid "Phone number"
msgstr "Número de telefone"

#: company/models.py:164
msgid "Contact phone number"
msgstr "Número de telefone do contato"

#: company/models.py:171
msgid "Contact email address"
msgstr "Endereço de e-mail do contato"

#: company/models.py:176 company/models.py:315 order/models.py:506
#: users/models.py:567
msgid "Contact"
msgstr "Contato"

#: company/models.py:178
msgid "Point of contact"
msgstr "Ponto de contato"

#: company/models.py:184
msgid "Link to external company information"
msgstr "Link para informações externas da empresa"

#: company/models.py:198
msgid "Is this company active?"
msgstr "Esta empresa está ativa?"

#: company/models.py:203
msgid "Is customer"
msgstr "É um cliente"

#: company/models.py:204
msgid "Do you sell items to this company?"
msgstr "Você vende itens para esta empresa?"

#: company/models.py:209
msgid "Is supplier"
msgstr "É fornecedor"

#: company/models.py:210
msgid "Do you purchase items from this company?"
msgstr "Você compra itens desta empresa?"

#: company/models.py:215
msgid "Is manufacturer"
msgstr "É fabricante"

#: company/models.py:216
msgid "Does this company manufacture parts?"
msgstr "Esta empresa fabrica peças?"

#: company/models.py:224
msgid "Default currency used for this company"
msgstr "Moeda padrão utilizada para esta empresa"

#: company/models.py:231
msgid "Tax ID"
msgstr "CNPJ"

#: company/models.py:232
msgid "Company Tax ID"
msgstr "CNPJ da empresa"

#: company/models.py:354 order/models.py:516
msgid "Address"
msgstr "Endereço"

#: company/models.py:355
msgid "Addresses"
msgstr "Endereços"

#: company/models.py:412
msgid "Select company"
msgstr "Selecione a Empresa"

#: company/models.py:417
msgid "Address title"
msgstr "Título do endereço"

#: company/models.py:418
msgid "Title describing the address entry"
msgstr "Título descrevendo a entrada do endereço"

#: company/models.py:424
msgid "Primary address"
msgstr "Endereço Principal"

#: company/models.py:425
msgid "Set as primary address"
msgstr "Definir como endereço principal"

#: company/models.py:430
msgid "Line 1"
msgstr "Linha 1"

#: company/models.py:431
msgid "Address line 1"
msgstr "Linha de endereço 1"

#: company/models.py:437
msgid "Line 2"
msgstr "Linha 2"

#: company/models.py:438
msgid "Address line 2"
msgstr "Linha de endereço 2"

#: company/models.py:444 company/models.py:445
msgid "Postal code"
msgstr "CEP"

#: company/models.py:451
msgid "City/Region"
msgstr "Cidade/Região"

#: company/models.py:452
msgid "Postal code city/region"
msgstr "Cidade CEP / região"

#: company/models.py:458
msgid "State/Province"
msgstr "Estado/Provincia"

#: company/models.py:459
msgid "State or province"
msgstr "Estado ou Província"

#: company/models.py:465
msgid "Country"
msgstr "País"

#: company/models.py:466
msgid "Address country"
msgstr "País do endereço"

#: company/models.py:472
msgid "Courier shipping notes"
msgstr "Notas de envio por correio"

#: company/models.py:473
msgid "Notes for shipping courier"
msgstr "Notas para o envio da transportadora"

#: company/models.py:479
msgid "Internal shipping notes"
msgstr "Notas de envio interno"

#: company/models.py:480
msgid "Shipping notes for internal use"
msgstr "Notas de envio para uso interno"

#: company/models.py:487
msgid "Link to address information (external)"
msgstr "Link para as informações do endereço (externo)"

#: company/models.py:511 company/models.py:628 company/models.py:850
#: company/serializers.py:482
msgid "Manufacturer Part"
msgstr "Fabricante da peça"

#: company/models.py:528 company/models.py:818 stock/models.py:1026
#: stock/serializers.py:444
msgid "Base Part"
msgstr "Peça base"

#: company/models.py:530 company/models.py:820
msgid "Select part"
msgstr "Selecionar peça"

#: company/models.py:540
msgid "Select manufacturer"
msgstr "Selecionar fabricante"

#: company/models.py:546 company/serializers.py:490 order/serializers.py:672
#: part/serializers.py:585
msgid "MPN"
msgstr "NPF"

#: company/models.py:547 stock/serializers.py:606
msgid "Manufacturer Part Number"
msgstr "Número de Peça do Fabricante"

#: company/models.py:554
msgid "URL for external manufacturer part link"
msgstr "URL do link externo da peça do fabricante"

#: company/models.py:563
msgid "Manufacturer part description"
msgstr "Descrição da peça do fabricante"

#: company/models.py:616
msgid "Manufacturer Part Parameter"
msgstr "Parâmetro da peça do fabricante"

#: company/models.py:635
msgid "Parameter name"
msgstr "Nome do parâmetro"

#: company/models.py:642
msgid "Parameter value"
msgstr "Valor do Parâmetro"

#: company/models.py:649 part/models.py:1216 part/models.py:3856
msgid "Units"
msgstr "Unidades"

#: company/models.py:650
msgid "Parameter units"
msgstr "Unidades do parâmetro"

#: company/models.py:758
msgid "Pack units must be compatible with the base part units"
msgstr "Unidades de pacote devem ser compatíveis com as unidades de peça base"

#: company/models.py:765
msgid "Pack units must be greater than zero"
msgstr "Unidades de pacote devem ser maior que zero"

#: company/models.py:779
msgid "Linked manufacturer part must reference the same base part"
msgstr "Parte do fabricante vinculado deve fazer referência à mesma peça base"

#: company/models.py:828 company/serializers.py:460 company/serializers.py:478
#: order/models.py:632 part/serializers.py:559
#: plugin/builtin/suppliers/digikey.py:26 plugin/builtin/suppliers/lcsc.py:27
#: plugin/builtin/suppliers/mouser.py:25 plugin/builtin/suppliers/tme.py:27
#: stock/api.py:544 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Fornecedor"

#: company/models.py:829
msgid "Select supplier"
msgstr "Selecione o fornecedor"

#: company/models.py:835 part/serializers.py:570
msgid "Supplier stock keeping unit"
msgstr "Unidade de reserva de estoque fornecedor"

#: company/models.py:841
msgid "Is this supplier part active?"
msgstr "Esta parte de fornecedor está ativa?"

#: company/models.py:851
msgid "Select manufacturer part"
msgstr "Selecionar peça do fabricante"

#: company/models.py:858
msgid "URL for external supplier part link"
msgstr "URL do link externo da peça do fabricante"

#: company/models.py:867
msgid "Supplier part description"
msgstr "Descrição da peça fornecedor"

#: company/models.py:874 order/serializers.py:818 order/serializers.py:2020
#: part/models.py:4069 part/models.py:4444 part/models.py:4813
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:39
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:836
msgid "Note"
msgstr "Anotação"

#: company/models.py:883 part/models.py:2249
msgid "base cost"
msgstr "preço base"

#: company/models.py:884 part/models.py:2250
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Taxa mínima (ex.: taxa de estoque)"

#: company/models.py:891 order/serializers.py:810 stock/models.py:1057
#: stock/serializers.py:1642
msgid "Packaging"
msgstr "Embalagem"

#: company/models.py:892
msgid "Part packaging"
msgstr "Embalagem de peças"

#: company/models.py:897
msgid "Pack Quantity"
msgstr "Quantidade de embalagens"

#: company/models.py:899
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Quantidade total fornecida em um único pacote. Deixe em branco para itens individuais."

#: company/models.py:918 part/models.py:2256
msgid "multiple"
msgstr "múltiplo"

#: company/models.py:919
msgid "Order multiple"
msgstr "Pedido múltiplo"

#: company/models.py:931
msgid "Quantity available from supplier"
msgstr "Quantidade disponível do fornecedor"

#: company/models.py:937
msgid "Availability Updated"
msgstr "Disponibilidade Atualizada"

#: company/models.py:938
msgid "Date of last update of availability data"
msgstr "Data da última atualização de dados disponíveis"

#: company/models.py:1066
msgid "Supplier Price Break"
msgstr "Parcelamento de Preço do Fornecedor"

#: company/serializers.py:178
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr "Retorna a representação de cadeia de caracteres para o endereço primário. Esta propriedade existe para compatibilidade retroativa."

#: company/serializers.py:209
msgid "Default currency used for this supplier"
msgstr "Moeda padrão utilizada para este fornecedor"

#: company/serializers.py:245
msgid "Company Name"
msgstr "Nome da Empresa"

#: company/serializers.py:444 part/serializers.py:948 stock/serializers.py:462
msgid "In Stock"
msgstr "Em Estoque"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:402
msgid "Error occurred during data export"
msgstr "Ocorreu um erro ao exportar os dados"

#: data_exporter/mixins.py:380
msgid "Data export plugin returned incorrect data format"
msgstr "A extensão de exportação de dados retornou dados incorretos"

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Formato de Exportação"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "Selecionar formato do arquivo de exportação"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Exportar extensão"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "Selecionar extensão de exportação"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Informação adicional de status para este item"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Tecla de status personalizada"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Personalizado"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Turma"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Valores"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Localizado"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Código de status inválido"

#: importer/models.py:73
msgid "Data File"
msgstr "Arquivo de dados"

#: importer/models.py:74
msgid "Data file to import"
msgstr "Arquivo de dados para importar"

#: importer/models.py:83
msgid "Columns"
msgstr "Colunas"

#: importer/models.py:90
msgid "Target model type for this import session"
msgstr "Categoria de modelo de destino para esta sessão de importação"

#: importer/models.py:96
msgid "Import status"
msgstr "Status de importação"

#: importer/models.py:106
msgid "Field Defaults"
msgstr "Campos padrões"

#: importer/models.py:113
msgid "Field Overrides"
msgstr "Substituições de campo"

#: importer/models.py:120
msgid "Field Filters"
msgstr "Campo de Filtros"

#: importer/models.py:126
msgid "Update Existing Records"
msgstr ""

#: importer/models.py:127
msgid "If enabled, existing records will be updated with new data"
msgstr ""

#: importer/models.py:259
msgid "Some required fields have not been mapped"
msgstr "Alguns campos necessários não foram mapeados"

#: importer/models.py:368
msgid "ID"
msgstr ""

#: importer/models.py:369
msgid "Existing database identifier for the record"
msgstr ""

#: importer/models.py:432
msgid "Column is already mapped to a database field"
msgstr "A coluna já está mapeada a um campo de banco de dados"

#: importer/models.py:437
msgid "Field is already mapped to a data column"
msgstr "O campo já está mapeado para uma coluna de dados"

#: importer/models.py:446
msgid "Column mapping must be linked to a valid import session"
msgstr "Mapeamento de coluna deve ser ligado a uma sessão de importação válida"

#: importer/models.py:451
msgid "Column does not exist in the data file"
msgstr "A coluna não existe no arquivo de dados"

#: importer/models.py:458
msgid "Field does not exist in the target model"
msgstr "O campo não existe no modelo de destino"

#: importer/models.py:462
msgid "Selected field is read-only"
msgstr "O campo selecionado é somente leitura"

#: importer/models.py:467 importer/models.py:538
msgid "Import Session"
msgstr "Importar Sessão"

#: importer/models.py:471
msgid "Field"
msgstr "Campo"

#: importer/models.py:473
msgid "Column"
msgstr "Coluna"

#: importer/models.py:542
msgid "Row Index"
msgstr "Índice de fileira"

#: importer/models.py:545
msgid "Original row data"
msgstr "Dados da linha original"

#: importer/models.py:550 machine/models.py:110
msgid "Errors"
msgstr "Erros"

#: importer/models.py:552 part/serializers.py:1239
msgid "Valid"
msgstr "Válido"

#: importer/models.py:690
msgid "ID is required for updating existing records."
msgstr ""

#: importer/models.py:696
msgid "No record found with the provided ID."
msgstr ""

#: importer/models.py:698
msgid "Invalid ID format provided."
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Formato de arquivo de dados não suportado"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Falha ao abrir o arquivo de dados"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Dimensões de arquivo de dados inválidas"

#: importer/serializers.py:92
msgid "Invalid field defaults"
msgstr "Padrão de campo inválido"

#: importer/serializers.py:105
msgid "Invalid field overrides"
msgstr "Substituição de campo inválida"

#: importer/serializers.py:118
msgid "Invalid field filters"
msgstr "Filtros de campo inválidos"

#: importer/serializers.py:177
msgid "Rows"
msgstr "Linhas"

#: importer/serializers.py:178
msgid "List of row IDs to accept"
msgstr "Lista de ID das linhas para aceitar"

#: importer/serializers.py:191
msgid "No rows provided"
msgstr "Nenhuma linha fornecida"

#: importer/serializers.py:195
msgid "Row does not belong to this session"
msgstr "A linha não pertence a esta sessão"

#: importer/serializers.py:198
msgid "Row contains invalid data"
msgstr "A linha contém dados inválidos"

#: importer/serializers.py:201
msgid "Row has already been completed"
msgstr "A linha já foi concluída"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Inicialização"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Mapeando Colunas"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Importando dados"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Processando dados"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "O arquivo de dados excede o limite de tamanho máximo"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "O arquivo de dados não contém cabeçalhos"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Arquivo de dados contém colunas em excesso"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Arquivo de dados contém linhas em excesso"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Valor deve ser um objeto de dicionário válido"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Cópias"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Número de cópias para cada rótulo"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Conectado"

#: machine/machine_types/label_printer.py:229 order/api.py:1759
msgid "Unknown"
msgstr "Desconhecido"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Imprimindo"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Sem mídia"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Papel enroscado"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Desconectado"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Impressora de etiqueta"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Imprima etiquetas diretamente para vários itens."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Local da impressora"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Escopo da impressora para um local específico"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Nome da máquina"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Categoria de máquina"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Categoria de máquina"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Controlador"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Controlador utilizado para a máquina"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Máquinas podem ser desativadas"

#: machine/models.py:95
msgid "Driver available"
msgstr "Controlador disponível"

#: machine/models.py:100
msgid "No errors"
msgstr "Nenhum erro"

#: machine/models.py:105
msgid "Initialized"
msgstr "Iniciado"

#: machine/models.py:117
msgid "Machine status"
msgstr "Estado da máquina"

#: machine/models.py:145
msgid "Machine"
msgstr "Máquina"

#: machine/models.py:157
msgid "Machine Config"
msgstr "Configuração da Máquina"

#: machine/models.py:162
msgid "Config type"
msgstr "Categoria de configuração"

#: order/api.py:121
msgid "Order Reference"
msgstr "Referência do Pedido"

#: order/api.py:149 order/api.py:1198
msgid "Outstanding"
msgstr "Pendente"

#: order/api.py:165
msgid "Has Project Code"
msgstr "Tem código do projeto"

#: order/api.py:179 order/models.py:481
msgid "Created By"
msgstr "Criado por"

#: order/api.py:183
msgid "Created Before"
msgstr "Criado Antes"

#: order/api.py:187
msgid "Created After"
msgstr "Criado Após"

#: order/api.py:191
msgid "Has Start Date"
msgstr "Tem Data Inicial"

#: order/api.py:199
msgid "Start Date Before"
msgstr "Data Inicial Antes"

#: order/api.py:203
msgid "Start Date After"
msgstr "Data Inicial Após"

#: order/api.py:207
msgid "Has Target Date"
msgstr "Tem Data Prevista"

#: order/api.py:215
msgid "Target Date Before"
msgstr "Data Prevista Antes"

#: order/api.py:219
msgid "Target Date After"
msgstr "Data Prevista Antes"

#: order/api.py:270
msgid "Has Pricing"
msgstr "Tem Preço"

#: order/api.py:323 order/api.py:803 order/api.py:1455
msgid "Completed Before"
msgstr "Concluído Antes"

#: order/api.py:327 order/api.py:807 order/api.py:1459
msgid "Completed After"
msgstr "Concluído Após"

#: order/api.py:333 order/api.py:337
msgid "External Build Order"
msgstr "Pedido de Produção Vencido"

#: order/api.py:520 order/api.py:905 order/api.py:1161 order/models.py:1864
#: order/models.py:1993 order/models.py:2044 order/models.py:2198
#: order/models.py:2364 order/models.py:2886 order/models.py:2952
msgid "Order"
msgstr "Pedido"

#: order/api.py:524 order/api.py:973
msgid "Order Complete"
msgstr "Pedido Completo"

#: order/api.py:556 order/api.py:560 order/serializers.py:683
msgid "Internal Part"
msgstr "Peça Interna"

#: order/api.py:578
msgid "Order Pending"
msgstr "Pedido pendente"

#: order/api.py:958
msgid "Completed"
msgstr "Concluído"

#: order/api.py:1214
msgid "Has Shipment"
msgstr "Possui Envio"

#: order/api.py:1753 order/models.py:545 order/models.py:1865
#: order/models.py:1994
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Pedido de Compra"

#: order/api.py:1755 order/models.py:1232 order/models.py:2045
#: order/models.py:2199 order/models.py:2365
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Pedido de Venda"

#: order/api.py:1757 order/models.py:2536 order/models.py:2887
#: order/models.py:2953
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Pedido de Devolução"

#: order/models.py:90
#: report/templates/report/inventree_purchase_order_report.html:38
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Preço Total"

#: order/models.py:91
msgid "Total price for this order"
msgstr "Preço total deste pedido"

#: order/models.py:96 order/serializers.py:76
msgid "Order Currency"
msgstr "Moeda do Pedido"

#: order/models.py:99 order/serializers.py:77
msgid "Currency for this order (leave blank to use company default)"
msgstr "Moeda para este pedido (deixe em branco para usar o padrão da empresa)"

#: order/models.py:324
msgid "This order is locked and cannot be modified"
msgstr "Este pedido está bloqueado e não pode ser modificado"

#: order/models.py:376
msgid "Contact does not match selected company"
msgstr "O contato não corresponde à empresa selecionada"

#: order/models.py:383
msgid "Start date must be before target date"
msgstr "Data inicial deve ser anterior à data limite"

#: order/models.py:436
msgid "Order description (optional)"
msgstr "Descrição do pedido (opcional)"

#: order/models.py:445
msgid "Select project code for this order"
msgstr "Selecione o código do projeto para este pedido"

#: order/models.py:451 order/models.py:1741 order/models.py:2253
msgid "Link to external page"
msgstr "Link para página externa"

#: order/models.py:458
msgid "Start date"
msgstr "Data inicial"

#: order/models.py:459
msgid "Scheduled start date for this order"
msgstr "Data de início programada para esta encomenda"

#: order/models.py:465 order/models.py:1748 order/serializers.py:270
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Data Prevista"

#: order/models.py:467
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Data esperada para entrega do pedido. O Pedido estará atrasado após esta data."

#: order/models.py:487
msgid "Issue Date"
msgstr "Data de emissão"

#: order/models.py:488
msgid "Date order was issued"
msgstr "Dia que o pedido foi feito"

#: order/models.py:496
msgid "User or group responsible for this order"
msgstr "Usuário ou grupo responsável para este pedido"

#: order/models.py:507
msgid "Point of contact for this order"
msgstr "Ponto de contato para este pedido"

#: order/models.py:517
msgid "Company address for this order"
msgstr "Endereço da empresa para este pedido"

#: order/models.py:608 order/models.py:1293
msgid "Order reference"
msgstr "Referência do pedido"

#: order/models.py:617 order/models.py:1317 order/models.py:2624
#: stock/serializers.py:593 stock/serializers.py:1011 users/models.py:548
msgid "Status"
msgstr "Situação"

#: order/models.py:618
msgid "Purchase order status"
msgstr "Estado do pedido"

#: order/models.py:633
msgid "Company from which the items are being ordered"
msgstr "Empresa da qual os itens estão sendo encomendados"

#: order/models.py:644
msgid "Supplier Reference"
msgstr "Referencia do fornecedor"

#: order/models.py:645
msgid "Supplier order reference code"
msgstr "Código de referência do pedido fornecedor"

#: order/models.py:654
msgid "received by"
msgstr "recebido por"

#: order/models.py:661 order/models.py:2639
msgid "Date order was completed"
msgstr "Dia que o pedido foi concluído"

#: order/models.py:670 order/models.py:1923
msgid "Destination"
msgstr "Destino"

#: order/models.py:671 order/models.py:1927
msgid "Destination for received items"
msgstr "Destino para os itens recebidos"

#: order/models.py:717
msgid "Part supplier must match PO supplier"
msgstr "Fornecedor de peça deve corresponder a fornecedor da OC"

#: order/models.py:984
msgid "Line item does not match purchase order"
msgstr ""

#: order/models.py:987
msgid "Line item is missing a linked part"
msgstr ""

#: order/models.py:1001
msgid "Quantity must be a positive number"
msgstr "Quantidade deve ser um número positivo"

#: order/models.py:1304 order/models.py:2611 stock/models.py:1079
#: stock/models.py:1080 stock/serializers.py:1358
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Cliente"

#: order/models.py:1305
msgid "Company to which the items are being sold"
msgstr "Empresa para qual os itens foi vendidos"

#: order/models.py:1318
msgid "Sales order status"
msgstr "Situação do Pedido de Venda"

#: order/models.py:1329 order/models.py:2631
msgid "Customer Reference "
msgstr "Referência do Cliente "

#: order/models.py:1330 order/models.py:2632
msgid "Customer order reference code"
msgstr "Código de Referência do pedido do cliente"

#: order/models.py:1334 order/models.py:2205
msgid "Shipment Date"
msgstr "Data de envio"

#: order/models.py:1343
msgid "shipped by"
msgstr "enviado por"

#: order/models.py:1382
msgid "Order is already complete"
msgstr "O pedido já está completo"

#: order/models.py:1385
msgid "Order is already cancelled"
msgstr "O pedido já está cancelado"

#: order/models.py:1389
msgid "Only an open order can be marked as complete"
msgstr "Apenas um pedido aberto pode ser marcado como completo"

#: order/models.py:1393
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Pedido não pode ser concluído, pois, há envios incompletos"

#: order/models.py:1398
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "O pedido não pode ser concluído, pois, há alocações incompletas"

#: order/models.py:1403
msgid "Order cannot be completed as there are incomplete line items"
msgstr "O pedido não pode ser concluído, pois, há itens de linha incompletos"

#: order/models.py:1687 order/models.py:1703
msgid "The order is locked and cannot be modified"
msgstr "O pedido está bloqueado e não pode ser modificado"

#: order/models.py:1711
msgid "Item quantity"
msgstr "Quantidade do item"

#: order/models.py:1728
msgid "Line item reference"
msgstr "Referência do Item em Linha"

#: order/models.py:1735
msgid "Line item notes"
msgstr "Observações do Item de Linha"

#: order/models.py:1750
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Data limite para este item de linha (deixe em branco para usar a data limite do pedido)"

#: order/models.py:1771
msgid "Line item description (optional)"
msgstr "Descrição do item de linha (opcional)"

#: order/models.py:1778
msgid "Additional context for this line"
msgstr "Contexto adicional para esta linha"

#: order/models.py:1788
msgid "Unit price"
msgstr "Preço Unitário"

#: order/models.py:1807
msgid "Purchase Order Line Item"
msgstr "Item de linha de pedido de compra"

#: order/models.py:1831
msgid "Supplier part must match supplier"
msgstr "A peça do fornecedor deve corresponder ao fornecedor"

#: order/models.py:1836
msgid "Build order must be marked as external"
msgstr "Pedido de produção deve ser marcada como externa"

#: order/models.py:1843
msgid "Build orders can only be linked to assembly parts"
msgstr "Os pedidos de produção só podem ser vinculados a partes de montagem"

#: order/models.py:1849
msgid "Build order part must match line item part"
msgstr "Criar parte do pedido deve combinar a parte do item de linha"

#: order/models.py:1884
msgid "Supplier part"
msgstr "Fornecedor da Peça"

#: order/models.py:1891
msgid "Received"
msgstr "Recebido"

#: order/models.py:1892
msgid "Number of items received"
msgstr "Número de itens recebidos"

#: order/models.py:1900 stock/models.py:1202 stock/serializers.py:660
msgid "Purchase Price"
msgstr "Preço de Compra"

#: order/models.py:1901
msgid "Unit purchase price"
msgstr "Preço unitário de compra"

#: order/models.py:1917
msgid "External Build Order to be fulfilled by this line item"
msgstr "Pedido de produção externa para ser preenchida por este item de linha"

#: order/models.py:1982
msgid "Purchase Order Extra Line"
msgstr "Linha Extra do Pedido de Compra"

#: order/models.py:2011
msgid "Sales Order Line Item"
msgstr "Item de Linha de Pedido de Vendas"

#: order/models.py:2032
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Peça virtual não pode ser atribuída a um pedido de venda"

#: order/models.py:2037
msgid "Only salable parts can be assigned to a sales order"
msgstr "Apenas peças vendáveis podem ser atribuídas a um pedido de venda"

#: order/models.py:2063
msgid "Sale Price"
msgstr "Preço de Venda"

#: order/models.py:2064
msgid "Unit sale price"
msgstr "Preço de venda unitário"

#: order/models.py:2073 order/status_codes.py:50
msgid "Shipped"
msgstr "Enviado"

#: order/models.py:2074
msgid "Shipped quantity"
msgstr "Quantidade enviada"

#: order/models.py:2174
msgid "Sales Order Shipment"
msgstr "Envio do Pedido de Venda"

#: order/models.py:2206
msgid "Date of shipment"
msgstr "Data do envio"

#: order/models.py:2212
msgid "Delivery Date"
msgstr "Data de Entrega"

#: order/models.py:2213
msgid "Date of delivery of shipment"
msgstr "Data da entrega do envio"

#: order/models.py:2221
msgid "Checked By"
msgstr "Verificado por"

#: order/models.py:2222
msgid "User who checked this shipment"
msgstr "Usuário que verificou este envio"

#: order/models.py:2229 order/models.py:2461 order/serializers.py:1678
#: order/serializers.py:1802
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Envio"

#: order/models.py:2230
msgid "Shipment number"
msgstr "Número do Envio"

#: order/models.py:2238
msgid "Tracking Number"
msgstr "Número de rastreio"

#: order/models.py:2239
msgid "Shipment tracking information"
msgstr "Informação de rastreamento"

#: order/models.py:2246
msgid "Invoice Number"
msgstr ""

#: order/models.py:2247
msgid "Reference number for associated invoice"
msgstr ""

#: order/models.py:2270
msgid "Shipment has already been sent"
msgstr ""

#: order/models.py:2273
msgid "Shipment has no allocated stock items"
msgstr ""

#: order/models.py:2353
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2382
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2405 order/models.py:2407
msgid "Stock item has not been assigned"
msgstr ""

#: order/models.py:2414
msgid "Cannot allocate stock item to a line with a different part"
msgstr ""

#: order/models.py:2417
msgid "Cannot allocate stock to a line without a part"
msgstr ""

#: order/models.py:2420
msgid "Allocation quantity cannot exceed stock quantity"
msgstr ""

#: order/models.py:2439 order/serializers.py:1548
msgid "Quantity must be 1 for serialized stock item"
msgstr ""

#: order/models.py:2442
msgid "Sales order does not match shipment"
msgstr ""

#: order/models.py:2443 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr ""

#: order/models.py:2451
msgid "Line"
msgstr "Linha"

#: order/models.py:2462
msgid "Sales order shipment reference"
msgstr ""

#: order/models.py:2475 order/models.py:2894
msgid "Item"
msgstr "Item"

#: order/models.py:2476
msgid "Select stock item to allocate"
msgstr ""

#: order/models.py:2485
msgid "Enter stock allocation quantity"
msgstr ""

#: order/models.py:2600
msgid "Return Order reference"
msgstr ""

#: order/models.py:2612
msgid "Company from which items are being returned"
msgstr ""

#: order/models.py:2625
msgid "Return order status"
msgstr ""

#: order/models.py:2852
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2865
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2869
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2874
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2879
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2895
msgid "Select item to return from customer"
msgstr ""

#: order/models.py:2910
msgid "Received Date"
msgstr ""

#: order/models.py:2911
msgid "The date this this return item was received"
msgstr ""

#: order/models.py:2923
msgid "Outcome"
msgstr ""

#: order/models.py:2924
msgid "Outcome for this line item"
msgstr ""

#: order/models.py:2931
msgid "Cost associated with return or repair for this line item"
msgstr ""

#: order/models.py:2941
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:90
msgid "Order ID"
msgstr ""

#: order/serializers.py:90
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:96
msgid "Copy Lines"
msgstr "Copiar linhas"

#: order/serializers.py:97
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:103
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:104
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:117
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr ""

#: order/serializers.py:122
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:173
msgid "Duplicate Order"
msgstr "Duplicar Pedido"

#: order/serializers.py:174
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:250
msgid "Invalid order ID"
msgstr "ID do pedido inválido"

#: order/serializers.py:389
msgid "Supplier Name"
msgstr ""

#: order/serializers.py:431
msgid "Order cannot be cancelled"
msgstr ""

#: order/serializers.py:446 order/serializers.py:1569
msgid "Allow order to be closed with incomplete line items"
msgstr ""

#: order/serializers.py:456 order/serializers.py:1579
msgid "Order has incomplete line items"
msgstr ""

#: order/serializers.py:611
msgid "Order is not open"
msgstr ""

#: order/serializers.py:632
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:634
msgid "Automatically calculate purchase price based on supplier part data"
msgstr ""

#: order/serializers.py:644
msgid "Purchase price currency"
msgstr ""

#: order/serializers.py:656
msgid "Merge Items"
msgstr "Mesclar Itens"

#: order/serializers.py:658
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:665 part/serializers.py:569
msgid "SKU"
msgstr "Código (SKU)"

#: order/serializers.py:679 part/models.py:1101 part/serializers.py:374
msgid "Internal Part Number"
msgstr ""

#: order/serializers.py:687
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:703
msgid "Supplier part must be specified"
msgstr ""

#: order/serializers.py:706
msgid "Purchase order must be specified"
msgstr ""

#: order/serializers.py:714
msgid "Supplier must match purchase order"
msgstr ""

#: order/serializers.py:715
msgid "Purchase order must match supplier"
msgstr ""

#: order/serializers.py:760 order/serializers.py:1649
msgid "Line Item"
msgstr ""

#: order/serializers.py:769 order/serializers.py:909 order/serializers.py:2016
msgid "Select destination location for received items"
msgstr ""

#: order/serializers.py:785
msgid "Enter batch code for incoming stock items"
msgstr ""

#: order/serializers.py:792 stock/models.py:1161
#: templates/email/stale_stock_notification.html:22 users/models.py:143
msgid "Expiry Date"
msgstr ""

#: order/serializers.py:793
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:801
msgid "Enter serial numbers for incoming stock items"
msgstr ""

#: order/serializers.py:811
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:819 order/serializers.py:2021
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:826
msgid "Barcode"
msgstr "Código de barras"

#: order/serializers.py:827
msgid "Scanned barcode"
msgstr ""

#: order/serializers.py:843
msgid "Barcode is already in use"
msgstr ""

#: order/serializers.py:926 order/serializers.py:2040
msgid "Line items must be provided"
msgstr ""

#: order/serializers.py:945
msgid "Destination location must be specified"
msgstr ""

#: order/serializers.py:952
msgid "Supplied barcode values must be unique"
msgstr ""

#: order/serializers.py:1066
msgid "Shipments"
msgstr ""

#: order/serializers.py:1070
msgid "Completed Shipments"
msgstr ""

#: order/serializers.py:1265
msgid "Sale price currency"
msgstr ""

#: order/serializers.py:1314
msgid "Allocated Items"
msgstr "Itens Alocados"

#: order/serializers.py:1451
msgid "No shipment details provided"
msgstr ""

#: order/serializers.py:1512 order/serializers.py:1658
msgid "Line item is not associated with this order"
msgstr ""

#: order/serializers.py:1531
msgid "Quantity must be positive"
msgstr ""

#: order/serializers.py:1668
msgid "Enter serial numbers to allocate"
msgstr ""

#: order/serializers.py:1690 order/serializers.py:1810
msgid "Shipment has already been shipped"
msgstr ""

#: order/serializers.py:1693 order/serializers.py:1813
msgid "Shipment is not associated with this order"
msgstr ""

#: order/serializers.py:1748
msgid "No match found for the following serial numbers"
msgstr ""

#: order/serializers.py:1755
msgid "The following serial numbers are unavailable"
msgstr "Os seguintes números de série não estão disponíveis"

#: order/serializers.py:1982
msgid "Return order line item"
msgstr ""

#: order/serializers.py:1992
msgid "Line item does not match return order"
msgstr ""

#: order/serializers.py:1995
msgid "Line item has already been received"
msgstr ""

#: order/serializers.py:2032
msgid "Items can only be received against orders which are in progress"
msgstr ""

#: order/serializers.py:2124
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2136
msgid "Line price currency"
msgstr ""

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Perdido"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Devolvido"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "Em andamento"

#: order/status_codes.py:105
msgid "Return"
msgstr ""

#: order/status_codes.py:108
msgid "Repair"
msgstr "Consertar"

#: order/status_codes.py:111
msgid "Replace"
msgstr ""

#: order/status_codes.py:114
msgid "Refund"
msgstr ""

#: order/status_codes.py:117
msgid "Reject"
msgstr "Rejeitado"

#: order/tasks.py:47
msgid "Overdue Purchase Order"
msgstr ""

#: order/tasks.py:52
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr ""

#: order/tasks.py:117
msgid "Overdue Sales Order"
msgstr ""

#: order/tasks.py:122
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr ""

#: order/tasks.py:184
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:189
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:111
msgid "Starred"
msgstr ""

#: part/api.py:113
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:130 stock/api.py:283
msgid "Depth"
msgstr ""

#: part/api.py:130
msgid "Filter by category depth"
msgstr ""

#: part/api.py:148 stock/api.py:301
msgid "Top Level"
msgstr ""

#: part/api.py:150
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:163 stock/api.py:316
msgid "Cascade"
msgstr ""

#: part/api.py:165
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:185
msgid "Parent"
msgstr ""

#: part/api.py:187
msgid "Filter by parent category"
msgstr ""

#: part/api.py:222
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:434
msgid "Has Results"
msgstr ""

#: part/api.py:660
msgid "Is Variant"
msgstr ""

#: part/api.py:668
msgid "Is Revision"
msgstr ""

#: part/api.py:678
msgid "Has Revisions"
msgstr ""

#: part/api.py:859
msgid "BOM Valid"
msgstr ""

#: part/api.py:1502
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1511
msgid "Component part is testable"
msgstr ""

#: part/api.py:1576
msgid "Uses"
msgstr ""

#: part/models.py:98 part/models.py:4162
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr ""

#: part/models.py:99 users/ruleset.py:27
msgid "Part Categories"
msgstr ""

#: part/models.py:117 part/models.py:1146
msgid "Default Location"
msgstr ""

#: part/models.py:118
msgid "Default location for parts in this category"
msgstr ""

#: part/models.py:123 stock/models.py:217
msgid "Structural"
msgstr ""

#: part/models.py:125
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr ""

#: part/models.py:134
msgid "Default keywords"
msgstr ""

#: part/models.py:135
msgid "Default keywords for parts in this category"
msgstr ""

#: part/models.py:142 stock/models.py:98 stock/models.py:199
msgid "Icon"
msgstr "Ícone"

#: part/models.py:143 part/serializers.py:147 part/serializers.py:166
#: stock/models.py:200
msgid "Icon (optional)"
msgstr "Ícone (opcional)"

#: part/models.py:187
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr ""

#: part/models.py:473 part/serializers.py:121 part/serializers.py:305
#: users/ruleset.py:28
msgid "Parts"
msgstr "Peças"

#: part/models.py:525
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:528
msgid "Cannot delete this part as it is still active"
msgstr ""

#: part/models.py:533
msgid "Cannot delete this part as it is used in an assembly"
msgstr ""

#: part/models.py:616 part/models.py:623
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr ""

#: part/models.py:635
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr ""

#: part/models.py:702
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr ""

#: part/models.py:710
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:717
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:724
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:731
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:738
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:744
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:841
msgid "Stock item with this serial number already exists"
msgstr ""

#: part/models.py:983
msgid "Duplicate IPN not allowed in part settings"
msgstr ""

#: part/models.py:995
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:1004
msgid "Part with this Name, IPN and Revision already exists."
msgstr ""

#: part/models.py:1019
msgid "Parts cannot be assigned to structural part categories!"
msgstr ""

#: part/models.py:1051
msgid "Part name"
msgstr "Nome da peça"

#: part/models.py:1056
msgid "Is Template"
msgstr "É um modelo"

#: part/models.py:1057
msgid "Is this part a template part?"
msgstr ""

#: part/models.py:1067
msgid "Is this part a variant of another part?"
msgstr ""

#: part/models.py:1068
msgid "Variant Of"
msgstr ""

#: part/models.py:1075
msgid "Part description (optional)"
msgstr ""

#: part/models.py:1082
msgid "Keywords"
msgstr "Palavras-chaves"

#: part/models.py:1083
msgid "Part keywords to improve visibility in search results"
msgstr ""

#: part/models.py:1093
msgid "Part category"
msgstr ""

#: part/models.py:1100 part/serializers.py:922
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr ""

#: part/models.py:1108
msgid "Part revision or version number"
msgstr ""

#: part/models.py:1109 report/models.py:228
msgid "Revision"
msgstr ""

#: part/models.py:1118
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1119
msgid "Revision Of"
msgstr ""

#: part/models.py:1144
msgid "Where is this item normally stored?"
msgstr ""

#: part/models.py:1190
msgid "Default Supplier"
msgstr ""

#: part/models.py:1191
msgid "Default supplier part"
msgstr ""

#: part/models.py:1198
msgid "Default Expiry"
msgstr ""

#: part/models.py:1199
msgid "Expiry time (in days) for stock items of this part"
msgstr ""

#: part/models.py:1207 part/serializers.py:996
msgid "Minimum Stock"
msgstr "Estoque Mínimo"

#: part/models.py:1208
msgid "Minimum allowed stock level"
msgstr ""

#: part/models.py:1217
msgid "Units of measure for this part"
msgstr ""

#: part/models.py:1224
msgid "Can this part be built from other parts?"
msgstr ""

#: part/models.py:1230
msgid "Can this part be used to build other parts?"
msgstr ""

#: part/models.py:1236
msgid "Does this part have tracking for unique items?"
msgstr ""

#: part/models.py:1242
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1248
msgid "Can this part be purchased from external suppliers?"
msgstr ""

#: part/models.py:1254
msgid "Can this part be sold to customers?"
msgstr ""

#: part/models.py:1258
msgid "Is this part active?"
msgstr ""

#: part/models.py:1264
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1270
msgid "Is this a virtual part, such as a software product or license?"
msgstr ""

#: part/models.py:1275
msgid "BOM Validated"
msgstr ""

#: part/models.py:1276
msgid "Is the BOM for this part valid?"
msgstr ""

#: part/models.py:1282
msgid "BOM checksum"
msgstr ""

#: part/models.py:1283
msgid "Stored BOM checksum"
msgstr ""

#: part/models.py:1291
msgid "BOM checked by"
msgstr ""

#: part/models.py:1296
msgid "BOM checked date"
msgstr ""

#: part/models.py:1312
msgid "Creation User"
msgstr ""

#: part/models.py:1322
msgid "Owner responsible for this part"
msgstr ""

#: part/models.py:2257
msgid "Sell multiple"
msgstr ""

#: part/models.py:3285
msgid "Currency used to cache pricing calculations"
msgstr ""

#: part/models.py:3301
msgid "Minimum BOM Cost"
msgstr ""

#: part/models.py:3302
msgid "Minimum cost of component parts"
msgstr ""

#: part/models.py:3308
msgid "Maximum BOM Cost"
msgstr ""

#: part/models.py:3309
msgid "Maximum cost of component parts"
msgstr ""

#: part/models.py:3315
msgid "Minimum Purchase Cost"
msgstr ""

#: part/models.py:3316
msgid "Minimum historical purchase cost"
msgstr ""

#: part/models.py:3322
msgid "Maximum Purchase Cost"
msgstr ""

#: part/models.py:3323
msgid "Maximum historical purchase cost"
msgstr ""

#: part/models.py:3329
msgid "Minimum Internal Price"
msgstr ""

#: part/models.py:3330
msgid "Minimum cost based on internal price breaks"
msgstr ""

#: part/models.py:3336
msgid "Maximum Internal Price"
msgstr ""

#: part/models.py:3337
msgid "Maximum cost based on internal price breaks"
msgstr ""

#: part/models.py:3343
msgid "Minimum Supplier Price"
msgstr ""

#: part/models.py:3344
msgid "Minimum price of part from external suppliers"
msgstr ""

#: part/models.py:3350
msgid "Maximum Supplier Price"
msgstr ""

#: part/models.py:3351
msgid "Maximum price of part from external suppliers"
msgstr ""

#: part/models.py:3357
msgid "Minimum Variant Cost"
msgstr ""

#: part/models.py:3358
msgid "Calculated minimum cost of variant parts"
msgstr ""

#: part/models.py:3364
msgid "Maximum Variant Cost"
msgstr ""

#: part/models.py:3365
msgid "Calculated maximum cost of variant parts"
msgstr ""

#: part/models.py:3371 part/models.py:3385
msgid "Minimum Cost"
msgstr ""

#: part/models.py:3372
msgid "Override minimum cost"
msgstr ""

#: part/models.py:3378 part/models.py:3392
msgid "Maximum Cost"
msgstr ""

#: part/models.py:3379
msgid "Override maximum cost"
msgstr ""

#: part/models.py:3386
msgid "Calculated overall minimum cost"
msgstr ""

#: part/models.py:3393
msgid "Calculated overall maximum cost"
msgstr ""

#: part/models.py:3399
msgid "Minimum Sale Price"
msgstr ""

#: part/models.py:3400
msgid "Minimum sale price based on price breaks"
msgstr ""

#: part/models.py:3406
msgid "Maximum Sale Price"
msgstr ""

#: part/models.py:3407
msgid "Maximum sale price based on price breaks"
msgstr ""

#: part/models.py:3413
msgid "Minimum Sale Cost"
msgstr ""

#: part/models.py:3414
msgid "Minimum historical sale price"
msgstr ""

#: part/models.py:3420
msgid "Maximum Sale Cost"
msgstr ""

#: part/models.py:3421
msgid "Maximum historical sale price"
msgstr ""

#: part/models.py:3439
msgid "Part for stocktake"
msgstr ""

#: part/models.py:3444
msgid "Item Count"
msgstr ""

#: part/models.py:3445
msgid "Number of individual stock entries at time of stocktake"
msgstr ""

#: part/models.py:3453
msgid "Total available stock at time of stocktake"
msgstr ""

#: part/models.py:3457 report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Data"

#: part/models.py:3458
msgid "Date stocktake was performed"
msgstr ""

#: part/models.py:3465
msgid "Minimum Stock Cost"
msgstr ""

#: part/models.py:3466
msgid "Estimated minimum cost of stock on hand"
msgstr ""

#: part/models.py:3472
msgid "Maximum Stock Cost"
msgstr ""

#: part/models.py:3473
msgid "Estimated maximum cost of stock on hand"
msgstr ""

#: part/models.py:3483
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3595
msgid "Part Test Template"
msgstr ""

#: part/models.py:3621
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3642 part/models.py:3815
msgid "Choices must be unique"
msgstr ""

#: part/models.py:3653
msgid "Test templates can only be created for testable parts"
msgstr "Modelos de teste só podem ser criados para partes testáveis"

#: part/models.py:3667
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3684
msgid "Test Name"
msgstr ""

#: part/models.py:3685
msgid "Enter a name for the test"
msgstr ""

#: part/models.py:3691
msgid "Test Key"
msgstr ""

#: part/models.py:3692
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3699
msgid "Test Description"
msgstr ""

#: part/models.py:3700
msgid "Enter description for this test"
msgstr ""

#: part/models.py:3704 report/models.py:287
msgid "Enabled"
msgstr "Habilitado"

#: part/models.py:3704
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3709
msgid "Required"
msgstr "Obrigatório"

#: part/models.py:3710
msgid "Is this test required to pass?"
msgstr ""

#: part/models.py:3715
msgid "Requires Value"
msgstr ""

#: part/models.py:3716
msgid "Does this test require a value when adding a test result?"
msgstr ""

#: part/models.py:3721
msgid "Requires Attachment"
msgstr ""

#: part/models.py:3723
msgid "Does this test require a file attachment when adding a test result?"
msgstr ""

#: part/models.py:3729 part/models.py:3877
msgid "Choices"
msgstr ""

#: part/models.py:3730
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3763
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3790
msgid "Checkbox parameters cannot have units"
msgstr ""

#: part/models.py:3795
msgid "Checkbox parameters cannot have choices"
msgstr ""

#: part/models.py:3832
msgid "Parameter template name must be unique"
msgstr ""

#: part/models.py:3850
msgid "Parameter Name"
msgstr "Nome do Parâmetro"

#: part/models.py:3857
msgid "Physical units for this parameter"
msgstr ""

#: part/models.py:3865
msgid "Parameter description"
msgstr ""

#: part/models.py:3871
msgid "Checkbox"
msgstr "Caixa de seleção"

#: part/models.py:3872
msgid "Is this parameter a checkbox?"
msgstr ""

#: part/models.py:3878
msgid "Valid choices for this parameter (comma-separated)"
msgstr ""

#: part/models.py:3889
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3931
msgid "Part Parameter"
msgstr ""

#: part/models.py:3957
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3995
msgid "Invalid choice for parameter value"
msgstr ""

#: part/models.py:4046
msgid "Parent Part"
msgstr ""

#: part/models.py:4054 part/models.py:4169 part/models.py:4170
msgid "Parameter Template"
msgstr ""

#: part/models.py:4060
msgid "Parameter Value"
msgstr ""

#: part/models.py:4070 stock/serializers.py:741
msgid "Optional note field"
msgstr ""

#: part/models.py:4117
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4176
msgid "Default Value"
msgstr "Valor Padrão"

#: part/models.py:4177
msgid "Default Parameter Value"
msgstr ""

#: part/models.py:4346
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4353
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4363
msgid "Select parent part"
msgstr ""

#: part/models.py:4373
msgid "Sub part"
msgstr "Sub peça"

#: part/models.py:4374
msgid "Select part to be used in BOM"
msgstr ""

#: part/models.py:4385
msgid "BOM quantity for this BOM item"
msgstr ""

#: part/models.py:4391
msgid "This BOM item is optional"
msgstr ""

#: part/models.py:4397
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr ""

#: part/models.py:4405
msgid "Setup Quantity"
msgstr ""

#: part/models.py:4406
msgid "Extra required quantity for a build, to account for setup losses"
msgstr ""

#: part/models.py:4414
msgid "Attrition"
msgstr ""

#: part/models.py:4416
msgid "Estimated attrition for a build, expressed as a percentage (0-100)"
msgstr ""

#: part/models.py:4427
msgid "Rounding Multiple"
msgstr ""

#: part/models.py:4429
msgid "Round up required production quantity to nearest multiple of this value"
msgstr ""

#: part/models.py:4437
msgid "BOM item reference"
msgstr ""

#: part/models.py:4445
msgid "BOM item notes"
msgstr ""

#: part/models.py:4451
msgid "Checksum"
msgstr ""

#: part/models.py:4452
msgid "BOM line checksum"
msgstr ""

#: part/models.py:4457
msgid "Validated"
msgstr ""

#: part/models.py:4458
msgid "This BOM item has been validated"
msgstr ""

#: part/models.py:4463
msgid "Gets inherited"
msgstr ""

#: part/models.py:4464
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr ""

#: part/models.py:4470
msgid "Stock items for variant parts can be used for this BOM item"
msgstr ""

#: part/models.py:4577 stock/models.py:926
msgid "Quantity must be integer value for trackable parts"
msgstr ""

#: part/models.py:4587 part/models.py:4589
msgid "Sub part must be specified"
msgstr ""

#: part/models.py:4740
msgid "BOM Item Substitute"
msgstr ""

#: part/models.py:4761
msgid "Substitute part cannot be the same as the master part"
msgstr ""

#: part/models.py:4774
msgid "Parent BOM item"
msgstr ""

#: part/models.py:4782
msgid "Substitute part"
msgstr ""

#: part/models.py:4798
msgid "Part 1"
msgstr ""

#: part/models.py:4806
msgid "Part 2"
msgstr ""

#: part/models.py:4807
msgid "Select Related Part"
msgstr ""

#: part/models.py:4814
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4833
msgid "Part relationship cannot be created between a part and itself"
msgstr ""

#: part/models.py:4838
msgid "Duplicate relationship already exists"
msgstr ""

#: part/serializers.py:116
msgid "Parent Category"
msgstr ""

#: part/serializers.py:117
msgid "Parent part category"
msgstr ""

#: part/serializers.py:125 part/serializers.py:163
msgid "Subcategories"
msgstr ""

#: part/serializers.py:202
msgid "Results"
msgstr ""

#: part/serializers.py:203
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:230 part/serializers.py:248 stock/serializers.py:666
msgid "Purchase currency of this stock item"
msgstr ""

#: part/serializers.py:275
msgid "File is not an image"
msgstr ""

#: part/serializers.py:306
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:480
msgid "Original Part"
msgstr ""

#: part/serializers.py:481
msgid "Select original part to duplicate"
msgstr ""

#: part/serializers.py:486
msgid "Copy Image"
msgstr ""

#: part/serializers.py:487
msgid "Copy image from original part"
msgstr ""

#: part/serializers.py:493
msgid "Copy BOM"
msgstr ""

#: part/serializers.py:494
msgid "Copy bill of materials from original part"
msgstr ""

#: part/serializers.py:500
msgid "Copy Parameters"
msgstr ""

#: part/serializers.py:501
msgid "Copy parameter data from original part"
msgstr ""

#: part/serializers.py:507
msgid "Copy Notes"
msgstr ""

#: part/serializers.py:508
msgid "Copy notes from original part"
msgstr ""

#: part/serializers.py:514
msgid "Copy Tests"
msgstr ""

#: part/serializers.py:515
msgid "Copy test templates from original part"
msgstr ""

#: part/serializers.py:533
msgid "Initial Stock Quantity"
msgstr ""

#: part/serializers.py:535
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr ""

#: part/serializers.py:542
msgid "Initial Stock Location"
msgstr ""

#: part/serializers.py:543
msgid "Specify initial stock location for this Part"
msgstr ""

#: part/serializers.py:560
msgid "Select supplier (or leave blank to skip)"
msgstr ""

#: part/serializers.py:576
msgid "Select manufacturer (or leave blank to skip)"
msgstr ""

#: part/serializers.py:586
msgid "Manufacturer part number"
msgstr ""

#: part/serializers.py:593
msgid "Selected company is not a valid supplier"
msgstr ""

#: part/serializers.py:602
msgid "Selected company is not a valid manufacturer"
msgstr ""

#: part/serializers.py:613
msgid "Manufacturer part matching this MPN already exists"
msgstr ""

#: part/serializers.py:620
msgid "Supplier part matching this SKU already exists"
msgstr ""

#: part/serializers.py:907
msgid "Category Name"
msgstr ""

#: part/serializers.py:936
msgid "Building"
msgstr ""

#: part/serializers.py:937
msgid "Quantity of this part currently being in production"
msgstr ""

#: part/serializers.py:944
msgid "Outstanding quantity of this part scheduled to be built"
msgstr ""

#: part/serializers.py:964 stock/serializers.py:1042 stock/serializers.py:1213
#: users/ruleset.py:30
msgid "Stock Items"
msgstr ""

#: part/serializers.py:968
msgid "Revisions"
msgstr ""

#: part/serializers.py:972
msgid "Suppliers"
msgstr ""

#: part/serializers.py:976 part/serializers.py:1268
#: templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr ""

#: part/serializers.py:984
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:992
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1025
msgid "Duplicate Part"
msgstr ""

#: part/serializers.py:1026
msgid "Copy initial data from another Part"
msgstr ""

#: part/serializers.py:1032
msgid "Initial Stock"
msgstr ""

#: part/serializers.py:1033
msgid "Create Part with initial stock quantity"
msgstr ""

#: part/serializers.py:1039
msgid "Supplier Information"
msgstr ""

#: part/serializers.py:1040
msgid "Add initial supplier information for this part"
msgstr ""

#: part/serializers.py:1048
msgid "Copy Category Parameters"
msgstr ""

#: part/serializers.py:1049
msgid "Copy parameter templates from selected part category"
msgstr ""

#: part/serializers.py:1054
msgid "Existing Image"
msgstr ""

#: part/serializers.py:1055
msgid "Filename of an existing part image"
msgstr ""

#: part/serializers.py:1072
msgid "Image file does not exist"
msgstr ""

#: part/serializers.py:1240
msgid "Validate entire Bill of Materials"
msgstr ""

#: part/serializers.py:1274 part/serializers.py:1736
msgid "Can Build"
msgstr ""

#: part/serializers.py:1291
msgid "Required for Build Orders"
msgstr ""

#: part/serializers.py:1296
msgid "Allocated to Build Orders"
msgstr ""

#: part/serializers.py:1303
msgid "Required for Sales Orders"
msgstr ""

#: part/serializers.py:1307
msgid "Allocated to Sales Orders"
msgstr ""

#: part/serializers.py:1446
msgid "Minimum Price"
msgstr ""

#: part/serializers.py:1447
msgid "Override calculated value for minimum price"
msgstr ""

#: part/serializers.py:1454
msgid "Minimum price currency"
msgstr ""

#: part/serializers.py:1461
msgid "Maximum Price"
msgstr ""

#: part/serializers.py:1462
msgid "Override calculated value for maximum price"
msgstr ""

#: part/serializers.py:1469
msgid "Maximum price currency"
msgstr ""

#: part/serializers.py:1498
msgid "Update"
msgstr "Atualizar"

#: part/serializers.py:1499
msgid "Update pricing for this part"
msgstr ""

#: part/serializers.py:1522
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr ""

#: part/serializers.py:1529
msgid "Minimum price must not be greater than maximum price"
msgstr ""

#: part/serializers.py:1532
msgid "Maximum price must not be less than minimum price"
msgstr ""

#: part/serializers.py:1702
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1716
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1882
msgid "Select part to copy BOM from"
msgstr ""

#: part/serializers.py:1890
msgid "Remove Existing Data"
msgstr ""

#: part/serializers.py:1891
msgid "Remove existing BOM items before copying"
msgstr ""

#: part/serializers.py:1896
msgid "Include Inherited"
msgstr ""

#: part/serializers.py:1897
msgid "Include BOM items which are inherited from templated parts"
msgstr ""

#: part/serializers.py:1902
msgid "Skip Invalid Rows"
msgstr ""

#: part/serializers.py:1903
msgid "Enable this option to skip invalid rows"
msgstr ""

#: part/serializers.py:1908
msgid "Copy Substitute Parts"
msgstr ""

#: part/serializers.py:1909
msgid "Copy substitute parts when duplicate BOM items"
msgstr ""

#: part/tasks.py:40
msgid "Low stock notification"
msgstr ""

#: part/tasks.py:42
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr ""

#: part/tasks.py:72
msgid "Stale stock notification"
msgstr ""

#: part/tasks.py:76
msgid "You have 1 stock item approaching its expiry date"
msgstr ""

#: part/tasks.py:78
#, python-brace-format
msgid "You have {item_count} stock items approaching their expiry dates"
msgstr ""

#: part/tasks.py:87
msgid "No expiry date"
msgstr ""

#: part/tasks.py:94
msgid "Expired {abs(days_diff)} days ago"
msgstr ""

#: part/tasks.py:97
msgid "Expires today"
msgstr ""

#: part/tasks.py:100
#, python-brace-format
msgid "{days_until_expiry} days"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr "Obrigatório"

#: plugin/api.py:107
msgid "Sample"
msgstr "Exemplo"

#: plugin/api.py:121 plugin/models.py:166
msgid "Installed"
msgstr "Instalado"

#: plugin/api.py:188
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Nenhuma ação especificada"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr ""

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Modelo não suportado"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr ""

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr ""

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr ""

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr ""

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr ""

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr ""

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr ""

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr ""

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr ""

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr ""

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr ""

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr ""

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr ""

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr ""

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr ""

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr ""

#: plugin/base/label/label.py:41
msgid "Label printing failed"
msgstr ""

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr ""

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/events/auto_create_builds.py:30
#: plugin/builtin/events/auto_issue_orders.py:19
#: plugin/builtin/exporter/bom_exporter.py:73
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/exporter/stocktake_exporter.py:47
#: plugin/builtin/integration/core_notifications.py:25
#: plugin/builtin/integration/core_notifications.py:65
#: plugin/builtin/integration/core_notifications.py:121
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:72
#: plugin/builtin/suppliers/digikey.py:20 plugin/builtin/suppliers/lcsc.py:22
#: plugin/builtin/suppliers/mouser.py:20 plugin/builtin/suppliers/tme.py:22
msgid "InvenTree contributors"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:28
msgid "Auto Create Builds"
msgstr ""

#: plugin/builtin/events/auto_create_builds.py:31
msgid "Automatically create build orders for assemblies"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:17
msgid "Auto Issue Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:20
msgid "Automatically issue orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:30
msgid "Auto Issue Build Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:32
msgid "Automatically issue build orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:38
msgid "Auto Issue Purchase Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:40
msgid "Automatically issue purchase orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:46
msgid "Auto Issue Sales Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:48
msgid "Automatically issue sales orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:54
msgid "Auto Issue Return Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:56
msgid "Automatically issue return orders on the assigned target date"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:62
msgid "Issue Backdated Orders"
msgstr ""

#: plugin/builtin/events/auto_issue_orders.py:63
msgid "Automatically issue orders that are backdated"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:21
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
msgid "Number of levels to export - set to zero to export all BOM levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:30
#: plugin/builtin/exporter/bom_exporter.py:114
msgid "Total Quantity"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include total quantity of each part in the BOM"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:35
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:39
#: plugin/builtin/exporter/part_parameter_exporter.py:21
#: plugin/builtin/exporter/stocktake_exporter.py:20
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:54
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:55
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:60
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:61
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:70
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:71
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:120
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:126
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:127
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:128
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:134
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:135
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:25
msgid "Include External Stock"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:26
msgid "Include external stock in the stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:31
msgid "Include Variant Items"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:32
msgid "Include part variant stock in pricing calculations"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:44
msgid "Part Stocktake Exporter"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:45
msgid "Exporter for part stocktake data"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:108
msgid "Minimum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:109
msgid "Maximum Unit Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:110
msgid "Minimum Total Cost"
msgstr ""

#: plugin/builtin/exporter/stocktake_exporter.py:111
msgid "Maximum Total Cost"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:23
msgid "InvenTree UI Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:26
msgid "Integrated UI notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:63
msgid "InvenTree Email Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:66
msgid "Integrated email notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:71
msgid "Allow email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:72
msgid "Allow email notifications to be sent to this user"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:119
msgid "InvenTree Slack Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:122
msgid "Integrated Slack notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:127
msgid "Slack incoming webhook url"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:128
msgid "URL that is used to send messages to a slack channel"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:158
msgid "Open link"
msgstr "Abrir link"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr "Enviar notificações"

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:78
msgid "Debug mode"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:79
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:162
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:179
msgid "Options"
msgstr "Opções"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Pular Etiquetas"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:43
msgid "Border"
msgstr "Margem"

#: plugin/builtin/labels/label_sheet.py:44
msgid "Print a border around each label"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:49 report/models.py:387
msgid "Landscape"
msgstr "Paisagem"

#: plugin/builtin/labels/label_sheet.py:50
msgid "Print the label sheet in landscape mode"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Page Margin"
msgstr "Margem da Página"

#: plugin/builtin/labels/label_sheet.py:56
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "InvenTree Label Sheet Printer"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:70
msgid "Arrays multiple labels onto a single sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:122
msgid "Label is too large for page size"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:161
msgid "No labels were generated"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:17
msgid "Supplier Integration - DigiKey"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:18
msgid "Provides support for scanning DigiKey barcodes"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:27
msgid "The Supplier which acts as 'DigiKey'"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Supplier Integration - LCSC"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:20
msgid "Provides support for scanning LCSC barcodes"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:28
msgid "The Supplier which acts as 'LCSC'"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:17
msgid "Supplier Integration - Mouser"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:18
msgid "Provides support for scanning Mouser barcodes"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:26
msgid "The Supplier which acts as 'Mouser'"
msgstr ""

#: plugin/builtin/suppliers/tme.py:19
msgid "Supplier Integration - TME"
msgstr ""

#: plugin/builtin/suppliers/tme.py:20
msgid "Provides support for scanning TME barcodes"
msgstr ""

#: plugin/builtin/suppliers/tme.py:28
msgid "The Supplier which acts as 'TME'"
msgstr ""

#: plugin/installer.py:240 plugin/installer.py:320
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:243
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:280
msgid "Installed plugin successfully"
msgstr ""

#: plugin/installer.py:285
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr ""

#: plugin/installer.py:311
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:314
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:317
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:337
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:341
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:347
msgid "Plugin cannot be uninstalled as it is mandatory"
msgstr ""

#: plugin/installer.py:352
msgid "Plugin cannot be uninstalled as it is a sample plugin"
msgstr ""

#: plugin/installer.py:357
msgid "Plugin cannot be uninstalled as it is a built-in plugin"
msgstr ""

#: plugin/installer.py:361
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:379
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:395
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr ""

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr ""

#: plugin/models.py:46
msgid "Key of plugin"
msgstr ""

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr ""

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr ""

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr ""

#: plugin/models.py:175
msgid "Sample plugin"
msgstr ""

#: plugin/models.py:183
msgid "Builtin Plugin"
msgstr ""

#: plugin/models.py:191
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:209
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:296 plugin/models.py:342
msgid "Plugin"
msgstr ""

#: plugin/plugin.py:384
msgid "No author found"
msgstr ""

#: plugin/registry.py:760
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr ""

#: plugin/registry.py:763
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr ""

#: plugin/registry.py:765
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr ""

#: plugin/samples/integration/sample.py:52
msgid "User Setting 1"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "A user setting that can be changed by the user"
msgstr ""

#: plugin/samples/integration/sample.py:57
msgid "User Setting 2"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "Another user setting"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "User Setting 3"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A user setting with choices"
msgstr ""

#: plugin/samples/integration/sample.py:72
msgid "Enable PO"
msgstr ""

#: plugin/samples/integration/sample.py:73
msgid "Enable PO functionality in InvenTree interface"
msgstr ""

#: plugin/samples/integration/sample.py:78
msgid "API Key"
msgstr ""

#: plugin/samples/integration/sample.py:79
msgid "Key required for accessing external API"
msgstr ""

#: plugin/samples/integration/sample.py:83
msgid "Numerical"
msgstr ""

#: plugin/samples/integration/sample.py:84
msgid "A numerical setting"
msgstr ""

#: plugin/samples/integration/sample.py:90
msgid "Choice Setting"
msgstr ""

#: plugin/samples/integration/sample.py:91
msgid "A setting with multiple choices"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr ""

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr ""

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr ""

#: plugin/serializers.py:128
msgid "Version"
msgstr ""

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr ""

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr ""

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr ""

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr ""

#: plugin/serializers.py:188
msgid "Full reload"
msgstr ""

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr ""

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:233
msgid "Activate Plugin"
msgstr ""

#: plugin/serializers.py:234
msgid "Activate this plugin"
msgstr ""

#: plugin/serializers.py:243
msgid "Mandatory plugin cannot be deactivated"
msgstr ""

#: plugin/serializers.py:261
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:262
msgid "Delete the plugin configuration from the database"
msgstr ""

#: plugin/serializers.py:293
msgid "The user for which this setting applies"
msgstr ""

#: report/api.py:43 report/serializers.py:103 report/serializers.py:153
msgid "Items"
msgstr ""

#: report/api.py:114
msgid "Plugin not found"
msgstr ""

#: report/api.py:116
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:164
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:182 report/api.py:264
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr ""

#: report/helpers.py:46
msgid "Letter"
msgstr ""

#: report/models.py:128
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:217
msgid "Template name"
msgstr ""

#: report/models.py:223
msgid "Template description"
msgstr ""

#: report/models.py:229
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:235
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:237
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:281
msgid "Filename Pattern"
msgstr ""

#: report/models.py:282
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:287
msgid "Template is enabled"
msgstr ""

#: report/models.py:294
msgid "Target model type for template"
msgstr ""

#: report/models.py:314
msgid "Filters"
msgstr "Filtros"

#: report/models.py:315
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:374 report/models.py:661
msgid "Template file"
msgstr "Arquivo Modelo"

#: report/models.py:382
msgid "Page size for PDF reports"
msgstr ""

#: report/models.py:388
msgid "Render report in landscape orientation"
msgstr ""

#: report/models.py:393
msgid "Merge"
msgstr "Mesclar"

#: report/models.py:394
msgid "Render a single report against selected items"
msgstr ""

#: report/models.py:449
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:546 report/models.py:582 report/models.py:583
msgid "Template syntax error"
msgstr ""

#: report/models.py:550 report/models.py:586
msgid "Error rendering report"
msgstr ""

#: report/models.py:606
msgid "Error generating report"
msgstr ""

#: report/models.py:635
msgid "Error merging report outputs"
msgstr ""

#: report/models.py:667
msgid "Width [mm]"
msgstr "Largura [mm]"

#: report/models.py:668
msgid "Label width, specified in mm"
msgstr "Largura da Etiqueta, em mm"

#: report/models.py:674
msgid "Height [mm]"
msgstr "Altura [mm]"

#: report/models.py:675
msgid "Label height, specified in mm"
msgstr "Altura da Etiqueta, em mm"

#: report/models.py:780
msgid "Error printing labels"
msgstr ""

#: report/models.py:799
msgid "Snippet"
msgstr ""

#: report/models.py:800
msgid "Report snippet file"
msgstr ""

#: report/models.py:807
msgid "Snippet file description"
msgstr ""

#: report/models.py:825
msgid "Asset"
msgstr ""

#: report/models.py:826
msgid "Report asset file"
msgstr ""

#: report/models.py:833
msgid "Asset file description"
msgstr ""

#: report/serializers.py:96
msgid "Select report template"
msgstr ""

#: report/serializers.py:104 report/serializers.py:154
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:137
msgid "Select label template"
msgstr ""

#: report/serializers.py:145
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:146
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "Código QR"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "Código QR"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr ""

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:47
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_stock_report_merge.html:84
#: report/templates/report/inventree_stock_report_merge.html:106
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Emitido por"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:22
msgid "Order Details"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:37
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:62
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:79
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Total"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_stock_report_merge.html:88
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1084
#: stock/serializers.py:163 templates/email/stale_stock_notification.html:21
msgid "Serial Number"
msgstr ""

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr ""

#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: templates/email/stale_stock_notification.html:20
msgid "Batch"
msgstr ""

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr ""

#: report/templates/report/inventree_stock_report_merge.html:21
#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr ""

#: report/templates/report/inventree_stock_report_merge.html:97
#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:649
msgid "Installed Items"
msgstr ""

#: report/templates/report/inventree_stock_report_merge.html:111
#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr ""

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Resultados do teste"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Teste"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Aprovado"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Reprovado"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr ""

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr ""

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr ""

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr ""

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr ""

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr ""

#: stock/api.py:283
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:303
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:318
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:339 stock/serializers.py:1209
msgid "Parent Location"
msgstr ""

#: stock/api.py:340
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:582
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:588
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:594
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:599
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:605
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:611
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:623
msgid "Minimum stock"
msgstr "Estoque mínimo"

#: stock/api.py:627
msgid "Maximum stock"
msgstr "Estoque máximo"

#: stock/api.py:630
msgid "Status Code"
msgstr ""

#: stock/api.py:670
msgid "External Location"
msgstr ""

#: stock/api.py:769
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:779
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:868
msgid "Part Tree"
msgstr ""

#: stock/api.py:890
msgid "Updated before"
msgstr ""

#: stock/api.py:894
msgid "Updated after"
msgstr ""

#: stock/api.py:898
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:902
msgid "Stocktake After"
msgstr ""

#: stock/api.py:907
msgid "Expiry date before"
msgstr ""

#: stock/api.py:911
msgid "Expiry date after"
msgstr ""

#: stock/api.py:914 stock/serializers.py:654
msgid "Stale"
msgstr ""

#: stock/api.py:1015
msgid "Quantity is required"
msgstr ""

#: stock/api.py:1020
msgid "Valid part must be supplied"
msgstr ""

#: stock/api.py:1051
msgid "The given supplier part does not exist"
msgstr ""

#: stock/api.py:1061
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr ""

#: stock/api.py:1093
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr ""

#: stock/models.py:72
msgid "Stock Location type"
msgstr ""

#: stock/models.py:73
msgid "Stock Location types"
msgstr ""

#: stock/models.py:99
msgid "Default icon for all locations that have no icon set (optional)"
msgstr ""

#: stock/models.py:160 stock/models.py:1046
msgid "Stock Location"
msgstr ""

#: stock/models.py:161 users/ruleset.py:29
msgid "Stock Locations"
msgstr ""

#: stock/models.py:210 stock/models.py:1211
msgid "Owner"
msgstr "Responsável"

#: stock/models.py:211 stock/models.py:1212
msgid "Select Owner"
msgstr "Selecionar Responsável"

#: stock/models.py:219
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr ""

#: stock/models.py:226 users/models.py:503
msgid "External"
msgstr ""

#: stock/models.py:227
msgid "This is an external stock location"
msgstr ""

#: stock/models.py:233
msgid "Location type"
msgstr ""

#: stock/models.py:237
msgid "Stock location type of this location"
msgstr ""

#: stock/models.py:309
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr ""

#: stock/models.py:595
#, python-brace-format
msgid "{field} does not exist"
msgstr ""

#: stock/models.py:608
msgid "Part must be specified"
msgstr ""

#: stock/models.py:905
msgid "Stock items cannot be located into structural stock locations!"
msgstr ""

#: stock/models.py:932 stock/serializers.py:487
msgid "Stock item cannot be created for virtual parts"
msgstr ""

#: stock/models.py:949
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr ""

#: stock/models.py:959 stock/models.py:972
msgid "Quantity must be 1 for item with a serial number"
msgstr ""

#: stock/models.py:962
msgid "Serial number cannot be set if quantity greater than 1"
msgstr ""

#: stock/models.py:984
msgid "Item cannot belong to itself"
msgstr ""

#: stock/models.py:989
msgid "Item must have a build reference if is_building=True"
msgstr ""

#: stock/models.py:1002
msgid "Build reference does not point to the same part object"
msgstr ""

#: stock/models.py:1016
msgid "Parent Stock Item"
msgstr ""

#: stock/models.py:1028
msgid "Base part"
msgstr ""

#: stock/models.py:1038
msgid "Select a matching supplier part for this stock item"
msgstr ""

#: stock/models.py:1050
msgid "Where is this stock item located?"
msgstr ""

#: stock/models.py:1058 stock/serializers.py:1643
msgid "Packaging this stock item is stored in"
msgstr ""

#: stock/models.py:1064
msgid "Installed In"
msgstr ""

#: stock/models.py:1069
msgid "Is this item installed in another item?"
msgstr ""

#: stock/models.py:1088
msgid "Serial number for this item"
msgstr ""

#: stock/models.py:1105 stock/serializers.py:1628
msgid "Batch code for this stock item"
msgstr ""

#: stock/models.py:1110
msgid "Stock Quantity"
msgstr ""

#: stock/models.py:1120
msgid "Source Build"
msgstr ""

#: stock/models.py:1123
msgid "Build for this stock item"
msgstr ""

#: stock/models.py:1130
msgid "Consumed By"
msgstr ""

#: stock/models.py:1133
msgid "Build order which consumed this stock item"
msgstr ""

#: stock/models.py:1142
msgid "Source Purchase Order"
msgstr ""

#: stock/models.py:1146
msgid "Purchase order for this stock item"
msgstr ""

#: stock/models.py:1152
msgid "Destination Sales Order"
msgstr ""

#: stock/models.py:1163
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr ""

#: stock/models.py:1181
msgid "Delete on deplete"
msgstr ""

#: stock/models.py:1182
msgid "Delete this Stock Item when stock is depleted"
msgstr ""

#: stock/models.py:1203
msgid "Single unit purchase price at time of purchase"
msgstr ""

#: stock/models.py:1234
msgid "Converted to part"
msgstr ""

#: stock/models.py:1436
msgid "Quantity exceeds available stock"
msgstr ""

#: stock/models.py:1871
msgid "Part is not set as trackable"
msgstr ""

#: stock/models.py:1877
msgid "Quantity must be integer"
msgstr ""

#: stock/models.py:1885
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr ""

#: stock/models.py:1891
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1896
msgid "Quantity does not match serial numbers"
msgstr ""

#: stock/models.py:2014 stock/models.py:2915
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:2032
msgid "Stock item has been assigned to a sales order"
msgstr ""

#: stock/models.py:2036
msgid "Stock item is installed in another item"
msgstr ""

#: stock/models.py:2039
msgid "Stock item contains other items"
msgstr ""

#: stock/models.py:2042
msgid "Stock item has been assigned to a customer"
msgstr ""

#: stock/models.py:2045 stock/models.py:2226
msgid "Stock item is currently in production"
msgstr ""

#: stock/models.py:2048
msgid "Serialized stock cannot be merged"
msgstr ""

#: stock/models.py:2055 stock/serializers.py:1498
msgid "Duplicate stock items"
msgstr ""

#: stock/models.py:2059
msgid "Stock items must refer to the same part"
msgstr ""

#: stock/models.py:2067
msgid "Stock items must refer to the same supplier part"
msgstr ""

#: stock/models.py:2072
msgid "Stock status codes must match"
msgstr ""

#: stock/models.py:2347
msgid "StockItem cannot be moved as it is not in stock"
msgstr ""

#: stock/models.py:2816
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2847
msgid "Entry notes"
msgstr ""

#: stock/models.py:2887
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2918
msgid "Value must be provided for this test"
msgstr ""

#: stock/models.py:2922
msgid "Attachment must be uploaded for this test"
msgstr ""

#: stock/models.py:2927
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2951
msgid "Test result"
msgstr ""

#: stock/models.py:2958
msgid "Test output value"
msgstr ""

#: stock/models.py:2966 stock/serializers.py:259
msgid "Test result attachment"
msgstr ""

#: stock/models.py:2970
msgid "Test notes"
msgstr ""

#: stock/models.py:2978
msgid "Test station"
msgstr ""

#: stock/models.py:2979
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2985
msgid "Started"
msgstr ""

#: stock/models.py:2986
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2992
msgid "Finished"
msgstr ""

#: stock/models.py:2993
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:289
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:299
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:451
msgid "Parent Item"
msgstr ""

#: stock/serializers.py:452
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:472
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr ""

#: stock/serializers.py:474
msgid "Use pack size"
msgstr ""

#: stock/serializers.py:481 stock/serializers.py:723
msgid "Enter serial numbers for new items"
msgstr ""

#: stock/serializers.py:599
msgid "Supplier Part Number"
msgstr ""

#: stock/serializers.py:646 users/models.py:193
msgid "Expired"
msgstr ""

#: stock/serializers.py:652
msgid "Child Items"
msgstr ""

#: stock/serializers.py:656
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:662
msgid "Purchase price of this stock item, per unit or pack"
msgstr ""

#: stock/serializers.py:700
msgid "Enter number of stock items to serialize"
msgstr ""

#: stock/serializers.py:708 stock/serializers.py:751 stock/serializers.py:789
#: stock/serializers.py:927
msgid "No stock item provided"
msgstr ""

#: stock/serializers.py:716
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr ""

#: stock/serializers.py:734 stock/serializers.py:1455 stock/serializers.py:1768
#: stock/serializers.py:1817
msgid "Destination stock location"
msgstr ""

#: stock/serializers.py:754
msgid "Serial numbers cannot be assigned to this part"
msgstr ""

#: stock/serializers.py:774
msgid "Serial numbers already exist"
msgstr ""

#: stock/serializers.py:824
msgid "Select stock item to install"
msgstr ""

#: stock/serializers.py:831
msgid "Quantity to Install"
msgstr ""

#: stock/serializers.py:832
msgid "Enter the quantity of items to install"
msgstr ""

#: stock/serializers.py:837 stock/serializers.py:917 stock/serializers.py:1059
msgid "Add transaction note (optional)"
msgstr ""

#: stock/serializers.py:845
msgid "Quantity to install must be at least 1"
msgstr ""

#: stock/serializers.py:853
msgid "Stock item is unavailable"
msgstr ""

#: stock/serializers.py:864
msgid "Selected part is not in the Bill of Materials"
msgstr ""

#: stock/serializers.py:877
msgid "Quantity to install must not exceed available quantity"
msgstr ""

#: stock/serializers.py:912
msgid "Destination location for uninstalled item"
msgstr ""

#: stock/serializers.py:950
msgid "Select part to convert stock item into"
msgstr ""

#: stock/serializers.py:963
msgid "Selected part is not a valid option for conversion"
msgstr ""

#: stock/serializers.py:980
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr ""

#: stock/serializers.py:1014
msgid "Stock item status code"
msgstr ""

#: stock/serializers.py:1043
msgid "Select stock items to change status"
msgstr ""

#: stock/serializers.py:1049
msgid "No stock items selected"
msgstr ""

#: stock/serializers.py:1138 stock/serializers.py:1215
msgid "Sublocations"
msgstr ""

#: stock/serializers.py:1210
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1327
msgid "Part must be salable"
msgstr ""

#: stock/serializers.py:1331
msgid "Item is allocated to a sales order"
msgstr ""

#: stock/serializers.py:1335
msgid "Item is allocated to a build order"
msgstr ""

#: stock/serializers.py:1359
msgid "Customer to assign stock items"
msgstr ""

#: stock/serializers.py:1365
msgid "Selected company is not a customer"
msgstr ""

#: stock/serializers.py:1373
msgid "Stock assignment notes"
msgstr ""

#: stock/serializers.py:1383 stock/serializers.py:1671
msgid "A list of stock items must be provided"
msgstr ""

#: stock/serializers.py:1462
msgid "Stock merging notes"
msgstr ""

#: stock/serializers.py:1467
msgid "Allow mismatched suppliers"
msgstr ""

#: stock/serializers.py:1468
msgid "Allow stock items with different supplier parts to be merged"
msgstr ""

#: stock/serializers.py:1473
msgid "Allow mismatched status"
msgstr ""

#: stock/serializers.py:1474
msgid "Allow stock items with different status codes to be merged"
msgstr ""

#: stock/serializers.py:1484
msgid "At least two stock items must be provided"
msgstr ""

#: stock/serializers.py:1551
msgid "No Change"
msgstr ""

#: stock/serializers.py:1589
msgid "StockItem primary key value"
msgstr ""

#: stock/serializers.py:1602
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1605
msgid "Stock item is already in stock"
msgstr ""

#: stock/serializers.py:1619
msgid "Quantity must not be negative"
msgstr ""

#: stock/serializers.py:1661
msgid "Stock transaction notes"
msgstr ""

#: stock/serializers.py:1823
msgid "Merge into existing stock"
msgstr ""

#: stock/serializers.py:1824
msgid "Merge returned items into existing stock items if possible"
msgstr ""

#: stock/serializers.py:1867
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1873
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr ""

#: stock/status_codes.py:13
msgid "Damaged"
msgstr ""

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr ""

#: stock/status_codes.py:15
msgid "Rejected"
msgstr ""

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr ""

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr ""

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr ""

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr ""

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr ""

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr ""

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr ""

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr ""

#: stock/status_codes.py:57
msgid "Returned to stock"
msgstr ""

#: stock/status_codes.py:60
msgid "Location changed"
msgstr ""

#: stock/status_codes.py:61
msgid "Stock updated"
msgstr ""

#: stock/status_codes.py:64
msgid "Installed into assembly"
msgstr ""

#: stock/status_codes.py:65
msgid "Removed from assembly"
msgstr ""

#: stock/status_codes.py:67
msgid "Installed component item"
msgstr ""

#: stock/status_codes.py:68
msgid "Removed component item"
msgstr ""

#: stock/status_codes.py:71
msgid "Split from parent item"
msgstr ""

#: stock/status_codes.py:72
msgid "Split child item"
msgstr ""

#: stock/status_codes.py:75
msgid "Merged stock items"
msgstr ""

#: stock/status_codes.py:78
msgid "Converted to variant"
msgstr ""

#: stock/status_codes.py:81
msgid "Build order output created"
msgstr ""

#: stock/status_codes.py:82
msgid "Build order output completed"
msgstr ""

#: stock/status_codes.py:83
msgid "Build order output rejected"
msgstr ""

#: stock/status_codes.py:84
msgid "Consumed by build order"
msgstr ""

#: stock/status_codes.py:87
msgid "Shipped against Sales Order"
msgstr ""

#: stock/status_codes.py:90
msgid "Received against Purchase Order"
msgstr ""

#: stock/status_codes.py:93
msgid "Returned against Return Order"
msgstr ""

#: stock/status_codes.py:96
msgid "Sent to customer"
msgstr ""

#: stock/status_codes.py:97
msgid "Returned from customer"
msgstr ""

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Permissão Negada"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr ""

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr ""

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr ""

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Página Não Encontrada"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr ""

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Erro Interno do Servidor"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr ""

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr ""

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Site em Manutenção"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr ""

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Reinicialização do Servidor é Necessária"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr ""

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr ""

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr ""

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr ""

#: templates/config_error.html:6 templates/config_error.html:10
msgid "Configuration Error"
msgstr ""

#: templates/config_error.html:11
#, python-format
msgid "The %(inventree_title)s server raised a configuration error"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr ""

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr ""

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr ""

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr ""

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr ""

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr ""

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr ""

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr ""

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr ""

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: templates/email/stale_stock_notification.html:10
msgid "The following stock items are approaching their expiry dates:"
msgstr ""

#: templates/email/stale_stock_notification.html:23
msgid "Days Until Expiry"
msgstr ""

#: templates/email/stale_stock_notification.html:57
msgid "You are receiving this email because you are subscribed to notifications for these parts"
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Usuários"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr ""

#: users/admin.py:137
msgid "Personal info"
msgstr "Informações pessoais"

#: users/admin.py:139
msgid "Permissions"
msgstr "Permissões"

#: users/admin.py:142
msgid "Important dates"
msgstr ""

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr ""

#: users/authentication.py:33
msgid "Token has expired"
msgstr ""

#: users/models.py:100
msgid "API Token"
msgstr ""

#: users/models.py:101
msgid "API Tokens"
msgstr ""

#: users/models.py:137
msgid "Token Name"
msgstr ""

#: users/models.py:138
msgid "Custom token name"
msgstr ""

#: users/models.py:144
msgid "Token expiry date"
msgstr ""

#: users/models.py:152
msgid "Last Seen"
msgstr ""

#: users/models.py:153
msgid "Last time the token was used"
msgstr ""

#: users/models.py:157
msgid "Revoked"
msgstr ""

#: users/models.py:235
msgid "Permission set"
msgstr ""

#: users/models.py:244
msgid "Group"
msgstr "Grupo"

#: users/models.py:248
msgid "View"
msgstr ""

#: users/models.py:248
msgid "Permission to view items"
msgstr "Permissão para exibir itens"

#: users/models.py:252
msgid "Add"
msgstr "Adicionar"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Permissão para adicionar itens"

#: users/models.py:256
msgid "Change"
msgstr "Alterar"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Permissões para editar itens"

#: users/models.py:262
msgid "Delete"
msgstr "Excluir"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Permissão para excluir itens"

#: users/models.py:501
msgid "Bot"
msgstr ""

#: users/models.py:502
msgid "Internal"
msgstr "Interno"

#: users/models.py:504
msgid "Guest"
msgstr "Convidado"

#: users/models.py:513
msgid "Language"
msgstr "Idioma"

#: users/models.py:514
msgid "Preferred language for the user"
msgstr "Idioma preferencial para o usuário"

#: users/models.py:519
msgid "Theme"
msgstr "Tema"

#: users/models.py:520
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:525
msgid "Widgets"
msgstr "Widgets"

#: users/models.py:527
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:534
msgid "Display Name"
msgstr "Nome de Exibição"

#: users/models.py:535
msgid "Chosen display name for the user"
msgstr "Nome de exibição escolhido para o usuário"

#: users/models.py:541
msgid "Position"
msgstr "Cargo"

#: users/models.py:542
msgid "Main job title or position"
msgstr ""

#: users/models.py:549
msgid "User status message"
msgstr "Mensagem de status do usuário"

#: users/models.py:556
msgid "User location information"
msgstr ""

#: users/models.py:561
msgid "User is actively using the system"
msgstr ""

#: users/models.py:568
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:574
msgid "User Type"
msgstr "Tipo de Usuário"

#: users/models.py:575
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:581
msgid "Organisation"
msgstr "Organização"

#: users/models.py:582
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:590
msgid "Primary Group"
msgstr ""

#: users/models.py:591
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:26
msgid "Admin"
msgstr "Admin"

#: users/ruleset.py:32
msgid "Purchase Orders"
msgstr ""

#: users/ruleset.py:33
msgid "Sales Orders"
msgstr ""

#: users/ruleset.py:34
msgid "Return Orders"
msgstr ""

#: users/serializers.py:196
msgid "Username"
msgstr "Nome de usuário"

#: users/serializers.py:199
msgid "First Name"
msgstr "Primeiro Nome"

#: users/serializers.py:199
msgid "First name of the user"
msgstr "Primeiro nome do usuário"

#: users/serializers.py:203
msgid "Last Name"
msgstr "Sobrenome"

#: users/serializers.py:203
msgid "Last name of the user"
msgstr "Sobrenome do usuário"

#: users/serializers.py:207
msgid "Email address of the user"
msgstr "Endereço de e-mail do usuário"

#: users/serializers.py:326
msgid "Staff"
msgstr ""

#: users/serializers.py:327
msgid "Does this user have staff permissions"
msgstr ""

#: users/serializers.py:332
msgid "Superuser"
msgstr "Superusuário"

#: users/serializers.py:332
msgid "Is this user a superuser"
msgstr ""

#: users/serializers.py:336
msgid "Is this user account active"
msgstr ""

#: users/serializers.py:348
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:376
msgid "Password"
msgstr "Senha"

#: users/serializers.py:377
msgid "Password for the user"
msgstr "Senha do usuário"

#: users/serializers.py:383
msgid "Override warning"
msgstr ""

#: users/serializers.py:384
msgid "Override the warning about password rules"
msgstr ""

#: users/serializers.py:426
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:431
msgid "You do not have permission to create users"
msgstr "Você não tem permissão para criar usuários"

#: users/serializers.py:452
msgid "Your account has been created."
msgstr "Sua conta foi criada."

#: users/serializers.py:454
msgid "Please use the password reset function to login"
msgstr ""

#: users/serializers.py:460
msgid "Welcome to InvenTree"
msgstr "Bem-vindo(a) ao InvenTree"

